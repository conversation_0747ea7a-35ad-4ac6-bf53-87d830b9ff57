<!DOCTYPE html>
<!-- saved from url=(0054)https://blog.csdn.net/qpeity/article/details/********* -->
<html lang="zh-CN"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <link rel="canonical" href="https://blog.csdn.net/qpeity/article/details/*********">
    
    <meta name="renderer" content="webkit">
    <meta name="force-rendering" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="report" content="{&quot;spm&quot;:&quot;1001.2101&quot;,&quot;extra&quot;:{&quot;titAb&quot;:&quot;control-1&quot;,&quot;lvab&quot;:&quot;t_new&quot;},&quot;pid&quot;:&quot;blog&quot;}">
    <meta name="referrer" content="always">
    <meta http-equiv="Cache-Control" content="no-siteapp"><link rel="alternate" media="handheld" href="https://blog.csdn.net/qpeity/article/details/*********#">
    <meta name="shenma-site-verification" content="5a59773ab8077d4a62bf469ab966a63b_1497598848">
    <meta name="applicable-device" content="pc">
    <link href="https://g.csdnimg.cn/static/logo/favicon32.ico" rel="shortcut icon" type="image/x-icon">
    <title>BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客</title>
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f.txt"></script><script type="text/javascript" async="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/auto_dup"></script><script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f(1).txt" id="google_shimpl"></script><script type="text/javascript" charset="utf-8" async="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/apiaccept"></script><script type="text/javascript" async="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/trackad.js.下载"></script><script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/push.js.下载"></script><script type="text/javascript" async="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/saved_resource"></script><script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/hm.js.下载"></script><script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/push.js(1).下载" id="ttzz"></script><script>
      (function(){ 
        var el = document.createElement("script"); 
        el.src = "https://s3a.pstatp.com/toutiao/push.js?1abfa13dfe74d72d41d83c86d240de427e7cac50c51ead53b2e79d40c7952a23ed7716d05b4a0f683a653eab3e214672511de2457e74e99286eb2c33f4428830"; 
        el.id = "ttzz"; 
        var s = document.getElementsByTagName("script")[0]; 
        s.parentNode.insertBefore(el, s);
      })(window)
    </script>
        <meta name="keywords" content="buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客-csdn博客_f">
        <meta name="csdn-baidu-search" content="{&quot;autorun&quot;:true,&quot;install&quot;:true,&quot;keyword&quot;:&quot;buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客-csdn博客_f&quot;}">
    <meta name="description" content="BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客-csdn博客_f">
        <link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/detail_enter-3ed6edfc90.min.css">
    <script type="application/ld+json">{"@context":"https://ziyuan.baidu.com/contexts/cambrian.jsonld","@id":"https://blog.csdn.net/qpeity/article/details/*********","appid":"1638831770136827","pubDate":"2022-11-05T12:32:11","title":"BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客","upDate":"2022-11-05T12:33:10"}</script>
        <link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/skin-blackboard-3adcc2c475.min.css">
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/jquery-1.9.1.min.js.下载" type="text/javascript"></script>
    <script type="text/javascript">
        var isCorporate = false;//注释删除enterprise
        var username =  "qpeity";
        var skinImg = "white";
        var blog_address = "https://blog.csdn.net/qpeity";
        var currentUserName = "pzn1022";
        var isOwner = false;
        var loginUrl = "http://passport.csdn.net/account/login?from=https://blog.csdn.net/qpeity/article/details/*********";
        var blogUrl = "https://blog.csdn.net/";
        var avatar = "https://profile-avatar.csdnimg.cn/6b2759cb62f743cf81d6a2848214a29c_qpeity.jpg!1";
        var articleTitle = "BUUCTF题目Misc部分wp（持续更新）";
        var articleDesc = "BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客-csdn博客_f";
        var articleTitles = "BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客";
        var nickName = "苦行僧(csdn)";
        var articleDetailUrl = "https://blog.csdn.net/qpeity/article/details/*********";
        if(window.location.host.split('.').length == 3) {
            blog_address = blogUrl + username;
        }
        var skinStatus = "White";
        var robotModule = '';
        var blogStaticHost = "https://csdnimg.cn/release/blogv2/"
        var mallTestStyle = "control"
    </script>
        <meta name="toolbar" content="{&quot;type&quot;:&quot;0&quot;,&quot;fixModel&quot;:&quot;1&quot;}">
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/saved_resource(1)" type="text/javascript"></script>
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/report.js.下载" type="text/javascript"></script>
    <link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/sandalstrap.min.css">
    <style>
        .MathJax, .MathJax_Message, .MathJax_Preview{
            display: none
        }
    </style>
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ds.js.下载"></script>
<link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-toolbar-default.css"><script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-notification.js.下载"></script><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/collection-box.css"><script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-login.js.下载"></script><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-tooltip.css"><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-medal.css"><script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/html2canvas.min.js.下载"></script><style></style><style type="text/css">.hljs-ln{border-collapse:collapse}            .hljs-ln td{padding:0}            .hljs-ln-n{text-align: right;padding-right: 8px;}            .hljs-ln-n:before{content:attr(data-line-number)}</style><style type="text/css">pre{position: relative}pre:hover .code-full-screen{display:none !important;}.code-full-screen{display: none !important;position: absolute;right: 4px;top: 3px;width: 24px !important;height: 24px !important;margin: 4px !important;}pre:hover .hljs-button{display: block}.hljs-button{display: none;position: absolute;right: 4px;top: 4px;font-size: 12px;color: #ffffff;background-color: #9999AA;padding: 2px 8px;margin: 8px;border-radius: 4px;cursor: pointer; box-shadow: 0 2px 4px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.05);}.hljs-button:after{content: attr(data-title)}code .hljs-button{margin: 2px 8px;}</style><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-accusation.css"><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-ordertip.css"><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/order-payment.css"><meta http-equiv="origin-trial" content="As0hBNJ8h++fNYlkq8cTye2qDLyom8NddByiVytXGGD0YVE+2CEuTCpqXMDxdhOMILKoaiaYifwEvCRlJ/9GcQ8AAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3MTk1MzI3OTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="AgRYsXo24ypxC89CJanC+JgEmraCCBebKl8ZmG7Tj5oJNx0cmH0NtNRZs3NB5ubhpbX/bIt7l2zJOSyO64NGmwMAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3MTk1MzI3OTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="As0hBNJ8h++fNYlkq8cTye2qDLyom8NddByiVytXGGD0YVE+2CEuTCpqXMDxdhOMILKoaiaYifwEvCRlJ/9GcQ8AAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3MTk1MzI3OTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><meta http-equiv="origin-trial" content="AgRYsXo24ypxC89CJanC+JgEmraCCBebKl8ZmG7Tj5oJNx0cmH0NtNRZs3NB5ubhpbX/bIt7l2zJOSyO64NGmwMAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3MTk1MzI3OTksImlzU3ViZG9tYWluIjp0cnVlfQ=="><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/side-toolbar.css"><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-login.css"><link rel="stylesheet" type="text/css" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-footer.css"><style type="text/css">.MathJax_Hover_Frame {border-radius: .25em; -webkit-border-radius: .25em; -moz-border-radius: .25em; -khtml-border-radius: .25em; box-shadow: 0px 0px 15px #83A; -webkit-box-shadow: 0px 0px 15px #83A; -moz-box-shadow: 0px 0px 15px #83A; -khtml-box-shadow: 0px 0px 15px #83A; border: 1px solid #A6D ! important; display: inline-block; position: absolute}
.MathJax_Menu_Button .MathJax_Hover_Arrow {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; -khtml-border-radius: 4px; font-family: 'Courier New',Courier; font-size: 9px; color: #F0F0F0}
.MathJax_Menu_Button .MathJax_Hover_Arrow span {display: block; background-color: #AAA; border: 1px solid; border-radius: 3px; line-height: 0; padding: 4px}
.MathJax_Hover_Arrow:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_Hover_Arrow:hover span {background-color: #CCC!important}
</style><style type="text/css">#MathJax_About {position: fixed; left: 50%; width: auto; text-align: center; border: 3px outset; padding: 1em 2em; background-color: #DDDDDD; color: black; cursor: default; font-family: message-box; font-size: 120%; font-style: normal; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; border-radius: 15px; -webkit-border-radius: 15px; -moz-border-radius: 15px; -khtml-border-radius: 15px; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_About.MathJax_MousePost {outline: none}
.MathJax_Menu {position: absolute; background-color: white; color: black; width: auto; padding: 2px; border: 1px solid #CCCCCC; margin: 0; cursor: default; font: menu; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; z-index: 201; box-shadow: 0px 10px 20px #808080; -webkit-box-shadow: 0px 10px 20px #808080; -moz-box-shadow: 0px 10px 20px #808080; -khtml-box-shadow: 0px 10px 20px #808080; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
.MathJax_MenuItem {padding: 2px 2em; background: transparent}
.MathJax_MenuArrow {position: absolute; right: .5em; padding-top: .25em; color: #666666; font-size: .75em}
.MathJax_MenuActive .MathJax_MenuArrow {color: white}
.MathJax_MenuArrow.RTL {left: .5em; right: auto}
.MathJax_MenuCheck {position: absolute; left: .7em}
.MathJax_MenuCheck.RTL {right: .7em; left: auto}
.MathJax_MenuRadioCheck {position: absolute; left: 1em}
.MathJax_MenuRadioCheck.RTL {right: 1em; left: auto}
.MathJax_MenuLabel {padding: 2px 2em 4px 1.33em; font-style: italic}
.MathJax_MenuRule {border-top: 1px solid #CCCCCC; margin: 4px 1px 0px}
.MathJax_MenuDisabled {color: GrayText}
.MathJax_MenuActive {background-color: Highlight; color: HighlightText}
.MathJax_MenuDisabled:focus, .MathJax_MenuLabel:focus {background-color: #E8E8E8}
.MathJax_ContextMenu:focus {outline: none}
.MathJax_ContextMenu .MathJax_MenuItem:focus {outline: none}
#MathJax_AboutClose {top: .2em; right: .2em}
.MathJax_Menu .MathJax_MenuClose {top: -10px; left: -10px}
.MathJax_MenuClose {position: absolute; cursor: pointer; display: inline-block; border: 2px solid #AAA; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; font-family: 'Courier New',Courier; font-size: 24px; color: #F0F0F0}
.MathJax_MenuClose span {display: block; background-color: #AAA; border: 1.5px solid; border-radius: 18px; -webkit-border-radius: 18px; -moz-border-radius: 18px; -khtml-border-radius: 18px; line-height: 0; padding: 8px 0 6px}
.MathJax_MenuClose:hover {color: white!important; border: 2px solid #CCC!important}
.MathJax_MenuClose:hover span {background-color: #CCC!important}
.MathJax_MenuClose:hover:focus {outline: none}
</style><style type="text/css">.MathJax_Preview .MJXf-math {color: inherit!important}
</style><style type="text/css">.MJX_Assistive_MathML {position: absolute!important; top: 0; left: 0; clip: rect(1px, 1px, 1px, 1px); padding: 1px 0 0 0!important; border: 0!important; height: 1px!important; width: 1px!important; overflow: hidden!important; display: block!important; -webkit-touch-callout: none; -webkit-user-select: none; -khtml-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none}
.MJX_Assistive_MathML.MJX_Assistive_MathML_Block {width: 100%!important}
</style><style type="text/css">#MathJax_Zoom {position: absolute; background-color: #F0F0F0; overflow: auto; display: block; z-index: 301; padding: .5em; border: 1px solid black; margin: 0; font-weight: normal; font-style: normal; text-align: left; text-indent: 0; text-transform: none; line-height: normal; letter-spacing: normal; word-spacing: normal; word-wrap: normal; white-space: nowrap; float: none; -webkit-box-sizing: content-box; -moz-box-sizing: content-box; box-sizing: content-box; box-shadow: 5px 5px 15px #AAAAAA; -webkit-box-shadow: 5px 5px 15px #AAAAAA; -moz-box-shadow: 5px 5px 15px #AAAAAA; -khtml-box-shadow: 5px 5px 15px #AAAAAA; filter: progid:DXImageTransform.Microsoft.dropshadow(OffX=2, OffY=2, Color='gray', Positive='true')}
#MathJax_ZoomOverlay {position: absolute; left: 0; top: 0; z-index: 300; display: inline-block; width: 100%; height: 100%; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
#MathJax_ZoomFrame {position: relative; display: inline-block; height: 0; width: 0}
#MathJax_ZoomEventTrap {position: absolute; left: 0; top: 0; z-index: 302; display: inline-block; border: 0; padding: 0; margin: 0; background-color: white; opacity: 0; filter: alpha(opacity=0)}
</style><style type="text/css">.MathJax_Preview {color: #888}
#MathJax_Message {position: fixed; left: 1em; bottom: 1.5em; background-color: #E6E6E6; border: 1px solid #959595; margin: 0px; padding: 2px 8px; z-index: 102; color: black; font-size: 80%; width: auto; white-space: nowrap}
#MathJax_MSIE_Frame {position: absolute; top: 0; left: 0; width: 0px; z-index: 101; border: 0px; margin: 0px; padding: 0px}
.MathJax_Error {color: #CC0000; font-style: italic}
</style><style type="text/css">.MJXp-script {font-size: .8em}
.MJXp-right {-webkit-transform-origin: right; -moz-transform-origin: right; -ms-transform-origin: right; -o-transform-origin: right; transform-origin: right}
.MJXp-bold {font-weight: bold}
.MJXp-italic {font-style: italic}
.MJXp-scr {font-family: MathJax_Script,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-frak {font-family: MathJax_Fraktur,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-sf {font-family: MathJax_SansSerif,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-cal {font-family: MathJax_Caligraphic,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-mono {font-family: MathJax_Typewriter,'Times New Roman',Times,STIXGeneral,serif}
.MJXp-largeop {font-size: 150%}
.MJXp-largeop.MJXp-int {vertical-align: -.2em}
.MJXp-math {display: inline-block; line-height: 1.2; text-indent: 0; font-family: 'Times New Roman',Times,STIXGeneral,serif; white-space: nowrap; border-collapse: collapse}
.MJXp-display {display: block; text-align: center; margin: 1em 0}
.MJXp-math span {display: inline-block}
.MJXp-box {display: block!important; text-align: center}
.MJXp-box:after {content: " "}
.MJXp-rule {display: block!important; margin-top: .1em}
.MJXp-char {display: block!important}
.MJXp-mo {margin: 0 .15em}
.MJXp-mfrac {margin: 0 .125em; vertical-align: .25em}
.MJXp-denom {display: inline-table!important; width: 100%}
.MJXp-denom > * {display: table-row!important}
.MJXp-surd {vertical-align: top}
.MJXp-surd > * {display: block!important}
.MJXp-script-box > *  {display: table!important; height: 50%}
.MJXp-script-box > * > * {display: table-cell!important; vertical-align: top}
.MJXp-script-box > *:last-child > * {vertical-align: bottom}
.MJXp-script-box > * > * > * {display: block!important}
.MJXp-mphantom {visibility: hidden}
.MJXp-munderover {display: inline-table!important}
.MJXp-over {display: inline-block!important; text-align: center}
.MJXp-over > * {display: block!important}
.MJXp-munderover > * {display: table-row!important}
.MJXp-mtable {vertical-align: .25em; margin: 0 .125em}
.MJXp-mtable > * {display: inline-table!important; vertical-align: middle}
.MJXp-mtr {display: table-row!important}
.MJXp-mtd {display: table-cell!important; text-align: center; padding: .5em 0 0 .5em}
.MJXp-mtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-mlabeledtr {display: table-row!important}
.MJXp-mlabeledtr > .MJXp-mtd:first-child {padding-left: 0}
.MJXp-mlabeledtr:first-child > .MJXp-mtd {padding-top: 0}
.MJXp-merror {background-color: #FFFF88; color: #CC0000; border: 1px solid #CC0000; padding: 1px 3px; font-style: normal; font-size: 90%}
.MJXp-scale0 {-webkit-transform: scaleX(.0); -moz-transform: scaleX(.0); -ms-transform: scaleX(.0); -o-transform: scaleX(.0); transform: scaleX(.0)}
.MJXp-scale1 {-webkit-transform: scaleX(.1); -moz-transform: scaleX(.1); -ms-transform: scaleX(.1); -o-transform: scaleX(.1); transform: scaleX(.1)}
.MJXp-scale2 {-webkit-transform: scaleX(.2); -moz-transform: scaleX(.2); -ms-transform: scaleX(.2); -o-transform: scaleX(.2); transform: scaleX(.2)}
.MJXp-scale3 {-webkit-transform: scaleX(.3); -moz-transform: scaleX(.3); -ms-transform: scaleX(.3); -o-transform: scaleX(.3); transform: scaleX(.3)}
.MJXp-scale4 {-webkit-transform: scaleX(.4); -moz-transform: scaleX(.4); -ms-transform: scaleX(.4); -o-transform: scaleX(.4); transform: scaleX(.4)}
.MJXp-scale5 {-webkit-transform: scaleX(.5); -moz-transform: scaleX(.5); -ms-transform: scaleX(.5); -o-transform: scaleX(.5); transform: scaleX(.5)}
.MJXp-scale6 {-webkit-transform: scaleX(.6); -moz-transform: scaleX(.6); -ms-transform: scaleX(.6); -o-transform: scaleX(.6); transform: scaleX(.6)}
.MJXp-scale7 {-webkit-transform: scaleX(.7); -moz-transform: scaleX(.7); -ms-transform: scaleX(.7); -o-transform: scaleX(.7); transform: scaleX(.7)}
.MJXp-scale8 {-webkit-transform: scaleX(.8); -moz-transform: scaleX(.8); -ms-transform: scaleX(.8); -o-transform: scaleX(.8); transform: scaleX(.8)}
.MJXp-scale9 {-webkit-transform: scaleX(.9); -moz-transform: scaleX(.9); -ms-transform: scaleX(.9); -o-transform: scaleX(.9); transform: scaleX(.9)}
.MathJax_PHTML .noError {vertical-align: ; font-size: 90%; text-align: left; color: black; padding: 1px 3px; border: 1px solid}
</style><script async="" src="https://fundingchoicesmessages.google.com/i/ca-pub-1076724771190722?ers=2"></script></head>
  <body class="nodata " style=""><div id="MathJax_Message" style="display: none;"></div>
    <div id="toolbarBox" style="min-height: 48px;"><div id="csdn-toolbar" style="position: relative; min-width: 100%; width: max-content; top: 0px; left: 0px;">
                    <div class="toolbar-inside exp3">
                      <div class="toolbar-container">
                        <div class="toolbar-container-left">
                          <div class="toolbar-logo toolbar-subMenu-box csdn-toolbar-fl"><a data-report-click="{&quot;spm&quot;:&quot;3001.4476&quot;}" data-report-query="spm=3001.4476" href="https://www.csdn.net/"><img title="CSDN首页" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20201124032511.png"></a>
                    </div>
                          <ul class="toolbar-menus csdn-toolbar-fl"><li class="active " title="阅读深度、前沿文章">
                                <a data-report-click="{&quot;mod&quot;:&quot;popu_336&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/&quot;,&quot;spm&quot;:&quot;3001.4477&quot;}" data-report-query="spm=3001.4477" href="https://blog.csdn.net/">
                                  博客
                                  
                                  
                                </a>
                                
                                
                              </li><li class="" title="高价值源码课程分享">
                                <a data-report-click="{&quot;mod&quot;:&quot;popu_336&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/&quot;,&quot;spm&quot;:&quot;3001.6907&quot;}" data-report-query="spm=3001.6907" href="https://download.csdn.net/">
                                  下载
                                  
                                  
                                </a>
                                
                                
                              </li><li class="" title="系统学习·问答·比赛">
                                <a data-report-click="{&quot;mod&quot;:&quot;popu_336&quot;,&quot;dest&quot;:&quot;https://edu.csdn.net/&quot;,&quot;spm&quot;:&quot;3001.4482&quot;}" data-report-query="spm=3001.4482" href="https://edu.csdn.net/">
                                  学习
                                  
                                  
                                </a>
                                
                                
                              </li><li class="" title="找到志同道合的伙伴">
                                <a data-report-click="{&quot;mod&quot;:&quot;popu_336&quot;,&quot;dest&quot;:&quot;https://bbs.csdn.net/&quot;,&quot;spm&quot;:&quot;3001.6068&quot;}" data-report-query="spm=3001.6068" href="https://bbs.csdn.net/">
                                  社区
                                  
                                  
                                </a>
                                
                                
                              </li>falsefalse<li class="" title="开源代码托管">
                                <a data-report-click="{&quot;mod&quot;:&quot;&quot;,&quot;dest&quot;:&quot;https://gitcode.net?utm_source=csdn_toolbar&quot;,&quot;spm&quot;:&quot;3001.6768&quot;}" data-report-query="spm=3001.6768" href="https://gitcode.net/?utm_source=csdn_toolbar">
                                  GitCode
                                  
                                  
                                </a>
                                
                                
                              </li><li class="" title="让你的灵感立即落地">
                                <a data-report-click="{&quot;mod&quot;:&quot;popu_336&quot;,&quot;dest&quot;:&quot;https://inscode.csdn.net?utm_source=260232576&quot;,&quot;spm&quot;:&quot;3001.8290&quot;}" data-report-query="spm=3001.8290" href="https://inscode.csdn.net/?utm_source=260232576">
                                  InsCode
                                  
                                  
                                </a>
                                
                                
                              </li></ul>
                        </div>
                        <div class="toolbar-container-middle">
                        <div class="toolbar-search onlySearch"><div class="toolbar-search-container">
                    <span class="icon-fire" style="display: block;"></span>
                    <input id="toolbar-search-input" maxlength="2000" autocomplete="off" type="text" value="" placeholder="BUUCTF" style="text-indent: 32px;"><div class="gradient"></div>
                    <button id="toolbar-search-button"><i></i><span>搜索</span></button>
                    <input type="password" autocomplete="new-password" readonly="" disabled="true" style="display: none; position:absolute;left:-9999999px;width:0;height:0;">
                  </div></div></div>
                        <div class="toolbar-container-right">
                          <div class="toolbar-btns onlyUser"><div class="toolbar-btn toolbar-btn-login toolbar-btn-login-new csdn-toolbar-fl toolbar-subMenu-box">
          <a class="hasAvatar" data-report-click="{&quot;spm&quot;: &quot;3001.5343&quot;}" data-report-query="spm=3001.5343" href="https://blog.csdn.net/pzn1022"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9a0a7257a1fc4f6e8e046123a4a5c58f_pzn1022.jpg!2"></a>
          <div id="csdn-toolbar-profile" class="csdn-toolbar-plugin">
            <div class="csdn-profile-top">
              <a class="csdn-profile-avatar" data-report-click="{&quot;spm&quot;: &quot;3001.5343&quot;}" data-report-query="spm=3001.5343" href="https://blog.csdn.net/pzn1022"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/default.jpg!3"></a>
              <p class="csdn-profile-nickName">--</p>
              <a data-report-click="{&quot;spm&quot;: &quot;3001.5344&quot;}" data-report-query="spm=3001.5344" href="https://mall.csdn.net/vip" class="csdn-profile-no-vip"></a>
            </div>
            <div class="csdn-profile-mid">
              <a data-report-click="{&quot;spm&quot;: &quot;3001.5347&quot;}" data-report-query="spm=3001.5347" href="https://blog.csdn.net/pzn1022?type=sub&amp;subType=fans"><i class="csdn-profile-fansCount">--</i>粉丝</a>
              <a data-report-click="{&quot;spm&quot;: &quot;3001.5348&quot;}" data-report-query="spm=3001.5348" href="https://blog.csdn.net/pzn1022?type=sub"><i class="csdn-profile-followCount">--</i>关注</a>
              <a data-report-click="{&quot;spm&quot;: &quot;3001.5349&quot;}" data-report-query="spm=3001.5349" href="https://blog.csdn.net/pzn1022"><i class="csdn-profile-likeCount">--</i>获赞</a>
            </div>
            <div class="csdn-profile-bottom">
              <ul class="csdn-border-bottom">
                <li class=""><a href="https://i.csdn.net/#/user-center/profile" data-report-click="{&quot;dest&quot;:&quot;https://i.csdn.net/#/user-center/profile&quot;,&quot;spm&quot;:&quot;3001.5111&quot;}" data-report-query="spm=3001.5111"><i class="csdn-profile-icon csdn-profile-icon-person"></i>个人中心</a></li><li class=""><a href="https://mp.csdn.net/mp_blog/manage/article?spm=1011.2124.3001.5298" data-report-click="{&quot;dest&quot;:&quot;https://mp.csdn.net/mp_blog/manage/article?spm=1011.2124.3001.5298&quot;,&quot;spm&quot;:&quot;3001.5448&quot;}" data-report-query="spm=3001.5448"><i class="csdn-profile-icon csdn-profile-icon-pages"></i>内容管理</a></li><li class=""><a href="https://edu.csdn.net/?utm_source=edu_txxl_mh" data-report-click="{&quot;dest&quot;:&quot;https://edu.csdn.net?utm_source=edu_txxl_mh&quot;,&quot;spm&quot;:&quot;3001.5350&quot;}" data-report-query="spm=3001.5350"><i class="csdn-profile-icon csdn-profile-icon-study"></i>我的学习</a></li><li class=""><a href="https://mall.csdn.net/myorder" data-report-click="{&quot;dest&quot;:&quot;https://mall.csdn.net/myorder&quot;,&quot;spm&quot;:&quot;3001.5137&quot;}" data-report-query="spm=3001.5137"><i class="csdn-profile-icon csdn-profile-icon-order"></i>我的订单</a></li><li class=""><a href="https://i.csdn.net/#/wallet/index" data-report-click="{&quot;dest&quot;:&quot;https://i.csdn.net/#/wallet/index&quot;,&quot;spm&quot;:&quot;3001.5136&quot;}" data-report-query="spm=3001.5136"><i class="csdn-profile-icon csdn-profile-icon-wallet"></i>我的钱包</a></li><li class=""><a href="https://dev-portal.csdn.net/welcome?utm_source=toolbar_user_profile" data-report-click="{&quot;dest&quot;:&quot;https://dev-portal.csdn.net/welcome?utm_source=toolbar_user_profile&quot;,&quot;spm&quot;:&quot;3001.7345&quot;}" data-report-query="spm=3001.7345"><i class="csdn-profile-icon csdn-profile-icon-API"></i>我的云服务</a></li><li class="pb-8 csdn-border-bottom"><a href="https://upload.csdn.net/level?utm_source=xz_pc_txxl" data-report-click="{&quot;dest&quot;:&quot;https://upload.csdn.net/level?utm_source=xz_pc_txxl&quot;,&quot;spm&quot;:&quot;3001.7346&quot;}" data-report-query="spm=3001.7346"><i class="csdn-profile-icon csdn-profile-icon-ac"></i>我的等级</a></li><li class="pt-8 pb-8 csdn-border-bottom"><a href="https://i.csdn.net/#/uc/reward" data-report-click="{&quot;dest&quot;:&quot;https://i.csdn.net/#/uc/reward&quot;,&quot;spm&quot;:&quot;3001.5351&quot;}" data-report-query="spm=3001.5351"><i class="csdn-profile-icon csdn-profile-icon-draw"></i>签到抽奖</a></li><li class="pt-8 csdn-profile-logout"><a href="javascript:;" data-report-click="{&quot;spm&quot;:&quot;3001.5139&quot;}" data-report-query="spm=3001.5139"><i class="csdn-profile-icon csdn-profile-icon-logout"></i>退出</a></li>
              </ul>
            </div>
          </div></div>
          <div class="toolbar-btn toolbar-btn-vip csdn-toolbar-fl">
            <a rel="nofollow" data-report-click="{&quot;mod&quot;:&quot;popu_336&quot;,&quot;dest&quot;:&quot;https://mall.csdn.net/vip&quot;,&quot;spm&quot;:&quot;3001.4496&quot;}" data-report-query="spm=3001.4496" href="https://mall.csdn.net/vip">
              会员中心 <img style="position: relative; vertical-align: middle; width: 14px; top: -2px; left: 0px;;display:inline-block" "="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210918025138.gif">
            </a>
          </div>
          <div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">
              <div class="toolbar-subMenu-box">
                <a rel="nofollow" data-report-click="{&quot;spm&quot;:&quot;3001.4508&quot;}" data-report-query="spm=3001.4508" id="toolbar-remind" href="https://i.csdn.net/#/msg/index"><span class="pos-rel">消息<i class="toolbar-msg-count"></i></span></a>
              <div class="toolbar-subMenu">
                          <a rel="nofollow" data-type="comment" href="https://i.csdn.net/#/msg/index"><span class="pos-rel">评论和@</span></a>
                          <a rel="nofollow" data-type="attention" href="https://i.csdn.net/#/msg/attention"><span class="pos-rel">新增粉丝</span></a>         
                          <a rel="nofollow" data-type="like" href="https://i.csdn.net/#/msg/like"><span class="pos-rel">赞和收藏</span></a>
                          <a rel="nofollow" data-type="chat" href="https://im.csdn.net/im/main.html"><span class="pos-rel">私信<i></i></span></a>
                          <a rel="nofollow" data-type="notice" href="https://i.csdn.net/#/msg/notice"><span class="pos-rel">系统通知<i></i></span></a>
                          <a rel="nofollow" href="https://i.csdn.net/#/msg/setting">消息设置</a>
                     </div></div>
              <div class="toolbar-msg-box"></div>
            </div>
          <div class="toolbar-btn toolbar-btn-collect csdn-toolbar-fl">
            <a rel="nofollow" data-report-click="{&quot;spm&quot;:&quot;3001.7480&quot;}" data-report-query="spm=3001.7480" href="https://i.csdn.net/#/user-center/history">历史</a>
          
          <div id="csdn-toolbar-collection" class="csdn-toolbar-plugin" style="display: none;">
          <div class="toolbar-collection-left csdn-toolbar-scroll-box">
          <ul>
          <li class="collection-folder-active">
            <div class="toolbar-collection-folder-name">浏览历史</div>
            
          </li>
        
          <li>
            <div class="toolbar-collection-folder-name">默认收藏夹</div>
             <div class="toolbar-collection-folder-count">0</div>
          </li>
        </ul>
        </div>
            
            <div class="toolbar-collection-right">
              <ul class="csdn-toolbar-scroll-box">
          <li>
            <a rel="nofollow" href="https://download.csdn.net/download/qq_45290991/85161043" target="_blank">
              <span class="toolbar-collection-type">下载</span>
              <span class="toolbar-collection-title">ctfweb题型总结大全（例题wp都有）</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/qpeity/article/details/*********" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">BUUCTF题目Misc部分wp（持续更新）</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/Aluxian_/article/details/125831308" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">CTFShow-MISC入门篇详细wp(1-56)</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/weixin_52450702/article/details/128847478" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">【CTF】ctf中用到的php伪协议总结及例题（持续更）</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/weixin_62808713/article/details/129909977" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">Web漏洞-文件包含漏洞超详细全解（附实例）</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/ing_end/article/details/123886703" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">文件包含(CTF)</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/redglare/article/details/127456686" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">ctf攻防渗透-文件包含-文件包含漏洞详解</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/qq_33137821/article/details/130452020" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">文件上传漏洞</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/qq_45751902/article/details/124039951" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">CTFHub-文件上传</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/qq_36495104/article/details/107071361" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">一些CTF 做题的tricks</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/JBlock/article/details/88311388" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">CTF命令执行及绕过技巧</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/solitudi/article/details/113588692" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">[CTF]PHP反序列化总结</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/qq_42383069/article/details/130221725" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">CTF之命令执行常见绕过</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/V1040375575/article/details/111646602" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">[CTF]php反序列化(unserialize)利用</span>
            </a>
          </li>
        
          <li>
            <a rel="nofollow" href="https://blog.csdn.net/weixin_44867191/article/details/130702472" target="_blank">
              <span class="toolbar-collection-type">博客</span>
              <span class="toolbar-collection-title">史上最详细sqlmap入门教程</span>
            </a>
          </li>
        </ul><a rel="nofollow" class="toolbar-collection-more">查看更多<i></i></a>
            </div>
            <div class="csdn-toolbar-plugin-triangle"></div>
          </div>
        </div>
          <div class="toolbar-btn toolbar-btn-mp csdn-toolbar-fl">
            <a rel="nofollow" title="创作中心" data-report-click="{&quot;dest&quot;:&quot;https://mp.csdn.net/&quot;,&quot;spm&quot;:&quot;3001.8539&quot;}" data-report-query="spm=3001.8539" href="https://mp.csdn.net/">
              创作中心
            </a>
          
        <div class="csdn-toolbar-creative-mp" style="left: -116px;">
          <a href="https://mp.csdn.net/edit" data-report-query="spm=3001.9762" data-report-click="{&quot;spm&quot;:&quot;3001.9762&quot;,&quot;extra&quot;:{&quot;dataType&quot;:1}}"><img class="csdn-toolbar-creative-mp-bg" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20230905113813.png" alt=""></a> 
          <img class="csdn-toolbar-creative-mp-close" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20230815023238.png" alt="">
        </div>
      </div>
          <div class="toolbar-btn toolbar-btn-write toolbar-btn-write-new csdn-toolbar-fl toolbar-subMenu-box"><a rel="nofollow" data-report-click="{&quot;spm&quot;:&quot;3001.4503&quot;,&quot;extra&quot;:{&quot;dataType&quot;:&quot;&quot;}}" data-report-query="spm=3001.4503" href="https://mp.csdn.net/edit">发布</a></div>
        </div>
                        </div>
                      </div>
                    </div>
                  </div></div>
        <script>
            var toolbarSearchExt = '{"landingWord":["buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客-csdn博客_f"],"queryWord":"","tag":["CTF","BUUCTF","MISC"],"title":"BUUCTF题目Misc部分wp（持续更新）"}';
        </script>
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-toolbar.js.下载" type="text/javascript"></script>
    <script>
    (function(){
        var bp = document.createElement('script');
        var curProtocol = window.location.protocol.split(':')[0];
        if (curProtocol === 'https') {
            bp.src = 'https://zz.bdstatic.com/linksubmit/push.js';
        }
        else {
            bp.src = 'http://push.zhanzhang.baidu.com/push.js';
        }
        var s = document.getElementsByTagName("script")[0];
        s.parentNode.insertBefore(bp, s);
    })();
    </script>

    <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/blog_code-01256533b5.min.css">
    <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/chart-3456820cac.css">
    <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/swiper.css">
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/swiper.js.下载" async=""></script>
    <script>
      var articleId = *********;
      var commentscount = 0;
      var curentUrl = "https://blog.csdn.net/qpeity/article/details/*********";
      var myUrl = "https://my.csdn.net/";
        var highlight = ["buuctf","csdn","misc","ctf","苦行僧","更新","持续","博客","题目","wp","专题","部分"];//高亮数组
        var isRecommendModule = true;
          var isBaiduPre = true;
          var baiduCount = 2;
          var setBaiduJsCount = 10;
      var share_card_url = "https://app-blog.csdn.net/share?article_id=*********&username=qpeity"
      var articleType = 1;
      var baiduKey = "buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客-csdn博客_f";
      var copyPopSwitch = true;
      var needInsertBaidu = true;
      var recommendRegularDomainArr = ["blog.csdn.net/.+/article/details/","download.csdn.net/download/","edu.csdn.net/course/detail/","ask.csdn.net/questions/","bbs.csdn.net/topics/","www.csdn.net/gather_.+/"]
      var codeStyle = "atom-one-dark";
      var baiduSearchType = "baidulandingword";
      var sharData = "{\"hot\":[{\"id\":1,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a5f4260710904e538002a6ab337939b3.png\"},{\"id\":2,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/188b37199a2c4b74b1d9ffc39e0d52de.png\"},{\"id\":3,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/14ded358b631444581edd98a256bc5af.png\"},{\"id\":4,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1470f23a770444d986ad551b9c33c5be.png\"},{\"id\":5,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c329f5181dc74f6c9bd28c982bb9f91d.png\"},{\"id\":6,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ccd8a3305e81460f9c505c95b432a65f.png\"},{\"id\":7,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/bc89d8283389440d97fc4d30e30f45e1.png\"},{\"id\":8,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/452d485b4a654f5592390550d2445edf.png\"},{\"id\":9,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f8b9939db2ed474a8f43a643015fc8b7.png\"},{\"id\":10,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/6de8864187ab4ed3b1db0856369c36ff.png\"},{\"id\":11,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/673cc3470ff74072acba958dc0c46e2d.png\"},{\"id\":12,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/930c119760ac4491804db80f9c6d4e3f.png\"},{\"id\":13,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/15e6befb05a24233bc2b65e96aa8d972.png\"},{\"id\":14,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2075fd6822184b95a41e214de4daec13.png\"},{\"id\":15,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/859b1552db244eb6891a809263a5c657.png\"},{\"id\":16,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/0be2f920f1f74290a98921974a9613fd.png\"},{\"id\":17,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2e97e00b43f14afab494ea55ef3f4a6e.png\"},{\"id\":18,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ff4ab252f46e444686f5135d6ebbfec0.png\"},{\"id\":19,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/ae029bbe99564e79911657912d36524f.png\"},{\"id\":20,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b3ece39963de440388728e9e7b9bf427.png\"},{\"id\":21,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/6f14651a99ba486e926d63b6fa692997.png\"},{\"id\":22,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/83ceddf050084875a341e32dcceca721.png\"},{\"id\":23,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b90368b8fd5d4c6c8c79a707d877cf7c.png\"},{\"id\":24,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/aeffae14ecf14e079b2616528c9a393b.png\"},{\"id\":25,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c5a06b5a13d44d16bed868fc3384897a.png\"},{\"id\":26,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/08b697658b844b318cea3b119e9541ef.png\"},{\"id\":27,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/68ccb0b8d09346ac961d2b5c1a8c77bf.png\"},{\"id\":28,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a2227a247e37418cbe0ea972ba6a859b.png\"},{\"id\":29,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/3a42825fede748f9993e5bb844ad350d.png\"},{\"id\":30,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/8882abc1dd484224b636966ea38555c3.png\"},{\"id\":31,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/4f6a5f636a3e444d83cf8cc06d87a159.png\"},{\"id\":32,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1953ef79c56b4407b78d7181bdff11c3.png\"},{\"id\":33,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c04a2a4f772948ed85b5b0380ed36287.png\"},{\"id\":34,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/5b4fecd05091405ea04d8c0f53e9f2c7.png\"},{\"id\":35,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b89f576d700344e280d6ceb2a66c2420.png\"},{\"id\":36,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/1c65780e11804bbd9971ebadb3d78bcf.png\"},{\"id\":37,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/d590db2055f345db9706eb68a7ec151a.png\"},{\"id\":38,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/fe602f80700b4f6fb3c4a9e4c135510e.png\"},{\"id\":39,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/39ff2fcd31e04feba301a071976a0ba7.png\"},{\"id\":40,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f9b61b3d113f436b828631837f89fb39.png\"},{\"id\":41,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/df1aca5f610c4ad48cd16da88c9c8499.png\"},{\"id\":42,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/d7acf73a1e6b41399a77a85040e10961.png\"},{\"id\":43,\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/b7f1b63542524b97962ff649ab4e7e23.png\"}],\"vip\":[{\"id\":1,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101150.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101154.png\"},{\"id\":2,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101204.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101208.png\"},{\"id\":3,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101211.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101215.png\"},{\"id\":4,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101218.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101220.png\"},{\"id\":5,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101223.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220920101226.png\"},{\"id\":6,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100635.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100639.png\"},{\"id\":7,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100642.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100644.png\"},{\"id\":8,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100647.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100649.png\"},{\"id\":9,\"vipUrl\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100652.png\",\"url\":\"https:\\/\\/img-home.csdnimg.cn\\/images\\/20220922100655.png\"},{\"id\":10,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/55de67481fde4b04b97ad78f11fe369a.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/bb2418fb537e4d78b10d8765ccd810c5.png\"},{\"id\":11,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/579c713394584d128104ef1044023954.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/f420d9fbcf5548079d31b5e809b6d6cd.png\"},{\"id\":12,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/75b7f3155ba642f5a4cc16b7baf44122.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a9030f5877be401f8b340b80b0d91e64.png\"},{\"id\":13,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/0903d33cafa54934be3780aa54ae958d.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/2cd8c8929f5a42fca5da2a0aeb456203.png\"},{\"id\":14,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/949fd7c22884439fbfc3c0e9c3b8dee7.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/dafbea9bd9eb4f3b962b48dc41657f89.png\"},{\"id\":15,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/4119cfddd71d4e6a8a27a18dbb74d90e.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/c56310c8b6384d9e85388e4e342ce508.png\"},{\"id\":16,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/121575274da142bcbbbbc2e8243dd411.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/5013993de06542f881018bb9abe2edf7.png\"},{\"id\":17,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/4d97aa6dd4fe4f09a6bef5bdf8a6abcd.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/76f23877b6ad4066ad45ce8e31b4b977.png\"},{\"id\":18,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/fdb619daf21b4c829de63b9ebc78859d.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/a1abe5d27a5441f599adfe662f510243.png\"},{\"id\":19,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/676b7707bb11410f8f56bc0ed2b2345c.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/7ac5b467fbf24e1d8c2de3f3332c4f54.png\"},{\"id\":20,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/0becb8cc227e4723b765bdd69a20fd4a.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/fdec85b26091486b9a89d0b8d45c3749.png\"},{\"id\":21,\"vipUrl\":\"https:\\/\\/img-blog.csdnimg.cn\\/1a6c06235ad44941b38c54cbc25a370c.png\",\"url\":\"https:\\/\\/img-blog.csdnimg.cn\\/410a06cda2d44b0c84578f88275caf70.png\"}],\"map\":{\"hot\":\"热门\",\"vip\":\"VIP\"}}";
      
      var canRead = true;
      var blogMoveHomeArticle = false;
      var showSearchText = "";
      var articleSource = 1;
      var articleReport = '{"spm":"1001.2101","extra":{"titAb":"control-1","lvab":"t_new"},"pid":"blog"}';
        var baiduSearchChannel = 'pc_relevant'
        var baiduSearchIdentification = '.235^v38^pc_relevant_sort'
        var distRequestId = '1697120942896_75973'
        var initRewardObject = {
          giver: "pzn1022",
          anchor: "qpeity",
          articleId: "*********",
          sign: "58125b1a5c7d3cae3f5032baba1f8be3",
        }
        var isLikeStatus = false;
        var isUnLikeStatus = false;
        var studyLearnWord = "";
        var isCurrentUserVip = false;
        var contentViewsHeight = 0;
        var contentViewsCount = 0;
        var contentViewsCountLimit = 5;
        var isShowConcision = true
      var isCookieConcision = false
      var isHasDirectoryModel = false
      var isShowSideModel = false
      var isShowDirectoryModel = true
      function getCookieConcision(sName){
        var allCookie = document.cookie.split("; ");
        for (var i=0; i < allCookie.length; i++){
          var aCrumb = allCookie[i].split("=");
          if (sName == aCrumb[0])
            return aCrumb[1];
        }
        return null;
      }
      if (getCookieConcision('blog_details_concision') && getCookieConcision('blog_details_concision') == 0){
        isCookieConcision = true
        isShowSideModel = true
        isShowDirectoryModel = false
      }
    </script>
        <div class="main_father clearfix d-flex justify-content-center" style="height: auto !important;">
          <div class="container clearfix" id="mainBox">
          <script>
          if (!isCookieConcision) {
            $('.main_father').removeClass('mainfather-concision')
            $('.main_father .container').removeClass('container-concision')
          }
          </script>
          <main>
<script type="text/javascript">
    var resourceId =  "";
    function getQueryString(name) {   
      var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象  
      var r = window.location.search.substr(1).match(reg);  //匹配目标参数
      if( r != null ) return decodeURIComponent( r[2] ); return '';   
    }
    function stripscript(s){ 
      var pattern = new RegExp("[`~!@#$^&*()=|{}':;',\\[\\].<>/?~！@#￥……&*（）——|{}【】‘；：”“'。，、？%]") 
      var rs = ""; 
      for (var i = 0; i < s.length; i++) { 
        rs = rs+s.substr(i, 1).replace(pattern, ''); 
      } 
      return rs;
    }
    var blogHotWords = stripscript(getQueryString('utm_term')).length > 1 ? stripscript(getQueryString('utm_term')) : ''
</script>
<div class="blog-content-box">
    <div class="article-header-box">
        <div class="article-header">
            <div class="article-title-box">
                <h1 class="title-article" id="articleContentId">BUUCTF题目Misc部分wp（持续更新）</h1>
            </div>
            <div class="article-info-box">
                <div class="article-bar-top">
                    <img class="article-type-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/original.png" alt="">
                    <div class="bar-content">
                      <a class="follow-nickName " href="https://blog.csdn.net/qpeity" target="_blank" rel="noopener" title="苦行僧(csdn)">苦行僧(csdn)</a>
                    <img class="article-time-img article-heard-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newUpTime2.png" alt="">
                    <span class="time">已于&nbsp;2023-09-19 00:25:23&nbsp;修改</span>
                    <img class="article-read-img article-heard-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/articleReadEyes2.png" alt="">
                    <span class="read-count">阅读量990</span>
                    <a id="blog_detail_zk_collection" class="un-collection" data-report-click="{&quot;mod&quot;:&quot;popu_823&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4232&quot;,&quot;ab&quot;:&quot;new&quot;}">
                        <img class="article-collect-img article-heard-img un-collect-status isdefault" style="display:inline-block" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/tobarCollect2.png" alt="">
                        <img class="article-collect-img article-heard-img collect-status isactive" style="display:none" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/tobarCollectionActive2.png" alt="">
                        <span class="name">收藏</span>
                        <span class="get-collection" style="color: rgb(153, 154, 170);">
                            3
                        </span>
                    </a>
                      <img class="article-read-img article-heard-img" style="display:none" id="is-like-imgactive-new" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newHeart2023Active.png" alt="">
                      <img class="article-read-img article-heard-img" style="display:block" id="is-like-img-new" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newHeart2023Black.png" alt="">
                      <span class="read-count" id="blog-digg-num">点赞数
                      </span>
                    </div>
                </div>
                <div class="blog-tags-box">
                    <div class="tags-box artic-tag-box">
                            <span class="label">分类专栏：</span>
                                <a class="tag-link" href="https://blog.csdn.net/qpeity/category_10883970.html" target="_blank" rel="noopener">信息安全</a>
                                <a class="tag-link" href="https://blog.csdn.net/qpeity/category_12089820.html" target="_blank" rel="noopener"># BUUCTF</a>
                            <span class="label">文章标签：</span>
                                <a rel="nofollow" data-report-click="{&quot;mod&quot;:&quot;popu_626&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4223&quot;,&quot;strategy&quot;:&quot;CTF&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;CTF\&quot;}&quot;}" class="tag-link" href="https://so.csdn.net/so/search/s.do?q=CTF&amp;t=all&amp;o=vip&amp;s=&amp;l=&amp;f=&amp;viparticle=" target="_blank">CTF</a>
                                <a rel="nofollow" data-report-click="{&quot;mod&quot;:&quot;popu_626&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4223&quot;,&quot;strategy&quot;:&quot;BUUCTF&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;BUUCTF\&quot;}&quot;}" class="tag-link" href="https://so.csdn.net/so/search/s.do?q=BUUCTF&amp;t=all&amp;o=vip&amp;s=&amp;l=&amp;f=&amp;viparticle=" target="_blank">BUUCTF</a>
                                <a rel="nofollow" data-report-click="{&quot;mod&quot;:&quot;popu_626&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4223&quot;,&quot;strategy&quot;:&quot;MISC&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;MISC\&quot;}&quot;}" class="tag-link" href="https://so.csdn.net/so/search/s.do?q=MISC&amp;t=all&amp;o=vip&amp;s=&amp;l=&amp;f=&amp;viparticle=" target="_blank">MISC</a>
                    </div>
                </div>
                <div class="up-time"><span>于&nbsp;2022-11-05 12:33:10&nbsp;首次发布</span></div>
                <div class="slide-content-box">
                    <div class="article-copyright">
                        <div class="creativecommons">
                            版权声明：本文为博主原创文章，遵循<a href="http://creativecommons.org/licenses/by-sa/4.0/" target="_blank" rel="noopener"> CC 4.0 BY-SA </a>版权协议，转载请附上原文出处链接和本声明。
                        </div>
                        <div class="article-source-link">
                            本文链接：<a href="https://blog.csdn.net/qpeity/article/details/*********" target="_blank">https://blog.csdn.net/qpeity/article/details/*********</a>
                        </div>
                    </div>
                </div>
                
                <div class="operating">
                    <a class="href-article-edit slide-toggle">版权</a>
                </div>
            </div>
        </div>
    </div>
    
        <div id="blogColumnPayAdvert">
            <div class="column-group">
                <div class="column-group-item column-group0 ">
                    <div class="item-l">
                        <a class="item-target" href="https://blog.csdn.net/qpeity/category_10883970.html" target="_blank" title="信息安全" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.6332&quot;}">
                            <img class="item-target" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210315134731469.png" alt="">
                            <span class="title item-target">
                                <span>
                                <span class="tit">信息安全</span>
                                    <span class="dec more">同时被 2 个专栏收录<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newArrowDown1White.png" alt=""></span>
                                </span>
                            </span>
                        </a>
                    </div>
                    <div class="item-m">
                        <span>14 篇文章</span>
                        <span>3 订阅</span>
                    </div>
                    <div class="item-r">
                            <a class="item-target article-column-bt articleColumnFreeBt" data-id="10883970">订阅专栏</a>
                    </div>
                </div>
                <div class="column-group-item column-group1 ">
                    <div class="item-l">
                        <a class="item-target" href="https://blog.csdn.net/qpeity/category_12089820.html" target="_blank" title="BUUCTF" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.6332&quot;}">
                            <img class="item-target" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/935c8559ed9741b0bef8f6774cc70659.jpeg" alt="">
                            <span class="title item-target">
                                <span>
                                <span class="tit">BUUCTF</span>
                                </span>
                            </span>
                        </a>
                    </div>
                    <div class="item-m">
                        <span>4 篇文章</span>
                        <span>0 订阅</span>
                    </div>
                    <div class="item-r">
                            <a class="item-target article-column-bt articleColumnFreeBt" data-id="12089820">订阅专栏</a>
                    </div>
                </div>
            </div>
        </div>
    <article class="baidu_pl">
        <div id="article_content" class="article_content clearfix">
        <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/kdoc_html_views-1a98987dfd.css">
        <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ck_htmledit_views-dc4a025e85.css">
                <div id="content_views" class="htmledit_views">
                    <h2><a name="t0"></a>伟大的司令官（题目原名称不能发）</h2> 
<p>题目是 伟大的司令官（题目原名称不能发）.gif，用<a href="https://so.csdn.net/so/search?q=Stegsolve&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=Stegsolve&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;Stegsolve\&quot;}&quot;}" data-tit="Stegsolve" data-pretit="stegsolve">Stegsolve</a>逐个frame查看，第21、51、79 frame拼起来是 flag{he11ohongke}</p> 
<p><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/76879c264a45423d99cc6dd937db17ff.png" width="118"><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/88b3d0f25fb74b998f7096938fe761ba.png" width="118"><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/eda933090ea54596a1857e04f7581a91.png" width="118"></p> 
<h2 id="%5BUTCTF2020%5Dsstv%C2%A0"><a name="t1"></a>[UTCTF2020]sstv&nbsp;</h2> 
<p>慢扫描TV工具，不知道这个工具就GG了。Kali上安装qsstv，选择Options-Sound-From file，然后选择附件 attachment.wav ，然后等待图像出现。 flag{6bdfeac1e2baa12d6ac5384cdfd166b0}</p> 
<p><img alt="" height="188" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5d3580ba0bcc495faea96c18f831dd3f.png" width="288"></p> 
<h2 id="%E4%BA%8C%E7%BB%B4%E7%A0%81%C2%A0"><a name="t2"></a>二维码&nbsp;</h2> 
<p>题目附件是个压缩包，没密码。解压是个QR_code.png，扫码得到secret is here。</p> 
<p><a href="https://so.csdn.net/so/search?q=binwalk&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=binwalk&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;binwalk\&quot;}&quot;}" data-tit="binwalk" data-pretit="binwalk">binwalk</a>可以从QR_code.png分离出另一个 4number.zip，里面是 4number.txt。根据这个提示，ARCHPR爆破一下，指定密码是4位数字，得到密码7639。解压看到内容是&nbsp;CTF{vjpw_wnoei}。提交flag{vjpw_wnoei}&nbsp;</p> 
<h2 id="%E4%BD%A0%E7%AB%9F%E7%84%B6%E8%B5%B6%E6%88%91%E8%B5%B0"><a name="t3"></a>你竟然赶我走</h2> 
<p>题目附件是biubiu.jpg，用010Editor打开，发现尾部有&nbsp;flag IS flag{stego_is_s0_bor1ing}</p> 
<h2 id="Next"><a name="t4"></a>大白</h2> 
<p>看不到图？ 是不是屏幕太小了。爆破png图片高度为479即可，flag{He1l0_d4_ba1}</p> 
<p><img alt="" height="162" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/faf72a490eda4d06bf4ff692e81f261c.png" width="230"></p> 
<p></p> 
<h2 id="N%E7%A7%8D%E6%96%B9%E6%B3%95%E8%A7%A3%E5%86%B3"><a name="t5"></a>N种方法解决</h2> 
<p>附件解压缩得到key.exe，发现不可执行。DIE检查发现是plain text，果断重命名为key.txt。内容只一行，内容是data:image/jpg;base64……一看就是base64编码转图片。全选，然后转成jpg图片，是一个二维码（<a class="link-info" href="https://www.wenyiso.com/tool/base642image/" title="在线工具">在线工具</a>）。扫码得到 KEY{dca57f966e4e4e31fd5b15417da63269}，提交flag{dca57f966e4e4e31fd5b15417da63269}</p> 
<p><img alt="" height="173" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c5fbcf46d54441a5b12816587e2beb4b.png" width="330"></p> 
<h2 id="%E4%B9%8C%E9%95%87%E5%B3%B0%E4%BC%9A%E7%A7%8D%E5%9B%BE"><a name="t6"></a>乌镇峰会种图</h2> 
<p>010Editor打开，最后有&nbsp;flag{97314e7864a8f62627b26f3f998c37f1}</p> 
<p><img alt="" height="140" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ad014968f23e4d7ba88a48eecb9f2943.jpeg" width="187"></p> 
<h2 id="%E5%9F%BA%E7%A1%80%E7%A0%B4%E8%A7%A3"><a name="t7"></a>基础破解</h2> 
<p>给你一个压缩包，你并不能获得什么，因为他是四位数字加密的哈哈哈哈哈哈哈。。。爆破一下密码，base64 再转一下，flag{70354300a5100ba78068805661b93a5c}</p> 
<h2 id="wireshark"><a name="t8"></a>wireshark</h2> 
<p>题目描述，黑客通过wireshark抓到管理员登陆网站的一段流量包（管理员的密码即是答案) 。wireshark搜索-字符串-分组字节流，搜索password，得到 flag{ffb7567a1d4f4abdffdb54e022f8facd}</p> 
<p><img alt="" height="279" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/fedf2195c19f425a93d30a93a489bd9c.png" width="751"></p> 
<h2 id="%E6%96%87%E4%BB%B6%E4%B8%AD%E7%9A%84%E7%A7%98%E5%AF%86"><a name="t9"></a>文件中的秘密</h2> 
<p>图片中的秘密.jpeg 属性-备注-&nbsp;flag{870c5a72806115cb5439345d8b014396}</p> 
<p><img alt="" height="225" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c6a8eae009564526b4e204b6b1c2b28c.png" width="377"></p> 
<h2 id="LSB"><a name="t10"></a>LSB</h2> 
<p>如此明显的提示。RGB plane0 LSB 另存为qr.png。扫描二维码得到&nbsp;cumtctf{1sb_i4_s0_Ea4y}，提交 flag{1sb_i4_s0_Ea4y}</p> 
<p><img alt="" height="268" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/558cca7ba50249a8bf91f0663829d741.png" width="268"><img alt="" height="291" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9ecbca3db7c54a29ad3549c0eb7d7878.png" width="273"></p> 
<p></p> 
<h2 id="zip%E4%BC%AA%E5%8A%A0%E5%AF%86"><a name="t11"></a>zip伪加密</h2> 
<p>如此明显的提示。加密标志字节，0x09 改为 0x00，解压缩得到&nbsp;flag{Adm1N-B2G-kU-SZIP}</p> 
<p><img alt="" height="213" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/76488cb3473f48d6ba7d049f58dfeb98.png" width="587"></p> 
<h2 id="rar"><a name="t12"></a>rar</h2> 
<p>根据提示爆破得到4位密码 8795，flag{1773c5da790bd3caff38e3decd180eb7}</p> 
<h2 id="%E5%B0%8F%E6%98%8E%E7%9A%84%E4%BF%9D%E9%99%A9%E7%AE%B1"><a name="t13"></a>小明的保险箱</h2> 
<p>小明的保险箱.jpg隐写了一个rar文件，根据提示爆破得到4位密码 7869，提交 flag{75a3d68bf071ee188c418ea6cf0bb043}</p> 
<h2 id="ningen"><a name="t14"></a>ningen</h2> 
<p>ningen.jpg隐写了一个zip文件，根据提示爆破得到4位密码&nbsp;8368，提交&nbsp;flag{b025fc9ca797a67d2103bfbc407a6d5f}</p> 
<h2 id="%E8%A2%AB%E5%97%85%E6%8E%A2%E7%9A%84%E6%B5%81%E9%87%8F"><a name="t15"></a>被嗅探的流量</h2> 
<p>某黑客潜入到某公司内网通过嗅探抓取了一段文件传输的数据，该数据也被该公司截获，你能帮该公司分析他抓取的到底是什么文件的数据吗？&nbsp;</p> 
<p>wireshark打开，搜索无果。过滤&nbsp;http.request.method==GET 无果。过滤http.request.method==POST，发下第233个包有传输了一张路飞的jpg图片，图片例隐写了一个字符串，提交&nbsp;flag{da73d88936010da1eeeb36e945ec4b97}</p> 
<p><img alt="" height="544" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cb9977741b774696b4d6ec2a0235781a.png" width="1200"></p> 
<h2 id="%E9%95%9C%E5%AD%90%E9%87%8C%E9%9D%A2%E7%9A%84%E4%B8%96%E7%95%8C"><a name="t16"></a>镜子里面的世界</h2> 
<p>图片名为steg.png，提示比较明显，LSB隐写，得到 flag{st3g0_saurus_wr3cks}</p> 
<p><img alt="" height="343" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/50876dae20b3494ca5372f3066156870.png" width="816"></p> 
<h2 id="%E7%88%B1%E5%9B%A0%E6%96%AF%E5%9D%A6"><a name="t17"></a>爱因斯坦</h2> 
<p>misc2.jpg隐写了一个zip，属性里有解压缩密码 this_is_not_password，提交 flag{dd22a92bf2cceb6c0cd0d6b83ff51606}</p> 
<p><img alt="" height="200" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/48e1ca5c77eb45ab896288ff0f650f8b.png" width="279"><img alt="" height="196" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d2242910178642c2ac80166127e5afdf.jpeg" width="349"></p> 
<h2 id="easycap"><a name="t18"></a>easycap</h2> 
<p>wireshark打开，发现只有TCP协议，过滤条件如下，发现每个包只传输一个字节，连起来就是flag。</p> 
<blockquote> 
 <p>ip.src_host==************* &amp;&amp; tcp.payload</p> 
</blockquote> 
<p>追踪TCP流，得到&nbsp;flag{385b87afc8671dee07550290d16a8071}</p> 
<h2 id="%E9%9A%90%E8%97%8F%E7%9A%84%E9%92%A5%E5%8C%99"><a name="t19"></a>隐藏的钥匙</h2> 
<p>隐藏的钥匙.jpg 图片隐写了一张图片。两张图片之间夹杂着数据 flag:base64:(Mzc3Y2JhZGRhMWVjYTJmMmY3M2QzNjI3Nzc4MWYwMGE=)，解码一下得到flag{377cbadda1eca2f2f73d36277781f00a}</p> 
<h2 id="%E5%8F%A6%E5%A4%96%E4%B8%80%E4%B8%AA%E4%B8%96%E7%95%8C"><a name="t20"></a>另外一个世界</h2> 
<p>monster.jpg尾部隐写了56个字符，01101011011011110110010101101011011010100011001101110011。变成7个字节，对应ascii为koekj3s，提交&nbsp;flag{koekj3s}</p> 
<p><img alt="" height="247" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e4bf3368475e44dc9f422c61efff0d44.jpeg" width="160"></p> 
<h2 id="FLAG"><a name="t21"></a>FLAG</h2> 
<p>注意：请将 hctf 替换为 flag 提交，格式 flag{}。FLAG.png文件LSB隐写了一个zip文件。解压缩得到 1.elf，放到linux执行一下 或者 010Editor看一下得到 flag{dd0gf4c3tok3yb0ard4g41n~~~}</p> 
<p><img alt="" height="227" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/b272cf4bc7ac40aa8cdc4a25b69f33e0.png" width="257"></p> 
<h2 id="BOOM"><a name="t22"></a>BOOM</h2> 
<p>这是在BUUCTF的QQ群里看到的。有一个BOOM.zip加密的压缩文件，README.md里面全是“阿巴……”，但是注意到BOOM.zip里面也有一个README.md，根据文件大小判断要采用明文攻击。</p> 
<p>把README.md压缩为README.zip。用<a href="https://so.csdn.net/so/search?q=ARCHPR&amp;spm=1001.2101.3001.7020" target="_blank" class="hl hl-1" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7020&quot;,&quot;dest&quot;:&quot;https://so.csdn.net/so/search?q=ARCHPR&amp;spm=1001.2101.3001.7020&quot;,&quot;extra&quot;:&quot;{\&quot;searchword\&quot;:\&quot;ARCHPR\&quot;}&quot;}" data-tit="ARCHPR" data-pretit="archpr">ARCHPR</a>明文攻击。<span style="color:#fe2c24;"><strong>这里有2点要特别注意：</strong></span></p> 
<ol><li><span style="color:#fe2c24;"><strong>压缩README.zip的时候使用windows自带压缩工具，右键-发送到-压缩（zipped）文件夹，这样可以避免ARCHPR报错</strong></span></li><li><span style="color:#fe2c24;"><strong>明文攻击搜索秘钥一般十几秒就可以完成，然后自动尝试破解密码，此时可以停止爆破就能看到3个密钥，用秘钥就可以破解zip文件。</strong></span></li></ol> 
<p><img alt="" height="414" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5656897cdff1475586cee272b4bc9485.png" width="1200"></p> 
<p><img alt="" height="230" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2060426d32aa494d9c65de915fe3c65b.png" width="463"></p> 
<p>得到的图片尾部有字符串，反转过来就是 flag{Don't_you_think_there's_something_missing_from_this_picture} ，但这不是flag。提示你少了什么，发现CRC32校验报错，经爆破是高度改了，真实高度是640，图片如下，提交flag{Stop_d@ydreaming!}</p> 
<p><img alt="" height="216" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/943cabdce5224d49ad0c421d4a4a7300.png" width="216"></p> 
<h2 id="%E7%A5%9E%E7%A7%98%E9%BE%99%E5%8D%B7%E9%A3%8E"><a name="t23"></a>神秘龙卷风</h2> 
<p>神秘龙卷风转转转，科学家用四位数字为它命名，但是发现解密后居然是一串外星人代码！！好可怕！ 注意：得到的 flag 请包上 flag{} 提交。爆破密码得到 5463，得到解压文件，里面全是“+.&gt;”之类的BrainFuck密码。解密码得到 flag{e4bbef8bdf9743f8bf5b727a9f6332a8}。</p> 
<p><img alt="" height="708" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/72e3bad59870400e9d22cff48ba381b0.png" width="1200"></p> 
<h2 id="%E5%81%87%E5%A6%82%E7%BB%99%E6%88%91%E4%B8%89%E5%A4%A9%E5%85%89%E6%98%8E"><a name="t24"></a>假如给我三天光明</h2> 
<p>附件是muzic.zip和pic.jpg。pic.jpg上的盲文为 KMDONOWG，小写 kmdonowg 是 muzic.zip 的解压密码，解压得到 music.wav。</p> 
<p><img alt="" height="196" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/12d2bc833f4c4464a974e87a7c77d504.png" width="394"></p> 
<p>听music.wav是电报码，用Audacity看波形得到摩斯码并解码如下。把CTF去掉，并提交小写的内容 flag{wpei08732?23dz}</p> 
<blockquote> 
 <p>1010 1 0010 011 0110 0 00 11111 11100 11000 00011 00111 001100 00111 00011 100 1100</p> 
 <p>-.-./-/..-./.--/.--././../-----/---../--.../...--/..---/..--../..---/...--/-../--..</p> 
 <p>CTFWPEI08732?23DZ</p> 
</blockquote> 
<p><img alt="" height="221" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/410f582b96c6444db3eed3996246936e.png" width="1051"></p> 
<h2 id="%E6%95%B0%E6%8D%AE%E5%8C%85%E4%B8%AD%E7%9A%84%E7%BA%BF%E7%B4%A2"><a name="t25"></a>数据包中的线索</h2> 
<p>公安机关近期截获到某网络犯罪团伙在线交流的数据包，但无法分析出具体的交流内容，聪明的你能帮公安机关找到线索吗？流量中的线索.pcapng里面 http.response.code==200 过滤一下只有一个包，把传输文本base64一下发现是jpg文件头，存成jpg得到 flag{209acebf6324a09671abc31c869de72c}</p> 
<p><img alt="" height="225" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/dc273be800c74fb3b44595f756e30c89.jpeg" width="272"></p> 
<h2 id="%E5%90%8E%E9%97%A8%E6%9F%A5%E6%9D%80%E3%80%81webshell%E5%90%8E%E9%97%A8%E3%80%90D%E7%9B%BE%E5%B7%A5%E5%85%B7%E3%80%91"><a name="t26"></a>后门查杀、webshell后门【D盾工具】</h2> 
<p>两个题目类似，网站打包备份作为附件，找到黑客的webshell(Webshell中的密码(md5)即为答案)。看writeup得知有个工具叫D盾，查杀指定目录，并在可疑文件中找关键字$pass即可找到答案。</p> 
<p>后门查杀 flag{6ac45fb83b3bc355c024f5034b947dd3}</p> 
<p><img alt="" height="347" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0b3ae43e4a894d1caf3e37e8778a83de.png" width="1200"></p> 
<p>webshell后门 flag{ba8e6c6f35a53933b871480bb9a9545c}</p> 
<p><img alt="" height="314" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0b1a52ad24be447b8b0a67246a42082a.png" width="1200"></p> 
<h2 id="%E6%9D%A5%E9%A6%96%E6%AD%8C%E5%90%A7"><a name="t27"></a>来首歌吧</h2> 
<p>附件stego100.wav看波形图1:10到2:30之间有规律，解密摩斯码得到字符串 5BC925649CB0188F52E617D70929191C。原以为要md5撞库（撞库结果是<em>valar dohaeris</em>），没想到flag就是这个（这次居然就是大写字母的） flag{5BC925649CB0188F52E617D70929191C}</p> 
<blockquote> 
 <p>...../-.../-.-./----./..---/...../-..../....-/----./-.-./-.../-----/.----/---../---../..-./...../..---/./-..../.----/--.../-../--.../-----/----./..---/----./.----/----./.----/-.-./</p> 
</blockquote> 
<p><img alt="" height="421" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7922fd7fa5f045e98edcdbee45113970.png" width="1200"></p> 
<h2 id="%E8%8D%B7%E5%85%B0%E5%AE%BD%E5%B8%A6%E6%95%B0%E6%8D%AE%E6%B3%84%E9%9C%B2%E3%80%90RouterPassView%E5%B7%A5%E5%85%B7%E3%80%91"><a name="t28"></a>荷兰宽带数据泄露【RouterPassView工具】</h2> 
<p>附件是config.bin文件，用工具RouterPassView打开可以看内容。搜索了flag、key、pass等都不是flag，看别人博客才知道原来username才是flag，提交 flag{053700357621}</p> 
<p><img alt="" height="180" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/966d7d714b2145a3892e06f11c460c97.png" width="417"></p> 
<h2 id="%E9%9D%A2%E5%85%B7%E4%B8%8B%E7%9A%84flag%E3%80%90zip%E4%BC%AA%E5%8A%A0%E5%AF%86%E3%80%81vmdk%E7%94%A87z%E8%A7%A3%E5%8E%8B%E7%BC%A9%E3%80%91"><a name="t29"></a>面具下的flag【zip伪加密、vmdk用7z解压缩】</h2> 
<p>附件mianju.jpg，binwalk看一下发现有隐写了一个zip文件。解压缩得到zip是加密的，但是ZIPFILERECORD头部是50 4B 03 04 14 00 00 00，ZIPDIRENTRY头部是50 4B 01 02 3F 00 14 00 09 00，说明是伪加密，把09改成00后直接解压缩。</p> 
<p><img alt="" height="202" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/bea7d6f1ad044aa2a3959e70460ff9e6.jpeg" width="320"></p> 
<p>得到flag.vmdk。本以为要用虚拟机挂盘打开，结果发现挂盘后文件根本打不开。直接用010Editor打开发现：NUL、key_part_one.txt文件附近有BrainFuck编码，where_is_flag_part_two.txt、flag_part_two_is_here.txt附近有Ook编码。<img alt="" height="328" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/dfa16c33a5cb4302ad08bf7c66b171cf.png" width="1200"></p> 
<p><img alt="" height="296" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4062d3d722784301897b1740e3c37729.png" width="1200"></p> 
<p>但是，不能直接复制出来解码。 NUL、key_part_one.txt文件附近有BrainFuck编码中间有一些字符被篡改了，把两块取能用的部分才可以解码。看其他的writeup，说用7z解码直接得到编码，原理可以看<a class="link-info" href="https://www.7-zip.org/" title="7z官网">7z官网</a>或<a class="link-info" href="https://blog.csdn.net/weixin_39628271/article/details/111329312" title="这篇博客">这篇博客</a>。两个编码分别解码，得到 flag{N7F5_AD5 和 _i5_funny!}，提交 flag{N7F5_AD5_i5_funny!}</p> 
<blockquote> 
 <p>7z x flag.vmdk</p> 
</blockquote> 
<p><img alt="" height="205" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/86d145300c3e44c89b8932663a40a1a4.png" width="1200"></p> 
<p><img alt="" height="594" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c9500aad2950410fb6bff43bf887bdd1.png" width="1200"></p> 
<h2 id="%E4%B9%9D%E8%BF%9E%E7%8E%AF%E3%80%90zip%E4%BC%AA%E5%8A%A0%E5%AF%86%E3%80%81steghide%E5%B7%A5%E5%85%B7%E3%80%91"><a name="t30"></a>九连环【zip伪加密、steghide工具】</h2> 
<p>附件是123456cry.jpg，隐写了一个zip文件。</p> 
<p><img alt="" height="121" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/721629cf2b964b21a7c046a1fc3da711.jpeg" width="121"></p> 
<p>zip文件是伪加密的，把good-已合并.jpg这个ZIPDIRENTRY的14 00 01 08改成14 00 00 08，另存一个z1.zip文件。</p> 
<p><img alt="" height="374" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/05cc26ea5644486e855c44aed450ec81.png" width="1200"></p> 
<p>z1.zip解压缩得到asd目录，里面有good-已合并.jpg和qwe.zip两个压缩文件。需要通过good-已合并.jpg找到qwe.zip的解压缩密码。steghide不需要密码找到ko.txt</p> 
<blockquote> 
 <p>steghide extract -sf good.jpg</p> 
</blockquote> 
<p><img alt="" height="83" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/83d9ff704d19498aac47e22a473e1e8a.png" width="1195"></p> 
<p>ko.txt里面是解压密码 bV1g6t5wZDJif^J7，提交 flag{1RTo8w@&amp;4nK@z*XL}</p> 
<p><img alt="" height="164" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/064b0e11f91849b5b9bf28b305b40083.png" width="1151"></p> 
<h2 id="%E8%A2%AB%E5%8A%AB%E6%8C%81%E7%9A%84%E7%A5%9E%E7%A7%98%E7%A4%BC%E7%89%A9"><a name="t31"></a>被劫持的神秘礼物</h2> 
<p>某天小明收到了一件很特别的礼物，有奇怪的后缀，奇怪的名字和格式。小明找到了知心姐姐度娘，度娘好像知道这是啥，但是度娘也不知道里面是啥。。。你帮帮小明？找到帐号密码，串在一起，用32位小写MD5哈希一下得到的就是答案。 链接: <a href="https://pan.baidu.com/s/1pwVVpA5_WWY8Og6dhCcWRw" title="百度网盘 请输入提取码">百度网盘 请输入提取码</a> 提取码: 31vk 注意：得到的 flag 请包上 flag{} 提交。</p> 
<p>附件gift.pcapng，根据提示是账号密码，过滤条件是http.request.method==POST，过滤得到一个包，里面name=admina word=adminb，adminaadminb的32位小写MD5哈希 flag{1d240aafe21a86afc11f38a45b541a49}</p> 
<p><img alt="" height="427" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e1bdb6647aa5413b9b9771aa57b06d4f.png" width="1200"></p> 
<h2 id="%E5%88%B7%E6%96%B0%E8%BF%87%E7%9A%84%E5%9B%BE%E7%89%87%E3%80%90F5-steganography%E5%B7%A5%E5%85%B7%E3%80%91"><a name="t32"></a>刷新过的图片【F5-steganography工具】</h2> 
<p>浏览图片的时候刷新键有没有用呢 注意：得到的 flag 请包上 flag{} 提交，附件是Misc.jpg。</p> 
<p>根据提示“刷新键”是F5，用 F5-steganography 工具，得到 output.txt。</p> 
<blockquote> 
 <p>java Extract Misc.jpg</p> 
</blockquote> 
<p><img alt="" height="239" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ea4e3e2310764a09861c3ee6286f2768.png" width="1200"></p> 
<p>观察output.txt内容判断为zip文件，而且是伪加密的。把 14 00 01 00 改成 14 00 00 00解压缩即可得到flag.txt，提交 flag{96efd0a2037d06f34199e921079778ee}</p> 
<p><img alt="" height="290" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/dd31ac3b377f460dae24d6ed418b4f2a.png" width="1200"></p> 
<h2 id="%5BBJDCTF2020%5D%E8%AE%A4%E7%9C%9F%E4%BD%A0%E5%B0%B1%E8%BE%93%E4%BA%86%E3%80%90ms-office%E6%96%87%E4%BB%B6%E6%9C%AC%E8%B4%A8%E6%98%AFzip%E3%80%91"><a name="t33"></a>[BJDCTF2020]认真你就输了【ms-office文件本质是zip】</h2> 
<p>附件是10.xls文件，打开有4个sheet，其中Line和Parabola画了一条直线和一条抛物线，并温馨提示flag就在这里。</p> 
<p>The flag is under this nicely made chart.</p> 
<p><img alt="" height="565" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a26c18775d1443b0964834c19faa514d.png" width="1200"></p> 
<p>The flag is also right under this nicely made chart.&nbsp;</p> 
<p><img alt="" height="527" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/82c14a464e1b456d9c22978efd7a0334.png" width="1200">但是，认真你就输了。这些和flag没有关系。MS-Office文件word、excel、ppt本质都是zip文件，就可以隐写。把01.xls重命名为01.zip，解压缩后在 xl/charts 目录里有 flag.txt 提交 flag{M9eVfi2Pcs#}</p> 
<h2 id="%5BBJDCTF2020%5D%E8%97%8F%E8%97%8F%E8%97%8F"><a name="t34"></a>[BJDCTF2020]藏藏藏</h2> 
<p>附件是蓝盾的jpg图片，隐写了一个zip没有密码，解压缩得到 福利.docx，里面是二维码，扫码得到 flag{you are the best!}</p> 
<p><img alt="" height="185" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c730b2b8ea704c2da108317dacbacee8.jpeg" width="261"></p> 
<h2 id="%E8%A2%AB%E5%81%B7%E8%B5%B0%E7%9A%84%E6%96%87%E4%BB%B6%E3%80%90rar%E6%96%87%E4%BB%B6%E6%A0%BC%E5%BC%8F%E3%80%91"><a name="t35"></a>被偷走的文件【zip和rar文件都应该4位爆破一下、rar文件格式】</h2> 
<p>一黑客入侵了某公司盗取了重要的机密文件，还好管理员记录了文件被盗走时的流量，请分析该流量，分析出该黑客盗走了什么文件。 注意：得到的 flag 请包上 flag{} 提交。</p> 
<p>附件 被偷走的文件.pcapng，因为提到偷走文件，找http或ftp，发现第55个包是ftp-data，传输了一个flag.rar文件。dump下来发现是加密的。分析了半天没有搞定，还是看了别人的writeup，码的，居然是4位数字爆破……一点提示都没有……<strong><span style="color:#fe2c24;">教训就是zip和rar文件拿到后都应该4位爆破一下。</span></strong></p> 
<p>爆破得到密码 5790，解压缩得到 flag{6fe99a5d03fb01f833ec3caa80358fa3}</p> 
<p><img alt="" height="652" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9857da80fe7148afb2abc721f03c1227.png" width="1200"></p> 
<p>顺便复习一下rar文件格式，RAR文件头部的Maigc有2种：</p> 
<h3 id="RAR%205.0%20%E6%96%87%E4%BB%B6%E6%A0%BC%E5%BC%8F"><a name="t36"></a>RAR 5.0 文件格式</h3> 
<blockquote> 
 <p>RAR <span style="color:#fe2c24;">5.0</span>的 Magic 由8个字节组成:<br><span style="color:#fe2c24;"><strong><code>0x52 0x61 0x72 0x21 0x1A 0x07 0x01 0x00</code></strong></span></p> 
</blockquote> 
<h3 id="RAR%204.x%20%E6%96%87%E4%BB%B6%E6%A0%BC%E5%BC%8F"><a name="t37"></a>RAR 4.x 文件格式</h3> 
<p>一个<span style="color:#fe2c24;"><code>RAR4.x</code></span>压缩文件由若干可变长度的块组成，常见块类型如下:</p> 
<blockquote> 
 <p>标记块：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; HEAD_TYPE=0x72<br> 压缩文件头：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; HEAD_TYPE=0x73<br> 文件头：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; HEAD_TYPE=0x74<br> 旧风格的注释头：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; HEAD_TYPE=0x75<br> 旧风格的用户身份信息：HEAD_TYPE=0x76<br> 旧风格的子块：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; HEAD_TYPE=0x77<br> 旧风格的恢复记录：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; HEAD_TYPE=0X78<br> 旧风格的用户身份信息：HEAD_TYPE=0X79<br> 子块：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; HEAD_TYPE=0x7A<br> 最后的结束块：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; HEAD_TYPE=0x7B</p> 
</blockquote> 
<p><strong>文件头部Magic，7字节内容是固定的。</strong>前两个字节<strong><span style="color:#fe2c24;"><code>0x52 0x61</code></span></strong>是CRC校验值，<strong><span style="color:#fe2c24;"><code>0x72</code></span></strong>是标记块。</p> 
<blockquote> 
 <p>RAR <span style="color:#fe2c24;">4.x</span>的 Magic 由7字节组成:<br><strong><span style="color:#fe2c24;"><code>0x52 0x61 0x72 0x21 0x1A 0x07 0x00</code></span></strong></p> 
</blockquote> 
<p><strong>压缩文件头</strong>，前两个字节 0xCE 0x99是CRC校验值，<span style="color:#fe2c24;">0x73</span>是压缩文件头标记。接下来2字节是位标记，0x8000表示加密，0x0000表示未加密。0xD000表示块长度13字节。</p> 
<p><img alt="" height="595" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5bf2f704c1354b43b0c56e055f279586.png" width="1200"></p> 
<p><strong>文件头</strong>， 前两个字节是CRC校验值，<span style="color:#fe2c24;">0x74</span>是压缩文件头标记。内容比较多，涉及操作系统、时间、压缩方式、文件名、文件内容等，可以参考<a class="link-info" href="https://blog.csdn.net/Claming_D/article/details/105899397" title="这篇博客">这篇博客</a>。</p> 
<p><strong>文件尾部，7字节内容是固定的。</strong>前两个字节<strong><span style="color:#fe2c24;"><code>0xC4 0x3D</code></span></strong>是CRC校验值，<strong><span style="color:#fe2c24;"><code>0x7B</code></span></strong>是结束块，<strong><span style="color:#fe2c24;"><code>0x00 0x40</code></span></strong>是位标记，<strong><span style="color:#fe2c24;"><code>0x07 0x00</code></span></strong>是块大小。</p> 
<blockquote> 
 <p>RAR <span style="color:#fe2c24;">4.x</span>的 Magic 由7字节组成:<br><strong><span style="color:#fe2c24;"><code>0xC4 0x3D 0x7B 0x00 0x40 0x07 0x00</code></span></strong></p> 
</blockquote> 
<h2 id="snake%E3%80%90serpent%E5%AF%B9%E7%A7%B0%E5%8A%A0%E5%AF%86%E7%AE%97%E6%B3%95%E3%80%91"><a name="t38"></a>snake【serpent对称加密算法】</h2> 
<p>附件snake.jpg隐写了一个zip，解压缩得到cipher和key两个文件。key里面是base64编码，解码得到 What is Nicki Minaj's favorite song that refers to snakes?，Nicki Minaj的歌曲里有一首anaconda（水蟒），所以真正的key就是anaconda。然后尝试对称加密算法，无果。</p> 
<p>看了writeup得知还有一种名为 serpent （蛇）的对称加密算法，在线解密地址为<a href="http://serpent.online-domain-tools.com/" title="Serpent Encryption – Easily encrypt or decrypt strings or files">Serpent Encryption – Easily encrypt or decrypt strings or files</a>。</p> 
<p>解密后提交 flag{who_knew_serpent_cipher_existed}</p> 
<p><img alt="" height="252" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/11c04403e15e4008a92a19f059be76cc.png" width="346"></p> 
<h2 id="%5BGXYCTF2019%5D%E4%BD%9B%E7%B3%BB%E9%9D%92%E5%B9%B4"><a name="t39"></a>[GXYCTF2019]佛系青年</h2> 
<p>附件[GXYCTF2019]佛系青年.zip，有两个文件 1.png 和 fo.txt。其中 fo.txt 是要密码的；1.png可以直接解压缩，010Editor打开发现其实是1.jpg格式，但是这图片没什么用。</p> 
<p>尝试4位字母、4位数字爆破zip没有结果，010Editor打开发现是伪加密。改完解压看到fo.txt，是与佛论禅编码，解码得到 flag{w0_fo_ci_Be1}</p> 
<p><img alt="" height="170" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/01b35a7ba0c44bf098d63bcfc8318eee.jpeg" width="200"><img alt="" height="170" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ce6cb9eace0b432398381d02ba20968c.png" width="471"></p> 
<h2><a name="t40"></a>[BJDCTF2020]你猜我是个啥</h2> 
<p>附件 [BJDCTF2020]你猜我是个啥.zip 打不开。010Editor看是png格式，重命名为[BJDCTF2020]你猜我是个啥.png，是个二维码，扫码得到“flag不在这”。010Editor打开png图片再仔细看，尾部隐写了字符串 flag{i_am_fl@g}</p> 
<h2><a name="t41"></a>菜刀666【流量分析】</h2> 
<p>附件 666666.pcapng。</p> 
<p>wireshark先过滤一下，发现在 http 200 的1367包里面传输了一个zip文件，里面有flag.txt。dump下来发现flag.zip是加密的，需要密码。</p> 
<blockquote> 
 <p>http.response.code==200</p> 
</blockquote> 
<p><img alt="" height="614" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/03a346e55ae14444b2befe270a302e66.png" width="1200"></p> 
<p><img alt="" height="94" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a124bf5750a74b1f8877288f407b2628.png" width="812"></p> 
<p>wireshark再过滤一下，发现POST的1068包里面传输的参数有特点，FFD8FF……FFD9，似乎是个jpg图片，保存下来发现图片就是密码 Th1s_1s_p4sswd_!!!&nbsp;&nbsp; 解压得到 flag{3OpWdJ-JP6FzK-koCMAK-VkfWBq-75Un2z}</p> 
<blockquote> 
 <p>http.request.method==POST</p> 
</blockquote> 
<p><img alt="" height="160" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/40ba8013725c42bf90631436108e98ba.png" width="419"><img alt="" height="160" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/04c9198b39234c2aaa105b4d9b3fc6ca.jpeg" width="246"></p> 
<h2><a name="t42"></a>秘密文件【流量分析】</h2> 
<p>附件 秘密文件.pcapng。按照提示有文件传输过滤，http协议9个包没有特点，那么过滤ftp协议，发现95包命令传输了文件 6b0341642a8ddcbeb7eca927dae6d541.rar，但是没有ftp-data协议。</p> 
<blockquote> 
 <p>ftp.request.command==RETR</p> 
</blockquote> 
<p><img alt="" height="412" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e0d94948de9d4f338bdfb55e6ddc2976.png" width="1200"></p> 
<p>在附近找到96、97包用TCP协议传输了这个文件。dump下来发现有解压缩密码，爆破得到4位数字弱口令1903，解压缩得到 flag{d72e5a671aa50fa5f400e5d10eedeaa5}</p> 
<p><img alt="" height="700" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/25b01502d007473882067c841f241b74.png" width="1200"></p> 
<p><img alt="" height="526" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/462bfc97985b4df5b8d13210d9318373.png" width="1200"></p> 
<h2><a name="t43"></a>[BJDCTF2020]just_a_rar</h2> 
<p>附件 4位数.rar，爆破得到4位数字密码2016，解压缩得到flag.jpg，图片属性备注 flag{Wadf_123}</p> 
<p><img alt="" height="200" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0a19d54a51914b5b885f967682da35ba.jpeg" width="134"></p> 
<h2><a name="t44"></a>[BJDCTF2020]鸡你太美【gif文件格式、脑洞】</h2> 
<p>附件 篮球.gif（鸡你太美）、篮球副本.gif（打不开）。010Editor打开篮球副本.gif，发现缺少gif的文件头部（复习一下gif的Magic是 GIF89a，也就是0x47 0x49 0x46 0x38 0x39 0x61）。在文件开始插入4个字节，写入GIF8，另存为b.gif。图片显示 zhi-yin-you-are-beautiful 提交发现不对，脑洞来了，把连字符换成下划线，提交&nbsp;flag{zhi_yin_you_are_beautiful} 正确。</p> 
<p><img alt="" height="320" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2e5b4a35bb4540cba0e5d49d710fd6d0.gif" width="180"><img alt="" height="320" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d70b3475d5b04973b765c7db4c0a7e86.gif" width="180"></p> 
<h2><a name="t45"></a>[BJDCTF2020]一叶障目</h2> 
<p>附件 1.png，crc32校验报错，爆破高度为844，另存2.png，图片里有 flag{66666}</p> 
<p style="text-align:center;"><img alt="" class="left" height="160" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0022e72d1fe94b28be9d3b8b8006b655.png" width="61"></p> 
<h2><a name="t46"></a>[SWPU2019]神奇的二维码【俄罗斯套娃、耐心】</h2> 
<p>附件 BitcoinPay.png 是个二维码，扫码得到 swpuctf{flag_is_not_here}，明显不是flag，<span style="color:#b95514;"><s>出题人好贱！</s></span>。</p> 
<p>binwalk 从 BitcoinPay.png 分离出4个rar文件：encode.rar、flag.jpg.rar、flag.doc.rar、good.rar，一个一个解决。<span style="color:#b95514;"><s>出题人好贱！</s></span></p> 
<p>1、encode.rar解压出encode.txt，内容 YXNkZmdoamtsMTIzNDU2Nzg5MA==，base64解码得到asdfghjkl1234567890，显然不是flag。<span style="color:#b95514;"><s>出题人好贱！</s></span></p> 
<p>2、flag.jpg.rar 解压出 flag.jpg、看看flag在不在里面^_^.rar 两个文件。看看flag在不在里面^_^.rar 的解压缩密码是 asdfghjkl1234567890，解压缩还是一模一样的 flag.jpg，这个文件没用。<span style="color:#b95514;"><s>出题人好贱！</s></span></p> 
<figure class="image"> 
 <img alt="" height="103" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1a165dca88dd459eaf0af8920c4e8ae1.jpeg" width="100"> 
 <figcaption>
   flag.jpg 
 </figcaption> 
</figure> 
<p>3、flag.doc.rar 解压出 flag.doc，内容是很长的 base64 编码，经过<span style="color:#b95514;">高达20次</span> base64 解码得到 comEON_YOuAreSOSoS0great，这个有用但不是flag。<span style="color:#b95514;"><s>出题人好贱！</s></span></p> 
<p>4、good.rar 需要解压缩密码 comEON_YOuAreSOSoS0great，解压缩得到 good.mp3，用 Audacity 看波形图得到摩斯码，摩斯码解码得到flag，提交 <span style="color:#fe2c24;">flag{morseisveryveryeasy} </span><span style="color:#b95514;"><s>出题人好贱！</s></span></p> 
<p><img alt="" height="234" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/b46def330371426e9840cc568082b4b6.png" width="1200"></p> 
<blockquote> 
 <p>--/---/.-./..././../.../...-/./.-./-.--/...-/./.-./-.--/./.-/.../-.--</p> 
 <p>MORSEISVERYVERYEASY</p> 
</blockquote> 
<h2><a name="t47"></a>梅花香之苦寒来【字符串描述的ascii】</h2> 
<p>附件 meihuai.jpg，写着图穷匕见，说明尾部有隐写。010Editor打开看到尾部有字符串， 28372c37290a28372c38290a……可以看出规律，每两位对应数字转ascii码为 (7,7)\n(7,8)……，应该是一组坐标，每行一个坐标。思路是字符串转坐标再画图，下面是代码。画出图像是二维码，扫码得到 flag{40fc0a979f759c8892f4dc045e28b820}</p> 
<p><img alt="" height="110" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f9d21d7b6dac4d47ba068f1cce3f0f62.jpeg" width="178"></p> 
<pre data-index="0" class="set-code-hide" name="code"><code class="language-python hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">import</span> matplotlib.pyplot <span class="hljs-keyword">as</span> plt</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-string"><span class="hljs-string">'''</span></span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-string">buuctf misc 梅花香之苦寒来</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-string">字符串存入meihuai.txt文件</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-string">'''</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">with</span> <span class="hljs-built_in">open</span>(<span class="hljs-string">'meihuai.txt'</span>, <span class="hljs-string">'r'</span>) <span class="hljs-keyword">as</span> f:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    string = f.readline()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="9"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="10"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-comment"># 删除括号 ascii 0x28 0x29; 分割坐标 ascii 0x0a; 注意去掉最后一个空字符串</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="11"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    strings = string.replace(<span class="hljs-string">'28'</span>, <span class="hljs-string">''</span>).replace(<span class="hljs-string">'29'</span>, <span class="hljs-string">''</span>).split(<span class="hljs-string">'0a'</span>)[:-<span class="hljs-number">1</span>]</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="12"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-comment"># 转换坐标 '372c313233'-&gt;['37','313233']-&gt;['7', '123']-&gt;(7,123)</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="13"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    coordinates = <span class="hljs-built_in">list</span>(<span class="hljs-built_in">map</span>(</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="14"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">lambda</span> s: <span class="hljs-built_in">tuple</span>(<span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> s_ascii: <span class="hljs-built_in">int</span>(s_ascii[<span class="hljs-number">1</span>::<span class="hljs-number">2</span>]), s.split(<span class="hljs-string">'2c'</span>))),</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="15"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        strings))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="16"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    x = <span class="hljs-built_in">list</span>(<span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> c: c[<span class="hljs-number">0</span>], coordinates))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="17"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    y = <span class="hljs-built_in">list</span>(<span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> c: c[<span class="hljs-number">1</span>], coordinates))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="18"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    plt.figure()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="19"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    plt.scatter(x, y, s=<span class="hljs-number">1</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="20"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    plt.axis(<span class="hljs-string">'equal'</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="21"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    plt.savefig(<span class="hljs-string">'meihuai.png'</span>)</div></div></li></ol></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newCodeMoreWhite.png" alt="" title=""></span></div><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<h2><a name="t48"></a>[BJDCTF2020]纳尼【gif文件格式】</h2> 
<p>附件 6.gif、题目.txt。还问我为啥gif打不开！文件头部缺少 GIF8 呗。补全得到能打开的gif，一共4个frame，把4段字符串拼接得到 Q1RGe3dhbmdfYmFvX3FpYW5nX2lzX3NhZH0=，base64解码得到 CTF{wang_bao_qiang_is_sad}，提交 flag{wang_bao_qiang_is_sad}</p> 
<p><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/812f966091df4475b6f3d2009f627071.gif" width="178"></p> 
<h2><a name="t49"></a>穿越时空的思念【摩斯码】</h2> 
<p>附件 穿越时空的思念.mp3，该题目为小写的32位字符，提交即可。用 Audacity 打开，把摩斯码解码，提交 flag{f029bd6f551139eedeb8e45a175b0786}</p> 
<blockquote> 
 <p>..-./-----/..---/----./-.../-../-..../..-./...../...../.----/.----/...--/----./././-.././-.../---.././....-/...../.-/.----/--.../...../-.../-----/--.../---../-..../..-./-----/..---/----./-.../-../-..../..-./.....<br> F029BD6F551139EEDEB8E45A175B0786F029BD6F5&nbsp;&nbsp; 数32个字符做flag的内容</p> 
</blockquote> 
<h2><a name="t50"></a>[ACTF新生赛2020]outguess</h2> 
<p>附件 [ACTF新生赛2020]outguess.tar，解压缩里面附件也很多，但是伪加密、改扩展名都是障眼法，有用的只有 mmm.jpg。flag.txt 还提示“你guess一下啊”，明显是用 outguess 工具。</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/78dc24b97cab49108feb3bac10cca03d.jpeg" width="107"> 
 <figcaption>
   mmm.jpg 
 </figcaption> 
</figure> 
<p></p> 
<p>mmm.jpg 的属性-备注是“公正民主公正文明公正和谐”，解码得到 abc。执行命令如下得到 ACTF{gue33_Gu3Ss!2020}，提交 flag{gue33_Gu3Ss!2020}</p> 
<blockquote> 
 <p>outguess -r mmm.jpg mmm.txt -k abc</p> 
</blockquote> 
<h2><a name="t51"></a>[HBNIS2018]excel破解【excel文件隐写】</h2> 
<p>附件是[HBNIS2018]excel破解.xls（有密码）。010Editor打开搜索flag得到 flag is here CTF{office_easy_cracked} 提交 flag{office_easy_cracked}</p> 
<h2><a name="t52"></a>[HBNIS2018]来题中等的吧【摩斯码】</h2> 
<p>附件转摩斯码解码得到</p> 
<blockquote> 
 <p>.-/.-../.--./..../.-/.-../.-/-...</p> 
 <p>ALPHALAB<br> flag{alphalab}</p> 
</blockquote> 
<p><img alt="" height="35" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c6fb0fdf961f46688a1d9d2322739015.png" width="126"></p> 
<h2><a name="t53"></a>谁赢了比赛？</h2> 
<p>附件who_won_the_game.png。binwalk分离出rar文件，爆破解压缩密码1020。解压缩得到hehe.gif。Stegsolve浏览frame，第310个frame有个图片，另存为 frame310.png。Stegsolve浏览 frame310.png 发现 red plane 0，有个二维码，扫码得到 flag{shanxiajingwu_won_the_game}</p> 
<figure class="image"> 
 <img alt="" height="200" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d5d3eaab3e844d6e829685f2bc53a0ad.gif" width="193"> 
 <figcaption>
   hehe.gif 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="200" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4bea5ca0b4874afd9c9406eea53d1706.png" width="193"> 
 <figcaption>
   frame310.png 
 </figcaption> 
</figure> 
<h2><a name="t54"></a>[SWPU2019]我有一只马里奥【ntfs流特性】</h2> 
<p>附件是一个exe，执行后得到1.txt，提示ntfs、flag.txt。</p> 
<p>这是新知识，直接看writeup。<span style="color:#be191c;">NTFS交换数据流（简称ADS）是NTFS磁盘格式的一个特性，在NTFS文件系统下，每个文件都可以存在多个数据流，除了主文件流之外还可以有许多非主文件流寄宿在主文件流中，而我们无法查看到非主文件数据流，因文件夹大小始终显示0</span></p> 
<p>以下是进行文件流计生和查看的方法，以后在windows命令行下查看文件，都用 dir /r</p> 
<blockquote> 
 <p>寄生在文件上</p> 
 <p>echo hidden_string &gt; 3.txt:flag.txt</p> 
 <p>notepad 3.txt:flag.txt</p> 
 <p></p> 
 <p>寄生在文件夹上</p> 
 <p>type 2.txt &gt; test:2.txt</p> 
 <p>notepad test:2.txt</p> 
 <p></p> 
 <p>查看方式</p> 
 <p>dir /r</p> 
</blockquote> 
<p><img alt="" height="132" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ee6e3524c1744c5bb7f250f1937ad12d.png" width="311"></p> 
<p>dir /r 1.txt:flag.txt 得到 flag{ddg_is_cute}</p> 
<h2><a name="t55"></a>[WUSTCTF2020]find_me.jpg【盲文】</h2> 
<p>附件 [WUSTCTF2020]find_me.jpg，属性-备注里有盲文，解码即可。地址戳这里<a href="https://www.qqxiuzi.cn/bianma/wenbenjiami.php?s=mangwen" title="文本加密为盲文,可自设密码|文本在线加密解密工具">文本加密为盲文,可自设密码|文本在线加密解密工具</a></p> 
<blockquote> 
 <p>⡇⡓⡄⡖⠂⠀⠂⠀⡋⡉⠔⠀⠔⡅⡯⡖⠔⠁⠔⡞⠔⡔⠔⡯⡽⠔⡕⠔⡕⠔⡕⠔⡕⠔⡕⡍=</p> 
 <p>wctf2020{y$0$u_f$1$n$d$_M$e$e$e$e$e}</p> 
 <p>flag{y$0$u_f$1$n$d$_M$e$e$e$e$e}</p> 
</blockquote> 
<h2><a name="t56"></a>[GXYCTF2019]gakki【字频统计、脑洞】</h2> 
<p>附件 wolaopo.jpg 是一张新垣结衣的图片。隐写了一个rar文件（有密码），爆破得到密码8864。解压缩得到 flag.txt，内容如下，没有规律。查了writeup，居然是字频分析，统计每个字符出现次数，按照字频从高到低排序。flag就隐藏在其中，下面是代码。提交 flag{gaki_IsMyw1fe}。<s><span style="color:#be191c;">出题人脑（真）洞（欠）好（揍）大。</span></s></p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/bdb0ab23b4804fc6a3bdabb8637c3ca0.jpeg" width="100"> 
 <figcaption>
   wolaopo.jpg 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="90" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7b944e2bfb634b6b8e22fc9eb08bbd8b.png" width="452"> 
 <figcaption>
   flag.txt 
 </figcaption> 
</figure> 
<pre data-index="1"><code class="language-python hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">with</span> <span class="hljs-built_in">open</span>(<span class="hljs-string">'flag.txt'</span>) <span class="hljs-keyword">as</span> f:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    line = f.readline()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    count = <span class="hljs-built_in">dict</span>()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-keyword">for</span> c <span class="hljs-keyword">in</span> line:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">if</span> count.get(c):</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">            count[c] += <span class="hljs-number">1</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">else</span>:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">            count[c] = <span class="hljs-number">1</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="9"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="10"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">keys = <span class="hljs-built_in">sorted</span>(count, key=<span class="hljs-keyword">lambda</span> x: count[x], reverse=<span class="hljs-literal">True</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="11"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">for</span> k <span class="hljs-keyword">in</span> keys:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="12"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-built_in">print</span>(k, count[k])</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="13"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="14"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-built_in">print</span>(<span class="hljs-string">''</span>.join(keys))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="15"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-comment"># GXY{gaki_IsMyw1fe}DAQOWJHNSEKUP*Z&amp;8#294BC%^V$3@)(R-FT05=L76ohqdujlmczxnpbvtr![;,. '</span></div></div></li></ol></code><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<h2><a name="t57"></a>[ACTF新生赛2020]base64隐写【base64隐写、脑洞】</h2> 
<p>附件 近在眼前.zip（无密码），解压缩得到 hint.jpeg 和 ComeOn!.txt。hint.jpeg是微信公众号二维码，备注里面提示去公众号找线索，但是并未找到（可能当年比赛时有线索）。ComeOn!.txt是多行base64编码，2次base64解码得到类似C++代码，但是flag并不在这里。</p> 
<p><img alt="" height="396" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ab2309757b7449e4966700a44df593b0.png" width="1200"></p> 
<p>查看writeup才知道这题脑洞多大。分析大佬们的脚本，得到如下结论</p> 
<blockquote> 
 <p>base64编码，6bit一组查base64 alphabet得到编码。这决定了当字符串长度不是3的倍数时，余有bit。剩余bit，用0补充到6位再查base64 alphabet得到最后一个编码表字符。编码最后用=补充，补充 (3 - len(string) % 3) % 3 个=。</p> 
 <p></p> 
 <p>以字母a为例，ascii编码为01100001<br> 一般base64编码会补0: 011000 010000 -&gt; YQ -&gt; YQ==<br> 但是如果补其他内容，只要位数不变，就不会影响解码的效果。<br> 比如a可以补位为是 010001 or 010010 ... or 011111 -&gt; YR== YS== ... Yf==<br> 运行下面的代码，就看到解码都是字母a<br> b1 = [b'YQ==', b'YR==', b'YS==', b'YT==', b'YU==', b'YV==', b'YW==', b'YX==',<br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; b'YY==', b'YZ==', b'Ya==', b'Yb==', b'Yc==', b'Yd==', b'Ye==', b'Yf==', ]<br> b2 = list(map(lambda x: base64.b64decode(x), b1))<br> print(b2)</p> 
</blockquote> 
<p>所以对隐写编码先base64解码再base64编码得到一般编码。隐写编码和一般编码最后一个字符的偏移量转二进制，左侧补0。补0后位数为该行编码等号个数的2倍，如果已经达到足够位数就不补0（用str.zfill实现）。然后8位一组转ascii得到 ACTF{6aseb4_f33!}，提交 flag{6aseb4_f33!}</p> 
<pre data-index="2" class="set-code-hide" name="code"><code class="language-python hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">alphabet = <span class="hljs-string">'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">with</span> <span class="hljs-built_in">open</span>(<span class="hljs-string">'ACTF2022_base64_steg.txt'</span>, <span class="hljs-string">'r'</span>) <span class="hljs-keyword">as</span> fin:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    steg_lines = fin.readlines()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    steg_lines = <span class="hljs-built_in">list</span>(<span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> line: line.strip(<span class="hljs-string">'\n'</span>),</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">                          steg_lines))  <span class="hljs-comment"># steg_lines要迭代多遍，所以转成list</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-comment"># 先解码再编码得到一般的base64编码</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="9"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    normal_lines = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> line: base64.b64decode(line.encode(<span class="hljs-string">'utf-8'</span>)),</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="10"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">                       steg_lines)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="11"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    normal_lines = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> line: base64.b64encode(line).decode(<span class="hljs-string">'utf-8'</span>),</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="12"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">                       normal_lines)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="13"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="14"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-comment"># 计数每行编码的=个数eq_counts</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="15"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    eq_counts = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> line: line.count(<span class="hljs-string">'='</span>), steg_lines)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="16"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="17"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-comment"># 去掉=，看最后一个字符的差别得到offsets</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="18"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    steg_lines = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> line: line.replace(<span class="hljs-string">'='</span>, <span class="hljs-string">''</span>), steg_lines)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="19"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    normal_lines = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> line: line.replace(<span class="hljs-string">'='</span>, <span class="hljs-string">''</span>), normal_lines)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="20"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    offsets = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> steg_line, normal_line: <span class="hljs-built_in">abs</span>(</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="21"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        alphabet.index(steg_line[-<span class="hljs-number">1</span>]) - alphabet.index(normal_line[-<span class="hljs-number">1</span>])),</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="22"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">                  steg_lines, normal_lines)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="23"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="24"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-comment"># 按照=个数*2，左边补0，形成二进制字符串</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="25"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    bin_strs = <span class="hljs-built_in">map</span>(<span class="hljs-keyword">lambda</span> eq_count, offset: <span class="hljs-built_in">bin</span>(offset)[<span class="hljs-number">2</span>:].zfill(</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="26"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        eq_count * <span class="hljs-number">2</span>) <span class="hljs-keyword">if</span> eq_count <span class="hljs-keyword">else</span> <span class="hljs-string">''</span>, eq_counts, offsets)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="27"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    bin_str = <span class="hljs-string">''</span>.join(bin_strs)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="28"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="29"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    chs = []</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="30"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> <span class="hljs-built_in">range</span>(<span class="hljs-number">0</span>, <span class="hljs-built_in">len</span>(bin_str), <span class="hljs-number">8</span>):</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="31"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        chs.append(<span class="hljs-built_in">chr</span>(<span class="hljs-built_in">int</span>(bin_str[i:i + <span class="hljs-number">8</span>], <span class="hljs-number">2</span>)))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="32"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-built_in">print</span>(<span class="hljs-string">''</span>.join(chs))</div></div></li></ol></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newCodeMoreWhite.png" alt="" title=""></span></div><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<h2><a name="t58"></a>[GUET-CTF2019]KO【Ook!】</h2> 
<p>附件 [GUET-CTF2019]KO.txt，内容是Ook编码。解码得到 flag{welcome to CTF}</p> 
<h2><a name="t59"></a>[MRCTF2020]ezmisc【png图片crc32爆破】</h2> 
<p>附件 flag.png 图片，crc32爆破得到weight: 500(0x1f4), height: 456(0x1c8)，提交 flag{1ts_vEryyyyyy_ez!}</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20a44d21c33f4bfabd988e6d88da4822.png" width="110"> 
 <figcaption>
   flag.png 
 </figcaption> 
</figure> 
<h2><a name="t60"></a>[SWPU2019]伟大的侦探【EBCDIC编码、图形密码】</h2> 
<p>附件 [SWPU2019]伟大的侦探.zip（有密码），里面 密码.txt 可以解压缩。看writeup说用<strong><a class="link-info" href="https://baike.baidu.com/item/ebcdic/6816534?fr=aladdin" title="EBCDIC">EBCDIC</a></strong>编码查看，得到解压缩密码 wllm_is_the_best_team!</p> 
<p><img alt="" height="325" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d5b097cd1f514cb6b45963501aaf5094.png" width="1200"></p> 
<p><img alt="" height="68" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/25c4b90f9ac84592bb5e829d25fe6ebf.png" width="887">解压缩得到18张图片，文件名是jpg实际是png图片。是《福尔摩斯探案集》里面跳舞的小人密码。</p> 
<p><img alt="" height="338" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/89f189c347794a4cb7e3e6186b07ab65.png" width="1200"></p> 
<p>大多数密码表对照不出来，参考<a href="https://blog.csdn.net/mochu7777777/article/details/109387134" title="BUUCTF：[SWPU2019]伟大的侦探_末初mochu7的博客-CSDN博客_[swpu2019]伟大的侦探">BUUCTF：[SWPU2019]伟大的侦探_末初mochu7的博客-CSDN博客_[swpu2019]伟大的侦探</a>可以对照出来，提交 flag{iloveholmesandwllm}</p> 
<h2><a name="t61"></a>黑客帝国【png、jpg文件格式】</h2> 
<p>附件 黑客帝国.rar（有密码），爆破得到密码 3690，解压缩得到txt文件，内容是一行字符串。看到52617221……知道是rar文件。010Editor粘贴后得到a.rar文件，解压缩得到a.png文件但打不开。010Editor查看发现内容有JFIF，以0xFFD9结尾，推断为jpg文件。把开头的4个字节从 0x89504E47 改为 0xFFD8FFE0，另存为a.jpg文件，打开得到</p> 
<p><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4ef436ad639b4369b5c049e5972a1738.jpeg" width="132"> flag{57cd4cfd4e07505b98048ca106132125}</p> 
<h2><a name="t62"></a>[MRCTF2020]你能看懂音符吗【rar文件格式、音符密码】</h2> 
<p>附件 [MRCTF2020]你能看懂音符吗.rar，文件打不开。010Editor查看，文件头部aRr！，改为Rar!也就是0x52617221，解压缩得到 你能看懂音符吗.docx。word文档-字体-隐藏文字，显示被隐藏内容，如下图。</p> 
<p><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/21f0f5f3b6c246a48050443dd53967b4.png" width="298"></p> 
<p>用搜狗输入打特殊符号如下</p> 
<p>♭♯♪‖¶♬♭♭♪♭‖‖♭♭♬‖♫♪‖♩♬‖♬♬♭♭♫‖♩♫‖♬♪♭♭♭‖¶∮‖‖‖‖♩♬‖♬♪‖♩♫♭♭♭♭♭§‖♩♩♭♭♫♭♭♭‖♬♭‖¶§♭♭♯‖♫∮‖♬¶‖¶∮‖♬♫‖♫♬‖♫♫§=</p> 
<p>在<a href="https://www.qqxiuzi.cn/bianma/wenbenjiami.php?s=yinyue" title="文本加密为音乐符号,可自设密码|文本在线加密解密工具">文本加密为音乐符号,可自设密码|文本在线加密解密工具</a>解密得到 MRCTF{thEse_n0tes_ArE_am@zing~} 提交 flag{thEse_n0tes_ArE_am@zing~}</p> 
<h2><a name="t63"></a>[HBNIS2018]caesar【凯撒密码】</h2> 
<blockquote> 
 <p>题目：caesar</p> 
 <p>描述：gmbhjtdbftbs</p> 
 <p>flag格式：XXX 明文</p> 
 <p>提交：直接提交明文 （小写）</p> 
</blockquote> 
<p>凯撒解密，offset=1，提交 flag{flagiscaesar}</p> 
<p></p> 
<h2><a name="t64"></a>[HBNIS2018]低个头【脑洞】</h2> 
<p>题目是txt文件，内容乱码中有可读部分——EWAZX RTY TGB IJN IO KL</p> 
<p><strong><s><span style="color:#fe2c24;">出题人脑洞真可（欠）以（打）……</span></s></strong>题目“低个头”居然是提示看键盘，这几个字母形成的字符是CTF，提交 flag{CTF}</p> 
<h2><a name="t65"></a>[SUCTF2018]single dog【aaencode等**encode密码】</h2> 
<p>附件[SUCTF2018]single dog.jpg，隐写了一个zip文件，解压得到1.txt。内容是颜文字。百科一下，知道了好几种代码混淆的工具可以作为密码使用，这里是aaencode。除此之外还有jjencode、ppencode、rrencode、uuencode、xxencode，暂时没有找到具体的算法，待研究。</p> 
<p><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/01d4ee33f0e84c0f9ae0db7d7756a366.jpeg" width="118"></p> 
<p>在线工具地址 <a href="http://www.hiencode.com/" title="CTF在线工具-CTF工具|CTF编码|CTF密码学|CTF加解密|程序员工具|在线编解码">CTF在线工具-CTF工具|CTF编码|CTF密码学|CTF加解密|程序员工具|在线编解码</a>，也可以使用随波逐流，解码得到 JavaScript 代码如下，提交 flag{happy double eleven}</p> 
<pre data-index="3"><code class="language-javascript hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">function</span> <span class="hljs-title function_">a</span>(<span class="hljs-params"></span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">{</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">var</span> a=<span class="hljs-string">"SUCTF{happy double eleven}"</span>;</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-title function_">alert</span>(<span class="hljs-string">"双十一快乐"</span>);</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">}</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-title function_">a</span>();</div></div></li></ol></code><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<h2><a name="t66"></a>我吃三明治【base32】</h2> 
<p>附件 flag.jpg，010Editor查看隐写了一个jpg，且两个jpg之间有字符串（像三明治一样夹在中间）。字符串 MZWGCZ33GZTDCNZZG5SDIMBYGBRDEOLCGY2GIYJVHA4TONZYGA2DMM3FGMYH2。</p> 
<p>应注意到base系列的padding问题，本例缺少padding导致部分工具无法破解。补齐padding的编码应为MZWGCZ33GZTDCNZZG5SDIMBYGBRDEOLCGY2GIYJVHA4TONZYGA2DMM3FGMYH2===，解码得到 flag{6f1797d4080b29b64da5897780463e30}</p> 
<p><img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f13a5b53d1ea4c678d729e540c55f141.jpeg" width="100">&nbsp; <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/092b144ec3f8421eb0de7a42065554c5.jpeg" width="133"></p> 
<h2><a name="t67"></a>sqltest【流量分析】</h2> 
<p>附件 sqltest.pcapng，wireshark打开先协议分级一下发现只有http协议。过滤发现只有GET请求。规律是：每次http请求，就是逐位尝试数据库名、表名、字段名和字段值。</p> 
<p>逐次递进最终尝试出：数据库名 db_flag、表名 tb_flag、字段名 flag。因此只要按照这个规则把每次（位）尝试的最后一次值求出来就是flag。</p> 
<p><img alt="" height="769" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/db902b8b15b442519299a768612a0ffb.png" width="1200"></p> 
<blockquote> 
 <p>使用 tshark过滤，注意过滤条件。</p> 
 <p>tshark -r sqltest.pcapng -2 -R 'http.request.uri.query.parameter contains "id=1 and ascii(substr(((select concat_ws(char(94), flag)&nbsp; from db_flag.tb_flag&nbsp; limit 0,1)), "' -T fields -e http.request.uri.query.parameter &gt; 1.txt</p> 
</blockquote> 
<p>得到内容如下，处理一下，取出每位最后一次尝试值，转为ascii。</p> 
<blockquote> 
 <p>102, 108, 97, 103, 123, 52, 55, 101, 100, 98, 56, 51, 48, 48, 101, 100, 53, 102, 57, 98, 50, 56, 102, 99, 53, 52, 98, 48, 100, 48, 57, 101, 99, 100, 101, 102, 55, 125</p> 
 <p>flag{47edb8300ed5f9b28fc54b0d09ecdef7}</p> 
</blockquote> 
<p><img alt="" height="542" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a242dcbadd214122ad5bc947351e47bb.png" width="1200"></p> 
<h2><a name="t68"></a>[ACTF新生赛2020]music【m4a文件格式】</h2> 
<p>附件vip.m4a无法播放，错误码0x80040241。这里必须知道.m4a文件的格式。前32个字节：</p> 
<ul><li>0-3字节都是0x00；</li><li>12-15字节都是0x00；</li><li>4-7字节是4个字符 ftyp；</li><li>8-10和16-18的内容是一样的，可能是 M4A，也可能是 mp42；</li></ul> 
<p><img alt="" height="115" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5a6f9f4a6be04185b413ced1a5588862.png" width="1164"></p> 
<p><img alt="" height="110" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/34aeebede2d14ffbafa78a4da7902933.png" width="1165"></p> 
<p>观察vip.m4a，前32字节中，0-3和12-15字节本应是0x00却变成了0xA1，且8-10和16-18字节内容相同，猜想vip.m4a文件和0xA1异或了。</p> 
<p><img alt="" height="108" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/8696a8e870704d289a7377b45a1f32fe.png" width="1163"></p> 
<p>代码如下，得到vip2.m4a，播放听到 actfabcdfghijk，提交 flag{abcdfghijk}</p> 
<pre data-index="4"><code class="language-python hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f1 = <span class="hljs-built_in">open</span>(<span class="hljs-string">'vip.m4a'</span>, <span class="hljs-string">'rb'</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">ba1 = f1.read()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f1.close()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">ba2 = <span class="hljs-built_in">bytes</span>()</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">for</span> b <span class="hljs-keyword">in</span> ba1:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    i = b ^ <span class="hljs-number">0xA1</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    ba2 += i.to_bytes(<span class="hljs-number">1</span>, <span class="hljs-string">'big'</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="9"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="10"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f2 = <span class="hljs-built_in">open</span>(<span class="hljs-string">'vip2.m4a'</span>, <span class="hljs-string">'wb'</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="11"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f2.write(ba2)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="12"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f2.close()</div></div></li></ol></code><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<p>也可以010Editor-Tool-Hex Operation，直接异或保存。</p> 
<p><img alt="" height="460" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e9ec7390f5914f288f316525511deac3.png" width="1164"></p> 
<h2><a name="t69"></a>[SWPU2019]你有没有好好看网课【Kinovea工具、敲击码】</h2> 
<p>附件解压缩得到 flag2.zip 和 flag3.zip，都有密码。看writeup才知道这题脑洞真不小。</p> 
<p>flag3.zip备注有提示，密码是6位数字爆破得到183792。解压缩得到 flag.docx 和 影流之主.mp4。flag.docx 这个word文件没有什么特别内容，据说520和711是提示截屏位置的，但是时间和帧数都对不上。影流之主.mp4在5秒和7秒的时候，吊灯位置一闪而过，写了密文。看writeup说用Kinovea工具，可以慢放，然后逐帧查看视频影像。<s><span style="color:#be191c;">我认为这个地方不好，CTF是技术，不是比脑洞和眼力。</span></s></p> 
<figure class="image"> 
 <img alt="" height="120" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/be196f62e4f7451b800c5a58a6b10b41.png" width="141"> 
 <figcaption>
   flag.docx 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="120" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/06b87b91d3b54fb8b517115af88878c7.png" width="193"> 
 <figcaption>
   第一个截屏 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="120" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9faf3985a7ef438bb500e83695e6904a.png" width="165"> 
 <figcaption>
   敲击码 
 </figcaption> 
</figure> 
<p></p> 
<figure class="image"> 
 <img alt="" height="120" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/866171ba19c341079cac617896ad433f.png" width="222"> 
 <figcaption>
   第二个截屏 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="120" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/aeaa2039ce714ca8b18bb5599fbea5f5.jpeg" width="120"> 
 <figcaption>
   Real flag.jpg 
 </figcaption> 
</figure> 
<p>第一个截屏，..... ../... ./... ./... ../ 是敲击码，对照码表得到 wllm。</p> 
<p>第二个截屏，dXBfdXBfdXA=，base64解码得到up_up_up。</p> 
<p>两个拼一起 wllmup_up_up 是 flag2.zip 的解压缩密码。得到 Real flag.jpg，文件尾部隐写了字符串swpuctf{A2e_Y0u_Ok?}，提交flag{A2e_Y0u_Ok?}</p> 
<h2><a name="t70"></a>[ACTF新生赛2020]NTFS数据流【ntfs流特性】</h2> 
<p>此题 与[SWPU2019]我有一只马里奥 异曲同工。直接 dir /r ，过滤 $DATA字符串，发现293.txt后面跟了NTFS流。notepad 293.txt:flag.txt 打开查看 ACTF{AAAds_nntfs_ffunn?}，提交 flag{AAAds_nntfs_ffunn?}</p> 
<p><img alt="" height="258" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4ccc762b90e44134ad393f8185d33447.png" width="1200"></p> 
<h2><a name="t71"></a>john-in-the-middle【流量分析、PNG隐写】</h2> 
<p>附件 john-in-the-middle.pcap，发现只有HTTP协议，只有 200 OK，传输了6张图片。全部复制为 Hex Stream，粘贴到010Editor里保存为png图片。逐个用StegSolve查看，发现logo.png里面在Random Color Map 时有 flag{J0hn_th3_Sn1ff3r}</p> 
<blockquote> 
 <p>(http.request.uri contains ".png") || (tcp.payload contains "PNG" &amp;&amp; tcp.payload contains "IEND")</p> 
</blockquote> 
<figure class="image"> 
 <img alt="" height="887" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1d4e19f1c60649369b73d570a7ec5704.png" width="1200"> 
 <figcaption>
   6张PNG图片，其中logo.png有flag 
 </figcaption> 
</figure> 
<p></p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f6cdea9ca4a84170b5c4c806b18160e9.png" width="100"> 
 <figcaption>
   logo.png 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5c0edbd66a634abfb8c38820a7795db5.png" width="100"> 
 <figcaption>
   logo-RandomColorMap.png 
 </figcaption> 
</figure> 
<p></p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/66b79758b3fb4c559fbe5b7429004399.png" width="150"> 
 <figcaption>
   scanlines.png 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/8cf2f4f619b5484b84b436e3d4301b19.png" width="227"> 
 <figcaption>
   texture.png 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cddb4920794147bc8062607e1b8f40a6.png" width="311"> 
 <figcaption>
   reply_cv.png 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d46067bafad541869f479962cddd3acc.png" width="414"> 
 <figcaption>
   cini.png 
 </figcaption> 
</figure> 
<p></p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9079254da81d4244972bb65a202d42a9.png" width="960"> 
 <figcaption>
   font_c3.png 
 </figcaption> 
</figure> 
<p></p> 
<h2><a name="t72"></a>喵喵喵【ntfs流及工具、umcompyle6工具】</h2> 
<p>附件mmm.png。StegSolve查看发现RGB三色plane0的顶部有异常。用Data Extract，选择RGB的plane0，通道顺序BGR，小端模式LSB，发现是一个PNG图片，另存为qr.png。qr.png是半个二维码，crc32爆破高度得到完整二维码qr1.png。可以用QRResearch直接扫码qr1.png，或者qr1.png反色后用微微二维码扫描，得到字符串 https://pan.baidu.com/s/1pLT2J4f。</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/3fd92f9146ba46c9b69a74316b9b3d4c.png" width="100"> 
 <figcaption>
   喵喵喵.png 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cf99ad473fa3481695b06ab9f561e288.png" width="131"> 
 <figcaption>
   Data Extract 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9c846f92fc0a47bf95b11f2e6d5802ad.png" width="90"> 
 <figcaption>
   QR Research 
 </figcaption> 
</figure> 
<p><br> 从网盘下载到flag.rar文件。winrar解压缩报错。里面的flag.txt内容为“flag不在这里哦&nbsp; 你猜猜flag在哪里呢？&nbsp; 找找看吧”。</p> 
<p><img alt="" height="97" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5cf46f424af9451bb66b71cf7d0cb620.png" width="1067"></p> 
<p>此时考虑NTFS流数据。用NtfsStreamsEditor工具搜索得到flag.pyc。</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5fdb0c5d955c44b3849e15a35328ece2.png" width="238"> 
 <figcaption>
   dir /r 发现ntfs流数据 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/74774c3cc17c42279210655fdcb31efe.png" width="218"> 
 <figcaption>
   NTFS Streams Editor 找到 flag.pyc 
 </figcaption> 
</figure> 
<p></p> 
<p>用uncompyle6反编译。</p> 
<figure class="image"> 
 <img alt="" height="200" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5e9db79895a040c7bdd52c2b0cfd339d.png" width="435"> 
 <figcaption>
   uncompyle6 -o flag.py flag.pyc 
 </figcaption> 
</figure> 
<p></p> 
<p>查看flag.py是一段加密代码，encode函数把flag字符串加密成ciphertext。</p> 
<pre data-index="5" class="set-code-hide" name="code"><code class="language-python hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-comment"># uncompyle6 version 3.8.0</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-comment"># Python bytecode 2.7 (62211)</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-comment"># Decompiled from: Python 3.10.7 (main, Oct  1 2022, 04:31:04) [GCC 12.2.0]</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-comment"># Embedded file name: flag.py</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-comment"># Compiled at: 2017-12-05 23:42:15</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">import</span> base64</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="9"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">def</span> <span class="hljs-title function_">encode</span>():</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="10"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    flag = <span class="hljs-string">'*************'</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="11"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    ciphertext = []</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="12"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-keyword">for</span> i <span class="hljs-keyword">in</span> <span class="hljs-built_in">range</span>(<span class="hljs-built_in">len</span>(flag)):</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="13"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        s = <span class="hljs-built_in">chr</span>(i ^ <span class="hljs-built_in">ord</span>(flag[i]))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="14"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">if</span> i % <span class="hljs-number">2</span> == <span class="hljs-number">0</span>:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="15"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">            s = <span class="hljs-built_in">ord</span>(s) + <span class="hljs-number">10</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="16"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">else</span>:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="17"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">            s = <span class="hljs-built_in">ord</span>(s) - <span class="hljs-number">10</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="18"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        ciphertext.append(<span class="hljs-built_in">str</span>(s))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="19"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="20"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-keyword">return</span> ciphertext[::-<span class="hljs-number">1</span>]</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="21"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="22"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="23"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">ciphertext = [</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="24"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-string">'96'</span>, <span class="hljs-string">'65'</span>, <span class="hljs-string">'93'</span>, <span class="hljs-string">'123'</span>, <span class="hljs-string">'91'</span>, <span class="hljs-string">'97'</span>, <span class="hljs-string">'22'</span>, <span class="hljs-string">'93'</span>, <span class="hljs-string">'70'</span>, <span class="hljs-string">'102'</span>, <span class="hljs-string">'94'</span>, <span class="hljs-string">'132'</span>,</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="25"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-string">'46'</span>, <span class="hljs-string">'112'</span>, <span class="hljs-string">'64'</span>, <span class="hljs-string">'97'</span>, <span class="hljs-string">'88'</span>, <span class="hljs-string">'80'</span>, <span class="hljs-string">'82'</span>, <span class="hljs-string">'137'</span>, <span class="hljs-string">'90'</span>, <span class="hljs-string">'109'</span>, <span class="hljs-string">'99'</span>, <span class="hljs-string">'112'</span>]</div></div></li></ol></code><div class="hide-preCode-box"><span class="hide-preCode-bt"><img class="look-more-preCode contentImg-no-view" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newCodeMoreWhite.png" alt="" title=""></span></div><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<p>我们编写decode函数把ciphertext解密得到 flag{Y@e_Cl3veR_C1Ever!}</p> 
<pre data-index="6" class="set-code-hide" name="code"><code class="language-python hljs"><ol class="hljs-ln" style="width:100%"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">def</span> <span class="hljs-title function_">decode</span>():</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    ciphertext = [</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-string">'96'</span>, <span class="hljs-string">'65'</span>, <span class="hljs-string">'93'</span>, <span class="hljs-string">'123'</span>, <span class="hljs-string">'91'</span>, <span class="hljs-string">'97'</span>, <span class="hljs-string">'22'</span>, <span class="hljs-string">'93'</span>, <span class="hljs-string">'70'</span>, <span class="hljs-string">'102'</span>, <span class="hljs-string">'94'</span>, <span class="hljs-string">'132'</span>,</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-string">'46'</span>, <span class="hljs-string">'112'</span>, <span class="hljs-string">'64'</span>, <span class="hljs-string">'97'</span>, <span class="hljs-string">'88'</span>, <span class="hljs-string">'80'</span>, <span class="hljs-string">'82'</span>, <span class="hljs-string">'137'</span>, <span class="hljs-string">'90'</span>, <span class="hljs-string">'109'</span>, <span class="hljs-string">'99'</span>, <span class="hljs-string">'112'</span>]</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    ciphertext = ciphertext[::-<span class="hljs-number">1</span>]</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    flag = []</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-keyword">for</span> i, s <span class="hljs-keyword">in</span> <span class="hljs-built_in">enumerate</span>(ciphertext):</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">if</span> i % <span class="hljs-number">2</span> == <span class="hljs-number">0</span>:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="9"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">            s = <span class="hljs-built_in">int</span>(s) - <span class="hljs-number">10</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="10"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        <span class="hljs-keyword">else</span>:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="11"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">            s = <span class="hljs-built_in">int</span>(s) + <span class="hljs-number">10</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="12"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        s = <span class="hljs-built_in">chr</span>(i ^ s)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="13"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">        flag.append(s)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="14"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    <span class="hljs-built_in">print</span>(<span class="hljs-string">''</span>.join(flag))</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="15"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="16"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="17"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">if</span> __name__ == <span class="hljs-string">'__main__'</span>:</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="18"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">    decode()</div></div></li></ol></code><div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view="{&quot;spm&quot;:&quot;1001.2101.3001.7365&quot;}"><img class="look-more-preCode contentImg-no-view" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newCodeMoreWhite.png" alt="" title=""></span></div><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<h3><a name="t73"></a>uncompyle6报错和配置</h3> 
<p>刚安装完，执行 uncompyle6 -h 发现报错。参看<a class="link-info" href="https://blog.csdn.net/principle1/article/details/122064538" title="大神的博客">大神的博客</a>知道是python版本不在uncompyle6一个文件的内部。修改文件，增加自己的python版本（我是3.10.7）。这样就正常了。</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/52283b453681408f803142d0ccb35085.png" width="175"> 
 <figcaption>
   uncompyle6 -h 发现报错 
 </figcaption> 
</figure> 
<p></p> 
<figure class="image"> 
 <img alt="" height="421" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7d79701d3eae4769a861dd0108010a92.png" width="1200"> 
 <figcaption>
   查找过程 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="380" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9c7bfb53ad1240f39f426edfea7cfadc.png" width="1200"> 
 <figcaption>
   编辑 ~/.local/lib/python3.10/site-packages/xdis/magics.py 找到 add_canonic_versions 把版本加进去 
 </figcaption> 
</figure> 
<h2><a name="t74"></a>[ACTF新生赛2020]swp【zip伪加密、vim】</h2> 
<p>附件wget.pcapng，协议里只有GET方法的HTTP流。虽然传输有大量图片，但是图片都没用。最后两次HTTP的GET内容有用，一个secret.zip（有密码），另一个是提示。hint.html页面上写着“&lt;h1&gt;you don't need password&lt;/h1&gt;”。那就是伪加密，修改后加密标志位可以解压缩。</p> 
<blockquote> 
 <p>(http.request.method==GET &amp;&amp; (http.request.uri contains "zip" || http.request.uri contains "hint")) || http.response.code==200</p> 
</blockquote> 
<p><img alt="" height="290" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1ca5016402b5485291c0375a1e2c7ca3.png" width="1200"></p> 
<p><img alt="" height="327" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/845a10eadbcd422984a7f7cfc0f16cde.png" width="1200"></p> 
<p>secret.zip解压缩得到 flag（elf文件） 和 .flag.swp 两个文件。看来是在用vim篡改这个elf文件。使用 vim -r flag 可以恢复flag文件，可以看看<a class="link-info" href="https://blog.csdn.net/qq_42200183/article/details/81531422" title="这篇博客">这篇博客</a>。但是不管原来的还是恢复的flag.elf都不能执行，执行就报错。最后用010Editor打开flag.elf直接搜索 actf 找到 actf{c5558bcf-26da-4f8b-b181-b61f3850b9e5}，提交 flag{c5558bcf-26da-4f8b-b181-b61f3850b9e5}</p> 
<h2><a name="t75"></a>[GXYCTF2019]SXMgdGhpcyBiYXNlPw==【base64隐写】</h2> 
<p>附件是flag.txt，有66行base64编码，解码得到一首法语诗（歌词）？内容不太重要。<span style="color:#fe2c24;"><strong>重点是以后看到有多行base64编码，的就考虑base64隐写。</strong></span>此题与 [ACTF新生赛2020]base64隐写 异曲同工。参考这题代码得到 GXY{fazhazhenhaoting}，提交 flag{fazhazhenhaoting}</p> 
<h2><a name="t76"></a>间谍启示录【隐藏文件】</h2> 
<p>附件是一个iso镜像，可以解压缩得到内容。文档说明里面有提示“杀毒杀掉的恰恰是线索”。</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2cb9f39972a8488884cf7e9964e1977b.png" width="351"> 
 <figcaption>
   杀毒杀掉的恰恰是线索 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e48b1eb6d7724ef0b5c0eff9b94d75e9.png" width="292"> 
 <figcaption>
   把flag写入文件 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e9f03ec3d9d645f0baf5066d12dc10aa.png" width="259"> 
 <figcaption>
   文件属性是“隐藏” 
 </figcaption> 
</figure> 
<p></p> 
<p>执行一下 systemzx.exe，发现是解压缩，得到 文件已被销毁.exe。执行 文件已被销毁.exe，没有反应，但这个exe肯定有问题。010Editor打开 文件已被销毁.exe，搜索 flag 发现里面有del flag.exe字样。binwalk 一下发现还真有 flag.exe。</p> 
<p>执行flag.exe发现没有反应。用IDA反编译一下，发现flag.exe打开了一个文件写入了flag。dir /r 看目录下并没有多出文件，但是按tab键逐个查看时发现有 机密文件.txt，这个文件的属性是“隐藏”。里面就是 flag{379:7b758:g7dfe7f19:9464f:4g9231}</p> 
<h2><a name="t77"></a>Mysterious【反编译】</h2> 
<p>附件 Mysterious.exe，打开后让输入字符串进行crack。没发现其他问题，用IDA反编译。</p> 
<p>WinMain中调用了DialogBoxParamA函数弹出对话框，回调函数是DialogFunc函数。DialogFunc函数调用sub_401090函数。我们需要构造String，让程序走到“well done”。这需要让v4=atoi(String)得到122，String的后3个字符为xyz。所以输入 122xyz，得到 flag{123_Buff3r_0v3rf|0w}</p> 
<p><img alt="" height="153" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5fa21f2d514d4e4eb9c6d07eef131ef9.png" width="966"></p> 
<p><img alt="" height="658" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7393a727e9294ef4aca7615b7c469f4b.png" width="1200"></p> 
<h2><a name="t78"></a>[UTCTF2020]docx【word本质是zip】</h2> 
<p>附件 [UTCTF2020]docx.docx。改为[UTCTF2020]docx.zip，解压缩，在word/media目录里找到image23.png图片，得到 flag{unz1p_3v3ryth1ng}</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7320ab6762014c27900a5b834109711d.png" width="200"> 
 <figcaption>
   image23.png 
 </figcaption> 
</figure> 
<h2><a name="t79"></a>弱口令【cloacked-pixel隐写工具】</h2> 
<p>附件 弱口令.zip 有密码。打开发现有备注。备注空格、制表符对应点、横线。</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/06c815fca4dc44889bab4f880a7a5259.png" width="572"> 
 <figcaption>
   备注空格、制表符对应点、横线 
 </figcaption> 
</figure> 
<p>对应的点、横线再摩斯码解码得到hell0forum，但这不是密码。</p> 
<blockquote> 
 <p>...././.-../.-../-----/..-./---/.-./..-/--<br> hell0forum</p> 
</blockquote> 
<p>把hell0forum存成一个txt文件作为字典，用ARCHPR工具字典爆破密码得到 HELL0FORUM</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a708fbddf2c54bcc9cdd78fc8e7c6257.png" width="313"> 
 <figcaption>
   字典破解得到密码 HELL0FORUM 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/71d2956354624a868c967e9233d89161.png" width="100"> 
 <figcaption>
   女神.png 
 </figcaption> 
</figure> 
<p>用 HELL0FORUM 密码解压缩得到 女神.png 图片。</p> 
<p>看writeup知道有个<a class="link-info" href="https://github.com/livz/cloacked-pixel" title="cloacked-pixel隐写工具">cloacked-pixel隐写工具</a>。这个工具比较老，需要python2环境。</p> 
<blockquote> 
 <p>conda create -n py27 python=2.7</p> 
 <p>conda activate py27</p> 
 <p>conda install numpy matplotlib pandas pillow pycryptodome</p> 
 <p>python .\lsb.py extract .\ns.png ns.txt 123456</p> 
</blockquote> 
<p>搭好环境，用弱口令 123456 执行命令得到 ns.txt，内容 flag{jsy09-wytg5-wius8}</p> 
<h2><a name="t80"></a>[RoarCTF2019]黄金6年【Kinovea工具】</h2> 
<p>附件是个mp4视频，用010Editor打开，发现结尾有base64编码。转码发现 Rar! 字样，考虑转成二进制文件，写段代码转成flag.rar文件（有密码）。</p> 
<pre data-index="7"><code class="language-python hljs"><ol class="hljs-ln" style="width:1883px"><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="1"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"><span class="hljs-keyword">import</span> base64</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="2"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="3"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">c = <span class="hljs-string">b'UmFyIRoHAQAzkrXlCgEFBgAFAQGAgADh7ek5VQIDPLAABKEAIEvsUpGAAwAIZmxhZy50eHQwAQADDx43HyOdLMGWfCE9WEsBZprAJQoBSVlWkJNS9TP5du2kyJ275JzsNo29BnSZCgMC3h+UFV9p1QEfJkBPPR6MrYwXmsMCMz67DN/k5u1NYw9ga53a83/B/t2G9FkG/IITuR+9gIvr/LEdd1ZRAwUEAA=='</span></div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="4"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">p = base64.b64decode(c)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="5"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line"> </div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="6"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f = <span class="hljs-built_in">open</span>(<span class="hljs-string">'flag.rar'</span>, <span class="hljs-string">'wb'</span>)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="7"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f.write(p)</div></div></li><li><div class="hljs-ln-numbers"><div class="hljs-ln-line hljs-ln-n" data-line-number="8"></div></div><div class="hljs-ln-code"><div class="hljs-ln-line">f.close()</div></div></li></ol></code><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<p>之后看writeup才知道要到视频里找解压密码。用Kinovea打开逐帧查看，我找到了3帧有二维码，扫码得到key1、key2和key3。据说key4要用Adobe Pr才能找到，反正我是没找到。拼起来密码iwantplayctf，解压缩得到flag.txt，提交flag{CTF-from-RuMen-to-RuYuan}</p> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/402b7d6165ad49a1bdf7e183482d6fd8.png" width="86"> 
 <figcaption>
   标题 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ec20fa89bcbd4c85a9a61bfcbfc2de13.png" width="85"> 
 <figcaption>
   key2:want 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d5aa1774228045e89e0207263c368740.png" width="73"> 
 <figcaption>
   key3:play 
 </figcaption> 
</figure> 
<h2><a name="t81"></a>小易的U盘【autorun.inf文件】</h2> 
<p>附件iso文件解压缩，得到U盘内容。下面是提示：</p> 
<blockquote> 
 <p>小易的U盘中了一个奇怪的病毒，电脑中莫名其妙会多出来东西。小易重装了系统，把U盘送到了攻防实验室，希望借各位的知识分析出里面有啥。请大家加油噢，不过他特别关照，千万别乱点他U盘中的资料，那是机密。</p> 
</blockquote> 
<p>用windows日记本（不是日志查看器）打开jnt文件，没有flag。flag.txt说flag还没有生成，那么查看编号2-64的autoflag.exe程序，但是运行也没有结果，生成的flag.avi文件里面只有单一的字符f。</p> 
<p>直接看writeup。autorun.inf 作用是允许在双击磁盘时自动运行指定的某个文件。但是近几年出现了用autorun.inf文件传播木马或病毒，它通过使用者的误操作让目标程序执行，达到侵入电脑的目的，带来了很大的负面影响。</p> 
<p>U盘里有 autorun.inf 文件，自动运行的是autoflag - 副本 (32)。用IDA反编译，得到&nbsp;flag{29a0vkrlek3eu10ue89yug9y4r0wdu10}。</p> 
<blockquote> 
 <p><strong>autorun.inf 里面 Open的含义</strong><br> 含义：指定设备启用时运行之命令行。<br> 格式：Open=命令行（命令行：程序路径名 [参数])<br> 参数：<br> 命令行：自动运行的命令行，必须是 .exe、.com、.bat 文件，其他格式文件可以使用start.exe打开或使用ShellExecute命令。</p> 
</blockquote> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a588a439be6543a694fc1d23ab945cf5.png" width="263"> 
 <figcaption>
   autorun.inf 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="100" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7fea58f76ca2486fa0928f6a66b2e157.png" width="323"> 
 <figcaption>
   标题 
 </figcaption> 
</figure> 
<h2><a name="t82"></a>[WUSTCTF2020]爬</h2> 
<p>附件是pdf，提示flag被图片覆盖住了，用Adobe编辑pdf，把图片删掉，下面还有一张图片。内容hex转成ascii得到&nbsp; wctf2020{th1s_1s_@_pdf_and_y0u_can_use_phot0sh0p}</p> 
<pre data-index="8"><code class="language-python hljs"><span class="hljs-number">0x77637466323032307b746831735f31735f405f7064665f616e645f7930755f63616e5f7573655f70686f7430736830707d</span></code><div class="hljs-button {2}" data-title="复制" onclick="hljs.copyCode(event)"></div></pre> 
<p><img alt="" height="663" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/01c267fea79e4aa49a7f059979f7f331.png" width="658"></p> 
<h2><a name="t83"></a>[WUSTCTF2020]alison_likes_jojo【john、outguess】</h2> 
<p>附件boki.jpg、jljy.jpg两个图片。</p> 
<figure class="image"> 
 <img alt="" height="198" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/92ff88f1e75f4512ab65fbf316150e60.jpeg" width="198"> 
 <figcaption>
   boki.jpg 
 </figcaption> 
</figure> 
<figure class="image"> 
 <img alt="" height="198" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f7c2c9b0655742db94779283267fc35a.jpeg" width="168"> 
 <figcaption>
   jljy.jpg 
 </figcaption> 
</figure> 
<p></p> 
<p>用binwalk从boki.jpg里分离出2B3DB.zip，在用john破解出压缩密码888866，解压缩得到beisi.txt。beisi.txt内容经过3次base64解码得到killerqueen。</p> 
<p><img alt="" height="298" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1bb8ddfa98914e4cab2e6d77d0919d73.png" width="867"></p> 
<p>再用outguess命令<span style="background-color:#cccccc;"> outguess -k killerqueen -r jljy.jpg jljy.txt </span>从jljy.jpg里面得到flag为 wctf2020{pretty_girl_alison_likes_jojo}</p> 
<h2 style="background-color:transparent;"><a name="t84"></a>[安洵杯 2019]吹着贝斯扫二维码</h2> 
<p>附件是加密的flag.zip和其他36个文件。36个文件都是jpg图片，且能拼图成一个二维码。扫描得到BASE Family Bucket ??? 85-&gt;64-&gt;85-&gt;13-&gt;16-&gt;32。</p> 
<p>flag.zip的备注是：GNATOMJVIQZUKNJXGRCTGNRTGI3EMNZTGNBTKRJWGI2UIMRRGNBDEQZWGI3DKMSFGNCDMRJTII3TMNBQGM4TERRTGEZTOMRXGQYDGOBWGI2DCNBY</p> 
<p>随波逐流可以先base32解码，再base16解码，看来要把上面的过程反向做一遍。32-&gt;16-&gt;13-&gt;85-&gt;64-&gt;85解码，其中13指的是rot13，最终得到解压密码ThisIsSecret!233。</p> 
<p>解压缩得到flag.txt内容是 flag{Qr_Is_MeAn1nGfuL}</p> 
<p></p> 
<h2 style="background-color:transparent;"><a name="t85"></a>Next</h2>
                </div><div data-report-view="{&quot;mod&quot;:&quot;1585297308_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6548&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/*********&quot;,&quot;extend1&quot;:&quot;pc&quot;,&quot;ab&quot;:&quot;new&quot;}"><div></div></div>
        </div>
        
    </article>
<script>
  $(function() {
    setTimeout(function () {
      var mathcodeList = document.querySelectorAll('.htmledit_views img.mathcode');
      if (mathcodeList.length > 0) {
        for (let i = 0; i < mathcodeList.length; i++) {
          if (mathcodeList[i].naturalWidth === 0 || mathcodeList[i].naturalHeight === 0) {
            var alt = mathcodeList[i].alt;
            alt = '\\(' + alt + '\\)';
            var curSpan = $('<span class="img-codecogs"></span>');
            curSpan.text(alt);
            $(mathcodeList[i]).before(curSpan);
            $(mathcodeList[i]).remove();
          }
        }
        MathJax.Hub.Queue(["Typeset",MathJax.Hub]);
      }
    }, 1000)
  });
</script>
</div>
<div class="directory-boxshadow-dialog" style="display:none;">
  <div class="directory-boxshadow-dialog-box">
  </div>
  <div class="vip-limited-time-offer-box">
    <div class="vip-limited-time-offer-content">
      <img class="limited-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/vip-limited-close.png">
      <div class="limited-box">
        <span class="limited-num"></span>
        <span class="limited-quan"> 优惠劵</span>
      </div>
      <div class="limited-time-box">
        <span class="time-hour"></span>
        <span class="time-minite"></span>
        <span class="time-second"></span>
      </div>
      <a class="limited-time-btn" href="https://mall.csdn.net/vip" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.9621&quot;}" data-report-query="spm=1001.2101.3001.9621"></a>
    </div>
  </div>
</div>    <div class="more-toolbox-new more-toolbox-active" id="toolBarBox">
      
      <div class="left-toolbox" style="position: fixed; z-index: 999; left: 340.333px; bottom: 0px; width: 1010px;">
        <div class="toolbox-left">
            <div class="profile-box">
              <a class="profile-href" target="_blank" href="https://blog.csdn.net/qpeity"><img class="profile-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/6b2759cb62f743cf81d6a2848214a29c_qpeity.jpg!1">
                <span class="profile-name">
                  苦行僧(csdn)
                </span>
              </a>
            </div>
            <div class="profile-attend">
              
                <a class="tool-attend tool-bt-button tool-bt-attend" href="javascript:;">关注</a>
              <a class="tool-item-follow active-animation" style="display:none;">关注</a>
            </div>
        </div>
        <div class="toolbox-middle">
          <ul class="toolbox-list">
            <li class="tool-item tool-item-size tool-active is-like" id="is-like">
              <a class="tool-item-href">
                <img style="display:none;" id="is-like-imgactive-animation-like" class="animation-dom active-animation" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/tobarThumbUpactive.png" alt="">
                <img class="isactive" style="display:none" id="is-like-imgactive" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newHeart2021Active.png" alt="">
                <img class="isdefault" style="display:block" id="is-like-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newHeart2021Black.png" alt="">
                <span id="spanCount" class="count ">
                      0
                </span>
              </a>
              <div class="tool-hover-tip"><span class="text space">点赞</span></div>
            </li>
            <li class="tool-item tool-item-size tool-active is-unlike" id="is-unlike">
              <a class="tool-item-href">
                <img class="isactive" style="margin-right:0px;display:none" id="is-unlike-imgactive" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newUnHeart2021Active.png" alt="">
                <img class="isdefault" style="margin-right:0px;display:block" id="is-unlike-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newUnHeart2021Black.png" alt="">
                <span id="unlikeCount" class="count "></span>
              </a>
              <div class="tool-hover-tip"><span class="text space">踩</span></div>
            </li>
            <li class="tool-item tool-item-size tool-active is-collection ">
              <a class="tool-item-href" href="javascript:;" data-report-click="{&quot;mod&quot;:&quot;popu_824&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4130&quot;,&quot;ab&quot;:&quot;new&quot;}">
                <img style="display:none" id="is-collection-img-collection" class="animation-dom active-animation" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/tobarCollectionActive.png" alt="">
                <img class="isdefault" id="is-collection-img" style="display:block" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newCollectBlack.png" alt="">
                <img class="isactive" id="is-collection-imgactive" style="display:none" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newCollectActive.png" alt="">
                <span class="count get-collection" id="get-collection" style="color: rgb(153, 154, 170);">
                    3
                </span>
              </a>
              <div class="tool-hover-tip collect">
                <div class="collect-operate-box">
                  <span class="collect-text" id="is-collection">
                    收藏
                  </span>
                </div>
              </div>
              <div class="tool-active-list">
                <div class="text">
                  觉得还不错?
                  <span class="collect-text" id="tool-active-list-collection">
                    一键收藏
                  </span>
                 <img id="tool-active-list-close" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/collectionCloseWhite.png" alt="">
                </div>
              </div>
            </li>
                <li class="tool-item tool-item-size tool-active tool-item-reward">
                  <a class="tool-item-href" href="javascript:;" data-report-click="{&quot;mod&quot;:&quot;popu_830&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4237&quot;,&quot;dest&quot;:&quot;&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <img class="isdefault reward-bt" id="rewardBtNew" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newRewardBlack.png" alt="打赏">
                    <span class="count"></span>
                  </a>
                  <div class="tool-hover-tip"><span class="text space">打赏</span></div>
                </li>
          <li class="tool-item tool-item-size tool-active tool-item-comment">
            
              <a class="tool-item-href go-side-comment" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7009&quot;}">
              <img class="isdefault" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newComment2021Black.png" alt="">
              <span class="count">0</span>
            </a>
            <div class="tool-hover-tip"><span class="text space">评论</span></div>
          </li>
          <li class="tool-item tool-item-bar">
          </li>
          <li class="tool-item tool-item-size tool-active tool-QRcode" data-type="article" id="tool-share">
            <a class="tool-item-href" href="javascript:;" data-report-click="{&quot;mod&quot;:&quot;1582594662_002&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4129&quot;,&quot;ab&quot;:&quot;new&quot;}">
              <img class="isdefault" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newShareBlack.png" alt="">
            </a>
              <div class="QRcode" id="tool-QRcode">
            <div class="share-bg-icon icon1 icon3" id="shareBgIcon"></div>
              <div class="share-bg-box">
                <div class="share-content">
                    <img class="share-avatar" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/6b2759cb62f743cf81d6a2848214a29c_qpeity.jpg!1" alt="">
                  <div class="share-tit">
                    BUUCTF题目Misc部分wp（持续更新）
                  </div>
                  <div class="share-dec">
                    BUUCTF题目Misc部分wp（持续更新）
                  </div>
                  <a id="copyPosterUrl" class="url" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7493&quot;}">复制链接</a>
                </div>
                <div class="share-code">
                  <div class="share-code-box" id="shareCode"><canvas width="65" height="65"></canvas><img style="display: none;"></div>
                  <div class="share-code-text">扫一扫</div>
                </div>
              </div>
                <div class="share-code-type"><p class="hot" data-type="hot"><span>热门</span></p><p class="vip" data-type="vip"><span>VIP</span></p></div>
            </div>
          </li>
        </ul>
      </div>
      <div class="toolbox-right">
            <div class="tool-directory">
                <a class="bt-columnlist-show" data-id="10883970" data-free="true" data-subscribe="false" data-title="信息安全" data-img="https://img-blog.csdnimg.cn/20210315134731469.png?x-oss-process=image/resize,m_fixed,h_224,w_224" data-url="https://blog.csdn.net/qpeity/category_10883970.html" data-sum="14" data-people="3" data-price="0" data-oldprice="0" data-join="false" data-studyvip="false" data-studysubscribe="false" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.6334&quot;,&quot;extend1&quot;:&quot;专栏目录&quot;}">专栏目录</a>
          </div>
</div>
</div>
</div>
<script type="text/javascript" crossorigin="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/qrcode-7c90a92189.min.js.下载"></script>
<script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/saved_resource(2)" type="text/javascript"></script>
<script type="text/javascript" crossorigin="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-login-box.js.下载"></script>
<script type="text/javascript" crossorigin="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/collection-box.js.下载"></script>                <div class="first-recommend-box recommend-box ">
<div class="recommend-item-box type_download clearfix" data-url="https://download.csdn.net/download/qq_43625917/13117832" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6661.1&quot;,&quot;mod&quot;:&quot;popu_871&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant_t0.none-task-download-2~default~CTRLIST~Paid-1-13117832-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;1&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Paid&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/qq_43625917/13117832&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://download.csdn.net/download/qq_43625917/13117832" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6661.1&quot;,&quot;mod&quot;:&quot;popu_871&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant_t0.none-task-download-2~default~CTRLIST~Paid-1-13117832-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;1&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Paid&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/qq_43625917/13117832&quot;}" data-report-query="spm=1001.2101.3001.6661.1&amp;utm_medium=distribute.pc_relevant_t0.none-task-download-2%7Edefault%7ECTRLIST%7EPaid-1-13117832-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant_t0.none-task-download-2%7Edefault%7ECTRLIST%7EPaid-1-13117832-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=1">
					<div class="left ellipsis-online ellipsis-online-1">2020上海大学生网络安全赛<em>MISC</em><em>题目</em>附件</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">11-16</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://download.csdn.net/download/qq_43625917/13117832" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6661.1&quot;,&quot;mod&quot;:&quot;popu_871&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant_t0.none-task-download-2~default~CTRLIST~Paid-1-13117832-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;1&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Paid&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/qq_43625917/13117832&quot;}" data-report-query="spm=1001.2101.3001.6661.1&amp;utm_medium=distribute.pc_relevant_t0.none-task-download-2%7Edefault%7ECTRLIST%7EPaid-1-13117832-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant_t0.none-task-download-2%7Edefault%7ECTRLIST%7EPaid-1-13117832-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=1">
				<div class="desc ellipsis-online ellipsis-online-1">这个资源是2020上海大学生网络安全赛<em>MISC</em><em>题目</em>资源，包括：<em>MISC</em><em>题目</em>附件、<em>题目</em>描述以及<em>题目</em>WriteUp。</div>
			</a>
		</div>
	</div>
</div>
                </div>
              <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/pc_wap_commontools-094b8ec121.min.js.下载" type="text/javascript" async=""></script>
                <div class="second-recommend-box recommend-box ">
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/Ivyyyyyy1/article/details/125590719" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.1&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-1-125590719-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;1&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Ivyyyyyy1/article/details/125590719&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/Ivyyyyyy1/article/details/125590719" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.1&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-1-125590719-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;1&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Ivyyyyyy1/article/details/125590719&quot;}" data-report-query="spm=1001.2101.3001.6650.1&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-1-125590719-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-1-125590719-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=2">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>Misc</em> <em>wp</em>大合集(1)</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/Ivyyyyyy1" target="_blank" data-report-query="spm=1001.2101.3001.6650.1&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-1-125590719-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-1-125590719-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=2"><span class="blog-title">Ivyyyyyy1的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">07-03</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					1426
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/Ivyyyyyy1/article/details/125590719" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.1&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-1-125590719-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;1&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Ivyyyyyy1/article/details/125590719&quot;}" data-report-query="spm=1001.2101.3001.6650.1&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-1-125590719-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-1-125590719-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=2">
				<div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>Misc</em> 杂项方向write up合集(一)，<em>持续</em><em>更新</em>中</div>
			</a>
		</div>
	</div>
</div>
                </div>
<a id="commentBox" name="commentBox"></a>
<div id="pcCommentBox" class="comment-box comment-box-new2 login-comment-box-new comment-box-nostyle" style="display:none">
    <div class="has-comment" style="display: none;">
      <div class="one-line-box">
        <div class="has-comment-tit go-side-comment">
          <span class="count">0</span>&nbsp;条评论
        </div>
        <div class="has-comment-con comment-operate-item"></div>
        <a class="has-comment-bt-right go-side-comment focus">写评论</a>
      </div>
    </div>
</div>              <div class="recommend-box insert-baidu-box recommend-box-style ">
                <div class="recommend-item-box no-index" style="display:none"></div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/qpeity/article/details/127691084" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-127691084-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;0\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.1&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/127691084&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;0&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/qpeity/article/details/127691084" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-127691084-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;0\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.1&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/127691084&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;0&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-127691084-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.1&amp;utm_relevant_index=3">					                <div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em><em>题目</em>N1BOOK<em>部分</em><em>wp</em>(<em>持续</em><em>更新</em>)_[第九章 <em>ctf</em>之<em>misc</em>章]两个<em>部分</em>的...</div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">10-7</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/qpeity/article/details/127691084" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-127691084-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;0\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.1&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/127691084&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;0&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-0-127691084-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.1&amp;utm_relevant_index=3">                      <div class="desc ellipsis-online ellipsis-online-1">2.jpg -属性-备注 写了 Part1: n1book{414e529ece64a77d。 把两<em>部分</em>合并得到答案。提交 flag{414e529ece64a77d25cc9ee5108a49c6} [第九章 <em>CTF</em>之<em>MISC</em>章]压缩包中的乐趣.zip【zip伪加密、明文攻击重点】 附件 1.zip。是伪加...</div>                    </a>                  </div>                </div>              </div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/weixin_43785509/article/details/132008670" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-132008670-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;0\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.2&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/weixin_43785509/article/details/132008670&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;1&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/weixin_43785509/article/details/132008670" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-132008670-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;0\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.2&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/weixin_43785509/article/details/132008670&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;1&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-132008670-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.2&amp;utm_relevant_index=4">					                <div class="left ellipsis-online ellipsis-online-1">...<em>BUUCTF</em>练习汇总(1-25题,不包括签到题)_<em>buuctf</em> 金_燕麦葡萄干的<em>博客</em>...</div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">10-12</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/weixin_43785509/article/details/132008670" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-132008670-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;0\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.2&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/weixin_43785509/article/details/132008670&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;1&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-1-132008670-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.2&amp;utm_relevant_index=4">                      <div class="desc ellipsis-online ellipsis-online-1"><em>CTF</em>-AWD-WEB AWD 模式下的WEB试题仓库 上传一些参加过的<em>CTF</em>线下赛AWD模式的WEB试题等 <em>BUUCTF</em><em>题目</em><em>Misc</em><em>部分</em><em>wp</em>(<em>持续</em><em>更新</em>) <em>苦行僧</em>的妖孽日常 970  <em>BUUCTF</em><em>题目</em><em>Misc</em><em>部分</em><em>wp</em>(<em>持续</em><em>更新</em>)  <em>buuctf</em>_<em>misc</em>刷题 cppuHsir的<em>博客</em> 336  <em>BUUCTF</em>_<em>MISC</em>刷...</div>                    </a>                  </div>                </div>              </div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/qpeity/article/details/132206951" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.2&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~YuanLiJiHua~Position-2-132206951-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;2&quot;,&quot;strategy&quot;:&quot;2~default~YuanLiJiHua~Position&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/132206951&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/qpeity/article/details/132206951" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.2&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~YuanLiJiHua~Position-2-132206951-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;2&quot;,&quot;strategy&quot;:&quot;2~default~YuanLiJiHua~Position&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/132206951&quot;}" data-report-query="spm=1001.2101.3001.6650.2&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EYuanLiJiHua%7EPosition-2-132206951-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EYuanLiJiHua%7EPosition-2-132206951-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=5">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em><em>题目</em>Web<em>部分</em><em>wp</em>（<em>持续</em><em>更新</em>）</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/qpeity" target="_blank" data-report-query="spm=1001.2101.3001.6650.2&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EYuanLiJiHua%7EPosition-2-132206951-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EYuanLiJiHua%7EPosition-2-132206951-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=5"><span class="blog-title">苦行僧的妖孽日常</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">08-10</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					794
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/qpeity/article/details/132206951" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.2&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~YuanLiJiHua~Position-2-132206951-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;2&quot;,&quot;strategy&quot;:&quot;2~default~YuanLiJiHua~Position&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/132206951&quot;}" data-report-query="spm=1001.2101.3001.6650.2&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EYuanLiJiHua%7EPosition-2-132206951-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EYuanLiJiHua%7EPosition-2-132206951-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=5">
				<div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em><em>题目</em>Web<em>部分</em>的writeup（<em>持续</em><em>更新</em>）</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/weixin_43669045/article/details/*********" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.3&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-3-*********-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;3&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/weixin_43669045/article/details/*********&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/weixin_43669045/article/details/*********" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.3&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-3-*********-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;3&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/weixin_43669045/article/details/*********&quot;}" data-report-query="spm=1001.2101.3001.6650.3&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-3-*********-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-3-*********-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=6">
					<div class="left ellipsis-online ellipsis-online-1"><em>Buuctf</em> -web <em>wp</em>汇总(三)</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/weixin_43669045" target="_blank" data-report-query="spm=1001.2101.3001.6650.3&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-3-*********-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-3-*********-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=6"><span class="blog-title">Alexhirchi的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">07-15</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					1683
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/weixin_43669045/article/details/*********" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.3&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-3-*********-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;3&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/weixin_43669045/article/details/*********&quot;}" data-report-query="spm=1001.2101.3001.6650.3&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-3-*********-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-3-*********-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=6">
				<div class="desc ellipsis-online ellipsis-online-1"><em>Buuctf</em> -web <em>wp</em>汇总(一)：链接
<em>Buuctf</em> -web <em>wp</em>汇总(二)：链接
<em>Buuctf</em> -web <em>wp</em>汇总(三)：链接

文章目录[WUST<em>CTF</em>2020]朴实无华[WUST<em>CTF</em>2020]颜值成绩查询[GK<em>CTF</em>2020]EZ三剑客-EzWeb[CISCN2019 华北赛区 Day1 Web5]CyberPunk[V&amp;N2020 公开赛]TimeTravel[N<em>CTF</em>2019]True XML cookbook
[WUST<em>CTF</em>2020]朴实无华
要点：逻辑漏洞 函数绕过
进入.</div>
			</a>
		</div>
	</div>
</div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/CCXuLuYun/article/details/*********" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-4-*********-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;3\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.3&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/CCXuLuYun/article/details/*********&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;4&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/CCXuLuYun/article/details/*********" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-4-*********-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;3\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.3&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/CCXuLuYun/article/details/*********&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;4&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-4-*********-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.3&amp;utm_relevant_index=7">					                <div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>Misc</em><em>部分</em><em>WP</em>_Arimakitsuchirono的<em>博客</em></div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">9-27</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/CCXuLuYun/article/details/*********" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-4-*********-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;3\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.3&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/CCXuLuYun/article/details/*********&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;4&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-4-*********-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.3&amp;utm_relevant_index=7">                      <div class="desc ellipsis-online ellipsis-online-1"><em>CTF</em>{vjpw_wnoei} 实际Flag应当为 flag{vjpw_wnoei} 你竟然赶我走 下图 丢进010 看文件末尾 (可以看的出来之里出题人在嘲讽拿LSB扫的笨比) flag{stego_is_s0_bor1ing} <em>题目</em>提示的很明显了,图片尺寸有问题 直接丢近tweakpng ...</div>                    </a>                  </div>                </div>              </div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/xuanyulevel6/article/details/126072948" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-5-126072948-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;3\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.4&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/xuanyulevel6/article/details/126072948&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;5&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/xuanyulevel6/article/details/126072948" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-5-126072948-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;3\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.4&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/xuanyulevel6/article/details/126072948&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;5&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-5-126072948-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.4&amp;utm_relevant_index=8">					                <div class="left ellipsis-online ellipsis-online-1">【<em>BUUCTF</em>】<em>Misc</em>题解_<em>buuctf</em> <em>misc</em>_苏柘_level6的<em>博客</em></div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">10-12</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/xuanyulevel6/article/details/126072948" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-5-126072948-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;3\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.4&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/xuanyulevel6/article/details/126072948&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;5&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-5-126072948-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.4&amp;utm_relevant_index=8">                      <div class="desc ellipsis-online ellipsis-online-1"><em>题目</em>里面是一个exe文件,尝试打开发现无法直接打开,于是使用010Editor打开,发现是一串base64解码。因此直接解码即可 这里顺便介绍一个工具,看到别人使用的ExeInfo PE通常用来对exe文件进行分析。 大白 这题打开之后便是一张大白的图片,看了一...</div>                    </a>                  </div>                </div>              </div>
		
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/m0_73891130/article/details/129765015" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.4&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-4-129765015-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;4&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/m0_73891130/article/details/129765015&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/m0_73891130/article/details/129765015" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.4&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-4-129765015-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;4&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/m0_73891130/article/details/129765015&quot;}" data-report-query="spm=1001.2101.3001.6650.4&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-4-129765015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-4-129765015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=9">
					<div class="left ellipsis-online ellipsis-online-1"><em>CTF</em>练习题<em>WP</em>1</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/m0_73891130" target="_blank" data-report-query="spm=1001.2101.3001.6650.4&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-4-129765015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-4-129765015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=9"><span class="blog-title">m0_73891130的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">03-25</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					163
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/m0_73891130/article/details/129765015" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.4&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-4-129765015-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;4&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/m0_73891130/article/details/129765015&quot;}" data-report-query="spm=1001.2101.3001.6650.4&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-4-129765015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-4-129765015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=9">
				<div class="desc ellipsis-online ellipsis-online-1">自己写的<em>CTF</em>常用网站的练习<em>题目</em>的<em>WP</em>，当然也有参考网上的大佬的<em>WP</em></div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/qq_45149716/article/details/121734431" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.5&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-5-121734431-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;5&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45149716/article/details/121734431&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/qq_45149716/article/details/121734431" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.5&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-5-121734431-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;5&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45149716/article/details/121734431&quot;}" data-report-query="spm=1001.2101.3001.6650.5&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-5-121734431-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-5-121734431-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=10">
					<div class="left ellipsis-online ellipsis-online-1">2021黑盾杯<em>CTF</em><em>部分</em><em>WP</em></div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/qq_45149716" target="_blank" data-report-query="spm=1001.2101.3001.6650.5&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-5-121734431-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-5-121734431-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=10"><span class="blog-title">qq_45149716的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">12-05</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					4501
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/qq_45149716/article/details/121734431" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.5&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-5-121734431-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;5&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45149716/article/details/121734431&quot;}" data-report-query="spm=1001.2101.3001.6650.5&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-5-121734431-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-5-121734431-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=10">
				<div class="desc ellipsis-online ellipsis-online-1">一、 Signin
通过观察附件得到附件为一个文本 全由01组成
根据以往的经验来是这个应该是是通过PIL库将01转换为像素点来构造二维码
from PIL import Image

MAX = 500
pic = Image.new("RGB", (MAX, MAX))
str="1111...1111" #文档太大所以省略了文档内容"

i = 0
for y in range(0, MAX):
    for x in range(0, MAX):
        if str[i] == '1':</div>
			</a>
		</div>
	</div>
</div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/qq_45924653/article/details/121153917" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-8-121153917-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;5\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.5&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45924653/article/details/121153917&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;8&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/qq_45924653/article/details/121153917" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-8-121153917-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;5\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.5&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45924653/article/details/121153917&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;8&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-8-121153917-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.5&amp;utm_relevant_index=11">					                <div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>MISC</em><em>部分</em><em>题目</em><em>wp</em>_<em>buuctf</em> <em>misc</em> <em>wp</em>_c7ay.的<em>博客</em></div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">9-7</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/qq_45924653/article/details/121153917" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-8-121153917-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;5\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.5&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45924653/article/details/121153917&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;8&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-8-121153917-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.5&amp;utm_relevant_index=11">                      <div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>MISC</em><em>部分</em><em>题目</em><em>wp</em> wireshak 下载pcap文件 直接查找flag字符串password的值就是flag 金san胖 文件下载下来是gif动图 用stegsove打开一张一张查看得到flag 二维码 使用binwalk -e分离出zip文件...</div>                    </a>                  </div>                </div>              </div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/qq_61839115/article/details/128479049" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-9-128479049-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;5\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.6&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_61839115/article/details/128479049&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;9&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/qq_61839115/article/details/128479049" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-9-128479049-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;5\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.6&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_61839115/article/details/128479049&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;9&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-9-128479049-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.6&amp;utm_relevant_index=12">					                <div class="left ellipsis-online ellipsis-online-1">【<em>BUUCTF</em>】<em>MISC</em>(第一页<em>wp</em>)_<em>buuctf</em> <em>misc</em> <em>wp</em></div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">10-6</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/qq_61839115/article/details/128479049" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-9-128479049-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;5\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.6&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_61839115/article/details/128479049&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;9&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-9-128479049-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.6&amp;utm_relevant_index=12">                      <div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>Misc</em> <em>wp</em>大合集(1) Ivyyyyyy1的<em>博客</em> 1413  <em>BUUCTF</em> <em>Misc</em> 杂项方向write up合集(一),<em>持续</em><em>更新</em>中  <em>CTF</em>-新手<em>misc</em> @摘星怪的<em>博客</em> 307  流量分析使用<em>题目</em>得到密码,由于是网站登录,打开之后过滤http 清除看到login,点开得到flag{ffb...</div>                    </a>                  </div>                </div>              </div>
<div class="recommend-item-box type_download clearfix" data-url="https://download.csdn.net/download/weixin_38729685/13743408" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.6&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-6-13743408-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;6&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_38729685/13743408&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://download.csdn.net/download/weixin_38729685/13743408" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.6&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-6-13743408-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;6&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_38729685/13743408&quot;}" data-report-query="spm=1001.2101.3001.6650.6&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-6-13743408-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-6-13743408-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=13">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em>之<em>MISC</em>（<em>持续</em><em>更新</em>）</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">12-21</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://download.csdn.net/download/weixin_38729685/13743408" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.6&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-6-13743408-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;6&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_38729685/13743408&quot;}" data-report-query="spm=1001.2101.3001.6650.6&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-6-13743408-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-6-13743408-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=13">
				<div class="desc ellipsis-online ellipsis-online-1">写在前面：这两天初接触<em>ctf</em>。先从最简单的杂项做起。之前看了杂项的教程也听了课，基本没怎么看答案。于是出现了好多问题，边做题边解决问题。。虽然这样对于深入学习不太好但是对于特别懒的我还挺喜欢这样的- -有几次真的心态崩溃。写一个记录贴。kali真的是个好东西。。
解决了的问题：
kali忘了密码怎么办
kali的vmroots兼容性问题与如何<em>更新</em>源
php的基础语法
wenhex的使用方法
kali的基本工具了解
签到题
睡醒后做的第一个签到题，我可能脑子还在梦里，没有看见那么大的一个flag。

金三胖（帧隐藏问题）
一个gif图片
用stdeslove打开后用FB帧浏览器分离帧。最开始分离</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/qq_45603443/article/details/126117249" data-report-view="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.7&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-7-126117249-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;7&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45603443/article/details/126117249&quot;}">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/qq_45603443/article/details/126117249" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.7&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-7-126117249-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;7&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45603443/article/details/126117249&quot;}" data-report-query="spm=1001.2101.3001.6650.7&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-7-126117249-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-7-126117249-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=14">
					<div class="left ellipsis-online ellipsis-online-1">【第六届强网杯<em>CTF</em>-<em>Wp</em>】</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/qq_45603443" target="_blank" data-report-query="spm=1001.2101.3001.6650.7&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-7-126117249-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-7-126117249-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=14"><span class="blog-title">qq_45603443的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">08-02</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					1985
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/qq_45603443/article/details/126117249" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.7&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-7-126117249-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;7&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_45603443/article/details/126117249&quot;}" data-report-query="spm=1001.2101.3001.6650.7&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-7-126117249-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-7-126117249-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=14">
				<div class="desc ellipsis-online ellipsis-online-1">第六届强网杯全国网络安全挑战赛<em>wp</em></div>
			</a>
		</div>
	</div>
</div><div class="recommend-item-box baiduSearch clearfix" data-url="https://blog.csdn.net/u013119911/article/details/125694432" data-type="blog" data-report-view="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-12-125694432-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;7\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.7&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/u013119911/article/details/125694432&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;12&quot;}">	                <div class="content-box">		                <div class="content-blog display-flex">			                  <div class="title-box">				                  <a class="tit" href="https://blog.csdn.net/u013119911/article/details/125694432" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-12-125694432-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;7\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.7&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/u013119911/article/details/125694432&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;12&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-12-125694432-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.7&amp;utm_relevant_index=15">					                <div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em>刷题记录——<em>MISC</em><em>部分</em>解答(一)_<em>buuctf</em> <em>misc</em>答案</div>				                  </a>			                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">10-12</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="https://blog.csdn.net/u013119911/article/details/125694432" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-12-125694432-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;,\&quot;parent_index\&quot;:\&quot;7\&quot;}&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4242.7&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/u013119911/article/details/125694432&quot;,&quot;strategy&quot;:&quot;2~default~baidujs_baidulandingword~default&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;index&quot;:&quot;12&quot;}" data-report-query="utm_medium=distribute.pc_relevant.none-task-blog-2~default~baidujs_baidulandingword~default-12-125694432-blog-*********.235^v38^pc_relevant_sort&amp;spm=1001.2101.3001.4242.7&amp;utm_relevant_index=15">                      <div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em>刷题记录——<em>MISC</em><em>部分</em>解答(一) 一、签到 直接打开签到就行了 二、 下载得到gif动图,发现动图中一闪而过flag,拖进PS中,打开时间轴,即可发现flag。 即flag{hellohongke},也可以用Stegsolve软件(需要安装java环境)——Frame ...</div>                    </a>                  </div>                </div>              </div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/ZxC789456302/article/details/127137257">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/ZxC789456302/article/details/127137257" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.8&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-8-127137257-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;8&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/ZxC789456302/article/details/127137257&quot;}" data-report-query="spm=1001.2101.3001.6650.8&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-8-127137257-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-8-127137257-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=16">
					<div class="left ellipsis-online ellipsis-online-1"><em>ctf</em>show大<em>部分</em><em>wp</em>(附带个人整理知识笔记)</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/ZxC789456302" target="_blank" data-report-query="spm=1001.2101.3001.6650.8&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-8-127137257-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-8-127137257-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=16"><span class="blog-title">ZxC789456302的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">10-01</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					5356
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/ZxC789456302/article/details/127137257" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.8&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-8-127137257-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;8&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/ZxC789456302/article/details/127137257&quot;}" data-report-query="spm=1001.2101.3001.6650.8&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-8-127137257-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-8-127137257-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=16">
				<div class="desc ellipsis-online ellipsis-online-1">WEB信息搜集入门（前10道都是水题，10题后没有水题了）源码中即为flag（签到题）前端js拦截，查看源代码即可，得到flagflag在响应头里面在访问robots.txt，得知存在flagishere.txt文件，访问获得flagphps源码泄露，访问index.phps，下载文件打开得到flag访问www.zip获取配置文件得知在服务器下存在fl000g.txt，线上访问得到flag访问.git（隐藏文件）注意格式为/.git/，得到flag隐藏文件的第二种情况，访问.svn，格式为/.svn/，得到</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/qq_52820087/article/details/125354627">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/qq_52820087/article/details/125354627" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.9&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-9-125354627-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;9&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_52820087/article/details/125354627&quot;}" data-report-query="spm=1001.2101.3001.6650.9&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-9-125354627-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-9-125354627-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=17">
					<div class="left ellipsis-online ellipsis-online-1">2022年暑期<em>CTF</em>刷题<em>WP</em>（停止<em>更新</em>）</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/qq_52820087" target="_blank" data-report-query="spm=1001.2101.3001.6650.9&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-9-125354627-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-9-125354627-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=17"><span class="blog-title">qq_52820087的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">06-19</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					3201
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/qq_52820087/article/details/125354627" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.9&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-9-125354627-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;9&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_52820087/article/details/125354627&quot;}" data-report-query="spm=1001.2101.3001.6650.9&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-9-125354627-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-9-125354627-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=17">
				<div class="desc ellipsis-online ellipsis-online-1">2022年暑期<em>CTF</em>刷题之路</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/zx66661/article/details/124346362">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/zx66661/article/details/124346362" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.10&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-10-124346362-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;10&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/zx66661/article/details/124346362&quot;}" data-report-query="spm=1001.2101.3001.6650.10&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-124346362-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-124346362-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=18">
					<div class="left ellipsis-online ellipsis-online-1">Buu <em>ctf</em> <em>misc</em> 练习</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/zx66661" target="_blank" data-report-query="spm=1001.2101.3001.6650.10&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-124346362-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-124346362-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=18"><span class="blog-title">zx66661的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">04-22</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					1995
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/zx66661/article/details/124346362" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.10&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-10-124346362-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;10&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/zx66661/article/details/124346362&quot;}" data-report-query="spm=1001.2101.3001.6650.10&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-124346362-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-10-124346362-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=18">
				<div class="desc ellipsis-online ellipsis-online-1">被刷新的图片

下载该文件，发现该文件为一张图片，判断该题为隐写术

将该图片放入16进制编辑器和StegSolve进行查看，没有获得有效的信息

猜测该题可能为F5隐写，通过kail下载F5隐写工具来解题


sudo git clone https://github.com/matthewgao/F5-steganography

下载完成后进入F5隐写工具文件夹

cd F5-steganoraphy

在该文件夹下对其图片进行解析

java Extract /位置/<em>Misc</em>.j...</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_download clearfix" data-url="https://download.csdn.net/download/weixin_38502814/14851653">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://download.csdn.net/download/weixin_38502814/14851653" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.11&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-11-14851653-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;11&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_38502814/14851653&quot;}" data-report-query="spm=1001.2101.3001.6650.11&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-11-14851653-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-11-14851653-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=19">
					<div class="left ellipsis-online ellipsis-online-1">【<em>BUUCTF</em>】<em>MISC</em> zip</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">01-20</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://download.csdn.net/download/weixin_38502814/14851653" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.11&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-11-14851653-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;11&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_38502814/14851653&quot;}" data-report-query="spm=1001.2101.3001.6650.11&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-11-14851653-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-11-14851653-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=19">
				<div class="desc ellipsis-online ellipsis-online-1">import zipfile
import string
import binascii
def CrackCrc(crc):
    for i in dic:
        for j in dic:
            for p in dic:
                for q in dic:
                    s = i + j + p + q
                    if crc == (binascii.crc32(s.encode())):
                        #print s
         </div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_download clearfix" data-url="https://download.csdn.net/download/weixin_52351575/86731015">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://download.csdn.net/download/weixin_52351575/86731015" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.12&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-12-86731015-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;12&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_52351575/86731015&quot;}" data-report-query="spm=1001.2101.3001.6650.12&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-12-86731015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-12-86731015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=20">
					<div class="left ellipsis-online ellipsis-online-1"><em>CTF</em>竞赛<em>MISC</em>入门难度<em>题目</em></div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">10-01</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://download.csdn.net/download/weixin_52351575/86731015" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.12&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-12-86731015-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;12&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_52351575/86731015&quot;}" data-report-query="spm=1001.2101.3001.6650.12&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-12-86731015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-12-86731015-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=20">
				<div class="desc ellipsis-online ellipsis-online-1">主要是一些入门级别难度的杂项<em>题目</em>，配套培训视频效果更佳
涉及到的杂项题型有：zip、rar、文件头修复、EXIF解析、gif逐帧解析、CRC校验、LSB隐写和音频
适合刚刚接触杂项的同学学习使用
网络安全杂项入门难度全套题（<em>CTF</em>-<em>MISC</em>）。
网络安全入门级别难度<em>题目</em></div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_download clearfix" data-url="https://download.csdn.net/download/weixin_62485906/86869074">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://download.csdn.net/download/weixin_62485906/86869074" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.13&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-13-86869074-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;13&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_62485906/86869074&quot;}" data-report-query="spm=1001.2101.3001.6650.13&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-13-86869074-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-13-86869074-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=21">
					<div class="left ellipsis-online ellipsis-online-1">2022年网络安全中山市香山杯<em>misc</em><em>题目</em>附件
</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">11-01</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://download.csdn.net/download/weixin_62485906/86869074" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.13&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-13-86869074-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;13&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/weixin_62485906/86869074&quot;}" data-report-query="spm=1001.2101.3001.6650.13&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-13-86869074-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-13-86869074-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=21">
				<div class="desc ellipsis-online ellipsis-online-1">2022年网络安全中山市香山杯<em>misc</em><em>题目</em>附件</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_download clearfix" data-url="https://download.csdn.net/download/rickliuxiao/88072843">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://download.csdn.net/download/rickliuxiao/88072843" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.14&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-14-88072843-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;14&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/rickliuxiao/88072843&quot;}" data-report-query="spm=1001.2101.3001.6650.14&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-14-88072843-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-14-88072843-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=22">
					<div class="left ellipsis-online ellipsis-online-1">中国电信2023巅峰极客网络安全技能挑战赛的一道<em>Misc</em><em>题目</em></div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">07-22</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://download.csdn.net/download/rickliuxiao/88072843" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.14&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-download-2~default~CTRLIST~Rate-14-88072843-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;14&quot;,&quot;strategy&quot;:&quot;2~default~CTRLIST~Rate&quot;,&quot;dest&quot;:&quot;https://download.csdn.net/download/rickliuxiao/88072843&quot;}" data-report-query="spm=1001.2101.3001.6650.14&amp;utm_medium=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-14-88072843-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-download-2%7Edefault%7ECTRLIST%7ERate-14-88072843-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=22">
				<div class="desc ellipsis-online ellipsis-online-1">中国电信2023巅峰极客网络安全技能挑战赛的一道<em>Misc</em><em>题目</em></div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/Ivyyyyyy1/article/details/125645078">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/Ivyyyyyy1/article/details/125645078" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.15&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-15-125645078-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;15&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Ivyyyyyy1/article/details/125645078&quot;}" data-report-query="spm=1001.2101.3001.6650.15&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-15-125645078-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-15-125645078-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=23">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>Misc</em> <em>wp</em>大合集(2)</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/Ivyyyyyy1" target="_blank" data-report-query="spm=1001.2101.3001.6650.15&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-15-125645078-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-15-125645078-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=23"><span class="blog-title">Ivyyyyyy1的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">07-06</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					1076
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/Ivyyyyyy1/article/details/125645078" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.15&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-15-125645078-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;15&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Ivyyyyyy1/article/details/125645078&quot;}" data-report-query="spm=1001.2101.3001.6650.15&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-15-125645078-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-15-125645078-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=23">
				<div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em> <em>Misc</em> 杂项方向write up合集(二)，<em>持续</em><em>更新</em>中</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/qq_44371274/article/details/128228954">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/qq_44371274/article/details/128228954" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.16&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-16-128228954-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;16&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_44371274/article/details/128228954&quot;}" data-report-query="spm=1001.2101.3001.6650.16&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-16-128228954-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-16-128228954-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=24">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em>web的一些<em>wp</em>（1）</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/qq_44371274" target="_blank" data-report-query="spm=1001.2101.3001.6650.16&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-16-128228954-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-16-128228954-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=24"><span class="blog-title">qq_44371274的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">12-08</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					464
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/qq_44371274/article/details/128228954" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.16&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-16-128228954-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;16&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qq_44371274/article/details/128228954&quot;}" data-report-query="spm=1001.2101.3001.6650.16&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-16-128228954-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-16-128228954-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=24">
				<div class="desc ellipsis-online ellipsis-online-1">是<em>BUUCTF</em>的<em>wp</em>捏</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/NANMUZN/article/details/128693752">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/NANMUZN/article/details/128693752" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.17&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-17-128693752-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;17&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/NANMUZN/article/details/128693752&quot;}" data-report-query="spm=1001.2101.3001.6650.17&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-17-128693752-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-17-128693752-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=25">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em>之PWN(<em>WP</em>1)</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/NANMUZN" target="_blank" data-report-query="spm=1001.2101.3001.6650.17&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-17-128693752-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-17-128693752-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=25"><span class="blog-title">NANMUZN的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">01-15</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					331
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/NANMUZN/article/details/128693752" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.17&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-17-128693752-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;17&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/NANMUZN/article/details/128693752&quot;}" data-report-query="spm=1001.2101.3001.6650.17&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-17-128693752-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-17-128693752-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=25">
				<div class="desc ellipsis-online ellipsis-online-1"><em>buuctf</em> pwn</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_blog clearfix" data-url="https://blog.csdn.net/Aluxian_/article/details/130053100">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://blog.csdn.net/Aluxian_/article/details/130053100" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.18&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-18-130053100-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;18&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Aluxian_/article/details/130053100&quot;}" data-report-query="spm=1001.2101.3001.6650.18&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-18-130053100-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-18-130053100-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=26">
					<div class="left ellipsis-online ellipsis-online-1"><em>BUUCTF</em>--Web篇详细<em>wp</em></div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info">
					<a href="https://blog.csdn.net/Aluxian_" target="_blank" data-report-query="spm=1001.2101.3001.6650.18&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-18-130053100-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-18-130053100-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=26"><span class="blog-title">Aluxian_的博客</span></a>
				</div>
				<div class="info display-flex">
					<span class="info-block time">04-10</span>
					<span class="info-block read"><img class="read-img" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					992
					</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://blog.csdn.net/Aluxian_/article/details/130053100" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.18&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-blog-2~default~BlogCommendFromBaidu~Rate-18-130053100-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;18&quot;,&quot;strategy&quot;:&quot;2~default~BlogCommendFromBaidu~Rate&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/Aluxian_/article/details/130053100&quot;}" data-report-query="spm=1001.2101.3001.6650.18&amp;utm_medium=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-18-130053100-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-blog-2%7Edefault%7EBlogCommendFromBaidu%7ERate-18-130053100-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=26">
				<div class="desc ellipsis-online ellipsis-online-1"><em>BUUCTF</em>--Web篇详细<em>wp</em>。</div>
			</a>
		</div>
	</div>
</div>
<div class="recommend-item-box type_chatgpt clearfix" data-url="https://wenku.csdn.net/answer/3w8swhbsgv">
	<div class="content-box">
		<div class="content-blog display-flex">
			<div class="title-box">
				<a href="https://wenku.csdn.net/answer/3w8swhbsgv" class="tit" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.19&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-chatgpt-2~default~OPENSEARCH~Position-19-3w8swhbsgv-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;19&quot;,&quot;strategy&quot;:&quot;2~default~OPENSEARCH~Position&quot;,&quot;dest&quot;:&quot;https://wenku.csdn.net/answer/3w8swhbsgv&quot;}" data-report-query="spm=1001.2101.3001.6650.19&amp;utm_medium=distribute.pc_relevant.none-task-chatgpt-2%7Edefault%7EOPENSEARCH%7EPosition-19-3w8swhbsgv-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-chatgpt-2%7Edefault%7EOPENSEARCH%7EPosition-19-3w8swhbsgv-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=27">
					<div class="left ellipsis-online ellipsis-online-1"><em>buuctf</em> <em>misc</em></div>
					<div class="tag">最新发布</div>
				</a>
			</div>
			<div class="info-box display-flex">
				<div class="info display-flex">
					<span class="info-block">09-28</span>
				</div>
			</div>
		</div>
		<div class="desc-box">
			<a href="https://wenku.csdn.net/answer/3w8swhbsgv" target="_blank" data-report-click="{&quot;ab&quot;:&quot;new&quot;,&quot;spm&quot;:&quot;1001.2101.3001.6650.19&quot;,&quot;mod&quot;:&quot;popu_387&quot;,&quot;extra&quot;:&quot;{\&quot;highlightScore\&quot;:0.0,\&quot;utm_medium\&quot;:\&quot;distribute.pc_relevant.none-task-chatgpt-2~default~OPENSEARCH~Position-19-3w8swhbsgv-blog-*********.235^v38^pc_relevant_sort\&quot;,\&quot;dist_request_id\&quot;:\&quot;1697120942896_75973\&quot;}&quot;,&quot;dist_request_id&quot;:&quot;1697120942896_75973&quot;,&quot;ab_strategy&quot;:&quot;increase_t0_anti_vip_v2&quot;,&quot;index&quot;:&quot;19&quot;,&quot;strategy&quot;:&quot;2~default~OPENSEARCH~Position&quot;,&quot;dest&quot;:&quot;https://wenku.csdn.net/answer/3w8swhbsgv&quot;}" data-report-query="spm=1001.2101.3001.6650.19&amp;utm_medium=distribute.pc_relevant.none-task-chatgpt-2%7Edefault%7EOPENSEARCH%7EPosition-19-3w8swhbsgv-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;depth_1-utm_source=distribute.pc_relevant.none-task-chatgpt-2%7Edefault%7EOPENSEARCH%7EPosition-19-3w8swhbsgv-blog-*********.235%5Ev38%5Epc_relevant_sort&amp;utm_relevant_index=27">
				<div class="desc ellipsis-online ellipsis-online-1"><em>buuctf</em> <em>misc</em>是一个<em>CTF</em>比赛中的一个<em>题目</em>，其中涉及到了压缩包解密和二维码分析等内容。根据引用内容，压缩包是用四位数字加密的，需要尝试不同的组合来解密。同时，二维码文件中可能含有pk字母，还可能包含一个zip压缩包。你可以使用Winhex来查看文件的内容，并使用binwalk来分析文件并提取zip压缩包。

</div>
			</a>
		</div>
	</div>
</div>
              </div>
<div id="recommendNps" class="recommend-nps-box common-nps-box" style="display: block;">
  <h3 class="aside-title">“相关推荐”对你有帮助么？</h3>
  <div class="aside-content">
      <ul class="newnps-list">
          <li class="newnps-item" data-type="非常没帮助">
              <div class="newnps-img-box">
                  <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel1.png" alt="">
                  <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey1.png" alt="">
              </div>
              <div class="newnps-text">非常没帮助</div>
          </li>
          <li class="newnps-item" data-type="没帮助">
              <div class="newnps-img-box">
                  <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel2.png" alt="">
                  <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey2.png" alt="">
              </div>
              <div class="newnps-text">没帮助</div>
          </li>
          <li class="newnps-item" data-type="一般">
              <div class="newnps-img-box">
                  <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel3.png" alt="">
                  <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey3.png" alt="">
              </div>
              <div class="newnps-text">一般</div>
          </li>
          <li class="newnps-item" data-type="有帮助">
              <div class="newnps-img-box">
                  <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel4.png" alt="">
                  <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey4.png" alt="">
              </div>
              <div class="newnps-text">有帮助</div>
          </li>
          <li class="newnps-item" data-type="非常有帮助">
              <div class="newnps-img-box">
                  <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel5.png" alt="">
                  <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey5.png" alt="">
              </div>
              <div class="newnps-text">非常有帮助</div>
          </li>
      </ul>
      <div class="newnps-form-box">
      <div class="newnps-form">
          <input type="text" placeholder="请输入建议或反馈后点击提交" class="newnps-input">
          <span class="newnps-btn">提交</span>
      </div>
      </div>
  </div>
</div><div class="blog-footer-bottom" style="margin-top:10px;">
        <div id="copyright-box" class="">
          <div id="csdn-copyright-footer" class="column small">
            <ul class="footer-column-t">
            <li>
              <a rel="nofollow" href="https://www.csdn.net/company/index.html#about" target="_blank">关于我们</a>
            </li>
            <li>
              <a rel="nofollow" href="https://www.csdn.net/company/index.html#recruit" target="_blank">招贤纳士</a>
            </li>
            <li><a rel="nofollow" href="https://marketing.csdn.net/questions/Q2202181741262323995" target="_blank">商务合作</a></li>
            <li><a rel="nofollow" href="https://marketing.csdn.net/questions/Q2202181748074189855" target="_blank">寻求报道</a></li>
            <li>
              <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/tel.png" alt="">
              <span>************</span>
            </li>
            <li>
              <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/email.png" alt="">
              <a rel="nofollow" href="mailto:<EMAIL>" target="_blank"><EMAIL></a>
            </li>
            <li>
              <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cs.png" alt="">
              <a rel="nofollow" href="https://csdn.s2.udesk.cn/im_client/?web_plugin_id=29181" target="_blank">在线客服</a>
            </li>
            <li>
              工作时间&nbsp;8:30-22:00
            </li>
          </ul>
            <ul class="footer-column-b">
            <li><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/badge.png" alt=""><a rel="nofollow" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=11010502030143" target="_blank">公安备案号11010502030143</a></li>
            <li><a rel="nofollow" href="http://beian.miit.gov.cn/publish/query/indexFirst.action" target="_blank">京ICP备19004658号</a></li>
            <li><a rel="nofollow" href="https://csdnimg.cn/release/live_fe/culture_license.png" target="_blank">京网文〔2020〕1039-165号</a></li>
            <li><a rel="nofollow" href="https://csdnimg.cn/cdn/content-toolbar/csdn-ICP.png" target="_blank">经营性网站备案信息</a></li>
            <li><a rel="nofollow" href="http://www.bjjubao.org/" target="_blank">北京互联网违法和不良信息举报中心</a></li>
            <li><a rel="nofollow" href="https://download.csdn.net/tutelage/home" target="_blank">家长监护</a></li>
            <li><a rel="nofollow" href="http://www.cyberpolice.cn/" target="_blank">网络110报警服务</a></li>
            <li><a rel="nofollow" href="http://www.12377.cn/" target="_blank">中国互联网举报中心</a></li>
            <li><a rel="nofollow" href="https://chrome.google.com/webstore/detail/csdn%E5%BC%80%E5%8F%91%E8%80%85%E5%8A%A9%E6%89%8B/kfkdboecolemdjodhmhmcibjocfopejo?hl=zh-CN" target="_blank">Chrome商店下载</a></li>
            <li><a rel="nofollow" href="https://blog.csdn.net/blogdevteam/article/details/*********" target="_blank">账号管理规范</a></li>
            <li><a rel="nofollow" href="https://www.csdn.net/company/index.html#statement" target="_blank">版权与免责声明</a></li>
            <li><a rel="nofollow" href="https://blog.csdn.net/blogdevteam/article/details/90369522" target="_blank">版权申诉</a></li>
            <li><a rel="nofollow" href="https://img-home.csdnimg.cn/images/20220705052819.png" target="_blank">出版物许可证</a></li>
            <li><a rel="nofollow" href="https://img-home.csdnimg.cn/images/20210414021142.jpg" target="_blank">营业执照</a></li>
            <li>©1999-2023北京创新乐知网络技术有限公司</li>
          </ul>
          </div>
        </div>
      </div>
<script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-footer.js.下载" data-isfootertrack="false" type="text/javascript"></script>
<script type="text/javascript">
    window.csdn.csdnFooter.options = {
        el: '.blog-footer-bottom',
        type: 2
    }
</script>          </main>
<aside class="blog_container_aside">
<div id="asideProfile" class="aside-box">
    <div class="profile-intro d-flex">
        <div class="avatar-box d-flex justify-content-center flex-column">
            <a href="https://blog.csdn.net/qpeity" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_379&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4121&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity&quot;,&quot;ab&quot;:&quot;new&quot;}">
                <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/6b2759cb62f743cf81d6a2848214a29c_qpeity.jpg!1" class="avatar_pic">
            </a>
        </div>
        <div class="user-info d-flex flex-column profile-intro-name-box">
            <div class="profile-intro-name-boxTop">
                <a href="https://blog.csdn.net/qpeity" target="_blank" class="" id="uid" title="苦行僧(csdn)" data-report-click="{&quot;mod&quot;:&quot;popu_379&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4122&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <span class="name " username="qpeity">苦行僧(csdn)</span>
                </a>
                <span>
                </span>
                <span class="flag expert-blog">
                <span class="bubble">CSDN认证博客专家</span>
                </span>
                <span class="flag company-blog">
                <span class="bubble">CSDN认证企业博客</span>
                </span>
            </div>
            <div class="profile-intro-name-boxFooter">
                <span class="personal-home-page personal-home-years" title="已加入 CSDN 12年">码龄12年</span>
                    <span class="personal-home-page">
                    <a class="personal-home-certification" href="https://i.csdn.net/#/uc/profile?utm_source=14998968" target="_blank" title="暂无认证">
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/nocErtification.png" alt="">
                    暂无认证
                    </a>
                    </span>
            </div>
        </div>
    </div>
    <div class="data-info d-flex item-tiling">
        <dl class="text-center" title="131">
            <a href="https://blog.csdn.net/qpeity" data-report-click="{&quot;mod&quot;:&quot;1598321000_001&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4310&quot;}" data-report-query="t=1">  
                <dt><span class="count">131</span></dt>
                <dd class="font">原创</dd>
            </a>
        </dl>
        <dl class="text-center" data-report-click="{&quot;mod&quot;:&quot;1598321000_002&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4311&quot;}" title="46100">
            <a href="https://blog.csdn.net/rank/list/weekly" target="_blank">
                <dt><span class="count">4万+</span></dt>
                <dd class="font">周排名</dd>
            </a>
        </dl>
        <dl class="text-center" title="20996">
            <a href="https://blog.csdn.net/rank/list/total" data-report-click="{&quot;mod&quot;:&quot;1598321000_003&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4312&quot;}" target="_blank">
                <dt><span class="count">2万+</span></dt>
                <dd class="font">总排名</dd>
            </a>
        </dl>
        <dl class="text-center" style="min-width:58px" title="158872">  
            <dt><span class="count">15万+</span></dt>
            <dd>访问</dd>
        </dl>
        <dl class="text-center" title="5级,点击查看等级说明">
            <dt><a href="https://blog.csdn.net/blogdevteam/article/details/103478461" target="_blank">
                <img class="level" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/blog5.png">
            </a>
            </dt>
            <dd>等级</dd>
        </dl>
    </div>
    <div class="item-rank"></div>
    <div class="data-info d-flex item-tiling">
        <dl class="text-center" title="1928">
            <dt><span class="count">1928</span></dt>
            <dd>积分</dd>
        </dl>
         <dl class="text-center" id="fanBox" title="86">
            <dt><span class="count" id="fan">86</span></dt>
            <dd>粉丝</dd>
        </dl>
        <dl class="text-center" title="76">
            <dt><span class="count">76</span></dt>
            <dd>获赞</dd>
        </dl>
        <dl class="text-center" title="39">
            <dt><span class="count">39</span></dt>
            <dd>评论</dd>
        </dl>
        <dl class="text-center" title="329">
            <dt><span class="count">329</span></dt>
            <dd>收藏</dd>
        </dl>
    </div>
    <div class="aside-box-footer">
        <div class="badge-box d-flex">
            <div class="badge d-flex">
                <div class="icon-badge" title="签到新秀">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="签到新秀">
                    </div>
                </div>
                <div class="icon-badge" title="领英">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="领英">
                    </div>
                </div>
                <div class="icon-badge" title="GitHub">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="GitHub">
                    </div>
                </div>
                <div class="icon-badge" title="笔耕不辍">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/02d34b42a3ee476fb50850304ab67017.png" alt="笔耕不辍">
                    </div>
                </div>
                <div class="icon-badge" title="阅读者勋章">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="阅读者勋章">
                    </div>
                </div>
                <div class="icon-badge" title="分享小兵">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="分享小兵">
                    </div>
                </div>
                <div class="icon-badge" title="分享达人">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="分享达人">
                    </div>
                </div>
                <div class="icon-badge" title="分享精英">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="分享精英">
                    </div>
                </div>
                <div class="icon-badge" title="分享宗师">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="分享宗师">
                    </div>
                </div>
                <div class="icon-badge" title="持续创作">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="持续创作">
                    </div>
                </div>
                <div class="icon-badge" title="1024勋章">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="1024勋章">
                    </div>
                </div>
                <div class="icon-badge" title="1024超级勋章">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="1024超级勋章">
                    </div>
                </div>
                <div class="icon-badge" title="创作能手">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="创作能手">
                    </div>
                </div>
                <div class="icon-badge" title="新人勋章">
                    <div class="mouse-box">
                        <img class="medal-img" data-report-click="{&quot;spm&quot;:&quot;3001.4296&quot;}" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/<EMAIL>" alt="新人勋章">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="profile-intro-name-boxOpration">
        <div class="opt-letter-watch-box">
        <a rel="nofollow" class="bt-button personal-letter" href="https://im.csdn.net/chat/qpeity" target="_blank">私信</a>
        </div>
        <div class="opt-letter-watch-box"> 
            <a class="personal-watch bt-button" id="btnAttent">关注</a>  
        </div>
    </div>
</div>
<a id="remuneration" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.9809&quot;}" href="https://blog.csdn.net/qpeity/article/details/*********" class="remuneration-box">
  <img src="https://blog.csdn.net/qpeity/article/details/*********" alt="">
</a>
  <div id="asideWriteGuide" class="aside-box side-write-guide-box type-1">
    <div class="content-box">
      <a href="https://mp.csdn.net/edit" target="_blank" class="btn-go-write" data-report-query="spm=3001.9727" data-report-click="{&quot;spm&quot;:&quot;3001.9727&quot;}">
        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20230817060237.png" alt="写文章">
      </a>
    </div>
  </div>
<div id="asideSearchArticle" class="aside-box">
	<div class="aside-content search-comter">
    <div class="aside-search aside-search-blog">         
        <input type="text" class="input-serch-blog" name="" autocomplete="off" value="" id="search-blog-words" placeholder="搜博主文章">
        <a class="btn-search-blog" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.9182&quot;}">
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-sou.png">
        </a>
    </div>
    </div>
</div>


<div id="asideHotArticle" class="aside-box">
	<h3 class="aside-title">热门文章</h3>
	<div class="aside-content">
		<ul class="hotArticle-list">
			<li>
				<a href="https://blog.csdn.net/qpeity/article/details/115479297" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_541&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4139&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/115479297&quot;,&quot;ab&quot;:&quot;new&quot;}">
				【救援过程】升级openssl导致libcrypto.so.1.1动态库不可用
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					<span class="read">12834</span>
                </a>
			</li>
			<li>
				<a href="https://blog.csdn.net/qpeity/article/details/99245430" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_541&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4139&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/99245430&quot;,&quot;ab&quot;:&quot;new&quot;}">
				chronyd服务
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					<span class="read">12212</span>
                </a>
			</li>
			<li>
				<a href="https://blog.csdn.net/qpeity/article/details/114669949" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_541&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4139&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/114669949&quot;,&quot;ab&quot;:&quot;new&quot;}">
				配置VNC
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					<span class="read">9893</span>
                </a>
			</li>
			<li>
				<a href="https://blog.csdn.net/qpeity/article/details/46716323" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_541&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4139&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/46716323&quot;,&quot;ab&quot;:&quot;new&quot;}">
				使用WinPcap和libpcap类库读写pcap文件（001）开发环境配置
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					<span class="read">6825</span>
                </a>
			</li>
			<li>
				<a href="https://blog.csdn.net/qpeity/article/details/115296200" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_541&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4139&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/115296200&quot;,&quot;ab&quot;:&quot;new&quot;}">
				centos8编译openssl-1.0.2u、openssl-1.1.1k
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/readCountWhite.png" alt="">
					<span class="read">6116</span>
                </a>
			</li>
		</ul>
	</div>
</div>
<div id="asideCategory" class="aside-box flexible-box">
    <h3 class="aside-title">分类专栏</h3>
    <div class="aside-content">
        <ul>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10883970.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10883970.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210315134731469(1).png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        信息安全
                    </span>
                </a>
                <span class="special-column-num">14篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12089820.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12089820.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/935c8559ed9741b0bef8f6774cc70659(1).jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        BUUCTF
                    </span>
                </a>
                <span class="special-column-num">4篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12412028.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12412028.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/15024207a6d84090be84c01ab8db629b.jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        CTFshow
                    </span>
                </a>
                <span class="special-column-num">1篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12089816.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12089816.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/eac8821da0f841cb9d80726617363921.jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Bugku
                    </span>
                </a>
                <span class="special-column-num">3篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12139538.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12139538.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a061f721512840fba1b53f24461499ab.jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        安全工具
                    </span>
                </a>
                <span class="special-column-num">3篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12139514.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12139514.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ea18cd7829d4463d833e01073aaf5dbd.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        网络安全竞赛
                    </span>
                </a>
                <span class="special-column-num">3篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9536322.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9536322.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20201206110259891.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Python
                    </span>
                </a>
                <span class="special-column-num">23篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9536624.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9536624.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191123174827226.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Python基础
                    </span>
                </a>
                <span class="special-column-num">20篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10728721.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10728721.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210111151026601.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Python实践与随笔
                    </span>
                </a>
                <span class="special-column-num">6篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10856769.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10856769.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306135425926.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Linux
                    </span>
                </a>
                <span class="special-column-num">47篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10856777.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10856777.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306135943342.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Linux实践与随笔
                    </span>
                </a>
                <span class="special-column-num">24篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9218531.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9218531.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306144210800.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Linux基础
                    </span>
                </a>
                <span class="special-column-num">17篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10856875.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10856875.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2021030614360945.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        HighAvailability
                    </span>
                </a>
                <span class="special-column-num">3篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10882419.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10882419.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar special-column-bar-second"></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210314213234639.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Ansible
                    </span>
                </a>
                <span class="special-column-num">6篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9610135.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9610135.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191220222647941.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        CV
                    </span>
                </a>
                <span class="special-column-num">19篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10638931.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10638931.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20201206110419763.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        MySQL
                    </span>
                </a>
                <span class="special-column-num">4篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_5612041.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_5612041.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191123160236367.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Oracle
                    </span>
                </a>
                <span class="special-column-num">3篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_5620215.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_5620215.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210324095440653.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Network
                    </span>
                </a>
                <span class="special-column-num">6篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_3269003.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_3269003.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191123160626567.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Qt
                    </span>
                </a>
                <span class="special-column-num">2篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_3143629.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_3143629.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306142033316.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        Windows
                    </span>
                </a>
                <span class="special-column-num">7篇</span>
            </li>
            <li>
                <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9853289.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9853289.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                    <div class="special-column-bar "></div>
                    <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20200330095741694.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                    <span class="title oneline">
                        SAP
                    </span>
                </a>
                <span class="special-column-num">10篇</span>
            </li>
        </ul>
    </div>
    <p class="text-center">
        <a class="flexible-btn" data-fbox="aside-archive"><img class="look-more" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/arrowDownWhite.png" alt=""></a>
    </p>
</div>
<div id="asideNewComments" class="aside-box">
    <h3 class="aside-title">最新评论</h3>
    <div class="aside-content">
        <ul class="newcomment-list">
            <li>
                <a class="title text-truncate" target="_blank" href="https://blog.csdn.net/qpeity/article/details/127102642#comments_27447701" data-report-click="{&quot;mod&quot;:&quot;popu_542&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4231&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/127102642#comments_27447701&quot;,&quot;ab&quot;:&quot;new&quot;}">2022年第三届指挥官杯练习赛题目和writeup（持续更新）</a>
                <p class="comment ellipsis">
                    <a href="https://blog.csdn.net/jiaoyang8888" class="user-name" target="_blank">骄阳8: </a>
                    <span class="code-comments">感觉第一个图片解出的 xinshen 有用应该是密码，目录jiehe应该也有用。感觉是解密8.jpg的。
outguess -k xinshen -r 8.jpg out.txt
没有成功。

感觉是【BUUCTF】[WUSTCTF2020]alison_likes_jojo题目演化过来的。</span>
                </p>
            </li>
            <li>
                <a class="title text-truncate" target="_blank" href="https://blog.csdn.net/qpeity/article/details/123130924#comments_25414241" data-report-click="{&quot;mod&quot;:&quot;popu_542&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4231&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/123130924#comments_25414241&quot;,&quot;ab&quot;:&quot;new&quot;}">SAP事务码f-02做账界面显示“页数”字段</a>
                <p class="comment ellipsis">
                    <a href="https://blog.csdn.net/weixin_70001886" class="user-name" target="_blank">單295: </a>
                    <span class="code-comments">对应字段的参数在哪里能找到，想添加一个其他字段，在行项目上</span>
                </p>
            </li>
            <li>
                <a class="title text-truncate" target="_blank" href="https://blog.csdn.net/qpeity/article/details/104258915#comments_24518063" data-report-click="{&quot;mod&quot;:&quot;popu_542&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4231&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/104258915#comments_24518063&quot;,&quot;ab&quot;:&quot;new&quot;}">CV07-DeepLab v3+笔记</a>
                <p class="comment ellipsis">
                    <a href="https://blog.csdn.net/qq_45874142" class="user-name" target="_blank">LaternZ: </a>
                    <span class="code-comments">博主个deeplabv3+中的ASPP，浮现的代码中，空洞卷积没有使用深度可分离吧</span>
                </p>
            </li>
            <li>
                <a class="title text-truncate" target="_blank" href="https://blog.csdn.net/qpeity/article/details/114856442#comments_24278848" data-report-click="{&quot;mod&quot;:&quot;popu_542&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4231&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/114856442#comments_24278848&quot;,&quot;ab&quot;:&quot;new&quot;}">用createrepo命令创建自己的yum源</a>
                <p class="comment ellipsis">
                    <a href="https://blog.csdn.net/qq_32398491" class="user-name" target="_blank">qq_32398491: </a>
                    <span class="code-comments">按照你这样好像有点问题，rpm文件是在Package文件夹下的，但是使用dnf install的时候，下载的路径是直接去/centos/8/路径下找的rpm文件，会404</span>
                </p>
            </li>
            <li>
                <a class="title text-truncate" target="_blank" href="https://blog.csdn.net/qpeity/article/details/114490672#comments_23996428" data-report-click="{&quot;mod&quot;:&quot;popu_542&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4231&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/114490672#comments_23996428&quot;,&quot;ab&quot;:&quot;new&quot;}">编译Linux Kernel(linux-4.19.178)并制作成rpm文件</a>
                <p class="comment ellipsis">
                    <a href="https://blog.csdn.net/weixin_56306070" class="user-name" target="_blank">RunAn449: </a>
                    <span class="code-comments">看到下载链接中的文件大小了。奇怪，看同事和其他资料中，编译好的内核RPM包只有几十兆。</span>
                </p>
            </li>
        </ul>
    </div>
</div>
<div id="asideNewNps" class="aside-box common-nps-box" style="display: block;">
    <h3 class="aside-title">您愿意向朋友推荐“博客详情页”吗？</h3>
    <div class="aside-content">
        <ul class="newnps-list">
            <li class="newnps-item" data-type="强烈不推荐">
                <div class="newnps-img-box">
                    <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel1.png" alt="">
                    <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey1.png" alt="">
                </div>
                <div class="newnps-text">强烈不推荐</div>
            </li>
            <li class="newnps-item" data-type="不推荐">
                <div class="newnps-img-box">
                    <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel2.png" alt="">
                    <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey2.png" alt="">
                </div>
                <div class="newnps-text">不推荐</div>
            </li>
            <li class="newnps-item" data-type="一般般">
                <div class="newnps-img-box">
                    <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel3.png" alt="">
                    <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey3.png" alt="">
                </div>
                <div class="newnps-text">一般般</div>
            </li>
            <li class="newnps-item" data-type="推荐">
                <div class="newnps-img-box">
                    <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel4.png" alt="">
                    <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey4.png" alt="">
                </div>
                <div class="newnps-text">推荐</div>
            </li>
            <li class="newnps-item" data-type="强烈推荐">
                <div class="newnps-img-box">
                    <img class="newnps-img active" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeel5.png" alt="">
                    <img class="newnps-img default" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/npsFeelGrey5.png" alt="">
                </div>
                <div class="newnps-text">强烈推荐</div>
            </li>
        </ul>
        <div class="newnps-form-box">
        <div class="newnps-form">
            <input type="text" placeholder="请输入建议或反馈后点击提交" class="newnps-input">
            <span class="newnps-btn">提交</span>
        </div>
        </div>
    </div>
</div>
<div id="asideArchive" class="aside-box" style="display:block!important; width:300px;">
    <h3 class="aside-title">最新文章</h3>
    <div class="aside-content">
        <ul class="inf_list clearfix">
            <li class="clearfix">
            <a href="https://blog.csdn.net/qpeity/article/details/132306374" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_382&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4136&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/132306374&quot;,&quot;ab&quot;:&quot;new&quot;}">CTFshow的Web题目wp（持续更新）</a>
            </li>
            <li class="clearfix">
            <a href="https://blog.csdn.net/qpeity/article/details/132206951" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_382&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4136&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/132206951&quot;,&quot;ab&quot;:&quot;new&quot;}">BUUCTF题目Web部分wp（持续更新）</a>
            </li>
            <li class="clearfix">
            <a href="https://blog.csdn.net/qpeity/article/details/132091755" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_382&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4136&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/article/details/132091755&quot;,&quot;ab&quot;:&quot;new&quot;}">CentOS7上配置vsftpd</a>
            </li>
        </ul>
        <div class="archive-bar"></div>
        <div class="archive-box">
                <div class="archive-list-item"><a href="https://blog.csdn.net/qpeity?type=blog&amp;year=2023&amp;month=08" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_538&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4138&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity?type=blog&amp;year=2023&amp;month=08&quot;}"><span class="year">2023年</span><span class="num">5篇</span></a></div>
                <div class="archive-list-item"><a href="https://blog.csdn.net/qpeity?type=blog&amp;year=2022&amp;month=11" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_538&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4138&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity?type=blog&amp;year=2022&amp;month=11&quot;}"><span class="year">2022年</span><span class="num">23篇</span></a></div>
                <div class="archive-list-item"><a href="https://blog.csdn.net/qpeity?type=blog&amp;year=2021&amp;month=12" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_538&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4138&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity?type=blog&amp;year=2021&amp;month=12&quot;}"><span class="year">2021年</span><span class="num">44篇</span></a></div>
                <div class="archive-list-item"><a href="https://blog.csdn.net/qpeity?type=blog&amp;year=2020&amp;month=12" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_538&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4138&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity?type=blog&amp;year=2020&amp;month=12&quot;}"><span class="year">2020年</span><span class="num">39篇</span></a></div>
                <div class="archive-list-item"><a href="https://blog.csdn.net/qpeity?type=blog&amp;year=2019&amp;month=12" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_538&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4138&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity?type=blog&amp;year=2019&amp;month=12&quot;}"><span class="year">2019年</span><span class="num">17篇</span></a></div>
                <div class="archive-list-item"><a href="https://blog.csdn.net/qpeity?type=blog&amp;year=2015&amp;month=08" target="_blank" data-report-click="{&quot;mod&quot;:&quot;popu_538&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4138&quot;,&quot;ab&quot;:&quot;new&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity?type=blog&amp;year=2015&amp;month=08&quot;}"><span class="year">2015年</span><span class="num">7篇</span></a></div>
        </div>
    </div>
</div>
	<div id="footerRightAds" class="isShowFooterAds">
		<div class="aside-box">
			<div id="kp_box_57" data-pid="57"><script async="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f(2).txt" crossorigin="anonymous" data-checked-head="true"></script>
<!-- PC-博客-详情页-左下视窗-全量 -->
<ins class="adsbygoogle" style="display:block" data-ad-client="ca-pub-1076724771190722" data-ad-slot="7553470938" data-ad-format="auto" data-full-width-responsive="true" data-adsbygoogle-status="done"></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script><img class="pre-img-lasy" data-src="https://kunyu.csdn.net/1.png?p=57&amp;adId=1033838&amp;adBlockFlag=0&amp;a=1033838&amp;c=0&amp;k=BUUCTF题目Misc部分wp（持续更新）&amp;spm=1001.2101.3001.5001&amp;articleId=*********&amp;d=1&amp;t=3&amp;u=c68e700c1b4649948016c54748c56efe" style="display: block;width: 0px;height: 0px;"></div>
		</div>
	</div>
    <!-- 详情页显示目录 -->
<!--文章目录-->
<div id="asidedirectory" class="aside-box">
    <div class="groupfile" id="directory">
        <h3 class="aside-title">目录</h3>
        <div class="align-items-stretch group_item">
            <div class="pos-box">
            <div class="scroll-box">
                <div class="toc-box"><ol><li class="active"><a href="https://blog.csdn.net/qpeity/article/details/*********#t0">伟大的司令官（题目原名称不能发）</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t1">[UTCTF2020]sstv&nbsp;</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t2">二维码&nbsp;</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t3">你竟然赶我走</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t4">大白</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t5">N种方法解决</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t6">乌镇峰会种图</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t7">基础破解</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t8">wireshark</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t9">文件中的秘密</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t10">LSB</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t11">zip伪加密</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t12">rar</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t13">小明的保险箱</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t14">ningen</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t15">被嗅探的流量</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t16">镜子里面的世界</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t17">爱因斯坦</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t18">easycap</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t19">隐藏的钥匙</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t20">另外一个世界</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t21">FLAG</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t22">BOOM</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t23">神秘龙卷风</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t24">假如给我三天光明</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t25">数据包中的线索</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t26">后门查杀、webshell后门【D盾工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t27">来首歌吧</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t28">荷兰宽带数据泄露【RouterPassView工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t29">面具下的flag【zip伪加密、vmdk用7z解压缩】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t30">九连环【zip伪加密、steghide工具】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t31">被劫持的神秘礼物</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t32">刷新过的图片【F5-steganography工具】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t33">[BJDCTF2020]认真你就输了【ms-office文件本质是zip】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t34">[BJDCTF2020]藏藏藏</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t35">被偷走的文件【zip和rar文件都应该4位爆破一下、rar文件格式】</a></li><li class="sub-box"><ol><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t36">RAR 5.0 文件格式</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t37">RAR 4.x 文件格式</a></li></ol></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t38">snake【serpent对称加密算法】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t39">[GXYCTF2019]佛系青年</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t40">[BJDCTF2020]你猜我是个啥</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t41">菜刀666【流量分析】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t42">秘密文件【流量分析】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t43">[BJDCTF2020]just_a_rar</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t44">[BJDCTF2020]鸡你太美【gif文件格式、脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t45">[BJDCTF2020]一叶障目</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t46">[SWPU2019]神奇的二维码【俄罗斯套娃、耐心】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t47">梅花香之苦寒来【字符串描述的ascii】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t48">[BJDCTF2020]纳尼【gif文件格式】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t49">穿越时空的思念【摩斯码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t50">[ACTF新生赛2020]outguess</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t51">[HBNIS2018]excel破解【excel文件隐写】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t52">[HBNIS2018]来题中等的吧【摩斯码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t53">谁赢了比赛？</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t54">[SWPU2019]我有一只马里奥【ntfs流特性】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t55">[WUSTCTF2020]find_me.jpg【盲文】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t56">[GXYCTF2019]gakki【字频统计、脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t57">[ACTF新生赛2020]base64隐写【base64隐写、脑洞】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t58">[GUET-CTF2019]KO【Ook!】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t59">[MRCTF2020]ezmisc【png图片crc32爆破】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t60">[SWPU2019]伟大的侦探【EBCDIC编码、图形密码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t61">黑客帝国【png、jpg文件格式】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t62">[MRCTF2020]你能看懂音符吗【rar文件格式、音符密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t63">[HBNIS2018]caesar【凯撒密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t64">[HBNIS2018]低个头【脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t65">[SUCTF2018]single dog【aaencode等**encode密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t66">我吃三明治【base32】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t67">sqltest【流量分析】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t68">[ACTF新生赛2020]music【m4a文件格式】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t69">[SWPU2019]你有没有好好看网课【Kinovea工具、敲击码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t70">[ACTF新生赛2020]NTFS数据流【ntfs流特性】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t71">john-in-the-middle【流量分析、PNG隐写】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t72">喵喵喵【ntfs流及工具、umcompyle6工具】</a></li><li class="sub-box"><ol><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t73">uncompyle6报错和配置</a></li></ol></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t74">[ACTF新生赛2020]swp【zip伪加密、vim】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t75">[GXYCTF2019]SXMgdGhpcyBiYXNlPw==【base64隐写】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t76">间谍启示录【隐藏文件】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t77">Mysterious【反编译】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t78">[UTCTF2020]docx【word本质是zip】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t79">弱口令【cloacked-pixel隐写工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t80">[RoarCTF2019]黄金6年【Kinovea工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t81">小易的U盘【autorun.inf文件】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t82">[WUSTCTF2020]爬</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t83">[WUSTCTF2020]alison_likes_jojo【john、outguess】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t84">[安洵杯 2019]吹着贝斯扫二维码</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t85">Next</a></li></ol></div>
            </div>
            </div>
        </div>
    </div>
</div>
</aside>
<script>
	$("a.flexible-btn").click(function(){
		$(this).parents('div.aside-box').removeClass('flexible-box');
		$(this).parents("p.text-center").remove();
	})
</script>
<script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-tooltip.js.下载"></script>
<script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-medal.js.下载"></script>        </div>
<div class="recommend-right align-items-stretch clearfix" id="rightAside" data-type="recommend" style="height: auto !important;">
    <aside class="recommend-right_aside" style="height: auto !important;">
        <div id="recommend-right">
                                <div class="programmer1Box">
                        <div id="kp_box_530" data-pid="530"><script async="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f(2).txt" crossorigin="anonymous" data-checked-head="true"></script>
<!-- PC-博客-详情页-右上视窗-全量 -->
<ins class="adsbygoogle" style="display: block; height: 600px;" data-ad-client="ca-pub-1076724771190722" data-ad-slot="8674980912" data-ad-format="auto" data-full-width-responsive="true" data-adsbygoogle-status="done" data-ad-status="unfilled"><div id="aswift_1_host" style="border: none; height: 600px; width: 300px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block; overflow: visible;" tabindex="0" title="Advertisement" aria-label="Advertisement"><iframe id="aswift_1" name="aswift_1" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:300px;height:600px;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" width="300" height="600" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ads.html" data-google-container-id="a!2" data-google-query-id="CNSOrZjc8IEDFVG6lgodDNwMuA" data-load-complete="true"></iframe></div></ins>
<script>
     (adsbygoogle = window.adsbygoogle || []).push({});
</script><img class="pre-img-lasy" data-src="https://kunyu.csdn.net/1.png?p=530&amp;adId=1033837&amp;adBlockFlag=0&amp;a=1033837&amp;c=0&amp;k=BUUCTF题目Misc部分wp（持续更新）&amp;spm=1001.2101.3001.4647&amp;articleId=*********&amp;d=1&amp;t=3&amp;u=76d32802abde42139955406a4caadf0f" style="display: block;width: 0px;height: 0px;" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1.png"></div>
                    </div>
            <div class="flex-column aside-box groupfile" id="groupfile" style="display: block; max-height: 475px;">
                <div class="groupfile-div" style="max-height: 475px;">
                <h3 class="aside-title">目录</h3>
                <div class="align-items-stretch group_item">
                    <div class="pos-box">
                        <div class="scroll-box">
                            <div class="toc-box"><ol><li class="active"><a href="https://blog.csdn.net/qpeity/article/details/*********#t0">伟大的司令官（题目原名称不能发）</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t1">[UTCTF2020]sstv&nbsp;</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t2">二维码&nbsp;</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t3">你竟然赶我走</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t4">大白</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t5">N种方法解决</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t6">乌镇峰会种图</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t7">基础破解</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t8">wireshark</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t9">文件中的秘密</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t10">LSB</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t11">zip伪加密</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t12">rar</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t13">小明的保险箱</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t14">ningen</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t15">被嗅探的流量</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t16">镜子里面的世界</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t17">爱因斯坦</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t18">easycap</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t19">隐藏的钥匙</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t20">另外一个世界</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t21">FLAG</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t22">BOOM</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t23">神秘龙卷风</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t24">假如给我三天光明</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t25">数据包中的线索</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t26">后门查杀、webshell后门【D盾工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t27">来首歌吧</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t28">荷兰宽带数据泄露【RouterPassView工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t29">面具下的flag【zip伪加密、vmdk用7z解压缩】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t30">九连环【zip伪加密、steghide工具】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t31">被劫持的神秘礼物</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t32">刷新过的图片【F5-steganography工具】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t33">[BJDCTF2020]认真你就输了【ms-office文件本质是zip】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t34">[BJDCTF2020]藏藏藏</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t35">被偷走的文件【zip和rar文件都应该4位爆破一下、rar文件格式】</a></li><li class="sub-box"><ol><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t36">RAR 5.0 文件格式</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t37">RAR 4.x 文件格式</a></li></ol></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t38">snake【serpent对称加密算法】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t39">[GXYCTF2019]佛系青年</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t40">[BJDCTF2020]你猜我是个啥</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t41">菜刀666【流量分析】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t42">秘密文件【流量分析】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t43">[BJDCTF2020]just_a_rar</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t44">[BJDCTF2020]鸡你太美【gif文件格式、脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t45">[BJDCTF2020]一叶障目</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t46">[SWPU2019]神奇的二维码【俄罗斯套娃、耐心】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t47">梅花香之苦寒来【字符串描述的ascii】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t48">[BJDCTF2020]纳尼【gif文件格式】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t49">穿越时空的思念【摩斯码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t50">[ACTF新生赛2020]outguess</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t51">[HBNIS2018]excel破解【excel文件隐写】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t52">[HBNIS2018]来题中等的吧【摩斯码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t53">谁赢了比赛？</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t54">[SWPU2019]我有一只马里奥【ntfs流特性】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t55">[WUSTCTF2020]find_me.jpg【盲文】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t56">[GXYCTF2019]gakki【字频统计、脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t57">[ACTF新生赛2020]base64隐写【base64隐写、脑洞】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t58">[GUET-CTF2019]KO【Ook!】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t59">[MRCTF2020]ezmisc【png图片crc32爆破】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t60">[SWPU2019]伟大的侦探【EBCDIC编码、图形密码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t61">黑客帝国【png、jpg文件格式】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t62">[MRCTF2020]你能看懂音符吗【rar文件格式、音符密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t63">[HBNIS2018]caesar【凯撒密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t64">[HBNIS2018]低个头【脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t65">[SUCTF2018]single dog【aaencode等**encode密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t66">我吃三明治【base32】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t67">sqltest【流量分析】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t68">[ACTF新生赛2020]music【m4a文件格式】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t69">[SWPU2019]你有没有好好看网课【Kinovea工具、敲击码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t70">[ACTF新生赛2020]NTFS数据流【ntfs流特性】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t71">john-in-the-middle【流量分析、PNG隐写】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t72">喵喵喵【ntfs流及工具、umcompyle6工具】</a></li><li class="sub-box"><ol><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t73">uncompyle6报错和配置</a></li></ol></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t74">[ACTF新生赛2020]swp【zip伪加密、vim】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t75">[GXYCTF2019]SXMgdGhpcyBiYXNlPw==【base64隐写】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t76">间谍启示录【隐藏文件】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t77">Mysterious【反编译】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t78">[UTCTF2020]docx【word本质是zip】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t79">弱口令【cloacked-pixel隐写工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t80">[RoarCTF2019]黄金6年【Kinovea工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t81">小易的U盘【autorun.inf文件】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t82">[WUSTCTF2020]爬</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t83">[WUSTCTF2020]alison_likes_jojo【john、outguess】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t84">[安洵杯 2019]吹着贝斯扫二维码</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t85">Next</a></li></ol></div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
            <div class="aside-box kind_person d-flex flex-column">
                    <h3 class="aside-title">分类专栏</h3>
                    <div class="align-items-stretch kindof_item" id="kind_person_column">
                        <div class="aside-content">
                            <ul>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10883970.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10883970.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210315134731469(1).png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            信息安全
                                        </span>
                                    </a>
                                    <span class="special-column-num">14篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12089820.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12089820.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/935c8559ed9741b0bef8f6774cc70659(1).jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            BUUCTF
                                        </span>
                                    </a>
                                    <span class="special-column-num">4篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12412028.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12412028.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/15024207a6d84090be84c01ab8db629b.jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            CTFshow
                                        </span>
                                    </a>
                                    <span class="special-column-num">1篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12089816.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12089816.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/eac8821da0f841cb9d80726617363921.jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Bugku
                                        </span>
                                    </a>
                                    <span class="special-column-num">3篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12139538.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12139538.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a061f721512840fba1b53f24461499ab.jpeg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            安全工具
                                        </span>
                                    </a>
                                    <span class="special-column-num">3篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_12139514.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_12139514.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ea18cd7829d4463d833e01073aaf5dbd.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            网络安全竞赛
                                        </span>
                                    </a>
                                    <span class="special-column-num">3篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9536322.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9536322.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20201206110259891.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Python
                                        </span>
                                    </a>
                                    <span class="special-column-num">23篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9536624.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9536624.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191123174827226.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Python基础
                                        </span>
                                    </a>
                                    <span class="special-column-num">20篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10728721.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10728721.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210111151026601.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Python实践与随笔
                                        </span>
                                    </a>
                                    <span class="special-column-num">6篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10856769.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10856769.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306135425926.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Linux
                                        </span>
                                    </a>
                                    <span class="special-column-num">47篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10856777.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10856777.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306135943342.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Linux实践与随笔
                                        </span>
                                    </a>
                                    <span class="special-column-num">24篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9218531.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9218531.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306144210800.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Linux基础
                                        </span>
                                    </a>
                                    <span class="special-column-num">17篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10856875.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10856875.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2021030614360945.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            HighAvailability
                                        </span>
                                    </a>
                                    <span class="special-column-num">3篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10882419.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10882419.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar special-column-bar-second"></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210314213234639.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Ansible
                                        </span>
                                    </a>
                                    <span class="special-column-num">6篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9610135.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9610135.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191220222647941.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            CV
                                        </span>
                                    </a>
                                    <span class="special-column-num">19篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_10638931.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_10638931.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20201206110419763.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            MySQL
                                        </span>
                                    </a>
                                    <span class="special-column-num">4篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_5612041.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_5612041.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191123160236367.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Oracle
                                        </span>
                                    </a>
                                    <span class="special-column-num">3篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_5620215.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_5620215.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210324095440653.jpg" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Network
                                        </span>
                                    </a>
                                    <span class="special-column-num">6篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_3269003.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_3269003.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20191123160626567.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Qt
                                        </span>
                                    </a>
                                    <span class="special-column-num">2篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_3143629.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_3143629.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20210306142033316.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            Windows
                                        </span>
                                    </a>
                                    <span class="special-column-num">7篇</span>
                                </li>
                                <li>
                                    <a class="clearfix special-column-name" href="https://blog.csdn.net/qpeity/category_9853289.html" data-report-click="{&quot;mod&quot;:&quot;popu_537&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4137&quot;,&quot;strategy&quot;:&quot;pc付费专栏左侧入口&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity/category_9853289.html&quot;,&quot;ab&quot;:&quot;new&quot;}">
                                        <div class="special-column-bar "></div>
                                        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20200330095741694.png" alt="" onerror="this.src=&#39;https://img-blog.csdnimg.cn/20201014180756922.png?x-oss-process=image/resize,m_fixed,h_64,w_64&#39;">
                                        <span class="">
                                            SAP
                                        </span>
                                    </a>
                                    <span class="special-column-num">10篇</span>
                                </li>
                            </ul>
                        </div>
                    </div>
            </div>
        </div>
    </aside>
</div>

<div class="recommend-right1  align-items-stretch clearfix" id="rightAsideConcision" data-type="recommend">
    <aside class="recommend-right_aside">
        <div id="recommend-right-concision">
            <div class="flex-column aside-box groupfile" id="groupfileConcision">
                <div class="groupfile-div1" style="max-height: 902px;">
                <h3 class="aside-title">目录</h3>
                <div class="align-items-stretch group_item">
                    <div class="pos-box">
                        <div class="scroll-box">
                            <div class="toc-box"><ol><li class="active"><a href="https://blog.csdn.net/qpeity/article/details/*********#t0">伟大的司令官（题目原名称不能发）</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t1">[UTCTF2020]sstv&nbsp;</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t2">二维码&nbsp;</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t3">你竟然赶我走</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t4">大白</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t5">N种方法解决</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t6">乌镇峰会种图</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t7">基础破解</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t8">wireshark</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t9">文件中的秘密</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t10">LSB</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t11">zip伪加密</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t12">rar</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t13">小明的保险箱</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t14">ningen</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t15">被嗅探的流量</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t16">镜子里面的世界</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t17">爱因斯坦</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t18">easycap</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t19">隐藏的钥匙</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t20">另外一个世界</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t21">FLAG</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t22">BOOM</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t23">神秘龙卷风</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t24">假如给我三天光明</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t25">数据包中的线索</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t26">后门查杀、webshell后门【D盾工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t27">来首歌吧</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t28">荷兰宽带数据泄露【RouterPassView工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t29">面具下的flag【zip伪加密、vmdk用7z解压缩】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t30">九连环【zip伪加密、steghide工具】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t31">被劫持的神秘礼物</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t32">刷新过的图片【F5-steganography工具】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t33">[BJDCTF2020]认真你就输了【ms-office文件本质是zip】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t34">[BJDCTF2020]藏藏藏</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t35">被偷走的文件【zip和rar文件都应该4位爆破一下、rar文件格式】</a></li><li class="sub-box"><ol><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t36">RAR 5.0 文件格式</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t37">RAR 4.x 文件格式</a></li></ol></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t38">snake【serpent对称加密算法】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t39">[GXYCTF2019]佛系青年</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t40">[BJDCTF2020]你猜我是个啥</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t41">菜刀666【流量分析】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t42">秘密文件【流量分析】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t43">[BJDCTF2020]just_a_rar</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t44">[BJDCTF2020]鸡你太美【gif文件格式、脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t45">[BJDCTF2020]一叶障目</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t46">[SWPU2019]神奇的二维码【俄罗斯套娃、耐心】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t47">梅花香之苦寒来【字符串描述的ascii】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t48">[BJDCTF2020]纳尼【gif文件格式】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t49">穿越时空的思念【摩斯码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t50">[ACTF新生赛2020]outguess</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t51">[HBNIS2018]excel破解【excel文件隐写】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t52">[HBNIS2018]来题中等的吧【摩斯码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t53">谁赢了比赛？</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t54">[SWPU2019]我有一只马里奥【ntfs流特性】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t55">[WUSTCTF2020]find_me.jpg【盲文】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t56">[GXYCTF2019]gakki【字频统计、脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t57">[ACTF新生赛2020]base64隐写【base64隐写、脑洞】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t58">[GUET-CTF2019]KO【Ook!】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t59">[MRCTF2020]ezmisc【png图片crc32爆破】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t60">[SWPU2019]伟大的侦探【EBCDIC编码、图形密码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t61">黑客帝国【png、jpg文件格式】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t62">[MRCTF2020]你能看懂音符吗【rar文件格式、音符密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t63">[HBNIS2018]caesar【凯撒密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t64">[HBNIS2018]低个头【脑洞】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t65">[SUCTF2018]single dog【aaencode等**encode密码】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t66">我吃三明治【base32】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t67">sqltest【流量分析】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t68">[ACTF新生赛2020]music【m4a文件格式】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t69">[SWPU2019]你有没有好好看网课【Kinovea工具、敲击码】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t70">[ACTF新生赛2020]NTFS数据流【ntfs流特性】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t71">john-in-the-middle【流量分析、PNG隐写】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t72">喵喵喵【ntfs流及工具、umcompyle6工具】</a></li><li class="sub-box"><ol><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t73">uncompyle6报错和配置</a></li></ol></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t74">[ACTF新生赛2020]swp【zip伪加密、vim】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t75">[GXYCTF2019]SXMgdGhpcyBiYXNlPw==【base64隐写】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t76">间谍启示录【隐藏文件】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t77">Mysterious【反编译】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t78">[UTCTF2020]docx【word本质是zip】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t79">弱口令【cloacked-pixel隐写工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t80">[RoarCTF2019]黄金6年【Kinovea工具】</a></li><li class=""><a href="https://blog.csdn.net/qpeity/article/details/*********#t81">小易的U盘【autorun.inf文件】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t82">[WUSTCTF2020]爬</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t83">[WUSTCTF2020]alison_likes_jojo【john、outguess】</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t84">[安洵杯 2019]吹着贝斯扫二维码</a></li><li><a href="https://blog.csdn.net/qpeity/article/details/*********#t85">Next</a></li></ol></div>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    </aside>
</div>

      </div>
      <div class="mask-dark"></div>
      <div class="skin-boxshadow"></div>
      <div class="directory-boxshadow"></div>
<div class="comment-side-box-shadow comment-side-tit-close" id="commentSideBoxshadow">
<div class="comment-side-content">
	<div class="comment-side-tit">
		<span class="comment-side-tit-count">评论</span>	
	<img class="comment-side-tit-close" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/closeBt.png"></div>
	<div id="pcCommentSideBox" class="comment-box comment-box-new2 " style="display:block">
		<div class="comment-edit-box d-flex">
			<div class="user-img">
				<a href="https://blog.csdn.net/pzn1022" target="_blank">
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9a0a7257a1fc4f6e8e046123a4a5c58f_pzn1022.jpg!1">
				</a>
			</div>
			<form id="commentform">
				<textarea class="comment-content" name="comment_content" id="comment_content" placeholder="欢迎高质量的评论，低质的评论会被折叠" maxlength="1000"></textarea>
				<div class="comment-reward-box" style="background-image: url(&#39;https://img-home.csdnimg.cn/images/20230131025301.png&#39;);">
          <a class="btn-remove-reward"></a>
          <div class="form-reward-box">
            <div class="info">
              成就一亿技术人!
            </div>
            <div class="price-info">
              拼手气红包<span class="price">6.0元</span>
            </div>
          </div>
        </div>
        <div class="comment-operate-box">
					<div class="comment-operate-l">
						<span id="tip_comment" class="tip">还能输入<em>1000</em>个字符</span>
					</div>
					<div class="comment-operate-c">
						&nbsp;
					</div>
					<div class="comment-operate-r">
            <div class="comment-operate-item comment-reward">
							<img class="comment-operate-img" data-url="https://csdnimg.cn/release/blogv2/dist/pc/img/" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/commentReward.png" alt="红包">
							<span class="comment-operate-tip">添加红包</span>
						</div>
						<div class="comment-operate-item comment-emoticon">
							<img class="comment-operate-img" data-url="https://csdnimg.cn/release/blogv2/dist/pc/img/" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/commentEmotionIcon.png" alt="表情包">
							<span class="comment-operate-tip">插入表情</span>
							<div class="comment-emoticon-box comment-operate-isshow" style="display: none;">
								<div class="comment-emoticon-img-box"></div>
							</div>
						</div>
						<div class="comment-operate-item comment-code">
							<img class="comment-operate-img" data-url="https://csdnimg.cn/release/blogv2/dist/pc/img/" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/commentCodeIcon.png" alt="表情包">
							<span class="comment-operate-tip">代码片</span>
							<div class="comment-code-box comment-operate-isshow" style="display: none;">
								<ul id="commentCode">
									<li><a data-code="html">HTML/XML</a></li>
									<li><a data-code="objc">objective-c</a></li>
									<li><a data-code="ruby">Ruby</a></li>
									<li><a data-code="php">PHP</a></li>
									<li><a data-code="csharp">C</a></li>
									<li><a data-code="cpp">C++</a></li>
									<li><a data-code="javascript">JavaScript</a></li>
									<li><a data-code="python">Python</a></li>
									<li><a data-code="java">Java</a></li>
									<li><a data-code="css">CSS</a></li>
									<li><a data-code="sql">SQL</a></li>
									<li><a data-code="plain">其它</a></li>
								</ul>
							</div>
						</div>
						<div class="comment-operate-item">
							<input type="hidden" id="comment_replyId" name="comment_replyId">
							<input type="hidden" id="article_id" name="article_id" value="*********">
							<input type="hidden" id="comment_userId" name="comment_userId" value="">
							<input type="hidden" id="commentId" name="commentId" value="">
							<a data-report-click="{&quot;mod&quot;:&quot;1582594662_003&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4227&quot;,&quot;ab&quot;:&quot;new&quot;}">
							<input type="submit" class="btn-comment btn-comment-input" value="评论">
							</a>
						</div>
					</div>
				</div>
			</form>
		</div>
		<div class="comment-list-container">
			<div class="comment-list-box comment-operate-item">
			</div>
			<div id="lookFlodComment" class="look-flod-comment" style="display: none;">
					<span class="count">0</span>&nbsp;条评论被折叠&nbsp;<a class="look-more-flodcomment">查看</a>
			</div>
			
		</div>
	</div>
	<div id="pcFlodCommentSideBox" class="pc-flodcomment-sidebox">
		<div class="comment-fold-tit"><span id="lookUnFlodComment" class="back"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/commentArrowLeftWhite.png" alt=""></span>被折叠的&nbsp;<span class="count">0</span>&nbsp;条评论
		 <a href="https://blogdev.blog.csdn.net/article/details/122245662" class="tip" target="_blank">为什么被折叠?</a>
		 <a href="https://bbs.csdn.net/forums/FreeZone" class="park" target="_blank">
		 <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconPark.png">到【灌水乐园】发言</a>                                
		</div>
		<div class="comment-fold-content"></div>
		<div id="lookBadComment" class="look-bad-comment side-look-comment">
			<a class="look-more-comment">查看更多评论<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/commentArrowDownWhite.png" alt=""></a>
		</div>
	</div>
</div>
<div class="comment-rewarddialog-box">
  <div class="form-box">
    <div class="title-box">
      添加红包
      <a class="btn-form-close"></a>
    </div>
    <form id="commentRewardForm">
      <div class="ipt-box">
        <label for="txtName">祝福语</label>
        <div class="ipt-btn-box">
          <input type="text" name="name" id="txtName" autocomplete="off" maxlength="50">
          <a class="btn-ipt btn-random"></a>
        </div>
        <p class="notice">请填写红包祝福语或标题</p>
      </div>
      <div class="ipt-box">
        <label for="txtSendAmount">红包数量</label>
        <div class="ipt-txt-box">
          <input type="text" name="sendAmount" maxlength="4" id="txtSendAmount" placeholder="请填写红包数量(最小10个)" autocomplete="off">
          <span class="after-txt">个</span>
        </div>
        <p class="notice">红包个数最小为10个</p>
      </div>
      <div class="ipt-box">
        <label for="txtMoney">红包总金额</label>
        <div class="ipt-txt-box error">
          <input type="text" name="money" maxlength="5" id="txtMoney" placeholder="请填写总金额(最低5元)" autocomplete="off">
          <span class="after-txt">元</span>
        </div>
        <p class="notice">红包金额最低5元</p>
      </div>
      <div class="balance-info-box">
        <label>余额支付</label>
        <div class="balance-info">
          当前余额<span class="balance">3.43</span>元
          <a href="https://i.csdn.net/#/wallet/balance/recharge" class="link-charge" target="_blank">前往充值 &gt;</a>
        </div>
      </div>
      <div class="opt-box">
        <div class="pay-info">
          需支付：<span class="price">10.00</span>元
        </div>
        <button type="button" class="ml-auto btn-cancel">取消</button>
        <button type="button" class="ml8 btn-submit" disabled="true">确定</button>
      </div>
    </form>
  </div>
</div>

</div>

<div class="redEnvolope" id="redEnvolope">
  <div class="env-box">
    <div class="env-container">
      <div class="pre-open" id="preOpen">
        <div class="top" style="background-image: url(&quot;https://img-home.csdnimg.cn/images/20230131025150.png&quot;);">
          <header>
            <img class="clearTpaErr" :src="redpacketAuthor.avatar" alt="">
            <div class="author">成就一亿技术人!</div>
          </header>
          <div class="bot-icon"></div>
        </div>
        <footer style="background-image: url(&quot;https://img-home.csdnimg.cn/images/20230131025228.png&quot;);">
          <div class="red-openbtn open-start" style="background-image: url(&quot;https://img-home.csdnimg.cn/images/20230131025209.png&quot;);"></div>
          <div class="tip">
            领取后你会自动成为博主和红包主的粉丝
            <a class="rule" target="_blank" href="https://blogdev.blog.csdn.net/article/details/128932621">规则</a>
          </div>
        </footer>
      </div>
      <div class="opened" id="opened">
        <div class="bot-icon">
          <header>
            <a class="creatorUrl" href="https://blog.csdn.net/qpeity/article/details/*********" target="_blank">
              <img class="clearTpaErr" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/default.jpg!2" alt="">
            </a>
            <div class="author">
              <div class="tt">hope_wisdom</div> 发出的红包
            </div>
          </header>
        </div>
        <div class="receive-box">
          <header></header>
          <div class="receive-list">
          </div>
        </div>
      </div>
    </div>
    <div class="close-btn"></div>
  </div>
</div>
<div id="rewardNew" class="reward-popupbox-new">
	<p class="rewad-title">打赏作者<span class="reward-close"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/closeBt.png"></span></p>
	<dl class="profile-box">
		<dd>
		<a href="https://blog.csdn.net/qpeity" data-report-click="{&quot;mod&quot;:&quot;popu_379&quot;,&quot;dest&quot;:&quot;https://blog.csdn.net/qpeity&quot;,&quot;ab&quot;:&quot;new&quot;}">
			<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/6b2759cb62f743cf81d6a2848214a29c_qpeity.jpg!1" class="avatar_pic">
		</a>
		</dd>
		<dt>
			<p class="blog-name">苦行僧(csdn)</p>
			<p class="blog-discript">你的鼓励将是我创作的最大动力</p>
		</dt>
	</dl>
	<div class="reward-box-new">
			<div class="reward-content"><div class="reward-right"></div></div>
	</div>
	<div class="money-box">
    <span class="choose-money choosed" data-id="1">¥1</span>
    <span class="choose-money " data-id="2">¥2</span>
    <span class="choose-money " data-id="4">¥4</span>
    <span class="choose-money " data-id="6">¥6</span>
    <span class="choose-money " data-id="10">¥10</span>
    <span class="choose-money " data-id="20">¥20</span>
	</div>
	<div class="sure-box">
		<div class="sure-box-money">
			<div class="code-box">
				<div class="code-num-box">
					<span class="code-name">扫码支付：</span><span class="code-num">¥1</span>
				</div>
				<div class="code-img-box">
					<div class="renovate">
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/pay-time-out.png">
					<span>获取中</span>
					</div>
				</div>
				<div class="code-pay-box">
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newWeiXin.png" alt="">
					<img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newZhiFuBao.png" alt="">
					<span>扫码支付</span>
				</div>
			</div>
		</div>
		<div class="sure-box-blance">
			<p class="tip">您的余额不足，请更换扫码支付或<a target="_blank" data-report-click="{&quot;mod&quot;:&quot;1597646289_003&quot;,&quot;spm&quot;:&quot;1001.2101.3001.4302&quot;}" href="https://i.csdn.net/#/wallet/balance/recharge?utm_source=RewardVip" class="go-invest">充值</a></p>
			<p class="is-have-money"><a class="reward-sure">打赏作者</a></p>
		</div>
	</div>
</div>
      
      <div class="pay-code">
      <div class="pay-money">实付<span class="pay-money-span" data-nowprice="" data-oldprice="">元</span></div>
      <div class="content-blance"><a class="blance-bt" href="javascript:;">使用余额支付</a></div>
      <div class="content-code">
        <div id="payCode" data-id="">
          <div class="renovate">
            <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/pay-time-out.png">
            <span>点击重新获取</span>
          </div>
        </div>
        <div class="pay-style"><span><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/weixin.png"></span><span><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/zhifubao.png"></span><span><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/jingdong.png"></span><span class="text">扫码支付</span></div>
      </div>
      <div class="bt-close">
        <svg t="1567152543821" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10924" xmlns:xlink="http://www.w3.org/1999/xlink" width="12" height="12">
          <defs>
            <style type="text/css"></style>
          </defs>
          <path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="10925"></path>
        </svg>
      </div>
      <div class="pay-balance">
        <input type="radio" class="pay-code-radio" data-type="details">
        <span class="span">钱包余额</span>
          <span class="balance" style="color:#FC5531;font-size:14px;">0</span>
          <div class="pay-code-tile">
            <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/pay-help.png" alt="">
            <div class="pay-code-content">
              <div class="span">
                <p class="title">抵扣说明：</p>
                <p> 1.余额是钱包充值的虚拟货币，按照1:1的比例进行支付金额的抵扣。<br> 2.余额无法直接购买下载，可以购买VIP、付费专栏及课程。</p>
              </div>
            </div>
          </div>
      </div>
      <a class="pay-balance-con" href="https://i.csdn.net/#/wallet/balance/recharge" target="_blank"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/recharge.png" alt=""><span>余额充值</span></a>
    </div>
    <div style="display:none;">
      <img src="https://blog.csdn.net/qpeity/article/details/*********" onerror="setTimeout(function(){if(!/(csdn.net|iteye.com|baiducontent.com|googleusercontent.com|360webcache.com|sogoucdn.com|bingj.com|baidu.com)$/.test(window.location.hostname)){window.location.href=&quot;\x68\x74\x74\x70\x73\x3a\x2f\x2f\x77\x77\x77\x2e\x63\x73\x64\x6e\x2e\x6e\x65\x74&quot;}},3000);">
    </div>
    <div class="keyword-dec-box" id="keywordDecBox"></div>
  
    
    <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/chart.css">
    <script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/chart.min.js.下载"></script>
    <script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/widget2chart.js.下载"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/axios-83fa28cedf.min.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/pc_wap_highlight-8defd55d6e.min.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/pc_wap_common-be82269d23.min.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/edit_copy_code-2d3931414f.min.js.下载" type="text/javascript"></script>
  <link rel="stylesheet" href="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/atom-one-dark.css">
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-accusation.js.下载" type="text/javascript"></script>
  <script>
    // 全局声明
    if (window.csdn === undefined) {
      window.csdn = {};
    }
    window.csdn.sideToolbar = {
      options: {
        report: {
          isShow: true,
        },
        qr: {
          isShow: false,
        },
        guide: {
          isShow: true
        }
      }
    }
    $(function() {
      $(document).on('click', "a.option-box[data-type='report']", function() {
        window.csdn.loginBox.key({
          biz: 'blog',
          subBiz: 'other_service',
          cb: function() {
            window.csdn.feedback({
              "type": 'blog',
              "rtype": 'article',
              "rid": articleId,
              "reportedName": username,
              "submitOptions": {
                "title": articleTitle,
                "contentUrl": articleDetailUrl
              },
              "callback": function() {
                showToast({
                  text: "感谢您的举报，我们会尽快审核！",
                  bottom: '10%',
                  zindex: 9000,
                  speed: 500,
                  time: 1500
                })
              }
            })
          }
        })
      });
    })
  </script>
    <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/baidu-search.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/qrcode.js.下载"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/qrcode.min.js.下载"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-ordercart.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/user-ordertip.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/order-payment.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/common-a425354f6a.min.js.下载" type="text/javascript"></script>
  <ins class="adsbygoogle adsbygoogle-noablate" data-adsbygoogle-status="done" style="display: none !important;" data-ad-status="unfilled"><div id="aswift_0_host" style="border: none; height: 0px; width: 0px; margin: 0px; padding: 0px; position: relative; visibility: visible; background-color: transparent; display: inline-block;" tabindex="0" title="Advertisement" aria-label="Advertisement"><iframe id="aswift_0" name="aswift_0" browsingtopics="true" style="left:0;position:absolute;top:0;border:0;width:undefinedpx;height:undefinedpx;" sandbox="allow-forms allow-popups allow-popups-to-escape-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-activation" frameborder="0" marginwidth="0" marginheight="0" vspace="0" hspace="0" allowtransparency="true" scrolling="no" allow="attribution-reporting" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ads(1).html" data-google-container-id="a!1" data-load-complete="true"></iframe></div></ins><script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/detail-7d1e7cdc5c.min.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/column-fe4f666d72.min.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/side-toolbar.js.下载" type="text/javascript"></script>
  <script src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/copyright.js.下载" type="text/javascript"></script>
  <script>
    $(".MathJax").remove();
    if ($('div.markdown_views pre.prettyprint code.hljs').length > 0) {
      $('div.markdown_views')[0].className = 'markdown_views';
    }
  </script>
  <script type="text/javascript" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/MathJax.js.下载"></script>
  <script type="text/x-mathjax-config;executed=true">
    MathJax.Hub.Config({
      "HTML-CSS": {
        linebreaks: { automatic: true, width: "94%container" },
        imageFont: null
      },
      tex2jax: {
      preview: "none",
      ignoreClass:"title-article"
      },
      mml2jax: {
      preview: 'none'
      }
    });
  </script>
<script type="text/javascript" crossorigin="" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/csdn-login-box.js.下载"></script><div id="pointDivs"><div class="point-outer point-pre"><div class="point-inner"></div></div><div class="point-outer point-pre"><div class="point-inner"></div></div><div class="point-outer point-pre"><div class="point-inner"></div></div><div class="point-outer point-pre"><div class="point-inner"></div></div><div class="point-outer point-pre"><div class="point-inner"></div></div></div><div id="st_mask" onclick="closeMask()" style="width: 100%; height: 100%; background: rgba(0, 0, 0, 0.4); position: fixed; left: 0px; top: 0px; display: none; z-index: 1;"></div><div id="st_confirmBox" style="width: 360px; position: fixed; text-align: left; display: none; z-index: 100; inset: 0px; height: 208px; margin: auto;"><div id="st_confirm" style="background: rgb(255, 255, 255); border-radius: 4px; overflow: hidden; padding: 24px; width: 360px; height: 208px;"><span id="st_confirm_tit" style="width: 100%; max-height: 24px; font-size: 18px; font-weight: 500; color: rgb(34, 34, 38); line-height: 24px; text-align: left; display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"></span><span id="st_confirm_text" style="text-align: left; height: 44px; font-size: 14px; font-weight: 400; color: rgb(85, 86, 102); line-height: 22px; overflow: hidden; display: -webkit-box; -webkit-box-orient: vertical; text-overflow: ellipsis; -webkit-line-clamp: 2; margin-top: 16px; margin-bottom: 40px;"></span><span class="st_confirm_btn success" style="background: rgb(252, 85, 51); color: rgb(255, 255, 255); text-align: center; display: block; width: 88px; height: 36px; line-height: 36px; margin-left: 16px; float: right; border-radius: 18px;">确定</span><span class="st_confirm_btn cancel" style="color: rgb(34, 34, 38); text-align: center; display: block; width: 88px; height: 36px; line-height: 36px; margin-left: 16px; float: right; box-sizing: border-box; border: 1px solid rgb(204, 204, 216); border-radius: 18px;">取消</span><span id="st_confirm_close" style="display: block; width: 12px; height: 12px; position: absolute; text-align: center; z-index: 100; top: 24px; right: 24px;"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/closeBt.png" style="display: block; width: 12px; height: 12px;"></span><div style="clear: both; display: block;"></div></div></div><div id="st_alertBox" style="width: 100%; position: fixed; left: 0px; top: 34%; text-align: center; display: none; z-index: 2;"><div id="st_alert" style="width: 80%; margin: 0px auto; background: rgb(255, 255, 255); border-radius: 2px; overflow: hidden; padding-top: 20px; text-align: center;"><span id="st_alert_text" style="background: rgb(255, 255, 255); overflow: hidden; padding: 15px 8px 30px; text-align: center; display: block;"></span><span id="st_alert_btn" onclick="closeMask()" style="background: rgb(27, 121, 248); color: rgb(255, 255, 255); padding: 8px; text-align: center; display: block; width: 72%; margin: 0px auto 20px; border-radius: 2px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"></span></div></div><div id="st_toastBox" style="width: 100%; position: fixed; left: 0px; bottom: 10%; text-align: center; display: none;"><span id="st_toastContent" style="color: rgb(255, 255, 255); background: rgba(0, 0, 0, 0.8); padding: 8px 24px; border-radius: 4px; max-width: 80%; display: inline-block; font-size: 16px;"></span></div> <div class="report-box">  <div class="pos-boxer">      <div class="pos-content">          <div class="box-title">              <p>举报</p>              <img class="icon btn-close" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/closeBlack.png">          </div>          <div class="box-header">              <div class="box-top"><span>选择你想要举报的内容（必选）</span></div>              <div class="box-botoom">                  <ul>                      <li data="1" type="nei">内容涉黄</li>                      <li data="2" type="nei">政治相关</li>                      <li data="3" type="nei">内容抄袭</li>                      <li data="4" type="nei">涉嫌广告</li>                      <li data="5" type="nei">内容侵权</li>                      <li data="6" type="nei">侮辱谩骂</li>                      <li data="8" type="nei">样式问题</li>                      <li data="7" type="nei">其他</li>                  </ul>              </div>          </div>          <div>          <div class="box-content">          </div>          <div class="box-content">          </div>                    <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>原文链接（必填）</span>                      </div>                      <div class="box-content-bottom" style="padding-bottom: 16px;">                        <div class="box-input" style="height: 32px;line-height: 32px;">                        <input class="content-input" type="text" id="originalurl" name="originalurl" placeholder="请输入被侵权原文链接">                        </div>                      </div>          </div>          <div class="box-content">          </div>          <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>请选择具体原因（必选）</span>                      </div>                  <div class="box-content-bottom">                          <ul>                              <li sub_type="1">包含不实信息</li>                              <li sub_type="2">涉及个人隐私</li>                          </ul>                      </div>          </div>          <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>请选择具体原因（必选）</span>                      </div>                  <div class="box-content-bottom">                          <ul>                              <li sub_type="1">侮辱谩骂</li>                              <li sub_type="2">诽谤</li>                          </ul>                  </div>          </div>          <div class="box-content" style="display:none;">                <div class="box-content-top">                        <span>请选择具体原因（必选）</span>                    </div>                <div class="box-content-bottom">                        <ul>                            <li sub_type="1">搬家样式</li>                            <li sub_type="2">博文样式</li>                        </ul>                </div>          </div>          <div class="box-content" style="display:none;">          </div>          </div>            <div id="cllcont" style="display:none;">            <div class="box-content-top">              <span class="box-content-span">补充说明（选填）</span>            </div>                <div class="box-content-bottom">                  <div class="box-input">                    <textarea class="ipt ipt-textarea" style="padding:0;" name="description" placeholder="请详细描述您的举报内容"></textarea>                  </div>                </div>            </div>            </div>      <div class="pos-footer">          <p class="btn-close">取消</p>          <p class="box-active">确定</p>      </div>  </div></div><svg aria-hidden="true" style="position: absolute; width: 0px; height: 0px; overflow: hidden;"><symbol id="sousuo" viewBox="0 0 1024 1024"><path d="M719.6779726 653.55865555l0.71080936 0.70145709 191.77828505 191.77828506c18.25658185 18.25658185 18.25658185 47.86273439 0 66.12399318-18.26593493 18.26125798-47.87208744 18.26125798-66.13334544 0l-191.77828505-191.77828506c-0.2338193-0.2338193-0.4676378-0.4676378-0.69678097-0.71081014-58.13206223 44.25257003-130.69075187 70.51978897-209.38952657 70.51978894C253.06424184 790.19776156 98.14049639 635.27869225 98.14049639 444.17380511S253.06424184 98.14049639 444.16912898 98.14049639c191.10488633 0 346.02863258 154.92374545 346.02863259 346.02863259 0 78.6987747-26.27189505 151.25746514-70.51978897 209.38952657z m-275.50884362 43.11621045c139.45428506 0 252.50573702-113.05145197 252.50573702-252.50573702s-113.05145197-252.50573702-252.50573702-252.50573783-252.50573702 113.05145197-252.50573783 252.50573783 113.05145197 252.50573702 252.50573783 252.50573702z"></path></symbol><symbol id="gonggong_csdnlogo_" viewBox="0 0 4096 1024"><path d="M1234.16069807 690.46341551c62.96962316 23.02318413 194.30703694 45.91141406 300.51598128 45.91141406 114.44114969 0 178.13952547-31.68724287 183.2407937-80.86454822 4.642424-44.8587714-42.21366937-50.93170978-171.44579784-81.53931916-178.57137886-43.77913792-292.49970264-111.55313011-281.32549604-219.86735976 12.9825927-125.75031047 181.27046257-220.78504823 439.49180199-220.78504822 125.88526465 0 247.93783044 8.87998544 311.17736197 29.60894839l-21.7006331 158.57116851c-41.05306337-14.27815288-198.1937175-34.11641822-304.48363435-34.11641822-107.7744129 0-163.56447339 33.90049151-167.42416309 71.06687432-4.85835069 47.04502922 51.14763648 49.23128703 191.14910897 86.50563321 189.58364043 48.09767188 272.47250144 115.81768239 261.6221849 220.81203906-12.71268432 123.51007099-164.13128096 228.53141851-466.48263918 228.53141851-125.85827383 0-234.33444849-22.96920244-294.09216204-45.93840492l19.730302-157.86940672zM3010.8325562 172.75216735c688.40130256-129.79893606 747.80813523 103.42888812 726.53935551 309.80082928l-40.08139323 381.78539207h-218.51781789l36.57258439-348.20879061c7.90831529-76.68096846 57.13960232-226.66905073-180.54170997-221.05495659-82.26807176 1.99732195-123.05122675 13.2794919-123.05122677 13.27949188s-7.15257186 92.65954408-15.81663059 161.13529804l-41.43093509 394.84895728h-214.3072473l42.53755943-389.15389062 28.09746151-302.43233073z m-869.48282929-18.05687008c49.12332368-5.34418577 124.58970448-10.76934404 228.45044598-10.76934405 173.38913812 0 313.57954648 30.17575597 400.38207891 93.63121421 77.94953781 59.16391512 129.82592689 154.95439631 115.4668015 293.74128117-13.25250106 129.15115596-80.405704 219.57046055-178.16651631 275.4954752-89.44763445 52.74009587-202.16137055 75.27744492-371.66382812 75.27744493-99.94707012 0-195.27870708-5.39816743-267.77609576-16.14052064L2141.37671774 154.69529727z m143.26736381 569.85754561c16.70732823 3.23890047 38.67786969 6.45081009 81.99816339 6.45081009 173.44311979 0 295.7386031-85.23706385 308.01943403-205.07638097 17.84094339-173.2271931-90.63523129-233.79463176-273.39018992-232.74198912-23.67096422 0-56.57279475 0-73.98188473 3.1849188l-42.6725136 428.15565036z" fill="#262626"></path><path d="M1109.8678928 870.30336371c-41.10704503 14.25116203-126.26313639 23.96786342-245.23874671 23.96786342-342.13585224 0-526.8071603-160.59548129-504.97157302-372.90540663C385.78470347 268.40769434 659.36382925 126.08500985 958.9081404 126.08500985c116.00661824 0 184.32042718 9.33882968 248.31570215 24.99351522l-20.5400271 170.42014604c-42.56455024-14.33213455-142.32268451-27.50366309-223.07926938-27.50366311-176.25016686 0-325.94134993 52.49717834-343.10752238 218.57179958-15.30380469 148.50358623 89.7715245 219.48948804 288.04621451 219.48948804 69.0155707 0 170.77102691-9.8786464 217.81605614-24.15679928l-16.49140154 162.40386737z" fill="#CA0C16"></path></symbol><symbol id="gonggong_csdnlogodanse_" viewBox="0 0 4096 1024"><path d="M1229.41995733 690.46341551c62.96962316 23.02318413 194.30703694 45.91141406 300.51598128 45.91141406 114.44114969 0 178.13952547-31.68724287 183.2407937-80.86454822 4.642424-44.8587714-42.21366937-50.93170978-171.44579784-81.53931916-178.57137886-43.77913792-292.49970264-111.55313011-281.32549604-219.86735976 12.9825927-125.75031047 181.27046257-220.78504823 439.49180199-220.78504822 125.88526465 0 247.93783044 8.87998544 311.17736197 29.60894839l-21.7006331 158.57116851c-41.05306337-14.27815288-198.1937175-34.11641822-304.48363435-34.11641822-107.7744129 0-163.56447339 33.90049151-167.42416309 71.06687432-4.85835069 47.04502922 51.14763648 49.23128703 191.14910897 86.50563321 189.58364043 48.09767188 272.47250144 115.81768239 261.6221849 220.81203906-12.71268432 123.51007099-164.13128096 228.53141851-466.48263918 228.53141851-125.85827383 0-234.33444849-22.96920244-294.09216204-45.93840492l19.730302-157.86940672zM3006.09181546 172.75216735c688.40130256-129.79893606 747.80813523 103.42888812 726.53935551 309.80082928l-40.08139323 381.78539207h-218.51781789l36.57258439-348.20879061c7.90831529-76.68096846 57.13960232-226.66905073-180.54170997-221.05495659-82.26807176 1.99732195-123.05122675 13.2794919-123.05122677 13.27949188s-7.15257186 92.65954408-15.81663059 161.13529804l-41.43093509 394.84895728h-214.3072473l42.53755943-389.15389062 28.09746151-302.43233073z m-869.48282929-18.05687008c49.12332368-5.34418577 124.58970448-10.76934404 228.45044598-10.76934405 173.38913812 0 313.57954648 30.17575597 400.38207891 93.63121421 77.94953781 59.16391512 129.82592689 154.95439631 115.4668015 293.74128117-13.25250106 129.15115596-80.405704 219.57046055-178.16651631 275.4954752-89.44763445 52.74009587-202.16137055 75.27744492-371.66382812 75.27744493-99.94707012 0-195.27870708-5.39816743-267.77609576-16.14052064L2136.635977 154.69529727z m143.26736381 569.85754561c16.70732823 3.23890047 38.67786969 6.45081009 81.99816339 6.45081009 173.44311979 0 295.7386031-85.23706385 308.01943403-205.07638097 17.84094339-173.2271931-90.63523129-233.79463176-273.39018992-232.74198912-23.67096422 0-56.57279475 0-73.98188473 3.1849188l-42.6725136 428.15565036z m-1174.74919792 145.75052083c-41.10704503 14.25116203-126.26313639 23.96786342-245.23874671 23.96786342-342.13585224 0-526.8071603-160.59548129-504.97157303-372.90540663C381.04396273 268.40769434 654.62308851 126.08500985 954.16739966 126.08500985c116.00661824 0 184.32042718 9.33882968 248.31570215 24.99351522l-20.5400271 170.42014604c-42.56455024-14.33213455-142.32268451-27.50366309-223.07926938-27.50366311-176.25016686 0-325.94134993 52.49717834-343.10752238 218.57179958-15.30380469 148.50358623 89.7715245 219.48948804 288.04621451 219.48948804 69.0155707 0 170.77102691-9.8786464 217.81605614-24.15679928l-16.49140154 162.40386737z"></path></symbol><symbol id="xieboke1" viewBox="0 0 1024 1024"><path d="M204.70021457 751.89799169h657.99199211a33.6932867 33.6932867 0 0 1 0 67.33536736H163.68452703a33.53966977 33.53966977 0 0 1-18.74125054-5.68382181c-18.63883902-9.4218307-18.17798882-29.44322156-15.20806401-39.17228615C199.0675982 570.27171976 309.41567149 409.58853908 435.38145354 290.12586836A243.22661203 243.22661203 0 0 1 536.97336934 234.20935065c138.10150976-33.79569759 228.3257813-29.95527721 318.60125827-28.52152054-17.15387692 20.48224105-36.20236071 41.6301547-57.29906892 62.93168529-3.1747472 3.22595323-164.67721739 19.91897936-187.97576692 47.05794871-23.29854894 27.13896932 129.60138005 7.37360691 125.19769798 11.11161576-21.6599699 18.33160576-44.90731339 36.4071831-69.94685287 53.8682939-4.50609297 3.1747472-149.52035944-0.35843931-174.61110436 27.85584737-25.19315641 28.16308124 101.89914903 18.12678338 96.0617103 21.40394206-67.43777825 37.63611797-125.96578207 64.62147036-212.70807253 93.8086635-57.65750823 19.4069231-121.8181284 133.13456658-146.5504346 179.06599187a435.75967738 435.75967738 0 0 0-23.04252112 49.10617311z" fill="#CA0C16"></path></symbol><symbol id="gitchat" viewBox="0 0 1024 1024"><path d="M892.08971773 729.08552746h-108.597062v-162.89559374H403.40293801v-108.59706198h488.68677972v271.49265572z m-651.58237345 54.298531V783.49265572h488.68678045v108.59706201H131.91028227V131.91028227h760.17943546v217.19412473h-108.597062V240.50734428H240.50734428v542.87671418z m542.98531145 0h108.597062v108.59706199h-108.597062v-108.59706199z" fill="#FF9100"></path></symbol><symbol id="toolbar-memberhead" viewBox="0 0 1303 1024"><path d="M1061.51168438 433.79527648A78.51879902 78.51879902 0 1 1 1129.35192643 472.74060007h-1.80593246l-48.05350474 403.97922198c-4.55409058 38.16013652-39.41643684 67.133573-80.79584389 67.13357302H319.35199503c-41.30088817 0-76.00619753-28.81639958-80.717325-66.97653526L189.01078861 472.74060007H187.12633728a78.51879902 78.51879902 0 1 1 67.76172401-38.86680556l193.31328323 119.81968805 158.13686148-336.06046024A78.5973179 78.5973179 0 0 1 658.23913228 80.14660493a78.51879902 78.51879902 0 0 1 51.58685077 137.721974l158.13686147 335.82490362 193.54883986-119.89820607z" fill="#FDD840"></path><path d="M1050.8331274 394.22180104a78.51879902 78.51879902 0 1 1 78.51879903 78.51879903h-1.80593246l-48.05350474 403.97922198c-4.55409058 38.16013652-39.41643684 67.133573-80.79584389 67.13357302H659.02432018C658.47468805 793.25433807 658.23913228 505.32590231 658.23913228 80.14660493a78.51879902 78.51879902 0 0 1 51.58685077 137.721974l158.13686147 335.82490362 193.54883986-119.89820607A78.51879902 78.51879902 0 0 1 1050.8331274 394.22180104z" fill="#FFBE00"></path></symbol><symbol id="toolbar-m-memberhead" viewBox="0 0 1303 1024"><path d="M1062.74839935 433.79527648A78.51879902 78.51879902 0 1 1 1130.58864141 472.74060007h-1.80593246l-48.05350474 403.97922198c-4.55409058 38.16013652-39.41643685 67.133573-80.79584389 67.13357302H320.58871c-41.30088817 0-76.00619753-28.81639958-80.71732499-66.97653526L190.24750358 472.74060007H188.36305226a78.51879902 78.51879902 0 1 1 67.761724-38.86680556l193.31328324 119.81968805 158.13686147-336.06046024A78.5973179 78.5973179 0 0 1 659.47584726 80.14660493a78.51879902 78.51879902 0 0 1 51.58685076 137.721974l158.13686148 335.82490362 193.54883985-119.89820607z" fill="#D6D6D6"></path><path d="M1052.06984238 394.22180104a78.51879902 78.51879902 0 1 1 78.51879903 78.51879903h-1.80593246l-48.05350474 403.97922198c-4.55409058 38.16013652-39.41643685 67.133573-80.79584389 67.13357302H660.26103515C659.71140302 793.25433807 659.47584726 505.32590231 659.47584726 80.14660493a78.51879902 78.51879902 0 0 1 51.58685076 137.721974l158.13686148 335.82490362 193.54883985-119.89820607A78.51879902 78.51879902 0 0 1 1052.06984238 394.22180104z" fill="#C1C1C1"></path></symbol><symbol id="csdnc-upload" viewBox="0 0 1024 1024"><path d="M216.37466416 723.16095396v84.46438188h591.25067168v-84.46438188c0-23.32483876 18.90735218-42.23219094 42.23219093-42.23219021s42.23219094 18.90735218 42.23219096 42.23219021v84.46438188c0 46.64967827-37.81470362 84.46438188-84.46438189 84.46438189H216.37466416c-46.64967827 0-84.46438188-37.81470362-84.46438189-84.4643819v-84.46438187c0-23.32483876 18.90735218-42.23219094 42.23219096-42.23219021s42.23219094 18.90735218 42.23219094 42.23219021zM469.76780906 275.55040991L246.55378774 499.53305726a42.30820888 42.30820888 0 0 1-59.99082735 0c-16.56346508-16.62259056-16.56346508-43.57095155 0-60.19354139L480.51167818 144.38144832A42.21952103 42.21952103 0 0 1 512 131.93984464a42.20262858 42.20262858 0 0 1 31.48409853 12.44160369l293.95294108 294.95806754c16.56346508 16.62259056 16.56346508 43.57095155 0 60.19354139a42.30820888 42.30820888 0 0 1-59.99082735 0L554.23219094 275.55040991V680.92876375c0 23.32483876-18.90735218 42.23219094-42.23219094 42.23219021s-42.23219094-18.90735218-42.23219094-42.23219021V275.55040991z"></path></symbol></svg><div>
  <div class="csdn-side-toolbar " style="left: 1674.33px;"><div class="sidetool-writeguide-box">
            <a class="btn-sidetool-writeguide" data-report-query="spm=3001.9732" href="https://mp.csdn.net/mp_blog/manage/creative" target="_blank" data-report-click="{&quot;spm&quot;:&quot;3001.9732&quot;,&quot;extra&quot;: {&quot;type&quot;:&quot;monkey&quot;}}">
              <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/btnGuideSide1.gif" alt="创作活动">
            </a>
            
            <div class="activity-swiper-box-act">
             <div class="activity-swiper-box">
              <button class="btn-close">
                <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/nerCloseWhite.png">
              </button>
              <p class="title">创作话题</p>
              <div class="swiper-box swiper">
                <div class="swiper-wrapper">
                  
            <div class="swiper-slide">
              <a class="activity-item" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://activity.csdn.net/creatActivity?id=10561&quot;,&quot;extra&quot;: {&quot;index&quot;:0,&quot;type&quot;:&quot;title&quot;}}" data-report-query="spm=3001.9733" href="https://activity.csdn.net/creatActivity?id=10561" target="_blank">如何看待unity新的收费模式？</a>
              <a class="btn-go-activity" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://mp.csdn.net/edit?activity_id=10561&quot;,&quot;extra&quot;: {&quot;index&quot;:0,&quot;type&quot;:&quot;button&quot;}}" data-report-query="spm=3001.9733" href="https://mp.csdn.net/edit?activity_id=10561" target="_blank">去创作</a>
            </div>
            
            <div class="swiper-slide">
              <a class="activity-item" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://activity.csdn.net/creatActivity?id=10559&quot;,&quot;extra&quot;: {&quot;index&quot;:1,&quot;type&quot;:&quot;title&quot;}}" data-report-query="spm=3001.9733" href="https://activity.csdn.net/creatActivity?id=10559" target="_blank">你写过最蠢的代码是？</a>
              <a class="btn-go-activity" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://mp.csdn.net/edit?activity_id=10559&quot;,&quot;extra&quot;: {&quot;index&quot;:1,&quot;type&quot;:&quot;button&quot;}}" data-report-query="spm=3001.9733" href="https://mp.csdn.net/edit?activity_id=10559" target="_blank">去创作</a>
            </div>
            
            <div class="swiper-slide">
              <a class="activity-item" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://activity.csdn.net/creatActivity?id=10563&quot;,&quot;extra&quot;: {&quot;index&quot;:2,&quot;type&quot;:&quot;title&quot;}}" data-report-query="spm=3001.9733" href="https://activity.csdn.net/creatActivity?id=10563" target="_blank">C++ 程序员入门需要多久，怎样才能学好？</a>
              <a class="btn-go-activity" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://mp.csdn.net/edit?activity_id=10563&quot;,&quot;extra&quot;: {&quot;index&quot;:2,&quot;type&quot;:&quot;button&quot;}}" data-report-query="spm=3001.9733" href="https://mp.csdn.net/edit?activity_id=10563" target="_blank">去创作</a>
            </div>
            
            <div class="swiper-slide">
              <a class="activity-item" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://activity.csdn.net/creatActivity?id=10567&quot;,&quot;extra&quot;: {&quot;index&quot;:3,&quot;type&quot;:&quot;title&quot;}}" data-report-query="spm=3001.9733" href="https://activity.csdn.net/creatActivity?id=10567" target="_blank">记录国庆发生的那些事儿</a>
              <a class="btn-go-activity" data-report-click="{&quot;spm&quot;:&quot;3001.9733&quot;,&quot;dest&quot;:&quot;https://mp.csdn.net/edit?activity_id=10567&quot;,&quot;extra&quot;: {&quot;index&quot;:3,&quot;type&quot;:&quot;button&quot;}}" data-report-query="spm=3001.9733" href="https://mp.csdn.net/edit?activity_id=10567" target="_blank">去创作</a>
            </div>
            
                </div>
                <div class="swiper-button-define-prev"></div>
                <div class="swiper-button-define-next"></div>
              </div>
             </div>
            </div>
          </div><a class="option-box directory directory-show" data-type="show" style="display:flex" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7790&quot;}">        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconShowDirectory.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">只看<br>目录</span>      </a><a class="option-box directory directory-hide" data-type="hide" style="display: none;" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7791&quot;}">        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconHideDirectory.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">隐藏<br>目录</span>      </a><a class="option-box sidecolumn sidecolumn-show" data-type="show" style="display: none;" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7788&quot;}">        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconShowSide.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">显示<br>侧栏</span>      </a><a class="option-box sidecolumn sidecolumn-hide" data-type="hide" style="display:flex" data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.7789&quot;}">        <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconHideSide.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">隐藏<br>侧栏</span>      </a>
    
    <a class="option-box" data-type="guide">
      <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/guide.png" alt="" srcset="">
      <span class="show-txt">新手<br>引导</span>
    </a>
    
    
    
    
    
    <a class="option-box" data-type="cs">
      <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/kefu.png" alt="" srcset="">
      <span class="show-txt">客服</span>
    </a>
    
    
    
    <a class="option-box" data-type="report">
      <span class="show-txt" style="display:flex;opacity:100;">举报</span>
    </a>
    
    
    <a class="option-box go-top-hide" data-type="gotop">
      <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/fanhuidingbucopy.png" alt="" srcset="">
      <span class="show-txt">返回<br>顶部</span>
    </a>
    
  </div>
  </div><iframe name="googlefcPresent" style="display: none; width: 0px; height: 0px; border: none; z-index: -1000; left: -1000px; top: -1000px;" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/saved_resource.html"></iframe><div class="imgViewDom">        <div class="swiper swiper-container-initialized swiper-container-horizontal">          <a class="close-btn">            <img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/quoteClose1White.png">          </a>          <div class="swiper-wrapper" style="transition: all 0ms ease 0s;"><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/76879c264a45423d99cc6dd937db17ff.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/88b3d0f25fb74b998f7096938fe761ba.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/eda933090ea54596a1857e04f7581a91.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5d3580ba0bcc495faea96c18f831dd3f.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/faf72a490eda4d06bf4ff692e81f261c.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c5fbcf46d54441a5b12816587e2beb4b.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ad014968f23e4d7ba88a48eecb9f2943.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/fedf2195c19f425a93d30a93a489bd9c.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c6a8eae009564526b4e204b6b1c2b28c.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/558cca7ba50249a8bf91f0663829d741.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9ecbca3db7c54a29ad3549c0eb7d7878.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/76488cb3473f48d6ba7d049f58dfeb98.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cb9977741b774696b4d6ec2a0235781a.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/50876dae20b3494ca5372f3066156870.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/48e1ca5c77eb45ab896288ff0f650f8b.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d2242910178642c2ac80166127e5afdf.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e4bf3368475e44dc9f422c61efff0d44.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/b272cf4bc7ac40aa8cdc4a25b69f33e0.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5656897cdff1475586cee272b4bc9485.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2060426d32aa494d9c65de915fe3c65b.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/943cabdce5224d49ad0c421d4a4a7300.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/72e3bad59870400e9d22cff48ba381b0.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/12d2bc833f4c4464a974e87a7c77d504.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/410f582b96c6444db3eed3996246936e.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/dc273be800c74fb3b44595f756e30c89.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0b3ae43e4a894d1caf3e37e8778a83de.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0b1a52ad24be447b8b0a67246a42082a.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7922fd7fa5f045e98edcdbee45113970.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/966d7d714b2145a3892e06f11c460c97.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/bea7d6f1ad044aa2a3959e70460ff9e6.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/dfa16c33a5cb4302ad08bf7c66b171cf.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4062d3d722784301897b1740e3c37729.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/86d145300c3e44c89b8932663a40a1a4.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c9500aad2950410fb6bff43bf887bdd1.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/721629cf2b964b21a7c046a1fc3da711.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/05cc26ea5644486e855c44aed450ec81.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/83d9ff704d19498aac47e22a473e1e8a.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/064b0e11f91849b5b9bf28b305b40083.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e1bdb6647aa5413b9b9771aa57b06d4f.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ea4e3e2310764a09861c3ee6286f2768.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/dd31ac3b377f460dae24d6ed418b4f2a.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a26c18775d1443b0964834c19faa514d.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/82c14a464e1b456d9c22978efd7a0334.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c730b2b8ea704c2da108317dacbacee8.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9857da80fe7148afb2abc721f03c1227.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5bf2f704c1354b43b0c56e055f279586.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/11c04403e15e4008a92a19f059be76cc.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/01b35a7ba0c44bf098d63bcfc8318eee.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ce6cb9eace0b432398381d02ba20968c.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/03a346e55ae14444b2befe270a302e66.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a124bf5750a74b1f8877288f407b2628.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/40ba8013725c42bf90631436108e98ba.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/04c9198b39234c2aaa105b4d9b3fc6ca.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e0d94948de9d4f338bdfb55e6ddc2976.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/25b01502d007473882067c841f241b74.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/462bfc97985b4df5b8d13210d9318373.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0a19d54a51914b5b885f967682da35ba.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2e5b4a35bb4540cba0e5d49d710fd6d0.gif"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d70b3475d5b04973b765c7db4c0a7e86.gif"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/0022e72d1fe94b28be9d3b8b8006b655.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1a165dca88dd459eaf0af8920c4e8ae1.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/b46def330371426e9840cc568082b4b6.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f9d21d7b6dac4d47ba068f1cce3f0f62.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/812f966091df4475b6f3d2009f627071.gif"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/78dc24b97cab49108feb3bac10cca03d.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/c6fb0fdf961f46688a1d9d2322739015.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d5d3eaab3e844d6e829685f2bc53a0ad.gif"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4bea5ca0b4874afd9c9406eea53d1706.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ee6e3524c1744c5bb7f250f1937ad12d.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/bdb0ab23b4804fc6a3bdabb8637c3ca0.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7b944e2bfb634b6b8e22fc9eb08bbd8b.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ab2309757b7449e4966700a44df593b0.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/20a44d21c33f4bfabd988e6d88da4822.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d5b097cd1f514cb6b45963501aaf5094.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/25c4b90f9ac84592bb5e829d25fe6ebf.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/89f189c347794a4cb7e3e6186b07ab65.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4ef436ad639b4369b5c049e5972a1738.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/21f0f5f3b6c246a48050443dd53967b4.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/01d4ee33f0e84c0f9ae0db7d7756a366.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f13a5b53d1ea4c678d729e540c55f141.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/092b144ec3f8421eb0de7a42065554c5.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/db902b8b15b442519299a768612a0ffb.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a242dcbadd214122ad5bc947351e47bb.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5a6f9f4a6be04185b413ced1a5588862.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/34aeebede2d14ffbafa78a4da7902933.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/8696a8e870704d289a7377b45a1f32fe.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e9ec7390f5914f288f316525511deac3.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/be196f62e4f7451b800c5a58a6b10b41.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/06b87b91d3b54fb8b517115af88878c7.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9faf3985a7ef438bb500e83695e6904a.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/866171ba19c341079cac617896ad433f.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/aeaa2039ce714ca8b18bb5599fbea5f5.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/4ccc762b90e44134ad393f8185d33447.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1d4e19f1c60649369b73d570a7ec5704.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f6cdea9ca4a84170b5c4c806b18160e9.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5c0edbd66a634abfb8c38820a7795db5.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/66b79758b3fb4c559fbe5b7429004399.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/8cf2f4f619b5484b84b436e3d4301b19.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cddb4920794147bc8062607e1b8f40a6.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d46067bafad541869f479962cddd3acc.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9079254da81d4244972bb65a202d42a9.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/3fd92f9146ba46c9b69a74316b9b3d4c.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/cf99ad473fa3481695b06ab9f561e288.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9c846f92fc0a47bf95b11f2e6d5802ad.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5cf46f424af9451bb66b71cf7d0cb620.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5fdb0c5d955c44b3849e15a35328ece2.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/74774c3cc17c42279210655fdcb31efe.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5e9db79895a040c7bdd52c2b0cfd339d.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/52283b453681408f803142d0ccb35085.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7d79701d3eae4769a861dd0108010a92.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/9c7bfb53ad1240f39f426edfea7cfadc.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1ca5016402b5485291c0375a1e2c7ca3.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/845a10eadbcd422984a7f7cfc0f16cde.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/2cb9f39972a8488884cf7e9964e1977b.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e48b1eb6d7724ef0b5c0eff9b94d75e9.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/e9f03ec3d9d645f0baf5066d12dc10aa.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/5fa21f2d514d4e4eb9c6d07eef131ef9.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7393a727e9294ef4aca7615b7c469f4b.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7320ab6762014c27900a5b834109711d.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/06c815fca4dc44889bab4f880a7a5259.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a708fbddf2c54bcc9cdd78fc8e7c6257.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/71d2956354624a868c967e9233d89161.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/402b7d6165ad49a1bdf7e183482d6fd8.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/ec20fa89bcbd4c85a9a61bfcbfc2de13.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/d5aa1774228045e89e0207263c368740.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/a588a439be6543a694fc1d23ab945cf5.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/7fea58f76ca2486fa0928f6a66b2e157.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/01c267fea79e4aa49a7f059979f7f331.png"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/92ff88f1e75f4512ab65fbf316150e60.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/f7c2c9b0655742db94779283267fc35a.jpeg"></div><div class="swiper-slide"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/1bb8ddfa98914e4cab2e6d77d0919d73.png"></div></div>          <div class="swiper-button-prev" tabindex="0" role="button" aria-label="Previous slide" aria-disabled="false"></div>          <div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide" aria-disabled="false"></div>        <span class="swiper-notification" aria-live="assertive" aria-atomic="true"></span></div>      </div><iframe src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/aframe.html" width="0" height="0" style="display: none;"></iframe><div class="notification" style="position: fixed; left:initial; right: 24px; top: 50px; bottom: initial; z-index: 99999;"></div><div id="articleSearchTip" class="article-search-tip" style="top: 2512px; left: 703px;">          <a class="article-href article-search" href="https://so.csdn.net/so/search?from=pc_blog_select&amp;q=115cb5" target="_blank"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newarticleSearchWhite.png"><span class="article-text">搜索</span></a>          <a data-report-click="{&quot;spm&quot;:&quot;1001.2101.3001.6773&quot;}" class="article-href article-comment" href="javascript:;" data-type="comment"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newarticleComment1White.png"><span class="article-text">评论</span></a>          <a class="article-href cnote" href="javascript:;" data-type="cnote"><img src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/newcNoteWhite.png"><span class="article-text">笔记</span></a>          </div> </body><!-- 富文本柱状图  --><iframe id="google_esf" name="google_esf" src="./BUUCTF题目Misc部分wp（持续更新）_buuctf题目misc部分wp(持续更新)_buuctf misc专题_苦行僧(csdn)的博客--CSDN博客_files/zrt_lookup.html" style="display: none;"></iframe></html>