function fixedSidebarInButton(){function t(){var t=$(".csdn-side-toolbar");if(0===t.length)var e=setInterval(function(){t=$(".csdn-side-toolbar"),t.length>0&&(t.css("left",i()+"px"),clearInterval(e))},200);else t.css("left",i()+"px")}function i(){return $("#rightAside").length&&"block"===$("#rightAside").css("display")?$("#rightAside").offset().left+300+16:$("#rightAsideConcision").length&&"block"===$("#rightAsideConcision").css("display")?$("#rightAsideConcision").offset().left+300+16:$("main").offset().left+$("main").width()+16}window.csdn.fixedSidebar({targetBox:$(".blog_container_aside"),mainBox:$("main"),sidebar:$(".blog_container_aside"),direction:"left",position:"fixed",bottom:0,zIndex:99,sidebarRightMargin:8,sidebarLeftMargin:8}),window.csdn.fixedSidebar.stopListener=!$(".main_father").hasClass("mainfather-concision"),t(),$(window).resize(function(){t()}),$(document).on("click",".csdn-side-toolbar .option-box",function(i){isShowConcision&&setTimeout(function(){t(),window.csdn.fixedSidebar({targetBox:$(".blog_container_aside"),mainBox:$("main"),sidebar:$(".blog_container_aside"),direction:"left",position:"fixed",bottom:0,zIndex:99,sidebarRightMargin:8,sidebarLeftMargin:8}),window.csdn.fixedSidebar.stopListener=!$(".main_father").hasClass("mainfather-concision")},100)})}function showInit(){$("<div id='st_mask' onclick='closeMask()'></div>").appendTo("body").css({width:"100%",height:"100%",background:"rgba(0,0,0,.4)",position:"fixed",left:"0",top:"0",display:"none","z-index":"1"}),$("<div id='st_confirmBox'></div>").appendTo("body").css({width:"360px",position:"fixed","text-align":"left",display:"none","z-index":"100",left:"0px",top:"0px",right:"0px",bottom:"0px",height:"208px",margin:"auto"}),$("<div id='st_confirm'></div>").appendTo("#st_confirmBox").css({background:"#fff","border-radius":"4px",overflow:"hidden",padding:"24px",width:"360px",height:"208px"}),$("<span id='st_confirm_tit'></span>").appendTo("#st_confirm").css({width:"100%","max-height":"24px","font-size":"18px","font-weight":"500",color:"#222226","line-height":"24px","text-align":"left",display:"block",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}),$("<span id='st_confirm_text'></span>").appendTo("#st_confirm").css({"text-align":"left",height:"44px","font-size":"14px","font-weight":"400",color:"#555666","line-height":"22px",overflow:"hidden",display:"-webkit-box",overflow:"hidden","-webkit-box-orient":"vertical","text-overflow":"ellipsis","-webkit-line-clamp":"2","margin-top":"16px","margin-bottom":"40px"}),$("<span class='st_confirm_btn success'>确定</span>").appendTo("#st_confirm").css({background:" #FC5533",color:"#fff","text-align":"center",display:"block",width:"88px",height:"36px","line-height":"36px","margin-left":"16px","float":"right","border-radius":"18px"}),$("<span class='st_confirm_btn cancel'>取消</span>").appendTo("#st_confirm").css({color:"#222226","text-align":"center",display:"block",width:"88px",height:"36px","line-height":"36px","margin-left":"16px","float":"right","box-sizing":"border-box",border:"1px solid #CCCCD8","border-radius":"18px"}),$("<span id='st_confirm_close'></span>").appendTo("#st_confirm").css({display:"block",width:"12px",height:"12px",position:"absolute","text-align":"center","z-index":"100",top:"24px",right:"24px"}),$("<img src='"+blogStaticHost+"dist/pc/img/closeBt.png'>").appendTo("#st_confirm_close").css({display:"block",width:"12px",height:"12px"}),$(".st_confirm_btn.cancel").mouseover(function(){$(".st_confirm_btn.cancel").css({border:"1px solid #555666",cursor:"pointer"})}).mouseout(function(){$(".st_confirm_btn.cancel").css({border:"1px solid #CCCCD8"})}),$(".st_confirm_btn.success").mouseover(function(){$(".st_confirm_btn.success").css({background:"#FC1944",cursor:"pointer"})}).mouseout(function(){$(".st_confirm_btn.success").css({background:" #FC5533"})}),$("#st_confirm_close").hover(function(){$("#st_confirm_close").css({cursor:"pointer"})}),$("<div></div>").appendTo("#st_confirm").css({clear:"both",display:"block"}),$("<div id='st_alertBox'></div>").appendTo("body").css({width:"100%",position:"fixed",left:"0",top:"34%","text-align":"center",display:"none","z-index":"2"}),$("<div id='st_alert'></div>").appendTo("#st_alertBox").css({width:"80%",margin:"0 auto",background:"#fff","border-radius":"2px",overflow:"hidden","padding-top":"20px","text-align":"center"}),$("<span id='st_alert_text'></span>").appendTo("#st_alert").css({background:"#fff",overflow:"hidden","padding-top":"20px","text-align":"center",display:"block",padding:"15px 8px 30px"}),$("<span id='st_alert_btn' onclick='closeMask()'></span>").appendTo("#st_alert").css({background:"#1b79f8",color:"#fff",padding:"8px","text-align":"center",display:"block",width:"72%",margin:"0 auto","margin-bottom":"20px","border-radius":"2px",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}),$("<div id='st_toastBox'></div>").appendTo("body").css({width:"100%",position:"fixed",left:"0",bottom:"10%","text-align":"center",display:"none"}),$("<span id='st_toastContent'></span>").appendTo("#st_toastBox").css({color:"#fff",background:"rgba(0,0,0,.8)",padding:"8px 24px","border-radius":"4px","max-width":"80%",display:"inline-block","font-size":"16px"})}function showToast(t){if(!t.text&&!t.html)return!1;clearTimeout(st_timer),$("#st_toastBox").hide();var i=t.text,e=parseInt(t.time?t.time:2300),s=t.speed?t.speed:"normal",n=t.bottom?t.bottom:"10%";if(t.zindex){var o=parseInt(t.zindex);$("#st_mask").css({"z-index":o-1}),$("#st_toastBox").css({"z-index":o})}else $("#st_mask").css({"z-index":1}),$("#st_toastBox").css({"z-index":2});$("#st_toastBox").css({bottom:n}),i?$("#st_toastContent").text(i):t.html&&$("#st_toastContent").html(t.html),$("#st_mask").fadeIn(s,function(){$("#st_toastBox").fadeIn(s)}),st_timer=setTimeout(function(){$("#st_toastBox").fadeOut(function(){$("#st_mask").fadeOut()})},e)}function showAlert(t){if(!t.text)return!1;var i=t.text,e=(t.tit,t.bgColor?t.bgColor:"#1b79f8"),s=t.color?t.color:"#fff",n=t.btnText?t.btnText:"确定",o=t.top?t.top:"34%";if(t.zindex){var a=parseInt(t.zindex);$("#st_mask").css({"z-index":a-1}),$("#st_alertBox").css({"z-index":a})}else $("#st_mask").css({"z-index":1}),$("#st_alertBox").css({"z-index":2});$("#st_alert_text").text(i),$("#st_alert_text").text(i),$("#st_alert_btn").css({background:e}),$("#st_alert_btn").css({color:s}),$("#st_alert_btn").text(n),$("#st_alertBox").css({top:o}),$("#st_mask,#st_alertBox").show(),t.success&&$("#st_alert_btn").off("click").on("click",function(){t.success()})}function showConfirm(t){if(!t.text)return!1;if(t.zindex){var i=parseInt(t.zindex);$("#st_mask").css({"z-index":i-1}),$("#st_confirmBox").css({"z-index":i})}else $("#st_mask").css({"z-index":1}),$("#st_confirmBox").css({"z-index":2});t.isBlack&&($("#st_confirmBox #st_confirm").css({background:"#2E2E32"}),$("#st_confirmBox #st_confirm_tit").css({color:"#D3D3D3"}),$("#st_confirmBox #st_confirm_text").css({color:"#999999"}),$("#st_confirmBox .st_confirm_btn.cancel").css({border:"1px solid #555666",color:"#CCCCD8"}),$("#st_confirmBox .st_confirm_btn.cancel").mouseover(function(){$("#st_confirmBox .st_confirm_btn.cancel").css({border:"1px solid #ccccd8",cursor:"pointer"})}).mouseout(function(){$("#st_confirmBox .st_confirm_btn.cancel").css({border:"1px solid #555666"})})),$("#st_confirm_tit").text(t.tit),$("#st_confirm_text").text(t.text),$(".st_confirm_btn.success").text(t.rightText?t.rightText:"确定"),$("#st_mask,#st_confirmBox").show(),$("#st_confirm_close").off("click").on("click",function(){closeMask(),t.cancel()}),t.cancel?$(".st_confirm_btn.cancel").off("click").on("click",function(){closeMask(),t.cancel()}):$(".st_confirm_btn.cancel").off("click").on("click",function(){closeMask()}),t.success?$(".st_confirm_btn.success").off("click").on("click",function(){closeMask(),t.success()}):$(".st_confirm_btn.success").off("click").on("click",function(){closeMask()})}function closeMask(){$("#st_mask,#st_alertBox,#st_confirmBox").hide()}function getCookie(t){for(var i=document.cookie.split("; "),e=0;e<i.length;e++){var s=i[e].split("=");if(t==s[0])return s[1]}return null}function setCookieBaseHour(t,i,e){var s=new Date;s.setTime(s.getTime()+36e5*e),document.cookie=t+"="+escape(i)+";expires="+s.toGMTString()+";domain=.csdn.net;path=/"}function toolbarIsSuspension(){return!!window.csdn.configuration_tool_parameterv}function actionNumberFormat(t){if(t>=1e5)return"10w+";if(t>=1e4){var i=Math.floor(t/1e4),e=t%1e4;return e<999?i+"w":i+"."+parseInt(e/1e3)+"w"}if(t>=1e3){var s=Math.floor(t/1e3),e=t%1e3;return e<99?s+"k":s+"."+parseInt(e/100)+"k"}return t<0?"0":t.toString()}$(function(){var t=$("div.box-box-default"),i=$("div.box-box-large");boxAways=$("div.box-box-aways"),t.find("a.btn-remove").click(function(){t.remove()}),i.find("a.btn-remove").click(function(){i.remove()}),boxAways.find("a.btn-remove").click(function(){boxAways.remove()})}),window.addLoadEvent=window.addLoadEvent?window.addLoadEvent:function(t){var i=window.onload;"function"!=typeof window.onload?window.onload=t:window.onload=function(){i(),t()}},$(function(){function t(){}function i(t){$(t).text("点开这里查看关注").fadeIn(),setTimeout(function(){$(t).fadeOut()},3e3)}t.prototype.animation=function(){if(!currentUserName)return!1;var t=$(this).offset(),e=$(".loginCenter").offset().top,s=$(".loginCenter").offset().left+20,n=t.left+80,o=t.top+20,a=$("#pointDivs .point-pre").first().removeClass("point-pre").css({left:n+"px",top:o+"px"}),r=a.find(".point-inner");setTimeout(function(){a[0].style.webkitTransform="translate3d(0,"+(e-o)+"px,0)",r[0].style.webkitTransform="translate3d("+(s-n)+"px,0,0)",setTimeout(function(){a.removeAttr("style").addClass("point-pre"),r.removeAttr("style"),i(".guo_tip_box")},800)},1)},t.prototype.animationInit=function(){for(var t=$('<div id="pointDivs"></div>').appendTo("body"),i=0;i<5;i++)$('<div class="point-outer point-pre"><div class="point-inner"/></div></div>').appendTo(t)},window.CSDNAnimation=t}),$(function(){function t(){return window.devicePixelRatio>1}function i(t,i){$("span.blog-expert-button-follow").each(function(e){t?$(this).attr("data-name")==i&&$(this).html('<span class="hover-hide">已关注</span><span class="hover-show">取消</span>').removeClass("btn-red-follow").addClass("btn-gray-follow attented"):$(this).attr("data-name")==i&&$(this).html("关注").addClass("btn-red-follow").removeClass("btn-gray-follow attented")}),i==$("p.csdn-tracking-statistics").attr("username")&&(t?($("#btnAttent").addClass("attented").text("已关注").removeClass("btn-red-hollow").addClass("btn-gray-hollow"),e(1)):($("#btnAttent").text("关注").addClass("btn-red-hollow").removeClass("btn-gray-hollow attented"),e(-1)))}function e(t){$("#fan").text().indexOf("+")<0?$("#fan").text(parseInt($("#fan").text())+t):$("#fanBox").attr("title",parseInt($("#fanBox").attr("title"))+t)}!function(){$(".medal-img").on("click",function(){window.csdn=window.csdn?window.csdn:{},window.csdn.userMedal.show({username:username,nickname:nickName,avatar:avatar})})}();var s=$("#search-blog-words"),n=$(".btn-search-blog");s.on("focus",function(){n.addClass("btn-search-blog-active"),n.find("img").attr("src","https://csdnimg.cn/cdn/content-toolbar/csdn-white-search.png?v=1587006908"),s.attr("placeholder","")}),s.on("blur",s,function(t){n.removeClass("btn-search-blog-active"),"Black"==skinStatus?n.find("img").attr("src","https://csdnimg.cn/cdn/content-toolbar/csdn-white-search.png?v=1587006908"):n.find("img").attr("src","https://csdnimg.cn/cdn/content-toolbar/csdn-sou.png?v=1587021042"),s.attr("placeholder","搜博主文章")}),s.keyup(function(t){var i=t.keyCode;if(13==i){var e=encodeURIComponent(s.val());if(e){var n="//so.csdn.net/so/search/s.do?q="+e+"&t=blog&u="+username;window.open(n)}}}),n.on("click",function(t){var i=encodeURIComponent(s.val());if(i){var e="//so.csdn.net/so/search/s.do?q="+i+"&t=blog&u="+username;window.open(e)}t.preventDefault()}),function(){if(t()){var i=$(".user-years");i.length>0&&(i=i.attr("src").split("/")[$(".user-years").attr("src").split("/").length-1],$(".user-years").attr("src","https://g.csdnimg.cn/static/user-reg-year/2x/"+i))}}(),window.csdn=window.csdn?window.csdn:{},window.csdn.watchBtnChange=i}),(isCorporate||"undefined"==typeof articleId||$(window).width()>0)&&$(document).ready(fixedSidebarInButton),window.leftFixedSide?window.leftFixedSide:window.leftFixedSide={},window.leftFixedSide.fixedSidebarInButton=fixedSidebarInButton,$(function(){function t(){var t=document.cookie.match(new RegExp("(^| )UserName=([^;]*)(;|$)"));return t?t[2]:""}function i(t){if($("#fan").text().indexOf("万")===-1&&$("#fan").text().indexOf("+")===-1){var i=parseInt($("#fan").text())+parseInt(t),e=i>0?i:0;$("#fan").text(e),$("#fanBox").attr("title",e)}var s=$(".fans_read_more");s.length>0&&s.trigger("click")}function e(t,i){var e=t.hasClass("attented");articleDetailUrl?e?n(t,i):s(t,i):getCookie("UserName")?e?n(t,i):s(t,i):window.csdn.loginBox.show()}function s(t,e){window.followByRR&&(a.source="BLOG_DETAIL_COMMENT_ENVELOPE",window.followByRR=!1);var s="https://mp-action.csdn.net/interact/wrapper/pc/fans/v1/api/follow";$.ajax({type:"POST",url:s,contentType:"application/json; charset=utf-8",data:JSON.stringify(a),xhrFields:{withCredentials:!0},success:function(s){s="object"!=typeof s?JSON.parse(s):s,200===s.code?(t.addClass("attented").text("已关注"),i(1),e&&window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"popu_379",spm:"1001.2101.3001.4123",extend1:"关注"})):400105100==s.code&&window.csdn.loginBox.show()},error:function(t){t="object"!=typeof t.responseText?JSON.parse(t.responseText):t.responseText,400105100==t.code&&window.csdn.loginBox.show()}})}function n(t,e){var s="https://mp-action.csdn.net/interact/wrapper/pc/fans/v1/api/unFollow";$.ajax({type:"POST",url:s,contentType:"application/json; charset=utf-8",data:JSON.stringify(a),xhrFields:{withCredentials:!0},success:function(s){200==s.code?(t.text("关注").removeClass("attented"),i(-1),e&&window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"popu_379",spm:"1001.2101.3001.4123",extend1:"已关注"})):400105100==s.code&&window.csdn.loginBox.show()}})}var o=new window.CSDNAnimation;if(o.animationInit(),$(".personal-watch").click(function(t){var i=!1;i=!!t.originalEvent,e($(".personal-watch"),i)}),articleDetailUrl)var a={username:getCookie("UserName"),follow:username,source:"BLOG_DETAIL",fromType:"pc",detailSourceName:articleTitle,sourceId:articleId};else var a={username:getCookie("UserName"),follow:username,source:"ME",fromType:"pc",detailSourceName:"个人主页"};t()}),function(){function t(t){var i=document.createElement("div");void 0!=i.textContent?i.textContent=t:i.innerText=t;var e=i.innerHTML;return i=null,e}function i(t){var i="";return 0==t.length?"":(i=t.replace(/</g,"&lt;"),i=i.replace(/>/g,"&gt;"))}window.csdn?window.csdn:window.csdn={},window.csdn.htmlEncode_inner=t,window.csdn.htmlEncode_Escape=i}(),!function(t,i){"function"==typeof define&&define.amd?define(["jquery"],i):"object"==typeof exports?module.exports=i():t.Query=i(window.Zepto||window.jQuery||$)}(this,function(t){return{getQuery:function(t,e,s){new RegExp("(^|&|#)"+t+"=([^&]*)(&|$|#)","i"),s=s||window;var n,o,a=s.location.href,r="";if(n="#"==e?a.split("#"):a.split("?"),""!=(o=1==n.length?"":n[1])){gg=o.split(/&|#/);var c=gg.length;for(str=arguments[0]+"=",i=0;i<c;i++)if(0==gg[i].indexOf(str)){r=gg[i].replace(str,"");break}}return decodeURI(r)},getForm:function(i){var e={},s={};t(i).find("*[name]").each(function(i,n){var o,a=t(n).attr("name"),r=t.trim(t(n).val()),c=[];if(""!=a&&!t(n).hasClass("getvalued")){if("money"==t(n).data("type")&&(r=r.replace(/\,/gi,"")),"radio"==t(n).attr("type")){var l=null;t("input[name='"+a+"']:radio").each(function(){t(this).is(":checked")&&(l=t.trim(t(this).val()))}),r=l||""}if("checkbox"==t(n).attr("type")){var l=[];t("input[name='"+a+"']:checkbox").each(function(){t(this).is(":checked")&&l.push(t.trim(t(this).val()))}),r=l.length?l.join(","):""}if(t(n).attr("listvalue")&&(e[t(n).attr("listvalue")]||(e[t(n).attr("listvalue")]=[],t("input[listvalue='"+t(n).attr("listvalue")+"']").each(function(){if(""!=t(this).val()){var i=t(this).attr("name"),s={};if("json"==t(this).data("type")?s[i]=JSON.parse(t(this).val()):s[i]=t.trim(t(this).val()),t(this).attr("paramquest")){var o=JSON.parse(t(this).attr("paramquest"));s=t.extend(s,o)}e[t(n).attr("listvalue")].push(s),t(this).addClass("getvalued")}}))),t(n).attr("arrayvalue")&&(e[t(n).attr("arrayvalue")]||(e[t(n).attr("arrayvalue")]=[],t("input[arrayvalue='"+t(n).attr("arrayvalue")+"']").each(function(){if(""!=t(this).val()){var i={};if(i="json"==t(this).data("type")?JSON.parse(t(this).val()):t.trim(t(this).val()),t(this).attr("paramquest")){var s=JSON.parse(t(this).attr("paramquest"));i=t.extend(i,s)}e[t(n).attr("arrayvalue")].push(i)}}))),""!=a&&!t(n).hasClass("getvalued"))if(a.match(/\./)){if(c=a.split("."),o=c[0],3==c.length)s[c[1]]=s[c[1]]||{},s[c[1]][c[2]]=r;else if("json"==t(n).data("type")){if(s[c[1]]=JSON.parse(r),t(n).attr("paramquest")){var d=JSON.parse(t(n).attr("paramquest"));s[c[1]]=t.extend(s[c[1]],d)}}else s[c[1]]=r;e[o]?e[o]=t.extend({},e[o],s):e[o]=s}else e[a]=r}});var n={};for(var o in e){var a=e[o];n[o]="object"==typeof a?JSON.stringify(a):e[o]}return t(".getvalued").removeClass("getvalued"),n},setHash:function(i){var e="";i=t.extend(this.getHash(),i);var s=[];for(var n in i)""!=i[n]&&s.push(n+"="+encodeURIComponent(i[n]));return e+=s.join("&"),location.hash=e,this},getHash:function(t){if("string"==typeof t)return this.getQuery(t,"#");var i={},e=location.hash;if(e.length>0){e=e.substr(1);for(var s=e.split("&"),n=0,o=s.length;n<o;n++){var a=s[n].split("=");a.length>0&&(i[a[0]]=decodeURI(a[1])||"")}}return i}}}),function(t,i){"function"==typeof define&&define.amd?define(["jquery","query"],i):"object"==typeof exports?module.exports=i():t.Paging=i(window.Zepto||window.jQuery||$,Query)}(this,function(t,i){function e(){var t=Math.random().toString().replace(".","");this.id="Paging_"+t}return t.fn.Paging=function(i){var s=[];return t(this).each(function(){var n=t.extend({target:t(this)},i),o=new e;o.init(n),s.push(o)}),s},e.prototype={init:function(i){this.settings=t.extend({callback:null,pagesize:10,current:1,prevTpl:"<",nextTpl:">",firstTpl:"首页",lastTpl:"末页",ellipseTpl:"...",toolbar:!1,hash:!1,pageSizeList:[5,10,15,20]},i),this.target=t(this.settings.target),this.container=t('<div id="'+this.id+'" class="ui-paging-container"/>'),this.target.append(this.container),this.render(this.settings),this.format(),this.bindEvent()},render:function(t){void 0!==t.count?this.count=t.count:this.count=this.settings.count,void 0!==t.pagesize?this.pagesize=t.pagesize:this.pagesize=this.settings.pagesize,void 0!==t.current?this.current=t.current:this.current=this.settings.current,this.pagecount=Math.ceil(this.count/this.pagesize),this.format()},bindEvent:function(){var i=this;this.container.on("click","li.js-page-action,li.ui-pager",function(e){return!t(this).hasClass("ui-pager-disabled")&&!t(this).hasClass("focus")&&(t(this).hasClass("js-page-action")?(t(this).hasClass("js-page-first")&&(i.current=1),t(this).hasClass("js-page-prev")&&(i.current=Math.max(1,i.current-1)),t(this).hasClass("js-page-next")&&(i.current=Math.min(i.pagecount,i.current+1)),t(this).hasClass("js-page-last")&&(i.current=i.pagecount)):t(this).data("page")&&(i.current=parseInt(t(this).data("page"))),void i.go())})},go:function(t){var e=this;this.current=t||this.current,this.current=Math.max(1,e.current),this.current=Math.min(this.current,e.pagecount),this.format(),this.settings.hash&&i.setHash({page:this.current}),this.settings.callback&&this.settings.callback(this.current,this.pagesize,this.pagecount)},changePagesize:function(t){this.render({pagesize:t}),this.settings.changePagesize&&this.settings.changePagesize.call(this,this.pagesize,this.current,this.pagecount)},format:function(){var i="<ul>";if(i+='<li class="js-page-first js-page-action ui-pager" >'+this.settings.firstTpl+"</li>",i+='<li class="js-page-prev js-page-action ui-pager">'+this.settings.prevTpl+"</li>",this.pagecount>6){if(i+='<li data-page="1" class="ui-pager">1</li>',this.current<=2)i+='<li data-page="2" class="ui-pager">2</li>',i+='<li data-page="3" class="ui-pager">3</li>',i+='<li class="ui-paging-ellipse">'+this.settings.ellipseTpl+"</li>";else if(this.current>2&&this.current<=this.pagecount-2)this.current>3&&(i+="<li>"+this.settings.ellipseTpl+"</li>"),i+='<li data-page="'+(this.current-1)+'" class="ui-pager">'+(this.current-1)+"</li>",i+='<li data-page="'+this.current+'" class="ui-pager">'+this.current+"</li>",i+='<li data-page="'+(this.current+1)+'" class="ui-pager">'+(this.current+1)+"</li>",this.current<this.pagecount-2&&(i+='<li class="ui-paging-ellipse" class="ui-pager">'+this.settings.ellipseTpl+"</li>");else{i+='<li class="ui-paging-ellipse" >'+this.settings.ellipseTpl+"</li>";for(var e=this.pagecount-2;e<this.pagecount;e++)i+='<li data-page="'+e+'" class="ui-pager">'+e+"</li>"}i+='<li data-page="'+this.pagecount+'" class="ui-pager">'+this.pagecount+"</li>"}else for(var e=1;e<=this.pagecount;e++)i+='<li data-page="'+e+'" class="ui-pager">'+e+"</li>";i+='<li class="js-page-next js-page-action ui-pager">'+this.settings.nextTpl+"</li>",i+='<li class="js-page-last js-page-action ui-pager">'+this.settings.lastTpl+"</li>",i+="</ul>",this.container.html(i),1==this.current&&(t(".js-page-prev",this.container).addClass("ui-pager-disabled"),t(".js-page-first",this.container).addClass("ui-pager-disabled")),this.current==this.pagecount&&(t(".js-page-next",this.container).addClass("ui-pager-disabled"),t(".js-page-last",this.container).addClass("ui-pager-disabled")),this.container.find('li[data-page="'+this.current+'"]').addClass("focus").siblings().removeClass("focus"),this.settings.toolbar&&this.bindToolbar()},bindToolbar:function(){for(var i=this,e=t('<li class="ui-paging-toolbar"><select class="ui-select-pagesize"></select><input type="text" class="ui-paging-count"/><a href="javascript:void(0)">跳转</a></li>'),s=t(".ui-select-pagesize",e),n="",o=0,a=this.settings.pageSizeList.length;o<a;o++)n+='<option value="'+this.settings.pageSizeList[o]+'">'+this.settings.pageSizeList[o]+"条/页</option>";s.html(n),s.val(this.pagesize),t("input",e).val(this.current),t("input",e).click(function(){t(this).select()}).keydown(function(e){if(13==e.keyCode){var s=parseInt(t(this).val())||1;i.go(s)}}),t("a",e).click(function(){var e=parseInt(t(this).prev().val())||1;i.go(e)}),s.change(function(){i.changePagesize(t(this).val())}),this.container.children("ul").append(e)}},e}),function(t,i){function e(t,e,s,n){i.post(t,e,s,n)}window.csdn?window.csdn:window.csdn={},window.csdn.post=e}(window,jQuery),$(function(){function t(t,i){var e=qrcode(6,"M");e.addData(i),e.make(),t.html(e.createImgTag(3,3)),t.html(t.html())}var i=$("#btShareColumn"),e=$("#ShareColumnCode"),s=$(".share-column-box");i.on("click",function(){var i=$(this).data("url");t(e,i),s.show()}),$(document).click(function(t){i.is(t.target)||0!==i.has(t.target).length||s.hide()})});var st_timer=null;$(function(){showInit()}),$(function(){function t(t,i,e){$.ajax({url:t,type:"POST",data:i,xhrFields:{withCredentials:!0},success:function(t){if(s.skinBoxshadow.html(""),200==t.code){var i='<div class="skin-success skin-tip skin-massage">          <svg t="1513153231313" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4182" id="mx_n_1513153231314" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m244.8 392L467.2 691.2c-8 9.6-24 12.8-36.8 12.8-12.8 0-27.2-3.2-36.8-12.8L267.2 560c-16-16-16-43.2 0-59.2s41.6-16 57.6 0l105.6 110.4 267.2-278.4c16-16 41.6-16 57.6 0s16 43.2 1.6 59.2z" p-id="4183" fill=""></path></svg>          <span>保存成功</span>          </div>';s.skinBoxshadow.append(i),s.skinBoxshadow.fadeIn(200),setTimeout(function(){s.skinBoxshadow.fadeOut(200),s.skinTipbox.slideUp(200),""!==e&&(window.location.href=e.window_url)},1e3)}else if(400200002==t.code){var n='<div class="skin-vip skin-adopt skin-massage">          <p class="skin-title">          <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>          <span>你还不是VIP会员</span></p>          <p class="skin-text">本皮肤为VIP用户可用哦，请您开通VIP会员后再试！</p>          <a href="https://mall.csdn.net/vip?utm_source=skinTemplateVip" class="adopt-vip" target="_blank">              <img class="isvip" src="'+blogStaticHost+'dist/pc/img/crown.png" >              <span>开通VIP会员</span>          </a>          <div class="skin-bt-close"><svg t="1567152543821" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10924" width="200" height="200"><path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="10925"></path></svg></div>        </div>';s.skinBoxshadow.append(n),s.skinBoxshadow.fadeIn(200),$(".skin-bt-close").on("click",function(){s.skinBoxshadow.fadeOut(200)})}else if(400200003==t.code){var o='<div class="grade-vip skin-adopt skin-massage">          <p class="skin-title">          <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>          <span>'+t.message+'</span>          <p class="skin-text">开通VIP会员，即可专享所有皮肤哦～！</p>          <a href="https://mall.csdn.net/vip?utm_source=skinTemplateVip" class="adopt-vip" target="_blank">              <img class="isvip" src="'+blogStaticHost+'dist/pc/img/crown.png" >              <span>开通VIP会员</span>          </a>          <div class="skin-bt-close"><svg t="1567152543821" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10924" width="200" height="200"><path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="10925"></path></svg></div>        </div>';s.skinBoxshadow.append(o),s.skinBoxshadow.fadeIn(200),$(".skin-bt-close").on("click",function(){s.skinBoxshadow.fadeOut(200)})}else{var a='<div class="skin-error skin-tip skin-massage">          <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>          <span>'+t.message+"</span>          </div>";s.skinBoxshadow.append(a),s.skinBoxshadow.fadeIn(200),setTimeout(function(){s.skinBoxshadow.fadeOut(200)},1e3)}},error:function(t){s.skinBoxshadow.html("");var i='<div class="skin-error skin-tip skin-massage">        <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>        <span>保存失败！请重新保存</span>        </div>';s.skinBoxshadow.append(i),s.skinBoxshadow.fadeIn(200),setTimeout(function(){s.skinBoxshadow.fadeOut(200)},1e3)}})}function i(){var t,i,e=window.location.href;return e.indexOf("?assign_skin_id")!=-1?(i=e.split("?")[0],t=e.split("?")[1].split("=")[1]):i=e,t=parseInt(t),{window_url:i,skin_id:t}}function e(){function t(){c={img_title:s.userSkin.find("#uploadHeaderimg").attr("value"),img_back:s.userSkin.find("#uploadBgimg").attr("value"),img_column:s.userSkin.find("#uploadColumnimg").attr("value"),color_title:s.userSkin.find("#topicColor").val(),color_title_hover:s.userSkin.find("#topicHoverColor").val(),color_sub_title:s.userSkin.find("#subTopicColor").val()};var t="^#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})$",i=new RegExp(t);return""===c.img_title?(o("头图必传",!1),!1):!(""!==c.color_title&&!i.test(c.color_title))||(o("请填写正确的颜色值",!1),!1)}function e(){s.userSkin.find("input").val(""),$("#uploadHeaderimg").value="",$("#uploadBgimg").value="",$("#uploadColumnimg").value="",s.userSkin.find(".upload-icon").show(),s.userSkin.find(".success-upload-icon").hide(),s.userSkin.find(".tip-massage").html("点击选择上传的图片"),s.userSkin.fadeOut(200)}function n(t,i,e){var n=blogUrl+"phoenix/web/v1/"+t;$.ajax({url:n,type:"POST",data:i,xhrFields:{withCredentials:!0},success:function(i){setTimeout(function(){if(200==i.code)"preview-skin"===t?!!e&&e(!0,i.data?i.data:"操作成功"):!!e&&e(!0,i.message?i.message:"操作成功");else{var n='<div class="skin-vip skin-adopt skin-massage">                <p class="skin-title">                <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>                <span>你还不是VIP会员</span></p>                <p class="skin-text">本功能为VIP用户可用哦，请您开通VIP会员后再试！</p>                <a href="https://mall.csdn.net/vip?utm_source=skinTemplateVip" class="adopt-vip" target="_blank">                    <img class="isvip" src="'+blogStaticHost+'dist/pc/img/crown.png" >                    <span>开通VIP会员</span>                </a>                <div class="skin-bt-close"><svg t="1567152543821" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="10924" width="200" height="200"><path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="10925"></path></svg></div>                </div>';s.skinBoxshadow.append(n),s.skinBoxshadow.fadeIn(200),
$(".skin-bt-close").on("click",function(){s.skinBoxshadow.fadeOut(200),s.skinBoxshadow.html("")})}},2e3)},error:function(t){o("系统错误，请重新上传",!1)}})}function o(t,i){s.skinBoxshadow.html("");var e="";e=i?'<div class="skin-success skin-tip skin-massage">        <svg t="1513153231313" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4182" id="mx_n_1513153231314" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m244.8 392L467.2 691.2c-8 9.6-24 12.8-36.8 12.8-12.8 0-27.2-3.2-36.8-12.8L267.2 560c-16-16-16-43.2 0-59.2s41.6-16 57.6 0l105.6 110.4 267.2-278.4c16-16 41.6-16 57.6 0s16 43.2 1.6 59.2z" p-id="4183" fill=""></path></svg>        <span>'+t+"</span>        </div>":'<div class="skin-error skin-tip skin-massage">        <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>        <span>'+t+"</span>        </div>",s.skinBoxshadow.append(e),s.skinBoxshadow.fadeIn(200),setTimeout(function(){s.skinBoxshadow.fadeOut(200)},1e3)}function a(t){var i=null;return void 0!=window.createObjectURL?i=window.createObjectURL(t):void 0!=window.URL?i=window.URL.createObjectURL(t):void 0!=window.webkitURL&&(i=window.webkitURL.createObjectURL(t)),i}function r(t){s.cropBox.find("#cropImg").cropper({aspectRatio:t,viewMode:2,dragMode:"crop",preview:".small",responsive:!0,restore:!1,modal:!0,guides:!0,center:!0,highlight:!0,background:!0,autoCrop:!0,autoCropArea:1,movable:!0,scalable:!1,zoomable:!0,rotatable:!1,preview:document.querySelectorAll(".final-img")})}$(".customize").click(function(){s.userSkin.fadeIn(200)});var c={img_title:"",img_back:"",img_column:"",color_title:"",color_title_hover:"",color_sub_title:""};s.userSkin.find(".bt-back").click(function(){e()}),s.userSkin.find(".user-skin-refer").click(function(){window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){if(t()){var e=function(t,e){t&&(o(e,!0),setTimeout(function(){i();window.location.href=blog_address},200))};n("save-custom-skin",c,e)}}})}),s.userSkin.find(".user-skin-preview").click(function(){if(t()){var i=function(t,i){t&&window.open(i)};n("preview-skin",c,i)}});var l;s.uploadSkinImg.change(function(t){var i=$(this)[0].files[0],e=i.type,n=i.size/1024;if(!/.(jpg|jpeg|png)$/.test(e))return o("请上传jpg、png格式图片",!1),!1;var c=a(i);switch(s.cropBox.find(".crop-img-before img").attr("src",c),l=$(this)[0].id,s.cropBox.find("#cropImg").cropper("destroy"),l){case"uploadHeaderimg":if(r(19.2),2048<n)return void o("请选择2M以内的图片",!1);s.cropBox.find("#cropImg").cropper("setCropBoxData",{width:1920,height:100});break;case"uploadBgimg":if(1024<n)return void o("请选择1M以内的图片",!1);r(1),s.cropBox.find("#cropImg").cropper("setCropBoxData",{width:100,height:100});break;case"uploadColumnimg":if(512<n)return void o("请选择0.5M以内的图片",!1);r(300/38),s.cropBox.find("#cropImg").cropper("setCropBoxData",{width:300,height:38})}s.cropBox.fadeIn(200),$(this)[0].value=""}),s.cropBox.find(".bt-back").click(function(){s.cropBox.fadeOut(200),s.cropBox.find("#cropImg").cropper("destroy")}),s.cropBox.find(".bt-next").click(function(){var t="",i="",e="";switch(l){case"uploadHeaderimg":t=1920,i=100,e="img_title";break;case"uploadBgimg":t=100,i=100,e="img_back";break;case"uploadColumnimg":t=300,i=38,e="img_column"}s.cropBox.find("#cropImg").cropper("getCroppedCanvas",{width:t,height:i,minWidth:t,minHeight:i,maxWidth:t,maxHeight:i}).toBlob(function(t){var i=new FormData;i.append("file",t),i.append("img_type",e),$.ajax("/pheapi/skin/skin_upload_img",{method:"POST",data:i,processData:!1,contentType:!1,xhrFields:{withCredentials:!0},success:function(t){if(t.result){var i=$("#"+l);switch(i.attr("value",t.url),l){case"upload-headerimg":c.img_title=i.val();break;case"upload-bgimg":c.img_back=i.val();break;case"upload-columnimg":c.img_column=i.val()}i.siblings(".upload-img").find(".upload-icon").hide(),i.siblings(".upload-img").find(".success-upload-icon").show(),i.siblings(".upload-img").find(".tip-massage").html("点击重新上传"),o("上传成功",!0),setTimeout(function(){s.cropBox.fadeOut(200)},1e3)}else o(t.content,!1),setTimeout(function(){s.cropBox.fadeOut(200)},1e3)},error:function(t){o("系统错误，请重新上传",!1),setTimeout(function(){s.cropBox.fadeOut(200)},1e3)}})},"image/png")}),s.cropBox.find(".bt-reduce").click(function(){s.cropBox.find("#cropImg").cropper("zoom",-.1)}),s.cropBox.find(".bt-add").click(function(){s.cropBox.find("#cropImg").cropper("zoom",.1)})}var s={skikTemplateBox:$(".skin-template-box"),skinTemplateItem:$(".skin-template-item"),skinList:$(".skin-list"),skinListShow:$(".skin-list-show"),skinItem:$(".skin-item"),skinTipbox:$("#skinTipbox"),skinChange:$("#skinChange"),meUser:$("#meUser"),skinBoxshadow:$(".skin-boxshadow"),userSkin:$("#userSkin"),cropBox:$("#cropBox"),uploadSkinImg:$(".upload-skin-img")};s.skinChange.on("click",function(){s.skikTemplateBox.fadeIn(200)}),$(document).mouseup(function(t){var i=s.skikTemplateBox;i.is(t.target)||0!==i.has(t.target).length||s.skikTemplateBox.fadeOut(200)}),s.skinTemplateItem.on("click",function(){var t=$(this).index();t<2?($(this).siblings().each(function(t,i){$(i).find("img").eq(1).removeClass("changed"),$(i).find("img").eq(0).addClass("changed")}),$(this).find("img").eq(0).removeClass("changed"),$(this).find("img").eq(1).addClass("changed"),s.skinList.hide(),s.skinList.eq(t).show()):(s.skikTemplateBox.fadeOut(200),s.userSkin.fadeIn(200))}),s.skinItem.on("click",function(){s.skinListShow.find("li").removeClass("skin-item-act"),$(this).addClass("skin-item-act"),s.skikTemplateBox.fadeOut(200)}),s.skinTipbox.on("click",".skin-exit",function(){s.skinTipbox.slideUp(200);var t=i();setTimeout(function(){window.location.href=t.window_url},200)}),s.skinTipbox.on("click",".skin-save",function(){var e=i(),s=blogUrl+"phoenix/web/v1/use-skin",n={skin_id:e.skin_id};t(s,n,e)}),e()}),window.csdn.configuration_tool_parameterv&&window.csdn.configuration_tool_parameterv({need_first_suspend:!0,need_little_suspend:!1,little_tool_id:"",little_need_insert_type:""}),$(function(){$.ajaxSetup({type:"POST",error:function(t,i,e){401==t.status&&(window.location="https://passport.csdn.net/account/login")},success:function(t){}})});