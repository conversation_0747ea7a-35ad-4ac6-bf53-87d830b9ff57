"use strict";function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var _slicedToArray=function(){function t(t,e){var i=[],o=!0,n=!1,s=void 0;try{for(var a,r=t[Symbol.iterator]();!(o=(a=r.next()).done)&&(i.push(a.value),!e||i.length!==e);o=!0);}catch(t){n=!0,s=t}finally{try{!o&&r.return&&r.return()}finally{if(n)throw s}}return i}return function(e,i){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return t(e,i);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),_createClass=function(){function t(t,e){for(var i=0;i<e.length;i++){var o=e[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,i,o){return i&&t(e.prototype,i),o&&t(e,o),e}}();!function(){function t(t){for(var e=document.cookie.split("; "),i=0;i<e.length;i++){var o=e[i].split("=");if(o[0]==t)return decodeURIComponent(o[1])}}function e(t){window.csdn&&window.csdn.report&&window.csdn.report.reportClick(t)}function i(t){window.csdn&&window.csdn.report&&window.csdn.report.reportView(t)}function o(){var t=/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone|csdn)/i.test(navigator.userAgent);return/(MicroMessenger)/i.test(navigator.userAgent)?!/(WindowsWechat|MacWechat)/i.test(navigator.userAgent):t}function n(){var t=/micromessenger/.test(navigator.userAgent.toLowerCase()),e=/wxwork/.test(navigator.userAgent.toLowerCase());if("undefined"!=typeof WeixinJSBridge||t)return!e}function s(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{toUn:""};this.app=h(),this.isMobile=o(),this.cb=void 0,this.payInfo=void 0,this.reloadOnClose=!1,this.closeOnClickMask=!0,this.showClose=!0,this.loginTip="",this.reportExtra={autoPopup:!1,pageType:""},t&&t.tip&&(this.loginTip=t.tip,delete t.tip),t&&t.reportExtra&&(this.reportExtra=Object.assign({},this.reportExtra,t.reportExtra),delete t.reportExtra),t&&t.hasOwnProperty("closeOnClickMask")&&(this.closeOnClickMask=!!t.closeOnClickMask,delete t.closeOnClickMask),t&&t.hasOwnProperty("showClose")&&(this.showClose=!!t.showClose,delete t.showClose),t&&t.cb&&(this.cb=t.cb),t&&t.biz&&"pay"===t.biz&&t.payInfo&&(this.payInfo=t.payInfo),this.cb&&(delete t.cb,t.hascb="yes"),this.payInfo&&delete t.payInfo,this.inputData=t;var e=410,i=534,n="";this.isMobile?(e=t.toUn?335:343,i=t.toUn?445:410,n=t.toUn?"https://passport.csdn.net/waploginv4":"https://passport.csdn.net/waplogin"):n=t.toUn?"https://passport.csdn.net/loginv4":"https://passport.csdn.net/account/login",this.defaultParams={domain:"csdn.net",isIframe:!0,frameWidth:e,frameHeight:i,append:"#passportbox",iframeName:"passport_iframe",from:encodeURIComponent(window.location.href),pvSource:"",service:"",loginService:n};var s=r&&r.spm?r.spm:"",a=t&&t.spm?t.spm:"",p=a||s;p&&(this.inputData=this.inputData||{},this.inputData.spm=m(p)),this.options=Object.assign({},this.defaultParams,this.inputData),this.extend="",this.version=this.isMobile?"popupv1":"loginv3",this.renderCss(),this.fileExtends(),this.init(this.options)}var a=null,r={},p=null;if(window.csdn=window.csdn||{},window.csdn&&window.csdn.loginBox&&window.csdn.loginBox.show)return void void 0;var l=function(t,e,i){var o=new Date;if(i)"number"==typeof i?o.setTime(o.getTime()+i):o=new Date(o.getFullYear(),o.getMonth(),o.getDate()+1,0,0,0);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+encodeURIComponent(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"},c=function(t){var e=/([^?#*&=]+)=([^?#*&=]+)/g,i={};return location.href.replace(e,function(){for(var t=arguments.length,e=Array(t),o=0;o<t;o++)e[o]=arguments[o];var n=e[1],s=e[2];i[n]=s}),i[t]},h=function(){return navigator.userAgent.toLowerCase().indexOf("csdn")>-1},d=function(){if(h()){var t=navigator.userAgent.toLowerCase(),e=JSON.stringify({url:"csdnapp://app.csdn.net/login/quick"});/iphone|ipad|ipod|ios/i.test(t)?window.webkit.messageHandlers.csdnjumpnewpage.postMessage(e):window.jsCallBackListener.csdnjumpnewpage(e)}},u=function(){if(h()){/iphone|ipad|ipod|ios/i.test(navigator.userAgent.toLowerCase())?window.webkit.messageHandlers.csdnLogOut.postMessage(null):window.jsCallBackListener.csdnLogOut()}},m=function(t){t=String(t);var e=t.split(".").length;if(2===e||3===e){var i=document.querySelector('meta[name="report"]'),o=i&&i.getAttribute("content")||"{}",n=JSON.parse(o);return n.spm?n.spm+"."+t:t}return t};s.prototype.init=function(t){if(this.app)return window.csdn.loginBox.self=void 0,d(),!1;this.wapDistribute(t)},s.prototype.fileExtends=function(){for(var t in this.inputData)this.defaultParams.hasOwnProperty(t)||(this.extend=this.extend+"&"+t+"="+this.inputData[t])},s.prototype.renderCss=function(){var t=window.document.head,e=t.firstElementChild||t.firstChild,i=document.createElement("style");i.innerText=".passport-login-container{position: fixed;top: 0;left: 0;z-index: 9999;width: 100%;height: 100%;}.passport-login-box{position: absolute;display: block;border-radius: 8px;left: 50%;top: 50%;z-index: 10001;-webkit-transform: translate(-50%, -50%);-ms-transform: translate(-50%, -50%);-o-transform: translate(-50%, -50%);-moz-transform: translate(-50%, -50%);transform: translate(-50%, -50%);background-color: #fff;}.passport-login-mark{position: absolute;top: 0;left: 0;z-index: 9999;background-color: rgba(0, 0, 0, 0.5);width: 100%;height: 100%;}",t.insertBefore(i,e)},s.prototype.renderHtml=function(t){var e=this,o=this.options.loginService,n=this.options.frameWidth,s=this.options.frameHeight;if(this.$loginDom=$('<div class="passport-login-container"><div id='+this.options.append.replace(/[#\.]/,"")+' class="passport-login-box" style="display: block;'+(s?"height:"+s+"px":"")+'"></div></div>'),this.$markDom=$('<div class="passport-login-mark"></div>'),window.document.domain=this.options.domain,o=o+(-1===o.indexOf("?")?"?from=":"&from=")+this.options.from,o=this.options.service?o+"&service="+this.options.service:o,o+="&iframe=true",o+="&newframe=true",o=o+"&parentWidth="+document.documentElement.clientWidth,o=this.options.pvSource?o+"&"+this.options.pvSource:o,o=this.version?o+"&version="+this.version:o,o=this.extend?o+this.extend:o,this.$iframeHtml=$('<iframe  width="'+n+'" height="'+s+'" name="'+this.options.iframeName+'" src="'+o+'" style="border-radius: 8px;" frameborder="0" scrolling="no"></iframe>'),t.toUn){this.$closeBtn=$('<img style="position: absolute;top: 14px;left: -16px;" src=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAOVJREFUOE+lk+8KgjAUxfdP3ySQigxxEb5N9Az5EOor1Ps0P5bhqyi4GyNma25CuG+D3d/OPfdcjBYevLAejQAh2hPGsuZ8/ZyDCtFsMKZHzqOrejcC6vq1RYjkCMnCB1HFhLA8CIYyjj8f/bQwB1HFjLFcyq5K091Dq5x4oCAY0wvAUGolvuKJAk01IQAArp+9CmyIuvd9V2XZV7ZpsneM2jD12GzHnpATYPYMwMD2ZFaByzCXsU4P5tz2QYwkuudsytUQxkix368aK4ntOQzlXSfMF2cFoZQckiS6eXPwz4It3sY33W+aEYevsiYAAAAASUVORK5CYII= />')}else this.$closeBtn=$('<span style="display: inline-block; color: #999; font-size: 22px; cursor: pointer; position:absolute; top:2%; right:3%;-moz-user-select:none; -webkit-user-select:none; user-select:none;">&times</span>');this.$closeBtn.on("click",function(){e.close()}),this.$loginDom.append(this.$markDom),$("body").append(this.$loginDom),$(this.options.append).append(this.$iframeHtml);var a=this;this.loginTip&&window.addEventListener("message",function(t){"login_onload"===t.data&&a.$iframeHtml[0].contentWindow.postMessage("JSON_"+JSON.stringify({data:a.loginTip,name:"setLoginTip"}),"*")}),this.showClose&&$(this.options.append).append(this.$closeBtn);var r={};return this.reportExtra&&(r.extra=JSON.stringify(this.reportExtra)),this.inputData.spm&&(r.spm=this.inputData.spm),i(r),!0},s.prototype.changeCloseReLoadSwitch=function(t){this.reloadOnClose=!!t},s.prototype.close=function(){var t={spm:"3001.6428"};this.reportExtra&&(t.extra=JSON.stringify(this.reportExtra)),e(t),this.$loginDom.remove(),this.reloadOnClose&&window.location.reload(),window.csdn.loginBox.self=void 0},s.prototype.getVersion=function(t){var e=this;$.ajax({type:"get",url:"https://passport.csdn.net/v1/register/pc/iframe/login/version",crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(i){i.status?(e.options.frameWidth=i.data.width,e.options.frameHeight=i.data.height,e.version=i.data.controlVersion,e.renderHtml(t)):e.renderHtml(t)},error:function(i){e.renderHtml(t)}})},s.prototype.wapDistribute=function(t){var e=this,i={platform:e.isMobile?"WAP":"PC",source:e.options.pvSource,spm:e.options.spm};this.reportExtra.popupType&&(i.popupType=this.reportExtra.popupType),$.ajax({type:"get",url:"https://passport.csdn.net/v1/login/distribute/login/route?from="+e.options.from,data:i,crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(i){if(i.status){if(e.reportExtra.pageType=i.data.popup?"popup":"page",!i.data.popup)return window.csdn.loginBox.self=void 0,window.location.href=i.data.routeUrl,!1;t.toUn||(e.options.frameWidth=i.data.width,e.options.frameHeight=i.data.height,e.version=i.data.version),e.isMobile&&i.data&&i.data.img&&(e.options.loginService=e.options.loginService+"?popimg="+i.data.img),e.renderHtml(t)}else e.renderHtml(t)},error:function(i){e.renderHtml(t)}})};var f=function(){function e(t){_classCallCheck(this,e),this.callBackFn=t&&t.cb||null,this.errorFn=t&&t.error||null,t&&delete t.cb,t&&delete t.error,this.biz="",this.subBiz="",t&&t.biz&&(this.biz=t.biz,delete t.biz),t&&t.subBiz&&(this.subBiz=t.subBiz,delete t.subBiz),this.app=h(),this.isMobile=o(),this.inputData=t,this.defaultParams={status:"activate",domain:"csdn.net",isIframe:!0,frameWidth:this.isMobile?295:366,frameHeight:this.isMobile?370:408,append:"#passportbox2",iframeName:"passport_iframe2",from:encodeURIComponent(window.location.href),loginService:"https://passport.csdn.net/key"},t&&t.spm&&(this.inputData.spm=m(t.spm)),this.options=Object.assign({},this.defaultParams,this.inputData),this.extend="",this.renderCss(),this.fileExtends(),this.init(this.options)}return _createClass(e,[{key:"getUserStatus",value:function(e){var i=this;$.ajax({url:"https://passport.csdn.net/v1/api/check/userstatus",timeout:5e3,type:"POST",contentType:"application/json",xhrFields:{withCredentials:!0},data:JSON.stringify({username:t("UserName")||"",biz:this.biz,subBiz:this.subBiz}),dataType:"json",success:function(t){if(t.status)if(t.detail){switch(t.detail){case"deleted":i.options.status="deleted",i.options.frameHeight=178;break;case"activate":i.options.status="activate";break;case"speechForbidden":i.options.status="forbidden";break;default:return!0}i.renderHtml()}else i.executeCallBack();else i.close(),window.csdn.loginBox.show()},error:function(t){throw i.executeError(t),void 0,new Error(t.responseText)}})}},{key:"renderCss",value:function(){var t=window.document.head,e=t.firstElementChild||t.firstChild,i=document.createElement("style");i.innerText=".passport-login-container2{position: fixed;top: 0;left: 0;z-index: 9999;width: 100%;height: 100%;}.passport-login-box2{position: absolute;display: block;border-radius: 8px;left: 50%;top: 50%;z-index: 10001;-webkit-transform: translate(-50%, -50%);-ms-transform: translate(-50%, -50%);-o-transform: translate(-50%, -50%);-moz-transform: translate(-50%, -50%);transform: translate(-50%, -50%);background-color: #fff;}.passport-login-mark2{position: absolute;top: 0;left: 0;z-index: 9999;background-color: rgba(0, 0, 0, 0.5);width: 100%;height: 100%;}",t.insertBefore(i,e)}},{key:"renderHtml",value:function(t){var e=this,o=this.options.loginService;if(this.$loginDom=$('<div class="passport-login-container2"><div id='+this.options.append.replace(/[#\.]/,"")+' class="passport-login-box2" style="display: block;'+(this.options.frameHeight?"height:"+this.options.frameHeight+"px":"")+'"></div></div>'),this.$markDom=$('<div class="passport-login-mark2"></div>'),window.document.domain=this.options.domain,o=o+(-1===o.indexOf("?")?"?status=":"&status=")+this.options.status,o=this.extend?o+this.extend:o,this.$iframeHtml=$('<iframe  width="'+this.options.frameWidth+'" height="'+this.options.frameHeight+'" name="'+this.options.iframeName+'" src="'+o+'" style="border-radius: 8px;" frameborder="0" scrolling="no"></iframe>'),this.$closeBtn=$('<span style="display: inline-block; color: #999; font-size: 22px; cursor: pointer; position:absolute; top:2%; right:3%;-moz-user-select:none; -webkit-user-select:none; user-select:none;">&times</span>'),this.$closeBtn.on("click",function(){e.close()}),this.$markDom.on("click",function(){e.close()}),this.$loginDom.append(this.$markDom),$("body").append(this.$loginDom),$(this.options.append).append(this.$iframeHtml),$(this.options.append).append(this.$closeBtn),this.options.spm){i({spm:this.options.spm})}}},{key:"close",value:function(){this.$loginDom&&this.$loginDom.remove(),window.csdn.loginBox.self2=void 0}},{key:"fileExtends",value:function(){for(var t in this.inputData)this.defaultParams.hasOwnProperty(t)||(this.extend=this.extend+"&"+t+"="+this.inputData[t])}},{key:"init",value:function(t){this.getUserStatus()}},{key:"executeCallBack",value:function(){this.callBackFn&&this.callBackFn(),this.close()}},{key:"executeError",value:function(t){this.errorFn&&this.errorFn(t),this.close()}}]),e}(),g=function(){function t(e){_classCallCheck(this,t),this.inputData=e,this.defaultParams={width:368,height:210,right:"24px",bottom:"24px",title:"登录后您可以享受以下权益："},this.reportExtra={autoPopup:!1,pageType:""},e&&e.reportExtra&&(this.reportExtra=Object.assign({},this.reportExtra,e.reportExtra),delete e.reportExtra),this.icons={code:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAvRJREFUWEfVl+1P0lEUx79XDGNubZqJqCssk2zOHqyVgWTYQgm1rcWW2kt//gf9Ef0H4bss22y9yMdcSgqStqzmG582Agsf2Cy3pjYV+LV7lZ9AKOCgX51XwL33nM/5nnPPLgQiGxE5PkIAWltf5vO8/xEPvhKAIsFwiwRkiJCUh83Nd90B3wIADe7nfRMAMhMcONzdjxQiOReAEADM5hftPPj7SQ7O3BOQ5xx3r2H78449NncsJEH2vfJZbOFMueEA/N/IPhCjhTOx5IMV+D8BcnKycEZVgNGxCWxsbMYsYtwKpKfLcLOqnAV53T8iBLpTX4Xs7Ex09wyjtLQIaVIpBgZHsbb2a1+YuAAyMo7AUKMFhVhaWkZn11vmnH5vbDDC6/XhSdsrtocqsrq2jr4+G1ZWfu4JETNAjjwLer0aaWlSFpxmv7m5xRyXlJzGtfLzcDrdeDMwCqn0EKr1GgYRUMrj+R4RIiYA5Ylc6HRXkZoqgcs1j0HLe/h8PsFhrbESCsUxDFrG4HB8Y79LJBJU6a5AqcxjytC1uTl6w0MtKoCqSAmt9hIIIZiccsBu/wye370oMtlhNDUa4ffzTP6tLa8QgZ7RqC+iuPgkOzNsHcfsrCuEICoArS2ruWcZnZ3bNQ826rxCU4a5rwvo77dHlLm+Tge5/ChryGft3fEBqFQF0FaUbSsw6YD9XagCBoMW+XlyDA1/+CM7ekatvoCzxaeYAlbrOGbiVYDi0jrSetK6Ol3zsOz0AG3IB011AHi0Pe0Kuf/hPWCxjMF1kB4I6EU7Wn8r9BZQsMrrl+F2e9DbZxWkDb8FtDS0hJEsag8EHwqfA6ur6ygsPA6b7SOmpr8IW+tqbyR+DgS8B0/C6RknCpR5sI18gte72/3V1ZrkTMJ9Z+oBF+MqwQFj7HssEoC4DxLRn2SiP0ppwUR9liej0WLx+W/9MwomNps7enjAEEsW0fYQoJfjTLcj7dtTgUQCAOhp4UzGuACiZZWoddF74De0FoIwwHUb7wAAAABJRU5ErkJggg==",download:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAYZJREFUWEftVbtOw0AQnEsRrIhP4NHnTyhS5ighBXchfAAdBR30oPgoQlqbLkL8SWok8gnIilNk0UkYodjxrZ3EbuLO8uzs7Mx5T6DmR9TcH2wBxoRnBDIAjhyiZwJCKdX94AzHFuCb4IvRPOk500oeb1sAcQgTjFaSNRwLZEl9E+wF7B3YO1CNAwU2XpG1ABDsIrvWWr7/L0ztgYIbr7AIreWJS8AngNNizGx0akWnHPiN4A1Ai03LA0YgSGcElms4DNqigRBAm8ftRE1piW6/L6eryLV3ge9PWmjMn0F04aTPAwgxxtIbaN2JsmDOy8iY4JKApxKRRCAaaH0+ztXHma5EJGstZ0ewCmRH4rC8tICkMCeSSAA3SslXjqsJxnkGssgyImFbvrEDCcFoNPIWi8OOfW82vye9Xm9eZPKNHCjTaF1NqQh2IsB/Ce9BdGsd3WaDDK4YQjzqq+6d/fbngG+CuILmiZ5YK+mtCrCH6GDH0+cIqC6CBYR4SEVQ0eSpNrX/BT/MdqghTZsnOgAAAABJRU5ErkJggg==",article:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAqVJREFUWEfFlktoE1EUhv+ThynioxV1KQq6URAUunDpQrAVLLZmTBHRlGQyYqlQ3LgIWuxCBDdBk8xMJCBVYRIXgrb42goKLgTRhSKI2k0RWkhJW5s5MsG0SazJncxIZjXMPed8/51z/zNDaPNFbebjvwhIp4295MVNMA4BmAFxdvr79htjY4eX6zfsugBVzUVAfAtAoA72bJ1/vi8cDi9UP3dNQDab7Vj6teE2wEP/aisBzwsF9I2OSsVKjCsCUqkHOz1e70MABwXO1IvpH9t6Ku1wLEDTcj0MngCwRQBeDmFQXJGD49Z9ywKYmXQ9F2fgCgCPKPxP3OeYLO1pWUAyea/L5/NPMNBrE1wJn43JUldLAlKZ/AGPaVr93tUi3EqbislSWbytFmiacY6BFIAOB/B5D5nd0Wjoo7CARGIyEAgUEiDIDsBW6ifTY/afj4TeC9tQVY0dIOQBdDuBE/CIGWdjMWlOeBCldeMIMe4D2OoAXiJQPBo9eZ2IWGgUWxbTMsZlMF1rwWLVjBk2aVBRgi8bTMfaJVU1NoNwF8BxB7u2Ul8v+0rBC0OD3xrVqXGBruf3m1y22G4ncAKlOzv5oiRJS83qrAjQtPw+hvkGwPpmSQ3Wi0ykKNGg9QaFrhUBqm5MgXFUKGutIMYXZrNfUULv7NRYFaAZlj022UmuxDLwOOD3nwmHT8zaza8W8JdFBIqZYL4qy9L4WhYTyF8dxapm2BNA+EnMp2X51FMRUFMb2hHAwFufxzsQiQx8dQK3cu23gDizWNw4PDLSu+gUXi+g1GTqLTBhWIlKd9wAV2pU2zAJLn/tvHUAyx2v2MQlRZE+uAmveQNuFxatZ+uHRLSonbiqUWw8cfCPZ4dpnfxJWZaO1bRA09oswNYWXAxu+xn4DRv63CEjK31nAAAAAElFTkSuQmCC",v:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAApJJREFUWEfNl7tuE0EUhv+z5AGChZAwPEAiCnAKGmioIKLgIrLKAyRrJ6VdUtimJ6KK7YkfIFoiEooooaKBhiJAgZwXSCIhZPIAZg6awRt5zV5mxomEq5XnzH++c5nZswTHX6ezNQOSa3o7e9Vy+fmhixTZbup2w8JvpjqYVwFMDfcPQLR+ibi5tOT3bTSNAer1D1PXrv9cIXADQCHFSZ9BjZOjK61m8/7ABMQIQIg38wx+BWDWRBRAjyWqlYq/n2efCbCxsTkr2VOO5/OEUtb3PJK15eXFXtr+RABVZym5waCVkTo7MmBA4JbnUSOpP2IAhnV2BUnsjzMAhzq7gvQIVAuChT0lcAbQESGnKjK+6DVCycirgX058LVvE4CDy9O4o4z7p9gl4EEWBAPvC9N4pGx+neIzgLkke2MAAj4FgX9PibS6WyVPyoMsAAJKQeB/VTZChB8ZuDsRgLpoWcpSpbL4TQl1RLgN4EkKxE458J+qtXZ78xZ5nipd4kkzzoC+6oHtSuA/y8kCEzAXRd8W4VsCNMykGdAMBlkwjl4JWmXAIAtW0TsB5GTBKnpXgFgvCBHeZuj7wTp6Z4BxZ8MToeqpm20IpY5p7lvWugeiTh49Ecqh+j/q/JwjGjsMzgDjWYhUbaKfpATaHwHvgsCPXUZChDsMPM66JUfXJsmA1mGgdnL0/bV6Lt64+RKMF6bOEzMwfB2rKXfGQigaQNNmxCSpQwJV/3kdK0s1kBSLP1ZBVM8YPC34YqZ9MDePj6+ujw6s/9dINh6aHkrhrYHx0Clswr4HWbUeSsedOYxrsbErCz73xoo2Gw6sF/NhMhpBysieOXqfSwYS++PvRwvyPj4uBMCpKRM2GffAeTkc1/kDgiVyMIKxs7oAAAAASUVORK5CYII="},this.theme="default",this.options=Object.assign({},this.defaultParams,this.inputData),this.getThemeName(),this.renderCss(),this.renderHtml(this.options)}return _createClass(t,[{key:"renderHtml",value:function(){var t=this;this.$dialogDom=$('<div class="passport-login-tip-container '+("dark"==this.theme&&"dark")+'" style="display:none;">\n                            <p class="tit">'+this.options.title+'</p>\n                            <ul>\n                              <li><img src="'+this.icons.code+'" alt="" /><span>免费复制代码</span></li>\n                              <li><img src="'+this.icons.v+'" alt="" /><span>和博主大V互动</span></li>\n                              <li><img src="'+this.icons.download+'" alt="" /><span>下载海量资源</span></li>\n                              <li><img src="'+this.icons.article+'" alt="" /><span>发动态/写文章/加入社区</span></li>\n                            </ul>            \n                          </div>'),this.$closeBtn=$('<span style="display: inline-block; color: #999; font-size: 22px; cursor: pointer; position:absolute; top:6px; right:18px;-moz-user-select:none; -webkit-user-select:none; user-select:none;">&times</span>'),this.$closeBtn.on("click",function(){t.$dialogDom.fadeOut()}),this.$dialogDom.append(this.$closeBtn),this.$loginBtn=$("<button>立即登录</button>"),this.$loginBtn.on("click",function(){t.$dialogDom.fadeOut();var i={spm:"3001.9568"},o={spm:i.spm};p&&p.curCookie&&(i.reportExtra={popupType:p.taskId+"-"+p.curCookie.version},o.extra=JSON.stringify(i.reportExtra)),e(o),window.csdn.loginBox.show(i)}),this.$dialogDom.append(this.$loginBtn),$("body").append(this.$dialogDom),this.$dialogDom.fadeIn();var o={spm:"3001.9568"};this.reportExtra&&(o.extra=JSON.stringify(this.reportExtra)),i(o)}},{key:"getThemeName",value:function(){var t=$('meta[name="toolbar"]');if(t.length){var e=t.attr("content")||{};e=JSON.parse(e),1==e.type&&(this.theme="dark")}}},{key:"renderCss",value:function(){var t=window.document.head,e=t.firstElementChild||t.firstChild,i=".passport-login-tip-container{\n        position: fixed;\n        font-family: -apple-system,SF UI Text,Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif;\n        bottom: "+this.options.bottom+";\n        right: "+this.options.right+";\n        width: "+this.options.width+"px;\n        padding: 24px 16px;\n        background: #fff;\n        color: #555666;\n        box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.06);\n        border-radius: 4px;\n        z-index: 9999;\n      }\n      .passport-login-tip-container.dark { background: #404041; color: #fff; } \n      .passport-login-tip-container p.tit { margin-bottom:16px; font-size: 14px; font-weight: 500;color: #222226; line-height: 22px;} \n      .passport-login-tip-container.dark p.tit { color: #fff; } \n      .passport-login-tip-container ul { display: flex; flex-wrap: wrap; } \n      .passport-login-tip-container ul li { flex: 0 0 50%; margin-bottom: 16px; font-size: 0; }  \n      .passport-login-tip-container ul li span { font-size: 14px; font-weight: 400;  line-height: 22px; vertical-align: middle; }\n      .passport-login-tip-container ul li img { margin-right: 3px; width: 16px; height: 16px; vertical-align: middle; }\n      .passport-login-tip-container button { border: none;margin-top: 8px; width: 100%; height: 40px; background: #FC5531; border-radius: 20px; font-size: 14px; font-weight: 500; color: #FFFFFF; transition: all .2s; line-height: 40px;}\n      .passport-login-tip-container button:hover { background: #FC1944; }\n      ",o=document.createElement("style");o.innerText=i,t.insertBefore(o,e)}}]),t}(),w=function(){function t(e){_classCallCheck(this,t),this.inputData=e,this.defaultParams={frameWidth:459,frameHeight:210,right:"24px",bottom:"24px",title:"登录可享受更多权益",loginService:"https://passport.csdn.net/account/login",pvSource:"",iframeName:"passport-auto-tip",from:encodeURIComponent(window.location.href)},this.reportExtra={autoPopup:!1,pageType:""},e&&e.reportExtra&&(this.reportExtra=Object.assign({},this.reportExtra,e.reportExtra),delete e.reportExtra),this.theme="default",this.options=Object.assign({},this.defaultParams,this.inputData),this.version="loginv3",this.extend="",this.getThemeName(),this.fileExtends(),this.renderCss(),this.renderHtml(this.options)}return _createClass(t,[{key:"fileExtends",value:function(){for(var t in this.inputData)this.defaultParams.hasOwnProperty(t)||(this.extend=this.extend+"&"+t+"="+this.inputData[t])}},{key:"close",value:function(){this.$autoTipLoginDom&&this.$autoTipLoginDom.remove(),window.csdn.loginBox.autoLoginTip=void 0}},{key:"renderCss",value:function(){var t=window.document.head,e=t.firstElementChild||t.firstChild,i=".passport-auto-tip-login-container{\n        position: fixed;\n        font-family: -apple-system,SF UI Text,Arial,PingFang SC,Hiragino Sans GB,Microsoft YaHei,WenQuanYi Micro Hei,sans-serif;\n        bottom: "+this.options.bottom+";\n        right: "+this.options.right+";\n        background: #fff;\n        color: #555666;\n        box-shadow: 0px 0px 10px 2px rgba(0,0,0,0.06);\n        border-radius: 4px;\n        z-index: 9999;\n      }\n      ",o=document.createElement("style");o.innerText=i,t.insertBefore(o,e)}},{key:"renderHtml",value:function(){var t=this,e=this.options.loginService;e+=(-1===e.indexOf("?")?"?from=":"&from=")+this.options.from,e+="&showTip=true&isBlackTheme="+("dark"===this.theme),e+="&iframe=true",e+="&newframe=true",e=this.version?e+"&version="+this.version:e,e=this.extend?e+this.extend:e,this.$autoTipLoginDom=$('<div class="passport-auto-tip-login-container" style="display: block;'+(this.options.frameHeight?"height:"+this.options.frameHeight+"px":"")+'"></div>'),this.$tipIframeHtml=$('<iframe  width="'+this.options.frameWidth+'" height="'+this.options.frameHeight+'" name="'+this.options.iframeName+'" src="'+e+'" style="border-radius: 8px;" frameborder="0" scrolling="no"></iframe>'),this.$closeBtn=$('<span style="display: inline-block; color: #999; font-size: 22px; cursor: pointer; position:absolute; top:6px; right:18px;-moz-user-select:none; -webkit-user-select:none; user-select:none;">&times</span>'),this.$closeBtn.on("click",function(){t.$autoTipLoginDom.fadeOut()}),this.$autoTipLoginDom.append(this.$closeBtn),this.$autoTipLoginDom.append(this.$tipIframeHtml),$("body").append(this.$autoTipLoginDom),this.$autoTipLoginDom.fadeIn();var o={spm:"1001.2101.3001.9568"};this.reportExtra&&(o.extra=JSON.stringify(this.reportExtra)),i(o)}},{key:"getThemeName",value:function(){var t=$('meta[name="toolbar"]');if(t.length){var e=t.attr("content")||{};e=JSON.parse(e),1==e.type&&(this.theme="dark")}}}]),t}();window.csdn.loginBox={self:void 0,self2:void 0,autoLoginTip:void 0,showTip:function(t){new g(t)},showAutoTip:function(t){this.autoLoginTip||(this.autoLoginTip=new w(t))},show:function(e){if(t("UserName"))return void void 0;this.self||(this.self=new s(e))},key:function(t){this.self2||(this.self2=new f(t))},close:function(){return this.self.close()},loginout:function(t){if(h())return u(),!1;var e=Object.assign({},t);return new Promise(function(t,i){$.ajax({type:"post",url:"https://passport.csdn.net/account/logout",data:JSON.stringify(e),crossDomain:!0,contentType:"application/json",xhrFields:{withCredentials:!0},success:function(e){t(e)},error:function(t){i(t)}})})},setlogin:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{UserName:void 0,UserToken:void 0},e=t.UserName||c("UserName"),i=t.UserToken||c("UserToken"),o=Object.assign({},{username:e,userToken:i});return new Promise(function(t,e){$.ajax({type:"post",url:"https://passport.csdn.net/v1/login/wap/userToken/refresh",data:JSON.stringify(o),crossDomain:!0,contentType:"application/json",xhrFields:{withCredentials:!0},success:function(e){t(e)},error:function(t){e(t)}})})}};var k=[{name:"blog-threeH-dialog",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],dynamicTipRule:["autoShowTip"],platform:["pc"],ab:"exp1",showTipTimes:3},{name:"blog-threeH-dialog2",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],platform:["pc"],ab:"exp2"},{name:"blog-threeH-tip",platform:["pc"],site:["blog"],time:108e5,showType:"tip",dynamicRule:["halfArticleOrDoubleScreen"],ab:"exp3"},{name:"blog-auto-tip",platform:["pc"],site:["blog"],time:108e5,showType:"autoTip",dynamicRule:["autoShowTip"],ab:"exp4"},{name:"blog-threeH-default",platform:["pc"],site:["blog"],time:108e5,showType:"dialog",dynamicRule:["unloginScrollStep"],isDefault:!0},{name:"blog-threeH-dialog-expa",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],platform:["pc"],params:{showThird:!0,toUn:!0},ab:"ExpA"},{name:"blog-threeH-dialog-expb",time:108e5,site:["blog"],showType:"dialog",tip:"登录后有海量资源下载哦",dynamicRule:["halfArticleOrDoubleScreen"],platform:["pc"],params:{toUn:!0},ab:"ExpB"}],y=[{name:"blog-wap-auto",platform:["wap"],site:["blog","download"],time:0,showType:"dialog",dynamicRule:["unLoginWap"],isDefault:!0},{name:"blog-wap-v2",platform:["wap"],site:["blog","download"],time:0,showType:"dialog",dynamicRule:["unLoginWapByUn"],params:{unVersion:"v2",toUn:!0},ab:"expA"}],v=function(){function e(){if(_classCallCheck(this,e),!t("UserName")){this.hasUn=Boolean(t("UN"))||!1,this.debug=!1,this.taskId=this.hasUn?310:308,this.abAPICheckTime=324e5,this.COOKIE_NAME="loginbox_wap_strategy",this.onDayTimeStamp=864e5,this.loginBoxStrategy=[],this.curCookie=t(this.COOKIE_NAME)||"{}";try{this.curCookie=JSON.parse(this.curCookie),void 0}catch(t){l(this.COOKIE_NAME,""),void 0,this.curCookie={}}this.taskId?(this.curCookie.taskId&&this.taskId!==this.curCookie.taskId&&(this.curCookie={}),this.curCookie.taskId=this.taskId,this.updateCookie()):(this.curCookie.taskId||this.curCookie.version)&&(delete this.curCookie.taskId,delete this.curCookie.version,delete this.curCookie.abCheckTime,this.updateCookie()),this.writeLog(this.curCookie),this.init()}}return _createClass(e,[{key:"writeLog",value:function(){if(this.debug){var t;return(t=console).log.apply(t,arguments)}}},{key:"init",value:function(){var t=this;if(this.taskId){this.curCookie.abCheckTime=this.curCookie.abCheckTime||0;var e=+new Date-this.curCookie.abCheckTime>this.abAPICheckTime;if(this.curCookie.version&&!e){var i=y.find(function(e){return e.ab===t.curCookie.version});i||(i=y.find(function(t){return t.isDefault})),i&&this.checkStrategy(i)}else this.getVersion(function(e){var i=void 0;e&&e.version&&(t.curCookie.version=e.version,t.curCookie.nickName=e.nickname,t.curCookie.abCheckTime=+new Date,l(t.COOKIE_NAME,JSON.stringify(t.curCookie)),i=y.find(function(t){return t.ab===e.version})),i||(i=y.find(function(t){return t.isDefault})),i&&t.checkStrategy(i)})}else{var o=y.find(function(t){return t.isDefault});o&&this.checkStrategy(o)}}},{key:"updateCookie",value:function(t){t=t||this.curCookie,l(this.COOKIE_NAME,JSON.stringify(t))}},{key:"getVersion",value:function(e){var i="https://passport.csdn.net/v1/login/abtest/version/get?taskId="+this.taskId;this.hasUn&&(i+="&un="+t("UN")),$.ajax({type:"get",url:i,crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(t){t.status?e(t.data):e()},error:function(){e()}})}},{key:"isCsdnSites",value:function(t){t=t||["blog","so","edu","download","ask","bbs"];var e=location.hostname||location.host,i=e.split("."),o=_slicedToArray(i,1),n=o[0],s=location.pathname.indexOf("/article/details")>=0;return t.indexOf("blog")>-1?s||t.indexOf(n)>-1:t.indexOf(n)>-1}},{key:"getTomorrowTimestamp",value:function(){var t=new Date;return t.setDate(t.getDate()+1),t.setHours(0,0,0,0),t.getTime()}},{key:"checkStrategy",value:function(t){var e=t?[t]:this.loginBoxStrategy,i=!0,n=!1,s=void 0;try{for(var a,r=e[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var p=a.value,l=p.platform,c=p.site,h=p.name,d=p.time,u=o()?"wap":"pc";if(-1===l.indexOf(u))break;this.writeLog(">>>>平台检查完成");if(!this.isCsdnSites(c))break;this.writeLog(">>>>网站检查完成");var m=!1,f=+new Date,g=this.curCookie[h]||"";if("number"==typeof d&&f-g>d&&(m=!0),"string"==typeof d&&d.indexOf("day")>-1){var w=+d.replace("day","");f=this.getTomorrowTimestamp(),f-g>w*this.onDayTimeStamp&&(m=!0)}if(!m)break;this.writeLog(">>>>时间检查完成"),this.curCookie[h]=f,this.onStrategyPass(p,this.updateCookie)}}catch(t){n=!0,s=t}finally{try{!i&&r.return&&r.return()}finally{if(n)throw s}}}},{key:"onStrategyPass",value:function(t,e){var i=this,o=t.showType,n=t.dynamicRule,s=t.params,a=(n||[]).reduce(function(t,e){return t[e]=!1,t},{}),r=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"dialog"===o&&(t=Object.assign({},s,{reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+i.curCookie.version},nickname:i.curCookie.nickName},t),csdn.loginBox.show(t)),e.call(i)};if(!n||!n.length)return void r();var p=function(t){n.every(function(t){return a[t]})&&r(t)};n.forEach(function(t){i[t](function(e){a[t]=!0,p(e)})})}},{key:"unLoginWap",value:function(e){var i=location.pathname.indexOf("/article/details")>=0,o=location.href.indexOf("https://download.csdn.net/")>=0;(i||o)&&$(document).ready(function(){if(i){var o=t("popTimes"),n=t("popShowed10s"),s=t("popShowed3t"),a=setTimeout(function(){n?(clearTimeout(a),a=null):(e({spm:"3001.7902"}),l("popShowed10s","yes","toToday24h"),clearTimeout(a),a=null)},1e4);o?"one"===o?l("popTimes","two","toToday24h"):s||(e({spm:"3001.7961"}),l("popShowed3t","yes","toToday24h")):l("popTimes","one","toToday24h")}else{var r=t("downloadPopShowed");if(r)return;e({spm:"3001.8532"}),r||l("downloadPopShowed","yes","toToday24h")}}),$(document).on("visibilitychange ready",function(o){t("popShowed30m")||("visible"===o.target.visibilityState?a=window.setInterval(function(){var o=t("popShowed30m"),n=+t("unloadshowm")||0;o?(window.clearInterval(a),a=null):n>=30&&i?(e({spm:"3001.8531"}),l("popShowed30m","yes","toToday24h")):(n++,l("unloadshowm",n,"toToday24h"))},6e4):"hidden"===o.target.visibilityState&&(window.clearInterval(a),a=null))})}},{key:"unLoginWapByUn",value:function(e){var i=location.pathname.indexOf("/article/details")>=0,o=location.href.indexOf("https://download.csdn.net/")>=0;(i||o)&&$(document).ready(function(){if(i){var o=t("popTimes"),n=t("popShowed10s"),s=t("popShowed3t"),a=setTimeout(function(){n?(clearTimeout(a),a=null):(e({spm:"3001.7902"}),l("popShowed10s","yes","toToday24h"),
clearTimeout(a),a=null)},1e4);o?"one"===o?l("popTimes","two","toToday24h"):s||(e({spm:"3001.7961"}),l("popShowed3t","yes","toToday24h")):l("popTimes","one","toToday24h")}else{var r=t("downloadPopShowed");if(r)return;e({spm:"3001.8532"}),r||l("downloadPopShowed","yes","toToday24h")}}),$(document).on("visibilitychange ready",function(o){t("popShowed3m")||("visible"===o.target.visibilityState?a=window.setInterval(function(){var o=t("popShowed3m"),n=+t("unloadshowm")||0;o?(window.clearInterval(a),a=null):n>=3&&i?(e(),l("popShowed3m","yes","toToday24h")):(n++,l("unloadshowm",n,"toToday24h"))},6e4):"hidden"===o.target.visibilityState&&(window.clearInterval(a),a=null))})}}]),e}(),b=function(){function e(){if(_classCallCheck(this,e),!t("UserName")){this.hasUn=Boolean(t("UN"))||!1,this.debug=!1,this.taskId=this.hasUn?317:308,this.abAPICheckTime=324e5,this.COOKIE_NAME="loginbox_strategy",this.onDayTimeStamp=864e5,this.loginBoxStrategy=[],this.curCookie=t(this.COOKIE_NAME)||"{}";try{this.curCookie=JSON.parse(this.curCookie),void 0}catch(t){l(this.COOKIE_NAME,""),void 0,this.curCookie={}}this.taskId?(this.curCookie.taskId&&this.taskId!==this.curCookie.taskId&&(this.curCookie={}),this.curCookie.taskId=this.taskId,this.updateCookie()):(this.curCookie.taskId||this.curCookie.version)&&(delete this.curCookie.taskId,delete this.curCookie.version,delete this.curCookie.abCheckTime,this.updateCookie()),this.writeLog(this.curCookie),this.init()}}return _createClass(e,[{key:"writeLog",value:function(){if(this.debug){var t;return(t=console).log.apply(t,arguments)}}},{key:"init",value:function(){var t=this;if(this.taskId){this.curCookie.abCheckTime=this.curCookie.abCheckTime||0;var e=+new Date-this.curCookie.abCheckTime>this.abAPICheckTime;if(this.curCookie.version&&!e){var i=k.find(function(e){return e.ab===t.curCookie.version});i||(i=k.find(function(t){return t.isDefault})),i&&this.checkStrategy(i)}else this.getVersion(function(e){var i=void 0;e&&e.version&&(t.curCookie.version=e.version,t.curCookie.nickName=e.nickname,t.curCookie.abCheckTime=+new Date,l(t.COOKIE_NAME,JSON.stringify(t.curCookie)),i=k.find(function(t){return t.ab===e.version})),i||(i=k.find(function(t){return t.isDefault})),i&&t.checkStrategy(i)})}else{var o=k.find(function(t){return t.isDefault});o&&this.checkStrategy(o)}}},{key:"updateCookie",value:function(t){t=t||this.curCookie,l(this.COOKIE_NAME,JSON.stringify(t))}},{key:"getVersion",value:function(e){var i="https://passport.csdn.net/v1/login/abtest/version/get?taskId="+this.taskId;this.hasUn&&(i+="&un="+t("UN")),$.ajax({type:"get",url:i,crossDomain:!0,xhrFields:{withCredentials:!0},contentType:"application/json",success:function(t){t.status?e(t.data):e()},error:function(){e()}})}},{key:"isCsdnSites",value:function(t){t=t||["blog","so","edu","download","ask","bbs"];var e=location.hostname||location.host,i=e.split("."),o=_slicedToArray(i,1),n=o[0],s=location.pathname.indexOf("/article/details")>=0;return t.indexOf("blog")>-1?s||t.indexOf(n)>-1:t.indexOf(n)>-1}},{key:"getTomorrowTimestamp",value:function(){var t=new Date;return t.setDate(t.getDate()+1),t.setHours(0,0,0,0),t.getTime()}},{key:"checkStrategy",value:function(t){var e=t?[t]:this.loginBoxStrategy,i=!0,n=!1,s=void 0;try{for(var a,r=e[Symbol.iterator]();!(i=(a=r.next()).done);i=!0){var p=a.value,l=p.platform,c=p.site,h=p.name,d=p.time,u=p.times,m=p.showTipTimes,f=o()?"wap":"pc";if(-1===l.indexOf(f))break;this.writeLog(">>>>平台检查完成");if(!this.isCsdnSites(c))break;this.writeLog(">>>>网站检查完成");var g=!1,w=+new Date,k=this.curCookie[h]||"";if("number"==typeof d&&w-k>d&&(g=!0),"string"==typeof d&&d.indexOf("day")>-1){var y=+d.replace("day","");w=this.getTomorrowTimestamp(),w-k>y*this.onDayTimeStamp&&(g=!0)}if(m>=0){var v=this.curCookie[h+"tipShowTimes"]||0;g?""===k?(v<m?(this.curCookie[h+"tipShowTimes"]=++v,this.onStrategyPassByShowTimes(p,this.updateCookie.bind(this,JSON.parse(JSON.stringify(this.curCookie)))),this.curCookie[h]=w):(this.curCookie[h+"tipShowTimes"]=++v,this.updateCookie(JSON.parse(JSON.stringify(this.curCookie))),this.curCookie[h]=w),this.onStrategyPass(p,this.updateCookie)):(this.curCookie[h+"tipShowTimes"]=1,this.curCookie[h]="",this.onStrategyPassByShowTimes(p,this.updateCookie.bind(this,JSON.parse(JSON.stringify(this.curCookie)))),this.curCookie[h]=w,this.onStrategyPass(p,this.updateCookie)):v<m?(this.curCookie[h+"tipShowTimes"]=++v,this.onStrategyPassByShowTimes(p,this.updateCookie)):(this.curCookie[h+"tipShowTimes"]=++v,this.updateCookie())}else{if(!g)break;if(this.writeLog(">>>>时间检查完成"),u){var b=+this.curCookie[h+"Times"]||0;u===b+1?(this.curCookie[h]=w,this.curCookie[h+"Times"]=0,this.onStrategyPass(p,this.updateCookie)):(this.curCookie[h+"Times"]=++b,this.updateCookie())}else this.curCookie[h]=w,this.onStrategyPass(p,this.updateCookie)}}}catch(t){n=!0,s=t}finally{try{!i&&r.return&&r.return()}finally{if(n)throw s}}}},{key:"halfArticleOrDoubleScreen",value:function(t){var e=this,i=function i(){var o="CSS1Compat"==document.compatMode?document.documentElement.clientHeight:document.body.clientHeight,n=Math.max(document.body.scrollTop,document.documentElement.scrollTop),s=n+o,a=s/document.documentElement.scrollHeight*100;e.writeLog("滚动高度 / 窗口高度",n,o,n/o),e.writeLog("百分比",a),(n/o>2||a>50)&&(t(),document.removeEventListener("scroll",i))};document.addEventListener("scroll",i)}},{key:"unloginScrollStep",value:function(t){var e=$("div.article_content");if(e&&e.length){var i=e.offset().top,o=e.height(),n=document.body.clientHeight||document.documentElement.clientHeight,s=$(document).scrollTop(),a=function e(){((s=$(document).scrollTop())+n-i>o/2||s+n-i>2*n)&&(t(),document.removeEventListener("scroll",e))};document.addEventListener("scroll",a)}}},{key:"autoShowTip",value:function(t){t()}},{key:"showOtherBox",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(){};"exp1"===e&&(t=Object.assign({},t,{spm:"1001.2101.3001.9568",reportExtra:{autoPopup:!0,popupType:this.taskId+"-"+this.curCookie.version,pageType:"popup"}}),csdn.loginBox.showAutoTip(t),i.call(this))}},{key:"onStrategyPassByShowTimes",value:function(t,e){var i=this,o=t.dynamicTipRule,n=t.ab;o.forEach(function(t){i[t](function(t){i.showOtherBox(t,n,e)})})}},{key:"onStrategyPass",value:function(t,e){var i=this,o=t.showType,n=t.dynamicRule,s=t.params,a=(n||[]).reduce(function(t,e){return t[e]=!1,t},{}),r=function(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};"dialog"===o&&(n=Object.assign({},n,s,{spm:"3001.6428",reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+i.curCookie.version},nickname:i.curCookie.nickName}),t.tip&&(n.tip=t.tip),csdn.loginBox.show(n)),"tip"===o&&(n=Object.assign({},n,s,{spm:"1001.2101.3001.9568",reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+i.curCookie.version,pageType:"popup"},nickname:i.curCookie.nickName}),csdn.loginBox.showTip(n)),"autoTip"===o&&(n=Object.assign({},n,s,{spm:"1001.2101.3001.9568",reportExtra:{autoPopup:!0,popupType:i.taskId+"-"+i.curCookie.version,pageType:"popup"},nickname:i.curCookie.nickName}),csdn.loginBox.showAutoTip(n)),e.call(i)};if(!n||!n.length)return void r();var p=function(t){n.every(function(t){return a[t]})&&r(t)};n.forEach(function(t){i[t](function(e){a[t]=!0,p(e)})})}}]),e}();p=o()?new v:new b,$(document).on("click",".c-login-check",function(e){r=$(this).data("reportClick")||$(this).parent().data("reportClick")||$(this).parent().parent().data("reportClick")||{},t("UserName")||(e.stopPropagation(),window.csdn.loginBox.show())}),$(document).ready(function(){var e=n(),i=t("keyTimes"),s=t("keyShowed"),a=window.location.pathname,r=a.indexOf("/article/details")>=0;if(!s&&r&&o()&&!h()&&t("UserName")&&e){var p=setTimeout(function(){if(s)return clearTimeout(p),void(p=null);window.csdn.loginBox.key({spm:"3001.7962"}),s||l("keyShowed","yes","toToday24h"),clearTimeout(p),p=null},1e4);if(!i)return void l("keyTimes","one","toToday24h");if("one"===i)return void l("keyTimes","two","toToday24h");window.csdn.loginBox.key({spm:"3001.7963"}),s||l("keyShowed","yes","toToday24h")}}),window.addEventListener("message",function(t){switch(void 0,t.data){case"key-close":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close();break;case"im_client":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close(),window.open("https://csdn.s2.udesk.cn/im_client/?web_plugin_id=29181");break;case"pop":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close(),window.csdn.loginBox.show();break;case"page_reload":window.csdn.loginBox.self2&&window.csdn.loginBox.self2.close(),window.location.reload();break;case"page_reload_onclose":window.csdn.loginBox.self&&window.csdn.loginBox.self.changeCloseReLoadSwitch(1);break;case"pay-cb":window.csdn.loginBox.self&&window.csdn.loginBox.self.cb&&window.csdn.loginBox.self.cb();break;case"pay-data":window.csdn.loginBox.self&&window.csdn.loginBox.self.$iframeHtml[0].contentWindow.postMessage({payInfo:window.csdn.loginBox.self.payInfo},"*");break;case"show_login_box_by_tip":window.csdn.loginBox.autoLoginTip&&window.csdn.loginBox.autoLoginTip.close();var i={spm:"1001.2101.3001.9568"},o={spm:i.spm};p&&p.curCookie&&(i.reportExtra={popupType:p.taskId+"-"+p.curCookie.version},o.extra=JSON.stringify(i.reportExtra)),e(o),window.csdn.loginBox.show(i);break;default:return!1}},!1)}();