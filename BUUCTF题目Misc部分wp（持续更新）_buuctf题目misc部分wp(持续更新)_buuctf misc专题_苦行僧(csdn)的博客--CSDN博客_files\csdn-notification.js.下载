"use strict";function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var _createClass=function(){function t(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}return function(e,n,i){return n&&t(e.prototype,n),i&&t(e,i),e}}();!function(){function t(){return void 0===n?null:document[n]}function e(t){for(var e=document.cookie,n=e.split("; "),i=0;i<n.length;i++){var o=n[i].split("=");if(o[0]==t)return o[1]}return""}var n=void 0,i=void 0,o=null,s=window.location.href.indexOf("test")>-1?"test":"prod";!function(){"undefined"!=typeof document&&(void 0!==document.hidden?(n="hidden",i="visibilitychange"):void 0!==document.msHidden?(n="msHidden",i="msvisibilitychange"):void 0!==document.webkitHidden&&(n="webkitHidden",i="webkitvisibilitychange"))}();var c=function(){function t(n){_classCallCheck(this,t),void 0,this.messageHandle=n.messageHandle,this.UserName=e("UserName")||"",this.deviceId=e("uuid_tt_dd")||"default",this.appId=n.appid||"CSDN-PC",this.chatSocket=null,this.wsConnectStatus=0,this.testMsg=null,this.wsUrl=null,this.lockReconnect=!1,this.tt=null,this.timeout=5e3,this.timeoutObj=null,this.serverTimeoutObj=null,this.connectInfo=null,this.isReallClose=!1,this.getIMtoken()}return _createClass(t,[{key:"getIMtoken",value:function(){var t=this.UserName||"";if(!t)return void void 0;var e={appId:this.appId,userId:t||"",token:"",linkType:1,deviceId:this.deviceId,groupId:"CSDN-private-MSG",channelType:"privateMsg",appFrom:1};this.connectInfo=e,this.webSocketConnect()}},{key:"webSocketConnect",value:function(){var t=this,e=this.connectInfo,n="test"===s?"https://test-im-manager.csdn.net/im-manage/v1.0/dispatch/do":"https://bizapi.csdn.net/im-manage/v1.0/dispatch/do";$.ajax({headers:{"Content-Type":"application/json"},type:"post",url:n,data:JSON.stringify(e),crossDomain:!0,xhrFields:{withCredentials:!0},success:function(n){if(n.data){t.wsUrl="wss://"+n.data.linkServers[0],t.testMsg=JSON.stringify({ver:"1.0",cmdId:2,isZip:0,body:{userId:e.userId,appId:e.appId,imToken:n.data.imToken,groupId:e.groupId}});try{t.chatSocket=new WebSocket(t.wsUrl),t.webSocketInit()}catch(e){t.webSocketReconnect()}}},error:function(t){void 0,this.webSocketReconnect()}})}},{key:"webSocketReconnect",value:function(){var t=this;this.isReallClose||this.wsConnectStatus>0||this.lockReconnect||(this.lockReconnect=!0,this.tt&&clearTimeout(this.tt),this.tt=setTimeout(function(){t.webSocketConnect(),t.lockReconnect=!1},3e3))}},{key:"webSocketInit",value:function(t){var e=this;this.wsConnectStatus=1,this.chatSocket.onopen=function(t){void 0,e.wsConnectStatus=2,e.chatSocket.send(e.testMsg)},this.chatSocket.onmessage=function(t){e.wsConnectStatus=2,e.onMessage(t.data),e.heartCheck()},this.chatSocket.onerror=function(t){void 0,e.onMessage({cmdId:-1,msg:"ws 连接失败"}),e.wsConnectStatus=-1,e.isReallClose||e.webSocketReconnect()},this.chatSocket.onclose=function(t){void 0,e.onMessage({msg:"ws 连接关闭"}),e.wsConnectStatus=-1,void 0,e.isReallClose||e.webSocketReconnect()}}},{key:"onMessage",value:function(t){var e="string"==typeof t?JSON.parse(t):t;5==+e.cmdId&&2===this.wsConnectStatus&&(this.wsConnectStatus=3),"function"==typeof this.messageHandle&&this.messageHandle(e)}},{key:"closeWebsocket",value:function(t){void 0,this.chatSocket.close(),t&&(this.isReallClose=!0),this.wsConnectStatus=0,void 0}},{key:"heartCheck",value:function(){var t=this;this.timeoutObj&&clearTimeout(this.timeoutObj),this.serverTimeoutObj&&clearTimeout(this.serverTimeoutObj),this.timeoutObj=setTimeout(function(){-1!==t.wsConnectStatus&&(t.chatSocket.send('{"cmdId":1}'),t.serverTimeoutObj=setTimeout(function(){void 0,t.chatSocket.close()},t.timeout))},this.timeout)}},{key:"sendMessage",value:function(t,e){return t||e?this.UserName?Chatervice.sendMessage({username:this.UserName,liveId:this.liveId,message:t,anchorId:this.anchorId,image:e},{deviceId:this.deviceId,appId:this.appId}):void void 0:void void 0}}]),t}(),a=function(){function t(e){_classCallCheck(this,t),Object.assign(this,{position:"fixed",top:"initial",bottom:"initial",left:"initial",right:"initial",zIndex:99999,dom:"body",height:"100vh",overflow:"hidden",time:6e3},e),this.$tpl=null,this.init()}return _createClass(t,[{key:"init",value:function(){this.$tpl=$('<div class="notification" style="position: '+this.position+"; left:"+this.left+"; right: "+this.right+"; top: "+this.top+"; bottom: "+this.bottom+"; z-index: "+this.zIndex+';"></div>'),$(this.dom).append(this.$tpl),$(".notification").on("click",".notification-close",function(){var t=$(this).closest(".notification-item");t.slideUp("200",function(){t.remove()})})}},{key:"create",value:function(t){var e=$(t);e.appendTo(this.$tpl).hide().slideDown(200),setTimeout(function(){e.slideUp("200",function(){e.remove()})},this.time)}}]),t}(),l=function(){function e(t,n){_classCallCheck(this,e),this.notification=null,this.chat=null,this.init(t,n)}return _createClass(e,[{key:"init",value:function(t,e){this.notification=new a(t),this.chat=new c({appid:"CSDN-PC",messageHandle:e.bind(this)}),-1===window.location.origin.indexOf("im.csdn.net")&&this.pageListener()}},{key:"create",value:function(t){this.notification.create(t)}},{key:"closeChat",value:function(){this.chat.closeWebsocket(!0)}},{key:"pageListener",value:function(){var e=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12e4;void 0!==i&&document.addEventListener(i,function(){var i=t();if(null!==i){if(o&&clearInterval(o),i&&2===e.chat.wsConnectStatus)return void(o=setTimeout(function(){e.chat.closeWebsocket(!0)},n));!i&&e.chat.isReallClose&&(e.chat.webSocketConnect(),e.chat.isReallClose=!1)}})}}]),e}();window.CsdnNotification=l}();