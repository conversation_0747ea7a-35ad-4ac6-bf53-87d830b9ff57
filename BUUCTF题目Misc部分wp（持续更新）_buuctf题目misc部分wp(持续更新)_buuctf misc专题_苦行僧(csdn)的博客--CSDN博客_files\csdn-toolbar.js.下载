"use strict";function _toConsumableArray(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var _createClass=function(){function t(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}return function(e,n,o){return n&&t(e.prototype,n),o&&t(e,o),e}}(),_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};"function"!=typeof Object.assign&&Object.defineProperty(Object,"assign",{value:function(t,e){if(null===t||void 0===t)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(t),o=1;o<arguments.length;o++){var a=arguments[o];if(null!==a&&void 0!==a)for(var r in a)Object.prototype.hasOwnProperty.call(a,r)&&(n[r]=a[r])}return n},writable:!0,configurable:!0,enumerable:!1}),window._hmt=window._hmt||[],function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(){var t=window.location.host;return 0===t.indexOf("blog")?"UA-*********-2":0===t.indexOf("download")?"UA-*********-8":0===t.indexOf("edu")?"UA-*********-9":0===t.indexOf("bbs")?"UA-*********-4":0===t.indexOf("ask")?"UA-*********-5":0===t.indexOf("gitbook")?"UA-*********-10":0===t.indexOf("iteye")?"UA-*********-6":0===t.indexOf("passport")?"UA-*********-7":0===t.indexOf("so")?"UA-*********-3":0===t.indexOf("www")?t.indexOf("iteye")>0?"UA-*********-6":"UA-*********-1":""}var n="6bcd52f51e9b3dce32bec4a3997715ac",o=function(){var t="6bcd52f51e9b3dce32bec4a3997715ac",e=$('meta[name="toolbar"]');if(e.length){var n=e.attr("content")||{};n=JSON.parse(n),t=n.hmId||t}return t}();if(function(t){for(var e=document.cookie.split("; "),o=0;o<e.length;o++){var a=e[o].split("=");/^Hm\_.+\_.+$/.test(a[0])&&-1===a[0].indexOf(t)&&-1===a[0].indexOf(n)&&(document.cookie=a[0]+"="+escape("")+";max-age=0;domain=.csdn.net;path=/")}}(o),function(t){_hmt.push(["_setAccount",t])}(o),function(){try{var e=!!t("UN"),n=!!t("UserName"),o=t("p_uid");o=o?o.substr(1,1):0,_hmt.push(["_setUserProperty",{islogin:+e,isonline:+n,isvip:+(1==o)}]),n&&_hmt.push(["_setUserId",t("UserName")]),_hmt.push(["_setUserTag","6525",t("uuid_tt_dd")])}catch(t){void 0}}(),window.self==window.top||function(){var t=null;if(parent!==window)try{t=parent.location.href}catch(e){t=document.referrer}return void 0,t}().indexOf("csdn.net")<0){void 0;var a=document.createElement("script");a.src="https://hm.baidu.com/hm.js?"+o;var r=document.getElementsByTagName("script")[0];r.parentNode.insertBefore(a,r)}else void 0;if(document.referrer.indexOf("google.com")>-1){var i=function(){dataLayer.push(arguments)},s=document.createElement("script");s.src="https://www.googletagmanager.com/gtag/js?id="+e(),r.parentNode.insertBefore(s,r),window.dataLayer=window.dataLayer||[];var c=t("UserName")||"";i("js",new Date),i("config",e()),c&&i("set",{user_id:c})}}(),function(){var t=document.createElement("script");t.type="text/javascript",t.async=!0,t.src="https://g.csdnimg.cn/??asdf/1.1.3/trackad.js,iconfont/nav/iconfont-1.0.1.js,notification/1.3.8/notify.js,notification/1.3.8/main.js";var e=document.getElementsByTagName("script")[0];e.parentNode.insertBefore(t,e)}(),window._hmt=window._hmt||[],function(t,e){function n(t){var e=document.createElement("style");e.innerText="#csdn-toolbar .toolbar-inside{display: none;}",document.getElementsByTagName("head")[0].appendChild(e);var n=document.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=t,n.addEventListener("load",function(){e.remove()}),document.getElementsByTagName("head")[0].appendChild(n)}function o(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t)return decodeURIComponent(o[1])}}function a(t,e,n){var o=new Date;if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+encodeURIComponent(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function r(t){var e=new Date;document.cookie=t+"="+escape("1")+";max-age=0;expires="+e.toGMTString()+";domain=.csdn.net;path=/"}function i(t,e){var n,o=null;return function(){var a=this,r=new Date;r-o-e>0?(n&&(clearTimeout(n),n=null),t.apply(a,arguments),o=r):n||(n=setTimeout(function(){t.apply(a,arguments)},e))}}function s(t,e){var n;return function(){var o=this,a=arguments;n&&clearTimeout(n),n=setTimeout(function(){t.apply(o,a)},e)}}function c(e){t.csdn&&t.csdn.loginBox&&t.csdn.loginBox.show?t.csdn.loginBox.show(e):t.location.href="https://passport.csdn.net/account/login"+(e?"?spm="+e.spm:"")}function l(t){t=t.replace("https://","");var e=t.split("/")[0];return~location.host.indexOf(e)}function d(){return location.origin+location.pathname==="https://www.csdn.net/c/"}function p(){return location.origin+location.pathname==="https://www.csdn.net/vip"}function u(t){return"string"==typeof t||t instanceof String}function h(){var t=navigator.userAgent;return t.indexOf("Opera")>-1||t.indexOf("OPR/")>-1?"Opera":t.indexOf("Edg")>-1?"Edge":t.indexOf("Chrome")>-1?"Chrome":t.indexOf("Safari")>-1?"Safari":t.indexOf("Firefox")>-1?"Firefox":t.indexOf("Trident")>-1?"IE":"Unknown"}function m(e){var n="/"===t.location.pathname?"":t.location.pathname,o=t.location.origin+n;return e.indexOf(o)>-1}function g(t){return 0==t.length?"":t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\'/g,"&#39;").replace(/\"/g,"&quot;")}function f(n){if("[object Object]"==Object.prototype.toString.call(n)){var o=e("#csdn-toolbar"),a=n.need_first_suspend||!1,r=n.need_little_suspend||!1,s=n.little_tool_id||"",c=n.little_need_insert_type||"",l=n.need_change_function||"",d="",p="";if(1!=a||1!=r){if(r&&""!=s&&(o=e(s)),r&&s&&""!=c&&(d="."+c,p=e(d)),0===o.length)return void S.push(f.bind(this,n));var u=i(function(){if((document.documentElement.scrollTop||document.body.scrollTop)>=50)o.css({position:"fixed",top:"0",left:"0","z-index":"1993","min-width":"100%",width:"max-content"}),r&&e(".secodn_level_csdn_logo").length&&e(".secodn_level_csdn_logo").css({display:"block"}),r&&""!=c&&(e("body").addClass("toolbar-second-drop"),"onlySearch"==c?e("#csdn_tool_otherPlace").append(p):"onlyUser"==c?e("#csdn_tool_otherPlace").append(p):"searchUser"==c&&(e("#csdn_tool_otherPlace").append(e(".onlySearch")),e("#csdn_tool_otherPlace").append(e(".onlyUser")))),"function"==typeof l&&l("fixed");else{if(o.css({position:"relative","z-index":"","min-width":"100%",width:"max-content"}),r&&e(".secodn_level_csdn_logo").length&&e(".secodn_level_csdn_logo").css({display:"none"}),r&&""!=c){e("body").removeClass("toolbar-second-drop");var t=e("#csdn-toolbar .toolbar-container");"onlySearch"==c?t.find(".toolbar-menus").after(p):"onlyUser"==c?t.find(".toolbar-container-right").append(p):"searchUser"==c&&(t.find(".toolbar-container-middle").append(e(".onlySearch")),t.find(".toolbar-container-right").append(e(".onlyUser")))}"function"==typeof l&&l("noFixed")}},80);(document.documentElement.scrollTop||document.body.scrollTop)>50&&u(),e(t).on("scroll",u)}}}function b(){var e=t.location.host,n=e.split(".")[0],o="";switch(n){case"www":case"blog":case"bbs":o="";break;case"download":o="doc";break;case"ask":o="ask";break;case"gitchat":case"geek":o="";break;case"edu":o="course";break;default:o=""}return e.indexOf(".blog.csdn.net")>-1&&(o="blog"),o}function v(t){var e={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];var a=n[1],r=n[2];e[a]=r}),t?e[t]:e}function w(t){void 0;try{t&&e(document).trigger(t)}catch(t){void 0}}function y(){return!!+(o("p_uid")||"").substr(1,1)}function x(t){try{t>1e4&&(t=parseFloat((t/1e4).toFixed(1))+"w")}catch(t){}return t}function k(e){try{return t.csdn.report.getFullSpm(e)}catch(t){return e}}function D(){this.currentUser={userName:"",userNick:'<a class="set-nick" href="https://passport.csdn.net/account/profile">设置昵称<span class="write-icon"></span></a>',desc:'<a class="fill-dec" href="//my.csdn.net" target="_blank">编辑自我介绍，让更多人了解你<span class="write-icon"></span></a>',avatar:""},this.hasLogin=!1,this.$container="",this.announcement=0,this.logoData={title:"CSDN首页",url:{default:"https://csdnimg.cn/cdn/content-toolbar/csdn-logo.png?v=********.1",dark:"https://g.csdnimg.cn/common/csdn-toolbar/images/csdn-logo.png",home:"https://g.csdnimg.cn/common/csdn-toolbar/images/csdn-logo.png"}},this.navData=[],this.personMenuData=[],this.advertData={date:{start:"2022/07/03 08:50:00",end:"2022/07/04 17:30:00"},background:{large:"https://csdnimg.cn/public/publick_img/ad_20200703_toolbar325.jpg",default:"https://csdnimg.cn/public/publick_img/ad_20200703_toolbar80_2.jpg"},color:"#027ef2",url:"https://aiprocon.csdn.net/p/live?utm_source=live_703"},this.mpMenuData={isShow:!1},this.renderBtnData={},this.demoSpm="",this.init()}t.csdn=t.csdn||{},t.csdn.configuration_tool_parameterv=f;var _=["csdn-toolbar-default","csdn-toolbar-dark","csdn-toolbar-home"],S=[],T=0,C="normal",$=0,B="https://so.csdn.net/so/search";D.prototype={constructor:D,init:function(){var t=this;t.checkLogin(function(e){e.userName&&(t.hasLogin=!0,_hmt.push(["_setUserTag","5744",e.userName])),t.setPersonMenuData()}),t.getToolbarData(t.render)},render:function(e){var n=this;n.isMiniRenderSearch=!1,"mini"===C?(n.renderMiniToolbar(),n.renderLogo(),n.renderMiniMenu(),n.renderBtnsJudgement(),n.chain(),t.csdn&&t.csdn.toolbarFinishCallback&&t.csdn.toolbarFinishCallback()):(n.renderToolbar(),n.renderLogo(),n.renderNav(),n.renderSearch(),n.renderBtnsJudgement(),n.getHotSearchWordData(),n.chain())},setPersonMenuData:function(){this.personMenuData=[{name:"我的关注",url:"https://i.csdn.net/#/uc/follow-list",report:{mod:"popu_789",dest:"https://i.csdn.net/#/uc/follow-list",spm:"3001.5109"},icon:"toolbar-icon-follow",class:""},{name:"我的收藏",url:"https://i.csdn.net/#/uc/collection-list?type=1",report:{mod:"popu_789",dest:"https://i.csdn.net/#/uc/collection-list?type=1",spm:"3001.5110"},icon:"toolbar-icon-collect",class:""},{name:"个人中心",url:"https://i.csdn.net/#/uc/profile",report:{mod:"popu_789",dest:"https://i.csdn.net/#/uc/profile",spm:"3001.5111"},icon:"toolbar-icon-profile",class:""},{name:"帐号设置",url:"https://i.csdn.net/#/account/index",report:{mod:"popu_789",dest:"https://i.csdn.net/#/account/index",spm:"3001.5112"},icon:"toolbar-icon-account",class:"toolbar-subMenu-border"},{name:"我的博客",url:"https://blog.csdn.net/"+this.currentUser.userName,report:{mod:"popu_789",dest:"https://blog.csdn.net/",spm:"3001.5113"},icon:"toolbar-icon-blog",class:""},{name:"管理博客",url:"https://mp.csdn.net/console/article",report:{mod:"popu_789",dest:"https://mp.csdn.net/console/article",spm:"3001.5114"},icon:"toolbar-icon-mp",class:""},{name:"我的学院",url:"https://edu.csdn.net/",report:{mod:"popu_789",dest:"https://edu.csdn.net/",spm:"3001.5115"},icon:"toolbar-icon-edu",class:""},{name:"我的下载",url:"https://mp.csdn.net/console/upDetailed",report:{mod:"popu_789",dest:"https://mp.csdn.net/console/upDetailed",spm:"3001.5116"},icon:"toolbar-icon-download",class:""},{name:"我的书架",url:"https://book.csdn.net/bookshelf",report:{mod:"popu_789",dest:"https://book.csdn.net/bookshelf",spm:"3001.5117"},icon:"toolbar-icon-book",class:"toolbar-subMenu-border"},{name:"我的钱包",url:"https://i.csdn.net/#/wallet/index",report:{mod:"popu_789",dest:"https://i.csdn.net/#/wallet/index",spm:"3001.5136"},icon:"toolbar-icon-wallet",class:""},{name:"我的订单",url:"https://mall.csdn.net/myorder",report:{mod:"popu_789",dest:"https://mall.csdn.net/myorder",spm:"3001.5137"},icon:"toolbar-icon-order",class:"toolbar-subMenu-border"},{name:"帮助",url:"https://blog.csdn.net/blogdevteam/article/details/103478461",report:{mod:"popu_789",dest:"https://blog.csdn.net/blogdevteam/article/details/103478461",spm:"3001.5138"},icon:"toolbar-icon-help",class:""},{name:"退出",url:"javascript:;",report:{spm:"3001.5139"},icon:"toolbar-icon-logout",class:"toolbar-btn-logout"}]},getAvatarByAu:function(t){return"https://profile-avatar.csdnimg.cn/default.jpg!3"},checkLogin:function(t){var e=o("UserNick"),n=o("UserName");this.currentUser.userNick=e,this.currentUser.userName=n,this.currentUser.avatar=this.getAvatarByAu(),t&&t(this.currentUser)},renderToolbar:function(){var t=this,n=t.getToolbarStyle(),o=e('<div id="csdn-toolbar">\n                    <div class="toolbar-inside" '+(n?'style="'+n+'"':"")+'>\n                      <div class="toolbar-container">\n                        <div class="toolbar-container-left">\n                          <div class="toolbar-logo toolbar-subMenu-box csdn-toolbar-fl"></div>\n                          <ul class="toolbar-menus csdn-toolbar-fl"></ul>\n                        </div>\n                        <div class="toolbar-container-middle">\n                        </div>\n                        <div class="toolbar-container-right">\n                          <div class="toolbar-btns onlyUser"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>');return 1==$?e("#toolbarBox").prepend(o):e("body").prepend(o),t.$container=o.find(".toolbar-container"),t.$logoBox=o.find(".toolbar-logo"),t.$NavBox=o.find(".toolbar-menus"),t.$btnsBox=o.find(".toolbar-btns"),e(document).on("click",function(n){0!==e(n.target).closest(".toolbar-search").length||t.searchInputFocus||(t.clearSeachDropMenu(),t.toggleSearchBarInput())}),this},renderMiniToolbar:function(){var t=this,n=t.getToolbarStyle(),o=e('<div id="csdn-toolbar">\n                    <div class="toolbar-inside" '+(n?'style="'+n+'"':"")+'>\n                      <div class="toolbar-container">\n                        <div class="toolbar-container-left">\n                          <div class="toolbar-logo toolbar-subMenu-box csdn-toolbar-fl"></div>\n                        </div>\n                        <div class="toolbar-container-mini-middle">\n                        </div>\n                        <div class="toolbar-container-right">\n                          <div class="toolbar-btns onlyUser"></div>\n                        </div>\n                      </div>\n                    </div>\n                  </div>');return e("body").prepend(o),t.$miniMenu=o.find(".toolbar-container-left"),t.$logoBox=o.find(".toolbar-logo"),t.$btnsBox=o.find(".toolbar-btns"),this},renderNav:function(){var t=this,e=""+this.navData.map(function(e){if(e.hasOwnProperty("whiteUrl")&&e.whiteUrl.length>0&&!m(e.whiteUrl))return!1;if(e.hasOwnProperty("browser")){var n=e.browser,o=h();if(o=o.toLowerCase(),Array.isArray(n)?n=n.map(function(t){return t.toLowerCase()}):u(n)&&(n=n.toLowerCase()),-1===n.indexOf(o))return!1}e.active=l(e.url),"index"===e.id&&d()?e.active=!1:"community"!==e.id||d()||(e.active=!1);var a=((new Date).getTime(),t.isEffectiveTime(e.icon));return'<li class="'+(e.active?"active ":"")+(e.children.length?"toolbar-subMenu-box":"")+(e.slider&&e.slider.list&&e.slider.list.length?" toolbar-subSlider-box":"")+"\" title='"+(e.title?e.title:"")+"'>\n                                <a "+(e.report?"data-report-click='"+JSON.stringify(e.report)+"'":"")+" "+(e.report&&e.report.spm?'data-report-query="spm='+e.report.spm+'"':"")+" href='"+e.url+"'>\n                                  "+e.name+"\n                                  "+(a?"<img style='"+e.icon.style+'\'" src="'+e.icon.url+'">':"")+"\n                                  "+(e.children.length?'<i class="toolbar-subMenu-arrowHead"></i>':"")+"\n                                </a>\n                                "+(e.children.length?'<div class="toolbar-subMenu">\n                                    '+e.children.map(function(t){return'<a rel="nofollow" data-report-click=\''+JSON.stringify(t.report)+"' data-report-query=\"spm="+t.report.spm+'" target="_blank" href=\''+t.url+"'>"+t.name+"</a>"}).join("")+"\n                                  </div>":"")+"\n                                "+(e.slider&&e.slider.list&&e.slider.list.length?'<div class="toolbar-subSlider">\n                                  <div class=toolbar-subSlider-'+e.id+">\n                                    "+e.slider.list.map(function(t){return(t.name&&t.name.length<=2||t.imgUrl)&&'<a rel="nofollow" '+(t.report?"data-report-click='"+JSON.stringify(t.report)+"'":"")+" "+(t.report&&t.report.spm?'data-report-query="spm='+t.report.spm+'"':"")+' target="_blank" href='+(t.url?t.url:"javascript:void(0);")+">"+(t.name?t.name:"")+(t.imgUrl?"<img src="+t.imgUrl+" style="+(t.imgStyle?t.imgStyle:"")+" />":"")+"</a>"}).join("")+"\n                                    </div>\n                                  </div>":"")+"\n                              </li>"}).join("");return this.$NavBox.append(e),this},renderLogo:function(){var t=["default","dark","home"],e=t[T],n='<a data-report-click=\'{"spm":"3001.4476"}\' data-report-query="spm=3001.4476" href="'+(this.logoData.link||"https://csdn.net")+'"><img title="'+(this.logoData.title||"CSDN首页")+'" src="'+this.logoData.url[e]+'"></a>\n                    '+(this.logoData.qrcode?'<div class="toolbar-subMenu">\n                    <img width="96" height="96" src="'+this.logoData.qrcode+'">\n                  </div>':"");return void 0,this.$logoBox.append(n),this},renderSearch:function(n){var o=this,a=e('<div class="toolbar-search onlySearch"><div class="toolbar-search-container">\n                    <span class="icon-fire"></span>\n                    <input id="toolbar-search-input" maxlength="2000" autocomplete="off" type="text" value="" placeholder="'+this.getSearchInputPlaceholder()+'"><div class="gradient"></div>\n                    <button id="toolbar-search-button"><i></i><span>搜索</span></button>\n                    <input type="password" autocomplete="new-password" readonly disabled="true" style="display: none; position:absolute;left:-9999999px;width:0;height:0;"/>\n                  </div></div>'),r=!1,i=s(this.getSearchAssociateWord,300).bind(this);a.on("focus","#toolbar-search-input",function(t){var a=e(this),s=a.val().trim();""===s?o.getSearchHistoryArray(o.renderSearchHistoryDropMenu):i(s,o.renderAssociateWordDropMenu.bind(o)),n||o.toggleSearchBarInput("focus"),o.searchInputFocus=!0,r=!0,o.refreshPlaceholder(),o.buriedPoint({spm:"3001.8516"})}).on("blur","#toolbar-search-input",function(t){o.searchInputFocus=!1,setTimeout(function(){o.refreshPlaceholder()},500)}).on("input","#toolbar-search-input",function(t){var n=e(this),a=n.val().trim();o.searchInputValue=a,void 0,""===a&&r?o.getSearchHistoryArray(o.renderSearchHistoryDropMenu):i(a,o.renderAssociateWordDropMenu.bind(o))}).on("click","button",function(t){var r=e("#toolbar-search-input").val(),i=o.placeholder&&o.placeholder.productId||o.placeholderRight&&o.placeholderRight.productId,s=r?"3001.4498":"3001.7499",c=r||i,l=o.clickSearchBtnHandler.call(o,r,s);return o.buriedPoint({spm:s,dest:l,extend1:r?"pc_search_uc_word":"pc_search_default_word",extra:JSON.stringify({utm_medium:o.utm_medium,searchword:c})}),o.clearSeachDropMenu(),a.find("#toolbar-search-input").blur(),n||o.toggleSearchBarInput(),!1}).on("keydown","#toolbar-search-input",function(t){229!==t.keyCode&&13===t.keyCode&&e("#toolbar-search-button").trigger("click")});var c=!1;return a.on("compositionstart","#toolbar-search-input",function(t){c=!0}).on("compositionupdate","#toolbar-search-input",function(t){c=!0}).on("compositionend","#toolbar-search-input",function(t){c=!1}),n?(e(n).append(a),o.isMiniRenderSearch=!0,e(document).on("click",function(t){0!==e(t.target).closest(".toolbar-search").length||o.searchInputFocus||o.clearSeachDropMenu()})):e(".toolbar-container-middle").append(a),o.$searchBox=e(".toolbar-search"),e(t).on("keydown",function(t){if(!c){var n=e(".associate-word-drop-menu");if(n.length&&r){var o=n.find(".toolbar-search-item"),i=n.find(".toolbar-search-item.active"),s=i.length?i.index():-1;"ArrowUp"===t.key?(s--,s<0&&(s=o.length-1),o.eq(s).addClass("active").siblings().removeClass("active"),a.find("#toolbar-search-input").val(o.eq(s).find(".search-key-data").text()),t.preventDefault()):"ArrowDown"===t.key&&(s++,s>o.length-1&&(s=0),o.eq(s).addClass("active").siblings().removeClass("active"),void 0,a.find("#toolbar-search-input").val(o.eq(s).find(".search-key-data").text()))}}}),e(t).on("resize",function(){r&&(o.clearSeachDropMenu(),a.find("#toolbar-search-input").blur(),n||o.toggleSearchBarInput())}),this},outPutSearchMethod:function(t){this.renderSearch(t),this.getHotSearchWordData()},renderBtnsJudgement:function(){if(Object.keys(this.renderBtnData).length>0){var t=o("c_segment");t=t?parseInt(t):0,this.renderBtnData.control1.indexOf(t)>-1?(document.querySelector(".toolbar-inside").classList.add("control1"),this.renderBtns()):this.renderBtnData.exp1.indexOf(t)>-1?(document.querySelector(".toolbar-inside").classList.add("exp1"),this.renderBtnsExp1()):this.renderBtnData.exp2.indexOf(t)>-1?(document.querySelector(".toolbar-inside").classList.add("exp2"),this.renderBtnsExp2()):this.renderBtnData.exp3.indexOf(t)>-1?(document.querySelector(".toolbar-inside").classList.add("exp3"),this.renderBtnsExp3()):this.renderBtns()}else this.renderBtns()},renderBtnsExp1:function(){var t=this,n=y(),o=n&&this.hasLogin?this.vipData.iconVip:this.vipData.iconNormal,a=(o.showIcon,e('<div class="toolbar-btn toolbar-btn-login toolbar-btn-login-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'">\n          '+(this.hasLogin?'<a class="hasAvatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343" href="https://blog.csdn.net/'+t.currentUser.userName+'"><img src="'+this.currentUser.avatar+'"></a>':' <a rel="nofollow" class="toolbar-btn-loginfun" data-report-click=\'{"spm":"3001.5105"}\'>登录</a>')+'\n          </div>\n          <div class="toolbar-btn toolbar-btn-vip csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\''+JSON.stringify(t.vipData.report)+"' "+(t.vipData.report.spm?'data-report-query="spm='+t.vipData.report.spm+'"':"")+" href='"+t.vipData.url+"'>\n              "+t.vipData.name+"\n            </a>\n          </div>\n          "+(this.hasLogin?'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n              <div class="toolbar-subMenu-box">\n                <a rel="nofollow" data-report-click=\'{"spm":"3001.4508"}\' data-report-query="spm=3001.4508" id="toolbar-remind" href="https://i.csdn.net/#/msg/index">消息</a>\n              </div>\n              <div class="toolbar-msg-box"></div>\n            </div>':"")+'\n          <div class="toolbar-btn toolbar-btn-collect csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\'{"spm":"3001.7480"}\' data-report-query="spm=3001.7480" href="https://i.csdn.net/#/user-center/history">历史</a>\n          </div>\n          <div class="toolbar-btn toolbar-btn-mp csdn-toolbar-fl">\n            <a rel="nofollow" title="创作中心" data-report-click=\'{"dest":"https://mp.csdn.net/","spm":"3001.8539"}\' data-report-view=\'{"dest":"https://mp.csdn.net/","spm":"3001.8539"}\'  data-report-query="spm=3001.8539" href="https://mp.csdn.net">创作中心</a>\n          </div>\n          <div class="toolbar-btn toolbar-btn-write toolbar-btn-write-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'"></div>\n        </div>'));this.$btnsBox.append(a),this.renderNewBtnWrite(),a.on("click",".toolbar-btn-loginfun",function(){var t=e(this).data("report-click");t?c(t):c()}),a.on("click",".toolbar-btn-logout",function(){t.clickLogoutBtnHandler()})},renderBtnsExp2:function(){var t=this,n=y(),o=n&&this.hasLogin?this.vipData.iconVip:this.vipData.iconNormal,a=(o.showIcon,e('<div class="toolbar-btn toolbar-btn-login toolbar-btn-login-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'">\n          '+(this.hasLogin?'<a rel="nofollow" class="hasAvatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343" href="https://blog.csdn.net/'+t.currentUser.userName+'"><img src="'+this.currentUser.avatar+'"></a>':' <a rel="nofollow" class="toolbar-btn-loginfun" data-report-click=\'{"spm":"3001.5105"}\'>登录</a>')+'\n          </div>\n          <div class="toolbar-btn toolbar-btn-vip csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\''+JSON.stringify(t.vipData.report)+"' "+(t.vipData.report.spm?'data-report-query="spm='+t.vipData.report.spm+'"':"")+" href='"+t.vipData.url+"' title='"+t.vipData.name+"'></a>\n          </div>\n          "+(this.hasLogin?'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n              <div class="toolbar-subMenu-box">\n                <a rel="nofollow" data-report-click=\'{"spm":"3001.4508"}\' data-report-query="spm=3001.4508" id="toolbar-remind" href="https://i.csdn.net/#/msg/index" title="消息"></a>\n              </div>\n              <div class="toolbar-msg-box"></div>\n            </div>':"")+'\n          <div class="toolbar-btn toolbar-btn-collect csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\'{"spm":"3001.7480"}\' data-report-query="spm=3001.7480" href="https://i.csdn.net/#/user-center/history" title="历史"></a>\n          </div>\n          <div class="toolbar-btn toolbar-btn-mp csdn-toolbar-fl">\n            <a rel="nofollow" title="创作中心" data-report-click=\'{"dest":"https://mp.csdn.net/","spm":"3001.8539"}\' data-report-view=\'{"dest":"https://mp.csdn.net/","spm":"3001.8539"}\' data-report-query="spm=3001.8539" href="https://mp.csdn.net"></a>\n          </div>\n          <div class="toolbar-btn toolbar-btn-write toolbar-btn-write-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'"></div>\n        </div>'));this.$btnsBox.append(a),this.renderNewBtnWrite(),a.on("click",".toolbar-btn-loginfun",function(){var t=e(this).data("report-click");t?c(t):c()}),a.on("click",".toolbar-btn-logout",function(){t.clickLogoutBtnHandler()})},renderBtnsExp3:function(){var t=this,n=y(),r=n&&this.hasLogin?this.vipData.iconVip:this.vipData.iconNormal,i=r.showIcon?"inline-block":"none",s=e('<div class="toolbar-btn toolbar-btn-login toolbar-btn-login-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'">\n          '+(this.hasLogin?'<a class="hasAvatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343" href="https://blog.csdn.net/'+t.currentUser.userName+'"><img src="'+this.currentUser.avatar+'"></a>':' <a class="toolbar-btn-loginfun" data-report-click=\'{"spm":"3001.5105"}\'>登录</a>')+'\n          </div>\n          <div class="toolbar-btn toolbar-btn-vip csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\''+JSON.stringify(t.vipData.report)+"' "+(t.vipData.report.spm?'data-report-query="spm='+t.vipData.report.spm+'"':"")+" href='"+t.vipData.url+"'>\n              "+t.vipData.name+" "+(t.isEffectiveTime(t.vipData.icon)?"<img style='"+t.vipData.icon.style+'\'" src="'+t.vipData.icon.url+'">':"<img style='"+r.style+";display:"+i+'\'" src="'+r.url+'">')+"\n            </a>\n          </div>\n          "+(this.hasLogin?'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n              <div class="toolbar-subMenu-box">\n                <a rel="nofollow" data-report-click=\'{"spm":"3001.4508"}\' data-report-query="spm=3001.4508" id="toolbar-remind" href="https://i.csdn.net/#/msg/index">消息</a>\n              </div>\n              <div class="toolbar-msg-box"></div>\n            </div>':'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n            <div class="toolbar-subMenu-box">\n              <a rel="nofollow" data-report-click=\'{"spm":"3001.9699"}\' data-report-view=\'{"spm":"3001.9699"}\' data-report-query="spm=3001.9699" id="toolbar-remind" href="https://i.csdn.net/#/msg/index">消息</a>\n            </div>\n          </div>')+'\n          <div class="toolbar-btn toolbar-btn-collect csdn-toolbar-fl">\n            <a rel="nofollow" data-report-click=\'{"spm":"3001.7480"}\' data-report-query="spm=3001.7480" href="https://i.csdn.net/#/user-center/history">历史</a>\n          </div>\n          <div class="toolbar-btn toolbar-btn-mp csdn-toolbar-fl">\n            <a rel="nofollow" title="创作中心" data-report-click=\'{"dest":"https://mp.csdn.net/","spm":"3001.8539"}\' data-report-view=\'{"dest":"https://mp.csdn.net/","spm":"3001.8539"}\' data-report-query="spm=3001.8539" href="https://mp.csdn.net">\n              创作中心'+this.getMpMenuIcon()+'\n            </a>\n          </div>\n          <div class="toolbar-btn toolbar-btn-write toolbar-btn-write-new csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'"></div>\n        </div>');this.$btnsBox.append(s),this.renderNewBtnWrite(),e("#toolbar-remind").on("click",function(){var t=o("toolbar_remind_num");t?t<3&&(t=parseInt(t)+1,a("toolbar_remind_num",t,864e5)):(t=1,a("toolbar_remind_num",t,864e5))}),s.on("click",".toolbar-btn-loginfun",function(){var t=e(this).data("report-click");t?c(t):c()}),s.on("click",".toolbar-btn-logout",function(){t.clickLogoutBtnHandler()})},renderBtns:function(){var t=y(),n=t&&this.hasLogin?this.vipData.iconVip:this.vipData.iconNormal,r=n.showIcon?"inline-block":"none";void 0
;var i=this,s=e('<div class="toolbar-btn toolbar-btn-login csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'">\n                      '+(this.hasLogin?'<a class="hasAvatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343" href="https://blog.csdn.net/'+i.currentUser.userName+'"><img src="'+this.currentUser.avatar+'"></a>':' <a class="toolbar-btn-loginfun" data-report-click=\'{"spm":"3001.5105"}\'>登录/注册</a>')+'\n                      </div>\n                    <div class="toolbar-btn toolbar-btn-vip csdn-toolbar-fl">\n                      <a rel="nofollow" data-report-click=\''+JSON.stringify(i.vipData.report)+"' "+(i.vipData.report.spm?'data-report-query="spm='+i.vipData.report.spm+'"':"")+" href='"+i.vipData.url+"'>"+i.vipData.name+" "+(i.isEffectiveTime(i.vipData.icon)?"<img style='"+i.vipData.icon.style+'\'" src="'+i.vipData.icon.url+'">':"<img style='"+n.style+";display:"+r+'\'" src="'+n.url+'">')+'</a>\n                      </div>\n                    <div class="toolbar-btn toolbar-btn-collect csdn-toolbar-fl">\n                      <a rel="nofollow" data-report-click=\'{"spm":"3001.7480"}\' data-report-query="spm=3001.7480" href="https://i.csdn.net/#/user-center/history">足迹</a>\n                    </div>\n                    <div class="toolbar-btn toolbar-btn-dynamic csdn-toolbar-fl toolbar-dynamic-box">\n                      <a rel="nofollow" data-report-click=\'{"spm":"3001.4507"}\' data-report-query="spm=3001.4507" href="https://blink.csdn.net">动态</a>\n                      <div class="toolbar-dynamic-subMenu">\n                        <a rel="nofollow" class=\'vote-item\' data-report-click=\'{"spm":"3001.8379"}\' data-report-query="spm=3001.8379" href="https://blink.csdn.net/?source=vote">\n                          <span class="pos-rel">投票<i></i></span>\n                        </a>\n                      </div>\n                    </div>\n                    '+(this.hasLogin?'<div class="toolbar-btn toolbar-btn-msg csdn-toolbar-fl">\n                        <div class="toolbar-subMenu-box">\n                          <a rel="nofollow" data-report-click=\'{"spm":"3001.4508"}\' data-report-query="spm=3001.4508" id="toolbar-remind" href="https://i.csdn.net/#/msg/index">消息</a>\n                        </div>\n                        <div class="toolbar-msg-box"></div>\n                      </div>':"")+"\n                    "+(this.hasShowMpMenu()?'\n                      <div class="toolbar-btn toolbar-btn-dynamic csdn-toolbar-fl toolbar-mp-menubox">\n                        <a rel="nofollow" title="'+this.mpMenuData.title+"\" data-report-click='"+JSON.stringify(this.mpMenuData.report)+"' data-report-query=\"spm="+this.mpMenuData.report.spm+'" href="'+this.mpMenuData.url+'">\n                          '+this.mpMenuData.title+"\n                          "+this.getMpMenuIcon()+"\n                        </a>\n                      </div>\n                    ":"")+'\n                    <div class="toolbar-btn toolbar-btn-write csdn-toolbar-fl '+(this.hasLogin?"toolbar-subMenu-box":"")+'"></div>\n                    </div>');return this.$btnsBox.append(s),this.renderBtnWrite(),s.on("click",".toolbar-btn-loginfun",function(){var t=e(this).data("report-click");t?c(t):c()}),s.on("click",".toolbar-btn-logout",function(){i.clickLogoutBtnHandler()}),this.$msgBox=s.find(".toolbar-msg-box"),o("has-vote-msg")||e(".vote-item").find("i").show(),e(".toolbar-dynamic-subMenu").on("click",".vote-item",function(){a("has-vote-msg",1,864e5),e(".vote-item").find("i").hide()}),this},renderBtnWrite:function(){var n=this.hasShowMpMenu()?"https://mp.csdn.net/edit":"https://mp.csdn.net",o='<a rel="nofollow" data-report-click=\'{"spm":"3001.4503","extra":{"dataType":"'+this.demoSpm+'"}}\' data-report-view=\'{"spm":"3001.4503","extra":{"dataType":"'+this.demoSpm+'"}}\' data-report-query="spm=3001.4503" href="'+n+'"><i></i>'+(this.hasShowMpMenu()?"发布":"创作")+"<i></i></a>";if(t.csdn.toolbarData&&t.csdn.toolbarData.writeBtnData&&t.csdn.toolbarData.writeBtnData.btnShow)if(!this.hasLogin&&t.csdn.toolbarData.writeBtnData&&t.csdn.toolbarData.writeBtnData.btnBgUnLoginOld)o='<a data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n            <img src="'+t.csdn.toolbarData.writeBtnData.btnBgUnLoginOld+'">\n          </a>',e(".toolbar-btn-write").append(o),t.csdn&&t.csdn.report&&t.csdn.report.reportView&&t.csdn.report.reportView({spm:"3001.7765"});else{e.ajax({url:"https://blog.csdn.net/phoenix/web/v1/is-zero-article-user",type:"get",xhrFields:{withCredentials:!0},success:function(e){200===e.code&&(e.data?t.csdn.toolbarData.writeBtnData.btnBgNewWriterOld&&(o='<a data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n                      <img src="'+t.csdn.toolbarData.writeBtnData.btnBgNewWriterOld+'">\n                    </a>'):t.csdn.toolbarData.writeBtnData.btnBgWriterOld&&(o='<a data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n                      <img src="'+t.csdn.toolbarData.writeBtnData.btnBgWriterOld+'">\n                    </a>'))},complete:function(){e(".toolbar-btn-write").append(o),t.csdn&&t.csdn.report&&t.csdn.report.reportView&&t.csdn.report.reportView({spm:"3001.7765"})}})}else e(".toolbar-btn-write").append(o),t.csdn&&t.csdn.report&&t.csdn.report.reportView&&t.csdn.report.reportView({spm:"3001.7765"})},renderNewBtnWrite:function(){var n="https://mp.csdn.net/edit",o='<a rel="nofollow" data-report-click=\'{"spm":"3001.4503","extra":{"dataType":"'+this.demoSpm+'"}}\' data-report-view=\'{"spm":"3001.4503","extra":{"dataType":"'+this.demoSpm+'"}}\' data-report-query="spm=3001.4503" href="'+n+'">发布</a>';if(t.csdn.toolbarData&&t.csdn.toolbarData.writeBtnData&&t.csdn.toolbarData.writeBtnData.btnShow)if(!this.hasLogin&&t.csdn.toolbarData.writeBtnData&&t.csdn.toolbarData.writeBtnData.btnBgUnLogin)o='<a class="has-img" data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n            <img src="'+t.csdn.toolbarData.writeBtnData.btnBgUnLogin+'">\n          </a>',e(".toolbar-btn-write").append(o),t.csdn&&t.csdn.report&&t.csdn.report.reportView&&t.csdn.report.reportView({spm:"3001.7765"});else{e.ajax({url:"https://blog.csdn.net/phoenix/web/v1/is-zero-article-user",type:"get",xhrFields:{withCredentials:!0},success:function(e){200===e.code&&(e.data?t.csdn.toolbarData.writeBtnData.btnBgNewWriter&&(o='<a class="has-img" data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n                      <img src="'+t.csdn.toolbarData.writeBtnData.btnBgNewWriter+'">\n                    </a>'):t.csdn.toolbarData.writeBtnData.btnBgWriter&&(o='<a class="has-img" data-report-click=\'{"spm":"3001.7765"}\' data-report-query="spm=3001.7765" href="'+n+'">\n                      <img src="'+t.csdn.toolbarData.writeBtnData.btnBgWriter+'">\n                    </a>'))},complete:function(){e(".toolbar-btn-write").append(o),t.csdn&&t.csdn.report&&t.csdn.report.reportView&&t.csdn.report.reportView({spm:"3001.7765"})}})}else e(".toolbar-btn-write").append(o),t.csdn&&t.csdn.report&&t.csdn.report.reportView&&t.csdn.report.reportView({spm:"3001.7765"})},renderMiniMenu:function(){var t=e('<div class="csdn-toolbar-mini csdn-toolbar-fl">\n        <div class="toolbar-mini-meun-logo">\n          <div class="toolbar-mini-icon"></div>\n          <span>导航</span>\n          <div class="toolbar-mini-partition"></div>\n        </div>\n        <ul class="csdn-toolbar-mini-ul">'+this.navData.map(function(t){if(t.hasOwnProperty("whiteUrl")&&t.whiteUrl.length>0&&!m(t.whiteUrl))return"";if(t.hasOwnProperty("browser")){var e=t.browser,n=h();if(n=n.toLowerCase(),Array.isArray(e)?e=e.map(function(t){return t.toLowerCase()}):u(e)&&(e=e.toLowerCase()),-1===e.indexOf(n))return""}return t.active=l(t.url),"index"===t.id&&d()?t.active=!1:"community"!==t.id||d()?p()&&(t.active=!1):t.active=!1,'<li class="'+(t.active?"active ":"")+'csdn-toolbar-mini-li">\n              <a '+(t.report?"data-report-click='"+JSON.stringify(t.report)+"'":"")+" "+(t.report&&t.report.spm?'data-report-query="spm='+t.report.spm+'"':"")+" href='"+t.url+"'>"+t.name+"</a>\n            </li>"}).join("")+"\n        </ul>\n      </div>");return this.$miniMenu.prepend(t),this},renderMsgMenu:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.hasLogin){var e='<div class="toolbar-subMenu">\n                          <a rel="nofollow" data-type="comment" href="https://i.csdn.net/#/msg/index"><span \n                          class="pos-rel">评论和@'+(t.comment?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" data-type="attention" href="https://i.csdn.net/#/msg/attention"><span class="pos-rel">新增粉丝'+(t.follow?"<i></i>":"")+'</span></a>         \n                          <a rel="nofollow" data-type="like" href="https://i.csdn.net/#/msg/like"><span class="pos-rel">赞和收藏'+(t.thumb_up?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" data-type="chat" href="https://im.csdn.net/im/main.html"><span class="pos-rel">私信'+(t.im?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" data-type="notice" href="https://i.csdn.net/#/msg/notice"><span class="pos-rel">系统通知'+(t.system?"<i></i>":"")+'</span></a>\n                          <a rel="nofollow" href="https://i.csdn.net/#/msg/setting">消息设置</a>\n                     </div>';this.$btnsBox.find(".toolbar-btn-msg .toolbar-subMenu-box").append(e)}else this.setRemind("https://passport.csdn.net/account/login",0);S.length&&S.forEach(function(t){return t()})},renderCoupon:function(n){var o=e('<a href="https://i.csdn.net/#/msg/notice" class="toolbar-msg-coupon">你有一张VIP限时优惠券哦</a>');n&&n>0&&-1===t.location.href.indexOf("assign_skin_id")&&(this.$msgBox.append(o),setTimeout(function(){o.remove()},5e3))},renderGuide:function(t){var n=this,r=o("c-toolbar-loginguide"),i=e('<span class="toolbar-msg-guide"><a href="https://i.csdn.net/#/msg/index">登录查看未读消息</a><i></i></span>');!r&&t&&(i.find("i").click(function(t){a("c-toolbar-loginguide",1,864e5),i.remove()}),!n.hasLogin&&t>0&&this.$msgBox.append(i))},renderLiveMsg:function(t){var n=this;if(t&&1===t.status){var o=e('<div class="toolbar-msg-live">\n                      <a class="toolbar-msg-live-title" target="_blank" href="'+t.url+'">'+t.title+'</a>\n                      <p class="toolbar-msg-live-count"><i></i>'+t.count+'人在看</p>\n                      <div class="toolbar-msg-live-avatar"><span><img src="'+t.avatar+'" alt=""></span></div>\n                      <span class="toolbar-msg-live-close"></span>\n                    </div>');o.on("click",".toolbar-msg-live-close",function(t){o.remove()}),o.on("click",".toolbar-msg-live-title",function(e){n.reportLiveId(+t.messageId)}),n.$msgBox.append(o),setTimeout(function(){o.remove()},15e3)}},reportLiveId:function(t){if(t===t){var n={messageId:t};n=JSON.stringify?JSON.stringify(n):'{"messageId":'+t+"}",e.ajax({url:"https://msg.csdn.net/v1/web/message/view/live",type:"post",data:n,contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){}})}},renderSearchHistoryDropMenu:function(){var t=this,n=this.getSearchHistoryData(),o=JSON.stringify({spm:"3001.7957"}),a=function(e,n){var o=k("3001.4501");return JSON.stringify({spm:o,index:""+n,dest:B+"?spm="+o+"&q="+encodeURIComponent(e),extra:{searchword:e,isDoublePlaceholder:t.isDoublePlaceholder?1:0}})},r=e('<div class="toolbar-search-drop-menu" data-report-view=\''+o+"'>\n        "+(n.length?'<div class="toolbar-search-history">\n          <p class="toolbar-search-title">搜索历史<span class="toolbar-search-clear"><i></i>清空</span></p>\n          <ul>\n            '+n.map(function(t,e){return'<li class="toolbar-search-item" data-type="history" data-index="'+e+"\" data-report-view='"+a(t,e)+"'><span>"+t+'</span><span class="del"></span></li>'}).join("")+"\n          </ul>\n        </div>":"")+"\n      </div>");r.on("click",".toolbar-search-clear",function(e){t.clearSearchHistory(),r.find(".toolbar-search-history").remove(),r.removeClass("toolbar-search-half")}).on("click",".del",function(n){var o=e(this).parent().text();t.clearSingleSearchHistory(o),e(this).parent().remove(),n.stopPropagation()}).on("click","li",function(n){t.clickSearchItemHandler.call(t,e(this)),t.isMiniRenderSearch||t.setAnimate(t.$NavBox,{width:"auto"})}).on("mouseover","li.toolbar-search-item",function(t){e(this).addClass("active").siblings().removeClass("active"),e(this).find(".del").show()}).on("mouseleave","li.toolbar-search-item",function(){e(this).removeClass("active"),e(this).find(".del").hide()}),t.clearSeachDropMenu(),t.$searchHotAndHistoryDropMenu=r,setTimeout(function(){t.reportViewCheck()}),n.length?(t.$searchBox.append(r),t.getHotSearchData(function(){r.addClass("toolbar-search-half")},n)):t.getHotSearchData(function(){t.$searchBox.append(r)},n)},renderSearchHotDropMenu:function(t,n){var o=this,a=Array.isArray(t)?t.slice():[];if(this.isDoublePlaceholder&&this.placeholderRight&&this.placeholderRight.productId){a=a.filter(function(t){return t.productId!==o.placeholderRight.productId});var r=Object.assign({},this.placeholderRight,{productId:g(this.placeholderRight.productId)});a.unshift(r)}if(a.length){"function"==typeof n&&n();var i=function(t,e){var n=t.productId||t.word,a=k("3001.4502"),r=Object.assign({},t.reportData.data,{spm:a,index:""+e,dest:B+"?spm="+a+"&q="+encodeURIComponent(n),extra:{searchword:n,isDoublePlaceholder:o.isDoublePlaceholder?1:0}});try{var i=JSON.parse(t.reportData.data.extra);r.extra=Object.assign({},i,r.extra)}catch(t){}return JSON.stringify(r)},s=e('<div class="toolbar-search-hot guess">\n        <p class="toolbar-search-title">搜索发现</p>\n        <ul>\n          '+a.map(function(t,e){return'<li class="toolbar-search-item '+(e<=2||t.hot?"hot":"")+'" data-type="hot" data-index="'+e+"\" data-report-view='"+i(t,e)+"'><span>"+(t.productId||t.word)+"</span></li>"}).join("")+"\n        </ul>\n      </div>");s.on("mouseover",".toolbar-search-item",function(t){e(this).addClass("active").siblings().removeClass("active")}).on("mouseleave",".toolbar-search-item",function(){e(this).removeClass("active")}),this.$searchHotAndHistoryDropMenu.find(".toolbar-search-hot").remove(),this.$searchHotAndHistoryDropMenu&&this.$searchHotAndHistoryDropMenu.append(s),setTimeout(function(){o.reportViewCheck()})}},renderAssociateWordDropMenu:function(t){if(this.searchInputFocus){var n=this,o=e(e.parseHTML('\n        <div id="dropDownList" class="toolbar-search-drop-menu associate-word-drop-menu" style="height: auto;">\n          <ul class="drop-menu-list">\n            '+t.map(function(t,e){var o=k("3001.7498"),a=t.ext&&t.ext.query||t.productId,r=B+"?spm="+o+"&q="+encodeURIComponent(a),i=t.reportData&&t.reportData.data||{};try{"string"==typeof i.extra&&(i.extra=JSON.parse(i.extra)||{})}catch(t){i.extra={}}return i.extra.searchword=a,Object.assign(i,{spm:o,dest:r}),'<li class="toolbar-search-item" data-index=\''+e+"' data-report-view='"+JSON.stringify(i)+'\'>\n                <span class="search-key-data" data-type="suggest" data-index=\''+e+"'>"+t.productId+'</span>\n                <span class="search-key-tip">\n                  '+("1"===n.searchAssociateType&&t.ext.pv>0?x(t.ext.pv)+" 人搜过":"")+"\n                  "+("2"===n.searchAssociateType&&t.ext.result_num>0?x(t.ext.result_num)+" 篇精华内容":"")+"\n                </span>\n              </li>"}).join("")+"\n          </ul>\n        </div>\n      "));o.on("click","li",function(t){n.clickSearchItemHandler.call(n,e(this).find(".search-key-data")),n.isMiniRenderSearch||n.setAnimate(n.$NavBox,{width:"auto"})}),o.on("mouseover","li.toolbar-search-item",function(t){e(this).addClass("active").siblings().removeClass("active")}),this.$searchBox.append(o),this.reportViewCheck()}},clickSearchBtnHandler:function(n,o){var a=this.placeholder&&this.placeholder.productId||this.placeholderRight&&this.placeholderRight.productId,r=n||a;if(!n&&this.placeholder&&"csdn_ad"===this.placeholder.recommendType&&this.placeholder.ext&&this.placeholder.ext.url)return void t.open(this.placeholder.ext.url);if(void 0===r||null===r)return e("#toolbar-search-input").focus(),!1;var i=encodeURIComponent(r),s=!n&&a,c="",l=B+"?spm="+k(o)+"&q="+i+"&t="+b()+"&u=",d=s?this.urlParamsPlaceholder:this.urlParams;if(d){for(var p in d)if(d.hasOwnProperty(p)){var u=d[p];c+="&"+p+"="+u}l+=c}return this.urlParams="",t.location.href.indexOf("so.csdn.net")>-1?t.csdn&&t.csdn.toolbarSearchUrl?t.location.href=t.csdn.toolbarSearchUrl+i+c:t.location.href=l:t.open(l),l},clickSearchItemHandler:function(n){var o=this,a=[n.text(),n.data("type"),n.data("index")],r=a[0],i=a[1],s=a[2],c={},l="";if("hot"===i){var d=o.hotSearchData[s],p=d&&d.reportData;c=p?o.getHotSearchPointData(p,r):Object.assign({},c,{spm:"3001.4502"}),d&&"csdn_ad"===d.recommendType&&d.ext&&d.ext.url&&(l=d.ext.url)}else if("history"===i)c={spm:"3001.4501"};else{var u=this.searchAssociateWord[s],h=u&&u.reportData&&u.reportData.data;this.urlParams=u&&u.reportData&&u.reportData.urlParams,c=Object.assign({},h,{spm:"3001.7498"})}l?t.open(l):(e("#toolbar-search-input").val(r).blur(),l=o.clickSearchBtnHandler.call(o,r,c.spm));try{"string"==typeof c.extra&&(c.extra=JSON.parse(c.extra))}catch(t){}c.extra=Object.assign({},c.extra,{searchword:r,isDoublePlaceholder:this.isDoublePlaceholder?1:0}),Object.assign(c,{dest:l,index:""+s}),o.buriedPoint(c),o.clearSeachDropMenu(),o.isMiniRenderSearch||o.toggleSearchBarInput()},clickAnnouncementHandler:function(){this.hasLogin&&(e("#toolbar-announcement").find(">i").remove(),this.announcement&&(this.announcement.announcementCount=0),a("announcement-new",JSON.stringify(this.announcement),this.announcement.announcementExpire||3e5),this.clearReadAnnouncement())},clickLogoutBtnHandler:function(){var e={mod:"popu_789"},n="https://passport.csdn.net/account/logout?from="+encodeURIComponent(t.location.href);e.dest=n,e.extend1="退出",t.location.href=n,csdn&&csdn.report&&csdn.report.reportClick(e)},clearSeachDropMenu:function(){e(".toolbar-search-drop-menu").remove()},clearReadAnnouncement:function(){e.ajax({url:"https://msg.csdn.net/v1/web/message/read_announcement",type:"post",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){},error:function(t){void 0}})},setUserAvatar:function(t){var n=t.avatarUrl;t.totalCount;n&&(this.currentUser.avatar=n,e(".toolbar-btn-login").find(".hasAvatar img").attr("src",n),e("#csdn-toolbar-profile").find(".csdn-profile-avatar > img").attr("src",n))},getHotSearchPointData:function(t,e){this.urlParams=t.urlParams;var n=Object.assign({},t.data,{spm:"3001.4502",extra:{searchword:e,isDoublePlaceholder:this.isDoublePlaceholder?1:0}});try{Object.assign(n.extra,JSON.parse(t.data.extra))}catch(t){}return n},getReadAnnouncement:function(){var t=this;e.ajax({url:"https://msg.csdn.net/v1/web/message/view/announcement",type:"post",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){if(e.status){var n=e.data;t.announcement=n,a("announcement-new",JSON.stringify(n),n.announcementExpire||3e5),t.hasLogin?t.getUnreadMsg():t.renderMsgMenu({announcement:n})}},error:function(t){void 0}})},getUnreadMsg:function(){if(this.hasLogin||o("UN")){var t=this,n=JSON.stringify?JSON.stringify({coupon:!0}):'{"coupon":true}';e.ajax({url:"https://msg.csdn.net/v1/web/message/view/unread",type:"post",data:n,contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){if(e.status){var n=e.data;t.setRemind("",n.totalCount),t.renderCoupon(n.coupon_order),t.renderGuide(n.totalCount),t.renderLiveMsg(n.live_info),t.setUserAvatar(n),t.renderMsgMenu(n)}}})}},getSearchHistoryData:function(){var t=this.searchHistoryArray.splice(0,10);return this.isDoublePlaceholder&&this.placeholder&&this.placeholder.productId&&(t.unshift(g(this.placeholder.productId)),t=Array.from(new Set(t))),t},getSearchHistoryArray:function(t){var n=this;e.ajax({url:"https://so.csdn.net/api/v1/get_search_his",type:"get",contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},success:function(e){200==e.code&&(n.searchHistoryArray=e.data.map(function(t){return g(t)}),t&&t.call(n))},error:function(t){void 0}})},getHotSearchData:function(t,n){var a=this;if((new Date).valueOf()-a.copyHotSearchDataTime<=2e3&&a.hotSearchData)return void a.renderSearchHotDropMenu(a.hotSearchData,t);e.ajax({url:"https://silkroad.csdn.net/api/v2/assemble/list/channel/pc_hot_word",type:"get",data:{user_foormark:1,channel_name:"pc_hot_word",size:20,user_name:a.currentUser.userName,platform:"pc",imei:o("uuid_tt_dd")},contentType:"application/json",dataType:"json",success:function(e){if(200===e.code){var o=e.data&&e.data.items||[];Array.isArray(o)&&Array.isArray(n)&&(o=o.filter(function(t){return!n.includes(t.productId)})),o=o.slice(0,10),o.forEach(function(t){t.productId=g(t.productId)}),a.copyHotSearchData(o),a.renderSearchHotDropMenu(o,t)}},error:function(e){a.hotSearchData&&a.renderSearchHotDropMenu(a.hotSearchData,t)}})},getSearchAssociateWord:function(t,n){if(this.searchInputValue){if(t===this.historySearchInputValue)return this.clearSeachDropMenu(),void n(this.searchAssociateWord);var a=this;e.ajax({url:"https://silkroad.csdn.net/api/v2/rcmd/list/channel/pc_toolbar_associateword",type:"POST",data:JSON.stringify({channel:"pc_toolbar_associateword",ext:{isAcceptDownGrade:!0,summary:!0,query:t,pageSize:10,page:0,type:"suggest",deviceid:"-",platform:"pc",user_name:a.currentUser.userName,imei:o("uuid_tt_dd")},size:10}),contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){if(200===e.code){if(a.clearSeachDropMenu(),!e.data.items.length)return;var o=e.data.items.map(function(t){return{productId:t.product_id,reportData:t.report_data,ext:t.ext}});a.searchAssociateType=e.data.ext.num_show,a.searchAssociateWord=o,a.historySearchInputValue=t,n&&n(o)}}})}},getHotSearchWordData:function(n){var a={new_hot_flag:1,channel_name:"pc_hot_word",size:20,user_name:this.currentUser.userName,platform:"pc",imei:o("uuid_tt_dd")};if(t.toolbarSearchExt)try{var r=_typeof(t.toolbarSearchExt);"object"===r?a.toolbarSearchExt=JSON.stringify(t.toolbarSearchExt):"string"===r&&(a.toolbarSearchExt=t.toolbarSearchExt)}catch(t){void 0}var i=this;e.ajax({url:"https://silkroad.csdn.net/api/v2/assemble/list/channel/search_hot_word",type:"get",data:a,contentType:"application/json",dataType:"json",success:function(t){if(200===t.code){var e=t.data&&t.data.items||[];a.toolbarSearchExt?this.isDoublePlaceholder=!1:i.isDoublePlaceholder=t.data&&t.data.ext&&t.data.ext.ab_test_ext&&"1"===t.data.ext.ab_test_ext.his_foot_flag,e.length&&(i.utm_medium=t.data.ext.utm_medium||"",i.isDoublePlaceholder?(i.placeholderList=e.filter(function(t){return!!t.productId&&"alirecmd"===t.strategyId}),i.placeholderListRight=e.filter(function(t){return!!t.productId&&"alirecmd"!==t.strategyId})):(i.placeholderList=e.filter(function(t){return!!t.productId}),i.placeholderListRight=null),i.setPlaceholderInterval(i.placeholderList,i.placeholderListRight)),n&&n()}},error:function(t){void 0}})},refreshPlaceholder:function(){this.setPlaceholderInterval(this.placeholderList,this.placeholderListRight,this.placeholderIndex)},setPlaceholderInterval:function(t){var n=this,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=t&&t.length?a%t.length:0,i=o&&o.length?a%o.length:0;this.placeholder=t&&t[r]||null,this.placeholderRight=this.isDoublePlaceholder&&o&&o[i]||null,setTimeout(function(){n.setSearchInputPlaceholder(n.placeholder,n.placeholderRight)},200),!this.isDoublePlaceholder&&this.placeholder&&r<3||this.isDoublePlaceholder&&this.placeholderRight&&i<3?(e(".icon-fire").show(),e("#toolbar-search-input").css("textIndent","32px")):(e(".icon-fire").hide(),e("#toolbar-search-input").css("textIndent","12px")),this.placeholderIndex=a,clearTimeout(this.placeholderTimeout),this.placeholderTimeout=setTimeout(function(){n.setPlaceholderInterval(t,o,a+1)},5e3)},copyHotSearchData:function(t){if(t){var e=this;e.hotSearchData=t,e.copyHotSearchDataTime=(new Date).getTime()}},clearSingleSearchHistory:function(t){e.ajax({url:"https://so.csdn.net/api/v1/del_one_search_his?del_query="+t,type:"get",contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},error:function(t){void 0}})},clearSearchHistory:function(){this.isDoublePlaceholder&&(this.placeholderList=[]),e.ajax({url:"https://so.csdn.net/api/v1/del_search_his",type:"get",contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},error:function(t){void 0}})},getSearchInputPlaceholder:function(){return this.placeholder?this.placeholder&&this.placeholder.productId:~location.host.indexOf("edu")?"搜学院课程":"搜CSDN"},setSearchInputPlaceholder:function(t,n){this.urlParamsPlaceholder=t&&t.reportData&&t.reportData.urlParams;var o=t&&t.productId||"",a=n&&n.productId||"",r="";this.searchInputFocus?r=o||a||"":o&&a?(o=o.slice(0,12),a=a.slice(0,12),r=o+" | "+a):r=o||a||"",this.reportPlaceholderView(t),this.reportPlaceholderView(n),r&&e("#toolbar-search-input").attr("placeholder",r)},reportPlaceholderView:function(n){try{if(!n||"csdn_ad"!==n.recommendType||n.isViewReported||e("#toolbar-search-input").val())return;var o=n.reportData.data;o.spm="3001.7499",t.csdn.report.reportView(o),n.isViewReported=!0}catch(t){}},isEffectiveTime:function(t){if(!t)return!1;if(t.always)return!0;var e=(new Date).valueOf(),n=new Date(t.start).valueOf();return e<=new Date(t.end).valueOf()&&e>=n},getCurrentLogoData:function(t){var e=this;return Array.isArray(t)?t.reduce(function(t,n){return t="default"!==n.type||t?t:n,t=e.isEffectiveTime(n.time)?n:t},void 0):"object"===(void 0===t?"undefined":_typeof(t))?t:e.logoData},getToolbarData:function(n){var o=this;e.ajax({url:"https://img-home.csdnimg.cn/data_json/toolbar/toolbar1105.json",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",success:function(e){void 0,o.navData=e.menus.slice(0,e.menus.length-1),o.vipData=e.menus.pop(),void 0,void 0,o.logoData=o.getCurrentLogoData(e.logo),void 0,o.background=e.background,o.redpackData=e.redpackData,o.mpMenuData=e.mpMenuData?e.mpMenuData:o.mpMenuData,o.renderBtnData=e.renderBtnData?e.renderBtnData:[],t.csdn.toolbarData=e,n&&n.call(o)},error:function(t){n&&n.call(o)}})},hasShowMpMenu:function(){return!("normal"!==C||!this.mpMenuData.isShow||void 0===this.mpMenuData.maxSegment)&&o("c_segment")<=parseInt(this.mpMenuData.maxSegment)},getMpMenuIcon:function(){if(!this.mpMenuData.isShow)return"";if(this.mpMenuData.icon&&this.mpMenuData.icon.length>0){return'<img src="'+(this.mpMenuData.icon[T]?this.mpMenuData.icon[T]:this.mpMenuData.icon[0])+'" class="icon-mp-menu">'}return""},buriedPoint:function(t){try{csdn&&csdn.report&&csdn.report.reportClick(t)}catch(t){void 0}},reportViewCheck:function(){try{csdn&&csdn.report&&csdn.report.viewCheck()}catch(t){void 0}},setDocumentTitle:function(t){var e=document.title;t>0&&(document.title="("+t+"条消息) "+e)},setRemind:function(t,n){var a=e("#toolbar-remind"),r=e(".toolbar-inside").hasClass("exp2");this.hasLogin;var i=o("toolbar_remind_num")?o("toolbar_remind_num"):0;a.html('<span class="pos-rel">'+(r?"":"消息")+(!this.hasLogin&&i<3||n>0?'<i class="toolbar-msg-count"></i>':"")+"</span>")},setAnimate:function(t,e,n){n&&t.animate(e,n)||t.css(e)},toggleNavItems:function(t,e){var n=this.$NavBox.find(">li").eq(t).nextAll();e&&n.show()||n.hide()},getNavItemsWidthByCount:function(t){var n=0;return this.$NavBox.find(">li").eq(t).prevAll().each(function(t,o){n+=e(this).width()}),n+=4},toggleSearchBarInput:function(t){var n=e(document).width();if(!(n>1440)){var o=5;1366<n&&n<=1440?o=5:1280<n&&n<=1366?o=5:n<=1280&&(o=4);var a=this.getNavItemsWidthByCount(o);"focus"===t?this.setAnimate(this.$NavBox,{width:a},200):this.setAnimate(this.$NavBox,{width:"auto"},0),this.toggleNavItems(o-1,"focus"!==t)}},getToolbarStyle:function(){var t=this,e=t.background,n=["default","dark","home"],o=n[T];if(e){var a=e[o];if(void 0,a.indexOf("http")>=0)return"background: url("+a+") no-repeat center center;background-size: cover;";if(a.indexOf("#")>=0)return"background: "+a}},setToolbarMsgCountByType:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(t&&this.hasLogin){var o=e(".toolbar-btn-msg").find('a[data-type="'+t+'"]');if(0!==o.length){o.find("i").remove(),o.html('<span class="pos-rel">'+o.text()+(n>0?"<i></i>":"")+"</span>");var a=e(".toolbar-btn-msg").find(".toolbar-subMenu a i").length;void 0,this.setRemind("",a)}}},chain:function(){var t=o("UserName");return w("toolbarReady"),t?this.getUnreadMsg():this.renderMsgMenu(),r("announcement"),r("announcement_new"),r("searchHistoryArray"),r("searchHistoryArray-new"),this}},function(){var t=e('meta[name="toolbar"]'),o="",a=0,r=0;if(t.length){var i=t.attr("content")||{};i=JSON.parse(i),a=i.type||a,C=i.model||C,r=i.fixModel||r}else a=v("toolbarSkinType")||a;o="https://g.csdnimg.cn/common/csdn-toolbar/"+_[a]+".css",T=a,$=r,-1===location.host.indexOf("loc-toolbar")&&n(o)}();var I=new D;t.csdn.toolbar={setToolbarMsgCountByType:I.setToolbarMsgCountByType.bind(I),configuration_tool_parameterv:f,renderSeasrchBox:I.outPutSearchMethod.bind(I)}}(window,jQuery),function(){function t(t){return t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}function e(t){return t.some(function(t){return new RegExp("^http(s)?:\\/\\/("+t+")(\\/|\\/\\?)?(spm=\\S*)?$").test(location.href)})}var n=function(){function t(){_classCallCheck(this,t),this.active={},this.loginUrl="https://passport.csdn.net/account/login?from="+encodeURIComponent(window.location.href)}return _createClass(t,[{key:"init",value:function(){this.getActiveData()}},{key:"getActiveData",value:function(){var t=this,e=this;$.ajax({url:"https://mp-activity.csdn.net/activity/report",type:"post",contentType:"application/json; charset=utf-8",xhrFields:{withCredentials:!0},data:JSON.stringify({pageUrl:window.location.href,action:"pageView",platform:"pc"}),dataType:"json",success:function(n){void 0;var o=n.data;return e.active=o,!(!o.matched||"popWindow"!==o.operationCommand)&&(o.ext.report&&-1!==o.ext.report.indexOf("exposure")&&window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:t.active.ext.spm}),"center"===o.ext.position?(e.renderCenterDom(),e.renderCenterCss()):"right"===o.ext.position?(e.renderRightDom(),e.renderRightCss()):(e.renderCenterDom(),e.renderCenterCss()),!1)},error:function(t){void 0}})}},{key:"handleClose",value:function(){var t=$("#csdn-active-dialog .closeBtn"),e=$("#csdn-active-mask");void 0,t.click(function(){void 0,$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove()}),e.click(function(){void 0,$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove()})}},{key:"handleJump",value:function(){var t=$("#csdn-active-dialog .active_main");void 0,t.click(function(){void 0,$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove()})}},{key:"renderCenterDom",value:function(){void 0
;var t='\n        <div id="csdn-active-mask" class="'+(this.active.ext.hasMask?"":"hide")+'"></div>\n        <div id="csdn-active-dialog">\n        <div class="dialog__wrapper">\n            <a \n              '+("close"!==this.active.ext.clickAction?"href='"+(this.active.ext.clickUrl||this.loginUrl)+"'":"")+"\n              "+(this.active.ext.report&&-1!==this.active.ext.report.indexOf("click")?'data-report-click=\'{"spm":'+JSON.stringify(this.active.ext.spm)+"}'":"")+'\n              data-report-query="spm='+this.active.ext.spm+'"\n              class="active_main"><img src="'+this.active.ext.backgroundPicUrl+'" alt=""></a>\n            <span class="closeBtn close_icon1"></span>\n          </div>\n        </div>\n      ';if($("body").append(t),this.handleClose(),this.handleJump(),-1!=this.active.ext.popUpDuration)var e=setTimeout(function(){$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove(),clearTimeout(e)},1e3*this.active.ext.popUpDuration)}},{key:"renderRightDom",value:function(){void 0;var t='\n        <div id="csdn-active-mask" class="'+(this.active.ext.hasMask?"":"hide")+'"></div>\n        <div id="csdn-active-dialog">\n        <div class="dialog__wrapper">\n            <span class="closeBtn close_icon2"></span>\n            <a \n            '+("close"!==this.active.ext.clickAction?"href='"+(this.active.ext.clickUrl||this.loginUrl)+"'":"")+"\n            "+(this.active.ext.report&&-1!==this.active.ext.report.indexOf("click")?'data-report-click=\'{"spm":'+JSON.stringify(this.active.ext.spm)+"}'":"")+'\n            data-report-query="spm='+this.active.ext.spm+'"\n            class="active_main"><img src="'+this.active.ext.backgroundPicUrl+'" alt=""></a>\n          </div>\n        </div>\n      ';if($("body").append(t),this.handleClose(),this.handleJump(),-1!=this.active.ext.popUpDuration)var e=setTimeout(function(){$("#csdn-active-mask").remove(),$("#csdn-active-dialog").remove(),clearTimeout(e)},1e3*this.active.ext.popUpDuration)}},{key:"renderCenterCss",value:function(){void 0;var t=$('<style type="text/css">\n                    #csdn-active-mask {\n                      position: fixed;\n                      width: 100%;\n                      height: 100%;\n                      background: rgba(0, 0, 0, 0.5);\n                      overflow: hidden;\n                      top: 0;\n                      left: 0;\n                      z-index: 2147483645;\n                    }\n                    .hide {\n                      display: none;\n                    }\n                    #csdn-active-dialog {\n                      position: fixed;\n                      overflow: hidden;\n                      top: 0;\n                      left: 0;\n                      z-index: 2147483646;\n                    }\n                    #csdn-active-dialog .dialog__wrapper {\n                      \n                      width: 400px;\n                      position: fixed;;\n                      top: 50%;\n                      left: 50%;\n                      margin-left: -200px;\n                      margin-top: -200px;\n                      box-sizing: border-box;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .active_main {\n                      width: 400px;\n                      height: 400px;\n                      cursor: pointer;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .active_main img{\n                      width: 400px;\n                      height: 400px;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .closeBtn{\n                      display: block;\n                      width: 32px;\n                      height: 32px;\n                      margin: 24px auto;\n                      cursor: pointer;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .close_icon1 {\n                      background: url("https://img-home.csdnimg.cn/images/20211202112243.png") no-repeat center center;\n                      background-size: contain;\n                    }\n                    #csdn-active-dialog .dialog__wrapper .close_icon2 {\n                      background: url("https://img-home.csdnimg.cn/images/20211208022627.png") no-repeat center center;\n                      background-size: contain;\n                    }\n                  </style>');document.head.insertBefore($(t)[0],document.head.getElementsByTagName("title")[0])}},{key:"renderRightCss",value:function(){void 0;var t=$('<style type="text/css">\n                      #csdn-active-mask {\n                        position: fixed;\n                        width: 100%;\n                        height: 100%;\n                        background: rgba(0, 0, 0, 0.5);\n                        overflow: hidden;\n                        top: 0;\n                        left: 0;\n                        z-index: 2147483645;\n                      }\n                      .hide {\n                        display: none;\n                      }\n                      #csdn-active-dialog {\n                        position: fixed;\n                        overflow: hidden;\n                        top: 0;\n                        left: 0;\n                        z-index: 2147483646;\n                      }\n                      #csdn-active-dialog .dialog__wrapper {\n                        \n                        width: 343px;\n                        position: fixed;\n                        top: 48px;\n                        right: 270px;\n                        box-sizing: border-box;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .active_main {\n                        display: block;\n                        overflow: hidden;\n                        width: 343px;\n                        height: 80px;\n                        cursor: pointer;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .active_main img{\n                        width: 343px;\n                        height: 80px;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .closeBtn{\n                        display: block;\n                        width: 28px;\n                        height: 28px;\n                        cursor: pointer;\n                        float:right\n                      }\n                      #csdn-active-dialog .dialog__wrapper .close_icon1 {\n                        background: url("https://img-home.csdnimg.cn/images/20211202112243.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-active-dialog .dialog__wrapper .close_icon2 {\n                        background: url("https://img-home.csdnimg.cn/images/20211208022627.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                  </style>');document.head.insertBefore($(t)[0],document.head.getElementsByTagName("title")[0])}}]),t}();$(document).on("toolbarReady",function(o){var a=window.csdn.toolbarData||{},r=a.activeData;void 0,r&&r.whiteList&&t(r.whiteList)&&(void 0,(new n).init()),r&&r.whiteRegexList&&e(r.whiteRegexList)&&(void 0,(new n).init())})}(),function(){!function(){function t(){_classCallCheck(this,t)}_createClass(t,[{key:"init",value:function(){"www.csdn.net"===window.location.host&&"/"===window.location.pathname&&this.renderStyle()}},{key:"renderStyle",value:function(){var t=document.createElement("style");t.innerHTML="\n        html {\n          -webkit-filter: grayscale(100%); /* webkit */\n          -moz-filter: grayscale(100%); /*firefox*/\n          -ms-filter: grayscale(100%); /*ie9*/\n          -o-filter: grayscale(100%); /*opera*/\n          filter: grayscale(100%);\n          filter:progid:DXImageTransform.Microsoft.BasicImage(grayscale=1);\n        }\n        body{\n          filter:gray; /*ie9- */\n          background : none!important;\n        }\n      ",document.querySelector("head").appendChild(t)}}])}()}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(t,e,n){var o=new Date;if(-1==n)return void(document.cookie=t+"="+escape(e)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+escape(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(t){var e=new Date;document.cookie=t+"="+escape("1")+";max-age=0;expires="+e.toGMTString()+";domain=.csdn.net;path=/"}function o(t){try{t&&$(document).trigger(t)}catch(t){void 0}}function a(t,e){var n=[603,604,605,606,607];void 0===e&&(e=(d?539:536)+","+n.toString());var o="https://kunpeng.csdn.net/ad/json/list?positions="+e;$.ajax({url:o,type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(n){if(200===n.code&&n.data){if(n.data.length>0){var o=n.data[0];if(-1===[536,539,541].indexOf(o.adPositionId))return;switch(o.adType){case"baodeng":var d=n.data.reduce(function(t,e,n){return 0===n?t=Object.assign({},e,{children:[]}):t.children.push(e),t},{});void 0,r(d);break;case"code":i(o)}}}else csdn&&csdn.toolbarData&&csdn.toolbarData.advert&&csdn.toolbarData.advert.checkPlugin&&541!==e&&setTimeout(function(){!s()&&!c()||l()||a(t,541)},5e3)}})}function r(a){var r=!!t("is_advert");if(!a)return void n("is_advert");var i="width:100%; height:100%; background-image: url("+a.imgUrl+"); background-size: auto 80px;background-repeat: no-repeat; background-position: center center;",s="width:100%; height:100%; background-image: url("+a.bigImgUrl+"); background-size: auto 320px;background-repeat: no-repeat; background-position: center center;",c=$('<div class="toolbar-advert">\n            <a href="'+a.clickUrl+'" target="_blank" style="background: '+a.backgroundColor+';" class="toolbar-advert-default '+(r?"":"toolbar-advert-lg")+'"><div style="'+(r?i:s)+'"></div>\n              '+(a.children.length?'<span class="toolbar-advert-more">'+a.children.map(function(t){return'<span class="toolbar-advert-more-item" data-adress="'+t.clickUrl+'" style="background-image: url('+t.imgUrl+'); background-size: contain;background-repeat: no-repeat; background-position: center center;"><img style="width:0;height:0;display:none;" src="'+t.exposureUrl+'"/></span>'}).join("")+"</span>":"")+'\n            </a>\n            <span class="toolbar-adver-btn">关闭</span>\n            <img style="width:0;height:0;display:none;" src="'+a.exposureUrl+'"/>\n          </div>');c.find(".toolbar-adver-btn").click(function(t){return c.remove(),o("toolbarHeightChange"),a.closeAdClickUrl&&$.ajax({url:a.closeAdClickUrl,type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(t){}}),!1}),c.on("click",".toolbar-advert-more-item",function(t){var e=$(this).data("adress")?$(this).data("adress"):"";return e&&window.open(e,"_blank"),t.stopPropagation(),!1}),$("#csdn-toolbar").prepend(c),o("toolbarHeightChange"),!r&&e("is_advert","1",864e5),c.on("transitionend",function(t){o("toolbarHeightChange")}),setTimeout(function(){!r&&$(".toolbar-advert-default").removeClass("toolbar-advert-lg").find("div").attr("style",i),o("toolbarHeightChange")},1e3*(a.showSeconds||5))}function i(t){var e=$('<div class="toolbar-advert"></div>');if(t.content&&e.append(t.content),t.exposureUrl){var n=$('<img width="0" height="0" style="display:none;" src="'+t.exposureUrl+'">');e.append(n)}o("toolbarHeightChange"),$("#csdn-toolbar").prepend(e),setTimeout(function(){o("toolbarHeightChange")},200)}function s(){var t=$('<div class="adsbox ad_box ads_box"></div>');if($("#csdn-toolbar").append(t),t.is(":hidden"))return t.remove(),!0;t.remove()}function c(){return $("#greenerSettings").length}function l(){return $("#open_chromePlugin_tab").length}var d=!1;$(document).on("toolbarReady",function(t){void 0,void 0,void 0,(window.csdn.toolbarData.advert.blacklist||[]).indexOf(location.host)>=0||a()})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(t,e,n){var o=new Date;if(-1==n)return void(document.cookie=t+"="+e+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+e+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(t){return t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}var o=function(){function n(){_classCallCheck(this,n),this.allData=[],this.barrageList=[],this.barrageDomArr=[],this.fnArr=[],this.barrageBoxWidth=400,this.pollDelay=30,this.timer=null,this.errorCount=0,this.toolbarHeight=44}return _createClass(n,[{key:"init",value:function(){"1"!==t("hideBarrage")&&(this.getToolbarHeight(),this.insertCss(),this.queryBarrage(),this.bindToolbarHeightChange(),this.handlePageVisibilityChange())}},{key:"queryBarrage",value:function(){var t=this;$.ajax({url:"https://barrage-kunpeng.csdn.net/api/barrage/list",type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){if(200===e.code){if(t.pollDelay=e.pollDelay,!e.data.length)return void t.setTimer();t.allData=e.data,t.barrageList=t.getBarrageList(),t.renderBarrage(),t.start(),t.insertAdCookie(),t.resetErrorCount()}else 201!==e.code&&t.handleQueryError()},error:function(e){t.handleQueryError()}})}},{key:"handleQueryError",value:function(){this.errorCount++,this.setTimer(),this.errorCount>10&&clearTimeout(this.timer)}},{key:"getBarrageList",value:function(){var t=this.allData.slice(0,3);return this.allData=this.allData.slice(3),t.map(function(t){return Object.assign({},t,{x:0,speed:2+Number(Math.random().toFixed(1)),stop:!1})})}},{key:"start",value:function(){for(var t=this,n=document.documentElement.clientWidth,o=0,a=0;a<this.barrageDomArr.length;a++)!function(a){t.fnArr.push(function(){if(t.barrageList[a].x-=t.barrageList[a].speed,t.barrageDomArr[a].style.transform="translateX("+t.barrageList[a].x+"px)",n+t.barrageList[a].x>=-t.barrageBoxWidth&&!t.barrageList[a].stop)requestAnimationFrame(t.fnArr[a]);else if(n+t.barrageList[a].x<-t.barrageBoxWidth&&!t.barrageList[a].stop&&++o===t.barrageDomArr.length){if(t.fnArr=[],$("#barrageBox").remove(),!t.checkIsPassport()){var r=t.getTomorrowTimeRemaining();e("ad_last_time",Date.now(),r)}t.barrageList=t.getBarrageList(),t.barrageList.length?(t.renderBarrage(),t.start(),t.insertAdCookie()):t.setTimer()}})}(a);for(var r=0;r<this.barrageList.length;r++)!function(e){requestAnimationFrame(t.fnArr[e]),setTimeout(function(){window.csdn.report.reportView({mod:"popu_894",extend1:t.barrageList[e].barrageId,dest:t.barrageList[e].clickUrl})},1e3)}(r)}},{key:"insertCss",value:function(){var t=document.getElementsByTagName("head")[0],e=document.createElement("style");e.type="text/css",e.appendChild(document.createTextNode("\n        #barrageBox .barrage-item {\n          position:relative;\n          display: flex;\n          align-items: center;\n          height: 40px;\n          margin-bottom: 62px;\n          border-radius: 0 100px 100px 0;\n          width: fit-content;\n        }\n        #barrageBox .barrage-item.barrage-link-hide {\n          visibility: hidden;\n        }\n        #barrageBox .barrage-item .barrage-follow {\n          cursor: pointer;\n          position: absolute;\n          left: 8px;\n          bottom: -9px;\n          z-index: 999;\n          border: none;\n          outline: none;\n          font-size: 12px;\n          font-weight: 500;\n          line-height: 17px;\n          color: #FFFFFF;\n          -webkit-background-clip: text;\n          -webkit-text-fill-color: transparent;\n        }\n        #barrageBox .barrage-item .barrage-follow.is-follow {\n          left: 2px\n        }\n        #barrageBox .barrage-item .barrage-link {\n          position: relative;\n          display: flex;\n          align-items: center;\n          color: #fff;\n          font-size: 14px;\n          font-weight: 500;\n          height: 100%;\n          padding-right: 24px;\n          min-width: 294px;\n        }\n        #barrageBox .barrage-item .barrage-link img {\n          width: 38px;\n          height: 38px;\n          display: block;\n          border-radius: 50%;\n          margin-right: 27px;\n        }\n        #barrageBox .barrage-item .barrage-link .barrage-content {\n          max-width: 592px;\n          overflow: hidden;\n          text-overflow: ellipsis;\n          white-space: nowrap;\n        }\n        #barrageBox .barrage-item .barrage-link .barrage-bg {\n          width: 320px;\n          height: 80px;\n          position: absolute;\n          left: -26px;\n          top: 50%;\n          transform: translateY(-50%);\n        }\n        #barrageBox .barrage-item .barrage-right {\n          position: relative;\n          height: 100%;\n          width: 44px;\n          display: flex;\n          align-items: center;\n        }\n        #barrageBox .barrage-item .barrage-right .barrage-close {\n          width: 100%;\n          height: 100%;\n          position: absolute;\n          background: url('https://img-home.csdnimg.cn/images/20201104030927.png') no-repeat center;\n          background-size: 16px 16px;\n          cursor: pointer;\n        }\n        #barrageBox .barrage-item .barrage-right .barrage-right-border {\n          width: 1px;\n          height: 20px;\n          background: rgba(255, 255, 255, 0.3);\n        }\n      ")),t.appendChild(e)}},{key:"renderBarrage",value:function(){for(var n="",o=0;o<this.barrageList.length;o++){var a=this.barrageList[o].barrageId,r=this.barrageList[o].clickUrl;n+='\n          <div class="barrage-item" style="background-color: '+this.barrageList[o].bgColor+"; box-shadow: -12px 4px 14px 0 "+this.barrageList[o].shadowColor+';">\n          '+(this.barrageList[o].type?"":'<button class="barrage-follow" data-username="'+this.barrageList[o].username+'">关注</button>')+'\n            <a class="'+(this.barrageList[o].type?"barrage-link barrage-link-redpack":"barrage-link")+'" href="'+(this.barrageList[o].type?"javascript:;":r)+'" target="'+(this.barrageList[o].type?"":"_blank")+'" data-report-click=\'{"mod":"popu_894","extend1": "'+a+'", "dest": "'+r+'"}\'>\n              <img src="'+this.barrageList[o].headImage+'" alt="">\n              <div class="barrage-content">'+this.barrageList[o].nickname.slice(0,10)+"："+this.barrageList[o].content+'</div>\n              <div class="barrage-bg" style="background: url('+this.barrageList[o].bgImage+') no-repeat center; background-size: cover;"></div>\n            </a>\n            <div class="barrage-right">\n              <div class="barrage-close"></div>\n              <div class="barrage-right-border"></div>\n            </div>\n          </div>\n        '}$("#csdn-toolbar").append('<div id="barrageBox" style="position: fixed; z-index: 99999; top: '+(this.toolbarHeight+32)+'px;">'+n+"</div>"),this.barrageDomArr=[].concat(_toConsumableArray(document.querySelectorAll("#barrageBox .barrage-item")));var i=$("#barrageBox");this.barrageBoxWidth=i.width()+26,i.css({right:-this.barrageBoxWidth+"px"});var s=this;i.on("mouseenter",".barrage-item",function(){s.barrageList[$(this).index()].stop=!0}),i.on("mouseleave",".barrage-item",function(){s.barrageList[$(this).index()].stop=!1,requestAnimationFrame(s.fnArr[$(this).index()])}),i.on("click",".barrage-close",function(){$("#barrageBox").remove(),clearTimeout(s.timer),e("hideBarrage","1",s.getTomorrowTimeRemaining())}),i.on("click",".barrage-follow",function(){$(this).hasClass("is-follow")||s.follow(s.barrageList[$(this).parent().index()].username)}),i.on("click",".barrage-link.barrage-link-redpack",function(){if(t("UserName")){if(!$(this).hasClass("already")){var e=$(this).parent().index();window.csdn.barrageRedpack&&window.csdn.barrageRedpack.open(s.barrageList[e].typeId),$(this).addClass("already").parent().addClass("barrage-link-hide")}}else window.location.href="https://passport.csdn.net/account/login?from="+encodeURIComponent(window.location.href)})}},{key:"insertAdCookie",value:function(){var n=t("ad_barrage_ids")||"",o=n?n.split(","):[];for(o=[].concat(_toConsumableArray(new Set(o.concat(this.barrageList.filter(function(t){return t.isAddCookie}).map(function(t){return t.barrageId})))));o.length>500;)o.shift();e("ad_barrage_ids",o.join(","),864e5)}},{key:"setTimer",value:function(){var t=this;clearTimeout(this.timer),this.timer=setTimeout(function(){t.queryBarrage()},1e3*this.pollDelay)}},{key:"resetErrorCount",value:function(){this.errorCount=0}},{key:"bindToolbarHeightChange",value:function(){var t=this;$(document).on("toolbarHeightChange",function(){t.getToolbarHeight();var e=$("#barrageBox");e.length&&e.css("top",t.toolbarHeight+32+"px")})}},{key:"getToolbarHeight",value:function(){var t=$("#csdn-toolbar");this.toolbarHeight=t.length?t.height():44}},{key:"handlePageVisibilityChange",value:function(){var t,e,n=this;void 0!==document.hidden?(t="hidden",e="visibilitychange"):void 0!==document.msHidden?(t="msHidden",e="msvisibilitychange"):void 0!==document.webkitHidden&&(t="webkitHidden",e="webkitvisibilitychange");var o=function(){document[t]?clearTimeout(n.timer):!$("#barrageBox").length&&n.setTimer()};document.addEventListener(e,o,!1)}},{key:"getTomorrowTimeRemaining",value:function(){var t=(new Date).getFullYear()+"/"+((new Date).getMonth()+1)+"/"+((new Date).getDate()+1);return new Date(t)-Date.now()}},{key:"follow",value:function(e){var n=this;t("UserName")?t("UserName")!==e&&$.ajax({url:"https://me.csdn.net/api/relation/create",type:"post",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},data:JSON.stringify({username:e}),dataType:"json",success:function(t){n.changeFollowStatus(e)},error:function(t){n.changeFollowStatus(e)}}):window.location.href="https://passport.csdn.net/account/login?from="+encodeURIComponent(window.location.href)}},{key:"changeFollowStatus",value:function(t){$("#barrageBox").find(".barrage-item").each(function(){$(this).find(".barrage-follow").attr("data-username")===t&&$(this).find(".barrage-follow").addClass("is-follow").text("已关注")})}},{key:"checkIsPassport",value:function(){return!!~window.location.href.indexOf("passport.csdn.net")}}]),n}();$(document).on("toolbarReady",function(){var t=window.csdn.toolbarData.barrageData.whiteList;t.length&&n(t)&&(new o).init()})}(),function(){function t(){return n("UserName")}function e(t){window.csdn&&window.csdn.loginBox&&window.csdn.loginBox.show?window.csdn.loginBox.show(t):window.location.href="https://passport.csdn.net/account/login"+(t?"?spm="+t.spm:"")}function n(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}var o=!1,a=function(){function t(){_classCallCheck(this,t),this.container=$(".toolbar-btn.toolbar-btn-collect"),this.data=[],this.history={},this.currentIndex=0,this.isHideOnce=!1,this.finishInit=!1,this.timer=null,this.init()}return _createClass(t,[{key:"init",value:function(){this.getCollectionFolder()}},{key:"getCollectionFolder",value:function(){var t=this;$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v2/favorites-list",type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){if(200===e.code){if(t.data=e.data.favoritesList,!t.data.length)return;t.data[0].contentList=e.data.contentList,t.render(),t.finishInit=!0}},error:function(t){void 0}})}},{key:"getCollectionContent",value:function(t){var e=this;$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-favorite-content?id="+t,type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){200===t.code&&(e.data[e.currentIndex].contentList=t.data,e.render())},error:function(t){void 0}})}},{key:"bindToggleFolder",value:function(){var t=this;$("#csdn-toolbar-collection").on("click",".toolbar-collection-left ul li",function(){$(this).index()!==t.currentIndex&&(t.currentIndex=$(this).index(),$(this).addClass("collection-folder-active").siblings().removeClass("collection-folder-active"),t.data[t.currentIndex].contentList?t.render():t.getCollectionContent(t.data[t.currentIndex].id))})}},{key:"bindToMore",value:function(){var t=this;$("#csdn-toolbar-collection").on("click",".toolbar-collection-more",function(){var e=0==t.currentIndex?"https://i.csdn.net/#/user-center/history":"https://i.csdn.net/#/user-center/collection-list?type=1&folder="+t.data[t.currentIndex].id+"&key="+(t.currentIndex-1);window.open(e,"_blank")})}},{key:"render",value:function(){if(this.finishInit)$("#csdn-toolbar-collection .toolbar-collection-right").empty().append(this.renderRight());else{var t='\n          <div id="csdn-toolbar-collection" class="csdn-toolbar-plugin">\n          '+(this.data.length<=1?"":'<div class="toolbar-collection-left csdn-toolbar-scroll-box">\n          <ul>'+this.renderLeft()+"</ul>\n        </div>")+'\n            \n            <div class="toolbar-collection-right">\n              '+this.renderRight()+'\n            </div>\n            <div class="csdn-toolbar-plugin-triangle"></div>\n          </div>\n        ';this.container.append(t),1==this.data.length&&$(".toolbar-collection-left").hide(),this.isHideOnce&&$("#csdn-toolbar-collection").hide(),this.bindToggleFolder(),this.bindToMore()}}},{key:"renderLeft",value:function(){for(var t="",e=0;e<this.data.length;e++){var n=this.data[e].name.replace(/</g,"&lt;").replace(/>/g,"&gt;");t+="\n          <li"+(e===this.currentIndex?' class="collection-folder-active"':"")+'>\n            <div class="toolbar-collection-folder-name">'+n+"</div>\n            "+(0==e?"":' <div class="toolbar-collection-folder-count">'+this.data[e].num+"</div>")+"\n          </li>\n        "}return t}},{key:"renderRight",value:function(){if(this.data[this.currentIndex].contentList.length){for(var t="",e=0;e<this.data[this.currentIndex].contentList.length;e++){var n=this.data[this.currentIndex].contentList[e];t+='\n          <li>\n            <a rel="nofollow" href="'+n.url+'" target="_blank">\n              <span '+("其他"===n.source?'class="toolbar-collection-type toolbar-collection-otherType"':'class="toolbar-collection-type"')+">"+n.source+'</span>\n              <span class="toolbar-collection-title">'+n.title+"</span>\n            </a>\n          </li>\n        "}return'<ul class="csdn-toolbar-scroll-box">'+t+"</ul>"+(this.data[this.currentIndex].num>15?'<a rel="nofollow" class="toolbar-collection-more">查看更多<i></i></a>':"")}return'\n          <div class="toolbar-collection-empty">\n            <div>空空如也</div>\n          </div>\n        '}},{key:"show",value:function(){clearTimeout(this.timer),this.timer=setTimeout(function(){o&&$("#csdn-toolbar-collection").stop().fadeIn(100)},150)}},{key:"hide",value:function(){this.isHideOnce=!0,clearTimeout(this.timer),o||$("#csdn-toolbar-collection").stop().fadeOut(100)}}]),t}(),r=function(){function t(){_classCallCheck(this,t),this.container=$(".toolbar-btn.toolbar-btn-collect"),this.init()}return _createClass(t,[{key:"init",value:function(){var t=$('<div id="csdn-toolbar-collection-nologin" class="csdn-toolbar-plugin">\n      <div class="csdn-toolbar-plugin-triangle"></div>\n      <div class="csdn-toolbar-collection-top">登录即可查看浏览历史和收藏</div>\n      <a rel="nofollow" class="csdn-toolbar-loginbtn" data-report-click=\'{"spm":"3001.8845"}\'>立即登录</a>\n</div>');this.container.append(t),t.find("a.csdn-toolbar-loginbtn").on("click",function(){var t=$(this).data("report-click");$("#csdn-toolbar-collection-nologin").hide(),t?e(t):e()})}},{key:"show",value:function(){clearTimeout(this.timer),this.timer=setTimeout(function(){o&&$("#csdn-toolbar-collection-nologin").stop().fadeIn(100)},150)}},{key:"hide",value:function(){this.isHideOnce=!0,clearTimeout(this.timer),o||$("#csdn-toolbar-collection-nologin").stop().fadeOut(100)}}]),t}();$(document).on("toolbarReady",function(){if(t()){var e=null,n=$(".toolbar-btn.toolbar-btn-collect");n.on("mouseenter",function(){o=!0,e?e.show():e=new a}),n.on("mouseleave",function(){o=!1,e.hide()})}else{void 0;var i=null,s=$(".toolbar-btn.toolbar-btn-collect");s.on("mouseenter",function(){o=!0,i?i.show():i=new r}),s.on("mouseleave",function(){o=!1,i.hide()})}})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t)return decodeURIComponent(o[1])}}function e(t,e,n){var o=new Date;if(-1==n)return void(document.cookie=t+"="+escape(e)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+escape(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}var n=(t("c_segment")&&parseInt(t("c_segment")),t("creative_btn_mp")?parseInt(t("creative_btn_mp")):0),o="",a=0,r=$('meta[name="toolbar"]'),i=0;if(r.length){var s=r.attr("content")||{};s=JSON.parse(s),i=s.type||i}else i=function(t){var e={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];var a=n[1],r=n[2];e[a]=r}),t?e[t]:e}("toolbarSkinType")||i;a=i;var c=function(){function r(){_classCallCheck(this,r),this.container=$(".toolbar-btn.toolbar-btn-mp"),this.init()}return _createClass(r,[{key:"init",value:function(){this.render()}},{key:"render",value:function(){var n=t("UserName"),a=t("creativeSetApiNew");if(a=a?JSON.parse(a):null,a=a&&a.userName===n?a:null,n&&a){var r=0;0===a.articleNum?a.toolbarImg?(o.newuser={def:a.toolbarImg,dark:a.toolbarImg,left:3===a.type?"-130":"-148"},r=0===a.type?3:2):r=1:a.articleNum>=0&&a.toolbarImg&&(o.olduser={def:a.toolbarImg,dark:a.toolbarImg,left:3===a.type?"-130":"-148"}),this.creativeSet(r,o)}else{var i=this;n?$.ajax({url:"https://blog.csdn.net/phoenix/web/v1/get-novice-period-info",type:"get",xhrFields:{withCredentials:!0},success:function(t){var a=0;if(200===t.code&&t.data){0===t.data.articleNum?t.data.toolbarImg?(a=0===t.data.type?3:2,o.newuser={def:t.data.toolbarImg,dark:t.data.toolbarImg,left:3===a?"-130":"-148"}):a=1:t.data.toolbarImg&&(o.olduser={def:t.data.toolbarImg,dark:t.data.toolbarImg,left:3===a?"-130":"-148"});var r=t.data;r.userName=n,e("creativeSetApiNew",JSON.stringify(r),3e5)}i.creativeSet(a,o)}}):this.creativeSet(0,o)}}},{key:"creativeSet",value:function(t,o){if(this.container.append('\n        <div class="csdn-toolbar-creative-mp" style="display:none">\n          <a href="https://mp.csdn.net/edit" data-report-query="spm=3001.9762" data-report-click=\'{"spm":"3001.9762","extra":'+JSON.stringify({dataType:t})+'}\'><img class="csdn-toolbar-creative-mp-bg" src="https://img-home.csdnimg.cn/images/20230815023727.png" alt=""></a> \n          <img class="csdn-toolbar-creative-mp-close" src="'+(1==a?"https://img-home.csdnimg.cn/images/20230815023232.png":"https://img-home.csdnimg.cn/images/20230815023238.png")+'" alt="">\n        </div>\n      '),o){var r="",i="",s="olduser";t>0&&(s="newuser"),r=1==a?o[s].dark:o[s].def,i=o[s].left+"px",$(".csdn-toolbar-creative-mp-bg").attr("src",r),
$(".csdn-toolbar-creative-mp").css("left",i);var c="old";t>0&&n<3?($(".csdn-toolbar-creative-mp").show(),c="new",window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9743",extra:{dataType:"new"}})):n<3&&(n=parseInt(n)+1,e("creative_btn_mp",n,864e5),$(".csdn-toolbar-creative-mp").show(),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9743",extra:{dataType:"old"}})),$(".csdn-toolbar-creative-mp-close").on("click",function(){$(".csdn-toolbar-creative-mp").hide(),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportClick({spm:"3001.9744",extra:{dataType:c}}),e("creative_btn_mp",3,864e5)})}}},{key:"hide",value:function(){$("#csdn-toolbar-write").stop().fadeOut(100)}}]),r}();$(document).on("toolbarReady",function(){var t=null;o=window.csdn.toolbarData.toolbarBtnMpAsk||"",t||(t=new c)})}(),function(){function t(){return window.csdn.toolbarData.menus.filter(function(t){return t.slider&&t.slider.list&&t.slider.list.length>0})}var e=function(){function t(e){_classCallCheck(this,t),this.sliderArray=e,this.init()}return _createClass(t,[{key:"init",value:function(){this.initSliderScroll()}},{key:"initSliderScroll",value:function(){var t=this;this.sliderArray.forEach(function(e){$(".toolbar-subSlider-"+e.id).children().length&&t.sliderScroll(".toolbar-subSlider-"+e.id,$(".toolbar-subSlider-"+e.id).children().length,e.slider.time||4)})}},{key:"sliderScroll",value:function(t,e,n){var o=0,a=$(t);setInterval(function(){o===e-1?(o+=1,a.append(a.children().first().clone()),a.css({"margin-top":-48*o+"px",transition:"all 0.8s"})):o>=e?(o=0,a.css({"margin-top":"0px",transition:"none"}),a.children("a").last().remove()):(o+=1,a.css({"margin-top":-48*o+"px",transition:"all 0.8s"}))},1e3*n),a.on("transitionend",function(){o>=e&&(o=0,a.css({"margin-top":"0px",transition:"none"}),a.children("a").last().remove())})}}]),t}();$(document).on("toolbarReady",function(){var n=t();n.length&&new e(n)})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(t,e,n){var o=new Date;if(-1==n)return void(document.cookie=t+"="+escape(e)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+escape(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(t){return t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}function o(t){window.csdn&&window.csdn.report&&window.csdn.report.reportClick(t)}function a(t){window.csdn&&window.csdn.report&&window.csdn.report.reportView(t)}function r(){this.closeTime=6,this.isLogin=t("UserName"),this.countDown="",this.radioValue="",this.reportUV="3001.6429",this.IPurl=i?"https://position.csdnimg.cn/oapi/get?ipAddr=***********":"https://position.csdnimg.cn/oapi/get",this.certUrl="https://i.csdn.net/#/user-center/profile?floor=edu&highSchool=true",this.closeValue=t("UserName")?"close":"nologin_close",this.isBlogDetail=window.location.href.indexOf("blog.csdn.net")>-1&&window.location.href.indexOf("/article/details")>-1,~location.href.indexOf("blog.csdn.net")&&!this.isBlogDetail||this.init()}var i=!1;r.prototype.init=function(){this.isHighSchoolIp()},r.prototype.render=function(){var t=this;this.$main=$('<div id="csdn-highschool-window" class="csdn-highschool-window '+(this.isBlogDetail?"csdn-highschool-blog-window":"")+'">\n      <img class="csdn-highschool-close" src="https://g.csdnimg.cn/common/csdn-toolbar/images/high-school-close.png"/>\n      <img class="csdn-highschool-monkey" src="https://g.csdnimg.cn/common/csdn-toolbar/images/high-school-monkey.png" />\n      <span class="csdn-highschool-countdown"></span>\n    </div>'),this.isLogin?this.hasCert||this.renderSelect():this.renderSelect(),this.$main.find(".csdn-highschool-close").on("click",function(){t.close(t.closeValue)}),$("body").append(this.$main),a({spm:this.reportUV})},r.prototype.renderSelect=function(){var t=this,e=$('<div class="csdn-highschool-select">\n      <p>认证学生身份,立享VIP折扣</p>\n      <div class="highschool-container">\n        <p>您是否为学生:</p>\n        <div class="highschool-select">\n          <label class="highschool-radio">\n            <input type="radio" name="type" id="highschool-radio-isStudent" value="cert" hidden/>\n            <label for="highschool-radio-isStudent" ></label>\n            <span class="radio-name">是,立即认证</span>\n          </label>\n          <label class="highschool-radio">\n            <input type="radio" name="type" id="highschool-radio-notStudent" value="notcert" hidden/>\n            <label for="highschool-radio-notStudent" ></label>\n            <span class="radio-name">否,立即关闭</span>\n          </label>\n        </div>\n      </div>\n      <div class="highschool-submit cannot-select">确定</div>\n    </div>');e.find(".highschool-radio").on("click",function(e){t.$main.find(".csdn-highschool-countdown").remove(),$(".highschool-submit").removeClass("cannot-select"),clearInterval(t.countDown),t.countDown=""}).on("click","input",function(e){t.radioValue=e.target.value}),e.find(".highschool-submit").on("click",function(){t.radioValue&&("cert"===t.radioValue?(window.open(t.certUrl,"_blank"),o({spm:t.reportUV,extend1:"是"})):o({spm:t.reportUV,extend1:"否"}),t.close(t.closeValue))}),this.$main.append(e)},r.prototype.renderAccount=function(){var t=this,e=$('<div class="csdn-highschool-account">\n      <p>高校社区扬帆起航 虚位以待静候卿来</p>\n      <div class="highschool-container">\n        <p class="highschool-name">'+this.schoolName+'</p>\n        <p class="highschool-desc">加入社区立即体验,参与校友互动</p>\n      </div>\n      <div class="highschool-submit">立即前往</div>\n    </div>');e.find(".highschool-submit").on("click",function(){window.open(t.schoolCommunityUrl,"_blank"),t.close(t.closeValue)}),this.$main.append(e)},r.prototype.isHighSchoolIp=function(){var t=this;$.ajax({url:this.IPurl,type:"get",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){void 0,e&&(t.isLogin?t.getAccountInfo():(t.render(),t.count()))},error:function(t){void 0}})},r.prototype.getAccountInfo=function(){var t=this;$.ajax({url:"https://g-api.csdn.net/community/personal-api/v1/get-school-community",type:"get",contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(e){void 0,200===e.code&&e.data&&e.data.studentCertification?(t.hasCert=!0,t.reportUV="3001.6434",t.schoolName=e.data.schoolName,t.schoolCommunityUrl=e.data.schoolCommunityUrl):(t.render(),t.count())},error:function(e){t.render(),t.count(),void 0}})},r.prototype.close=function(t){this.$main.remove(),clearInterval(this.countDown),this.countDown="",e("csdn_highschool_close",t||"close",864e5)},r.prototype.count=function(){var t=this,e=this.closeTime;this.countDown=setInterval(function(){0===e?t.close(t.closeValue):(t.$main.find(".csdn-highschool-countdown").html(e+"秒"),e--)},1e3)},$(document).on("toolbarReady",function(e){var o=window.csdn.toolbarData||{},a=o.highSchoolData;a&&a.whiteList&&n(a.whiteList)&&(!t("csdn_highschool_close")||t("UserName")&&"nologin_close"===t("csdn_highschool_close"))&&(window.csdn.highSchool=new r)})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t)return decodeURIComponent(o[1])}}function e(t,e,n){var o=new Date;if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+encodeURIComponent(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window;return t&&$(t).height()}function o(){return!!t("needShowLoginBoxAuto")}function a(){return t("UserName")}function r(){var t=!window.csdn||void 0===window.csdn.needShowLoginBoxAuto||!!window.csdn.needShowLoginBoxAuto;window.csdn&&window.csdn.loginBox&&t&&!o()&&!a()&&(setTimeout(function(){try{window.csdn.loginBox.show()}catch(t){void 0}},800),e("needShowLoginBoxAuto","1",60*c.hours*60*1e3))}function i(){$(window).on("scroll",function(t){var e=$(this).scrollTop(),o=n(document),a=n(window);void 0,e>=(o-a)*c.ratio&&r()})}function s(t){return t&&t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}var c={whiteList:["loc-toolbar.csdn.net"],blackList:[],ratio:.5,hours:6};try{window.csdn=window.csdn||{},csdn.loginBox=csdn.loginBox||{},csdn.loginBox.loginBoxParams={isClosedBtn:!0}}catch(t){void 0}$(document).on("toolbarReady",function(t){var e=window.csdn.toolbarData||{};if(c=Object.assign({},c,e.loginBoxData),void 0,c&&s(c.whiteList)&&!s(c.blackList)){var o=n(document),a=n(window);void 0,(o-a)*c.ratio<100?(void 0,r()):(void 0,i())}})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(t){window.csdn&&window.csdn.report&&window.csdn.report.reportClick(t)}function n(){var t=new Date;return t.setDate(t.getDate()+1),t.setHours(0),t.setMinutes(0),t.setSeconds(0),t}function o(){var t=window.location.href;return s.some(function(e){return t.startsWith(e)})}function a(t){$.ajax({url:"https://kunpeng.csdn.net/ad/json/integrate/list?positions=932",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(e){if(200===e.code&&e.data){if(e.data.length>0){var n=e.data[0];t(n)}}else;}})}function r(t){if(t&&t.file2){var o=$('<div class="csdn-common-logo-advert">\n      <img class="logo-advert-close" src="https://img-operation.csdnimg.cn/csdn/silkroad/img/1666168836091.png" />\n      <img class="logo-advert-back link-img" src='+t.file2+" />\n      "+(t.file1?'<div class="logo-advert-bubble"><img class="link-img" src='+t.file1+" /></div>":"")+"\n    </div>"),a=$(t.con).find("img.pre-img-lasy");a.attr("data-src",a.attr("data-src")+"&timestamp="+Date.now()),o.append(a),o.on("click",".link-img",function(){t.clickUrl&&window.open(t.clickUrl)}).on("click",".logo-advert-close",function(){t.spm&&e({spm:t.spm}),document.cookie="logo_advert_close="+escape(1)+";expires="+n()+";domain=.csdn.net;path=/",o.remove()}),i(o,[".csdn-side-toolbar",".so-fixed-menus"])}}function i(t,e){var n=0,o=setInterval(function(){n++,e.forEach(function(e){var o=$(e);o.length&&(n=15,setTimeout(function(){o.prepend(t),"live.csdn.net"===window.location.host&&$(o).attr("style","z-index: 20000"),setTimeout(function(){window.csdn.trackad.checkImgs()},100)},1e3))}),n>=15&&(clearInterval(o),o=null)},1e3)}var s=["https://download.csdn.net","https://mall.csdn.net/vip","https://www.csdn.net/vip","https://vip.csdn.net"];!function(){t("logo_advert_close")||o()||$(document).on("toolbarReady",function(t){a(r)})}()}(),function(){function t(t){return t&&t.el&&t.url}function e(t){return t=t.split("?")[0],t.indexOf(".csdn.net")>-1}function n(t){return/^#/g.test(t)}function o(o){t(o)?(void 0,$(o.el).on("click","a",function(t){var a=$(this).attr("href")||"",r=e(a)?a:o.url+(o.url.indexOf("?")>-1?"&":"?")+"target="+encodeURIComponent(a);t.preventDefault(),void 0,a&&!n(a)&&window.open(r,"_blank")})):void 0}window.csdn=window.csdn||{},window.csdn.middleJump=o}(),function(){!function(t,e){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,e&&e())}:n.onload=function(){e&&e()},n.src=t,document.getElementsByTagName("head")[0].appendChild(n)}("https://g.csdnimg.cn/common/csdn-notification/csdn-notification.js",function(){function t(t){if(303===t.cmdId){var n=t.body,o={spm:"3001.6004",dest:n.url,extra:JSON.stringify(JSON.parse(n.report_data).data.extra)},a='<div class="notification-item" data-report-view=\''+JSON.stringify(o)+'\'>\n        <span class="notification-close"></span>\n        <div class="notification-info">'+n.description+'</div>\n        <div class="notification-bottom">\n          <div class="notification-title">'+n.popularityWord+'</div>\n          <a href="'+n.url+'" target="_blank" class="notification-options" data-report-click=\''+JSON.stringify(o)+'\' data-report-query="spm=3001.6004">'+n.jumpTag+"</a>\n        </div>\n        </div>";csdn.report&&csdn.report.viewCheck(),e.create(a)}if(304===t.cmdId){var n=t.body,o=n.report_data?{spm:"3001.6004",dest:n.url,extra:JSON.stringify(JSON.parse(n.report_data).data.extra)}:{},a='<div class="notification-item" data-report-view=\''+JSON.stringify(o)+'\'>\n        <span class="notification-close"></span>\n        <div class="notification-info">'+n.description+'</div>\n        <div class="notification-bottom">\n          <span class="notification-footer">'+n.time+'</span>\n          <a href="'+n.url+'" target="_blank" class="notification-options" data-report-click=\''+JSON.stringify(o)+'\' data-report-query="spm=3001.6004">'+n.jumpTag+"</a>\n        </div>\n        </div>";csdn.report&&csdn.report.viewCheck(),e.create(a)}}var e;setTimeout(function(){e=new CsdnNotification({top:"50px",right:"24px",time:9e3},t)},3e4)})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(t,e,n){var o=new Date;if(-1==n)return void(document.cookie=t+"="+escape(e)+";domain=.csdn.net;path=/");if(n)o.setTime(o.getTime()+n);else{o.setTime(o.getTime()+2592e6)}document.cookie=t+"="+escape(e)+";expires="+o.toGMTString()+";domain=.csdn.net;path=/"}function n(){return"https://profile-avatar.csdnimg.cn/default.jpg!3"}function o(t){var e={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];var a=n[1],r=n[2];e[a]=r}),t?e[t]:e}function a(){return t("UserName")}function r(t){window.csdn&&window.csdn.loginBox&&window.csdn.loginBox.show?window.csdn.loginBox.show(t):window.location.href="https://passport.csdn.net/account/login"+(t?"?spm="+t.spm:"")}function i(){return!!+(t("p_uid")||"").substr(1,1)}function s(){return"--"}function c(){var t=0,e=$('meta[name="toolbar"]');if(e.length){var n=e.attr("content")||{};n=JSON.parse(n),t=n.type||t}else t=o("toolbarSkinType")||t;return t}function l(){this.data={},this.isVip=i(),this.isRender=!1,this.$box=$(".toolbar-btn.toolbar-btn-login"),this.nickName=s(),this.userName=t("UserName"),this.avatar=n(),this.list=[{name:"个人中心",url:"https://i.csdn.net/#/user-center/profile",report:{dest:"https://i.csdn.net/#/user-center/profile",spm:"3001.5111"},icon:"csdn-profile-icon-person",class:""},{name:"内容管理",url:"https://mp.csdn.net/mp_blog/manage/article?spm=1011.2124.3001.5298",report:{dest:"https://mp.csdn.net/mp_blog/manage/article?spm=1011.2124.3001.5298",spm:"3001.5448"},icon:"csdn-profile-icon-pages",class:""},{name:"我的学习",url:"https://edu.csdn.net?utm_source=edu_txxl_mh",report:{dest:"https://edu.csdn.net?utm_source=edu_txxl_mh",spm:"3001.5350"},icon:"csdn-profile-icon-study",class:""},{name:"我的订单",url:"https://mall.csdn.net/myorder",report:{dest:"https://mall.csdn.net/myorder",spm:"3001.5137"},icon:"csdn-profile-icon-order",class:""},{name:"我的钱包",url:"https://i.csdn.net/#/wallet/index",report:{dest:"https://i.csdn.net/#/wallet/index",spm:"3001.5136"},icon:"csdn-profile-icon-wallet",class:""},{name:"我的云服务",url:"https://dev-portal.csdn.net/welcome?utm_source=toolbar_user_profile",report:{dest:"https://dev-portal.csdn.net/welcome?utm_source=toolbar_user_profile",spm:"3001.7345"},icon:"csdn-profile-icon-API",class:""},{name:"我的等级",url:"https://upload.csdn.net/level?utm_source=xz_pc_txxl",report:{dest:"https://upload.csdn.net/level?utm_source=xz_pc_txxl",spm:"3001.7346"},icon:"csdn-profile-icon-ac",class:"pb-8 csdn-border-bottom"},{name:"签到抽奖",url:"https://i.csdn.net/#/uc/reward",report:{dest:"https://i.csdn.net/#/uc/reward",spm:"3001.5351"},icon:"csdn-profile-icon-draw",class:"pt-8 pb-8 csdn-border-bottom"},{name:"退出",url:"javascript:;",report:{spm:"3001.5139"},icon:"csdn-profile-icon-logout",class:"pt-8 csdn-profile-logout"}],this.render()}function d(){this.cookieTime=864e5,this.cookieKey="hide_login",this.init=!!t(this.cookieKey),this.type=c(),this.isRender=!1,this.$box=$(".toolbar-btn.toolbar-btn-login"),void 0,this.list=[[{icon:"https://img-home.csdnimg.cn/images/20220208105133.png",text:"免费复制代码"},{icon:"https://img-home.csdnimg.cn/images/20220208105144.png",text:"关注/点赞/评论/收藏"},{icon:"https://img-home.csdnimg.cn/images/20220208105156.png",text:"下载海量资源"},{icon:"https://img-home.csdnimg.cn/images/20220208105204.png",text:"写文章/发动态/加入社区"}],[{icon:"https://img-home.csdnimg.cn/images/20220208020025.png",text:"免费复制代码"},{icon:"https://img-home.csdnimg.cn/images/20220208020036.png",text:"关注/点赞/评论/收藏"},{icon:"https://img-home.csdnimg.cn/images/20220208020055.png",text:"下载海量资源"},{icon:"https://img-home.csdnimg.cn/images/20220208020057.png",text:"写文章/发动态/加入社区"}]],!this.init&&this.render()}l.prototype.render=function(){var t=this,e=$('<div id="csdn-toolbar-profile" class="csdn-toolbar-plugin">\n            <div class="csdn-profile-top">\n              <a class="csdn-profile-avatar" data-report-click=\'{"spm": "3001.5343"}\' data-report-query="spm=3001.5343"  href="https://blog.csdn.net/'+t.userName+'"><img src="'+this.avatar+'"></a>\n              <p class="csdn-profile-nickName">'+t.nickName+'</p>\n              <a data-report-click=\'{"spm": "3001.5344"}\' data-report-query="spm=3001.5344" href="https://mall.csdn.net/vip" class="csdn-profile-no-vip"></a>\n            </div>\n            <div class="csdn-profile-mid">\n              <a data-report-click=\'{"spm": "3001.5347"}\' data-report-query="spm=3001.5347" href="https://blog.csdn.net/'+t.userName+'?type=sub&subType=fans"><i class="csdn-profile-fansCount">--</i>粉丝</a>\n              <a data-report-click=\'{"spm": "3001.5348"}\' data-report-query="spm=3001.5348" href="https://blog.csdn.net/'+t.userName+'?type=sub"><i class="csdn-profile-followCount">--</i>关注</a>\n              <a data-report-click=\'{"spm": "3001.5349"}\' data-report-query="spm=3001.5349" href="https://blog.csdn.net/'+t.userName+'"><i class="csdn-profile-likeCount">--</i>获赞</a>\n            </div>\n            <div class="csdn-profile-bottom">\n              <ul class="csdn-border-bottom">\n                '+t.list.map(function(t){return'<li class="'+t.class+'"><a href="'+t.url+'" '+(t.report?"data-report-click='"+JSON.stringify(t.report)+"'":"")+" "+(t.report?"data-report-query='spm="+t.report.spm+"'":"")+'><i class="csdn-profile-icon '+t.icon+'"></i>'+t.name+"</a></li>"}).join("")+"\n              </ul>\n            </div>\n          </div>");return this.$box.append(e),this.$tpl=e,this.$box.on("mouseenter",function(e){void 0,t.isEenter=!0,t.isRender&&t.showProfile()||t.getData()}).on("mouseleave",function(e){void 0,t.isEenter=!1,t.hideProfile()}),this.$tpl.find(".csdn-profile-logout").on("click",function(t){$.ajax({type:"post",url:"https://passport.csdn.net/account/logout",data:JSON.stringify({}),crossDomain:!0,xhrFields:{withCredentials:!0},success:function(t){var e={mod:"popu_789"},n="https://passport.csdn.net/account/logout?from="+encodeURIComponent(window.location.href);e.dest=n,e.extend1="退出",csdn&&csdn.report&&csdn.report.reportClick(e),window.location.reload()},error:function(t){}})}),this},l.prototype.update=function(t){if(t){var e=t.fansCount,n=t.likeCount,o=t.favoritesCount,a=t.nickName,r=t.followCount;this.avatar=t.avatar,this.toggleVip(t),this.isRender=!0,$(".toolbar-btn-login").find(".hasAvatar img").attr("src",t.avatar),$(".csdn-profile-fansCount").text(e||"--"),$(".csdn-profile-likeCount").text(n||"--"),$(".csdn-profile-favoritesCount").text(o||"--"),$(".csdn-profile-nickName").text(a||"--"),$(".csdn-profile-followCount").text(r||"--")}},l.prototype.toggleVip=function(t){this.isVip=1===t.vip,this.isVip&&$(".csdn-profile-no-vip").addClass("csdn-profile-vip").removeClass("csdn-profile-no-vip").attr("href","https://www.csdn.net/vip")},l.prototype.showProfile=function(){var t=this;this.timer&&clearTimeout(this.timer),$("#csdn-toolbar-profile").find(".csdn-profile-avatar > img").attr("src")!==$(".toolbar-btn-login").find(".hasAvatar img").attr("src")&&$("#csdn-toolbar-profile").find(".csdn-profile-avatar > img").attr("src",$(".toolbar-btn-login").find(".hasAvatar img").attr("src")),this.timer=setTimeout(function(){t.isEenter&&($(".toolbar-btn-login").find(".hasAvatar img").attr("src",t.avatar),void 0,$(".csdn-toolbar-plugin").hide(),t.$box.addClass("toolbar-btn-login-action"),t.$tpl.stop().fadeIn(200))},150)},l.prototype.hideProfile=function(){if(this.timer&&clearTimeout(this.timer),!this.isEenter){void 0;this.$box.removeClass("toolbar-btn-login-action"),this.$tpl.stop().fadeOut(100)}},l.prototype.getData=function(){void 0;var t=this;if(!t.isRender){var e=window.csdn.toolbar.profileData;if(e)return t.update(e),void t.showProfile();$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-user-info",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(e){200===e.code&&(t.update(e.data),window.csdn.toolbar.profileData=e.data),t.showProfile()},error:function(t){}})}},d.prototype.render=function(){var t=this;!this.init&&e(this.cookieKey,"1",this.cookieTime);var n=this,o=$('<div id="csdn-toolbar-profile-nologin" class="csdn-toolbar-plugin">\n                <div class="csdn-toolbar-plugin-triangle"></div>\n                <div class="csdn-toolbar-profile-title">登录后您可以：</div>\n                <ul class="csdn-profile-top">\n                '+n.list[n.type].map(function(t){return'<li class="csdn-profile-a"><i class="csdn-profile-icon" style="background-image: url('+t.icon+'); "></i>'+t.text+"</li>"}).join("")+'\n                </ul>\n                <a class="csdn-toolbar-loginbtn" data-report-click=\'{"spm":"3001.8844"}\'>立即登录</a>\n          </div>');if(this.$box.append(o),this.$tpl=o,this.$box.find("a.csdn-toolbar-loginbtn").on("click",function(){var t=$(this).data("report-click");$(".csdn-toolbar-plugin").hide(),t?r(t):r()}),window.location.href.indexOf("passport.csdn.net")>-1)this.$tpl.hide(),n.isRender=!0;else var a=setTimeout(function(){n.isRender=!0,$(".csdn-toolbar-plugin").hide(),t.$tpl.stop().fadeOut(200),clearTimeout(a)},3e3);this.$box.on("mouseenter",function(t){void 0,n.isEenter=!0,n.isRender&&n.showProfile(),n.isRender=!0}).on("mouseleave",function(t){void 0,n.isEenter=!1,n.hideProfile()}),$(document).on("scroll",function(t){void 0,n.isEenter=!1,n.isRender=!0,n.hideProfile()})},d.prototype.showProfile=function(){var t=this;this.timer&&clearTimeout(this.timer),this.timer=setTimeout(function(){t.isEenter&&(void 0,$(".csdn-toolbar-plugin").hide(),t.$tpl.stop().fadeIn(200))},150)},d.prototype.hideProfile=function(){if(this.timer&&clearTimeout(this.timer),!this.isEenter){void 0;this.$tpl.stop().fadeOut(100)}},$(document).on("toolbarReady",function(t){void 0,setTimeout(function(){a()?new l:new d},200)})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function e(t){return t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}function n(){this.el=null,this.host="https://mp-luckydraw.csdn.net/",this.dev=!1,this.type=["redPacket","coupon","common-coupon"],this.renderCss()}n.prototype.open=function(e){if(e){var n=this;$.ajax({url:n.host+"/luckydraw/api/timesWin",type:"post",data:JSON.stringify({lotteryId:e,username:t("UserName")}),contentType:"application/json",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){n.dev&&(t={code:200,data:{prizeId:"123",name:n.type[parseInt(99*Math.random())%3],url:"",money:(10*Math.random()).toFixed(2)}}),200===t.code&&t.data?n.clear().render(t.data):n.clear().render()}})}},n.prototype.render=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};void 0;var e=this,n="csdn-redpack-sorry";t.prizeId&&"common-coupon"===t.name?n="csdn-redpack-common-coupon":t.prizeId&&"coupon"===t.name?n="csdn-redpack-coupon":!t.prizeId||"redPacket"!==t.name&&"randomRedPacket"!==t.name||(n="csdn-redpack-cash"),e.el=$('<div id="csdn-redpack-barrage" class="csdn-redpack-barrage csdn-redpack-container">\n                  <div class="csdn-redpack-result '+n+'">\n                    <a class="csdn-redpack-result-link" href="'+(t.url?t.url:"javascript:;")+'" '+(t.url?'target="_blank"':"")+'></a>\n                    <em class="csdn-redpack-result-close"></em>\n                    '+("csdn-redpack-sorry"!==n?"<span>"+t.money+"</span>":"")+"\n                    "+("csdn-redpack-common-coupon"===n||"csdn-redpack-coupon"===n?"<i>"+t.money+"</i>":"")+"\n                  </div>\n              </div>"),e.el.find(".csdn-redpack-result-close").on("click",function(t){e.clear()}),e.el.find(".csdn-redpack-result-link").on("click",function(t){e.clear()}),$("body").append(e.el)},n.prototype.renderCss=function(){void 0
;var t=$('<style type="text/css">\n                      #csdn-redpack-barrage {\n                        position: fixed;\n                        width: 100%;\n                        height: 100%;\n                        background: rgba(0, 0, 0, 0.74);\n                        overflow: hidden;\n                        top: 0;\n                        left: 0;\n                        z-index: 2147483647;\n                      }\n                      #csdn-redpack-barrage * {\n                        margin: 0;\n                        padding: 0;\n                        -webkit-box-sizing: border-box;\n                        box-sizing: border-box;\n                        -webkit-user-select: none;\n                        -moz-user-select: none;\n                        -ms-user-select: none;\n                        user-select: none;\n                      }\n                      #csdn-redpack-barrage a {\n                        text-decoration: none;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result {\n                        width: 950px;\n                        height: 720px;\n                        position: absolute;\n                        top: 50%;\n                        left: 50%;\n                        z-index: 999;\n                        -webkit-transform: translate(-50%, -45%);\n                        transform: translate(-50%, -45%);\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result .csdn-redpack-result-close {\n                        display: block;\n                        position: absolute;\n                        width: 50px;\n                        height: 50px;\n                        z-index: 99999;\n                        top: 18px;\n                        right: 300px;\n                        cursor: pointer;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result .csdn-redpack-result-link {\n                        display: block;\n                        position: absolute;\n                        width: 188px;\n                        height: 50px;\n                        z-index: 99999;\n                        left: 50%;\n                        bottom: 276px;\n                        cursor: pointer;\n                        margin-left: -84px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-sorry {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020632.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-cash {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020622.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-cash span {\n                        position: absolute;\n                        display: block;\n                        text-align: center;\n                        min-width: 100px;\n                        font-size: 40px;\n                        font-weight: 900;\n                        height: 56px;\n                        line-height: 56px;\n                        font-family: Arial-Black, Arial;\n                        color: #FE1826;\n                        text-shadow: 0px 2px 12px rgba(234, 202, 105, 0.39);\n                        left: 50%;\n                        -webkit-transform: translate(-40%, 0);\n                        transform: translate(-40%, 0);\n                        top: 135px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-cash .csdn-redpack-result-link {\n                        bottom: 316px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020628.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon {\n                        background: url("https://img-home.csdnimg.cn/images/20201110020625.png") no-repeat center center;\n                        background-size: contain;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon span,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon span,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon i,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon i {\n                        min-width: 90px;\n                        font-size: 24px;\n                        font-weight: 900;\n                        font-family: Arial-Black, Arial;\n                        color: #FE1826;\n                        text-shadow: 0px 2px 12px rgba(234, 202, 105, 0.39);\n                        position: absolute;\n                        top: 318px;\n                        left: 346px;\n                        text-align: center;\n                        line-height: 34px;\n                        height: 34px;\n                      }\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-coupon i,\n                      #csdn-redpack-barrage .csdn-redpack-result.csdn-redpack-common-coupon i {\n                        top: 303px;\n                        left: 516px;\n                        text-align: center;\n                        font-style: normal;\n                      }\n                  </style>');document.head.insertBefore($(t)[0],document.head.getElementsByTagName("title")[0])},n.prototype.clear=function(){return void 0,this.el=null,this.dev=!1,$(".csdn-redpack-barrage").remove(),this},n.prototype.test=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:243,e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.dev=e,this.host="https://test-luckydraw.csdn.net/",this.open(t)},$(document).on("toolbarReady",function(t){var o=window.csdn.toolbarData||{},a=o.barrageRedpackData;void 0,a&&a.whiteList&&e(a.whiteList)&&(void 0,window.csdn.barrageRedpack=new n)})}(),function(){function t(t){var e="width:100%; height:100%; background-image: url("+t.imgUrl+"); background-size: auto 80px;background-repeat: no-repeat; background-position: center center;",n=$('<div class="toolbar-redpack-advert">\n            <a href="'+t.clickUrl+'" style="background: '+t.backgroundColor+';" class="toolbar-redpack-advert-default"><div style="'+e+'"></div></a>\n            <span class="toolbar-redpack-adver-btn" '+(t.closeSpm?'data-report-click=\'{"spm":'+JSON.stringify(t.closeSpm)+"}'":"")+" ></span>\n            "+(t.exposureUrl?'<img style="width:0;height:0;display:none;" src="'+t.exposureUrl+'"/>':"")+"\n          </div>");n.find(".toolbar-redpack-adver-btn").click(function(t){n.remove()}),n.find(".toolbar-redpack-advert-default").click(function(e){t.clickCallback&&"function"==typeof t.clickCallback&&t.clickCallback()}),$("#csdn-toolbar").prepend(n)}function e(){$("body").find("#csdn-toolbar .toolbar-redpack-advert").remove()}var n={clickUrl:"javascript:void(0);",imgUrl:"https://g.csdnimg.cn/common/redpack/images/redpaceAdvert.png",backgroundColor:"#FFCEA6"};window.csdn=window.csdn||{},window.csdn.bannerAdvert={show:function(o){e(),t(Object.assign({},n,o))},close:function(){e()}}}(),function(){function t(t){var e=document.createElement("link");e.rel="stylesheet",e.type="text/css",e.href=t,document.getElementsByTagName("head")[0].appendChild(e)}function e(t,e){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,e&&e())}:n.onload=function(){e&&e()},n.src=t,document.getElementsByTagName("head")[0].appendChild(n)}function n(){t("https://g.csdnimg.cn/common/redpack/redpack.css"),e("https://g.csdnimg.cn/common/redpack/redpack.js")}function o(t){return t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}$(document).on("toolbarReady",function(t){var e=window.csdn.toolbarData||{},a=e.redpackData;void 0,a&&a.whiteList&&o(a.whiteList)&&(void 0,n())})}(),function(){function t(t){var e={};return location.href.replace(/([^*#&=?]+)=([^*#&=?]+)/g,function(){for(var t=arguments.length,n=Array(t),o=0;o<t;o++)n[o]=arguments[o];var a=n[1],r=n[2];e[a]=r}),t?e[t]:e}function e(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t&&"UD"==t)return decodeURIComponent(o[1]);if(o[0]==t)return decodeURI(o[1])}}function n(){return e("UserName")}function o(){this.isVip=!1,this.timer=null,this.box=null,this.isRender=!1,this.bindEvent()}var a=0;!function(){var e=$('meta[name="toolbar"]'),n=0;if(e.length){var o=e.attr("content")||{};o=JSON.parse(o),n=o.type||n}else n=t("toolbarSkinType")||n;a=n}(),o.prototype.getProfileData=function(t){var e=this;$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-user-info",type:"get",dataType:"JSON",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},success:function(n){200===n.code&&(t&&t.call(e,n.data),window.csdn.toolbar.profileData=n.data)},error:function(t){}})},o.prototype.renderVip=function(t){void 0,void 0;var e=window.csdn.toolbarData.vipItemPath||{normal:[{icon:"https://img-home.csdnimg.cn/images/20210826043933.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbcj#banner",title:"限时抽奖"},{icon:"https://img-home.csdnimg.cn/images/20210826043936.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytblq#discount_center",title:"领券中心"},{icon:"https://img-home.csdnimg.cn/images/20210826043937.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbhyg#vip_shop",title:"会员购"},{icon:"https://img-home.csdnimg.cn/images/20210826043940.png",url:"https://www.csdn.net/vip?utm_source=vip_hyzx_hytb",title:"更多特权"}],dark:[{icon:"https://img-home.csdnimg.cn/images/20210826043735.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbcj#banner",title:"限时抽奖"},{icon:"https://img-home.csdnimg.cn/images/20210826043738.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytblq#discount_center",title:"领券中心"},{icon:"https://img-home.csdnimg.cn/images/20210826043740.png",url:"https://vip.csdn.net/welfarecenter?utm_source=vip_hyzx_hytbhyg#vip_shop",title:"会员购"},{icon:"https://img-home.csdnimg.cn/images/20210826043742.png",url:"https://www.csdn.net/vip?utm_source=vip_hyzx_hytb",title:"更多特权"}]},n=this,o=1===t.vip,r="https://mall.csdn.net/vip",i=1===t.vip?"3001.6441":"3001.6439",s=1===t.vip?"3001.6442":"3001.6440",c=void 0;1!==a?e.normal&&(c=e.normal.map(function(t){return'<a rel="nofollow" href="'+t.url+'"><i class="csdn-plugin-vip-icon" style="background:url('+t.icon+');background-size: contain;"></i><br/>'+t.title+"</a>"}).join("")):e.dark&&(c=e.dark.map(function(t){return'<a rel="nofollow" href="'+t.url+'"><i class="csdn-plugin-vip-icon" style="background:url('+t.icon+');background-size: contain;"></i><br/>'+t.title+"</a>"}).join(""));var l=window.csdn.toolbarData.vipBg||{darkBg:"https://img-home.csdnimg.cn/images/20210826055052.png",defaultBg:"https://img-home.csdnimg.cn/images/20210826055049.png"},d=1==a?l.darkBg:l.defaultBg,p=$('<div id="csdn-plugin-vip" style=\'background:url('+d+') no-repeat center center; background-size: cover;\'}>\n                        <div class="csdn-plugin-vip-header">\n                            会员特权\n                        </div>\n                        <div class="csdn-plugin-vip-body">\n                            '+c+'\n                        </div>\n                        <div class="csdn-plugin-vip-footer">                \n                            <a rel="nofollow" data-report-click=\'{"spm": "'+s+'"}\' data-report-query="spm='+s+'" class="csdn-plugin-vip-footer-link" href="'+r+'">\n                              '+(o?"领取限时优惠券，最低可享7折":"领取限时优惠券，最高可减80元")+'<i></i>\n                            </a>\n                            <a rel="nofollow" data-report-click=\'{"spm": "'+i+'"}\' data-report-query="spm='+i+'" class="csdn-plugin-vip-footer-btn" href="'+r+'">\n                              '+(o?"领券续费":"领券开通")+"\n                            </a>\n                        </div>\n                    </div>"),u=$(".toolbar-btn-vip").find(">a");$(".toolbar-btn-vip").append(p),o&&(u.attr("href","https://www.csdn.net/vip"),u.attr("data-report-click",'{"spm": "3001.5399"}'),u.attr("data-report-query","spm=3001.5399")),n.box=p,n.showVip(),n.isRender=!0},o.prototype.showVip=function(){var t=this;clearTimeout(t.timer),t.timer=setTimeout(function(){t.isEnter&&t.box&&t.box.stop().fadeIn(100)},150)},o.prototype.hideVip=function(){var t=this;clearTimeout(t.timer),t.isEnter||t.box&&t.box.stop().fadeOut(100)},o.prototype.init=function(){var t=window.csdn.toolbar&&window.csdn.toolbar.profileData;if(t)this.renderVip(t);else{n()?this.getProfileData(this.renderVip):(this.renderVip(this,{vip:0}),window.csdn.toolbar.profileData={vip:0})}},o.prototype.bindEvent=function(){var t=this;$(".toolbar-btn-vip").on("mouseenter",function(e){t.isEnter=!0,t.isRender?t.showVip():t.init()}).on("mouseleave",function(e){t.isEnter=!1,t.hideVip()})},$(document).on("toolbarReady",function(t){void 0,setTimeout(function(){new o},200)})}(),function(){function t(t,e){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,e&&e())}:n.onload=function(){e&&e()},n.src=t,document.getElementsByTagName("head")[0].appendChild(n)}function e(){t("https://g.csdnimg.cn/common/vip-buyside/vip-buyside.js")}function n(t){return t.some(function(t){return"*"===t||!!~location.href.indexOf(t)})}$(document).on("toolbarReady",function(t){var o=window.csdn.toolbarData||{},a=o.vipBuySideDate;void 0,a&&a.whiteList&&n(a.whiteList)&&(void 0,e())})}(),function(){function t(t){for(var e=document.cookie.split("; "),n=0;n<e.length;n++){var o=e[n].split("=");if(o[0]==t)return decodeURIComponent(o[1])}}var e=!1,n=(t("c_segment")&&parseInt(t("c_segment")),function(){function t(){_classCallCheck(this,t),this.container=$(".toolbar-btn.toolbar-btn-write"),this.activityList=[],this.spmextra="control1",this.init()}return _createClass(t,[{key:"init",value:function(){this.render(),this.renderexp2(),this.participatescrollTop(),this.getActivityList(),this.abtest()}},{key:"getActivityList",value:function(t){var e=this;if(t)$.ajax({url:"https://g-api.csdn.net/community/toolbar-api/v1/get-activity-list",type:"get",contentType:"application/x-www-form-urlencoded; charset=utf-8",xhrFields:{withCredentials:!0},dataType:"json",success:function(t){200===t.code&&(e.activityList=t.data.list,e.activityList.length&&e.insertActivity())},error:function(t){void 0}});else{var n=window.csdn.toolbarData.remunerationData||"";if(n){var o='\n            <div class="toolbar-write-activity-more has-activity">\n              <a rel="nofollow" data-report-click=\'{"spm":"3001.9810"}\' class="write-banner-box" href="'+n.url+'" target="_blank">\n                <img src="'+n.toolbar+'">\n              </a>\n            </div>\n          ';$("#csdn-toolbar-write").append(o),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9810"})}}}},{key:"abtest",value:function(){var t=this;$("#csdn-toolbar-write").append(' <img src="https://img-home.csdnimg.cn/images/20230807043417.png" data-report-click=\'{"spm":"3001.9697"}\' data-report-view=\'{"spm":"3001.9697"}\' class="toolbar-write-close" alt="">'),$(".toolbar-write-close").on("click",function(){e=!1,t.hide()})}},{key:"render",value:function(t){this.container.append('\n        <div id="csdn-toolbar-write" class="csdn-toolbar-plugin" data-report-view=\'{"spm":"3001.9643","extra":'+JSON.stringify({dataType:this.spmextra})+'}\'>\n          <div class="csdn-toolbar-plugin-triangle"></div>\n          <ul class="csdn-toolbar-write-box">\n            <li class="csdn-toolbar-write-box-blog">\n              <a rel="nofollow" href="https://mp.csdn.net/edit" target="_blank" data-report-click=\'{"spm":"3001.5352","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-view=\'{"spm":"3001.5352","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5352">\n                <i class="csdn-toolbar-write-icon"></i>\n                <span>写文章</span>\n              </a>\n            </li>\n            <li class="csdn-toolbar-write-box-inscode">\n              <a rel="nofollow" href="https://inscode.csdn.net/?utm_source=109355915" target="_blank" data-report-click=\'{"spm":"3001.9241","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.9241">\n                <i class="csdn-toolbar-write-icon"></i>\n                <span>写代码</span>\n              </a>\n            </li>\n            <li class="csdn-toolbar-write-box-blink">\n              <a rel="nofollow" href="https://blink.csdn.net" target="_blank" data-report-click=\'{"spm":"3001.5353","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5353">\n                <i class="csdn-toolbar-write-icon"></i>\n                <span>发动态</span>\n              </a>\n            </li>\n            <li class="csdn-toolbar-write-box-ask">\n              <a rel="nofollow" href="https://ask.csdn.net/new?utm_source=p_toolbar" target="_blank" data-report-click=\'{"spm":"3001.5354","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5354">\n                <i class="csdn-toolbar-write-icon"></i>\n                <span>提问题</span>\n              </a>\n            </li>\n            <li class="csdn-toolbar-write-box-upload">\n              <a rel="nofollow" href="https://mp.csdn.net/mp_download/creation/uploadResources" target="_blank" data-report-click=\'{"spm":"3001.5355","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5355">\n                <i class="csdn-toolbar-write-icon"></i>\n                <span>传资源</span>\n              </a>\n            </li>\n            <li class="csdn-toolbar-write-box-code">\n              <a rel="nofollow" href="https://gitcode.net/explore" target="_blank" data-report-click=\'{"spm":"3001.5356","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5356">\n                <i class="csdn-toolbar-write-icon"></i>\n                <span>建项目</span>\n              </a>\n            </li>\n          </ul>\n        </div>\n      ')}},{key:"renderexp2",value:function(){var t=this,e=window.csdn.toolbarData.userParticipate||"",n=window.csdn.toolbarData.topicData||"",o="",a="";if(e){var r=e.splice(0,9);try{for(var i=0;i<e.length;i++){var s=Math.round(Math.random()*(e.length-1-i))+i,c=[e[s],e[i]];e[i]=c[0],e[s]=c[1]}r=e.splice(0,9),r.forEach(function(e){a+=' <a data-report-click=\'{"spm":"3001.9737","extra":'+JSON.stringify({dataType:t.spmextra})+'}\' data-report-view=\'{"spm":"3001.9737","extra":'+JSON.stringify({dataType:t.spmextra})+"}' href=\""+e.activityUrl+'" target="_blank">\n              <img src="'+e.avatar+'" alt="">\n            </a>  '})}catch(t){}}n&&n.forEach(function(e){o+=' <a data-report-click=\'{"spm":"3001.9738","dest":"'+e.url+'","extra":'+JSON.stringify({dataType:t.spmextra})+'}\' data-report-view=\'{"spm":"3001.9738","dest":"'+e.url+'","extra":'+JSON.stringify({dataType:t.spmextra})+"}' href=\""+e.url+'" target="_blank"><i></i> <span>'+e.title+"</span></a>  "}),$("#csdn-toolbar-write").append('\n        <div class="participate-box">\n          <div class="participate-head">\n            <span>他们都在参与话题</span>\n            <a href="https://mp.csdn.net/mp_blog/manage/creative" target="_blank" data-report-click=\'{"spm":"3001.9736","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.9736">围观看看<i></i></a>\n          </div>\n          <div class="participate-cont">\n          '+a+'\n          </div>\n          <div class="participate-bottom">\n            <div id="participate-scroll-box">\n              '+o+"\n            </div>\n          </div>\n        </div>\n      ")}},{key:"participatescrollTop",value:function(){var t=$("#participate-scroll-box"),e=$("#participate-scroll-box a").length,n=0,o=null,a=function(){o&&clearInterval(o),o=setInterval(function(){n===e-1?(n+=1,t.append(t.children().first().clone()),t.css({top:-22*n+"px",transition:"all 0.8s"})):n>=e?(n=0,t.css({top:"0px",transition:"none"}),t.children().last().remove()):(n+=1,t.css({top:-22*n+"px",transition:"all 0.8s"}))},5e3),t.on("transitionend",function(){n>=e&&(n=0,t.css({top:"0px",transition:"none"}),t.children().last().remove())})},r=function(){o&&clearInterval(o)};a(),t.hover(function(){r()},function(){a()})}},{key:"isEffectiveTime",value:function(t){if(!t)return!1;if(t.always)return!0;var e=(new Date).valueOf(),n=new Date(t.start).valueOf();return e<=new Date(t.end).valueOf()&&e>=n}},{key:"renderBanner",value:function(){var t=window.csdn.toolbarData.remunerationData||"";if(t){var e='\n          <a rel="nofollow" data-report-click=\'{"spm":"3001.9810"}\' class="write-banner-box" href="'+t.url+'" target="_blank">\n            <img src="'+t.toolbar+'">\n          </a>\n        ';$("#csdn-toolbar-write").find(".toolbar-write-activity-more").addClass("has-activity").append(e),window.csdn&&window.csdn.report&&window.csdn.report.reportView&&window.csdn.report.reportView({spm:"3001.9810"})}else $("#csdn-toolbar-write").find(".toolbar-write-activity-more").removeClass("has-activity")}},{key:"insertActivity",value:function(){for(var t="",e=0;e<this.activityList.length;e++)t+='\n          <li>\n            <a rel="nofollow" href="'+this.activityList[e].url+'" data-report-view=\'{"spm":"3001.9642","dest":"'+this.activityList[e].url+'","extra": '+JSON.stringify({index:e,dataType:this.spmextra})+'}\' data-report-click=\'{"spm":"3001.9642","dest":"'+this.activityList[e].url+'","extra": '+JSON.stringify({index:e,dataType:this.spmextra})+'}\' data-report-query="spm=3001.9642" target="_blank">#'+this.activityList[e].name+"</a>\n          </li>\n        ";this.container.find("#csdn-toolbar-write").append('\n        <div class="csdn-toolbar-write-activity">\n          <div class="csdn-toolbar-write-activity-head"><span>创作活动</span><a rel="nofollow" href="https://mp.csdn.net/mp_blog/manage/creative?spm=1011.2124.3001.5311" target="_blank" data-report-view=\'{"spm":"3001.5358","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-click=\'{"spm":"3001.5358","extra":'+JSON.stringify({dataType:this.spmextra})+'}\' data-report-query="spm=3001.5358">更多<i></i></a></div>\n          <ul>'+t+'</ul>\n          <div class="toolbar-write-activity-more">\n          </div>\n        </div>\n      '),this.renderBanner()}},{key:"show",value:function(){clearTimeout(this.timer),this.timer=setTimeout(function(){e&&$("#csdn-toolbar-write").stop().fadeIn(100)},150)}},{key:"hide",value:function(){clearTimeout(this.timer),e||$("#csdn-toolbar-write").stop().fadeOut(100)}}]),t}());$(document).on("toolbarReady",function(){var t=null,o=$(".toolbar-btn.toolbar-btn-write");o.on("mouseenter",function(){e=!0,t?t.show():t=new n,window.csdn&&window.csdn.report&&window.csdn.report.viewCheck&&window.csdn.report.viewCheck()}),o.on("mouseleave",function(){e=!1}),$(document).on("click",function(){t&&!e&&t.hide()})})}();