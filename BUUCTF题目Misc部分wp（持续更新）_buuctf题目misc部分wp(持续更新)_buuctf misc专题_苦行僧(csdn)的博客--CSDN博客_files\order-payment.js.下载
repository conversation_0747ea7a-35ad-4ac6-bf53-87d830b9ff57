"use strict";!function(o,e,s){function n(o){if(o&&C[o])return o;var e=window.location.host;I=-1!==e.indexOf("blog.csdn.net")?"blog":-1!==e.indexOf("download.csdn.net")?"download":"default"}function t(){for(var o={},e=0;e<arguments.length;e++)s.extend(!0,o,arguments[e]);return o}function a(o){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1500,n=e.createElement("div");n.innerHTML=o,n.style.cssText="min-width:124px;padding:0 8px;opacity: 0.8;height: 40px;background:rgba(34,34,38,1);color: rgb(255, 255, 255);line-height: 40px;text-align: center;border-radius: 4px;position: fixed;top: 35%;left:50%;transform: translateX(-50%);z-index: 999999;font-size: 16px;",e.getElementById("order-payment").appendChild(n),setTimeout(function(){n.style.webkitTransition="-webkit-transform 0.5s ease-in, opacity 0.5s ease-in",n.style.opacity="0",setTimeout(function(){e.getElementById("order-payment")&&e.getElementById("order-payment").removeChild(n)},500)},s)}function i(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1500,e='\n      <div class="loading_warp" id="order-payment-loading">\n        <div class="icon_box">\n          <img class="rotating" src="'+b+'/icon-paying.png"/>\n        </div>\n        <div class="pay_msg">查询中...</div>\n      </div>\n    ';s(".orderpayment_dialog").append(e).find(".orderpayment_content").addClass("noScroll"),setTimeout(function(){s("#order-payment-loading").remove(),s(".orderpayment_dialog .orderpayment_content").removeClass("noScroll")},o)}function r(){var o=c("api_env")||"",e="https://mall.csdn.net/",s=/^beta|test|loc[a-z]*/;return o.match(s)?e="https://test-mall.csdn.net/":o.match(/^pre-|pre[a-z]*/)&&(e="https://pre-mall.csdn.net/"),e}function c(o){var e=new RegExp("(^|&)"+o+"=([^&]*)(&|$)","i"),s=window.location.search.substr(1).match(e);return null!=s?unescape(s[2]):""}function d(o,n){QRCode?n.pay_url&&(G.priceInfo=n,s("#ordertip_qr_code").html(""),s("#ordertip_notify").hide(),s("#pay_btn").attr("href",n.pay_url),new QRCode(e.getElementById("ordertip_qr_code"),{text:n.pay_url,width:132,height:132})):void 0}function l(){g("pay_error","获取失败,点击重试","code_2")}function p(){g("pay_time_out","点击重新获取","")}function h(){g("pay_error","已扫码<br>请在手机端操作","")}function _(o){G.show_config.needLoading&&i(),setTimeout(function(){G.checkBalacneGoods?(a("余额充值成功，使用余额支付完成购买吧","1500"),s(".orderpayment_c_l_goodsitem").eq(0).trigger("click")):(a("支付成功","1000"),setTimeout(function(){x?(G.close(),x(o)):"reload"===G.show_config.successProcess?window.location.reload():"jump"===G.show_config.successProcess&&(window.location.href=u(o))},1e3))},G.show_config.needLoading?1500:0)}function u(o){return"success"===o.errorMessage&&o.jumpUrl&&1===o.status?o.jumpUrl:!o.need_third_pay&&o.paySuccessUrl?o.paySuccessUrl:"https://mall.csdn.net/myorder"}function g(o,e,n){s("#ordertip_notify").show().html('<img class="pay_icon" src="https://csdnimg.cn/release/download/images/'+o+'.png"/><span class="pay_tip">'+e+"</span>"),s("#ordertip_qr_code").html('<img src="https://csdnimg.cn/public/static/img/csdn-userimg250.gif" width="145" height="145"/>'),"pay_time_out"==o?s("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").on("click",function(){G.getPayCode()}):s("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").off("click")}function m(){this.checkBalacneGoods=!1,this.goodsInfo={},this.goodsList=[],this.balanceList={},this.balanceGoodsInfo={},this.activeGoodsId="",this.voucherKeysList=[],this.voucherKeysObj={},this.temporaryVoucherKeysList=[],this.temporaryVoucherKeysObj={},this.isUseBalance=!0,this.priceInfo={},this.errType="",this.reportExt={},this.navList=[],this.payMethods=[],this.price=0,this.payUrl="",this.params={},this._cart=null,this.show_params={},this.show_in_goodsobj={},this.show_config={showHeads:!0,showBalance:!0,showGoods:!0,needLoading:!0,successProcess:"reload"},this.transX=0,this.listBoxWidth=626,this.listScrollWidth=0,this.goodsTabWidth=136,w=this}function y(o){k=(new Date).getTime(),G=new m,G.show(o),window._orderPayment=G}function v(){G.close()}var f="https://g.csdnimg.cn/order-payment/",b=f+"4.0.3/images",w=null,x=null,I="default",k="",L=!0,C={blog:"直接购买<br />无限次学习",download:"直接购买<br />立即下载资料",default:"放弃优惠<br />直接购买"},V=function(o){var s=e.cookie;return s&&function(){var e,n={};s=s.split("; ");for(var t=0,a=s.length;t<a&&(e=s[t].split("="),!(e.length>0&&(e[0]===o&&(n.key=e[0],n.value=e[1],n.status=!0),"key"in n)));t++);return"key"in n&&n}()};!function(o){var s=e.createElement("link");s.rel="stylesheet",s.type="text/css",s.href=o,e.getElementsByTagName("head")[0].appendChild(s)}("https://g.csdnimg.cn/order-payment/4.0.3/order-payment.css"),m.prototype={constructor:m,close:function(){this._cart.clearTimer(),s("#order-payment-box").remove()},show:function(){var o=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return window.csdn.cart?window.csdn.cartClass?(n(e.type),this.show_params=e,this.show_config=Object.assign(this.show_config,e.config||{}),"function"==typeof e.get_pay_success_callback&&(x=e.get_pay_success_callback),e.params.forEach(function(e){o.show_in_goodsobj[e.goodsId]=e}),this.sale=e.sale_source||c("sale_source"),void this.init(e.params||{})):void void 0:void void 0},init:function(o){this._cart=new window.csdn.cartClass,this.reportExt=o.report_ext||{},this.getGoodsShowListByIds(o,function(o){w.getGoodsInfo(o,function(e){w.goodsInfo=t(o,e),w.goodsCodeData(e.new_price,e.available_amount),w.show_config.showBalance&&w.show_config.showGoods?w.getGoodsShowList():(w.activeGoodsId=w.goodsInfo.goods_id,w.checkBalacneGoods=!1,w.initDialog(),w.getPayCode())},!1)})},bindEvents:function(){s(e).off("click",".orderpayment_dialog .user_balance").on("click",".orderpayment_dialog .user_balance",function(){if(s(".orderpayment_dialog .user_balance").hasClass("disable"))return void(w.isUseBalance=!1);s(".orderpayment_dialog .user_balance").hasClass("active")?(s(".orderpayment_dialog .user_balance").removeClass("active"),w.isUseBalance=!1):(s(".orderpayment_dialog .user_balance").addClass("active"),w.isUseBalance=!0),w.goodsCodeData(w.goodsInfo.new_price,w.goodsInfo.available_amount,w.countVoucher()),w.changePriceHtml(w.price),w.getPayCode(w.goodsInfo)}),s(e).off("click","#pay_btn").on("click","#pay_btn",function(o){var e=V("UserName").value;if(L){L=!1;var n=t(w.show_in_goodsobj[w.goodsInfo.goods_id],{product_id:w.goodsInfo.product_id,goods_id:w.goodsInfo.goods_id,flag:w.goodsInfo.flag,goodsSource:w.goodsInfo.goodsSource||"",is_use_balance:2,sale_source:w.sale,request_id:e+"_"+k+"_"+w.goodsInfo.product_id+"_"+w.goodsInfo.goods_id+"_"+w.goodsInfo.flag});return void w._cart.quickBuy({params:n,get_pay_success_callback:function(o){L=!0,200==o.code&&!1===o.data.need_third_pay&&_(o.data)},error_function:function(o){L=!0,400103012==o.status&&s(".orderpayment_item.active").trigger("click"),void 0}})}}),s(e).off("click",".order-payment #useVoucherBtn").on("click",".order-payment #useVoucherBtn",function(o){w.temporaryVoucherKeysObj=JSON.parse(JSON.stringify(w.voucherKeysObj)),w.temporaryVoucherKeysList=JSON.parse(JSON.stringify(w.voucherKeysList)),s(".voucher_warp .v_w_desc").html(w.changeVoucherPriceHtml(!0)),s(".order-payment .voucher_warp").removeClass("none")}),s(e).off("click",".order-payment .voucher_card").on("click",".order-payment .voucher_card",function(o){var e=s(this).attr("data-key")||"";w.temporaryVoucherKeysObj[e]=!s(this).hasClass("checked"),w.temporaryVoucherKeysList=[],s(this).toggleClass("checked");for(var n in w.temporaryVoucherKeysObj)w.temporaryVoucherKeysObj[n]&&w.temporaryVoucherKeysList.push(n);s(".voucher_warp .v_w_desc").html(w.changeVoucherPriceHtml(!0))}),s(e).off("click",".order-payment #sureUseVoucher").on("click",".order-payment #sureUseVoucher",function(o){w.voucherKeysObj=JSON.parse(JSON.stringify(w.temporaryVoucherKeysObj)),w.voucherKeysList=JSON.parse(JSON.stringify(w.temporaryVoucherKeysList)),w.goodsInfo.cash_coupon_keys=w.voucherKeysList.join(","),s("#useVoucherBtn .voucher-title").html(w.changeVoucherPriceHtml()),w.goodsCodeData(w.goodsInfo.new_price,w.goodsInfo.available_amount,w.countVoucher()),w.changePriceHtml(w.price),w.getPayCode(w.goodsInfo),s(".order-payment .voucher_warp").addClass("none")}),s(e).on("click",".orderpayment_header_btn",function(o){w.close()}),s(e).off("click",".order-payment .list_btn.btn_left").on("click",".order-payment .list_btn.btn_left",function(o){w.moveTab("left")}),s(e).off("click",".order-payment .list_btn.btn_right").on("click",".order-payment .list_btn.btn_right",function(o){w.moveTab("right")}),s(e).off("click",".orderpayment_item").on("click",".orderpayment_item",function(o){var e=JSON.parse(s(this).attr("data-goods")||"{}");if(e="balance"===e.type?t(e,w.balanceList[e.index].ext,w.show_in_goodsobj[w.balanceList[e.index].ext.goodsId]):t(e,w.goodsList[e.index].ext,w.show_in_goodsobj[w.goodsList[e.index].ext.goodsId]),s(this).has("active"))return s(this).addClass("active").siblings().removeClass("active"),void w.getGoodsInfo(e,function(o){"balance"===e.type?(w.checkBalacneGoods=!0,w.balanceGoodsInfo=o):(w.goodsInfo=o,w.checkBalacneGoods=!1,o.available_amount>0&&(w.isUseBalance=!0)),w.activeGoodsId=o.goods_id,w.goodsCodeData(o.new_price,o.available_amount),s(".orderpayment_dialog .orderpayment_c_goodsinfo").html(w.setGoodsInfos()),s(".orderpayment_dialog .orderpayment_c_activity").html(w.goodsInfo.activityDesc),s(".orderpayment_dialog .commodity_box").html("").append(w.setPayPriceHtml()),w.resetVoucherHtml(),s(".orderpayment_dialog .scan_code").html("").append(w.setPaylistHtml())})})},initDialog:function(){this.bindEvents(),this.renderDialog()},setGoodsInfos:function(){var o=this.checkBalacneGoods?this.balanceGoodsInfo:this.goodsInfo;return'\n          <div class="orderpayment_c_goods_warp">\n            <img class="orderpayment_c_goods_img" src="'+(o.product_pic&&o.product_pic.split("|")[0]||"https://img-home.csdnimg.cn/images/20210910025238.png")+'" />\n          </div>\n          <div class="orderpayment_c_goods_t"><p>'+o.name+"</p></div>"},setGoodsListHtml:function(){for(var o="",e=0;e<this.balanceList.length;e++)o+='<div class="orderpayment_c_l_bitem orderpayment_item '+(this.activeGoodsId==this.balanceList[e].ext.goodsId?"active":"")+"\" data-goods='"+JSON.stringify({index:e,type:"balance"})+"'>\n          <span class=\"orderpayment_c_l_b_tips "+(this.balanceList[e].ext.activityContent?"":"none")+'">'+(this.balanceList[e].ext.activityContent||"")+'</span>\n          <div class="orderpayment_c_l_b_price"><span>¥</span>'+this.balanceList[e].ext.price+'</div>\n          <div class="orderpayment_c_l_b_dis">'+(this.balanceList[e].ext.activityDiscountDesc||"")+'</div>\n          <div class="orderpayment_c_l_b_activity '+(this.balanceList[e].ext.activityDesc?"":"none")+'"><p>'+(this.balanceList[e].ext.activityDesc||"")+"</p></div>\n        </div>";for(var s="",e=0;e<this.goodsList.length;e++)s+='<div class="orderpayment_c_l_goodsitem orderpayment_c_l_bitem orderpayment_item '+(this.activeGoodsId==this.goodsList[e].ext.goodsId?"active":"")+" "+(w.show_config.showGoods?"":"none")+"\" data-goods='"+JSON.stringify({index:e,type:"other"})+"'>\n          <span class=\"orderpayment_c_l_b_tips "+(this.goodsList[e].ext.activityContent?"":"none")+'" >'+this.goodsList[e].ext.activityContent+'</span>\n            <div class="orderpayment_c_l_b_price"><span>¥</span>'+this.goodsList[e].ext.price+'</div>\n            <div class="orderpayment_c_l_b_activity '+("string"!=typeof this.show_params.params[e].aloneDesc||this.show_params.params[e].aloneDesc?"":"none")+'"><p>'+(this.show_params.params[e].aloneDesc?this.show_params.params[e].aloneDesc:C[I])+"</p></div>\n        </div>";return o+s},setPaylistHtml:function(){var o=w.checkBalacneGoods?w.balanceGoodsInfo:w.goodsInfo,e=[];o.payTypeList&&o.payTypeList.forEach(function(o){"unionpay"!==o.name&&e.push(o)});for(var s="",n=0;n<e.length;n++)s+='<img class="icon_item '+e[n].name+'" src='+JSON.stringify(e[n].image)+' alt="img">';return s+'<span class="pay_intro">扫码支付</span>'},setVoucherListHtml:function(){for(var o=w.goodsInfo.cashCouponVoList||[],e="",s=0;s<o.length;s++)e+='<div class="voucher_card '+(w.temporaryVoucherKeysObj[o[s].couponKey]?"checked":"")+'" data-key="'+o[s].couponKey+'">\n          <div class="c1">\n            余¥<span class="v_c_num">'+o[s].residualValue/100+'</span><span class="v_c_all">面值:¥'+o[s].groupValue/100+'</span>\n          </div>\n          <div class="c2 ellipis" alt="'+o[s].description+'">'+o[s].description+'</div>\n          <div class="c3">有效期至'+o[s].validDate+"</div>\n        </div>";return e},setVoucherHtml:function(){return'<div class="voucher_warp none">\n        <div class="v_w_head">\n          <div class="v_w_desc">\n            <div>\n              金额抵用：<span class="v_unit">¥ </span><span class="v_price">0</span>使用代金券0张，抵用0元\n            </div>\n          </div>\n          <div class="hide_warp" id="sureUseVoucher">\n            <span class="voucher-title">确认使用</span>\n          </div>\n        </div>\n        <div class="v_w_content">\n          '+this.setVoucherListHtml()+"\n        </div>\n      </div>"},setPayPriceHtml:function(){var o=(w.priceInfo,w.checkBalacneGoods?w.balanceGoodsInfo:w.goodsInfo);return'<ul class="commodity_desc">\n                    <li class="amount_actually">\n                      待支付金额<span class="num">¥ <b>'+w.price+"</b></span>"+(parseFloat(o.old_price)<=parseFloat(w.price)?"":"<em><i>"+o.old_price+"</i>元</em>")+'<span class="'+(o.num>1?"":"none")+'">（共'+o.num+'件）</span>\n                    </li>\n                    <li class="voucher '+(o.cashCouponVoList&&o.cashCouponVoList.length>0?"":"none")+'" id="useVoucherBtn"><div class="voucher-title ">有'+o.cashCouponVoList.length+'张代金券可用，选择代金券</div>\n                    </li>\n                    <li class="gift '+(o.discount_msg?"block":"none")+'">\n                      <img src="'+b+'/enjoy.png" alt="">\n                      <span>'+o.discount_msg+'</span>\n                    </li>\n\n                    <li class="user_balance '+(o.available_amount>0?w.isUseBalance?"active":"":"disable")+" "+(19===o.flag?"none":"")+'" >\n                      <img src="'+b+'/unchecked.png" alt="" class="unchecked">\n                      <img src="'+b+'/checked.png" alt="" class="checked">\n                      钱包余额 <b class="num">'+o.available_amount+'</b>\n                      <div class="balance_tips_box">\n                        <img src="'+b+'/help.png" alt="" class="help">\n                        <span class="balance_tips">\n                          <i class="chat"></i>\n                          <b>抵扣说明：</b>\n                          <em>1、余额是钱包充值的虚拟货币，按照1:1的比例进行支付金额的抵扣；</em>\n                          <em>2、余额无法直接购买下载，可以购买VIP、课程；</em>\n                        </span>\n                      </div>\n                      <span class="'+(w.price>0?"block":"none")+'" style="font-size: 12px; color: #999aaa;">钱包余额不足</span>\n                    </li>\n                    <div class="btn '+(0!=w.price?"none":"block")+'" id="pay_btn">确认余额支付</div>\n                  </ul>'},renderDialog:function(){this.createMask();var e=this.setGoodsInfos(),n=this.setGoodsListHtml(),t=this.setPaylistHtml(),a=this.setPayPriceHtml(),i=this.setVoucherHtml(),r='<div id="order-payment" class="order-payment noselect">\n          <div class="orderpayment_dialog">\n              <div class="orderpayment_header">\n                <div class="orderpayment_header_title">扫码支付</div>\n                <span class="orderpayment_header_btn"> + </span>\n              </div>\n              <div class="orderpayment_content">\n                <div class="orderpayment_c_goodsinfo '+(w.show_config.showHeads?"":"none")+'">\n                  '+e+'\n                </div>\n                <div class="orderpayment_c_goodslist '+(w.show_config.showGoods?"":"none")+'">\n                  <span class="list_btn btn_left">\n                    <img src="'+b+'/icon-left.png">\n                  </span>\n                  <span class="list_btn btn_right">\n                    <img src="'+b+'/icon-right.png">\n                  </span>\n                  <div class="orderpayment_c_goodslist_scroll">\n                    '+n+'\n                  </div>\n                </div>\n                <div class="orderpayment_c_activity '+(this.goodsInfo.activityDesc?"":"none")+'">'+(this.goodsInfo.activityDesc||"")+'</div>\n                <div class="orderpayment_paybox">\n                  <div class="recharge_mode '+(0!=w.price?"block":"visnone")+'">\n                    <div class="recharge_mode_qr_code" id="ordertip_qr_code">\n                      <img class="loading" src="'+b+'/checked.png" width="50" height="50">\n                    </div>\n                    <div id="ordertip_notify" class="pay_notify"></div>\n                    <p class="scan_code">\n                      '+t+'\n                    </p>\n                  </div>\n                  <div class="commodity_box">\n                    '+a+'\n                  </div>\n                  <div class="voucher_warp_box">\n                    '+i+'\n                  </div>\n                </div>\n                <div class="orderpayment_custombox '+(this.show_config.customBox?"":"none")+'"">'+w.show_config.customBox+'</div>\n              </div>\n            </div>\n          <div class="orderpayment_mask"></div>\n      </div>\n      ',c=s(r);s("#order-payment-box").append(c),w.listScrollWidth=s(".orderpayment_c_goodslist_scroll").width()||0,w.listBoxWidth=s(".orderpayment_c_goodslist").width()||626,w.listBoxWidth>=w.listScrollWidth&&s("#order-payment-box .list_btn").hide(),o.report&&window.csdn.report.viewCheck()},createMask:function(){var o=e.createElement("div");o.id="order-payment-box",e.body.appendChild(o)},getGoodsShowList:function(){s.ajax({url:r()+"mp/mallorder/api/internal/goods/showListV2?showType=balancePopup",type:"GET",dataType:"json",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(o){if(o.data){w.balanceList=o.data.balancePopup||[];w.activeGoodsId=w.goodsInfo.goods_id,w.checkBalacneGoods=!1,w.initDialog(),w.getPayCode()}else void 0},error:function(o){void 0}})},getGoodsShowListByIds:function(o,e){s.ajax({url:r()+"mp/mallorder/api/internal/goods/listForPopup",type:"POST",dataType:"json",contentType:"application/json",data:o?JSON.stringify({goodsList:o}):"",async:!1,xhrFields:{withCredentials:!0},success:function(o){o.data?(w.goodsList=o.data,e&&e(w.findDefaultGood(w.goodsList))):void 0},error:function(o){void 0}})},getGoodsInfo:function(o,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];o.ext&&delete o.ext,s.ajax({url:r()+"mp/mallorder/api/internal/goods/getGoodsInfo",type:"GET",dataType:"json",data:t(o,{goods_id:o.goodsId,product_id:o.productId}),contentType:"application/json",xhrFields:{withCredentials:!0},success:function(o){200==o.code&&o.data?(19===o.data.flag&&(w.isUseBalance=!1),e&&e(o.data),w.changePriceHtml(w.price),n&&w.getPayCode(o.data)):void 0},error:function(o){void 0}})},countVoucher:function(){var o=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=0,s=[];for(var n in o?w.temporaryVoucherKeysObj:w.voucherKeysObj)(o?w.temporaryVoucherKeysObj[n]:w.voucherKeysObj[n])&&s.push(n);return w.goodsInfo.cashCouponVoList.forEach(function(o){s.indexOf(o.couponKey)>-1&&(e+=Number(o.residualValue))}),e/100},resetVoucherHtml:function(){this.voucherKeysList=[],this.voucherKeysObj={},this.temporaryVoucherKeysList=[],this.temporaryVoucherKeysObj={},s(".orderpayment_dialog .voucher_warp_box").html(w.setVoucherHtml())},changeVoucherPriceHtml:function(){var o=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=w.countVoucher(o),s=w.goodsInfo.new_price,n=s>e?e:Number(s).toFixed(2);return'<div>金额抵用：<span class="v_unit">¥</span><span class="v_price">'+n+"</span>使用代金券"+(o?w.temporaryVoucherKeysList:w.voucherKeysList).length+"张，抵用"+n+"元</div>"},goodsCodeData:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=0;n=this.isUseBalance?o-s-e<0?0:(100*o-100*s-100*e)/100:o-s<0?0:(100*o-100*s)/100,this.price=Number(n).toFixed(2)},changePriceHtml:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;Number(o)<=0?(s(".orderpayment_dialog .recharge_mode").addClass("visnone"),s(".orderpayment_dialog #pay_btn").addClass("show").removeClass("none")):(s(".orderpayment_dialog .recharge_mode").removeClass("visnone"),s(".orderpayment_dialog #pay_btn").removeClass("show").addClass("none")),s(".orderpayment_dialog .amount_actually .num b").html(o)},getPayCode:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:w.checkBalacneGoods?w.balanceGoodsInfo:w.goodsInfo,e={product_id:o.product_id,goods_id:o.goods_id,goodsSource:o.goodsSource||"",flag:o.flag,sale_source:w.sale,report_ext:w.reportExt,is_use_balance:Number(w.isUseBalance),coupon_key:o.coupon_key,cash_coupon_keys:o.cash_coupon_keys,use_cache:!0,success_function:d,error_function:l,timeout_function:p,payment_function:h,get_pay_success_callback:_},s=w.show_params.params.find(function(e){return e.goodsId==o.goods_id})||{};w._cart.qrPay(Object.assign(s,e))},findDefaultGood:function(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=o.find(function(o){return 1===o.ext.default})||o[0];return t({goods_id:e.ext.goodsId||"",flag:e.ext.flag||"",product_id:e.ext.productId||""},e.ext,w.show_in_goodsobj[e.ext.goodsId])},moveTab:function(o){var e=s(".orderpayment_c_goodslist_scroll");"left"==o?(w.transX>=0||Math.abs(w.transX)<2.5*w.goodsTabWidth?(w.transX=0,s("#order-payment .btn_left").hide()):w.transX+=w.goodsTabWidth,s("#order-payment .btn_right").show(),e.css("transform","translateX("+w.transX+"px)")):(Math.abs(w.transX)+w.listBoxWidth>=w.listScrollWidth||w.listScrollWidth-w.listBoxWidth-Math.abs(w.transX)<2.5*w.goodsTabWidth?(w.transX=-(w.listScrollWidth-w.listBoxWidth),s("#order-payment .btn_right").hide()):w.transX-=w.goodsTabWidth,s("#order-payment .btn_left").show(),w.transX-=10,e.css("transform","translateX("+w.transX+"px)"))}};var G=void 0;window.csdn.userOrderPayment={show:y,close:v}}(window.csdn=window.csdn||{},document,jQuery);