function _newArrowCheck(e,E){if(e!==E)throw new TypeError("Cannot instantiate an arrow function")}var _this=this;(function(){var e=this;_newArrowCheck(this,_this);var E=function(){"use strict";var E=this;return _newArrowCheck(this,e),function(e){return _newArrowCheck(this,E),{name:"COBOL",aliases:["standard-cobol","cobol"],case_insensitive:!0,keywords:{$pattern:/[a-zA-Z]+(?:-[a-zA-Z0-9]+)*/,keyword:["ACCEPT","ACCESS","ACTIVE-CLASS","ADD","ADDRESS","ADVANCING","AFTER","ALIGNED","ALLOCATE","ALPHABET","ALPHABETIC","ALPHABETIC-LOWER","ALPHABETIC-UPPER","ALPHANUMERIC","ALPHANUMERIC-EDITED","ALSO","ALTERNATE","AND","ANY","ANYCASE","ARE","AREA","AREAS","AS","ASCENDING","ASSIGN","AT","B-AND","B-NOT","B-OR","B-SHIFT","B-SHIFT-LC","B-SHIFT-RC","BY","B-XOR","BASED","BEFORE","BINARY","BINARY-CHAR","BINARY-DOUBLE","BINARY-LONG","BINARY-SHORT","BIT","BLANK","BLOCK","BOOLEAN","BOTTOM","CALL","CANCEL","CF","CH","CHARACTER","CHARACTERS","CLASS","CLASS-ID","CLOSE","CODE","CODE-SET","COL","COLLATING","COLS","COLUMN","COLUMNS","COMMA","COMMIT","COMMON","COMP","COMPUTATIONAL","COMPUTE","CONFIGURATION","CONSTANT","CONTAINS","CONTENT","CONTINUE","CONTROL","CONTROLS","CONVERTING","COPY","CORR","CORRESPONDING","COUNT","CRT","CURRENCY","CURSOR","DATA","DATA-POINTER","DATE","DAY","DAY-OF-WEEK","DE","DECIMAL-POINT","DECLARATIVES","DEFAULT","DELETE","DELIMITED","DELIMITER","DEPENDING","DESCENDING","DESTINATION","DETAIL","DISPLAY","DIVIDE","DIVISION","DOWN","DUPLICATES","DYNAMIC","EC","EDITING","ELSE","EMI","END","END-ACCEPT","END-ADD","END-CALL","END-COMPUTE","END-DELETE","END-DISPLAY","END-DIVIDE","END-EVALUATE","END-IF","END-MULTIPLY","END-OF-PAGE","END-PERFORM","END-RECEIVE","END-READ","END-RETURN","END-REWRITE","END-SEARCH","END-START","END-STRING","END-SUBTRACT","END-UNSTRING","END-WRITE","ENVIRONMENT","EOL","EOP","EQUAL","ERROR","EVALUATE","EXCEPTION","EXCEPTION-OBJECT","EXCLUSIVE-OR","EXIT","EXTEND","EXTERNAL","FACTORY","FARTHEST-FROM-ZERO","FALSE","FD","FILE","FILE-CONTROL","FILLER","FINAL","FINALLY","FIRST","FLOAT-BINARY-32","FLOAT-BINARY-64","FLOAT-BINARY-128","FLOAT-DECIMAL-16","FLOAT-DECIMAL-34","FLOAT-EXTENDED","FLOAT-INFINITY","FLOAT-LONG","FLOAT-NOT-A-NUMBER","FLOAT-NOT-A-NUMBER-QUIET","FLOAT-NOT-A-NUMBER-SIGNALING","FOOTING","FOR","FORMAT","FREE","FROM","FUNCTION","FUNCTION-ID","FUNCTION-POINTER","GENERATE","GET","GIVING","GLOBAL","GO","GOBACK","GREATER","GROUP","GROUP-USAGE","HEADING","I-O","I-O-CONTROL","IDENTIFICATION","IF","IN","IN-ARITHMETIC-RANGE","INDEX","INDEXED","INDICATE","INHERITS","INITIAL","INITIALIZE","INITIATE","INPUT","INPUT-OUTPUT","INSPECT","INTERFACE","INTERFACE-ID","INTO","INVALID","INVOKE","IS","JUST","JUSTIFIED","KEY","LAST","LEADING","LEFT","LENGTH","LESS","LIMIT","LIMITS","LINAGE","LINAGE-COUNTER","LINE","LINE-COUNTER","LINES","LINKAGE","LOCAL-STORAGE","LOCALE","LOCATION","LOCK","MERGE","MESSAGE-TAG","METHOD","METHOD-ID","MINUS","MODE","MOVE","MULTIPLY","NATIONAL","NATIONAL-EDITED","NATIVE","NEAREST-TO-ZERO","NESTED","NEXT","NO","NOT","NULL","NUMBER","NUMERIC","NUMERIC-EDITED","OBJECT","OBJECT-COMPUTER","OBJECT-REFERENCE","OCCURS","OF","OFF","OMITTED","ON","OPEN","OPTIONAL","OPTIONS","OR","ORDER","ORGANIZATION","OTHER","OUTPUT","OVERFLOW","OVERRIDE","PACKED-DECIMAL","PAGE","PAGE-COUNTER","PERFORM","PF","PH","PIC","PICTURE","PLUS","POINTER","POSITIVE","PRESENT","PRINTING","PROCEDURE","PROGRAM","PROGRAM-ID","PROGRAM-POINTER","PROPERTY","PROTOTYPE","RAISE","RAISING","RANDOM","RD","READ","RECEIVE","RECORD","RECORDS","REDEFINES","REEL","REF","REFERENCE","RELATIVE","RELEASE","REMAINDER","REMOVAL","RENAMES","REPLACE","REPLACING","REPORT","REPORTING","REPORTS","REPOSITORY","RESERVE","RESET","RESUME","RETRY","RETURN","RETURNING","REWIND","REWRITE","RF","RH","RIGHT","ROLLBACK","ROUNDED","RUN","SAME","SCREEN","SD","SEARCH","SECTION","SELECT","SEND","SELF","SENTENCE","SEPARATE","SEQUENCE","SEQUENTIAL","SET","SHARING","SIGN","SIZE","SORT","SORT-MERGE","SOURCE","SOURCE-COMPUTER","SOURCES","SPECIAL-NAMES","STANDARD","STANDARD-1","STANDARD-2","START","STATUS","STOP","STRING","SUBTRACT","SUM","SUPER","SUPPRESS","SYMBOLIC","SYNC","SYNCHRONIZED","SYSTEM-DEFAULT","TABLE","TALLYING","TERMINATE","TEST","THAN","THEN","THROUGH","THRU","TIME","TIMES","TO","TOP","TRAILING","TRUE","TYPE","TYPEDEF","UNIT","UNIVERSAL","UNLOCK","UNSTRING","UNTIL","UP","UPON","USAGE","USE","USE","USER-DEFAULT","USING","VAL-STATUS","VALID","VALIDATE","VALIDATE-STATUS","VALUE","VALUES","VARYING","WHEN","WITH","WORKING-STORAGE","WRITE","XOR"],literal:["ZERO","ZEROES","ZEROS","SPACE","SPACES","HIGH-VALUE","HIGH-VALUES","LOW-VALUE","LOW-VALUES","QUOTE","QUOTES","ALL"]},contains:[{scope:"comment",begin:/(^[ 0-9a-zA-Z]{1,6}[*])/,end:/\n/},{scope:"doctag",begin:/>>/,end:/\n/},{scope:"type",begin:/(9|S9|V9|X|A)+(\([0-9]*\))+/},{scope:"operator",begin:/(\+| - |\*\*|\*|\/|<>|>=|<=|>|<|=|&|::)/},{scope:"number",begin:/([0-9]+(?:(\.|,)[0-9]+)*)/},{scope:"string",begin:'"',end:'"'},{scope:"string",begin:"'",end:"'"}]}}.bind(this)}.bind(this)();hljs.registerLanguage("cobol",E)}).bind(this)(),function(e,E){"use strict";function n(){var e=E.createElement("style");e.type="text/css",e.innerHTML=R(".{0}{border-collapse:collapse}            .{0} td{padding:0}            .{1}{text-align: right;padding-right: 8px;}            .{1}:before{content:attr({2})}",[L,c,D]),E.getElementsByTagName("head")[0].appendChild(e)}function t(n){"complete"===E.readyState?i(n):e.addEventListener("DOMContentLoaded",function(){i(n)})}function i(n){try{var t=E.querySelectorAll("code.hljs");for(var i in t)t.hasOwnProperty(i)&&T(t[i],n)}catch(N){e.console.error("LineNumbers error: ",N)}}function T(e,E){if("object"==typeof e){E=E||{singleLine:!1};var n=E.singleLine?0:1;I(function(){r(e),e.innerHTML=N(e.innerHTML,n),Array.apply(null,e.childNodes).forEach(o)})}}function N(e,E){var n=s(e);if(""===n[n.length-1].trim()&&n.pop(),n.length>E){for(var t="",i=0,T=n.length;i<T;i++)t+=R('<li><div class="{0}"><div class="{1} {2}" {3}="{5}"></div></div><div class="{4}"><div class="{1}">{6}</div></div></li>',[d,C,c,D,S,i+1,n[i].length>0?n[i]:" "]);return R('<ol class="{0}">{1}</ol>',[L,t])}return e}function r(e){var E=e.childNodes;for(var n in E)if(E.hasOwnProperty(n)){var t=E[n];a(t.textContent)>0&&(t.childNodes.length>0?r(t):A(t.parentNode))}}function A(e){var E=e.className;if(/hljs-/.test(E)){for(var n=s(e.innerHTML),t=0,i="";t<n.length;t++)i+=R('<span class="{0}">{1}</span>\n',[E,n[t]]);e.innerHTML=i.trim()}}function s(e){return 0===e.length?[]:e.split(h)}function a(e){return(e.trim().match(h)||[]).length}function I(E){e.setTimeout(E,0)}function R(e,E){return e.replace(/\{(\d+)\}/g,function(e,n){return E[n]?E[n]:""})}function o(e,E,n){var t,i={id:0,length:0},T=Array.apply(null,e.childNodes),N={hundred:100,thousand:1e3};T.forEach(function(e,E,n){if(1==e.nodeType&&e.getElementsByClassName("hljs-ln-code").length){var T=e.getElementsByClassName("hljs-ln-code")[0].offsetWidth;switch(E){case N.hundred:t="hundred";break;case N.thousand:t="thousand"}T>i.length&&(i.length=T,i.id=E)}});var r=e.parentNode.offsetWidth,A=T[i.id];if(void 0!=A&&1==A.nodeType&&(void 0!=t&&(e.className=e.className+" "+t),A.getElementsByClassName("hljs-ln-numbers").length)){var s=A.getElementsByClassName("hljs-ln-numbers")[0].offsetWidth,a=A.getElementsByClassName("hljs-ln-code")[0].offsetWidth;r<s+a+u?e.setAttribute("style","width:"+(s+a+u)+"px"):O()||l()||e.setAttribute("style","width:100%")}}function O(){return!!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i)}function l(){return!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i)}var L="hljs-ln",C="hljs-ln-line",S="hljs-ln-code",d="hljs-ln-numbers",c="hljs-ln-n",D="data-line-number",h=/\r\n|\r|\n/g,u=50;e.hljs?(e.hljs.initLineNumbersOnLoad=t,e.hljs.lineNumbersBlock=T,e.hljs.getLines=s,n()):e.console.error("highlight.js not detected!")}(window,document),$(function(){function e(){hljs.initHighlighting(),"ie"!==T&&hljs.initCopyButtonOnLoad(),hljs.initLineNumbersOnLoad(),$("pre .language-plain").length>0&&$("pre .language-plain").each(function(e,E){var n=hljs.highlightAuto(i(E.innerHTML));E.innerHTML=n.value,E.className="language-"+n.language})}var E=/&(lt|gt|amp|quot|nbsp|shy|#\d{1,5});/g,n={lt:"<",gt:">",amp:"&",quot:'"',nbsp:" ",shy:"­"},t=function(e,E){return n[E]},i=function(e){return e.replace(E,t)},T=function(){var e=window.navigator.userAgent,E=function(E){return e.indexOf(E)>=0},n=function(){return"ActiveXObject"in window}();return E("MSIE")||n?"ie":E("Firefox")&&!n?"Firefox":E("Chrome")&&!n?e.indexOf("Edge")>-1?"Edge":"Chrome":E("Opera")&&!n?"Opera":E("Safari")&&!n?"Safari":void 0}();$("#content_views").hasClass("htmledit_views")&&($("#content_views pre").find("code").addClass("hljs"),e())}),function(){function e(){return!!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i)}function E(E){var n=e()?"get"==E.type?"1001.2101.3001.6708":"1001.2101.3001.6867":"get"==E.type?"1001.2101.3001.6707":"1001.2101.3001.6866",t="",T=E.tree,N='<div class="skill-tree-head">'+("get"==E.type?"文章已被收录至官方知识档案":e()?"文章知识点与官方知识档案匹配":"文章知识点与官方知识档案匹配，可进一步学习相关知识")+"</div>",r=document.createElement("div");if(r.classList.add("skill-tree-box"),e())for(var A=0;A<T.length;A++){var s=T[A],a=' data-report-click=\'{"spm":"'+n+'","dest":"'+s.url+"\"}' ",I=' href="'+s.url+'" target="_blank" ',R='<div class="skill-tree-href"><span>'+s.tree.join("</span><i></i><span>")+"</span></div>",o='<div class="skill-tree-con"><span class="skill-tree-count">'+s.studyCount+"</span> 人正在系统学习中</div>",O='<a class="skill-tree-item" '+a+I+">"+R+o+"</a>";t+=O}else for(var A=0;A<T.length;A++){var s=T[A],a=' data-report-click=\'{"spm":"'+n+'","dest":"'+s.url+"\"}' ",I=' href="'+s.url+'" target="_blank" ',R='<span class="skill-tree-href"><a'+a+I+">"+s.tree.join("</a><i></i><a"+a+I+">")+"</a></span>",o='<span class="skill-tree-con"><span class="skill-tree-count">'+s.studyCount+"</span> 人正在系统学习中</span>",O='<div class="skill-tree-item">'+R+o+"</div>";t+=O}var l='<div class="skill-tree-body">'+t+"</div>";t=N+l,r.innerHTML=t,i.appendChild(r),i.setAttribute("data-report-view",JSON.stringify({spm:n})),i.style.display="block"}function n(){window.keyword_list_init=!0,$.ajax({type:"GET",url:(e()?base_url:blogUrl)+"/phoenix/web/v2/skill-tree-info?articleId="+articleId,dataType:"json",timeout:1500,xhrFields:{withCredentials:!0},success:function(e){200==e.code&&e.data&&e.data.tree.length>0?E(e.data):i.remove()}})}function t(){return!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i)}var i=document.getElementById("treeSkill");i&&(t()||n())}();