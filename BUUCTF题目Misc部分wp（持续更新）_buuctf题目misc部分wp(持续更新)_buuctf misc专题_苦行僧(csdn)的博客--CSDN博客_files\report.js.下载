!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof haDef&&haDef.amd?haDef(t):(e="undefined"!=typeof globalThis?globalThis:e||self).agconnect=t()}(this,(function(){"use strict";var e=new(function(){function e(){this.servicesIdentifierMap=new Map,this.registeredService=new Map}return e.prototype.registryService=function(e){var t=e.name;this.registeredService.has(t)||this.registeredService.set(t,e)},e.prototype.unregistryService=function(e){},e.prototype.getService=function(e,t,n){void 0===n&&(n="[DEFAULT_CATEGORY]");var r=this.servicesIdentifierMap.get(n);if(null==r)r=new Map,this.servicesIdentifierMap.set(n,r);else if(null!=(o=r.get(e)))return o;var i=this.registeredService.get(e);if(null!=i){var o=i.serviceFactory(t);return r.set(e,o),o}return null},e}()),t=new(function(){function t(){}return t.prototype.registerApiProvider=function(e,t,n){this[e]=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t(e)},null!=n&&function e(t,n){if(!(n instanceof Object))return n;switch(n.constructor){case Array:t=[];break;case Object:void 0===t&&(t={});break;case Date:return new Date(n.getTime());default:return n}for(var r in n)n.hasOwnProperty(r)&&(t[r]=e(t[r],n[r]));return t}(this[e],n)},t.prototype.registerInternalService=function(t){e.registryService(t)},t.prototype.getService=function(t,n,r){return e.getService(t,n,r)},t}()),n="[DEFAULT_CATEGORY]",r=function(){function e(e){this.factory=e}return e.prototype.get=function(){return null==this.instance&&(this.instance=this.factory()),this.instance},e}(),i=function(){function e(e){this.instanceMap={},this.factory=e}return e.prototype.get=function(e){if(e&&Array.isArray(e)&&e.length>=1)return this.instanceMap[e[0]]||(this.instanceMap[e[0]]=this.factory(e)),this.instanceMap[e[0]];var t=new Array;return t[0]="[DEFAULT_CATEGORY]",this.get(t)},e}(),o={}.toString,a=function(e){return o.call(e).slice(8,-1)},s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function c(e,t){return e(t={exports:{}},t.exports),t.exports}var u=c((function(e){var t=e.exports={version:"2.6.12"};"number"==typeof __e&&(__e=t)}));u.version;var l=c((function(e){var t=e.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=t)})),f=c((function(e){var t=l["__core-js_shared__"]||(l["__core-js_shared__"]={});(e.exports=function(e,n){return t[e]||(t[e]=void 0!==n?n:{})})("versions",[]).push({version:u.version,mode:"global",copyright:"© 2020 Denis Pushkarev (zloirock.ru)"})})),p=0,h=Math.random(),d=function(e){return"Symbol(".concat(void 0===e?"":e,")_",(++p+h).toString(36))},g=c((function(e){var t=f("wks"),n=l.Symbol,r="function"==typeof n;(e.exports=function(e){return t[e]||(t[e]=r&&n[e]||(r?n:d)("Symbol."+e))}).store=t})),v=g("toStringTag"),m="Arguments"==a(function(){return arguments}()),E=function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=Object(e),v))?n:m?a(t):"Object"==(r=a(t))&&"function"==typeof t.callee?"Arguments":r},y=function(e){return"object"==typeof e?null!==e:"function"==typeof e},S=function(e){if(!y(e))throw TypeError(e+" is not an object!");return e},T=function(e){try{return!!e()}catch(e){return!0}},w=!T((function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})),C=l.document,A=y(C)&&y(C.createElement),b=function(e){return A?C.createElement(e):{}},I=!w&&!T((function(){return 7!=Object.defineProperty(b("div"),"a",{get:function(){return 7}}).a})),P=function(e,t){if(!y(e))return e;var n,r;if(t&&"function"==typeof(n=e.toString)&&!y(r=n.call(e)))return r;if("function"==typeof(n=e.valueOf)&&!y(r=n.call(e)))return r;if(!t&&"function"==typeof(n=e.toString)&&!y(r=n.call(e)))return r;throw TypeError("Can't convert object to primitive value")},O=Object.defineProperty,_={f:w?Object.defineProperty:function(e,t,n){if(S(e),t=P(t,!0),S(n),I)try{return O(e,t,n)}catch(e){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(e[t]=n.value),e}},N=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}},R=w?function(e,t,n){return _.f(e,t,N(1,n))}:function(e,t,n){return e[t]=n,e},L={}.hasOwnProperty,$=function(e,t){return L.call(e,t)},D=f("native-function-to-string",Function.toString),k=c((function(e){var t=d("src"),n=(""+D).split("toString");u.inspectSource=function(e){return D.call(e)},(e.exports=function(e,r,i,o){var a="function"==typeof i;a&&($(i,"name")||R(i,"name",r)),e[r]!==i&&(a&&($(i,t)||R(i,t,e[r]?""+e[r]:n.join(String(r)))),e===l?e[r]=i:o?e[r]?e[r]=i:R(e,r,i):(delete e[r],R(e,r,i)))})(Function.prototype,"toString",(function(){return"function"==typeof this&&this[t]||D.call(this)}))})),U={};U[g("toStringTag")]="z",U+""!="[object z]"&&k(Object.prototype,"toString",(function(){return"[object "+E(this)+"]"}),!0);var M=function(e){if("function"!=typeof e)throw TypeError(e+" is not a function!");return e},x=function(e,t,n){if(M(e),void 0===t)return e;switch(n){case 1:return function(n){return e.call(t,n)};case 2:return function(n,r){return e.call(t,n,r)};case 3:return function(n,r,i){return e.call(t,n,r,i)}}return function(){return e.apply(t,arguments)}},V=function(e,t,n){var r,i,o,a,s=e&V.F,c=e&V.G,f=e&V.S,p=e&V.P,h=e&V.B,d=c?l:f?l[t]||(l[t]={}):(l[t]||{}).prototype,g=c?u:u[t]||(u[t]={}),v=g.prototype||(g.prototype={});for(r in c&&(n=t),n)o=((i=!s&&d&&void 0!==d[r])?d:n)[r],a=h&&i?x(o,l):p&&"function"==typeof o?x(Function.call,o):o,d&&k(d,r,o,e&V.U),g[r]!=o&&R(g,r,a),p&&v[r]!=o&&(v[r]=o)};l.core=u,V.F=1,V.G=2,V.S=4,V.P=8,V.B=16,V.W=32,V.U=64,V.R=128;var H,F,j,B=V,G=function(e,t,n,r){if(!(e instanceof t)||void 0!==r&&r in e)throw TypeError(n+": incorrect invocation!");return e},W=function(e,t,n,r){try{return r?t(S(n)[0],n[1]):t(n)}catch(t){var i=e.return;throw void 0!==i&&S(i.call(e)),t}},Y={},K=g("iterator"),q=Array.prototype,z=function(e){return void 0!==e&&(Y.Array===e||q[K]===e)},J=Math.ceil,X=Math.floor,Q=function(e){return isNaN(e=+e)?0:(e>0?X:J)(e)},Z=Math.min,ee=function(e){return e>0?Z(Q(e),9007199254740991):0},te=g("iterator"),ne=u.getIteratorMethod=function(e){if(null!=e)return e[te]||e["@@iterator"]||Y[E(e)]},re=c((function(e){var t={},n={},r=e.exports=function(e,r,i,o,a){var s,c,u,l,f=a?function(){return e}:ne(e),p=x(i,o,r?2:1),h=0;if("function"!=typeof f)throw TypeError(e+" is not iterable!");if(z(f)){for(s=ee(e.length);s>h;h++)if((l=r?p(S(c=e[h])[0],c[1]):p(e[h]))===t||l===n)return l}else for(u=f.call(e);!(c=u.next()).done;)if((l=W(u,p,c.value,r))===t||l===n)return l};r.BREAK=t,r.RETURN=n})),ie=g("species"),oe=function(e,t){var n,r=S(e).constructor;return void 0===r||null==(n=S(r)[ie])?t:M(n)},ae=function(e,t,n){var r=void 0===n;switch(t.length){case 0:return r?e():e.call(n);case 1:return r?e(t[0]):e.call(n,t[0]);case 2:return r?e(t[0],t[1]):e.call(n,t[0],t[1]);case 3:return r?e(t[0],t[1],t[2]):e.call(n,t[0],t[1],t[2]);case 4:return r?e(t[0],t[1],t[2],t[3]):e.call(n,t[0],t[1],t[2],t[3])}return e.apply(n,t)},se=l.document,ce=se&&se.documentElement,ue=l.process,le=l.setImmediate,fe=l.clearImmediate,pe=l.MessageChannel,he=l.Dispatch,de=0,ge={},ve=function(){var e=+this;if(ge.hasOwnProperty(e)){var t=ge[e];delete ge[e],t()}},me=function(e){ve.call(e.data)};le&&fe||(le=function(e){for(var t=[],n=1;arguments.length>n;)t.push(arguments[n++]);return ge[++de]=function(){ae("function"==typeof e?e:Function(e),t)},H(de),de},fe=function(e){delete ge[e]},"process"==a(ue)?H=function(e){ue.nextTick(x(ve,e,1))}:he&&he.now?H=function(e){he.now(x(ve,e,1))}:pe?(j=(F=new pe).port2,F.port1.onmessage=me,H=x(j.postMessage,j,1)):l.addEventListener&&"function"==typeof postMessage&&!l.importScripts?(H=function(e){l.postMessage(e+"","*")},l.addEventListener("message",me,!1)):H="onreadystatechange"in b("script")?function(e){ce.appendChild(b("script")).onreadystatechange=function(){ce.removeChild(this),ve.call(e)}}:function(e){setTimeout(x(ve,e,1),0)});var Ee={set:le,clear:fe},ye=Ee.set,Se=l.MutationObserver||l.WebKitMutationObserver,Te=l.process,we=l.Promise,Ce="process"==a(Te);function Ae(e){var t,n;this.promise=new e((function(e,r){if(void 0!==t||void 0!==n)throw TypeError("Bad Promise constructor");t=e,n=r})),this.resolve=M(t),this.reject=M(n)}var be={f:function(e){return new Ae(e)}},Ie=function(e){try{return{e:!1,v:e()}}catch(e){return{e:!0,v:e}}},Pe=l.navigator,Oe=Pe&&Pe.userAgent||"",_e=function(e,t,n){for(var r in t)k(e,r,t[r],n);return e},Ne=_.f,Re=g("toStringTag"),Le=function(e,t,n){e&&!$(e=n?e:e.prototype,Re)&&Ne(e,Re,{configurable:!0,value:t})},$e=g("species"),De=function(e){var t=l[e];w&&t&&!t[$e]&&_.f(t,$e,{configurable:!0,get:function(){return this}})},ke=g("iterator"),Ue=!1;try{var Me=[7][ke]();Me.return=function(){Ue=!0},Array.from(Me,(function(){throw 2}))}catch(tc){}var xe,Ve,He,Fe,je=function(e,t){if(!t&&!Ue)return!1;var n=!1;try{var r=[7],i=r[ke]();i.next=function(){return{done:n=!0}},r[ke]=function(){return i},e(r)}catch(e){}return n},Be=Ee.set,Ge=function(){var e,t,n,r=function(){var r,i;for(Ce&&(r=Te.domain)&&r.exit();e;){i=e.fn,e=e.next;try{i()}catch(r){throw e?n():t=void 0,r}}t=void 0,r&&r.enter()};if(Ce)n=function(){Te.nextTick(r)};else if(!Se||l.navigator&&l.navigator.standalone)if(we&&we.resolve){var i=we.resolve(void 0);n=function(){i.then(r)}}else n=function(){ye.call(l,r)};else{var o=!0,a=document.createTextNode("");new Se(r).observe(a,{characterData:!0}),n=function(){a.data=o=!o}}return function(r){var i={fn:r,next:void 0};t&&(t.next=i),e||(e=i,n()),t=i}}(),We=l.TypeError,Ye=l.process,Ke=Ye&&Ye.versions,qe=Ke&&Ke.v8||"",ze=l.Promise,Je="process"==E(Ye),Xe=function(){},Qe=Ve=be.f,Ze=!!function(){try{var e=ze.resolve(1),t=(e.constructor={})[g("species")]=function(e){e(Xe,Xe)};return(Je||"function"==typeof PromiseRejectionEvent)&&e.then(Xe)instanceof t&&0!==qe.indexOf("6.6")&&-1===Oe.indexOf("Chrome/66")}catch(e){}}(),et=function(e){var t;return!(!y(e)||"function"!=typeof(t=e.then))&&t},tt=function(e,t){if(!e._n){e._n=!0;var n=e._c;Ge((function(){for(var r=e._v,i=1==e._s,o=0,a=function(t){var n,o,a,s=i?t.ok:t.fail,c=t.resolve,u=t.reject,l=t.domain;try{s?(i||(2==e._h&&it(e),e._h=1),!0===s?n=r:(l&&l.enter(),n=s(r),l&&(l.exit(),a=!0)),n===t.promise?u(We("Promise-chain cycle")):(o=et(n))?o.call(n,c,u):c(n)):u(r)}catch(e){l&&!a&&l.exit(),u(e)}};n.length>o;)a(n[o++]);e._c=[],e._n=!1,t&&!e._h&&nt(e)}))}},nt=function(e){Be.call(l,(function(){var t,n,r,i=e._v,o=rt(e);if(o&&(t=Ie((function(){Je?Ye.emit("unhandledRejection",i,e):(n=l.onunhandledrejection)?n({promise:e,reason:i}):(r=l.console)&&r.error&&r.error("Unhandled promise rejection",i)})),e._h=Je||rt(e)?2:1),e._a=void 0,o&&t.e)throw t.v}))},rt=function(e){return 1!==e._h&&0===(e._a||e._c).length},it=function(e){Be.call(l,(function(){var t;Je?Ye.emit("rejectionHandled",e):(t=l.onrejectionhandled)&&t({promise:e,reason:e._v})}))},ot=function(e){var t=this;t._d||(t._d=!0,(t=t._w||t)._v=e,t._s=2,t._a||(t._a=t._c.slice()),tt(t,!0))},at=function(e){var t,n=this;if(!n._d){n._d=!0,n=n._w||n;try{if(n===e)throw We("Promise can't be resolved itself");(t=et(e))?Ge((function(){var r={_w:n,_d:!1};try{t.call(e,x(at,r,1),x(ot,r,1))}catch(e){ot.call(r,e)}})):(n._v=e,n._s=1,tt(n,!1))}catch(e){ot.call({_w:n,_d:!1},e)}}};Ze||(ze=function(e){G(this,ze,"Promise","_h"),M(e),xe.call(this);try{e(x(at,this,1),x(ot,this,1))}catch(e){ot.call(this,e)}},(xe=function(e){this._c=[],this._a=void 0,this._s=0,this._d=!1,this._v=void 0,this._h=0,this._n=!1}).prototype=_e(ze.prototype,{then:function(e,t){var n=Qe(oe(this,ze));return n.ok="function"!=typeof e||e,n.fail="function"==typeof t&&t,n.domain=Je?Ye.domain:void 0,this._c.push(n),this._a&&this._a.push(n),this._s&&tt(this,!1),n.promise},catch:function(e){return this.then(void 0,e)}}),He=function(){var e=new xe;this.promise=e,this.resolve=x(at,e,1),this.reject=x(ot,e,1)},be.f=Qe=function(e){return e===ze||e===Fe?new He(e):Ve(e)}),B(B.G+B.W+B.F*!Ze,{Promise:ze}),Le(ze,"Promise"),De("Promise"),Fe=u.Promise,B(B.S+B.F*!Ze,"Promise",{reject:function(e){var t=Qe(this);return(0,t.reject)(e),t.promise}}),B(B.S+B.F*!Ze,"Promise",{resolve:function(e){return function(e,t){if(S(e),y(t)&&t.constructor===e)return t;var n=be.f(e);return(0,n.resolve)(t),n.promise}(this,e)}}),B(B.S+B.F*!(Ze&&je((function(e){ze.all(e).catch(Xe)}))),"Promise",{all:function(e){var t=this,n=Qe(t),r=n.resolve,i=n.reject,o=Ie((function(){var n=[],o=0,a=1;re(e,!1,(function(e){var s=o++,c=!1;n.push(void 0),a++,t.resolve(e).then((function(e){c||(c=!0,n[s]=e,--a||r(n))}),i)})),--a||r(n)}));return o.e&&i(o.v),n.promise},race:function(e){var t=this,n=Qe(t),r=n.reject,i=Ie((function(){re(e,!1,(function(e){t.resolve(e).then(n.resolve,r)}))}));return i.e&&r(i.v),n.promise}});var st=Math.floor;B(B.S,"Number",{isInteger:function(e){return!y(e)&&isFinite(e)&&st(e)===e}});var ct={f:{}.propertyIsEnumerable},ut=Object("z").propertyIsEnumerable(0)?Object:function(e){return"String"==a(e)?e.split(""):Object(e)},lt=function(e){if(null==e)throw TypeError("Can't call method on  "+e);return e},ft=function(e){return ut(lt(e))},pt=Object.getOwnPropertyDescriptor,ht={f:w?pt:function(e,t){if(e=ft(e),t=P(t,!0),I)try{return pt(e,t)}catch(e){}if($(e,t))return N(!ct.f.call(e,t),e[t])}},dt=function(e,t){if(S(e),!y(t)&&null!==t)throw TypeError(t+": can't set as prototype!")},gt={set:Object.setPrototypeOf||("__proto__"in{}?function(e,t,n){try{(n=x(Function.call,ht.f(Object.prototype,"__proto__").set,2))(e,[]),t=!(e instanceof Array)}catch(e){t=!0}return function(e,r){return dt(e,r),t?e.__proto__=r:n(e,r),e}}({},!1):void 0),check:dt}.set,vt=function(e,t,n){var r,i=t.constructor;return i!==n&&"function"==typeof i&&(r=i.prototype)!==n.prototype&&y(r)&&gt&&gt(e,r),e},mt=Math.max,Et=Math.min,yt=function(e,t){return(e=Q(e))<0?mt(e+t,0):Et(e,t)},St=function(e){return function(t,n,r){var i,o=ft(t),a=ee(o.length),s=yt(r,a);if(e&&n!=n){for(;a>s;)if((i=o[s++])!=i)return!0}else for(;a>s;s++)if((e||s in o)&&o[s]===n)return e||s||0;return!e&&-1}},Tt=f("keys"),wt=function(e){return Tt[e]||(Tt[e]=d(e))},Ct=St(!1),At=wt("IE_PROTO"),bt=function(e,t){var n,r=ft(e),i=0,o=[];for(n in r)n!=At&&$(r,n)&&o.push(n);for(;t.length>i;)$(r,n=t[i++])&&(~Ct(o,n)||o.push(n));return o},It="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(","),Pt=It.concat("length","prototype"),Ot={f:Object.getOwnPropertyNames||function(e){return bt(e,Pt)}},_t="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff",Nt="["+_t+"]",Rt=RegExp("^"+Nt+Nt+"*"),Lt=RegExp(Nt+Nt+"*$"),$t=function(e,t,n){var r={},i=T((function(){return!!_t[e]()||"​"!="​"[e]()})),o=r[e]=i?t(Dt):_t[e];n&&(r[n]=o),B(B.P+B.F*i,"String",r)},Dt=$t.trim=function(e,t){return e=String(lt(e)),1&t&&(e=e.replace(Rt,"")),2&t&&(e=e.replace(Lt,"")),e},kt=$t,Ut=Object.keys||function(e){return bt(e,It)},Mt=w?Object.defineProperties:function(e,t){S(e);for(var n,r=Ut(t),i=r.length,o=0;i>o;)_.f(e,n=r[o++],t[n]);return e},xt=wt("IE_PROTO"),Vt=function(){},Ht=function(){var e,t=b("iframe"),n=It.length;for(t.style.display="none",ce.appendChild(t),t.src="javascript:",(e=t.contentWindow.document).open(),e.write("<script>document.F=Object<\/script>"),e.close(),Ht=e.F;n--;)delete Ht.prototype[It[n]];return Ht()},Ft=Object.create||function(e,t){var n;return null!==e?(Vt.prototype=S(e),n=new Vt,Vt.prototype=null,n[xt]=e):n=Ht(),void 0===t?n:Mt(n,t)},jt=Ot.f,Bt=ht.f,Gt=_.f,Wt=kt.trim,Yt=l.Number,Kt=Yt,qt=Yt.prototype,zt="Number"==a(Ft(qt)),Jt="trim"in String.prototype,Xt=function(e){var t=P(e,!1);if("string"==typeof t&&t.length>2){var n,r,i,o=(t=Jt?t.trim():Wt(t,3)).charCodeAt(0);if(43===o||45===o){if(88===(n=t.charCodeAt(2))||120===n)return NaN}else if(48===o){switch(t.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+t}for(var a,s=t.slice(2),c=0,u=s.length;c<u;c++)if((a=s.charCodeAt(c))<48||a>i)return NaN;return parseInt(s,r)}}return+t};if(!Yt(" 0o1")||!Yt("0b1")||Yt("+0x1")){Yt=function(e){var t=arguments.length<1?0:e,n=this;return n instanceof Yt&&(zt?T((function(){qt.valueOf.call(n)})):"Number"!=a(n))?vt(new Kt(Xt(t)),n,Yt):Xt(t)};for(var Qt,Zt=w?jt(Kt):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger".split(","),en=0;Zt.length>en;en++)$(Kt,Qt=Zt[en])&&!$(Yt,Qt)&&Gt(Yt,Qt,Bt(Kt,Qt));Yt.prototype=qt,qt.constructor=Yt,k(l,"Number",Yt)}var tn=function(e){return Object(lt(e))},nn=Array.isArray||function(e){return"Array"==a(e)},rn=g("species"),on=function(e,t){return new(function(e){var t;return nn(e)&&("function"!=typeof(t=e.constructor)||t!==Array&&!nn(t.prototype)||(t=void 0),y(t)&&null===(t=t[rn])&&(t=void 0)),void 0===t?Array:t}(e))(t)},an=function(e,t){var n=1==e,r=2==e,i=3==e,o=4==e,a=6==e,s=5==e||a,c=t||on;return function(t,u,l){for(var f,p,h=tn(t),d=ut(h),g=x(u,l,3),v=ee(d.length),m=0,E=n?c(t,v):r?c(t,0):void 0;v>m;m++)if((s||m in d)&&(p=g(f=d[m],m,h),e))if(n)E[m]=p;else if(p)switch(e){case 3:return!0;case 5:return f;case 6:return m;case 2:E.push(f)}else if(o)return!1;return a?-1:i||o?o:E}},sn=function(e,t){return!!e&&T((function(){t?e.call(null,(function(){}),1):e.call(null)}))},cn=an(3);B(B.P+B.F*!sn([].some,!0),"Array",{some:function(e){return cn(this,e,arguments[1])}});
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0

  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.

  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** */
var un=function(e,t){return(un=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(e,t)};function ln(e,t){function n(){this.constructor=e}un(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}function fn(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function s(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n((function(t){t(e.value)})).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function pn(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!(i=a.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}function hn(e){return(hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}!function(e,t){var n=(u.Object||{})[e]||Object[e],r={};r[e]=t(n),B(B.S+B.F*T((function(){n(1)})),"Object",r)}("keys",(function(){return function(e){return Ut(tn(e))}}));var dn=[].slice;B(B.P+B.F*T((function(){ce&&dn.call(ce)})),"Array",{slice:function(e,t){var n=ee(this.length),r=a(this);if(t=void 0===t?n:t,"Array"==r)return dn.call(this,e,t);for(var i=yt(e,n),o=yt(t,n),s=ee(o-i),c=new Array(s),u=0;u<s;u++)c[u]="String"==r?this.charAt(i+u):this[i+u];return c}});var gn=an(0),vn=sn([].forEach,!0);B(B.P+B.F*!vn,"Array",{forEach:function(e){return gn(this,e,arguments[1])}});var mn=St(!1),En=[].indexOf,yn=!!En&&1/[1].indexOf(1,-0)<0;B(B.P+B.F*(yn||!sn(En)),"Array",{indexOf:function(e){return yn?En.apply(this,arguments)||0:mn(this,e,arguments[1])}});var Sn=an(2);B(B.P+B.F*!sn([].filter,!0),"Array",{filter:function(e){return Sn(this,e,arguments[1])}});var Tn,wn=ct.f,Cn=(Tn=!0,function(e){for(var t,n=ft(e),r=Ut(n),i=r.length,o=0,a=[];i>o;)t=r[o++],w&&!wn.call(n,t)||a.push(Tn?[t,n[t]]:n[t]);return a});B(B.S,"Object",{entries:function(e){return Cn(e)}});var An,bn="header",In="events_common",Pn="events",On="event",_n="debugMode",Nn="routePolicy",Rn="terminalName",Ln="App-Id",$n="App-Ver",Dn="Sdk-Name",kn="Device-Type",Un="servicetag",Mn="Package-Name",xn="Request-Id",Vn="x-hasdk-debug",Hn="x-hasdk-token",Fn="x-hasdk-log-region",jn="x-hasdk-resourceid",Bn="x-hasdk-productid",Gn="x-hasdk-clientid",Wn="_openness_log_tag",Yn="_openness_config_tag",Kn="$StartType",qn="$StartScene",zn="$PrevActivityName",Jn="$PrevActivityId",Xn="$CurActivityName",Qn="$CurActivityId",Zn="$PrePageId",er="$CurrPageId",tr="$UrlParams",nr="$Duration",rr="Edge",ir="IE",or="Firefox",ar="Chrome",sr="Opera",cr="Safari",ur=64,lr=100,fr=128,pr=256,hr=30720,dr=2048,gr=100,vr=204800,mr=50,Er=25,yr=/^[a-zA-Z_$][A-Za-z0-9_]*$/,Sr=/^[a-zA-Z][A-Za-z0-9_]*$/,Tr=/^[a-zA-Z_$][A-Za-z0-9_]*$/,wr={value:5e3,label:"5s"},Cr={value:18e6,label:"5h"};!function(e){e[e.BEACON=1]="BEACON",e[e.AJAX=2]="AJAX",e[e.IMG=3]="IMG",e[e.WXREQUEST=4]="WXREQUEST"}(An||(An={}));var Ar,br=0,Ir=1,Pr=2,Or=3,_r=5,Nr="0",Rr="1",Lr=1,$r=60,Dr=30,kr=1e3;!function(e){e.AES_CBC_NETWORK="AES_CBC_NETWORK",e.AES_CBC_STORAGE="AES_CBC_STORAGE"}(Ar||(Ar={}));var Ur="HW_ha_eventSessionName",Mr="HW_ha_analyticsEnabled",xr="HW_ha_restrictionEnabled",Vr="HW_ha_restrictionShared",Hr="HW_ha_isFirstRun",Fr={CN:"HW_ha_pubKeyCN",DE:"HW_ha_pubKeyDE",RU:"HW_ha_pubKeyRU",SG:"HW_ha_pubKeySG"},jr={CN:"HW_ha_workKeyCN",DE:"HW_ha_workKeyDE",RU:"HW_ha_workKeyRU",SG:"HW_ha_workKeySG"},Br={CREATEPAYMENTINFO:"$CreatePaymentInfo",ADDPRODUCT2CART:"$AddProduct2Cart",ADDPRODUCT2WISHLIST:"$AddProduct2WishList",STARTAPP:"$StartApp",STARTCHECKOUT:"$StartCheckout",VIEWCAMPAIGN:"$ViewCampaign",VIEWCHECKOUTSTEP:"$ViewCheckoutStep",WINVIRTUALCOIN:"$WinVirtualCoin",COMPLETEPURCHASE:"$CompletePurchase",OBTAINLEADS:"$ObtainLeads",JOINUSERGROUP:"$JoinUserGroup",COMPLETELEVEL:"$CompleteLevel",STARTLEVEL:"$StartLevel",UPGRADELEVEL:"$UpgradeLevel",SIGNIN:"$SignIn",SIGNOUT:"$SignOut",SUBMITSCORE:"$SubmitScore",CREATEORDER:"$CreateOrder",REFUNDORDER:"$RefundOrder",DELPRODUCTFROMCART:"$DelProductFromCart",SEARCH:"$Search",VIEWCONTENT:"$ViewContent",UPDATECHECKOUTOPTION:"$UpdateCheckoutOption",SHARECONTENT:"$ShareContent",REGISTERACCOUNT:"$RegisterAccount",CONSUMEVIRTUALCOIN:"$ConsumeVirtualCoin",STARTTUTORIAL:"$StartTutorial",COMPLETETUTORIAL:"$CompleteTutorial",OBTAINACHIEVEMENT:"$ObtainAchievement",VIEWPRODUCT:"$ViewProduct",VIEWPRODUCTLIST:"$ViewProductList",VIEWSEARCHRESULT:"$ViewSearchResult",UPDATEMEMBERSHIPLEVEL:"$UpdateMembershipLevel",FILTRATEPRODUCT:"$FiltrateProduct",VIEWCATEGORY:"$ViewCategory",UPDATEORDER:"$UpdateOrder",CANCELORDER:"$CancelOrder",COMPLETEORDER:"$CompleteOrder",CANCELCHECKOUT:"$CancelCheckout",OBTAINVOUCHER:"$ObtainVoucher",CONTACTCUSTOMSERVICE:"$ContactCustomService",NOVICEGUIDESTART:"$NoviceGuideStart",NOVICEGUIDEEND:"$NoviceGuideEnd",STARTGAME:"$StartGame",ENDGAME:"$EndGame",WINPROPS:"$WinProps",EXCHANGEGOODS:"$ExchangeGoods",VIEWHOUSELIST:"$ViewHouseList",VIEWHOUSEDETAIL:"$ViewHouseDetail",INVITE:"$Invite",RATE:"$Rate",CONSUMEPROPS:"$ConsumeProps",ADDFRIEND:"$AddFriend",ADDBLACKLIST:"$AddBlacklist",VIEWFRIENDLIST:"$ViewFriendList",QUITUSERGROUP:"$QuitUserGroup",CREATEUSERGROUP:"$CreateUserGroup",DISBANDUSERGROUP:"$DisbandUserGroup",UPGRADEUSERGROUP:"$UpgradeUserGroup",VIEWUSERGROUP:"$ViewUserGroup",JOINTEAM:"$JoinTeam",SENDMESSAGE:"$SendMessage",LEARNSKILL:"$LearnSkill",USESKILL:"$UseSkill",GETEQUIPMENT:"$GetEquipment",LOSEEQUIPMENT:"$LoseEquipment",ENHANCEEQUIPMENT:"$EnhanceEquipment",SWITCHCLASS:"$SwitchClass",ACCEPTTASK:"$AcceptTask",FINISHTASK:"$FinishTask",ATTENDACTIVITY:"$AttendActivity",FINISHCUTSCENE:"$FinishCutscene",SKIPCUTSCENE:"$SkipCutscene",GETPET:"$GetPet",LOSEPET:"$LosePet",ENHANCEPET:"$EnhancePet",GETMOUNT:"$GetMount",LOSEMOUNT:"$LoseMount",ENHANCEMOUNT:"$EnhanceMount",CREATEROLE:"$CreateRole",SIGNINROLE:"$SignInRole",SIGNOUTROLE:"$SignOutRole",STARTBATTLE:"$StartBattle",ENDBATTLE:"$EndBattle",STARTDUNGEON:"$StartDungeon",FINISHDUNGEON:"$FinishDungeon",VIEWPACKAGE:"$ViewPackage",REDEEM:"$Redeem",MODIFYSETTING:"$ModifySetting",WATCHVIDEO:"$WatchVideo",CLICKMESSAGE:"$ClickMessage",DRAWCARD:"$DrawCard",VIEWCARDLIST:"$ViewCardList",BINDACCOUNT:"$BindAccount",STARTEXERCISE:"$StartExercise",ENDEXERCISE:"$EndExercise",STARTPLAYMEDIA:"$StartPlayMedia",ENDPLAYMEDIA:"$EndPlayMedia",STARTEXAMINE:"$StartExamine",ENDEXAMINE:"$EndExamine",CHECKIN:"$CheckIn",COMPENSATION:"$Compensation",POST:"$Post",SHAREAPP:"$ShareApp",BINDDEVICE:"$BindDevice",UNBINDDEVICE:"$UnBindDevice",RESERVEMAINTENANCE:"$ReserveMaintenance",DEVICEMISSINGREPORT:"$DeviceMissingReport",MODULARCLICK:"$ModularClick",PAGEVIEW:"$PageView",STARTBOOKING:"$StartBooking",LEARNTARGET:"$LearnTarget",LANGUAGETEST:"$LanguageTest",STARTTRAINING:"$StartTraining",ENDTRAINING:"$EndTraining",REGISTERACTIVITY:"$RegisterActivity",EXITACTIVITY:"$ExitActivity",COMPLETEACTIVITY:"$CompleteActivity",FOLLOWCONTENT:"$FollowContent",ENTERACCOUNTOPENING:"$EnterAccountOpening",SUBMITACCOUNTOPENING:"$SubmitAccountOpening",BANDCARD:"$BandCard",BANKTRANSFERIN:"$BankTransferIn",BANKTRANSFEROUT:"$BankTransferOut",VIEWSTOCKDETAIL:"$ViewStockDetail",TRADESTOCKS:"$TradeStocks",VIEWFINANCEPAGE:"$ViewFinancePage",PURCHASEFINANCE:"$PurchaseFinance",REDEMPTIONFINANCE:"$RedemptionFinance",FUNDTRADING:"$FundTrading",FIXEDINVESTMENT:"$Fixedinvestment",APPLYNEW:"$ApplyNew",VIEWINFORMATIONSECTION:"$ViewInformationSection",VIEWINFORMATION:"$ViewInformation",DISPLAYVOUCHER:"$DisplayVoucher",BOOKCOURSE:"$BookCourse",LEARNCOURSES:"$LearnCourses",TRYOUT:"$Tryout",ANSWER:"$Answer",VIEWFUNDPAGE:"$ViewFundPage",CLICKPURCHASE:"$ClickPurchase",ENABLEMEMBER:"$EnableMember",CANCELMEMBER:"$CancelMember",COMMENTCONTENT:"$CommentContent",LIKECONTENT:"$LikeContent",DELETEPRODUCT2WISHLIST:"$DeleteProduct2WishList",ADCLICK:"$AdClick",ADDISPLAY:"$AdDisplay",VIPSUC:"$VipSuc",REGISTERFAILED:"$RegisterFailed",VIPCLICK:"$VipClick",VIPFAILED:"$VipFailed",IMPROVEINFORMATION:"$ImproveInformation"},Gr={JOINABTASK:"$JoinABTask",FIRST_OPEN:"$AppFirstOpen",ENTER_SCREEN:"$EnterScreen",EXIT_SCREEN:"$ExitScreen",ADD_QUICK_APP:"$AddQuickApp",HA_NOTIFICATION_DISPLAY:"$HA_NOTIFICATION_DISPLAY",DisplayNotification:"$DisplayNotification",HA_NOTIFICATION_CLICK:"$HA_NOTIFICATION_CLICK",ClickNotification:"$ClickNotification",HA_NOTIFICATION_CLEAR:"$HA_NOTIFICATION_CLEAR",ClearNotification:"$ClearNotification",HA_APP_INSTALL:"$HA_APP_INSTALL",InstallApp:"$InstallApp",HA_APP_START:"$HA_APP_START",LaunchApp:"$LaunchApp",HA_APP_UPDATE:"$HA_APP_UPDATE",UpdateApp:"$UpdateApp",HA_FIRST_OPEN:"$HA_FIRST_OPEN",AppFirstOpen:"$AppFirstOpen",HA_SCREEN_ENTER:"$HA_SCREEN_ENTER",EnterScreen:"$EnterScreen",HA_SCREEN_EXIT:"$HA_SCREEN_EXIT",ExitScreen:"$ExitScreen",HA_CLEAR_DATA:"$HA_CLEAR_DATA",ClearData:"$ClearData",HA_CLEAR_CACHE:"$HA_CLEAR_CACHE",ClearCache:"$ClearCache",HA_APP_UNINSTALL:"$HA_APP_UNINSTALL",UninstallApp:"$UninstallApp",InAppPurchase:"$InAppPurchase",RequestAd:"$RequestAd",DisplayAd:"$DisplayAd",ClickAd:"$ClickAd",STOP_ANALYTICS_COLLECTION:"$StopAnalyticsCollection",AppError:"$AppError",AppPushToken:"$AppPushToken",CampaignPushClick:"$CampaignPushClick",SignIn:"$SignIn",SignOut:"$SignOut",ObtainAdAward:"$ObtainAdAward"},Wr=[Gr.SignIn,Gr.SignOut,Gr.JOINABTASK],Yr={};Yr[Br.WINVIRTUALCOIN]={VIRTUALCURRNAME:"$VirtualCurrName",LEVEL:"$Level",ENTRY:"$Entry"},Yr[Br.CONSUMEVIRTUALCOIN]={VIRTUALCURRNAME:"$VirtualCurrName",LEVEL:"$Level",ENTRY:"$Entry"},Yr[Br.COMPLETEPURCHASE]={LEVEL:"$Level",ENTRY:"$Entry",PURCHASEENTRY:"$PurchaseEntry"},Yr[Br.NOVICEGUIDEEND]={RESULT:"$Result"},Yr[Br.ENDGAME]={RESULT:"$Result",DURATION:"$Duration"},Yr[Br.WINPROPS]={PROPS:"$Props",LEVEL:"$Level",ENTRY:"$Entry",QUANTITY:"$Quantity"},Yr[Br.CONSUMEPROPS]={PROPS:"$Props",LEVEL:"$Level",ENTRY:"$Entry",QUANTITY:"$Quantity"},Yr[Br.ADDPRODUCT2CART]={ORDERID:"$OrderId",BRAND:"$Brand",CATEGORY:"$Category",SOURCE:"$Source",MEDIUM:"$Medium",PROMOTIONNAME:"$PromotionName",CONTENT:"$Content",KEYWORDS:"$Keywords",MATERIALNAME:"$MaterialName",MATERIALSLOT:"$MaterialSlot"};var Kr=function(e){return function(t,n){var r,i,o=String(lt(t)),a=Q(n),s=o.length;return a<0||a>=s?e?"":void 0:(r=o.charCodeAt(a))<55296||r>56319||a+1===s||(i=o.charCodeAt(a+1))<56320||i>57343?e?o.charAt(a):r:e?o.slice(a,a+2):i-56320+(r-55296<<10)+65536}},qr=Kr(!0),zr=function(e,t,n){return t+(n?qr(e,t).length:1)},Jr=RegExp.prototype.exec,Xr=function(e,t){var n=e.exec;if("function"==typeof n){var r=n.call(e,t);if("object"!=typeof r)throw new TypeError("RegExp exec method returned something other than an Object or null");return r}if("RegExp"!==E(e))throw new TypeError("RegExp#exec called on incompatible receiver");return Jr.call(e,t)},Qr=function(){var e=S(this),t="";return e.global&&(t+="g"),e.ignoreCase&&(t+="i"),e.multiline&&(t+="m"),e.unicode&&(t+="u"),e.sticky&&(t+="y"),t},Zr=RegExp.prototype.exec,ei=String.prototype.replace,ti=Zr,ni=function(){var e=/a/,t=/b*/g;return Zr.call(e,"a"),Zr.call(t,"a"),0!==e.lastIndex||0!==t.lastIndex}(),ri=void 0!==/()??/.exec("")[1];(ni||ri)&&(ti=function(e){var t,n,r,i,o=this;return ri&&(n=new RegExp("^"+o.source+"$(?!\\s)",Qr.call(o))),ni&&(t=o.lastIndex),r=Zr.call(o,e),ni&&r&&(o.lastIndex=o.global?r.index+r[0].length:t),ri&&r&&r.length>1&&ei.call(r[0],n,(function(){for(i=1;i<arguments.length-2;i++)void 0===arguments[i]&&(r[i]=void 0)})),r});var ii=ti;B({target:"RegExp",proto:!0,forced:ii!==/./.exec},{exec:ii});var oi=g("species"),ai=!T((function(){var e=/./;return e.exec=function(){var e=[];return e.groups={a:"7"},e},"7"!=="".replace(e,"$<a>")})),si=function(){var e=/(?:)/,t=e.exec;e.exec=function(){return t.apply(this,arguments)};var n="ab".split(e);return 2===n.length&&"a"===n[0]&&"b"===n[1]}(),ci=function(e,t,n){var r=g(e),i=!T((function(){var t={};return t[r]=function(){return 7},7!=""[e](t)})),o=i?!T((function(){var t=!1,n=/a/;return n.exec=function(){return t=!0,null},"split"===e&&(n.constructor={},n.constructor[oi]=function(){return n}),n[r](""),!t})):void 0;if(!i||!o||"replace"===e&&!ai||"split"===e&&!si){var a=/./[r],s=n(lt,r,""[e],(function(e,t,n,r,o){return t.exec===ii?i&&!o?{done:!0,value:a.call(t,n,r)}:{done:!0,value:e.call(n,t,r)}:{done:!1}})),c=s[0],u=s[1];k(String.prototype,e,c),R(RegExp.prototype,r,2==t?function(e,t){return u.call(e,this,t)}:function(e){return u.call(e,this)})}},ui=Math.max,li=Math.min,fi=Math.floor,pi=/\$([$&`']|\d\d?|<[^>]*>)/g,hi=/\$([$&`']|\d\d?)/g;ci("replace",2,(function(e,t,n,r){return[function(r,i){var o=e(this),a=null==r?void 0:r[t];return void 0!==a?a.call(r,o,i):n.call(String(o),r,i)},function(e,t){var o=r(n,e,this,t);if(o.done)return o.value;var a=S(e),s=String(this),c="function"==typeof t;c||(t=String(t));var u=a.global;if(u){var l=a.unicode;a.lastIndex=0}for(var f=[];;){var p=Xr(a,s);if(null===p)break;if(f.push(p),!u)break;""===String(p[0])&&(a.lastIndex=zr(s,ee(a.lastIndex),l))}for(var h,d="",g=0,v=0;v<f.length;v++){p=f[v];for(var m=String(p[0]),E=ui(li(Q(p.index),s.length),0),y=[],T=1;T<p.length;T++)y.push(void 0===(h=p[T])?h:String(h));var w=p.groups;if(c){var C=[m].concat(y,E,s);void 0!==w&&C.push(w);var A=String(t.apply(void 0,C))}else A=i(m,s,E,y,w,t);E>=g&&(d+=s.slice(g,E)+A,g=E+m.length)}return d+s.slice(g)}];function i(e,t,r,i,o,a){var s=r+e.length,c=i.length,u=hi;return void 0!==o&&(o=tn(o),u=pi),n.call(a,u,(function(n,a){var u;switch(a.charAt(0)){case"$":return"$";case"&":return e;case"`":return t.slice(0,r);case"'":return t.slice(s);case"<":u=o[a.slice(1,-1)];break;default:var l=+a;if(0===l)return n;if(l>c){var f=fi(l/10);return 0===f?n:f<=c?void 0===i[f-1]?a.charAt(1):i[f-1]+a.charAt(1):n}u=i[l-1]}return void 0===u?"":u}))}}));var di=g("match"),gi=function(e){var t;return y(e)&&(void 0!==(t=e[di])?!!t:"RegExp"==a(e))},vi=Math.min,mi=[].push,Ei="length",yi=!T((function(){RegExp(4294967295,"y")}));ci("split",2,(function(e,t,n,r){var i;return i="c"=="abbc".split(/(b)*/)[1]||4!="test".split(/(?:)/,-1)[Ei]||2!="ab".split(/(?:ab)*/)[Ei]||4!=".".split(/(.?)(.?)/)[Ei]||".".split(/()()/)[Ei]>1||"".split(/.?/)[Ei]?function(e,t){var r=String(this);if(void 0===e&&0===t)return[];if(!gi(e))return n.call(r,e,t);for(var i,o,a,s=[],c=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),u=0,l=void 0===t?4294967295:t>>>0,f=new RegExp(e.source,c+"g");(i=ii.call(f,r))&&!((o=f.lastIndex)>u&&(s.push(r.slice(u,i.index)),i[Ei]>1&&i.index<r[Ei]&&mi.apply(s,i.slice(1)),a=i[0][Ei],u=o,s[Ei]>=l));)f.lastIndex===i.index&&f.lastIndex++;return u===r[Ei]?!a&&f.test("")||s.push(""):s.push(r.slice(u)),s[Ei]>l?s.slice(0,l):s}:"0".split(void 0,0)[Ei]?function(e,t){return void 0===e&&0===t?[]:n.call(this,e,t)}:n,[function(n,r){var o=e(this),a=null==n?void 0:n[t];return void 0!==a?a.call(n,o,r):i.call(String(o),n,r)},function(e,t){var o=r(i,e,this,t,i!==n);if(o.done)return o.value;var a=S(e),s=String(this),c=oe(a,RegExp),u=a.unicode,l=(a.ignoreCase?"i":"")+(a.multiline?"m":"")+(a.unicode?"u":"")+(yi?"y":"g"),f=new c(yi?a:"^(?:"+a.source+")",l),p=void 0===t?4294967295:t>>>0;if(0===p)return[];if(0===s.length)return null===Xr(f,s)?[s]:[];for(var h=0,d=0,g=[];d<s.length;){f.lastIndex=yi?d:0;var v,m=Xr(f,yi?s:s.slice(d));if(null===m||(v=vi(ee(f.lastIndex+(yi?0:d)),s.length))===h)d=zr(s,d,u);else{if(g.push(s.slice(h,d)),g.length===p)return g;for(var E=1;E<=m.length-1;E++)if(g.push(m[E]),g.length===p)return g;d=h=v}}return g.push(s.slice(h)),g}]}));var Si=an(1);B(B.P+B.F*!sn([].map,!0),"Array",{map:function(e){return Si(this,e,arguments[1])}});for(var Ti,wi=d("typed_array"),Ci=d("view"),Ai=!(!l.ArrayBuffer||!l.DataView),bi=Ai,Ii=0,Pi="Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array".split(",");Ii<9;)(Ti=l[Pi[Ii++]])?(R(Ti.prototype,wi,!0),R(Ti.prototype,Ci,!0)):bi=!1;var Oi={ABV:Ai,CONSTR:bi,TYPED:wi,VIEW:Ci},_i=function(e){if(void 0===e)return 0;var t=Q(e),n=ee(t);if(t!==n)throw RangeError("Wrong length!");return n},Ni=function(e){for(var t=tn(this),n=ee(t.length),r=arguments.length,i=yt(r>1?arguments[1]:void 0,n),o=r>2?arguments[2]:void 0,a=void 0===o?n:yt(o,n);a>i;)t[i++]=e;return t},Ri=c((function(e,t){var n=Ot.f,r=_.f,i=l.ArrayBuffer,o=l.DataView,a=l.Math,s=l.RangeError,c=l.Infinity,u=i,f=a.abs,p=a.pow,h=a.floor,d=a.log,g=a.LN2,v=w?"_b":"buffer",m=w?"_l":"byteLength",E=w?"_o":"byteOffset";function y(e,t,n){var r,i,o,a=new Array(n),s=8*n-t-1,u=(1<<s)-1,l=u>>1,v=23===t?p(2,-24)-p(2,-77):0,m=0,E=e<0||0===e&&1/e<0?1:0;for((e=f(e))!=e||e===c?(i=e!=e?1:0,r=u):(r=h(d(e)/g),e*(o=p(2,-r))<1&&(r--,o*=2),(e+=r+l>=1?v/o:v*p(2,1-l))*o>=2&&(r++,o/=2),r+l>=u?(i=0,r=u):r+l>=1?(i=(e*o-1)*p(2,t),r+=l):(i=e*p(2,l-1)*p(2,t),r=0));t>=8;a[m++]=255&i,i/=256,t-=8);for(r=r<<t|i,s+=t;s>0;a[m++]=255&r,r/=256,s-=8);return a[--m]|=128*E,a}function S(e,t,n){var r,i=8*n-t-1,o=(1<<i)-1,a=o>>1,s=i-7,u=n-1,l=e[u--],f=127&l;for(l>>=7;s>0;f=256*f+e[u],u--,s-=8);for(r=f&(1<<-s)-1,f>>=-s,s+=t;s>0;r=256*r+e[u],u--,s-=8);if(0===f)f=1-a;else{if(f===o)return r?NaN:l?-c:c;r+=p(2,t),f-=a}return(l?-1:1)*r*p(2,f-t)}function C(e){return e[3]<<24|e[2]<<16|e[1]<<8|e[0]}function A(e){return[255&e]}function b(e){return[255&e,e>>8&255]}function I(e){return[255&e,e>>8&255,e>>16&255,e>>24&255]}function P(e){return y(e,52,8)}function O(e){return y(e,23,4)}function N(e,t,n){r(e.prototype,t,{get:function(){return this[n]}})}function L(e,t,n,r){var i=_i(+n);if(i+t>e[m])throw s("Wrong index!");var o=e[v]._b,a=i+e[E],c=o.slice(a,a+t);return r?c:c.reverse()}function $(e,t,n,r,i,o){var a=_i(+n);if(a+t>e[m])throw s("Wrong index!");for(var c=e[v]._b,u=a+e[E],l=r(+i),f=0;f<t;f++)c[u+f]=l[o?f:t-f-1]}if(Oi.ABV){if(!T((function(){i(1)}))||!T((function(){new i(-1)}))||T((function(){return new i,new i(1.5),new i(NaN),"ArrayBuffer"!=i.name}))){for(var D,k=(i=function(e){return G(this,i),new u(_i(e))}).prototype=u.prototype,U=n(u),M=0;U.length>M;)(D=U[M++])in i||R(i,D,u[D]);k.constructor=i}var x=new o(new i(2)),V=o.prototype.setInt8;x.setInt8(0,2147483648),x.setInt8(1,2147483649),!x.getInt8(0)&&x.getInt8(1)||_e(o.prototype,{setInt8:function(e,t){V.call(this,e,t<<24>>24)},setUint8:function(e,t){V.call(this,e,t<<24>>24)}},!0)}else i=function(e){G(this,i,"ArrayBuffer");var t=_i(e);this._b=Ni.call(new Array(t),0),this[m]=t},o=function(e,t,n){G(this,o,"DataView"),G(e,i,"DataView");var r=e[m],a=Q(t);if(a<0||a>r)throw s("Wrong offset!");if(a+(n=void 0===n?r-a:ee(n))>r)throw s("Wrong length!");this[v]=e,this[E]=a,this[m]=n},w&&(N(i,"byteLength","_l"),N(o,"buffer","_b"),N(o,"byteLength","_l"),N(o,"byteOffset","_o")),_e(o.prototype,{getInt8:function(e){return L(this,1,e)[0]<<24>>24},getUint8:function(e){return L(this,1,e)[0]},getInt16:function(e){var t=L(this,2,e,arguments[1]);return(t[1]<<8|t[0])<<16>>16},getUint16:function(e){var t=L(this,2,e,arguments[1]);return t[1]<<8|t[0]},getInt32:function(e){return C(L(this,4,e,arguments[1]))},getUint32:function(e){return C(L(this,4,e,arguments[1]))>>>0},getFloat32:function(e){return S(L(this,4,e,arguments[1]),23,4)},getFloat64:function(e){return S(L(this,8,e,arguments[1]),52,8)},setInt8:function(e,t){$(this,1,e,A,t)},setUint8:function(e,t){$(this,1,e,A,t)},setInt16:function(e,t){$(this,2,e,b,t,arguments[2])},setUint16:function(e,t){$(this,2,e,b,t,arguments[2])},setInt32:function(e,t){$(this,4,e,I,t,arguments[2])},setUint32:function(e,t){$(this,4,e,I,t,arguments[2])},setFloat32:function(e,t){$(this,4,e,O,t,arguments[2])},setFloat64:function(e,t){$(this,8,e,P,t,arguments[2])}});Le(i,"ArrayBuffer"),Le(o,"DataView"),R(o.prototype,Oi.VIEW,!0),t.ArrayBuffer=i,t.DataView=o})),Li=wt("IE_PROTO"),$i=Object.prototype,Di=Object.getPrototypeOf||function(e){return e=tn(e),$(e,Li)?e[Li]:"function"==typeof e.constructor&&e instanceof e.constructor?e.constructor.prototype:e instanceof Object?$i:null},ki=g("unscopables"),Ui=Array.prototype;null==Ui[ki]&&R(Ui,ki,{});var Mi=function(e){Ui[ki][e]=!0},xi=function(e,t){return{value:t,done:!!e}},Vi={};R(Vi,g("iterator"),(function(){return this}));var Hi=function(e,t,n){e.prototype=Ft(Vi,{next:N(1,n)}),Le(e,t+" Iterator")},Fi=g("iterator"),ji=!([].keys&&"next"in[].keys()),Bi=function(){return this},Gi=function(e,t,n,r,i,o,a){Hi(n,t,r);var s,c,u,l=function(e){if(!ji&&e in d)return d[e];switch(e){case"keys":case"values":return function(){return new n(this,e)}}return function(){return new n(this,e)}},f=t+" Iterator",p="values"==i,h=!1,d=e.prototype,g=d[Fi]||d["@@iterator"]||i&&d[i],v=g||l(i),m=i?p?l("entries"):v:void 0,E="Array"==t&&d.entries||g;if(E&&(u=Di(E.call(new e)))!==Object.prototype&&u.next&&(Le(u,f,!0),"function"!=typeof u[Fi]&&R(u,Fi,Bi)),p&&g&&"values"!==g.name&&(h=!0,v=function(){return g.call(this)}),(ji||h||!d[Fi])&&R(d,Fi,v),Y[t]=v,Y[f]=Bi,i)if(s={values:p?v:l("values"),keys:o?v:l("keys"),entries:m},a)for(c in s)c in d||k(d,c,s[c]);else B(B.P+B.F*(ji||h),t,s);return s},Wi=Gi(Array,"Array",(function(e,t){this._t=ft(e),this._i=0,this._k=t}),(function(){var e=this._t,t=this._k,n=this._i++;return!e||n>=e.length?(this._t=void 0,xi(1)):xi(0,"keys"==t?n:"values"==t?e[n]:[n,e[n]])}),"values");Y.Arguments=Y.Array,Mi("keys"),Mi("values"),Mi("entries");var Yi=[].copyWithin||function(e,t){var n=tn(this),r=ee(n.length),i=yt(e,r),o=yt(t,r),a=arguments.length>2?arguments[2]:void 0,s=Math.min((void 0===a?r:yt(a,r))-o,r-i),c=1;for(o<i&&i<o+s&&(c=-1,o+=s-1,i+=s-1);s-- >0;)o in n?n[i]=n[o]:delete n[i],i+=c,o+=c;return n};c((function(e){if(w){var t=l,n=T,r=B,i=Oi,o=Ri,a=x,s=G,c=N,u=R,f=_e,p=Q,h=ee,v=_i,m=yt,S=P,C=$,A=E,b=y,I=tn,O=z,L=Ft,D=Di,k=Ot.f,U=ne,M=d,V=g,H=an,F=St,j=oe,W=Wi,K=Y,q=je,J=De,X=Ni,Z=Yi,te=_,re=ht,ie=te.f,ae=re.f,se=t.RangeError,ce=t.TypeError,ue=t.Uint8Array,le=Array.prototype,fe=o.ArrayBuffer,pe=o.DataView,he=H(0),de=H(2),ge=H(3),ve=H(4),me=H(5),Ee=H(6),ye=F(!0),Se=F(!1),Te=W.values,we=W.keys,Ce=W.entries,Ae=le.lastIndexOf,be=le.reduce,Ie=le.reduceRight,Pe=le.join,Oe=le.sort,Ne=le.slice,Re=le.toString,Le=le.toLocaleString,$e=V("iterator"),ke=V("toStringTag"),Ue=M("typed_constructor"),Me=M("def_constructor"),xe=i.CONSTR,Ve=i.TYPED,He=i.VIEW,Fe=H(1,(function(e,t){return Ke(j(e,e[Me]),t)})),Be=n((function(){return 1===new ue(new Uint16Array([1]).buffer)[0]})),Ge=!!ue&&!!ue.prototype.set&&n((function(){new ue(1).set({})})),We=function(e,t){var n=p(e);if(n<0||n%t)throw se("Wrong offset!");return n},Ye=function(e){if(b(e)&&Ve in e)return e;throw ce(e+" is not a typed array!")},Ke=function(e,t){if(!b(e)||!(Ue in e))throw ce("It is not a typed array constructor!");return new e(t)},qe=function(e,t){return ze(j(e,e[Me]),t)},ze=function(e,t){for(var n=0,r=t.length,i=Ke(e,r);r>n;)i[n]=t[n++];return i},Je=function(e,t,n){ie(e,t,{get:function(){return this._d[n]}})},Xe=function(e){var t,n,r,i,o,s,c=I(e),u=arguments.length,l=u>1?arguments[1]:void 0,f=void 0!==l,p=U(c);if(null!=p&&!O(p)){for(s=p.call(c),r=[],t=0;!(o=s.next()).done;t++)r.push(o.value);c=r}for(f&&u>2&&(l=a(l,arguments[2],2)),t=0,n=h(c.length),i=Ke(this,n);n>t;t++)i[t]=f?l(c[t],t):c[t];return i},Qe=function(){for(var e=0,t=arguments.length,n=Ke(this,t);t>e;)n[e]=arguments[e++];return n},Ze=!!ue&&n((function(){Le.call(new ue(1))})),et=function(){return Le.apply(Ze?Ne.call(Ye(this)):Ye(this),arguments)},tt={copyWithin:function(e,t){return Z.call(Ye(this),e,t,arguments.length>2?arguments[2]:void 0)},every:function(e){return ve(Ye(this),e,arguments.length>1?arguments[1]:void 0)},fill:function(e){return X.apply(Ye(this),arguments)},filter:function(e){return qe(this,de(Ye(this),e,arguments.length>1?arguments[1]:void 0))},find:function(e){return me(Ye(this),e,arguments.length>1?arguments[1]:void 0)},findIndex:function(e){return Ee(Ye(this),e,arguments.length>1?arguments[1]:void 0)},forEach:function(e){he(Ye(this),e,arguments.length>1?arguments[1]:void 0)},indexOf:function(e){return Se(Ye(this),e,arguments.length>1?arguments[1]:void 0)},includes:function(e){return ye(Ye(this),e,arguments.length>1?arguments[1]:void 0)},join:function(e){return Pe.apply(Ye(this),arguments)},lastIndexOf:function(e){return Ae.apply(Ye(this),arguments)},map:function(e){return Fe(Ye(this),e,arguments.length>1?arguments[1]:void 0)},reduce:function(e){return be.apply(Ye(this),arguments)},reduceRight:function(e){return Ie.apply(Ye(this),arguments)},reverse:function(){for(var e,t=Ye(this).length,n=Math.floor(t/2),r=0;r<n;)e=this[r],this[r++]=this[--t],this[t]=e;return this},some:function(e){return ge(Ye(this),e,arguments.length>1?arguments[1]:void 0)},sort:function(e){return Oe.call(Ye(this),e)},subarray:function(e,t){var n=Ye(this),r=n.length,i=m(e,r);return new(j(n,n[Me]))(n.buffer,n.byteOffset+i*n.BYTES_PER_ELEMENT,h((void 0===t?r:m(t,r))-i))}},nt=function(e,t){return qe(this,Ne.call(Ye(this),e,t))},rt=function(e){Ye(this);var t=We(arguments[1],1),n=this.length,r=I(e),i=h(r.length),o=0;if(i+t>n)throw se("Wrong length!");for(;o<i;)this[t+o]=r[o++]},it={entries:function(){return Ce.call(Ye(this))},keys:function(){return we.call(Ye(this))},values:function(){return Te.call(Ye(this))}},ot=function(e,t){return b(e)&&e[Ve]&&"symbol"!=typeof t&&t in e&&String(+t)==String(t)},at=function(e,t){return ot(e,t=S(t,!0))?c(2,e[t]):ae(e,t)},st=function(e,t,n){return!(ot(e,t=S(t,!0))&&b(n)&&C(n,"value"))||C(n,"get")||C(n,"set")||n.configurable||C(n,"writable")&&!n.writable||C(n,"enumerable")&&!n.enumerable?ie(e,t,n):(e[t]=n.value,e)};xe||(re.f=at,te.f=st),r(r.S+r.F*!xe,"Object",{getOwnPropertyDescriptor:at,defineProperty:st}),n((function(){Re.call({})}))&&(Re=Le=function(){return Pe.call(this)});var ct=f({},tt);f(ct,it),u(ct,$e,it.values),f(ct,{slice:nt,set:rt,constructor:function(){},toString:Re,toLocaleString:et}),Je(ct,"buffer","b"),Je(ct,"byteOffset","o"),Je(ct,"byteLength","l"),Je(ct,"length","e"),ie(ct,ke,{get:function(){return this[Ve]}}),e.exports=function(e,o,a,c){var l=e+((c=!!c)?"Clamped":"")+"Array",f="get"+e,p="set"+e,d=t[l],g=d||{},m=d&&D(d),E=!d||!i.ABV,y={},S=d&&d.prototype,T=function(e,t){ie(e,t,{get:function(){return function(e,t){var n=e._d;return n.v[f](t*o+n.o,Be)}(this,t)},set:function(e){return function(e,t,n){var r=e._d;c&&(n=(n=Math.round(n))<0?0:n>255?255:255&n),r.v[p](t*o+r.o,n,Be)}(this,t,e)},enumerable:!0})};E?(d=a((function(e,t,n,r){s(e,d,l,"_d");var i,a,c,f,p=0,g=0;if(b(t)){if(!(t instanceof fe||"ArrayBuffer"==(f=A(t))||"SharedArrayBuffer"==f))return Ve in t?ze(d,t):Xe.call(d,t);i=t,g=We(n,o);var m=t.byteLength;if(void 0===r){if(m%o)throw se("Wrong length!");if((a=m-g)<0)throw se("Wrong length!")}else if((a=h(r)*o)+g>m)throw se("Wrong length!");c=a/o}else c=v(t),i=new fe(a=c*o);for(u(e,"_d",{b:i,o:g,l:a,e:c,v:new pe(i)});p<c;)T(e,p++)})),S=d.prototype=L(ct),u(S,"constructor",d)):n((function(){d(1)}))&&n((function(){new d(-1)}))&&q((function(e){new d,new d(null),new d(1.5),new d(e)}),!0)||(d=a((function(e,t,n,r){var i;return s(e,d,l),b(t)?t instanceof fe||"ArrayBuffer"==(i=A(t))||"SharedArrayBuffer"==i?void 0!==r?new g(t,We(n,o),r):void 0!==n?new g(t,We(n,o)):new g(t):Ve in t?ze(d,t):Xe.call(d,t):new g(v(t))})),he(m!==Function.prototype?k(g).concat(k(m)):k(g),(function(e){e in d||u(d,e,g[e])})),d.prototype=S,S.constructor=d);var w=S[$e],C=!!w&&("values"==w.name||null==w.name),I=it.values;u(d,Ue,!0),u(S,Ve,l),u(S,He,!0),u(S,Me,d),(c?new d(1)[ke]==l:ke in S)||ie(S,ke,{get:function(){return l}}),y[l]=d,r(r.G+r.W+r.F*(d!=g),y),r(r.S,l,{BYTES_PER_ELEMENT:o}),r(r.S+r.F*n((function(){g.of.call(d,1)})),l,{from:Xe,of:Qe}),"BYTES_PER_ELEMENT"in S||u(S,"BYTES_PER_ELEMENT",o),r(r.P,l,tt),J(l),r(r.P+r.F*Ge,l,{set:rt}),r(r.P+r.F*!C,l,it),S.toString!=Re&&(S.toString=Re),r(r.P+r.F*n((function(){new d(1).slice()})),l,{slice:nt}),r(r.P+r.F*(n((function(){return[1,2].toLocaleString()!=new d([1,2]).toLocaleString()}))||!n((function(){S.toLocaleString.call([1,2])}))),l,{toLocaleString:et}),K[l]=C?w:I,C||u(S,$e,I)}}else e.exports=function(){}}))("Uint8",1,(function(e){return function(t,n,r){return e(this,t,n,r)}}));var Ki=c((function(e){var t=d("meta"),n=_.f,r=0,i=Object.isExtensible||function(){return!0},o=!T((function(){return i(Object.preventExtensions({}))})),a=function(e){n(e,t,{value:{i:"O"+ ++r,w:{}}})},s=e.exports={KEY:t,NEED:!1,fastKey:function(e,n){if(!y(e))return"symbol"==typeof e?e:("string"==typeof e?"S":"P")+e;if(!$(e,t)){if(!i(e))return"F";if(!n)return"E";a(e)}return e[t].i},getWeak:function(e,n){if(!$(e,t)){if(!i(e))return!0;if(!n)return!1;a(e)}return e[t].w},onFreeze:function(e){return o&&s.NEED&&i(e)&&!$(e,t)&&a(e),e}}}));Ki.KEY,Ki.NEED,Ki.fastKey,Ki.getWeak,Ki.onFreeze;var qi={f:g},zi=_.f,Ji=function(e){var t=u.Symbol||(u.Symbol=l.Symbol||{});"_"==e.charAt(0)||e in t||zi(t,e,{value:qi.f(e)})},Xi={f:Object.getOwnPropertySymbols},Qi=Ot.f,Zi={}.toString,eo="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],to={f:function(e){return eo&&"[object Window]"==Zi.call(e)?function(e){try{return Qi(e)}catch(e){return eo.slice()}}(e):Qi(ft(e))}},no=Ki.KEY,ro=ht.f,io=_.f,oo=to.f,ao=l.Symbol,so=l.JSON,co=so&&so.stringify,uo=g("_hidden"),lo=g("toPrimitive"),fo={}.propertyIsEnumerable,po=f("symbol-registry"),ho=f("symbols"),go=f("op-symbols"),vo=Object.prototype,mo="function"==typeof ao&&!!Xi.f,Eo=l.QObject,yo=!Eo||!Eo.prototype||!Eo.prototype.findChild,So=w&&T((function(){return 7!=Ft(io({},"a",{get:function(){return io(this,"a",{value:7}).a}})).a}))?function(e,t,n){var r=ro(vo,t);r&&delete vo[t],io(e,t,n),r&&e!==vo&&io(vo,t,r)}:io,To=function(e){var t=ho[e]=Ft(ao.prototype);return t._k=e,t},wo=mo&&"symbol"==typeof ao.iterator?function(e){return"symbol"==typeof e}:function(e){return e instanceof ao},Co=function(e,t,n){return e===vo&&Co(go,t,n),S(e),t=P(t,!0),S(n),$(ho,t)?(n.enumerable?($(e,uo)&&e[uo][t]&&(e[uo][t]=!1),n=Ft(n,{enumerable:N(0,!1)})):($(e,uo)||io(e,uo,N(1,{})),e[uo][t]=!0),So(e,t,n)):io(e,t,n)},Ao=function(e,t){S(e);for(var n,r=function(e){var t=Ut(e),n=Xi.f;if(n)for(var r,i=n(e),o=ct.f,a=0;i.length>a;)o.call(e,r=i[a++])&&t.push(r);return t}(t=ft(t)),i=0,o=r.length;o>i;)Co(e,n=r[i++],t[n]);return e},bo=function(e){var t=fo.call(this,e=P(e,!0));return!(this===vo&&$(ho,e)&&!$(go,e))&&(!(t||!$(this,e)||!$(ho,e)||$(this,uo)&&this[uo][e])||t)},Io=function(e,t){if(e=ft(e),t=P(t,!0),e!==vo||!$(ho,t)||$(go,t)){var n=ro(e,t);return!n||!$(ho,t)||$(e,uo)&&e[uo][t]||(n.enumerable=!0),n}},Po=function(e){for(var t,n=oo(ft(e)),r=[],i=0;n.length>i;)$(ho,t=n[i++])||t==uo||t==no||r.push(t);return r},Oo=function(e){for(var t,n=e===vo,r=oo(n?go:ft(e)),i=[],o=0;r.length>o;)!$(ho,t=r[o++])||n&&!$(vo,t)||i.push(ho[t]);return i};mo||(k((ao=function(){if(this instanceof ao)throw TypeError("Symbol is not a constructor!");var e=d(arguments.length>0?arguments[0]:void 0),t=function(n){this===vo&&t.call(go,n),$(this,uo)&&$(this[uo],e)&&(this[uo][e]=!1),So(this,e,N(1,n))};return w&&yo&&So(vo,e,{configurable:!0,set:t}),To(e)}).prototype,"toString",(function(){return this._k})),ht.f=Io,_.f=Co,Ot.f=to.f=Po,ct.f=bo,Xi.f=Oo,w&&k(vo,"propertyIsEnumerable",bo,!0),qi.f=function(e){return To(g(e))}),B(B.G+B.W+B.F*!mo,{Symbol:ao});for(var _o="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),No=0;_o.length>No;)g(_o[No++]);for(var Ro=Ut(g.store),Lo=0;Ro.length>Lo;)Ji(Ro[Lo++]);B(B.S+B.F*!mo,"Symbol",{for:function(e){return $(po,e+="")?po[e]:po[e]=ao(e)},keyFor:function(e){if(!wo(e))throw TypeError(e+" is not a symbol!");for(var t in po)if(po[t]===e)return t},useSetter:function(){yo=!0},useSimple:function(){yo=!1}}),B(B.S+B.F*!mo,"Object",{create:function(e,t){return void 0===t?Ft(e):Ao(Ft(e),t)},defineProperty:Co,defineProperties:Ao,getOwnPropertyDescriptor:Io,getOwnPropertyNames:Po,getOwnPropertySymbols:Oo});var $o=T((function(){Xi.f(1)}));B(B.S+B.F*$o,"Object",{getOwnPropertySymbols:function(e){return Xi.f(tn(e))}}),so&&B(B.S+B.F*(!mo||T((function(){var e=ao();return"[null]"!=co([e])||"{}"!=co({a:e})||"{}"!=co(Object(e))}))),"JSON",{stringify:function(e){for(var t,n,r=[e],i=1;arguments.length>i;)r.push(arguments[i++]);if(n=t=r[1],(y(t)||void 0!==e)&&!wo(e))return nn(t)||(t=function(e,t){if("function"==typeof n&&(t=n.call(this,e,t)),!wo(t))return t}),r[1]=t,co.apply(so,r)}}),ao.prototype[lo]||R(ao.prototype,lo,ao.prototype.valueOf),Le(ao,"Symbol"),Le(Math,"Math",!0),Le(l.JSON,"JSON",!0);var Do=function(e,t,n){t in e?_.f(e,t,N(0,n)):e[t]=n};B(B.S+B.F*!je((function(e){Array.from(e)})),"Array",{from:function(e){var t,n,r,i,o=tn(e),a="function"==typeof this?this:Array,s=arguments.length,c=s>1?arguments[1]:void 0,u=void 0!==c,l=0,f=ne(o);if(u&&(c=x(c,s>2?arguments[2]:void 0,2)),null==f||a==Array&&z(f))for(n=new a(t=ee(o.length));t>l;l++)Do(n,l,u?c(o[l],l):o[l]);else for(i=f.call(o),n=new a;!(r=i.next()).done;l++)Do(n,l,u?W(i,c,[r.value,l],!0):r.value);return n.length=l,n}});var ko=Kr(!0);Gi(String,"String",(function(e){this._t=String(e),this._i=0}),(function(){var e,t=this._t,n=this._i;return n>=t.length?{value:void 0,done:!0}:(e=ko(t,n),this._i+=e.length,{value:e,done:!1})}));for(var Uo=g("iterator"),Mo=g("toStringTag"),xo=Y.Array,Vo={CSSRuleList:!0,CSSStyleDeclaration:!1,CSSValueList:!1,ClientRectList:!1,DOMRectList:!1,DOMStringList:!1,DOMTokenList:!0,DataTransferItemList:!1,FileList:!1,HTMLAllCollection:!1,HTMLCollection:!1,HTMLFormElement:!1,HTMLSelectElement:!1,MediaList:!0,MimeTypeArray:!1,NamedNodeMap:!1,NodeList:!0,PaintRequestList:!1,Plugin:!1,PluginArray:!1,SVGLengthList:!1,SVGNumberList:!1,SVGPathSegList:!1,SVGPointList:!1,SVGStringList:!1,SVGTransformList:!1,SourceBufferList:!1,StyleSheetList:!0,TextTrackCueList:!1,TextTrackList:!1,TouchList:!1},Ho=Ut(Vo),Fo=0;Fo<Ho.length;Fo++){var jo,Bo=Ho[Fo],Go=Vo[Bo],Wo=l[Bo],Yo=Wo&&Wo.prototype;if(Yo&&(Yo[Uo]||R(Yo,Uo,xo),Yo[Mo]||R(Yo,Mo,Bo),Y[Bo]=xo,Go))for(jo in Wi)Yo[jo]||k(Yo,jo,Wi[jo],!0)}var Ko=Date.prototype,qo=Ko.toString,zo=Ko.getTime;new Date(NaN)+""!="Invalid Date"&&k(Ko,"toString",(function(){var e=zo.call(this);return e==e?qo.call(this):"Invalid Date"})),w&&"g"!=/./g.flags&&_.f(RegExp.prototype,"flags",{configurable:!0,get:Qr});var Jo=/./.toString,Xo=function(e){k(RegExp.prototype,"toString",e,!0)};function Qo(e){return null==e||""===e}function Zo(e){return"string"==typeof e&&!Qo(e)}function ea(e,t){if(!Zo(e))return t&&t(),!1;for(var n=!1,r=(e=e.replace(/,$/gi,"")).split(","),i=0;i<r.length;i++)Qo(r[i])?t&&t(i):0===i&&(n=!0);return n}T((function(){return"/a/b"!=Jo.call({source:"a",flags:"b"})}))?Xo((function(){var e=S(this);return"/".concat(e.source,"/","flags"in e?e.flags:!w&&e instanceof RegExp?Qr.call(e):void 0)})):"toString"!=Jo.name&&Xo((function(){return Jo.call(this)})),ci("match",1,(function(e,t,n,r){return[function(n){var r=e(this),i=null==n?void 0:n[t];return void 0!==i?i.call(n,r):new RegExp(n)[t](String(r))},function(e){var t=r(n,e,this);if(t.done)return t.value;var i=S(e),o=String(this);if(!i.global)return Xr(i,o);var a=i.unicode;i.lastIndex=0;for(var s,c=[],u=0;null!==(s=Xr(i,o));){var l=String(s[0]);c[u]=l,""===l&&(i.lastIndex=zr(o,ee(i.lastIndex),a)),u++}return 0===u?null:c}]})),B(B.P,"Array",{fill:Ni}),Mi("fill");var ta=new(function(){function e(){}return e.prototype.create=function(){var e=Math.floor(Math.random()*(Math.pow(2,12)-1-0+1))+0,t=Math.floor(Math.random()*(Math.pow(2,32)-1-0+1))+0,n=Math.floor(Math.random()*(Math.pow(2,16)-1-0+1))+0,r=Math.floor(Math.random()*(Math.pow(2,6)-1-0+1))+0,i=Math.floor(Math.random()*(Math.pow(2,8)-1-0+1))+0,o=(0|Math.random()*(1<<30))+(0|Math.random()*(1<<18))*(1<<30);function a(e,t,n){n=n||"0";for(var r=t-(e=String(e)).length;r>0;r>>>=1,n+=n)1&r&&(e=n+e);return e}return[a(t.toString(16),8),a(n.toString(16),4),a((16384|e).toString(16),4),a((128|r).toString(16),2),a(i.toString(16),2),a(o.toString(16),12)].join("")},e}());B(B.S,"Array",{isArray:nn});var na=_.f,ra=Ot.f,ia=l.RegExp,oa=ia,aa=ia.prototype,sa=/a/g,ca=/a/g,ua=new ia(sa)!==sa;if(w&&(!ua||T((function(){return ca[g("match")]=!1,ia(sa)!=sa||ia(ca)==ca||"/a/i"!=ia(sa,"i")})))){ia=function(e,t){var n=this instanceof ia,r=gi(e),i=void 0===t;return!n&&r&&e.constructor===ia&&i?e:vt(ua?new oa(r&&!i?e.source:e,t):oa((r=e instanceof ia)?e.source:e,r&&i?Qr.call(e):t),n?this:aa,ia)};for(var la=function(e){e in ia||na(ia,e,{configurable:!0,get:function(){return oa[e]},set:function(t){oa[e]=t}})},fa=ra(oa),pa=0;fa.length>pa;)la(fa[pa++]);aa.constructor=ia,ia.prototype=aa,k(l,"RegExp",ia)}function ha(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=!1),"object"!==hn(e))return e;if(Array.isArray(e)){for(var r=[],i=0;i<e.length;i++)-1===t.indexOf(e[i])&&r.push(ha(e[i]));return r}if(null!=e&&e.constructor!==RegExp){var o={};for(var a in e)(n||"function"!=typeof e[a])&&-1===t.indexOf(a)&&(o[a]=ha(e[a]));return o}return e}function da(e,t){return null!=e&&Object.prototype.hasOwnProperty.call(e,t)}function ga(e,t){return-1!==e.indexOf(t)}De("RegExp");var va=new(function(){function e(){this.sessionDuration=18e5,this.minSessionDuration=3e4,this.defaultEventParams={},this.expiryTimeInfinity=-1,this.keyList=[],this.dataList={},this.initCompletedAt=0,this.initPromise=null}return e.prototype.init=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r,i;return pn(this,(function(o){switch(o.label){case 0:return this.initPromise?[2]:[4,ls.hiMigration.migrate()];case 1:return o.sent(),e=this.initKeyNameList(),t=this,[4,this.clearInvalidKey(e)];case 2:t.keyList=o.sent(),n=0,o.label=3;case 3:return n<this.keyList.length?(r=this.keyList[n],[4,ls.hiStorage.getSavedObject(r)]):[3,6];case 4:if(i=o.sent()||{},!this.isValidStoredItem(i))return[3,5];-1!=this.initNoRefreshKeyList().indexOf(r)?this.dataList[r]=i:this.saveClientValue(r,i.value),o.label=5;case 5:return n++,[3,3];case 6:return this.initCompletedAt=(new Date).getTime(),ls.sendTask.processEventsReceivedAfterLaunch(),[2]}}))}))},e.prototype.clearInvalidKey=function(e){return fn(this,void 0,void 0,(function(){var t,n;return pn(this,(function(r){switch(r.label){case 0:t=0,r.label=1;case 1:return t<e.length?(n=e[t],[4,ls.hiStorage.isKeyExist(n)]):[3,4];case 2:r.sent()||(e.splice(t,1),t--),r.label=3;case 3:return t++,[3,1];case 4:return[2,e]}}))}))},e.prototype.handleSessionValue=function(e,t){void 0===e&&(e=!0),void 0===t&&(t=!1);var n=this.getSessionValue(),r=e?ta.create()+"_"+(new Date).getTime():n||ta.create()+"_"+(new Date).getTime(),i=ls.sendTask.getCurFocusStateSessionDuration(!e&&!Qo(n)&&!t);this.saveClientValue(Ur,r,i),n!==r&&(ls.sendTask.upLoadService.isInit=!1)},e.prototype.getSessionValue=function(){return this.getClientValue(Ur)},e.prototype.setSessionDuration=function(e){Number.isInteger(e)&&(this.sessionDuration=e,this.handleSessionValue())},e.prototype.setBackgroundSessionDuration=function(e){Number.isInteger(e)&&(this.minSessionDuration=e,this.handleSessionValue())},e.prototype.getClientExpire=function(e){var t=this;return this.isValidStoredItem(this.dataList[e],(function(){Qo(t.dataList[e])||t.removeClientValue(e)}))?this.dataList[e].expiryTime:0},e.prototype.getClientValue=function(e){var t=this;return this.isValidStoredItem(this.dataList[e],(function(){Qo(t.dataList[e])||t.removeClientValue(e)}))?this.dataList[e].value:""},e.prototype.removeClientValue=function(e){delete this.dataList[e],ls.hiStorage.removeItem(e);var t=this.keyList.indexOf(e);-1!==t&&delete this.keyList[t]},e.prototype.isKeyExist=function(e){return!!this.getClientValue(e)},e.prototype.saveClientValue=function(e,t,n){this.dataList[e]={value:t,expiryTime:this.makeExpiryTime(n)},ls.hiStorage.saveClientValue(e,t,n),-1==this.keyList.indexOf(e)&&this.keyList.push(e)},e.prototype.isValidStoredItem=function(e,t){return null===e||""===e||void 0===(n="string"==typeof e?JSON.parse(e):e)||isNaN(n.expiryTime)||!1===da(n,"value")?(t&&t(),!1):!(n.expiryTime!==this.expiryTimeInfinity&&n.expiryTime<(new Date).getTime())||(t&&t(),!1);var n},e.prototype.makeExpiryTime=function(e){var t=(new Date).getTime()+31536e6;return e&&!isNaN(e)&&e>0&&(t=(new Date).getTime()+e),t},e.prototype.initKeyNameList=function(){return["HW_ha_clientOaidFlag","HW_ha_serverOaidFlag","HW_ha_pushTokenFlag","HW_ha_getConfigTime",Hr,"HW_ha_isTestDevice","HW_ha_isNewUser",Ur,"HW_ha_appVersion","HW_ha_saltExpire","HW_ha_wxAppId",Fr.CN,Fr.DE,Fr.RU,Fr.SG,jr.CN,jr.DE,jr.RU,jr.SG,"HW_ha_eventUploadPolicy","HW_ha_disableEvents","HW_ha_apiChannel","HW_ha_firstOpenTime","HW_ha_existShortcut","HW_ha_existUserId",Mr,Vr,xr]},e.prototype.initNoRefreshKeyList=function(){return[Ur,"HW_ha_saltExpire",Fr.CN,Fr.DE,Fr.RU,Fr.SG,jr.CN,jr.DE,jr.RU,jr.SG]},e}());kt("trim",(function(e){return function(){return e(this,3)}})),B(B.S,"Date",{now:function(){return(new Date).getTime()}});var ma=Object.assign,Ea=!ma||T((function(){var e={},t={},n=Symbol(),r="abcdefghijklmnopqrst";return e[n]=7,r.split("").forEach((function(e){t[e]=e})),7!=ma({},e)[n]||Object.keys(ma({},t)).join("")!=r}))?function(e,t){for(var n=tn(e),r=arguments.length,i=1,o=Xi.f,a=ct.f;r>i;)for(var s,c=ut(arguments[i++]),u=o?Ut(c).concat(o(c)):Ut(c),l=u.length,f=0;l>f;)s=u[f++],w&&!a.call(c,s)||(n[s]=c[s]);return n}:ma;B(B.S+B.F,"Object",{assign:Ea});var ya={code:"CE-001",message:"Cannot find agcgw/url from agconnect-service.json."},Sa={code:"PE-002",message:"Length of $1 exceeds the limit. Max Length is $2."},Ta={code:"PE-003",message:"$1 missed."},wa={code:"PE-004",message:"$1 is reserved."},Ca={code:"PE-005",message:"Too many $1 parameters. Max number of parameters is $2."},Aa={code:"PE-006",message:"$1 is invalid."},ba={code:"PE-007",message:"The size exceeds the limit, the maximum size is $1."},Ia={code:"IE-004",message:"Missing agconnect sdk."},Pa={code:"IE-006",message:"The Analytics Kit is disabled."},Oa={code:"SE-001",message:"Failed to initialize the Analytics Kit."},_a={code:"SE-002",message:"Failed to obtain AAID from hms core sdk."},Na={code:"SE-003",message:"Failed to obtain token from agconnect sdk."},Ra=function(){function e(){}return e.checkString=function(e,t,n,r){return Qo(n)?(ls.hiLog.warn(e,Ta.code,Ta.message,{$1:t}),!1):!(String(n).length>r)||(ls.hiLog.warn(e,Sa.code,Sa.message,{$1:t,$2:r}),!1)},e.checkPattern=function(e,t,n,r){return!!r.test(n)||(ls.hiLog.warn(e,Aa.code,Aa.message,{$1:t}),!1)},e.checkReserve=function(e,t,n,r){for(var i in r)if(n===r[i])return ls.hiLog.warn(e,wa.code,wa.message,{$1:t}),!1;return!0},e.checkLength=function(e,t,n,r){return!(n>r)||(ls.hiLog.warn(e,Ca.code,Ca.message,{$1:t,$2:r}),!1)},e.checkSwitchIsOpen=function(e,t){return!!t||(ls.hiLog.warn(e,Pa.code,Pa.message),!1)},e.checkExceedMaxSize=function(e,t,n,r){return!(n>r)||(ls.hiLog.warn(e,ba.code,ba.message,{$1:t}),!1)},e.checkEmpty=function(e,t,n){return!Qo(n)||(ls.hiLog.warn(e,Ta.code,Ta.message,{$1:t}),!1)},e}(),La="1.0",$a="web",Da="hianalytics",ka="Analytics",Ua="aaid",Ma="token",xa=an(5),Va=!0;"find"in[]&&Array(1).find((function(){Va=!1})),B(B.P+B.F*Va,"Array",{find:function(e){return xa(this,e,arguments.length>1?arguments[1]:void 0)}}),Mi("find");var Ha=new(function(){function e(){this.attributesCollectors=[]}return e.prototype.register=function(e){this.attributesCollectors.push(e)},e.prototype.collectAll=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r;return pn(this,(function(i){switch(i.label){case 0:e={},t=0,n=this.attributesCollectors,i.label=1;case 1:return t<n.length?[4,n[t].doCollect()]:[3,4];case 2:r=i.sent(),Object.assign(e,r),i.label=3;case 3:return t++,[3,1];case 4:return[2,e]}}))}))},e.prototype.collect=function(e){return fn(this,void 0,void 0,(function(){var t,n;return pn(this,(function(r){switch(r.label){case 0:return t={},n={},this.attributesCollectors.some((function(t,r){return t.key===e&&(n=t,!0)})),n.doCollect?[4,n.doCollect()]:[3,2];case 1:t=r.sent(),r.label=2;case 2:return[2,t]}}))}))},e.prototype.syncCollect=function(e){return fn(this,void 0,void 0,(function(){var t,n;return pn(this,(function(r){switch(r.label){case 0:return t={},(n=this.attributesCollectors.find((function(t){return t.key===e})))&&n.doCollect?[4,n.doCollect()]:[3,2];case 1:t=r.sent(),r.label=2;case 2:return[2,t]}}))}))},e}()),Fa=new(function(){function e(){this.tag="AaidCollector",this.key=Ua,this.cache=null}return e.prototype.doCollect=function(){return fn(this,void 0,void 0,(function(){var e;return pn(this,(function(t){switch(t.label){case 0:return this.cache&&this.cache[this.key]?[3,2]:(this.cache=this.cache||{},[4,ls.hiAgcUtil.getAaid(this.tag)]);case 1:(e=t.sent())&&(this.cache[this.key]=e),t.label=2;case 2:return[2,this.cache]}}))}))},e.prototype.getAaid=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.doCollect()];case 1:return e.sent(),[2,this.cache[Ua]||""]}}))}))},e.prototype.refreshAaid=function(){return fn(this,void 0,void 0,(function(){var e;return pn(this,(function(t){switch(t.label){case 0:return delete this.cache[Ua],[4,ls.hiAgcUtil.deleteAaid()];case 1:return t.sent(),[4,this.doCollect()];case 2:return(e=t.sent())[Ua]&&(os.addConfig("aaid",e[Ua]),os.setIsTestDevice(),va.removeClientValue("HW_ha_isNewUser")),[2]}}))}))},e}()),ja=/"/g,Ba=function(e,t,n,r){var i=String(lt(e)),o="<"+t;return""!==n&&(o+=" "+n+'="'+String(r).replace(ja,"&quot;")+'"'),o+">"+i+"</"+t+">"};!function(e,t){var n={};n[e]=t(Ba),B(B.P+B.F*T((function(){var t=""[e]('"');return t!==t.toLowerCase()||t.split('"').length>3})),"String",n)}("anchor",(function(e){return function(t){return e(this,"a","name",t)}}));var Ga=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t};ci("search",1,(function(e,t,n,r){return[function(n){var r=e(this),i=null==n?void 0:n[t];return void 0!==i?i.call(n,r):new RegExp(n)[t](String(r))},function(e){var t=r(n,e,this);if(t.done)return t.value;var i=S(e),o=String(this),a=i.lastIndex;Ga(a,0)||(i.lastIndex=0);var s=Xr(i,o);return Ga(i.lastIndex,a)||(i.lastIndex=a),null===s?-1:s.index}]}));var Wa=function(e,t){if(!y(e)||e._t!==t)throw TypeError("Incompatible receiver, "+t+" required!");return e},Ya=_.f,Ka=Ki.fastKey,qa=w?"_s":"size",za=function(e,t){var n,r=Ka(t);if("F"!==r)return e._i[r];for(n=e._f;n;n=n.n)if(n.k==t)return n},Ja={getConstructor:function(e,t,n,r){var i=e((function(e,o){G(e,i,t,"_i"),e._t=t,e._i=Ft(null),e._f=void 0,e._l=void 0,e[qa]=0,null!=o&&re(o,n,e[r],e)}));return _e(i.prototype,{clear:function(){for(var e=Wa(this,t),n=e._i,r=e._f;r;r=r.n)r.r=!0,r.p&&(r.p=r.p.n=void 0),delete n[r.i];e._f=e._l=void 0,e[qa]=0},delete:function(e){var n=Wa(this,t),r=za(n,e);if(r){var i=r.n,o=r.p;delete n._i[r.i],r.r=!0,o&&(o.n=i),i&&(i.p=o),n._f==r&&(n._f=i),n._l==r&&(n._l=o),n[qa]--}return!!r},forEach:function(e){Wa(this,t);for(var n,r=x(e,arguments.length>1?arguments[1]:void 0,3);n=n?n.n:this._f;)for(r(n.v,n.k,this);n&&n.r;)n=n.p},has:function(e){return!!za(Wa(this,t),e)}}),w&&Ya(i.prototype,"size",{get:function(){return Wa(this,t)[qa]}}),i},def:function(e,t,n){var r,i,o=za(e,t);return o?o.v=n:(e._l=o={i:i=Ka(t,!0),k:t,v:n,p:r=e._l,n:void 0,r:!1},e._f||(e._f=o),r&&(r.n=o),e[qa]++,"F"!==i&&(e._i[i]=o)),e},getEntry:za,setStrong:function(e,t,n){Gi(e,t,(function(e,n){this._t=Wa(e,t),this._k=n,this._l=void 0}),(function(){for(var e=this._k,t=this._l;t&&t.r;)t=t.p;return this._t&&(this._l=t=t?t.n:this._t._f)?xi(0,"keys"==e?t.k:"values"==e?t.v:[t.k,t.v]):(this._t=void 0,xi(1))}),n?"entries":"values",!n,!0),De(t)}};!function(e,t,n,r,i,o){var a=l[e],s=a,c=i?"set":"add",u=s&&s.prototype,f={},p=function(e){var t=u[e];k(u,e,"delete"==e||"has"==e?function(e){return!(o&&!y(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return o&&!y(e)?void 0:t.call(this,0===e?0:e)}:"add"==e?function(e){return t.call(this,0===e?0:e),this}:function(e,n){return t.call(this,0===e?0:e,n),this})};if("function"==typeof s&&(o||u.forEach&&!T((function(){(new s).entries().next()})))){var h=new s,d=h[c](o?{}:-0,1)!=h,g=T((function(){h.has(1)})),v=je((function(e){new s(e)})),m=!o&&T((function(){for(var e=new s,t=5;t--;)e[c](t,t);return!e.has(-0)}));v||((s=t((function(t,n){G(t,s,e);var r=vt(new a,t,s);return null!=n&&re(n,i,r[c],r),r}))).prototype=u,u.constructor=s),(g||m)&&(p("delete"),p("has"),i&&p("get")),(m||d)&&p(c),o&&u.clear&&delete u.clear}else s=r.getConstructor(t,e,i,c),_e(s.prototype,n),Ki.NEED=!0;Le(s,e),f[e]=s,B(B.G+B.W+B.F*(s!=a),f),o||r.setStrong(s,e,i)}("Map",(function(e){return function(){return e(this,arguments.length>0?arguments[0]:void 0)}}),{get:function(e){var t=Ja.getEntry(Wa(this,"Map"),e);return t&&t.v},set:function(e,t){return Ja.def(Wa(this,"Map"),0===e?0:e,t)}},Ja,!0);var Xa,Qa,Za,es=function(){function e(){}return e.parse=function(t){var n,r,i,o,a,s,c,u,l,f,p,h;if("function"==typeof URL)return new URL(t);var d={hash:"",host:"",hostname:"",href:"",origin:"",password:"",pathname:"",port:"",protocol:"",search:"",searchParams:new Map,username:""},g=t.match(/^(http:|https:)\/\/(.+[:][^/]+[@])?([^:/]+)([:]\d+)?([^#?]+)?([?])?([^#]+)?([#].+)?$/);return null==g||(g=g.map((function(e){return Qo(e)?"":e})),d.hash=null!==(n=e.parseHash(g))&&void 0!==n?n:"",d.host=null!==(r=e.parseHost(g))&&void 0!==r?r:"",d.hostname=null!==(i=e.parseHostName(g))&&void 0!==i?i:"",d.href=null!==(o=e.parseHref(g))&&void 0!==o?o:"",d.origin=null!==(a=e.parseOrigin(g))&&void 0!==a?a:"",d.password=null!==(s=e.parsePassword(g))&&void 0!==s?s:"",d.pathname=null!==(c=e.parsePathname(g))&&void 0!==c?c:"",d.port=null!==(u=e.parsePort(g))&&void 0!==u?u:"",d.protocol=null!==(l=e.parseProtocol(g))&&void 0!==l?l:"",d.search=null!==(f=e.parseSearch(g))&&void 0!==f?f:"",d.searchParams=null!==(p=e.parseSearchParams(g))&&void 0!==p?p:new Map,d.username=null!==(h=e.parseUsername(g))&&void 0!==h?h:""),d},e.parseHash=function(e){return e[8]},e.parseHost=function(e){return e[3]+e[4]},e.parseHostName=function(e){return e[3]},e.parseHref=function(e){return e[0]},e.parseOrigin=function(e){return e[1]+"//"+e[3]+e[4]},e.parsePassword=function(e){return Qo(e[2])?"":e[2].replace("@","").split(":")[1]},e.parsePathname=function(e){return e[5]},e.parsePort=function(e){return Qo(e[4])?"":e[4].replace(":","")},e.parseProtocol=function(e){return e[1]},e.parseSearch=function(e){return e[6]+e[7]},e.parseSearchParams=function(e){if(Qo(e[7]))return new Map;var t=e[7].split("&").map((function(e){return e.split("=")})),n=new Map;for(var r in t){var i=t[r];n.set(i[0],decodeURIComponent(i[1]))}return n},e.parseUsername=function(e){return Qo(e[2])?"":e[2].replace("@","").split(":")[0]},e}(),ts=new(function(){function e(){}return e.prototype.getPageIdByUrl=function(e){if(!os.urlClusteringOptions.enabled){var t=e.indexOf("?");return-1===t?e:e.slice(0,t)}var n=e;if(!/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/.test(e))return e;var r=this.getUrlInfos(n),i=os.urlClusteringOptions;if(!0===i.removeProtocol&&(r.protocol=""),!0===i.removeOrigin&&(r.origin="",r.protocol=""),!0===i.removeAnchor&&(r.anchor="",r.anchorParams=""),i.removeAllParams)r.urlParams="",r.anchorParams="";else{var o=this.removeParamValues(i,r);r.urlParams=this.generateParams(o.urlParams),Qo(r.anchorParams)?r.anchorParams="":r.anchorParams=this.generateParams(o.anchorParams)}return n=this.generatePageId(r),n=this.matchUrlPatterns(n,i)},e.prototype.removeParamValues=function(e,t){var n=this.getParams(t.urlParams),r=this.getParams(t.anchorParams),i=[],o=[];if(Object.prototype.hasOwnProperty.call(e.removeParams,"params")){var a=e.removeParams.params;for(var s in a)!0===a[s].removeValuesOnly?"search"===a[s].position?n[s]="":"anchor"===a[s].position?r[s]="":"both"===a[s].position&&(n[s]="",r[s]=""):"search"===a[s].position?i.push(s):"anchor"===a[s].position?o.push(s):"both"===a[s].position&&(i.push(s),o.push(s))}if(da(e.removeParams,"removeValuesOnly")&&!0===e.removeParams.removeValuesOnly){for(var s in n)-1===i.indexOf(s)&&(n[s]="");for(var s in r)-1===o.indexOf(s)&&(r[s]="")}return{urlParams:n,anchorParams:r}},e.prototype.generateParams=function(e){var t=[];for(var n in e)t.push(n+"="+e[n]);return t.join("&")},e.prototype.generatePageId=function(e){var t="";return e.protocol&&(t+=e.protocol+"//"),e.origin&&(t+=e.origin),e.path&&(t+=e.path),e.urlParams&&(t+="?"+e.urlParams),e.anchor&&(t+="#"+e.anchor,e.anchorParams&&(t+="?"+e.anchorParams)),t},e.prototype.matchUrlPatterns=function(e,t){var n=t.urlPatterns,r=t.urlSeparators,i=e;r="["+r+"]";var o=new RegExp(r);for(var a in n){for(var s=n[a],c=s.split(/\{\w+\}/g),u=0,l=0,f=!1;;){var p=i.indexOf(c[u],l);if(-1===p){f=!1;break}if(0===u&&0!==p){f=!1;break}if(l=p+c[u].length,c.length>=u&&l>i.length){f=!0;break}var h=i;c.length>u&&(h=c[u+1]);var d=i.indexOf(h,l);if(!(f=null===i.substr(l,d-l).match(o))){f=!1;break}if(u+1>=c.length){f=!0;break}u++}f&&(i=s)}return i},e.prototype.getUrlInfos=function(e){var t=es.parse(e),n={};n.protocol=t.protocol,n.origin=t.host,n.path=t.pathname,n.urlParams=""===t.search?"":t.search.split("?")[1];var r=""===t.hash?"":t.hash.split("#")[1];return n.anchor=""===r?"":r.split("?")[0],n.anchorParams=""===r?"":r.split("?")[1],n},e.prototype.getParams=function(e){var t={};if(""===e||null==e)return t;var n=e.split("&");for(var r in n){var i={key:n[r].split("=")[0],value:n[r].split("=")[1]};t[i.key]=i.value}return t},e}());!function(e){e.OFF="OFF",e.INFO="INFO",e.WARN="WARN",e.ERROR="ERROR"}(Xa||(Xa={})),function(e){e.CN="CN",e.DE="DE",e.RU="RU",e.SG="SG"}(Qa||(Qa={})),function(e){e.ISFULLLEVEL="is_full_level",e.ISMEMBER="is_member",e.USERLEVEL="user_level"}(Za||(Za={}));var ns=new(function(){function e(){this.tag="TokenCollector",this.key=Ma,this.cache=null}return e.prototype.doCollect=function(){return fn(this,void 0,void 0,(function(){var e;return pn(this,(function(t){switch(t.label){case 0:return this.cache=this.cache||{},[4,ls.hiAgcUtil.getToken(this.tag)];case 1:return(e=t.sent())&&(this.cache[this.key]=e),[2,this.cache]}}))}))},e.prototype.getToken=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.doCollect()];case 1:return e.sent(),[2,this.cache[Ma]||""]}}))}))},e}()),rs=new function(){this.debugMode=!1,this.terminalName="",this.logDisabled=!1,this.logLevel=Xa.INFO,this.routePolicy=null},is="Cannot find valid $1 from agconnect-service.json.",os=new(function(){function e(){this.tag="GlobalSetting",this.clientConfig={},this.serverConfig={},this.externalConfig={},this.userProfiles={},this.prevScreenName="",this.currentScreenName="",this.reportType=-1,this.pageEntryTime=0,this.logLevel=Xa.INFO,this.urlClusteringOptions={},this.campaignPushInfo=null,this.isBackground=!1,this.eventUploadPolicy={}}return e.prototype.init=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return this.updateAnalyticsUrl(),[2]}))}))},e.prototype.initDebugMode=function(e,t){os.addConfig(_n,e),e&&t?((t=t.trim()).length>30&&(ls.hiLog.warn(ka,Sa.code,Sa.message,{$1:"terminalName",$2:30}),t=t.substring(0,30)),os.addConfig(Rn,t)):os.addConfig(Rn,"")},e.prototype.initLogLevel=function(e,t){e?os.logLevel=Xa.OFF:ga(Object.keys(Xa),t.toUpperCase())?os.logLevel=t.toUpperCase():os.logLevel=Xa.INFO},e.prototype.initDisableEvents=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r;return pn(this,(function(i){switch(i.label){case 0:return[4,ls.hiClient.isOnline()];case 1:return i.sent()?(e="",[4,os.getHaSdkToken()]):[2];case 2:return(t=i.sent())?(n=ls.hiEncryptSendData.generateDisableEventsHttpHeader({sdkToken:t}),(r={}).method="get",r.url=os.getConfig("analyticsUrl")+"/analytics/api/events/online",ls.hiEncryptSendData.getOnlineAndDisableEvents(e,n,r).then((function(e){e.disable_evts&&va.saveClientValue("HW_ha_disableEvents",e.disable_evts)})),[2]):[2]}}))}))},e.prototype.initServerOaidFlag=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r;return pn(this,(function(i){switch(i.label){case 0:return os.getRoutePolicy()!==Qa.DE?[2,!0]:(e=va.getClientValue("HW_ha_getConfigTime"),!Qo(e)&&Date.now()-Number(e)<=864e5?(t=va.getClientValue("HW_ha_serverOaidFlag")||"false",[2,Promise.resolve("true"===t)]):(n="",[4,os.getHaSdkToken()]));case 1:return(r=i.sent())?[2,new Promise((function(e,t){var i=ls.hiEncryptSendData.generateOaidFlagHttpHeader({sdkToken:r}),o={method:"get"};o.url=os.getConfig("analyticsUrl")+"/analytics/api/config?config_key=oaid_flag",ls.hiEncryptSendData.getServerOaidFlag(n,i,o).then((function(t){var n;va.saveClientValue("HW_ha_getConfigTime",String(Date.now()));var r=null!==(n=t.configList)&&void 0!==n?n:[],i=r[0]&&"true"===r[0].config_value;va.saveClientValue("HW_ha_serverOaidFlag",String(i),864e5),e(i)})).catch((function(){va.saveClientValue("HW_ha_serverOaidFlag",String(!1),864e5),e(!1)}))}))]:[2,!1]}}))}))},e.prototype.initPrevScreen=function(){for(var e,t=ls.sendTask.eventModels.length-1;t>=0;t--){var n=ls.sendTask.eventModels[t];if(n.event===Gr.EXIT_SCREEN){e=n;break}}e&&this.setCurrentScreen(e.properties[er])},e.prototype.getAAID=function(){var e=this.getConfig(Ua);return e||(ls.hiLog.error(ka,_a.code,_a.message),"")},e.prototype.getConfigs=function(){return Object.assign({},this.serverConfig,this.clientConfig,this.externalConfig)},e.prototype.getConfig=function(e){var t,n;return null!==(n=null!==(t=this.externalConfig[e])&&void 0!==t?t:this.clientConfig[e])&&void 0!==n?n:this.serverConfig[e]},e.prototype.addConfig=function(e,t){this.externalConfig[e]=t},e.prototype.removeConfig=function(e){delete this.externalConfig[e]},e.prototype.resolveTerminalName=function(){var e;return fn(this,void 0,void 0,(function(){var t,n;return pn(this,(function(r){switch(r.label){case 0:return t="",this.getConfig(_n)?0!==(t=null!==(e=this.getConfig(Rn))&&void 0!==e?e:"").length?[3,2]:[4,Fa.getAaid()]:[3,2];case 1:(n=r.sent())&&(t=n.substring(0,8)),r.label=2;case 2:return[2,t]}}))}))},e.prototype.setUserProfile=function(e,t){if(Ra.checkString(ka,"user name",e,pr)&&Ra.checkPattern(ka,"user name",e,Sr))if(null!==t){if(Ra.checkString(ka,"user value",t,pr))if(da(this.userProfiles,e))this.userProfiles[e]=t;else{var n=Object.keys(this.userProfiles).length+1;Ra.checkLength(ka,"User Profile",n,Er)&&(this.userProfiles[e]=t)}}else delete this.userProfiles[e]},e.prototype.getUserProfiles=function(e){return fn(this,void 0,void 0,(function(){var t,n,r,i;return pn(this,(function(o){switch(o.label){case 0:return t={},e?(r=(n=Object).assign,i=[t],[4,ls.hiClientProperty.generateUserCommonProperty()]):[3,2];case 1:return t=r.apply(n,i.concat([o.sent()])),[3,3];case 2:t=Object.assign(t,this.userProfiles),o.label=3;case 3:return[2,t]}}))}))},e.prototype.pageStart=function(e){if(Ra.checkString(ka,"screenName",e,pr)){this.pageEntryTime=(new Date).getTime();var t={};t[tr]=-1===e.indexOf("?")?"":e.slice(e.indexOf("?")+1),t[zn]=ts.getPageIdByUrl(this.currentScreenName),t[Xn]=ts.getPageIdByUrl(e),t[Jn]=t[zn],t[Qn]=t[Xn],e!==this.currentScreenName&&(this.prevScreenName=this.currentScreenName,this.currentScreenName=e);fs.onEvent(Gr.ENTER_SCREEN,t,{immediately:!1,isReserved:!0})}},e.prototype.setPrevScreenName=function(e){this.prevScreenName=e},e.prototype.setCurrentScreen=function(e){this.currentScreenName=e},e.prototype.setPageEntryTime=function(e){this.pageEntryTime=e},e.prototype.getPageEntryTime=function(){return this.pageEntryTime},e.prototype.pageEnd=function(e){if(Ra.checkString(ka,"screenName",e,pr)&&this.getPageEntryTime()){var t=(new Date).getTime()-this.getPageEntryTime(),n={};n[nr]=t,n[Xn]=ts.getPageIdByUrl(e),n[Qn]=n[Xn];fs.onEvent(Gr.EXIT_SCREEN,n,{immediately:!1,isReserved:!0})}},e.prototype.setReportType=function(e){this.reportType=e},e.prototype.getReportType=function(){return this.reportType},e.prototype.generateClientKey=function(e){return("HW_"+e+"_"+this.getConfig("appId")).replace(/\./g,"_")},e.prototype.checkRoutePolicy=function(e){var t=ls.hiAgcUtil.getConfig().service.analytics;if(!os.getConfig(Nn))return ls.hiLog.warn(ka,"","Invalid routePolicy!"),!1;if(!ea(t.collector_url,(function(t){null==t?!e&&ls.hiLog.warn(ka,ya.code,"Cannot find valid service/analytics/collector_url from agconnect-service.json."):!e&&ls.hiLog.warn(ka,ya.code,"Cannot find valid service/analytics/collector_url[$1] from agconnect-service.json.",{$1:t})})))return!1;if(os.getConfig("routePolicyUsed")){var n=Object.keys(Qa),r=function(r){var i="collector_url_"+n[r].toLowerCase();if(!ea(t[i],(function(n){null==n?e&&null!=t[i]||ls.hiLog.warn(ka,ya.code,"Cannot find valid service/analytics/$1 from agconnect-service.json.",{$1:i}):e&&null!=t[i]||ls.hiLog.warn(ka,ya.code,"Cannot find valid service/analytics/$1[$2] from agconnect-service.json.",{$1:i,$2:n})})))return{value:!1}};for(var i in n){var o=r(i);if("object"===hn(o))return o.value}}return!0},e.prototype.getHaSdkToken=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,ns.getToken()]}))}))},e.prototype.setRoutePolicy=function(e,t){return""===(e=(e=null!=e?e:"").toUpperCase())?os.addConfig(Nn,"UNKNOWN"):da(Qa,e)?os.addConfig(Nn,e):os.addConfig(Nn,null),os.addConfig("routePolicyUsed",""!==e),!!this.checkRoutePolicy(t)},e.prototype.getRoutePolicy=function(){var e,t=this.getConfig(Nn);return"UNKNOWN"===t?null!==(e=ls.hiAgcUtil.getConfig().region)&&void 0!==e?e:"":t},e.prototype.updateAnalyticsUrl=function(){var e;return fn(this,void 0,void 0,(function(){var t,n,r;return pn(this,(function(i){switch(i.label){case 0:return t=this,[4,Ha.syncCollect("serverConfig")];case 1:return t.serverConfig=i.sent(),n=this.serverConfig.analyticsUrl,null==(r=os.getConfig(Nn))?n="":"UNKNOWN"!==r&&(n=null!==(e=this.serverConfig.collectorUrls[r.toUpperCase()])&&void 0!==e?e:""),os.addConfig("analyticsUrl",n),ls.hiSendData.setUrl(n),[2]}}))}))},e.prototype.checkAgcConfig=function(){var e,t,n,r,i,o,a=ls.hiAgcUtil.getConfig();Zo(null===(t=null===(e=null==a?void 0:a.service)||void 0===e?void 0:e.analytics)||void 0===t?void 0:t.resource_id)||ls.hiLog.warn(ka,"",is,{$1:"service/analytics/resource_id"}),Zo(null===(n=null==a?void 0:a.client)||void 0===n?void 0:n.product_id)||ls.hiLog.warn(ka,"",is,{$1:"client/product_id"}),Zo(null===(r=null==a?void 0:a.client)||void 0===r?void 0:r.app_id)||ls.hiLog.warn(ka,"",is,{$1:"client/app_id"}),Zo(null===(i=null==a?void 0:a.client)||void 0===i?void 0:i.client_id)||ls.hiLog.warn(ka,"",is,{$1:"client/client_id"});var s=null===(o=null==a?void 0:a.client)||void 0===o?void 0:o.package_name;null==s||Zo(s)||ls.hiLog.warn(ka,"",is,{$1:"client/package_name"});var c=null==a?void 0:a.region;return null==c||Zo(c)&&Qa[c]||ls.hiLog.warn(ka,"",is,{$1:"region"}),os.setRoutePolicy(rs.routePolicy,!1)},e.prototype.getNewUserFlag=function(){if("false"===va.getClientValue("HW_ha_isNewUser"))return 0;var e=Number(va.getClientValue("HW_ha_firstOpenTime")),t=new Date(e).toDateString()===(new Date).toDateString();return t||va.saveClientValue("HW_ha_isNewUser","false"),t?1:0},e.prototype.setIsTestDevice=function(){var e=os.getConfig(_n),t="true"===va.getClientValue("HW_ha_isTestDevice")||e?"true":"false";"true"===t&&va.saveClientValue("HW_ha_isTestDevice",t)},e.prototype.isTestDevice=function(){return"true"===va.getClientValue("HW_ha_isTestDevice")?1:0},e}()),as={ON_SCHEDULED_TIME_POLICY:5,ON_CACHE_THRESHOLD_POLICY:30},ss=function(){function e(){this.reportPolicyDefault=as}return e.prototype.eventUploadPolicyLaunch=function(){ls.sendTask.uploadData(!0,Gr.LaunchApp)},e.prototype.eventUploadPolicyThreshold=function(){var e=this.getEventUploadPolicy();if(e.ON_CACHE_THRESHOLD_POLICY){if(e.ON_CACHE_THRESHOLD_POLICY<Dr)return;if(e.ON_CACHE_THRESHOLD_POLICY>kr)return;ls.sendTask.eventModels.filter((function(e){return e.event!==Gr.STOP_ANALYTICS_COLLECTION&&e.sendState===Ir||e.sendState===_r})).length>=e.ON_CACHE_THRESHOLD_POLICY&&ls.sendTask.uploadData(!1,"")}},e.prototype.eventUploadPolicyScheduleTime=function(){var e,t=this.getEventUploadPolicy();if(os.getConfig(_n))e=1e3;else{if(!t.ON_SCHEDULED_TIME_POLICY)return;if(t.ON_SCHEDULED_TIME_POLICY<Lr)return;if(t.ON_SCHEDULED_TIME_POLICY>$r)return;e=1e3*t.ON_SCHEDULED_TIME_POLICY}ls.sendTask.setHeartbeatFunc(e)},e.prototype.eventUploadPolicyBackground=function(){},e.prototype.logUploadPolicyLaunch=function(){var e=this.getEventUploadPolicy();ls.hiAutoReport.isLaunchUpload()&&e.ON_APP_LAUNCH_POLICY&&e.ON_APP_LAUNCH_POLICY>0&&ls.sendLogTask.uploadData()},e.prototype.logUploadPolicyThreshold=function(){var e=this.getEventUploadPolicy();if(e.ON_CACHE_THRESHOLD_POLICY){if(e.ON_CACHE_THRESHOLD_POLICY<Dr)return;if(e.ON_CACHE_THRESHOLD_POLICY>kr)return;ls.sendLogTask.logRecords.filter((function(e){return e.sendState===Ir||e.sendState===_r})).length>=e.ON_CACHE_THRESHOLD_POLICY&&ls.sendLogTask.uploadData()}},e.prototype.logUploadPolicyScheduleTime=function(){var e,t=this.getEventUploadPolicy();if(os.getConfig(_n))e=1e3;else{if(!t.ON_SCHEDULED_TIME_POLICY)return;if(t.ON_SCHEDULED_TIME_POLICY<Lr)return;if(t.ON_SCHEDULED_TIME_POLICY>$r)return;e=1e3*t.ON_SCHEDULED_TIME_POLICY}ls.sendLogTask.setHeartbeatFunc(e)},e.prototype.logUploadPolicyBackground=function(){},e.prototype.getUploadMaxCount=function(){var e=this.getEventUploadPolicy();return-1===e.ON_CACHE_THRESHOLD_POLICY?300:Math.min(300,e.ON_CACHE_THRESHOLD_POLICY)},e.prototype.getEventUploadPolicy=function(){var e=va.getClientValue("HW_ha_eventUploadPolicy");return Qo(e)&&(e=this.reportPolicyDefault,va.saveClientValue("HW_ha_eventUploadPolicy",e)),e},e}();new ss;var cs=function(){function e(){}return e.prototype.queryAllModels=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,this.generateEventModels()]}))}))},e.prototype.queryModels=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){return[2,this.generateEventModels(void 0,e)]}))}))},e.prototype.saveModel=function(e,t){return fn(this,void 0,void 0,(function(){var n,r;return pn(this,(function(i){switch(i.label){case 0:return n=ha(t,["sendState","collectionUrl","routePolicy"]),r=JSON.stringify(n),[4,ls.hiDBStorage.saveClientValue(e,r,!0)];case 1:return i.sent(),[2]}}))}))},e.prototype.removeModel=function(e){ls.hiDBStorage.removeItem(e,!0)},e.prototype.removeModels=function(e){ls.hiDBStorage.removeItems(e,!0)},e.prototype.removeAllModels=function(){ls.hiDBStorage.removeAllItems(!0)},e.prototype.generateEventModels=function(e,t){return fn(this,void 0,void 0,(function(){var e,t,n,r,i,o;return pn(this,(function(a){switch(a.label){case 0:return[4,ls.hiDBStorage.getAllClientValue(!0)];case 1:for(e=a.sent(),t=[],n=0;n<e.length;n++)r=e[n],i=r,null!=(o=JSON.parse(i)).id&&t.push(o);return[2,t]}}))}))},e}(),us=function(){function e(){}return e.prototype.getDatabase=function(){},e.prototype.getAllClientValue=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,Promise.resolve([""])]}))}))},e.prototype.getClientValues=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,Promise.resolve([""])]}))}))},e.prototype.getClientValue=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,Promise.resolve("")]}))}))},e.prototype.isKeyExist=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,Promise.resolve(!1)]}))}))},e.prototype.saveClientValue=function(e,t,n){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.removeItem=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.removeItems=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.removeAllItems=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e}(),ls=new(function(){function e(){this.hiAnalytics={},this.hiLog={},this.hiAutoReport={},this.hiEncryptSendData={},this.hiSendData={},this.hiClient={},this.hiClientProperty={},this.hiStorage={},this.hiDBStorage={},this.hiFileStorage={},this.hiMigration={},this.sendLogTask={},this.sendTask={},this.sendPolicy={},this.hiEncrypt={},this.hiKeyStore={},this.hiAgcUtil={},this.hiEventModelsAdapter={},this.setSendPolicy(new ss),this.setHiEventModelsAdapter(new cs),this.setHiDBStorage(new us)}return e.prototype.setHiAnalytics=function(e){this.hiAnalytics=e},e.prototype.setHiLog=function(e){this.hiLog=e},e.prototype.setHiAutoReport=function(e){this.hiAutoReport=e},e.prototype.setHiEncryptSendData=function(e){this.hiEncryptSendData=e,this.setHiSendData(e)},e.prototype.setHiSendData=function(e){this.hiSendData=e},e.prototype.setHiClient=function(e){this.hiClient=e},e.prototype.setHiClientProperty=function(e){this.hiClientProperty=e},e.prototype.setHiStorage=function(e){this.hiStorage=e},e.prototype.setHiDBStorage=function(e){this.hiDBStorage=e},e.prototype.setHiFileStorage=function(e){this.hiFileStorage=e},e.prototype.setHiMigration=function(e){this.hiMigration=e},e.prototype.setSendLogTask=function(e){this.sendLogTask=e},e.prototype.setSendTask=function(e){this.sendTask=e},e.prototype.setSendPolicy=function(e){this.sendPolicy=e},e.prototype.setHiEncrypt=function(e){this.hiEncrypt=e},e.prototype.setHiKeyStore=function(e){this.hiKeyStore=e},e.prototype.setHiAgcUtil=function(e){this.hiAgcUtil=e},e.prototype.setHiEventModelsAdapter=function(e){this.hiEventModelsAdapter=e},e}()),fs=new(function(){function e(){this.eventsReceived=[],this.initialized=!1,this.reportFlag=!0}return e.prototype.init=function(){var e=va.getClientValue(Mr);null==e||0===e.length?va.saveClientValue(Mr,String(this.reportFlag)):this.reportFlag=e===String(!0)},e.prototype.setAnalyticsEnabled=function(e){if(this.reportFlag!==e&&(this.reportFlag=e,va.saveClientValue(Mr,String(e)),!e)){var t=va.getClientValue(xr);"true"!==t&&"false"!==t||va.saveClientValue(xr,String(!0));var n=va.getClientValue(Vr);"true"!==n&&"false"!==n||va.saveClientValue(Vr,String(!0)),ls.hiEventModelsAdapter.removeAllModels(),this.onEvent(Gr.STOP_ANALYTICS_COLLECTION,{},{isReserved:!0,forceReport:!0,immediately:!0})}},e.prototype.setRestrictionEnabled=function(e){if(this.reportFlag&&(va.saveClientValue(xr,String(e)),e)){this.onEvent(Gr.STOP_ANALYTICS_COLLECTION,{},{isReserved:!0,forceReport:!0,immediately:!0});var t=va.getClientValue(Vr);"true"!==t&&"false"!==t||va.saveClientValue(Vr,String(e))}},e.prototype.isRestrictionEnabled=function(){var e=va.getClientValue(Mr),t=va.getClientValue(xr);return""!==t?"true"===t:"true"!==e},e.prototype.setRestrictionShared=function(e){this.reportFlag?os.getRoutePolicy()===Qa.CN?this.isRestrictionEnabled()?ls.hiLog.warn(ka,"","Cannot set restriction_shared because restriction_enabled is true!"):va.saveClientValue(Vr,String(e)):ls.hiLog.warn(ka,"","Cannot set restriction_shared because routePolicy is not CN!"):ls.hiLog.warn(ka,"","Cannot set restriction_shared because analytics_enabled is false!")},e.prototype.isRestrictionShared=function(){return os.getRoutePolicy()!==Qa.CN||(!this.reportFlag||(os.getRoutePolicy()!==Qa.CN||(!!this.isRestrictionEnabled()||"true"===va.getClientValue(Vr))))},e.prototype.checkEventLength=function(e){return!!Ra.checkString(ka,"eventId",e,pr)},e.prototype.checkIsPredineEvents=function(e){var t=!1,n=Br;for(var r in n){if(e===n[r]){t=!0;break}}return t},e.prototype.getEventParams=function(e,t){void 0===t&&(t={});var n={};for(var r in this.putEventParams(n,t,e,!1),this.putEventParams(n,va.defaultEventParams,e,!0),n)null==n[r]&&delete n[r];var i=Object.keys(n);if(i.length>dr){ls.hiLog.warn(ka,Ca.code,Ca.message,{$1:"Event",$2:dr});for(var o=i.slice(dr),a=0;a<o.length;a++){delete n[r=o[a]]}}return n},e.prototype.putEventParams=function(e,t,n,r){if(r&&n)return e;for(var i in t)r&&da(e,i)||Ra.checkString(ka,"params key",i,pr)&&(n||Ra.checkPattern(ka,"params key",i,Tr))&&(e[i]=t[i]);return e},e.prototype.checkEventIsValid=function(e,t){return!!this.checkEventLength(e)&&(!(!t&&!Ra.checkReserve(ka,"eventId",e,Gr))&&(!!(this.checkIsPredineEvents(e)||t||Ra.checkPattern(ka,"eventId",e,yr))&&(!this.isDisableEvent(e)||(ls.hiLog.info("The event is disabled: "+e),!1))))},e.prototype.processEventsReceived=function(){var e=this;0!==this.eventsReceived.length&&(this.reportFlag?this.eventsReceived.forEach((function(t){var n=t.eventId,r=t.param,i=t.config;i.isReserved=Wr.some((function(e){return e===n})),e.onEvent(n,r,i)})):ls.hiLog.warn(ka,Pa.code,Pa.message),this.eventsReceived.length=0)},e.prototype.onEventCallback=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n={}),this.initialized?this.reportFlag?this.onEvent(e,t,n):ls.hiLog.warn(ka,Pa.code,Pa.message):(this.eventsReceived.length>=300&&(this.eventsReceived.length=0),this.eventsReceived.push({eventId:e,param:t,config:n}))},e.prototype.onEvent=function(e,t,n){void 0===t&&(t={}),void 0===n&&(n={});var r=n.isReserved||!1,i=n.forceReport||!1;if(this.checkEventIsValid(e,r)&&(i||Ra.checkSwitchIsOpen(ka,this.reportFlag))){var o=this.getEventParams(r,t),a=vr/1024+"KB",s=JSON.stringify(o).length;if(Ra.checkExceedMaxSize(ka,a,s,vr)){o.$TaskId="";var c={};c[Zn]=ts.getPageIdByUrl(os.prevScreenName),c[er]=ts.getPageIdByUrl(os.currentScreenName),this.putEventParams(o,c,r,!1);var u=n.eventtime||(new Date).getTime();ls.sendTask.onEvent(e,o,"oper",u,n)}}},e.prototype.addDefaultEventParams=function(e){if(null!=e)if("object"===hn(e)){var t={};for(var n in e){null==(r=e[n])?delete va.defaultEventParams[n]:t[n]=r}for(var n in t){var r=t[n];if(this.validateDefaultParam(n,r)){if(!da(va.defaultEventParams,n)){var i=Object.keys(va.defaultEventParams).length+1;if(!Ra.checkLength(ka,"default event",i,gr))continue}va.defaultEventParams[n]=r}}}else ls.hiLog.warn(ka,Aa.code,Aa.message,{$1:"default event params"});else va.defaultEventParams={}},e.prototype.validateDefaultParam=function(e,t){return!!Ra.checkString(ka,"default event params key",e,pr)&&(!!Ra.checkPattern(ka,"default event params key",e,Tr)&&(!("string"==typeof t&&t.length>pr)||(ls.hiLog.warn(ka,Sa.code,Sa.message,{$1:"default event params value",$2:pr}),!1)))},e.prototype.isDisableEvent=function(e){var t=va.getClientValue("HW_ha_disableEvents");return"object"===hn(t)&&t instanceof Array&&-1!==t.indexOf(e)},e.prototype.writeLog=function(e,t){return fn(this,void 0,void 0,(function(){var n,r,i,o,a,s,c,u,l,f;return pn(this,(function(p){return null==e||Qo(t)?(ls.hiLog.warn(ka,Aa.code,Aa.message,{$1:null==e?"logConfig":"content"}),[2]):"string"!=typeof t?(ls.hiLog.warn(ka,Aa.code,Aa.message,{$1:"content"}),[2]):(n=e.region,r=e.projectId,i=e.groupId,o=e.streamId,a=e.tags,Ra.checkString(ka,"region",n,fr)&&Ra.checkString(ka,"projectId",r,fr)&&Ra.checkString(ka,"groupId",i,fr)&&Ra.checkString(ka,"streamId",o,fr)?(null!=a&&"object"===hn(a)||(ls.hiLog.warn(ka,Aa.code,Aa.message,{$1:"tags"}),a={}),s={},(c=Object.entries(a||{}).filter((function(e){var t=e[0],n=e[1];return!!Ra.checkString(ka,"tag key",t,ur)&&(!!Ra.checkPattern(ka,"tag key",t,Sr)&&!!Ra.checkString(ka,"tag value",n,pr))}))).length>mr&&ls.hiLog.warn(ka,Ca.code,Ca.message,{$1:"tags",$2:mr}),c.slice(0,mr).forEach((function(e){var t=e[0],n=e[1];s[t]=n})),t.length>hr&&(ls.hiLog.warn(ka,Sa.code,Sa.message,{$1:"content",$2:hr}),t=t.substring(0,hr)),u=os.getConfig(Nn),l=ls.hiAgcUtil.getConfig().region||"",f={region:n,projectId:r,groupId:i,streamId:o,tags:s,id:ta.create(),content:t,type:"maint",eventtime:(new Date).getTime(),collectionUrl:os.getConfig("analyticsUrl"),routePolicy:"UNKNOWN"===u?l:u,sendState:br},ls.sendLogTask.writeLog(f),[2]):[2])}))}))},e.prototype.onReport=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return ls.sendTask.uploadData(!1,""),ls.sendLogTask.uploadData(),[2]}))}))},e}()),ps=new(function(){function e(){this.userId="",this.clientId=""}return e.prototype.init=function(){},e.prototype.setUserId=function(e){var t=this.isValidUserId(e);t&&va.saveClientValue("HW_ha_existUserId",null!=e?Rr:Nr),(e=t&&null!=e?String(e):"")!==this.userId&&va.handleSessionValue(),this.userId=e||""},e.prototype.isValidUserId=function(e){return null==e||("string"!=typeof e&&"number"!=typeof e?(ls.hiLog.warn(ka,"",Aa.message,{$1:"userId"}),!1):!!Ra.checkString(ka,"userId",e,pr))},e.prototype.setSessionDuration=function(e){return e<wr.value&&(ls.hiLog.warn(ka,ba.code,"session duration less than minimum $1.",{$1:wr.label}),e=wr.value),e>Cr.value&&(ls.hiLog.warn(ka,ba.code,"session duration greater than maximum $1.",{$1:Cr.label}),e=Cr.value),va.setSessionDuration(e)},e.prototype.setBackgroundSessionDuration=function(e){return e<wr.value&&(ls.hiLog.warn(ka,ba.code,"min session duration less than minimum $1.",{$1:wr.label}),e=wr.value),e>Cr.value&&(ls.hiLog.warn(ka,ba.code,"min session duration greater than maximum $1.",{$1:Cr.label}),e=Cr.value),va.setBackgroundSessionDuration(e)},e.prototype.getUserInfo=function(e){var t=(void 0===e?{}:e).routePolicy,n=void 0===t?"":t;return fn(this,void 0,void 0,(function(){var e,t,r,i,o,a,s,c,u,l;return pn(this,(function(f){switch(f.label){case 0:return e=os.getAAID(),[4,ls.hiClient.getSystemOaidFlag()];case 1:return t=f.sent(),(i=t)?[4,ls.hiClient.getClientOaidFlag()]:[3,3];case 2:i=f.sent(),f.label=3;case 3:return r=i,(o=t)?[4,ls.hiClient.getServerOaidFlag(n)]:[3,5];case 4:o=f.sent(),f.label=5;case 5:return a={systemOaidFlag:t,clientOaidFlag:r,serverOaidFlag:o},c={aaid:e,userid:this.userId},[4,ls.hiClientProperty.generateUserCommonProperty(a)];case 6:return c.properties=f.sent(),c.events_global_properties=os.userProfiles,s=c,[3,11];case 7:return(void 0).oaid=f.sent(),u=s,[4,ls.hiClient.getOaidSource()];case 8:return u.oaid_source=f.sent(),[3,10];case 9:s.oaid="",s.oaid_source="",f.label=10;case 10:case 11:return[3,15];case 12:return(void 0).oaid=f.sent(),l=s,[4,ls.hiClient.getOaidSource()];case 13:return l.oaid_source=f.sent(),[3,15];case 14:s.oaid="",s.oaid_source="",f.label=15;case 15:return[2,s]}}))}))},e}()),hs=new(function(){function e(){this.autoReport={},this.hiStorage={},this.hiClient={}}return e.prototype.init=function(){this.autoReport=ls.hiAutoReport,this.hiStorage=ls.hiStorage,this.hiClient=ls.hiClient},e.prototype.reportFirstOpenEvent=function(e){void 0===e&&(e=!1);var t=(new Date).getTime();va.saveClientValue("HW_ha_firstOpenTime",String(t));var n={eventtime:t,immediately:!1,isReserved:!0},r={},i=e?"clearCachedData":ls.hiClient.getStartType();null!=i&&(r[Kn]=i);var o=ls.hiClient.getStartScene();null!=o&&(r[qn]=o),fs.onEvent(Gr.FIRST_OPEN,r,n)},e.prototype.reportLaunchAppEvent=function(e){void 0===e&&(e=!1);var t={},n=e?"clearCachedData":ls.hiClient.getStartType();null!=n&&(t[Kn]=n);var r=ls.hiClient.getStartScene();null!=r&&(t[qn]=r),fs.onEvent(Gr.LaunchApp,t,{immediately:!1,isReserved:!0})},e.prototype.reportPageEntryEvent=function(){this.reportPageEntryEventAsync()},e.prototype.reportPageEntryEventAsync=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r,i;return pn(this,(function(o){switch(o.label){case 0:return[4,ls.hiAnalytics.initPromise];case 1:return o.sent(),e={},t=(new Date).getTime(),os.setPageEntryTime(t),n=os.currentScreenName,r=ls.hiClient.getHref(),os.setPrevScreenName(os.currentScreenName),os.setCurrentScreen(r),e[tr]=-1===r.indexOf("?")?"":r.slice(r.indexOf("?")+1),e[zn]=n,e[Xn]=ts.getPageIdByUrl(r),e[Jn]=e[zn],e[Qn]=e[Xn],i={immediately:!1,isReserved:!0},fs.onEvent(Gr.ENTER_SCREEN,e,i),[2]}}))}))},e.prototype.reportPageExitEvent=function(){var e=os.getPageEntryTime();if(e){var t={},n=(new Date).getTime()-e;t[Xn]=ts.getPageIdByUrl(os.currentScreenName),t[Qn]=t[Xn],t[nr]=n;fs.onEvent(Gr.EXIT_SCREEN,t,{immediately:!0,isReserved:!0})}},e.prototype.reportCampaignPushInfoEvent=function(){var e=os.campaignPushInfo;if(e){var t={};t.$CampaignPushInfo=JSON.stringify(e),t.$JobId=e.jobId,t.$ActivityId=e.activityId;fs.onEvent(Gr.CampaignPushClick,t,{immediately:!1,isReserved:!0})}},e.prototype.computeLoadTime=function(){},e.prototype.stateChanged=function(){},e}()),ds=new(function(){function e(){this.eventListeners=[]}return e.prototype.addListener=function(e){this.eventListeners.push(e)},e.prototype.init=function(){return fn(this,void 0,void 0,(function(){var e,t;return pn(this,(function(n){switch(n.label){case 0:e=0,t=this.eventListeners,n.label=1;case 1:return e<t.length?[4,t[e].init()]:[3,4];case 2:n.sent(),n.label=3;case 3:return e++,[3,1];case 4:return[2]}}))}))},e}()),gs=new(function(){function e(){}return e.prototype.init=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(n){return e&&e.forEach((function(e){Ha.register(e)})),t&&t.forEach((function(e){ds.addListener(e)})),ds.init(),[2]}))}))},e}()),vs=new(function(){function e(){this.key="serverConfig",this.cache=null}return e.prototype.doCollect=function(){return fn(this,void 0,void 0,(function(){var e,t,n;return pn(this,(function(r){if(!this.cache){for(n in e=ls.hiAgcUtil.getConfig(),t={},Qa)t[n]=this.getUrl(e.service.analytics["collector_url_"+n.toLowerCase()]);this.cache={productId:e.client.product_id,appId:e.client.app_id,clientId:e.client.client_id,packageName:e.client.package_name||e.client.app_id,resourceId:e.service.analytics.resource_id,collectorUrls:t,analyticsUrl:this.getUrl(e.service.analytics.collector_url)}}return[2,this.cache]}))}))},e.prototype.getUrl=function(e){if(null!=e&&"string"!=typeof e)return"";var t=(null!=e?e:"").split(",")[0];return/^https?:\/\//.test(t)||(t="https://"+t),""!==t&&ls.sendTask.isServerWebV3()&&(t+="/webv3"),t},e}()),ms=new(function(){function e(){}return e.prototype.init=function(){hs.init(),fs.init()},e.prototype.onEvent=function(){},e}()),Es=new function(){this.enabled=!1,this.removeProtocol=!0,this.removeOrigin=!1,this.removeAllParams=!0,this.removeParams={removeValuesOnly:!0,params:{}},this.removeAnchor=!0,this.urlPatterns=[],this.urlSeparators=":/.?=&#"},ys=function(){function e(e,t){var n=this;ls.setHiAnalytics(this),this.preConfigPromise=this.preConfig(e,t),this.preStoragePromise=va.init(),this.initPromise=this.initSDK(e),this.initPromise.then((function(){ls.hiLog.info("init success"),n.initDone()})).catch((function(e){ls.hiLog.warn(ka,Oa.code,Oa.message)}))}return e.prototype.preConfig=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,ls.hiAgcUtil.init()];case 1:return e.sent(),os.initDebugMode(rs.debugMode,rs.terminalName),os.initLogLevel(rs.logDisabled,rs.logLevel),os.urlClusteringOptions=JSON.parse(JSON.stringify(Es)),os.addConfig("launchAppTime",(new Date).getTime()),[2,os.checkAgcConfig()]}}))}))},e.prototype.initDone=function(){fs.initialized=!0,fs.processEventsReceived(),ls.sendPolicy.eventUploadPolicyScheduleTime(),ls.sendPolicy.logUploadPolicyScheduleTime(),setTimeout((function(){ls.sendPolicy.eventUploadPolicyLaunch(),ls.sendPolicy.logUploadPolicyLaunch()}),50),setInterval((function(){ls.sendPolicy.eventUploadPolicyThreshold(),ls.sendPolicy.logUploadPolicyThreshold()}),1e3)},e.prototype.initSDK=function(e){return fn(this,void 0,void 0,(function(){var e,t;return pn(this,(function(n){switch(n.label){case 0:return ls.hiAutoReport.initAutoEvents(),[4,this.preConfigPromise];case 1:return n.sent()?[4,this.preStoragePromise]:[2,Promise.reject()];case 2:return n.sent(),os.campaignPushInfo=ls.hiClient.getCampaignPushInfo(),[4,gs.init([vs,Fa],[ms])];case 3:return n.sent(),[4,os.init()];case 4:return n.sent(),ps.init(),[4,ls.sendTask.init()];case 5:return n.sent(),[4,ls.sendLogTask.init()];case 6:return n.sent(),os.initPrevScreen(),ls.hiAutoReport.initEventTriggers(),[4,Ha.collect(Ua)];case 7:return(e=n.sent())[Ua]&&(os.addConfig("aaid",e[Ua]),os.setIsTestDevice()),null==(t=va.getClientValue(Hr))||""===t?(hs.reportFirstOpenEvent(),va.saveClientValue(Hr,"true")):"true"===t&&va.saveClientValue(Hr,"false"),os.getNewUserFlag(),va.getSessionValue()||hs.reportLaunchAppEvent(),hs.reportCampaignPushInfoEvent(),[2]}}))}))},e.prototype.destroySDK=function(){},e.prototype.setAnalyticsEnabled=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.setAnalyticsEnabled(e),[2]}}))}))},e.prototype.setRestrictionEnabled=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.setRestrictionEnabled(e),[2]}}))}))},e.prototype.isRestrictionEnabled=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.initPromise];case 1:return e.sent(),[2,fs.isRestrictionEnabled()]}}))}))},e.prototype.setRestrictionShared=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.setRestrictionShared(e),[2]}}))}))},e.prototype.isRestrictionShared=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.initPromise];case 1:return e.sent(),[2,fs.isRestrictionShared()]}}))}))},e.prototype.setUserId=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.reportFlag?(ps.setUserId(e),[2]):[2]}}))}))},e.prototype.setUserProfile=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(n){switch(n.label){case 0:return[4,this.initPromise];case 1:return n.sent(),fs.reportFlag?(os.setUserProfile(e,t),[2]):[2]}}))}))},e.prototype.getUserProfiles=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.reportFlag?[2,os.getUserProfiles(e)]:[2,{}]}}))}))},e.prototype.pageStart=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.reportFlag?(os.pageStart(e),[2]):[2]}}))}))},e.prototype.pageEnd=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.reportFlag?(os.pageEnd(e),[2]):[2]}}))}))},e.prototype.setSessionDuration=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.preConfigPromise];case 1:return t.sent(),fs.reportFlag?(Number.isInteger(e)&&ps.setSessionDuration(e),[2]):[2]}}))}))},e.prototype.onEvent=function(e,t){return fn(this,void 0,void 0,(function(){var n;return pn(this,(function(r){switch(r.label){case 0:return[4,this.initPromise];case 1:return r.sent(),fs.reportFlag?(n={isReserved:Wr.some((function(t){return t===e}))},fs.onEvent(e,t,n),[2]):(ls.hiLog.warn(ka,Pa.code,Pa.message),[2])}}))}))},e.prototype.setAppVersion=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.preConfigPromise];case 1:return t.sent(),fs.reportFlag?Ra.checkString(ka,"app version",e,lr)?(os.addConfig("appVersion",e),[2]):[2]:[2]}}))}))},e.prototype.getAAID=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.initPromise];case 1:return e.sent(),[2,os.getAAID()]}}))}))},e.prototype.addDefaultEventParams=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.initPromise];case 1:return t.sent(),fs.reportFlag?(fs.addDefaultEventParams(e),[2]):[2]}}))}))},e.prototype.writeLog=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(n){switch(n.label){case 0:return[4,this.initPromise];case 1:return n.sent(),fs.reportFlag?(fs.writeLog(e,t),[2]):(ls.hiLog.warn(ka,Pa.code,Pa.message),[2])}}))}))},e.prototype.setRoutePolicy=function(e){return fn(this,void 0,void 0,(function(){var t,n;return pn(this,(function(r){switch(r.label){case 0:return[4,this.initPromise];case 1:return r.sent(),t=os.getRoutePolicy(),os.setRoutePolicy(e),n=os.getRoutePolicy(),t===n?[2]:(fs.reportFlag||ls.sendTask.clearData(t),[4,os.updateAnalyticsUrl()]);case 2:return r.sent(),[2]}}))}))},e.prototype.setReportPolicies=function(e){return fn(this,void 0,void 0,(function(){var t;return pn(this,(function(n){switch(n.label){case 0:return[4,this.preConfigPromise];case 1:return n.sent(),t={},"number"!=typeof e.ON_SCHEDULED_TIME_POLICY?t.ON_SCHEDULED_TIME_POLICY=as.ON_SCHEDULED_TIME_POLICY:e.ON_SCHEDULED_TIME_POLICY<Lr?t.ON_SCHEDULED_TIME_POLICY=Lr:e.ON_SCHEDULED_TIME_POLICY>$r?t.ON_SCHEDULED_TIME_POLICY=$r:t.ON_SCHEDULED_TIME_POLICY=e.ON_SCHEDULED_TIME_POLICY,"number"!=typeof e.ON_CACHE_THRESHOLD_POLICY?t.ON_CACHE_THRESHOLD_POLICY=as.ON_CACHE_THRESHOLD_POLICY:e.ON_CACHE_THRESHOLD_POLICY<Dr?t.ON_CACHE_THRESHOLD_POLICY=Dr:e.ON_CACHE_THRESHOLD_POLICY>kr?t.ON_CACHE_THRESHOLD_POLICY=kr:t.ON_CACHE_THRESHOLD_POLICY=e.ON_CACHE_THRESHOLD_POLICY,va.saveClientValue("HW_ha_eventUploadPolicy",t),[2]}}))}))},e.prototype.onReport=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.preConfigPromise];case 1:return e.sent(),[2,fs.onReport()]}}))}))},e}(),Ss=function(e,t,n,r){r&&(r.$1&&(n=n.replace("$1",r.$1)),r.$2&&(n=n.replace("$2",r.$2)));var i=[];return e&&i.push(e),t&&i.push(t),n&&i.push(n),Ts+" => "+i.join("|")},Ts="OpennessSDK",ws=new(function(){function e(){}return e.prototype.info=function(e,t){if(ga(this.generateLevels(os.logLevel),Xa.INFO)){var n=Ss(null,null,e,t);this.printInfo(n)}},e.prototype.warn=function(e,t,n,r){if(ga(this.generateLevels(os.logLevel),Xa.WARN)){var i=Ss(e,t,n,r);this.printWarn(i),r&&r.$error&&this.printError(r.$error)}},e.prototype.error=function(e,t,n,r){if(ga(this.generateLevels(os.logLevel),Xa.ERROR)){var i=Ss(e,t,n,r);this.printError(i),r&&r.$error&&this.printError(r.$error)}},e.prototype.generateLevels=function(e){return e===Xa.INFO?[Xa.INFO,Xa.WARN,Xa.ERROR]:e===Xa.WARN?[Xa.WARN,Xa.ERROR]:e===Xa.ERROR?[Xa.ERROR]:[]},e.prototype.printInfo=function(e){console.info(e)},e.prototype.printWarn=function(e){console.warn(e)},e.prototype.printError=function(e){console.error(e)},e}()),Cs=[].slice,As={},bs=function(e,t,n){if(!(t in As)){for(var r=[],i=0;i<t;i++)r[i]="a["+i+"]";As[t]=Function("F,a","return new F("+r.join(",")+")")}return As[t](e,n)},Is=Function.bind||function(e){var t=M(this),n=Cs.call(arguments,1),r=function(){var i=n.concat(Cs.call(arguments));return this instanceof r?bs(t,i.length,i):ae(t,i,e)};return y(t.prototype)&&(r.prototype=t.prototype),r};B(B.P,"Function",{bind:Is});var Ps=new(function(){function e(){}return e.prototype.initAutoEvents=function(){this.attachEventListener(window,"DOMContentLoaded",hs.reportPageEntryEvent.bind(hs)),this.attachEventListener(window,"beforeunload",hs.reportPageExitEvent.bind(hs)),this.attachEventListener(window,"load",hs.computeLoadTime.bind(hs)),this.attachEventListener(document,"visibilitychange",hs.stateChanged.bind(hs))},e.prototype.attachEventListener=function(e,t,n){var r=this;e.attachEvent?e.attachEvent("on"+t,(function(){n.apply(r)})):e.addEventListener(t,(function(){n.apply(r)}),!1)},e.prototype.initEventTriggers=function(){},e.prototype.isLaunchUpload=function(){return!1},e}()),Os=new(function(){function e(){this.url="",this.sendFun={},this.sendFun[An.AJAX]=this.sendByAjax,this.sendFun[An.BEACON]=this.sendByBeacon,this.sendFun[An.IMG]=this.sendByImg}return e.prototype.onEvent=function(e,t,n){(n=n||{}).async=n.async||!0;var r=n&&n.mode;return r&&this.sendFun[r]?this.sendFun[r].call(this,e,t,n):this.smartSend(e,t,n)},e.prototype.smartSend=function(e,t,n){return"undefined"!=typeof fetch&&(null==n?void 0:n.async)?this.sendByBeacon(e,t,n):window.XMLHttpRequest?this.sendByAjax(e,t,n):this.sendByImg(e,t,n)},e.prototype.sendByBeacon=function(e,t,n){if("undefined"!=typeof fetch){var r=(null==n?void 0:n.url)||this.url,i=new Headers(t);return new Promise((function(t,n){fetch(r,{method:"POST",body:JSON.stringify(e),headers:i}).then((function(e){e.ok?t():n(e.statusText)})).catch((function(e){n(e)}))}))}return Promise.reject(new Error("missing fetch method"))},e.prototype.sendByAjax=function(e,t,n){var r=this;return void 0===t&&(t={}),new Promise((function(i,o){var a=(null==n?void 0:n.url)||r.url;if(window.XMLHttpRequest&&a){var s=new XMLHttpRequest;if(s.onabort=function(){o(new Error("The request was aborted!"))},s.onerror=function(){o(new Error("An error occurred during the transaction!"))},s.ontimeout=function(){o(new Error("Timeout!"))},s.onload=function(){i()},n&&"get"===String(n.method).toLocaleLowerCase()){for(var c in s.open("GET",a+"?"+e,!0),t)s.setRequestHeader(c,t[c]);s.send()}else{var u=n&&n.async;for(var c in s.open("POST",a,null==u||u),t)s.setRequestHeader(c,t[c]);s.send(JSON.stringify(e))}}else o(new Error("url is missing"))}))},e.prototype.sendByImg=function(e,t,n){return Promise.reject(new Error("not supported"))},e.prototype.setUrl=function(e){this.url=e},e}()),_s=new(function(){function e(){}return e.prototype.getReferrer=function(){return document&&document.referrer},e.prototype.getHref=function(){return window&&window.location.href},e.prototype.getHost=function(){return window&&window.location.host},e.prototype.getDefaultTitle=function(){return document&&document.title},e.prototype.getUAType=function(){var e=navigator.userAgent;return e.match(/AppleWebKit.*Mobile.*/)?-1!==e.indexOf(" wv")?2:1:0},e.prototype.getStartType=function(){var e;try{var t=os.campaignPushInfo;return null!==(e=null==t?void 0:t.source)&&void 0!==e?e:es.parse(this.getReferrer()).origin}catch(e){return""}},e.prototype.getStartScene=function(){return null},e.prototype.getLibType=function(){return"Web"},e.prototype.getOaid=function(){return Promise.resolve("")},e.prototype.getOaidSource=function(){return Promise.resolve("")},e.prototype.getClientOaidFlag=function(){return Promise.resolve(!1)},e.prototype.getServerOaidFlag=function(){return Promise.resolve(!1)},e.prototype.getSystemOaidFlag=function(){return Promise.resolve(!1)},e.prototype.getCampaignPushInfo=function(){try{var e=es.parse(this.getHref());if(!(null==e?void 0:e.searchParams))return null;var t=e.searchParams.get("$CampaignPushInfo");if(!t)return null;var n=JSON.parse(t);return n&&0!==Object.keys(n).length?n:null}catch(e){return ls.hiLog.warn(ka,"",e.message),null}},e.prototype.isOnline=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,!0]}))}))},e}()),Ns=_.f,Rs=Function.prototype,Ls=/^\s*function ([^ (]*)/;"name"in Rs||w&&Ns(Rs,"name",{configurable:!0,get:function(){try{return(""+this).match(Ls)[1]}catch(e){return""}}});var $s=new(function(){function e(){this.infoMap={browser:["Safari","Chrome","Edge","IE","Firefox","Firefox Focus","Chromium","Opera"],os:["Windows","Linux","Mac OS","Android","HarmonyOS","Ubuntu","iOS"]}}return e.prototype.getMatchMap=function(e){return{Windows:e.indexOf("windows")>-1,Linux:e.indexOf("linux")>-1||e.indexOf("x11")>-1,"Mac OS":e.indexOf("macintosh")>-1,Android:e.indexOf("android")>-1||e.indexOf("adr")>-1,HarmonyOS:e.indexOf("harmonyos")>-1,Ubuntu:e.indexOf("ubuntu")>-1,iOS:e.indexOf("like mac os x")>-1}},e.prototype.getMatchInfo=function(e){var t="",n=this.getMatchMap(e);for(var r in this.infoMap)for(var i=0;i<this.infoMap[r].length;i++){var o=this.infoMap[r][i];n[o]&&(t=o)}return t},e.prototype.getOSName=function(e){return this.getMatchInfo(e)},e.prototype.getOsVersion=function(e){var t="",n={Windows:function(){var t=e.replace(/^.*windows nt ([\d.]+);.*$/,"$1"),n=[{version:"6.4",name:"10"},{version:"6.3",name:"8.1"},{version:"6.2",name:"8"},{version:"6.1",name:"7"},{version:"6.0",name:"Vista"},{version:"5.2",name:"XP"},{version:"5.1",name:"XP"},{version:"5.0",name:"2000"}].find((function(e){return e.version===t}));return n&&n.name||t},Android:function(){return e.replace(/^.*android ([\d.]+);.*$/,"$1")},HarmonyOS:function(){return e.replace(/^.*harmonyos ([\d.]+);.*$/,"$1")},iOS:function(){return e.replace(/^.*os ([\d_]+) like.*$/,"$1").replace(/_/g,".")},"Mac OS":function(){return e.replace(/^.*mac os x ([\d_]+).*$/,"$1").replace(/_/g,".")}},r=this.getOSName(e);return n[r]&&(t=n[r]())===e&&(t=""),t},e.prototype.getBrowserInfo=function(){var e,t={},n=navigator.userAgent.toLowerCase();return(e=n.match(/edge\/([\d.]+)/))?t.edge=e[1]:(e=n.match(/rv:([\d.]+)\) like gecko/))||(e=n.match(/msie ([\d.]+)/))?t.ie=e[1]:(e=n.match(/firefox\/([\d.]+)/))?t.firefox=e[1]:(e=n.match(/chrome\/([\d.]+)/))?t.chrome=e[1]:(e=n.match(/opera.([\d.]+)/))?t.opera=e[1]:(e=n.match(/version\/([\d.]+).*safari/))&&(t.safari=e[1]),t.chrome?{broswer:ar,version:t.chrome}:t.opera?{broswer:sr,version:t.opera}:t.safari?{broswer:cr,version:t.safari}:t.edge?{broswer:rr,version:t.edge}:t.ie?{broswer:ir,version:t.ie}:t.firefox?{broswer:or,version:t.firefox}:{broswer:"",version:"0"}},e.prototype.generateUserCommonProperty=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r,i;return pn(this,(function(o){switch(o.label){case 0:return this.immutableProperties||(e=navigator.userAgent.toLowerCase(),t=navigator.language.split("-").join("_").toLocaleUpperCase(),n=this.getBrowserInfo(),this.immutableProperties={_model:n.broswer,_lib_ver:"6.9.9-301",_lib_name:"hianalytics",_lib_type:ls.hiClient.getLibType(),_sys_language:t,_os:this.getOSName(e),_os_ver:this.getOsVersion(e),_screen_height:window&&window.screen.height||0,_screen_width:window&&window.screen.width||0,_browser:n.broswer,_browser_version:n.version,_browser_language:t,_first_open_time:va.getClientValue("HW_ha_firstOpenTime"),_is_testdevice:os.isTestDevice(),_package_name:os.getConfig("appId")||""}),i={_new_user_flag:os.getNewUserFlag(),_signed:Number(va.getClientValue("HW_ha_existUserId")||Nr),_restriction_enabled:String(fs.isRestrictionEnabled())},[4,os.resolveTerminalName()];case 1:return i._terminal_name=o.sent(),i._app_ver=os.getConfig("appVersion")||"1.0.0",r=i,os.getRoutePolicy()===Qa.CN&&(r._restriction_shared=String(fs.isRestrictionShared())),[2,Object.assign(r,this.immutableProperties)]}}))}))},e}()),Ds=new(function(){function e(){}return e.prototype.getClientValue=function(e){return fn(this,void 0,void 0,(function(){var t;return pn(this,(function(n){switch(n.label){case 0:return[4,this.getSavedObject(e)];case 1:return t=n.sent(),0===Object.keys(t).length?[2,Promise.resolve("")]:[2,Promise.resolve(t.value)]}}))}))},e.prototype.getSavedObject=function(e){return fn(this,void 0,void 0,(function(){var t,n,r=this;return pn(this,(function(i){return null===(t=localStorage.getItem(e))?[2,Promise.resolve({})]:va.isValidStoredItem(t,(function(){return fn(r,void 0,void 0,(function(){return pn(this,(function(t){return localStorage.getItem(e)&&localStorage.removeItem(e),[2]}))}))}))?"object"!==hn(n=JSON.parse(t))?[2,Promise.resolve({})]:[2,Promise.resolve(n)]:[2,Promise.resolve({})]}))}))},e.prototype.getSavedString=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){return[2,Promise.resolve(localStorage.getItem(e)||"")]}))}))},e.prototype.isKeyExist=function(e){return fn(this,void 0,void 0,(function(){var t;return pn(this,(function(n){switch(n.label){case 0:return[4,this.getClientValue(e)];case 1:return t=n.sent(),[2,Promise.resolve(!!t)]}}))}))},e.prototype.saveClientValue=function(e,t,n){return fn(this,void 0,void 0,(function(){var r;return pn(this,(function(i){return r=va.makeExpiryTime(n),localStorage.setItem(e,JSON.stringify({value:t,expiryTime:r})),[2,Promise.resolve()]}))}))},e.prototype.removeItem=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.getClientValue(e)];case 1:return""!==t.sent()&&localStorage.removeItem(e),[2]}}))}))},e}()),ks="ID",Us="CONTENT",Ms=new(function(){function e(){}return e.prototype.getDatabase=function(){return fn(this,void 0,void 0,(function(){var e=this;return pn(this,(function(t){return[2,new Promise((function(t,n){if(e.database)t(e.database);else{var r=window.indexedDB.open("EventModels.db",1);r.onerror=function(e){ls.hiLog.error(ka,"","open indexedDB failed")},r.onsuccess=function(r){try{e.database=r.target.result,t(e.database)}catch(e){n(e)}},r.onupgradeneeded=function(t){e.database=t.target.result;try{e.database.objectStoreNames.contains("EVENTS")||e.database.createObjectStore("EVENTS",{keyPath:ks})}catch(e){n(e)}}}}))]}))}))},e.prototype.getAllClientValue=function(e){return fn(this,void 0,void 0,(function(){var t=this;return pn(this,(function(n){switch(n.label){case 0:return[4,this.getDatabase()];case 1:return null==n.sent()?[2,[]]:[2,new Promise((function(n,r){var i=[];t.database.transaction("EVENTS").objectStore("EVENTS").openCursor().onsuccess=function(r){var o=r.target.result;if(o){var a=o.value;Qo(a[Us])&&t.removeItem(a[ks],e),i.push(a[Us]),o.continue()}else n(i)}}))]}}))}))},e.prototype.getClientValues=function(e,t){return fn(this,void 0,void 0,(function(){var n=this;return pn(this,(function(r){switch(r.label){case 0:return[4,this.getDatabase()];case 1:return null==r.sent()?[2,[]]:[2,new Promise((function(r,i){var o=[];n.database.transaction("EVENTS").objectStore("EVENTS").getAll(e).onsuccess=function(e){var i=e.target.result;if(i){var a=i.value;Qo(a[Us])&&n.removeItem(a[ks],t),o.push(a[Us]),i.continue()}else r(o)}}))]}}))}))},e.prototype.getClientValue=function(e,t){return fn(this,void 0,void 0,(function(){var t=this;return pn(this,(function(n){switch(n.label){case 0:return[4,this.getDatabase()];case 1:return null==n.sent()?[2,""]:[2,new Promise((function(n,r){var i=t.database.transaction("EVENTS").objectStore("EVENTS").get(e);i.onerror=function(e){return n("")},i.onsuccess=function(e){var t;return n((null===(t=null==i?void 0:i.result)||void 0===t?void 0:t.content)||"")}}))]}}))}))},e.prototype.isKeyExist=function(e,t){return fn(this,void 0,void 0,(function(){var n;return pn(this,(function(r){switch(r.label){case 0:return[4,this.getClientValue(e,t)];case 1:return n=r.sent(),[2,Promise.resolve(!!n)]}}))}))},e.prototype.saveClientValue=function(e,t,n){return fn(this,void 0,void 0,(function(){var n,r,i;return pn(this,(function(o){switch(o.label){case 0:return[4,this.getDatabase()];case 1:return null==o.sent()?[2]:((n={})[ks]=e,n[Us]=t,r=this.database.transaction(["EVENTS"],"readwrite").objectStore("EVENTS"),(i=r.get(e)).onsuccess=function(e){var t=i.result,o=null!=t&&Us in t?r.put(n):r.add(n);o.onsuccess=function(){return Promise.resolve()},o.onerror=function(e){return Promise.reject(e.target.error)}},i.onerror=function(e){return Promise.reject(e.target.error)},[2,Promise.resolve()])}}))}))},e.prototype.removeItem=function(e,t){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){switch(t.label){case 0:return[4,this.getDatabase()];case 1:return null==t.sent()||this.database.transaction(["EVENTS"],"readwrite").objectStore("EVENTS").delete(e),[2]}}))}))},e.prototype.removeItems=function(e,t){return fn(this,void 0,void 0,(function(){var n;return pn(this,(function(r){switch(r.label){case 0:return[4,this.getDatabase()];case 1:if(null==r.sent())return[2];for(n=0;n<e.length;n++)this.removeItem(e[n],t);return[2]}}))}))},e.prototype.removeAllItems=function(e){return fn(this,void 0,void 0,(function(){var e=this;return pn(this,(function(t){switch(t.label){case 0:return[4,this.getDatabase()];case 1:return null==t.sent()?[2]:[2,new Promise((function(t,n){e.database.transaction("EVENTS","readwrite").objectStore("EVENTS").clear()}))]}}))}))},e}()),xs=function(){function e(e){this.type="",this.event="",this.properties={},this.eventtime=0,this.event_session_name="",this.first_session_event="",this.id="",this.sendState=br,this.collectionUrl="",this.routePolicy="",this.id=null!=e?e:ta.create()}return e.toMemoryEventModel=function(e){return ha(e,["properties","eventtime","event_session_name","first_session_event"])},e.toStorageEventModel=function(e){return ha(e,["sendState"])},e}(),Vs=new(function(){function e(){this.headAppId="",this.timestamp=0,this.serviceTag=Yn,this.serviceId="hmshioperqrt",this.reqID="",this.productId=""}return e.prototype.setHeadAppId=function(e){this.headAppId=e},e.prototype.setTimestamp=function(e){this.timestamp=e},e.prototype.setServiceTag=function(e){this.serviceTag=e},e.prototype.setServiceId=function(e){this.serviceId=e},e.prototype.setReqID=function(e){this.reqID=e},e.prototype.setProductId=function(e){this.productId=e},e.prototype.getEventHead=function(){return{protocol_version:"1",serviceid:this.serviceId,appid:this.headAppId,timestamp:this.timestamp,servicetag:this.serviceTag,requestid:this.reqID,productid:this.productId}},e}());var Hs=La,Fs=$a,js=Da,Bs=function(){function e(){this.sessionFlag="",this.eventUploadData={},this.isInit=!1}return e.prototype.getEvent=function(e,t,n,r,i){var o,a,s,c=new xs;c.properties=t,c.event=e,c.eventtime=r,c.type=n,c.first_session_event=String(!this.isInit),c.event_session_name=(Qo(s=va.getSessionValue())&&(va.handleSessionValue(!0,!1),s=va.getSessionValue()),s),c.sendState=Ir,c.collectionUrl=null!==(o=i.url)&&void 0!==o?o:os.getConfig("analyticsUrl");var u=null!==(a=i.region)&&void 0!==a?a:os.getConfig(Nn),l=ls.hiAgcUtil.getConfig().region||"";c.routePolicy="UNKNOWN"===u?l:u;var f=ls.hiAgcUtil.getABTestProvider();if(f){var p=c,h={tag:ka,type:n,source:On};f.onEvent(e,p,h)}return this.isInit||(this.isInit=!0),c},e.prototype.generateHttpHeader=function(e){var t=void 0===e?{}:e,n=t.reqId,r=void 0===n?"":n,i=t.logRegion,o=void 0===i?"":i,a=t.sdkToken,s=void 0===a?"":a,c=t.supportDebugMode,u=void 0===c||c,l={};return l[Ln]=os.getConfig("appId"),l[Bn]=os.getConfig("productId"),l[jn]=os.getConfig("resourceId"),l[$n]=Hs,l[kn]=Fs,l[Un]=o?Wn:Yn,l[Mn]=os.getConfig("packageName"),l[xn]=r,l[Dn]=js,l[Gn]=os.getConfig("clientId"),o&&(l[Fn]=encodeURIComponent(o)),u&&(l[Vn]=String(os.getConfig(_n))),l[Hn]=s,l},e.prototype.generateEventUploadData=function(e,t){var n;return fn(this,void 0,void 0,(function(){var r,i,o,a;return pn(this,(function(s){switch(s.label){case 0:return Vs.setHeadAppId(os.getConfig("appId")),Vs.setProductId(os.getConfig("productId")),Vs.setTimestamp((new Date).getTime()),Vs.setReqID(t),r=Vs.getEventHead(),(i={})[Pn]=e.map((function(e){return ha(e,["id","sendState","collectionUrl","routePolicy"])})),o=null===(n=null==e?void 0:e[0])||void 0===n?void 0:n.routePolicy,[4,ps.getUserInfo({routePolicy:o})];case 1:return a=s.sent(),i[In]=a,this.eventUploadData[bn]=r,this.eventUploadData[On]=i,[2,this.eventUploadData]}}))}))},e.prototype.generateLogPayload=function(e,t){var n={protocol_version:"1",serviceid:"hmshimaintqrt",appid:os.getConfig("appId"),timestamp:(new Date).getTime(),servicetag:Wn,requestid:t,productid:os.getConfig("productId")},r=[];return e.forEach((function(e){var t,n,i=r.find((function(t){var n=t.events_common;return n.projectId===e.projectId&&n.groupId===e.groupId&&n.streamId===e.streamId}));i?(i.events_common.tags=e.tags,i.events.push({eventtime:String(e.eventtime),event:"$WriteLog",properties:(t={},t[Jn]=ts.getPageIdByUrl(os.prevScreenName),t[Qn]=ts.getPageIdByUrl(os.currentScreenName),t.$Content=e.content,t)})):r.push({events_common:{projectId:e.projectId,groupId:e.groupId,streamId:e.streamId,tags:e.tags},events:[{eventtime:String(e.eventtime),event:"$WriteLog",properties:(n={},n[Jn]=ts.getPageIdByUrl(os.prevScreenName),n[Qn]=ts.getPageIdByUrl(os.currentScreenName),n.$Content=e.content,n)}]})})),{header:n,event:r}},e.prototype.getUploadEventModels=function(e,t){var n;n=e===Gr.STOP_ANALYTICS_COLLECTION?ls.sendTask.eventModels.filter((function(e){return e.event===Gr.STOP_ANALYTICS_COLLECTION&&e.sendState===Ir})):e===Gr.LaunchApp?t?ls.sendTask.eventModels.filter((function(e){var t=e.sendState===Ir,n=e.sendState===_r;return-1!=[Gr.LaunchApp,Gr.FIRST_OPEN,Gr.ENTER_SCREEN,Gr.EXIT_SCREEN].indexOf(e.event)&&(t||n)})):ls.sendTask.eventModels.filter((function(t){var n=t.sendState===Ir,r=t.sendState===_r,i=t.event!==Gr.STOP_ANALYTICS_COLLECTION;return t.event===e&&(i&&n||r)})):t?ls.sendTask.eventModels.filter((function(t){var n=t.sendState===Ir,r=t.sendState===_r,i=t.event!==Gr.STOP_ANALYTICS_COLLECTION;return t.event===e&&(i&&n||r)})):ls.sendTask.eventModels.filter((function(e){var t=e.sendState===Ir,n=e.sendState===_r;return e.event!==Gr.STOP_ANALYTICS_COLLECTION&&t||n}));var r=os.getConfig("analyticsUrl"),i="",o=n.filter((function(e){var t=e.collectionUrl,n=t!==r&&(""===i||t===i);return n&&""===i&&(i=e.collectionUrl),n}));return o.length>0&&(n=o),n=n.filter((function(e){var t=!0;return fs.isDisableEvent(e.event)&&(t=!1,ls.sendTask.eventModels=ls.sendTask.eventModels.filter((function(t){return t.id!==e.id})),ls.hiEventModelsAdapter.removeModel(e.id)),t}))},e.prototype.getUploadLogModels=function(){var e=ls.sendLogTask.logRecords.filter((function(e){return e.sendState===Ir||e.sendState===_r})),t=os.getConfig("analyticsUrl"),n="",r=e.filter((function(e){var r=e.collectionUrl!==t&&(""===n||e.collectionUrl===n);return r&&""===n&&(n=e.collectionUrl),r}));return r.length>0&&(e=r),e},e}(),Gs=!1,Ws=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return ln(t,e),t.prototype.saveEventModel=function(e){return e.event===Gr.EXIT_SCREEN&&ls.hiEventModelsAdapter.saveModel(e.id,e),Promise.resolve()},t.prototype.dtmProcessAspect=function(e,t,n){void 0===n&&(n={});var r=!1;if(n.isCallBack||!1)return r;if("undefined"!=typeof window&&window.hmscore_dtm)if(n.isReserved||!1){var i=window.hmscore_dtm.logAutoEvent;"function"==typeof i&&i(e,t)}else{var o=window.hmscore_dtm.logEvent;"function"==typeof o&&o(e,t)&&(r=!0)}return r},t}(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return ln(t,e),t.prototype.init=function(){return fn(this,void 0,void 0,(function(){var e;return pn(this,(function(t){switch(t.label){case 0:return[4,ls.hiEventModelsAdapter.queryAllModels(!0)];case 1:return 0==(e=t.sent()).length||this.generateEventModels(e),[2]}}))}))},t.prototype.generateEventModels=function(e){this.eventModels=this.eventModels.concat(e.map((function(e){var t=new xs;Object.assign(t,e),t.sendState=Ir,t.collectionUrl=os.getConfig("analyticsUrl");var n=os.getConfig(Nn),r=ls.hiAgcUtil.getConfig().region||"";return t.routePolicy="UNKNOWN"===n?r:n,t})))},t.prototype.doEventSendDone=function(t,n){void 0===n&&(n=!1),e.prototype.doEventSendDone.call(this,t,n);var r=this.eventModels.filter((function(e){return e.sendState===Or})).map((function(e){return e.id}));ls.hiEventModelsAdapter.removeModels(r)},t.prototype.doEventSendFail=function(t,n){if(e.prototype.doEventSendFail.call(this,t,n),n||os.getConfig(_n)){var r=this.eventModels.filter((function(e){return e.sendState===_r})).map((function(e){return e.id}));ls.hiEventModelsAdapter.removeModels(r)}},t.prototype.saveEventModel=function(e){return ls.hiEventModelsAdapter.saveModel(e.id,e)},t.prototype.removeAllEventModels=function(){ls.hiEventModelsAdapter.removeAllModels()},t.prototype.removeEventModels=function(e){var t=this.eventModels.filter((function(t){return null==e||t.routePolicy===e})).map((function(e){return e.id}));ls.hiEventModelsAdapter.removeModels(t)},t.prototype.generateFinalUploadEventData=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){return[2,e]}))}))},t.prototype.isServerWebV3=function(){return!0},t}(function(){function e(){this.eventModels=[],this.upLoadService=new Bs}return e.prototype.init=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.uploadData=function(e,t){return void 0===e&&(e=!1),fn(this,void 0,void 0,(function(){var n,r,i,o,a,s,c,u=this;return pn(this,(function(l){switch(l.label){case 0:return[4,ls.hiClient.isOnline()];case 1:return l.sent()&&(fs.reportFlag||e&&t===Gr.STOP_ANALYTICS_COLLECTION)?0===this.eventModels.length?[2]:[4,this.getAaidAndAuthInfo()]:[2];case 2:return n=l.sent(),r=n.isValid,n.aaid,i=n.authInfo,r&&i?[4,this.generateFinalUploadInfos(e,t,i)]:[2];case 3:if(0===(o=l.sent()).length)return[2];for(a=function(t){0===t?s.sendEventData(e,o[t]):setTimeout((function(){u.sendEventData(e,o[t])}),200*t)},s=this,c=0;c<o.length;c++)a(c);return[2]}}))}))},e.prototype.sendEventData=function(e,t){var n=this,r=t.finalUploadEventData,i=t.uploadEventDataEventModels,o=t.isOtherCollectionUrl,a=t.config,s=t.uploadDataHeader;ls.hiSendData.onEvent(r,s,a).then((function(t){ls.hiLog.info("resultCode: 200"),n.setEventModelsStatus(i,Or),n.doEventSendDone(i,e),n.eventModels=n.eventModels.filter((function(e){return e.sendState!==Or}))})).catch((function(){n.setEventModelsStatus(i,_r),n.doEventSendFail(i,o),(o||os.getConfig(_n))&&(n.eventModels=n.eventModels.filter((function(e){return e.sendState!==_r})))}))},e.prototype.chunkEventModels=function(e){for(var t=[],n=ls.sendPolicy.getUploadMaxCount(),r=Math.ceil(e.length/n),i=0;i<r;i++){var o=e.slice(i*n,(i+1)*n);t.push(o)}return t},e.prototype.generateFinalUploadInfos=function(e,t,n){return void 0===e&&(e=!1),fn(this,void 0,void 0,(function(){var r,i,o,a,s,c,u,l,f,p,h,d,g;return pn(this,(function(v){switch(v.label){case 0:if(r=[],0===(i=this.upLoadService.getUploadEventModels(t,e)).length)return[2,r];o=this.backupEventModelStatus(i),this.setEventModelsStatus(i,Pr),"",a=this.upLoadService.generateHttpHeader({reqId:"",sdkToken:n}),s=this.chunkEventModels(i),c=0,v.label=1;case 1:return c<s.length?[4,this.getUploadEventDataEventModels(s[c])]:[3,6];case 2:return u=v.sent(),this.revertEventModelStatus(u,s[c],o),l=u[0],f=os.getConfig("analyticsUrl")!==l.collectionUrl,p={mode:os.getReportType(),url:l.collectionUrl,async:!e},h=ta.create(),[4,this.upLoadService.generateEventUploadData(u,h)];case 3:return d=v.sent(),this.uploadDataAppendEventHeader(d,l),[4,this.generateFinalUploadEventData(d)];case 4:if("string"==typeof(g=v.sent())&&Qo(g))return[3,5];a[xn]=h,r.push(ha({finalUploadEventData:g,uploadEventDataEventModels:u,config:p,isOtherCollectionUrl:f,uploadDataHeader:a})),v.label=5;case 5:return c++,[3,1];case 6:return[2,r]}}))}))},e.prototype.backupEventModelStatus=function(e){for(var t={},n=0;n<e.length;n++){var r=e[n];t[r.id]=r.sendState}return t},e.prototype.revertEventModelStatus=function(e,t,n){if(e.length!==t.length)for(var r=e.map((function(e){return e.id})),i=0;i<t.length;i++){var o=t[i];-1===r.indexOf(o.id)&&this.setEventModelsStatus([o],n[o.id])}},e.prototype.getUploadEventDataEventModels=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){return[2,e]}))}))},e.prototype.getAaidAndAuthInfo=function(){return fn(this,void 0,void 0,(function(){var e,t,n;return pn(this,(function(r){switch(r.label){case 0:return(e=os.getAAID())?[3,2]:[4,Ha.collect(Ua)];case 1:if(!(t=r.sent())[Ua])return[2,{isValid:!1}];os.addConfig("aaid",t[Ua]),os.setIsTestDevice(),e=os.getAAID(),r.label=2;case 2:return[4,os.getHaSdkToken()];case 3:return(n=r.sent())?[2,{aaid:e,authInfo:n,isValid:!0}]:[2,{isValid:!1}]}}))}))},e.prototype.uploadDataAppendEventHeader=function(e,t){},e.prototype.generateFinalUploadEventData=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){return[2,e]}))}))},e.prototype.doEventSendDone=function(e,t){},e.prototype.doEventSendFail=function(e,t){},e.prototype.dtmProcessAspect=function(e,t,n){return!1},e.prototype.onEvent=function(e,t,n,r,i){var o,a=this;void 0===i&&(i={}),t=t||{};var s=i.immediately||!1;if(!this.dtmProcessAspect(e,t,i)&&""!==(null!==(o=i.url)&&void 0!==o?o:os.getConfig("analyticsUrl"))){var c=this.upLoadService.getEvent(e,t,n,r,i);this.pushEventModel(c);var u=this.saveEventModel(c);s&&u.then((function(){a.uploadData(s,e).then((function(e){})).catch((function(e){}))}))}},e.prototype.setHeartbeatFunc=function(e){var t=this;this.timer=setTimeout((function(){Gs||(Gs=!0,t.uploadData(!1,"").then((function(e){Gs=!1,ls.sendPolicy.eventUploadPolicyScheduleTime()})).catch((function(e){Gs=!1,ls.sendPolicy.eventUploadPolicyScheduleTime()})))}),e)},e.prototype.setEventModelsStatus=function(e,t,n){var r=e.map((function(e){return e.id}));this.eventModels.forEach((function(e){-1!==r.indexOf(e.id)&&(void 0!==n&&e.sendState!==n||(e.sendState=t))}))},e.prototype.processEventsReceivedAfterLaunch=function(){"false"===va.getClientValue(Mr)&&(this.eventModels.length=0,this.removeAllEventModels())},e.prototype.clearData=function(e){this.removeEventModels(e),this.eventModels=this.eventModels.filter((function(t){return null!=e&&t.routePolicy!==e}))},e.prototype.getCurFocusStateSessionDuration=function(e){return os.isBackground?va.minSessionDuration:va.sessionDuration},e.prototype.pushEventModel=function(e){this.eventModels.push(e)},e.prototype.saveEventModel=function(e){return Promise.resolve()},e.prototype.removeEventModels=function(e){},e.prototype.removeAllEventModels=function(){},e.prototype.isServerWebV3=function(){return!0},e.prototype.isServerWebEvent=function(){return!1},e}()))),Ys=new(function(){function e(){}return e.prototype.getClientValue=function(e){var t=new RegExp("(^|;)[ ]*"+e+"=([^;]*)").exec(document.cookie);return Promise.resolve(t?decodeURIComponent(t[2]):"")},e.prototype.getSavedObject=function(e){return fn(this,void 0,void 0,(function(){var t,n;return pn(this,(function(r){switch(r.label){case 0:return[4,this.getClientValue(e)];case 1:return t=r.sent(),n=va.makeExpiryTime(31536e6),[2,Promise.resolve({value:t,expiryTime:n})]}}))}))},e.prototype.getSavedString=function(e){return fn(this,void 0,void 0,(function(){return pn(this,(function(t){return[2,this.getClientValue(e)]}))}))},e.prototype.isKeyExist=function(e){return fn(this,void 0,void 0,(function(){var t;return pn(this,(function(n){switch(n.label){case 0:return[4,this.getClientValue(e)];case 1:return t=n.sent(),[2,Promise.resolve(!!t)]}}))}))},e.prototype.saveClientValue=function(e,t,n,r,i,o){var a=new Date;return e!==Mr&&e!==xr||(n=31536e6),n&&a.setTime(a.getTime()+n),document.cookie=e+"="+encodeURIComponent(t)+(n?";expires="+a.toUTCString():"")+";path="+(r||"/")+(i?";domain="+i:"")+(o?";secure":""),Promise.resolve()},e.prototype.removeItem=function(e){return document.cookie=e+"=;path=/;expires="+new Date(0).toUTCString(),Promise.resolve()},e.prototype.getAllItems=function(){return fn(this,void 0,void 0,(function(){var e,t;return pn(this,(function(n){return e=document.cookie.split(/;\s/g),t={},e.forEach((function(e){var n=e.split("=")[0].trim();-1!==n.search(new RegExp("^HW_"))&&(t[n]=e.split("=")[1])})),[2,Promise.resolve(t)]}))}))},e}()),Ks=new(function(){function e(){}return e.prototype.migrate=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){switch(e.label){case 0:return[4,this.migrateCookie2LocalStorage()];case 1:return e.sent(),[2,Promise.resolve()]}}))}))},e.prototype.migrateCookie2LocalStorage=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r,i,o,a;return pn(this,(function(s){switch(s.label){case 0:return[4,Ys.getAllItems()];case 1:for(n in e=s.sent(),t=[],e)t.push(n);r=0,s.label=2;case 2:return r<t.length?(i=t[r],o=this.getSavedKey(i),a=o===Hr?"true":e[i],[4,ls.hiStorage.saveClientValue(o,a,this.getDuration(i))]):[3,5];case 3:s.sent(),Ys.removeItem(i),s.label=4;case 4:return r++,[3,2];case 5:return[2]}}))}))},e.prototype.getSavedKey=function(e){var t=ls.hiAgcUtil.getConfig().client.app_id,n=e;return n==="HW_ha_"+t?n=Hr:n==="HW_hid_"+t?n=Ur:"HW_analytics_enabled"===n?n=Mr:"HW_ha_restriction_enabled"===n&&(n=xr),n},e.prototype.getDuration=function(e){var t=31536e6;return e===Ur&&(t=va.sessionDuration),t},e}()),qs=!1,zs=function(){function e(){this.logRecords=[],this.timerId=0,this.upLoadService=new Bs}return e.prototype.init=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.uploadData=function(){return fn(this,void 0,void 0,(function(){var e,t,n,r=this;return pn(this,(function(i){switch(i.label){case 0:return[4,ls.hiClient.isOnline()];case 1:return i.sent()&&fs.reportFlag?0===this.logRecords.length?[2]:[4,os.getHaSdkToken()]:[2];case 2:return(e=i.sent())?(t={mode:os.getReportType(),url:os.getConfig("analyticsUrl")},n={},this.logRecords.forEach((function(e){var t=e.sendState;if(t===Ir||t===_r){var r=e.region;n[r]=n[r]||[],n[r].push(e)}})),Object.entries(n).forEach((function(n){for(var i=n[0],o=n[1],a=r.chunkLogDatas(o),s=function(n){var o=a[n];if(0===o.length)return{value:void 0};var s=ta.create(),c=r.upLoadService.generateLogPayload(o,s),u=r.generateFinalUploadLogData(c);if("string"==typeof u&&Qo(u))return{value:void 0};var l=r.upLoadService.generateHttpHeader({reqId:s,logRegion:i,supportDebugMode:!1,sdkToken:e});o.forEach((function(e){return e.sendState=Pr})),ls.hiSendData.onEvent(u,l,t).then((function(){ls.hiLog.info("upload log successfully, resultCode: 200"),o.forEach((function(e){return e.sendState=Or})),r.doLogSendDone(o),r.logRecords=r.logRecords.filter((function(e){return e.sendState!==Or}))}),(function(){o.forEach((function(e){return e.sendState=_r})),r.doLogSendFail(o)}))},c=0;c<a.length;c++){var u=s(c);if("object"===hn(u))return u.value}})),[2]):[2]}}))}))},e.prototype.chunkLogDatas=function(e){var t=ls.sendPolicy.getUploadMaxCount();return[e.slice(0,t)]},e.prototype.generateFinalUploadLogData=function(e){return e},e.prototype.setHeartbeatFunc=function(e){var t=this;this.timerId=setTimeout((function(){qs||(qs=!0,t.uploadData().then((function(e){qs=!1,ls.sendPolicy.logUploadPolicyScheduleTime()})).catch((function(e){qs=!1,ls.sendPolicy.logUploadPolicyScheduleTime()})))}),e)},e.prototype.writeLog=function(e){e.sendState=Ir,this.logRecords.push(e),this.saveLogModel(e)},e.prototype.doLogSendDone=function(e){},e.prototype.doLogSendFail=function(e){},e.prototype.saveLogModel=function(e){},e}();new zs;var Js,Xs,Qs=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return ln(t,e),t}(zs));!function(e){e[e.DEBUG=0]="DEBUG",e[e.VERBOSE=1]="VERBOSE",e[e.INFO=2]="INFO",e[e.WARN=3]="WARN",e[e.ERROR=4]="ERROR",e[e.SILENT=5]="SILENT"}(Xs||(Xs={}));var Zs,ec,tc=function(){function e(){var t=this;this.moduleName="",this.logLevel=Xs.VERBOSE,this.userLogProvider=null,this.logProvider=function(n,r){for(var i=[],o=2;o<arguments.length;o++)i[o-2]=arguments[o];if(!(r<t.logLevel)){var a=e.consoleType[r];if(!a)throw new Error("invalid logType: "+r);console[a]("[Module:"+n+"] ["+(new Date).toISOString()+"] | ",i.toString())}}}return e.createLogger=function(t){for(var n=0,r=e.logInstanceArray;n<r.length;n++){var i=r[n];if(i.moduleName==t)return i}var o=new e;return o.moduleName=t,e.logInstanceArray.push(o),o},e.prototype.setLogProvider=function(e){if("function"!=typeof e)throw new Error("logProvider must be set as a function");this.logProvider=e},e.prototype.setUserLogProvider=function(e){this.userLogProvider=e},e.prototype.debug=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.doLog(Xs.DEBUG,e)},e.prototype.log=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.doLog(Xs.VERBOSE,e)},e.prototype.info=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.doLog(Xs.INFO,e)},e.prototype.warn=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.doLog(Xs.WARN,e)},e.prototype.error=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];this.doLog(Xs.ERROR,e)},e.prototype.doLog=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];this.userLogProvider&&this.userLogProvider(this.moduleName,e,t),this.logProvider(this.moduleName,e,t)},e.consoleType=((Js={})[Xs.DEBUG]="log",Js[Xs.VERBOSE]="log",Js[Xs.SILENT]="log",Js[Xs.INFO]="info",Js[Xs.WARN]="warn",Js[Xs.ERROR]="error",Js),e.logInstanceArray=[],e}(),nc=function(e,t){return(nc=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)},rc=function(e){function t(n,r,i){var o=this,a=n.code+t.COLON+n.message;return i&&(a=i+t.DASH+a),r&&(r.message?a=a+t.COMMA+r.message:"string"==typeof r&&(a=a+t.COMMA+r)),(o=e.call(this,a)||this).__proto__=t.prototype,o.code=n.code,o.message=n.message,r&&(r.message?o.message=n.message+t.COMMA+r.message:"string"==typeof r&&(o.message=n.message+t.COMMA+r)),o.msg=o.message,o}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}nc(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),t.COLON=": ",t.COMMA=", ",t.DASH="-",t}(Error),ic=function(){function e(){}return e.AGC_INNER_ERROR={code:1e4,message:"agc inner error"},e.NETWORK_REQUEST_ERROR={code:10001,message:"agc network request error"},e.GET_AAID_ERROR={code:10002,message:"agc get aaid error"},e.ABTEST_LOAD_EXPERIMENTS_ERROR={code:10003,message:"abtest load experiments error"},e.ABTEST_SAVE_EXPERIMENTS_ERROR={code:10004,message:"abtest save experiments error"},e.ABTEST_REPLACE_EXPERIMENTS_ERROR={code:10005,message:"abtest repalce experiments error"},e.FAIL_TO_GET_STORAGE_SERVICE={code:10006,message:"get agcStorage service failed"},e.FAIL_TO_GET_NETWORK_SERVICE={code:10007,message:"get agcNetwork service failed"},e.REMOVE_TOKEN_FAILED={code:10008,message:"remove client token faild"},e.GET_TOKEN_FAILED={code:10009,message:"get client token faild"},e.AGC_INIT_ERROR={code:10010,message:"AGCInstance init error"},e.FAIL_TO_GET_CRIDENTIAL_SERVICE={code:10011,message:"get agcCredential service failed"},e.WEBSOCKET_NOT_SUPPORT={code:10012,message:"websocket is not support"},e.WEBSOCKET_ERROR={code:10013,message:"websocket error"},e}(),oc=tc.createLogger("agconnectInstance"),ac=function(){function e(e,t){this.appVersion="",this._config=null,this._customCredentialsProvider=null,this._customAuthProvider=null,this.cryptImpl=void 0,this.option=null,this.repo_=e,this.name_=t||n}return e.prototype.name=function(){return this.name_},e.prototype.configInstance=function(e){return this._config=e,this},e.prototype.config=function(){return this.checkBeforeSetKey(),this._config},e.prototype.setApiKey=function(e){this.checkBeforeSetKey(),this._config.client.api_key=e},e.prototype.setClientSecret=function(e){this.checkBeforeSetKey(),this._config.client.client_secret=e},e.prototype.setClientId=function(e){this.checkBeforeSetKey(),this._config.client.client_id=e},e.prototype.getService=function(e){return this.repo_.getService(e,this,this.name_)},e.prototype.setCustomCredentialsProvider=function(e){return e?e.getToken?1!=e.getToken.length?(oc.error("the customCredentialsProvider getToken method must contain single parameter：forceRefresh."),!1):(this._customCredentialsProvider=e,!0):(oc.error("the customCredentialsProvider must contain getToken method."),!1):(this._customCredentialsProvider=e,!0)},e.prototype.setCustomAuthProvider=function(e){return e?e.getToken?1!=e.getToken.length?(oc.error("the customAuthProvider getToken method must contain single parameter：forceRefresh."),!1):(this._customAuthProvider=e,!0):(oc.error("the customAuthProvider must contain getToken method."),!1):(this._customAuthProvider=e,!0)},e.prototype.getCustomCredentialsProvider=function(){return this._customCredentialsProvider},e.prototype.getCustomAuthProvider=function(){return this._customAuthProvider},e.prototype.setCryptImp=function(e){return e?e.decrypt&&e.decrypt instanceof Function&&e.encrypt&&e.encrypt instanceof Function?(this.cryptImpl=e,!0):(oc.error("the crypt is not exist necessary methods."),!1):(oc.error("the crypt is not available."),!1)},e.prototype.setAppVersion=function(e){this.appVersion=e},e.prototype.setOption=function(e){this.option=e},e.prototype.addHttpToUrl=function(e){return e&&!e.startsWith("https://")?"https://"+e:e},e.prototype.getGwUrl=function(){return null!=this.option?this.option.routePolicy==Number(1)?this.addHttpToUrl(this._config.agcgw_all.CN):this.option.routePolicy==Number(2)?this.addHttpToUrl(this._config.agcgw_all.DE):this.option.routePolicy==Number(3)?this.addHttpToUrl(this._config.agcgw_all.RU):this.option.routePolicy==Number(4)?this.addHttpToUrl(this._config.agcgw_all.SG):this.addHttpToUrl(this._config.agcgw.url):this.addHttpToUrl(this._config.agcgw.url)},e.prototype.getGwBackUrl=function(){return null!=this.option?1==this.option.routePolicy?this.addHttpToUrl(this._config.agcgw_all.CN_back):2==this.option.routePolicy?this.addHttpToUrl(this._config.agcgw_all.DE_back):3==this.option.routePolicy?this.addHttpToUrl(this._config.agcgw_all.RU_back):4==this.option.routePolicy?this.addHttpToUrl(this._config.agcgw_all.SG_back):this.addHttpToUrl(this._config.agcgw.backurl):this.addHttpToUrl(this._config.agcgw.backurl)},e.prototype.getAppVersion=function(){return this.appVersion},e.prototype.getCryptImp=function(){return this.cryptImpl},e.prototype.checkBeforeSetKey=function(){if(null==this._config)throw new rc(ic.AGC_INIT_ERROR,{message:"AGCInstance not configurated. call agconnect.instance().configInstance() to configure the agconnect."},"instance");if(!this._config.agcgw||!this._config.client)throw new rc(ic.AGC_INIT_ERROR,{message:"AGCInstance config is invalid."},"instance")},e}(),sc=new i((function(e){return new ac(t,e[0])}));
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */!function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.CHINA=1]="CHINA",e[e.GERMANY=2]="GERMANY",e[e.RUSSIA=3]="RUSSIA",e[e.SINGAPORE=4]="SINGAPORE"}(Zs||(Zs={})),ec={AGCRoutePolicy:Zs},t.registerApiProvider("instance",(function(e){return sc.get(e)}),ec);var cc=function(){function e(){this.storage={}}return e.getInstance=function(t,n){return e.memoryInsMap.has(t)&&e.memoryInsMap.get(t)||e.memoryInsMap.set(t,new e),e.memoryInsMap.get(t)},e.prototype.get=function(e){return Promise.resolve(this.storage[e])},e.prototype.remove=function(e){return delete this.storage[e],Promise.resolve()},e.prototype.set=function(e,t){return this.storage[e]=t,Promise.resolve()},e.memoryInsMap=new Map,e}(),uc=function(){function e(){this.encryptImpl=void 0}return e.prototype.setEncryptImp=function(e){this.encryptImpl=e},e.prototype.decrypt=function(e){return null!=this.encryptImpl&&null!=this.encryptImpl&&null!=e&&null!=e?this.encryptImpl.decrypt(e):e},e.prototype.encrypt=function(e){return null!=this.encryptImpl&&null!=this.encryptImpl&&null!=e&&null!=e?this.encryptImpl.encrypt(e):e},e}(),lc=function(){function e(){this.DB_NAME="agcLocalStorageDb",this.OBJECT_STORE_NAME="agc",this.KEY_PATH="agcStorage",this.VERSION=1,this.agcCryptImpl=new uc}return e.getInstance=function(t,n){if(!window.indexedDB)throw tc.createLogger("AGCStorageService").error("Your environment doesn't support a stable version of IndexedDB."),new rc(ic.FAIL_TO_GET_STORAGE_SERVICE,{message:"Your environment doesn't support a stable version of IndexedDB."});e.indexedDBInsMap.has(t)&&e.indexedDBInsMap.get(t)||e.indexedDBInsMap.set(t,new e);var r=e.indexedDBInsMap.get(t);return r.agcCryptImpl.setEncryptImp(n),r},e.prototype.initIndexedDb=function(e){var t=this;return window.indexedDB?new Promise((function(n,r){var i=window.indexedDB.open(t.DB_NAME,t.VERSION),o=t;i.onupgradeneeded=function(e){var t=e.target.result;try{t.objectStoreNames.contains(o.OBJECT_STORE_NAME)||t.createObjectStore(o.OBJECT_STORE_NAME,{keyPath:o.KEY_PATH})}catch(e){r(e)}},i.onsuccess=function(t){try{n(t.target.result.transaction([o.OBJECT_STORE_NAME],e).objectStore(o.OBJECT_STORE_NAME))}catch(e){r(e)}},i.onerror=function(e){r(e.target.error)}})):Promise.reject(new rc(ic.FAIL_TO_GET_STORAGE_SERVICE,{message:"Your environment doesn't support a stable version of IndexedDB."},"storage"))},e.prototype.get=function(e){if(!e)return Promise.reject(new Error("get key is null"));var t=this;return this.initIndexedDb("readonly").then((function(n){return new Promise((function(r,i){try{var o=n.get(e);o.onsuccess=function(){var e=o.result;r(null!=e&&"value"in e?t.agcCryptImpl.decrypt(e.value):e)},o.onerror=function(e){i(e.target.error)}}catch(e){i(e)}}))})).catch((function(e){return Promise.reject(e)}))},e.prototype.set=function(e,t){if(!e||!t)return Promise.reject(new Error("set key or value is null"));var n={},r={};n[this.KEY_PATH]=e,r.value=this.agcCryptImpl.encrypt(t);var i=Object.assign(r,n);return this.initIndexedDb("readwrite").then((function(t){try{var n=t.get(e);return n.onsuccess=function(){var e=n.result,r=null!=e&&"value"in e?t.put(i):t.add(i);r.onsuccess=function(){return Promise.resolve()},r.onerror=function(e){return Promise.reject(e.target.error)}},n.onerror=function(e){return Promise.reject(e.target.error)},Promise.resolve()}catch(e){return Promise.reject(e)}})).catch((function(e){return Promise.reject(e)}))},e.prototype.remove=function(e){return e?this.initIndexedDb("readwrite").then((function(t){try{var n=t.delete(e);return n.onsuccess=function(){return Promise.resolve()},n.onerror=function(e){return Promise.reject(e.target.error)},Promise.resolve()}catch(e){return Promise.reject(e)}})).catch((function(e){return Promise.reject(e)})):Promise.reject(new Error("remove key is null"))},e.indexedDBInsMap=new Map,e}(),fc=function(){function e(){this.agcCryptImpl=new uc}return e.getInstance=function(t,n){if(!e.isSessionStorageAvailable())throw tc.createLogger("AGCStorageService").error("Your environment doesn't support a stable version of sessionStorage."),new rc(ic.FAIL_TO_GET_STORAGE_SERVICE,{message:"Your environment doesn't support a stable version of sessionStorage."});!e.sessionMap.has(t)&&e.sessionMap.get(t)||e.sessionMap.set(t,new e);var r=e.sessionMap.get(t);return r.agcCryptImpl.setEncryptImp(n),r},e.isSessionStorageAvailable=function(){try{return sessionStorage.setItem("agctestKey","testValue"),sessionStorage.removeItem("agctestKey"),!0}catch(e){return!1}},e.prototype.get=function(e){if(!e)return Promise.reject(new Error("key is null"));try{var t=sessionStorage.getItem(e);return Promise.resolve(""===t?null:this.agcCryptImpl.decrypt(t))}catch(e){return Promise.reject(e)}},e.prototype.set=function(e,t){if(!e||!t)return Promise.reject(new Error("key or value is null"));try{return sessionStorage.setItem(e,this.agcCryptImpl.encrypt(t)),Promise.resolve()}catch(e){return Promise.reject(e)}},e.prototype.remove=function(e){if(!e)return Promise.reject(new Error("key is null"));try{return sessionStorage.removeItem(e),Promise.resolve()}catch(e){return Promise.reject(e)}},e.sessionMap=new Map,e}(),pc=function(){function e(){this.agcCryptImpl=new uc}return e.getInstance=function(t,n){e.MiniProgramStorageMap.has(t)&&e.MiniProgramStorageMap.get(t)||e.MiniProgramStorageMap.set(t,new e);var r=e.MiniProgramStorageMap.get(t);return r.agcCryptImpl.setEncryptImp(n),r},e.prototype.get=function(e){var t=this;return new Promise((function(n,r){try{wx.getStorage({key:e,success:function(e){n(e?t.agcCryptImpl.decrypt(e.data):void 0)},fail:function(e){n(void 0)}})}catch(e){r(e)}}))},e.prototype.set=function(e,t){var n=this;return new Promise((function(r,i){try{wx.setStorage({key:e,data:n.agcCryptImpl.encrypt(t),success:function(){r()},fail:function(e){i(e)}})}catch(e){i(e)}}))},e.prototype.remove=function(e){return new Promise((function(t,n){try{wx.removeStorage({key:e,success:function(){t()},fail:function(e){n(e)}})}catch(e){n(e)}}))},e.MiniProgramStorageMap=new Map,e}(),hc="undefined"==typeof window&&"object"==typeof wx,dc=function(){function e(e){this.name=n,e&&(this.name=e)}return e.prototype.getStorageInstance=function(e,t){var n;if(hc)return pc.getInstance(this.name,t);switch(e){case 2:n=cc.getInstance(this.name,t);break;case 0:n=lc.getInstance(this.name,t);break;case 1:n=fc.getInstance(this.name,t);break;default:n=cc.getInstance(this.name,t)}return n},e.prototype.createPersistentStorage=function(){return hc?pc.getInstance(this.name):lc.getInstance(this.name)},e.prototype.createTemporaryStorage=function(){return hc?pc.getInstance(this.name):fc.getInstance(this.name)},e.prototype.createMemoryStorage=function(){return hc?pc.getInstance(this.name):cc.getInstance(this.name)},e}();t.registerInternalService({name:"AGCStorageService",serviceFactory:function(e){return new dc(e.name())}});var gc=function(e,t){return function(){for(var n=new Array(arguments.length),r=0;r<n.length;r++)n[r]=arguments[r];return e.apply(t,n)}},vc=Object.prototype.toString;function mc(e){return"[object Array]"===vc.call(e)}function Ec(e){return void 0===e}function yc(e){return null!==e&&"object"==typeof e}function Sc(e){if("[object Object]"!==vc.call(e))return!1;var t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}function Tc(e){return"[object Function]"===vc.call(e)}function wc(e,t){if(null!=e)if("object"!=typeof e&&(e=[e]),mc(e))for(var n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.call(null,e[i],i,e)}var Cc={isArray:mc,isArrayBuffer:function(e){return"[object ArrayBuffer]"===vc.call(e)},isBuffer:function(e){return null!==e&&!Ec(e)&&null!==e.constructor&&!Ec(e.constructor)&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)},isFormData:function(e){return"undefined"!=typeof FormData&&e instanceof FormData},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&e.buffer instanceof ArrayBuffer},isString:function(e){return"string"==typeof e},isNumber:function(e){return"number"==typeof e},isObject:yc,isPlainObject:Sc,isUndefined:Ec,isDate:function(e){return"[object Date]"===vc.call(e)},isFile:function(e){return"[object File]"===vc.call(e)},isBlob:function(e){return"[object Blob]"===vc.call(e)},isFunction:Tc,isStream:function(e){return yc(e)&&Tc(e.pipe)},isURLSearchParams:function(e){return"undefined"!=typeof URLSearchParams&&e instanceof URLSearchParams},isStandardBrowserEnv:function(){return("undefined"==typeof navigator||"ReactNative"!==navigator.product&&"NativeScript"!==navigator.product&&"NS"!==navigator.product)&&("undefined"!=typeof window&&"undefined"!=typeof document)},forEach:wc,merge:function e(){var t={};function n(n,r){Sc(t[r])&&Sc(n)?t[r]=e(t[r],n):Sc(n)?t[r]=e({},n):mc(n)?t[r]=n.slice():t[r]=n}for(var r=0,i=arguments.length;r<i;r++)wc(arguments[r],n);return t},extend:function(e,t,n){return wc(t,(function(t,r){e[r]=n&&"function"==typeof t?gc(t,n):t})),e},trim:function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")},stripBOM:function(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),e}};function Ac(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}var bc=function(e,t,n){if(!t)return e;var r;if(n)r=n(t);else if(Cc.isURLSearchParams(t))r=t.toString();else{var i=[];Cc.forEach(t,(function(e,t){null!=e&&(Cc.isArray(e)?t+="[]":e=[e],Cc.forEach(e,(function(e){Cc.isDate(e)?e=e.toISOString():Cc.isObject(e)&&(e=JSON.stringify(e)),i.push(Ac(t)+"="+Ac(e))})))})),r=i.join("&")}if(r){var o=e.indexOf("#");-1!==o&&(e=e.slice(0,o)),e+=(-1===e.indexOf("?")?"?":"&")+r}return e};function Ic(){this.handlers=[]}Ic.prototype.use=function(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1},Ic.prototype.eject=function(e){this.handlers[e]&&(this.handlers[e]=null)},Ic.prototype.forEach=function(e){Cc.forEach(this.handlers,(function(t){null!==t&&e(t)}))};var Pc=Ic,Oc=function(e,t){Cc.forEach(e,(function(n,r){r!==t&&r.toUpperCase()===t.toUpperCase()&&(e[t]=n,delete e[r])}))},_c=function(e,t,n,r,i){return e.config=t,n&&(e.code=n),e.request=r,e.response=i,e.isAxiosError=!0,e.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code}},e},Nc=function(e,t,n,r,i){var o=new Error(e);return _c(o,t,n,r,i)},Rc=Cc.isStandardBrowserEnv()?{write:function(e,t,n,r,i,o){var a=[];a.push(e+"="+encodeURIComponent(t)),Cc.isNumber(n)&&a.push("expires="+new Date(n).toGMTString()),Cc.isString(r)&&a.push("path="+r),Cc.isString(i)&&a.push("domain="+i),!0===o&&a.push("secure"),document.cookie=a.join("; ")},read:function(e){var t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove:function(e){this.write(e,"",Date.now()-864e5)}}:{write:function(){},read:function(){return null},remove:function(){}},Lc=["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"],$c=Cc.isStandardBrowserEnv()?function(){var e,t=/(msie|trident)/i.test(navigator.userAgent),n=document.createElement("a");function r(e){var r=e;return t&&(n.setAttribute("href",r),r=n.href),n.setAttribute("href",r),{href:n.href,protocol:n.protocol?n.protocol.replace(/:$/,""):"",host:n.host,search:n.search?n.search.replace(/^\?/,""):"",hash:n.hash?n.hash.replace(/^#/,""):"",hostname:n.hostname,port:n.port,pathname:"/"===n.pathname.charAt(0)?n.pathname:"/"+n.pathname}}return e=r(window.location.href),function(t){var n=Cc.isString(t)?r(t):t;return n.protocol===e.protocol&&n.host===e.host}}():function(){return!0},Dc=function(e){return new Promise((function(t,n){var r=e.data,i=e.headers,o=e.responseType;Cc.isFormData(r)&&delete i["Content-Type"];var a=new XMLHttpRequest;if(e.auth){var s=e.auth.username||"",c=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";i.Authorization="Basic "+btoa(s+":"+c)}var u,l,f=(u=e.baseURL,l=e.url,u&&!/^([a-z][a-z\d\+\-\.]*:)?\/\//i.test(l)?function(e,t){return t?e.replace(/\/+$/,"")+"/"+t.replace(/^\/+/,""):e}(u,l):l);function p(){if(a){var r="getAllResponseHeaders"in a?function(e){var t,n,r,i={};return e?(Cc.forEach(e.split("\n"),(function(e){if(r=e.indexOf(":"),t=Cc.trim(e.substr(0,r)).toLowerCase(),n=Cc.trim(e.substr(r+1)),t){if(i[t]&&Lc.indexOf(t)>=0)return;i[t]="set-cookie"===t?(i[t]?i[t]:[]).concat([n]):i[t]?i[t]+", "+n:n}})),i):i}(a.getAllResponseHeaders()):null,i={data:o&&"text"!==o&&"json"!==o?a.response:a.responseText,status:a.status,statusText:a.statusText,headers:r,config:e,request:a};!function(e,t,n){var r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(Nc("Request failed with status code "+n.status,n.config,null,n.request,n)):e(n)}(t,n,i),a=null}}if(a.open(e.method.toUpperCase(),bc(f,e.params,e.paramsSerializer),!0),a.timeout=e.timeout,"onloadend"in a?a.onloadend=p:a.onreadystatechange=function(){a&&4===a.readyState&&(0!==a.status||a.responseURL&&0===a.responseURL.indexOf("file:"))&&setTimeout(p)},a.onabort=function(){a&&(n(Nc("Request aborted",e,"ECONNABORTED",a)),a=null)},a.onerror=function(){n(Nc("Network Error",e,null,a)),a=null},a.ontimeout=function(){var t="timeout of "+e.timeout+"ms exceeded";e.timeoutErrorMessage&&(t=e.timeoutErrorMessage),n(Nc(t,e,e.transitional&&e.transitional.clarifyTimeoutError?"ETIMEDOUT":"ECONNABORTED",a)),a=null},Cc.isStandardBrowserEnv()){var h=(e.withCredentials||$c(f))&&e.xsrfCookieName?Rc.read(e.xsrfCookieName):void 0;h&&(i[e.xsrfHeaderName]=h)}"setRequestHeader"in a&&Cc.forEach(i,(function(e,t){void 0===r&&"content-type"===t.toLowerCase()?delete i[t]:a.setRequestHeader(t,e)})),Cc.isUndefined(e.withCredentials)||(a.withCredentials=!!e.withCredentials),o&&"json"!==o&&(a.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&a.addEventListener("progress",e.onDownloadProgress),"function"==typeof e.onUploadProgress&&a.upload&&a.upload.addEventListener("progress",e.onUploadProgress),e.cancelToken&&e.cancelToken.promise.then((function(e){a&&(a.abort(),n(e),a=null)})),r||(r=null),a.send(r)}))},kc={"Content-Type":"application/x-www-form-urlencoded"};function Uc(e,t){!Cc.isUndefined(e)&&Cc.isUndefined(e["Content-Type"])&&(e["Content-Type"]=t)}var Mc,xc={transitional:{silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},adapter:(("undefined"!=typeof XMLHttpRequest||"undefined"!=typeof process&&"[object process]"===Object.prototype.toString.call(process))&&(Mc=Dc),Mc),transformRequest:[function(e,t){return Oc(t,"Accept"),Oc(t,"Content-Type"),Cc.isFormData(e)||Cc.isArrayBuffer(e)||Cc.isBuffer(e)||Cc.isStream(e)||Cc.isFile(e)||Cc.isBlob(e)?e:Cc.isArrayBufferView(e)?e.buffer:Cc.isURLSearchParams(e)?(Uc(t,"application/x-www-form-urlencoded;charset=utf-8"),e.toString()):Cc.isObject(e)||t&&"application/json"===t["Content-Type"]?(Uc(t,"application/json"),function(e,t,n){if(Cc.isString(e))try{return(t||JSON.parse)(e),Cc.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){var t=this.transitional,n=t&&t.silentJSONParsing,r=t&&t.forcedJSONParsing,i=!n&&"json"===this.responseType;if(i||r&&Cc.isString(e)&&e.length)try{return JSON.parse(e)}catch(e){if(i){if("SyntaxError"===e.name)throw _c(e,this,"E_JSON_PARSE");throw e}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,validateStatus:function(e){return e>=200&&e<300}};xc.headers={common:{Accept:"application/json, text/plain, */*"}},Cc.forEach(["delete","get","head"],(function(e){xc.headers[e]={}})),Cc.forEach(["post","put","patch"],(function(e){xc.headers[e]=Cc.merge(kc)}));var Vc=xc,Hc=function(e,t,n){var r=this||Vc;return Cc.forEach(n,(function(n){e=n.call(r,e,t)})),e},Fc=function(e){return!(!e||!e.__CANCEL__)};function jc(e){e.cancelToken&&e.cancelToken.throwIfRequested()}var Bc=function(e){return jc(e),e.headers=e.headers||{},e.data=Hc.call(e,e.data,e.headers,e.transformRequest),e.headers=Cc.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),Cc.forEach(["delete","get","head","post","put","patch","common"],(function(t){delete e.headers[t]})),(e.adapter||Vc.adapter)(e).then((function(t){return jc(e),t.data=Hc.call(e,t.data,t.headers,e.transformResponse),t}),(function(t){return Fc(t)||(jc(e),t&&t.response&&(t.response.data=Hc.call(e,t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)}))},Gc=function(e,t){t=t||{};var n={},r=["url","method","data"],i=["headers","auth","proxy","params"],o=["baseURL","transformRequest","transformResponse","paramsSerializer","timeout","timeoutMessage","withCredentials","adapter","responseType","xsrfCookieName","xsrfHeaderName","onUploadProgress","onDownloadProgress","decompress","maxContentLength","maxBodyLength","maxRedirects","transport","httpAgent","httpsAgent","cancelToken","socketPath","responseEncoding"],a=["validateStatus"];function s(e,t){return Cc.isPlainObject(e)&&Cc.isPlainObject(t)?Cc.merge(e,t):Cc.isPlainObject(t)?Cc.merge({},t):Cc.isArray(t)?t.slice():t}function c(r){Cc.isUndefined(t[r])?Cc.isUndefined(e[r])||(n[r]=s(void 0,e[r])):n[r]=s(e[r],t[r])}Cc.forEach(r,(function(e){Cc.isUndefined(t[e])||(n[e]=s(void 0,t[e]))})),Cc.forEach(i,c),Cc.forEach(o,(function(r){Cc.isUndefined(t[r])?Cc.isUndefined(e[r])||(n[r]=s(void 0,e[r])):n[r]=s(void 0,t[r])})),Cc.forEach(a,(function(r){r in t?n[r]=s(e[r],t[r]):r in e&&(n[r]=s(void 0,e[r]))}));var u=r.concat(i).concat(o).concat(a),l=Object.keys(e).concat(Object.keys(t)).filter((function(e){return-1===u.indexOf(e)}));return Cc.forEach(l,c),n},Wc="axios@0.21.4",Yc="sha512-ut5vewkiu8jjGBdqpM44XxjuCjq9LAKeHVmoVfHVzy8eHgxxq8SbAVQNovDA8mVi05kP0Ea/n/UzcSHcTJQfNg==",Kc={},qc={type:"range",registry:!0,raw:"axios@^0.21.1",name:"axios",escapedName:"axios",rawSpec:"^0.21.1",saveSpec:null,fetchSpec:"^0.21.1"},zc=["/@agconnect/network"],Jc="https://mirrors.tools.huawei.com/npm/axios/-/axios-0.21.4.tgz",Xc="c67b90dc0568e5c1cf2b0b858c43ba28e2eda575",Qc="/devcloud/ws/sGwpK/workspace/j_TIOJB8ON/HiAnalyticsSDK-JS/node_modules/@agconnect/network",Zc={name:"Matt Zabriskie"},eu={"./lib/adapters/http.js":"./lib/adapters/xhr.js"},tu={url:"https://github.com/axios/axios/issues"},nu=[{path:"./dist/axios.min.js",threshold:"5kB"}],ru={"follow-redirects":"^1.14.0"},iu="Promise based HTTP client for the browser and node.js",ou={coveralls:"^3.0.0","es6-promise":"^4.2.4",grunt:"^1.3.0","grunt-banner":"^0.6.0","grunt-cli":"^1.2.0","grunt-contrib-clean":"^1.1.0","grunt-contrib-watch":"^1.0.0","grunt-eslint":"^23.0.0","grunt-karma":"^4.0.0","grunt-mocha-test":"^0.13.3","grunt-ts":"^6.0.0-beta.19","grunt-webpack":"^4.0.2","istanbul-instrumenter-loader":"^1.0.0","jasmine-core":"^2.4.1",karma:"^6.3.2","karma-chrome-launcher":"^3.1.0","karma-firefox-launcher":"^2.1.0","karma-jasmine":"^1.1.1","karma-jasmine-ajax":"^0.1.13","karma-safari-launcher":"^1.0.0","karma-sauce-launcher":"^4.3.6","karma-sinon":"^1.0.5","karma-sourcemap-loader":"^0.3.8","karma-webpack":"^4.0.2","load-grunt-tasks":"^3.5.2",minimist:"^1.2.0",mocha:"^8.2.1",sinon:"^4.5.0","terser-webpack-plugin":"^4.2.3",typescript:"^4.0.5","url-search-params":"^0.10.0",webpack:"^4.44.2","webpack-dev-server":"^3.11.0"},au=["xhr","http","ajax","promise","node"],su={type:"git",url:"git+https://github.com/axios/axios.git"},cu={build:"NODE_ENV=production grunt build",coveralls:"cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js",examples:"node ./examples/server.js",fix:"eslint --fix lib/**/*.js",postversion:"git push && git push --tags",preversion:"npm test",start:"node ./sandbox/server.js",test:"grunt test",version:"npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json"},uu="dist/axios.min.js",lu={_from:"axios@^0.21.1",_id:Wc,_inBundle:!1,_integrity:Yc,_location:"/axios",_phantomChildren:Kc,_requested:qc,_requiredBy:zc,_resolved:Jc,_shasum:Xc,_spec:"axios@^0.21.1",_where:Qc,author:Zc,browser:eu,bugs:tu,bundleDependencies:!1,bundlesize:nu,dependencies:ru,deprecated:!1,description:iu,devDependencies:ou,homepage:"https://axios-http.com",jsdelivr:"dist/axios.min.js",keywords:au,license:"MIT",main:"index.js",name:"axios",repository:su,scripts:cu,typings:"./index.d.ts",unpkg:uu,version:"0.21.4"},fu=function(e){return e&&e.default||e}(Object.freeze({__proto__:null,_from:"axios@^0.21.1",_id:Wc,_inBundle:!1,_integrity:Yc,_location:"/axios",_phantomChildren:Kc,_requested:qc,_requiredBy:zc,_resolved:Jc,_shasum:Xc,_spec:"axios@^0.21.1",_where:Qc,author:Zc,browser:eu,bugs:tu,bundleDependencies:!1,bundlesize:nu,dependencies:ru,deprecated:!1,description:iu,devDependencies:ou,homepage:"https://axios-http.com",jsdelivr:"dist/axios.min.js",keywords:au,license:"MIT",main:"index.js",name:"axios",repository:su,scripts:cu,typings:"./index.d.ts",unpkg:uu,version:"0.21.4",default:lu})),pu={};["object","boolean","number","function","string","symbol"].forEach((function(e,t){pu[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));var hu={},du=fu.version.split(".");function gu(e,t){for(var n=t?t.split("."):du,r=e.split("."),i=0;i<3;i++){if(n[i]>r[i])return!0;if(n[i]<r[i])return!1}return!1}pu.transitional=function(e,t,n){var r=t&&gu(t);function i(e,t){return"[Axios v"+fu.version+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return function(n,o,a){if(!1===e)throw new Error(i(o," has been removed in "+t));return r&&!hu[o]&&(hu[o]=!0,console.warn(i(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,a)}};var vu={isOlderVersion:gu,assertOptions:function(e,t,n){if("object"!=typeof e)throw new TypeError("options must be an object");for(var r=Object.keys(e),i=r.length;i-- >0;){var o=r[i],a=t[o];if(a){var s=e[o],c=void 0===s||a(s,o,e);if(!0!==c)throw new TypeError("option "+o+" must be "+c)}else if(!0!==n)throw Error("Unknown option "+o)}},validators:pu},mu=vu.validators;function Eu(e){this.defaults=e,this.interceptors={request:new Pc,response:new Pc}}Eu.prototype.request=function(e){"string"==typeof e?(e=arguments[1]||{}).url=arguments[0]:e=e||{},(e=Gc(this.defaults,e)).method?e.method=e.method.toLowerCase():this.defaults.method?e.method=this.defaults.method.toLowerCase():e.method="get";var t=e.transitional;void 0!==t&&vu.assertOptions(t,{silentJSONParsing:mu.transitional(mu.boolean,"1.0.0"),forcedJSONParsing:mu.transitional(mu.boolean,"1.0.0"),clarifyTimeoutError:mu.transitional(mu.boolean,"1.0.0")},!1);var n=[],r=!0;this.interceptors.request.forEach((function(t){"function"==typeof t.runWhen&&!1===t.runWhen(e)||(r=r&&t.synchronous,n.unshift(t.fulfilled,t.rejected))}));var i,o=[];if(this.interceptors.response.forEach((function(e){o.push(e.fulfilled,e.rejected)})),!r){var a=[Bc,void 0];for(Array.prototype.unshift.apply(a,n),a=a.concat(o),i=Promise.resolve(e);a.length;)i=i.then(a.shift(),a.shift());return i}for(var s=e;n.length;){var c=n.shift(),u=n.shift();try{s=c(s)}catch(e){u(e);break}}try{i=Bc(s)}catch(e){return Promise.reject(e)}for(;o.length;)i=i.then(o.shift(),o.shift());return i},Eu.prototype.getUri=function(e){return e=Gc(this.defaults,e),bc(e.url,e.params,e.paramsSerializer).replace(/^\?/,"")},Cc.forEach(["delete","get","head","options"],(function(e){Eu.prototype[e]=function(t,n){return this.request(Gc(n||{},{method:e,url:t,data:(n||{}).data}))}})),Cc.forEach(["post","put","patch"],(function(e){Eu.prototype[e]=function(t,n,r){return this.request(Gc(r||{},{method:e,url:t,data:n}))}}));var yu=Eu;function Su(e){this.message=e}Su.prototype.toString=function(){return"Cancel"+(this.message?": "+this.message:"")},Su.prototype.__CANCEL__=!0;var Tu=Su;function wu(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");var t;this.promise=new Promise((function(e){t=e}));var n=this;e((function(e){n.reason||(n.reason=new Tu(e),t(n.reason))}))}wu.prototype.throwIfRequested=function(){if(this.reason)throw this.reason},wu.source=function(){var e;return{token:new wu((function(t){e=t})),cancel:e}};var Cu=wu;function Au(e){var t=new yu(e),n=gc(yu.prototype.request,t);return Cc.extend(n,yu.prototype,t),Cc.extend(n,t),n}var bu=Au(Vc);bu.Axios=yu,bu.create=function(e){return Au(Gc(bu.defaults,e))},bu.Cancel=Tu,bu.CancelToken=Cu,bu.isCancel=Fc,bu.all=function(e){return Promise.all(e)},bu.spread=function(e){return function(t){return e.apply(null,t)}},bu.isAxiosError=function(e){return"object"==typeof e&&!0===e.isAxiosError};var Iu=bu,Pu=bu;Iu.default=Pu;var Ou=Iu,_u=c((function(e,t){!function(n,r){var i="model",o="name",a="type",s="vendor",c="version",u="mobile",l="tablet",f="smarttv",p=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},h=function(e,t){return"string"==typeof e&&-1!==d(t).indexOf(d(e))},d=function(e){return e.toLowerCase()},g=function(e,t){if("string"==typeof e)return e=e.replace(/^\s\s*/,"").replace(/\s\s*$/,""),void 0===t?e:e.substring(0,255)},v=function(e,t){for(var n,r,i,o,a,s,c=0;c<t.length&&!a;){var u=t[c],l=t[c+1];for(n=r=0;n<u.length&&!a;)if(a=u[n++].exec(e))for(i=0;i<l.length;i++)s=a[++r],"object"==typeof(o=l[i])&&o.length>0?2===o.length?"function"==typeof o[1]?this[o[0]]=o[1].call(this,s):this[o[0]]=o[1]:3===o.length?"function"!=typeof o[1]||o[1].exec&&o[1].test?this[o[0]]=s?s.replace(o[1],o[2]):void 0:this[o[0]]=s?o[1].call(this,s,o[2]):void 0:4===o.length&&(this[o[0]]=s?o[3].call(this,s.replace(o[1],o[2])):void 0):this[o]=s||void 0;c+=2}},m=function(e,t){for(var n in t)if("object"==typeof t[n]&&t[n].length>0){for(var r=0;r<t[n].length;r++)if(h(t[n][r],e))return"?"===n?void 0:n}else if(h(t[n],e))return"?"===n?void 0:n;return e},E={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},y={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[c,[o,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[c,[o,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[o,c],[/opios[\/ ]+([\w\.]+)/i],[c,[o,"Opera Mini"]],[/\bopr\/([\w\.]+)/i],[c,[o,"Opera"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([-\w\.]+)/i,/(weibo)__([\d\.]+)/i],[o,c],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[c,[o,"UCBrowser"]],[/\bqbcore\/([\w\.]+)/i],[c,[o,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[c,[o,"WeChat"]],[/konqueror\/([\w\.]+)/i],[c,[o,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[c,[o,"IE"]],[/yabrowser\/([\w\.]+)/i],[c,[o,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[o,/(.+)/,"$1 Secure Browser"],c],[/\bfocus\/([\w\.]+)/i],[c,[o,"Firefox Focus"]],[/\bopt\/([\w\.]+)/i],[c,[o,"Opera Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[c,[o,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[c,[o,"Dolphin"]],[/coast\/([\w\.]+)/i],[c,[o,"Opera Coast"]],[/miuibrowser\/([\w\.]+)/i],[c,[o,"MIUI Browser"]],[/fxios\/([-\w\.]+)/i],[c,[o,"Firefox"]],[/\bqihu|(qi?ho?o?|360)browser/i],[[o,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[o,/(.+)/,"$1 Browser"],c],[/(comodo_dragon)\/([\w\.]+)/i],[[o,/_/g," "],c],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[o,c],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i],[o],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[o,"Facebook"],c],[/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[o,c],[/\bgsa\/([\w\.]+) .*safari\//i],[c,[o,"GSA"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[c,[o,"Chrome Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[o,"Chrome WebView"],c],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[c,[o,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[o,c],[/version\/([\w\.]+) .*mobile\/\w+ (safari)/i],[c,[o,"Mobile Safari"]],[/version\/([\w\.]+) .*(mobile ?safari|safari)/i],[c,o],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[o,[c,m,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[o,c],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[o,"Netscape"],c],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[c,[o,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[o,c]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[["architecture","amd64"]],[/(ia32(?=;))/i],[["architecture",d]],[/((?:i[346]|x)86)[;\)]/i],[["architecture","ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[["architecture","arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[["architecture","armhf"]],[/windows (ce|mobile); ppc;/i],[["architecture","arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[["architecture",/ower/,"",d]],[/(sun4\w)[;\)]/i],[["architecture","sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[["architecture",d]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[i,[s,"Samsung"],[a,l]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[i,[s,"Samsung"],[a,u]],[/\((ip(?:hone|od)[\w ]*);/i],[i,[s,"Apple"],[a,u]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[i,[s,"Apple"],[a,l]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[i,[s,"Huawei"],[a,l]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}-[atu]?[ln][01259x][012359][an]?)\b(?!.+d\/s)/i],[i,[s,"Huawei"],[a,u]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[i,/_/g," "],[s,"Xiaomi"],[a,u]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[i,/_/g," "],[s,"Xiaomi"],[a,l]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[i,[s,"OPPO"],[a,u]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[i,[s,"Vivo"],[a,u]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[i,[s,"Realme"],[a,u]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[i,[s,"Motorola"],[a,u]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[i,[s,"Motorola"],[a,l]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[i,[s,"LG"],[a,l]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[i,[s,"LG"],[a,u]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[i,[s,"Lenovo"],[a,l]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[i,/_/g," "],[s,"Nokia"],[a,u]],[/(pixel c)\b/i],[i,[s,"Google"],[a,l]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[i,[s,"Google"],[a,u]],[/droid.+ ([c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[i,[s,"Sony"],[a,u]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[i,"Xperia Tablet"],[s,"Sony"],[a,l]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[i,[s,"OnePlus"],[a,u]],[/(alexa)webm/i,/(kf[a-z]{2}wi)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[i,[s,"Amazon"],[a,l]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[i,/(.+)/g,"Fire Phone $1"],[s,"Amazon"],[a,u]],[/(playbook);[-\w\),; ]+(rim)/i],[i,s,[a,l]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[i,[s,"BlackBerry"],[a,u]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[i,[s,"ASUS"],[a,l]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[i,[s,"ASUS"],[a,u]],[/(nexus 9)/i],[i,[s,"HTC"],[a,l]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic|sony)[-_ ]?([-\w]*)/i],[s,[i,/_/g," "],[a,u]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[i,[s,"Acer"],[a,l]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[i,[s,"Meizu"],[a,u]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[i,[s,"Sharp"],[a,u]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[s,i,[a,u]],[/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[s,i,[a,l]],[/(surface duo)/i],[i,[s,"Microsoft"],[a,l]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[i,[s,"Fairphone"],[a,u]],[/(u304aa)/i],[i,[s,"AT&T"],[a,u]],[/\bsie-(\w*)/i],[i,[s,"Siemens"],[a,u]],[/\b(rct\w+) b/i],[i,[s,"RCA"],[a,l]],[/\b(venue[\d ]{2,7}) b/i],[i,[s,"Dell"],[a,l]],[/\b(q(?:mv|ta)\w+) b/i],[i,[s,"Verizon"],[a,l]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[i,[s,"Barnes & Noble"],[a,l]],[/\b(tm\d{3}\w+) b/i],[i,[s,"NuVision"],[a,l]],[/\b(k88) b/i],[i,[s,"ZTE"],[a,l]],[/\b(nx\d{3}j) b/i],[i,[s,"ZTE"],[a,u]],[/\b(gen\d{3}) b.+49h/i],[i,[s,"Swiss"],[a,u]],[/\b(zur\d{3}) b/i],[i,[s,"Swiss"],[a,l]],[/\b((zeki)?tb.*\b) b/i],[i,[s,"Zeki"],[a,l]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[s,"Dragon Touch"],i,[a,l]],[/\b(ns-?\w{0,9}) b/i],[i,[s,"Insignia"],[a,l]],[/\b((nxa|next)-?\w{0,9}) b/i],[i,[s,"NextBook"],[a,l]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[s,"Voice"],i,[a,u]],[/\b(lvtel\-)?(v1[12]) b/i],[[s,"LvTel"],i,[a,u]],[/\b(ph-1) /i],[i,[s,"Essential"],[a,u]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[i,[s,"Envizen"],[a,l]],[/\b(trio[-\w\. ]+) b/i],[i,[s,"MachSpeed"],[a,l]],[/\btu_(1491) b/i],[i,[s,"Rotor"],[a,l]],[/(shield[\w ]+) b/i],[i,[s,"Nvidia"],[a,l]],[/(sprint) (\w+)/i],[s,i,[a,u]],[/(kin\.[onetw]{3})/i],[[i,/\./g," "],[s,"Microsoft"],[a,u]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[i,[s,"Zebra"],[a,l]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[i,[s,"Zebra"],[a,u]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[s,i,[a,"console"]],[/droid.+; (shield) bui/i],[i,[s,"Nvidia"],[a,"console"]],[/(playstation [345portablevi]+)/i],[i,[s,"Sony"],[a,"console"]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[i,[s,"Microsoft"],[a,"console"]],[/smart-tv.+(samsung)/i],[s,[a,f]],[/hbbtv.+maple;(\d+)/i],[[i,/^/,"SmartTV"],[s,"Samsung"],[a,f]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[s,"LG"],[a,f]],[/(apple) ?tv/i],[s,[i,"Apple TV"],[a,f]],[/crkey/i],[[i,"Chromecast"],[s,"Google"],[a,f]],[/droid.+aft(\w)( bui|\))/i],[i,[s,"Amazon"],[a,f]],[/\(dtv[\);].+(aquos)/i],[i,[s,"Sharp"],[a,f]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w ]*; *(\w[^;]*);([^;]*)/i],[[s,g],[i,g],[a,f]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[a,f]],[/((pebble))app/i],[s,i,[a,"wearable"]],[/droid.+; (glass) \d/i],[i,[s,"Google"],[a,"wearable"]],[/droid.+; (wt63?0{2,3})\)/i],[i,[s,"Zebra"],[a,"wearable"]],[/(quest( 2)?)/i],[i,[s,"Facebook"],[a,"wearable"]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[s,[a,"embedded"]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[i,[a,u]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[i,[a,l]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[a,l]],[/(phone|mobile(?:[;\/]| safari)|pda(?=.+windows ce))/i],[[a,u]],[/(android[-\w\. ]{0,9});.+buil/i],[i,[s,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[c,[o,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[c,[o,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i],[o,c],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[c,o]],os:[[/microsoft (windows) (vista|xp)/i],[o,c],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[o,[c,m,E]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[o,"Windows"],[c,m,E]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/cfnetwork\/.+darwin/i],[[c,/_/g,"."],[o,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[o,"Mac OS"],[c,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86)/i],[c,o],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[o,c],[/\(bb(10);/i],[c,[o,"BlackBerry"]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[c,[o,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[c,[o,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[c,[o,"webOS"]],[/crkey\/([\d\.]+)/i],[c,[o,"Chromecast"]],[/(cros) [\w]+ ([\w\.]+\w)/i],[[o,"Chromium OS"],c],[/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[o,c],[/(sunos) ?([\w\.\d]*)/i],[[o,"Solaris"],c],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux)/i,/(unix) ?([\w\.]*)/i],[o,c]]},S=function(e,t){if("object"==typeof e&&(t=e,e=void 0),!(this instanceof S))return new S(e,t).getResult();var r=e||(void 0!==n&&n.navigator&&n.navigator.userAgent?n.navigator.userAgent:""),i=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(y,t):y;return this.getBrowser=function(){var e={name:void 0,version:void 0};return v.call(e,r,i.browser),e.major=function(e){return"string"==typeof e?e.replace(/[^\d\.]/g,"").split(".")[0]:void 0}(e.version),e},this.getCPU=function(){var e={architecture:void 0};return v.call(e,r,i.cpu),e},this.getDevice=function(){var e={vendor:void 0,model:void 0,type:void 0};return v.call(e,r,i.device),e},this.getEngine=function(){var e={name:void 0,version:void 0};return v.call(e,r,i.engine),e},this.getOS=function(){var e={name:void 0,version:void 0};return v.call(e,r,i.os),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return r},this.setUA=function(e){return r="string"==typeof e&&e.length>255?g(e,255):e,this},this.setUA(r),this};S.VERSION="1.0.2",S.BROWSER=p([o,c,"major"]),S.CPU=p(["architecture"]),S.DEVICE=p([i,s,a,"console",u,f,l,"wearable","embedded"]),S.ENGINE=S.OS=p([o,c]),e.exports&&(t=e.exports=S),t.UAParser=S;var T=void 0!==n&&(n.jQuery||n.Zepto);if(T&&!T.ua){var w=new S;T.ua=w.getResult(),T.ua.get=function(){return w.getUA()},T.ua.set=function(e){w.setUA(e);var t=w.getResult();for(var n in t)T.ua[n]=t[n]}}}("object"==typeof window?window:s)})).UAParser,Nu=function(e,t){return(Nu=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function Ru(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}Nu(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var Lu=function(e){function t(t){var n=e.call(this)||this;return n.CancelToken=Ou.CancelToken,Ou.defaults.timeout=5e3,null==t&&null==t||(Ou.defaults.adapter=t),n}return Ru(t,e),t.prototype.getAxiosIns=function(){return Ou},t.prototype.sendRequest=function(e,t,n,r,i){return function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function s(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}(this,void 0,void 0,(function(){var o,a;return function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!((i=(i=a.trys).length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}(this,(function(s){switch(s.label){case 0:return this.checkParam(t)?(o={url:t,method:e,transformResponse:null==i?void 0:i.transformResponse,headers:r,timeout:null==i?void 0:i.timeout,responseType:null==i?void 0:i.responseType,onUploadProgress:null==i?void 0:i.onUploadProgress,onDownloadProgress:null==i?void 0:i.onDownloadProgress,validateStatus:null==i?void 0:i.validateStatus,cancelToken:null==i?void 0:i.cancelToken},"PUT"!=e&&"POST"!=e||(o.data=n),"GET"!=e&&"DELETE"!=e||(o.params=n),i&&i.throwOriginException?[2,Ou.request(o)]:[4,Ou.request(o).catch((function(e){return Promise.reject(new rc(ic.NETWORK_REQUEST_ERROR,e,"network"))}))]):[2,Promise.reject(new rc(ic.NETWORK_REQUEST_ERROR,{message:"url is illegal"},"network"))];case 1:return a=s.sent(),[2,Promise.resolve(a)]}}))}))},t}(function(){function e(){this.CancelToken=void 0}return e.prototype.post=function(e,t,n,r){return this.sendRequest("POST",e,t,n,r)},e.prototype.get=function(e,t,n,r){return this.sendRequest("GET",e,t,n,r)},e.prototype.delete=function(e,t,n,r){return this.sendRequest("DELETE",e,t,n,r)},e.prototype.put=function(e,t,n,r){return this.sendRequest("PUT",e,t,n,r)},e.prototype.checkParam=function(e){return!(!e||e.match(/\s/g)||!e.match(/^(ht)tp(s?)\:\/\/[0-9a-zA-Z]([-.\w]*[0-9a-zA-Z])*(:(0-9)*)*(\/?)([a-zA-Z0-9\-\.\?\,\'\/\\\+&amp;%\$#_]*)?/))},e}()),$u=function(){function e(){}return e.prototype.getPlatform=function(){return""},e.prototype.getPlatformVersion=function(){return""},e.prototype.getPackageName=function(){return""},e.prototype.getAppVersion=function(){return""},e.prototype.getLanguage=function(){return""},e.prototype.getScript=function(){return""},e.prototype.getCountry=function(){return""},e.prototype.getSystemInfo=function(){return{name:"",version:""}},e.prototype.getBrowsersInfo=function(){return{name:"",version:""}},e}(),Du=function(e){function n(n){var r=e.call(this)||this;return r.logger=tc.createLogger("AGCPlatformInfoService"),r.instance=n||t.instance(),r}return Ru(n,e),n.prototype.getPlatform=function(){return"JS-SDK"},n.prototype.getAppVersion=function(){return this.instance.getAppVersion()},n.prototype.getLanguage=function(){var e;return(e=navigator.languages&&navigator.languages[0]?navigator.languages[0]:navigator.language).toLowerCase().length>2?e.toLowerCase().substr(0,2):""},n.prototype.getSystemInfo=function(){var e="",t="";try{var n=navigator.userAgent,r=new _u;r.setUA(n);var i=r.getResult();e=i.os.name,t=i.os.version}catch(e){this.logger.warn("get system info failed")}return"Mac OS"===e&&(e="Macintosh"),{name:e,version:t}},n.prototype.getBrowsersInfo=function(){var e="",t="";try{var n=navigator.userAgent,r=new _u;r.setUA(n);var i=r.getResult();e=i.browser.name,t=i.browser.version}catch(e){this.logger.warn("get browser info failed")}return{name:e,version:t}},n}($u),ku=function(){function e(){this.logger=tc.createLogger("AGCWebSocketService")}return e.prototype.getReadyState=function(){return this.websocket?Number(this.websocket.readyState):null},e.prototype.send=function(e,t,n){try{if(this.websocket){if(this.websocket.send(e),t)return t()}else if(n)return n()}catch(e){if(this.logger.error(e),n)return n()}},e.prototype.close=function(e,t,n,r){try{if(this.websocket){if(this.websocket.close(e,t),n)return n()}else if(r)return r()}catch(e){if(this.logger.error(e),r)return r()}},e.prototype.onOpen=function(e){if(!this.websocket)throw new rc(ic.WEBSOCKET_ERROR,{message:"webSocket connect fail"},"network");this.websocket.onopen=e},e.prototype.onMessage=function(e){if(!this.websocket)throw new rc(ic.WEBSOCKET_ERROR,{message:"webSocket connect fail"},"network");this.websocket.onmessage=function(t){e&&e(t.data)}},e.prototype.onClose=function(e){if(!this.websocket)throw new rc(ic.WEBSOCKET_ERROR,{message:"webSocket connect fail"},"network");this.websocket.onclose=function(t){e&&e(t.code,t.reason,t.wasClean)}},e.prototype.onError=function(e){if(!this.websocket)throw new rc(ic.WEBSOCKET_ERROR,{message:"webSocket connect fail"},"network");this.websocket.onerror=function(t){e&&e(t)}},e}(),Uu=function(e){function t(){var t=e.call(this)||this;if(t.WebSocketImpl="MozWebSocket"in window?MozWebSocket:WebSocket,!t.WebSocketImpl)throw new rc(ic.WEBSOCKET_NOT_SUPPORT,{message:"Sorry, your browser does not support WebSockets."},"network");return t}return Ru(t,e),t.prototype.connect=function(e,t,n){if(this.websocket&&3!=this.websocket.readyState&&(this.websocket.close(),this.websocket=null),this.websocket=new this.WebSocketImpl(e,n),!this.websocket)throw new rc(ic.WEBSOCKET_ERROR,{message:"webSocket create fail"},"network");return Promise.resolve()},t}(ku);function Mu(e){return new Promise((function(t,n){var r,i=e.method&&e.method.toUpperCase()||"GET",o={method:i,header:e.headers,url:e.url,dataType:e.transformResponse?"text":"json",success:function(r){var i=r.data;e.transformResponse&&(i=e.transformResponse[0](r.data));var o={data:i,status:r.statusCode,statusText:r.errMsg,headers:r.header,config:e};e.validateStatus&&e.validateStatus(r.statusCode)||!e.validateStatus&&200==r.statusCode?t(o):n({message:r.errMsg,code:r.statusCode,response:o})},fail:function(e){n({message:e.errMsg,response:{}})},complete:function(){r=void 0}};e.timeout?o.timeout=e.timeout:o.timeout=5e3,e.responseType&&(o.responseType=e.responseType),e.cancelToken&&e.cancelToken.promise.then((function(e){r&&(r.abort&&r.abort(),n(e),r=void 0)})),"PUT"==i||"POST"==i?o.data=e.data:"GET"!=i&&"DELETE"!=i||(o.data=e.params),r=wx.request(o)}))}var xu=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return Ru(t,e),t.prototype.connect=function(e,t,n){return this.websocket&&3!=this.websocket.readyState&&(this.websocket.close(),this.websocket=null),this.websocket=wx.connectSocket({url:e,header:t,protocols:n}),this.websocket?Promise.resolve():(this.logger.error("webSocket create fail"),Promise.reject(new rc(ic.WEBSOCKET_ERROR,{message:"webSocket create failed"},"network")))},t.prototype.send=function(e,t,n){this.websocket?this.websocket.send({data:e,success:t,fail:n}):n&&n()},t.prototype.close=function(e,t,n,r){this.websocket?this.websocket.close({code:e,reason:t,success:n,fail:r}):r&&r()},t.prototype.onOpen=function(e){this.websocket?this.websocket.onOpen(e):this.logger.error("webSocket connect failed")},t.prototype.onMessage=function(e){this.websocket?this.websocket.onMessage((function(t){e&&e(t.data)})):this.logger.error("webSocket connect failed")},t.prototype.onClose=function(e){this.websocket?this.websocket.onClose((function(t){e&&e(t.code,t.reason,t.wasClean)})):this.logger.error("webSocket connect failed")},t.prototype.onError=function(e){this.websocket?this.websocket.onError(e):this.logger.error("webSocket connect failed")},t}(ku),Vu=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.logger=tc.createLogger("AGCPlatformInfoService"),t}return Ru(t,e),t.prototype.getPlatform=function(){return"JS-SDK-Mini-Program"},t.prototype.getPlatformVersion=function(){try{return wx.getSystemInfoSync().system}catch(e){return this.logger.error("getLanguage:fail,",e.message),""}},t.prototype.getAppVersion=function(){try{return wx.getAccountInfoSync().miniProgram.version}catch(e){return this.logger.error("getAppVersion:fail,",e.message),""}},t.prototype.getLanguage=function(){try{var e=wx.getSystemInfoSync().language;return e.toLowerCase().length>2?e.toLowerCase().substr(0,2):""}catch(e){return this.logger.error("getLanguage:fail,",e.message),""}},t}($u),Hu="undefined"==typeof window&&"object"==typeof wx,Fu=t;Fu.registerInternalService({name:"AGCNetworkService",serviceFactory:function(e){return Hu?new Lu(Mu):new Lu}}),Fu.registerInternalService({name:"AGCWebSocketService",serviceFactory:function(e){return Hu?new xu:new Uu}}),Fu.registerInternalService({name:"AGCPlatformInfoService",serviceFactory:function(e){return Hu?new Vu:new Du(e)}});
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */
var ju=function(e,t){return(ju=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function Bu(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function s(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}function Gu(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!((i=(i=a.trys).length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}var Wu=function(){function e(){this.TWO_MINUTES_EARLY=12e4,this.ONE_HOUR=36e5,this.expiration=0,this.tokenString="",this.issuedAt="0",this.notBefore="0",this.lastRefreshTime=0}return e.prototype.constructFromJson=function(e){this.expiration=e.expiration,this.issuedAt=e.issuedAt,this.tokenString=e.tokenString,this.notBefore=e.notBefore,this.lastRefreshTime=e.lastRefreshTime},e.prototype.isValid=function(){var e=(new Date).getTime(),t=this.lastRefreshTime+1e3*this.expiration-this.TWO_MINUTES_EARLY;return null!=this.tokenString&&e<=t},e.prototype.allowRefresh=function(){return(new Date).getTime()-this.lastRefreshTime>this.ONE_HOUR},e}(),Yu=function(e){function t(t){var n=e.call(this,t)||this;return n.REQUEST_URL="/agc/apigw/oauth2/v1/token",n.useJwt=0,n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}ju(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}(t,e),t.prototype.getBody=function(){return{grant_type:"client_credentials",client_id:this.getHeaderClientId(),client_secret:this.getClientSecret(),useJwt:this.getUseJwt()}},t.prototype.getUrl=function(){return this.getAgcgwUrl()+this.REQUEST_URL},t.prototype.setUseJwt=function(e){this.useJwt=e},t.prototype.getUseJwt=function(){return this.useJwt},t}(function(){function e(e){this.sdkPlatform="",this.sdkPlatformVersion="",this.sdkType="JS",this.packageName="",this.appVersion="",this.headerProductId="",this.headerAppId="",this.headerClientId="",this.clientSecret="",this.agcgwUrl="",this.agcgwBackUrl="",this.agcConfig=null,this.instance=null,this.sdkServiceName="agconnect-credential",this.sdkVersion="1.3.1",this.setAGCInstance(e)}return e.prototype.setAGCInstance=function(e){if(!e)throw new rc(ic.AGC_INNER_ERROR,{message:"set AGCInstance using null or undefined object"},"CredentialsService");this.instance=e,this.agcConfig=null},e.prototype.getHeaderClientId=function(){return this.initConfig()?this.headerClientId:""},e.prototype.getClientSecret=function(){return this.initConfig()?this.clientSecret:""},e.prototype.getAgcgwUrl=function(){return this.initConfig()?this.agcgwUrl:""},e.prototype.getHeader=function(){if(!this.initConfig())return"";var e=this.instance.getService("AGCPlatformInfoService");return e&&(this.sdkPlatform=e.getPlatform(),this.sdkPlatformVersion=e.getPlatformVersion(),this.packageName=e.getPackageName(),this.appVersion=e.getAppVersion()),{sdkVersion:this.sdkVersion,sdkPlatform:this.sdkPlatform,sdkServiceName:this.sdkServiceName,sdkPlatformVersion:this.sdkPlatformVersion,sdkType:this.sdkType,packageName:this.packageName,appVersion:this.appVersion,app_id:this.headerAppId,client_id:this.headerClientId,productId:this.headerProductId,"Content-Type":"application/json;charset=UTF-8"}},e.prototype.initConfig=function(){if(!this.instance)throw new rc(ic.AGC_INNER_ERROR,{message:"set AGCInstance using null or undefined object"},"CredentialsService");return null==this.agcConfig&&(this.agcConfig=this.instance.config(),this.headerProductId=this.agcConfig.client.product_id,this.headerClientId=this.agcConfig.client.client_id,this.headerAppId=this.agcConfig.client.app_id,this.clientSecret=this.agcConfig.client.client_secret,this.agcgwUrl=this.instance.getGwUrl(),this.agcgwBackUrl=this.instance.getGwBackUrl()),!0},e}()),Ku=function(){function e(){}return e.getInstance=function(){return e.instance||(e.instance=new e),e.instance},e.prototype.getStorage=function(e){var t=e.getService("AGCStorageService");if(null!=t)return t.getStorageInstance(0,e.getCryptImp());qu.error("Get AGC storage Service failed.")},e}(),qu=tc.createLogger("CredentialsService"),zu=function(){function e(e){this.instance=e||t.instance()}return e.prototype.removeToken=function(){if(this.instance.getCustomCredentialsProvider())return Promise.resolve();var e=this.getCredentialStoreKey(),t=Ku.getInstance().getStorage(this.instance);return t?t.remove(e).catch((function(e){return e instanceof rc?Promise.reject(e):Promise.reject(new rc(ic.REMOVE_TOKEN_FAILED,e,"CredentialsService"))})):Promise.reject(new rc(ic.FAIL_TO_GET_STORAGE_SERVICE,null,"CredentialsService"))},e.prototype.getToken=function(e){return Bu(this,void 0,void 0,(function(){var t,n,r,i,o,a=this;return Gu(this,(function(s){switch(s.label){case 0:return this.instance.getCustomCredentialsProvider()?(t=new Wu,[4,this.instance.getCustomCredentialsProvider().getToken(!!e)]):[3,2];case 1:return(n=s.sent())&&n.tokenString&&n.expiration?isNaN(Number(n.expiration))||Number(n.expiration)<=0?(qu.error("the customCredentialsProvider getToken must return valid expiration."),[2,Promise.reject(new rc(ic.GET_TOKEN_FAILED,{message:"the customCredentialsProvider getToken must return valid expiration"},"CredentialsService"))]):(t.tokenString=n.tokenString,t.expiration=n.expiration,t.lastRefreshTime=(new Date).getTime(),[2,Promise.resolve(t)]):(qu.error("the customCredentialsProvider getToken method must contain return value: tokenString, expiration."),[2,Promise.reject(new rc(ic.GET_TOKEN_FAILED,{message:"the customCredentialsProvider getToken method must contain return value: tokenString, expiration"},"CredentialsService"))]);case 2:return r=this.getCredentialStoreKey(),(i=Ku.getInstance().getStorage(this.instance))?(o=new Wu,[2,i.get(r).then((function(t){return Bu(a,void 0,void 0,(function(){var n,a;return Gu(this,(function(s){switch(s.label){case 0:return t?o.constructFromJson(JSON.parse(t)):o=null,o&&o.isValid()?e&&o.allowRefresh()?[4,this.requestToken()]:[3,3]:[3,4];case 1:return n=s.sent(),[4,i.set(r,JSON.stringify(n))];case 2:return s.sent(),[2,Promise.resolve(n)];case 3:return[2,Promise.resolve(o)];case 4:return[4,this.requestToken()];case 5:return a=s.sent(),[4,i.set(r,JSON.stringify(a))];case 6:return s.sent(),[2,Promise.resolve(a)]}}))}))})).catch((function(e){return e instanceof rc?Promise.reject(e):Promise.reject(new rc(ic.GET_TOKEN_FAILED,e,"CredentialsService"))}))]):[2,Promise.reject(new rc(ic.FAIL_TO_GET_STORAGE_SERVICE,null,"CredentialsService"))]}}))}))},e.prototype.getCredentialStoreKey=function(){return this.instance.name()===n?"agcClientToken:"+this.instance.config().client.client_id:"agcClientToken_"+this.instance.name()+":"+this.instance.config().client.client_id},e.prototype.requestToken=function(){return Bu(this,void 0,void 0,(function(){var e,t;return Gu(this,(function(n){return(e=this.instance.getService("AGCNetworkService"))?((t=new Yu(this.instance)).setUseJwt(1),[2,e.post(t.getUrl(),t.getBody(),t.getHeader()).then((function(e){if(e.data.ret&&0!=e.data.ret.code)return Promise.reject(new rc({code:e.data.ret.code,message:e.data.ret.msg},null,"CredentialsService"));var t=new Wu;return t.tokenString=e.data.access_token,t.expiration=e.data.expires_in,t.lastRefreshTime=(new Date).getTime(),Promise.resolve(t)}))]):[2,Promise.reject(new rc(ic.FAIL_TO_GET_NETWORK_SERVICE,null,"CredentialsService"))]}))}))},e}();t.registerInternalService({name:"CredentialsService",serviceFactory:function(e){return new zu(e)}});var Ju,Xu=function(){function e(){this.storageImpl=void 0}return e.prototype.getAaid=function(t){
/*! *****************************************************************************
  Copyright (c) Microsoft Corporation.

  Permission to use, copy, modify, and/or distribute this software for any
  purpose with or without fee is hereby granted.

  THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
  REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
  AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
  INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
  LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
  OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
  PERFORMANCE OF THIS SOFTWARE.
  ***************************************************************************** */return function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function a(e){try{c(r.next(e))}catch(e){o(e)}}function s(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}(this,void 0,void 0,(function(){var r,i,o,a,s;return function(e,t){var n,r,i,o,a={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return a.label++,{value:o[1],done:!1};case 5:a.label++,r=o[1],o=[0];continue;case 7:o=a.ops.pop(),a.trys.pop();continue;default:if(!((i=(i=a.trys).length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){a=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){a.label=o[1];break}if(6===o[0]&&a.label<i[1]){a.label=i[1],i=o;break}if(i&&a.label<i[2]){a.label=i[2],a.ops.push(o);break}i[2]&&a.ops.pop(),a.trys.pop();continue}o=t.call(e,a)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}}(this,(function(c){switch(c.label){case 0:r=tc.createLogger("ClientInfoService"),c.label=1;case 1:return c.trys.push([1,5,,6]),i="agcAaid_"+t.name()+":"+t.config().client.client_id,t.name()===n&&(i="agcAaid:"+t.config().client.client_id),null==(o=t.getService("AGCStorageService"))?(r.error("Get AGC storage Service failed."),[2,Promise.reject(new rc(ic.GET_AAID_ERROR,{message:"get AGC storage Service failed"},"util"))]):(this.storageImpl||(this.storageImpl=o.getStorageInstance(e.persistence)),[4,this.storageImpl.get(i).catch((function(e){return r.error("get aaid failed",e.message),Promise.reject(new rc(ic.GET_AAID_ERROR,{message:"get aaid from storage failed"},"util"))}))]);case 2:return null!=(a=c.sent())&&null!=a&&""!=a?[3,4]:(a=this.getRandomString(),[4,this.storageImpl.set(i,a).catch((function(e){return r.error("get aaid failed",e.message),Promise.reject(new rc(ic.GET_AAID_ERROR,{message:"save aaid to storage failed"},"util"))}))]);case 3:c.sent(),c.label=4;case 4:return[2,a];case 5:return s=c.sent(),r.error("get aaid failed",s.message),[2,Promise.reject(new rc(ic.GET_AAID_ERROR,s,"util"))];case 6:return[2]}}))}))},e.prototype.getRandomString=function(){var e="0123456789abcdef",t=e.length;return"xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx".replace(/x/g,(function(){return e.charAt(Math.floor(Math.random()*t))}))},e.persistence=0,e.INSTANCE=new e,e}(),Qu=function(){function e(){}return e.getEnvironmentIns=function(){return e.instance},e.prototype.isBrowser=function(){return"object"==typeof self&&self.self===self},e.prototype.isNodeJS=function(){try{return"[object process]"===Object.prototype.toString.call(global.process)}catch(e){return!1}},e.prototype.isReactNative=function(){return"object"==typeof navigator&&"ReactNative"===navigator.product},e.prototype.isQuickApp=function(){try{return global&&"fastapp"in global}catch(e){return!1}},e.instance=new e,e}(),Zu=function(){function e(e){this.instance=e||t.instance()}return e.prototype.getAaid=function(){return Xu.INSTANCE.getAaid(this.instance)},e.prototype.getEnvironmentUtil=function(){return Qu.getEnvironmentIns()},e}();function el(e){return null!=e}!function(e){e.ObserverImpl=function(e,t,n){if("function"==typeof e||el(t)||el(n))this.next=e,this.error=t||null,this.complete=n||null;else{var r=e;this.next=r.next||null,this.error=r.error||null,this.complete=r.complete||null}}}(Ju||(Ju={})),t.registerInternalService({name:"ClientInfoService",serviceFactory:function(e){return new Zu(e)}});var tl=new(function(){function e(){}return e.prototype.init=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.getConfig=function(){return t.instance().config()},e.prototype.getToken=function(e){return fn(this,void 0,void 0,(function(){var n;return pn(this,(function(r){switch(r.label){case 0:if(!(n=t.instance().getService("CredentialsService")))return[3,5];r.label=1;case 1:return r.trys.push([1,3,,4]),[4,n.getToken()];case 2:return[2,r.sent().tokenString];case 3:return r.sent(),ls.hiLog.error(e,Na.code,Na.message),[2,""];case 4:return[3,6];case 5:return ls.hiLog.warn(e,Ia.code,Ia.message),[2,""];case 6:return[2]}}))}))},e.prototype.getAaid=function(e){return fn(this,void 0,void 0,(function(){var n;return pn(this,(function(r){try{return null!=(n=t.instance().getService("ClientInfoService"))?[2,n.getAaid()]:(ls.hiLog.warn(e,Ia.code,Ia.message),[2,""])}catch(t){return ls.hiLog.error(e,_a.code,_a.message),[2,""]}return[2]}))}))},e.prototype.deleteAaid=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2]}))}))},e.prototype.getPushToken=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,""]}))}))},e.prototype.getOaid=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,""]}))}))},e.prototype.getOaidFlag=function(){return fn(this,void 0,void 0,(function(){return pn(this,(function(e){return[2,!0]}))}))},e.prototype.getABTestProvider=function(){var e=t.instance().getService("ABTestService");if(null!==e)return e.getABtestInstance("REMOTE_CONFIG")?e:null},e}());ls.setHiLog(ws),ls.setHiSendData(Os),ls.setSendLogTask(Qs),ls.setSendTask(Ws),ls.setHiAutoReport(Ps),ls.setHiStorage(Ds),ls.setHiDBStorage(Ms),ls.setHiClientProperty($s),ls.setHiClient(_s),ls.setHiMigration(Ks),ls.setHiAgcUtil(tl);var nl=function(){function e(){}return e.prototype.onEventCallback=function(e,t){var n={eventtime:(new Date).getTime(),isCallBack:!0};fs.onEventCallback(e,t,n)},e}(),rl=new r((function(){return new ys})),il=new r((function(){return new nl})),ol={InitSettings:rs,RoutePolicy:Qa,LogLevel:Xa,UrlClusteringOptions:Es,UserProfileType:Za,EventName:Br,ParamName:{STORENAME:"$StoreName",BRAND:"$Brand",CATEGORY:"$Category",PRODUCTID:"$ProductId",PRODUCTNAME:"$ProductName",PRODUCTFEATURE:"$ProductFeature",PRICE:"$Price",QUANTITY:"$Quantity",REVENUE:"$Revenue",CURRNAME:"$CurrName",PLACEID:"$PlaceId",DESTINATION:"$Destination",ENDDATE:"$EndDate",BOOKINGDAYS:"$BookingDays",PASSENGERSNUMBER:"$PassengersNumber",BOOKINGROOMS:"$BookingRooms",ORIGINATINGPLACE:"$OriginatingPlace",BEGINDATE:"$BeginDate",TRANSACTIONID:"$TransactionId",CLASS:"$Class",CLICKID:"$ClickId",PROMOTIONNAME:"$PromotionName",CONTENT:"$Content",EXTENDPARAM:"$ExtendParam",MATERIALNAME:"$MaterialName",MATERIALSLOT:"$MaterialSlot",MEDIUM:"$Medium",SOURCE:"$Source",KEYWORDS:"$Keywords",OPTION:"$Option",STEP:"$Step",VIRTUALCURRNAME:"$VirtualCurrName",VOUCHER:"$Voucher",PLACE:"$Place",SHIPPING:"$Shipping",TAXFEE:"$TaxFee",USERGROUPID:"$UserGroupId",LEVELNAME:"$LevelName",RESULT:"$Result",ROLENAME:"$RoleName",LEVELID:"$LevelId",CHANNEL:"$Channel",SCORE:"$Score",SEARCHKEYWORDS:"$SearchKeywords",CONTENTTYPE:"$ContentType",ACHIEVEMENTID:"$AchievementId",FLIGHTNO:"$FlightNo",POSITIONID:"$PositionId",PRODUCTLIST:"$ProductList",ACOUNTTYPE:"$AcountType",EVTRESULT:"$EvtResult",PREVLEVEL:"$PrevLevel",CURRVLEVEL:"$CurrvLevel",VOUCHERS:"$Vouchers",MATERIALSLOTTYPE:"$MaterialSlotType",LISTID:"$ListId",FILTERS:"$Filters",SORTS:"$Sorts",ORDERID:"$OrderId",PAYTYPE:"$PayType",REASON:"$Reason",EXPIREDATE:"$ExpireDate",VOUCHERTYPE:"$VoucherType",SERVICETYPE:"$ServiceType",DETAILS:"$Details",COMMENTTYPE:"$CommentType",REGISTMETHOD:"$RegistMethod",FEATURE:"$Feature",ROOMS:"$Rooms",SALEPRICE:"$SalePrice",RENTFEE:"$RentFee",RENTTYPE:"$RentType",ACTIVITYNAME:"$ActivityName",ACTIVITYTYPE:"$ActivityType",AMOUNT:"$Amount",BALANCE:"$Balance",BATTLENAME:"$BattleName",BATTLETYPE:"$BattleType",CARDLIST:"$CardList",CLASSLIMIT:"$ClassLimit",COMBAT:"$Combat",CUTSCENENAME:"$CutsceneName",DAMAGE:"$Damage",DIFFICULTY:"$Difficulty",DISCOUNT:"$Discount",DUNGEONNAME:"$DungeonName",DURATION:"$Duration",ENHANCETYPE:"$EnhanceType",EQUIPMENTID:"$EquipmentId",EQUIPMENTLEVEL:"$EquipmentLevel",EQUIPMENTNAME:"$EquipmentName",FIRSTCREATE:"$FirstCreate",FIRSTPAY:"$FirstPay",FIRSTSIGNIN:"$FirstSignIn",FRIENDNUMBER:"$FriendNumber",GIFTNAME:"$GiftName",GIFTTYPE:"$GiftType",INVITER:"$Inviter",ISFREE:"$IsFree",ISTOPLEVEL:"$IsTopLevel",ITEMLIST:"$ItemList",LEFTPULLSFORGUARANTEE:"$LeftPullsForGuarantee",LEVELBEFORE:"$LevelBefore",LEVELLIMIT:"$LevelLimit",MEMBERS:"$Members",MESSAGETITLE:"$MessageTitle",MESSAGETYPE:"$MessageType",MOUNTDEFAULTNAME:"$MountDefaultName",MOUNTID:"$MountId",MOUNTLEVEL:"$MountLevel",MVP:"$MVP",NEWCLASS:"$NewClass",NEWVALUE:"$NewValue",NUMBEROFCARDS:"$NumberOfCards",NUMBEROFDRAWING:"$NumberOfDrawing",OLDCLASS:"$OldClass",OLDVALUE:"$OldValue",OPERATION:"$Operation",PACKAGETYPE:"$PackageType",PARTICIPANTS:"$Participants",PETDEFAULTNAME:"$PetDefaultName",PETID:"$PetId",PETLEVEL:"$PetLevel",QUALITY:"$Quality",RANKING:"$Ranking",REWARD:"$Reward",ROLECLASS:"$RoleClass",ROLECOMBAT:"$RoleCombat",ROLEGENDER:"$RoleGender",SERVER:"$Server",SKILLLEVEL:"$SkillLevel",SKILLLEVELBEFORE:"$SkillLevelBefore",SKILLNAME:"$SkillName",TASKNAME:"$TaskName",TASKTYPE:"$TaskType",TOTALAFTERCHANGE:"$TotalAfterChange",TYPE:"$Type",USERGROUPLEVEL:"$UserGroupLevel",USERGROUPNAME:"$UserGroupName",VIDEONAME:"$VideoName",VIDEOTYPE:"$VideoType",VIPLEVEL:"$VIPLevel",WINREASON:"$WinReason",VIPTYPE:"$VIPType",VIPSTATUS:"$VIPStatus",VIPEXPIREDATE:"$VIPExpireDate",ENTER:"$Enter",STARTDATE:"$StartDate",EFFECTIVETIME:"$EffectiveTime",NAME:"$Name",MODE:"$Mode",SUBJECT:"$Subject",ACCURACY:"$Accuracy",CONTENTLENGTH:"$ContentLength",REMARK:"$Remark",DAYS:"$Days",POSTID:"$PostId",ENTRY:"$Entry",INFORMATIONTYPE:"$InformationType",INFORMATION:"$Information",TASKID:"$TaskId",LEVEL:"$Level",CONTENTNAME:"$ContentName",PAGENAME:"$PageName",PROPS:"$Props",PURCHASEENTRY:"$PurchaseEntry",ARRIVALDATE:"$ArrivalDate",BINDDURATION:"$BindDuration",BUYERTYPE:"$BuyerType",CONFIGURATION:"$Configuration",DEALERNAME:"$DealerName",DEVICETYPE:"$DeviceType",DEVICENAME:"$DeviceName",ENERGY:"$Energy",ISLOAN:"$IsLoan",LOANCHANNEL:"$LoanChannel",LOANPRODUCTNAME:"$LoanProductName",MODEL:"$Model",OCCURREDDATE:"$OccurredDate",REPAYMENTMETHOD:"$RepaymentMethod",SEAT:"$Seat",SERIES:"$Series",ACTION:"$Action",PAGE:"$Page",INDEX:"$Index",MODULE:"$Module",SOURCEPAGENAME:"$SourcePageName",CITY:"$City",FROMCITY:"$FromCity",TOCITY:"$ToCity",DEPARTUREDATE:"$DepartureDate",RETURNDATE:"$ReturnDate",TRIPTYPE:"$TripType",SEARCHHOTEL:"$SearchHotel",SPECIALTICKET:"$SpecialTicket",HASBABY:"$HasBaby",HASCHILDREN:"$HasChildren",COUNTRY:"$Country",STAR:"$Star",CHECKINDATE:"$CheckinDate",CHECKOUTDATE:"$CheckoutDate",ALDULTCOUNT:"$AldultCount",BABYCOUNT:"$BabyCount",CHILDRENCOUNT:"$ChildrenCount",FROM:"$From",CARMODE:"$CarMode",CARTIME:"$CarTime",FLIGHTID:"$FlightID",SEARCHRESULT:"$SearchResult",PRODUCT:"$Product",DEPARTURETIME:"$DepartureTime",ARRIVETIME:"$ArriveTime",DEPARTUREAIRPORT:"$DepartureAirport",ARRIVEEAIRPORT:"$ArriveeAirport",AIRLINE:"$Airline",FLIGHTTYPE:"$FlightType",DIRECTFLIGHT:"$DirectFlight",TRAINTYPE:"$TrainType",FROMSTATION:"$FromStation",BEDTYPE:"$BedType",BREAKFAST:"$Breakfast",ARRIVEDATE:"$ArriveDate",TRAINID:"$TrainID",HOTELID:"$HotelID",HOTELNAME:"$HotelName",TRIPTAG:"$TripTag",HOTELTYPE:"$HotelType",CABIN:"$Cabin",ROOMTYPE:"$RoomType",CARTYPE:"$CarType",SUPPLIER:"$Supplier",STUDENTCOUNT:"$StudentCount",ROOMCOUNT:"$RoomCount",PERSONCOUNT:"$PersonCount",VOUCHERID:"$VoucherID",VOUCHERNAME:"$VoucherName",VOUCHERPRICE:"$VoucherPrice",USERTYPE:"$UserType",TARGET:"$Target",ISCOMPELETED:"$IsCompeleted",USERLEVEL:"$UserLevel",TIME:"$Time",DISTANCE:"$Distance",CALORIECONSUMED:"$CalorieConsumed",PROGRESS:"$Progress",SOURCEPAGE:"$SourcePage",MULTIPLEACCOUNTS:"$MultipleAccounts",ACCOUNTTYPE:"$AccountType",FAILUREREASON:"$FailureReason",AUTHORITY:"$Authority",CARDTYPE:"$CardType",ISSUEBANK:"$IssueBank",TRANSFORMAMOUNT:"$TransformAmount",BANKNAME:"$BankName",ANK_NAME:"bank_name",SOURCEMODULE:"$SourceModule",STOCKCODE:"$Stockcode",STOCKNAME:"$Stockname",MARKETCODE:"$MarketCode",MARKETNAME:"$MarketName",VIEWTYPE:"$ViewType",TRENDCYCLE:"$TrendCycle",TRANSACTIONTYPE:"$TransactionType",CURRENCY:"$Currency",MONEY:"$Money",FINANCEID:"$FinanceId",FINANCENAME:"$FinanceName",FINANCETYPE:"$FinanceType",FINANCERATE:"$FinanceRate",FINANCETIMELIMIT:"$FinanceTimeLimit",FINANCEAMOUNTMIN:"$FinanceAmountMin",FINANCERISKLEV:"$FinanceRiskLev",PURCHASEAMOUNT:"$PurchaseAmount",HANDLINGFEES:"$HandlingFees",REDEMPTIONAMOUNT:"$RedemptionAmount",RETURNAMOUNT:"$ReturnAmount",FUNDCODE:"$FundCode",FUNDTYPE:"$FundType",FUNDNAME:"$FundName",FUNDRISKLEV:"$FundRiskLev",CHARGERATE:"$ChargeRate",PAYMENTMETHOD:"$PaymentMethod",FIXEDCYCLE:"$FixedCycle",ENTRANCE:"$Entrance",CODE:"$code",NEWSTOPIC:"$NewsTopic",INFORMATIONSOURCE:"$InformationSource",COMMENTSNUMBER:"$CommentsNumber",FORWARDINGNUMBER:"$forwardingNumber",LIKES:"$Likes",TITLE:"$Title",SEARCHTYPE:"$SearchType",SOURCELOCATION:"$SourceLocation",LOCATION:"$Location",ID:"$Id",PLAYMODE:"$PlayMode",LISTS:"$Lists",ADLOCATION:"$Adlocation",ADCATEGORY:"$AdCategory",ADTHEME:"$AdTheme",BUTTONNAME:"$ButtonName",USERID:"$UserID",PAGECATEGORY:"$PageCategory",RATING:"$Rating",PERFORMANCE:"$Performance",TRADINGRULES:"$TradingRules",PORTFOLIO:"$Portfolio",INVESTMENTMANAGER:"$InvestmentManager",FUNDSIZE:"$FundSize",VIPMONEY:"$VipMoney",VIPLOCATION:"$VipLocation",VIPFAILED:"$VipFailed",SECTION:"$Section"}};return t.registerApiProvider("analytics",(function(){return rl.get()}),ol),function(e){e.registerApiProvider("hmscore_analytics",(function(){return il.get()}))}(t),t}));

!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.lib=e():t.lib=e()}(window,function(){return n=[function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.webPrint=void 0;var a=n(3),n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.logLevel=a.LogLevel.INFO}var e,n,r;return e=t,(n=[{key:"initLogLevel",value:function(t){t&&(t.debug?t.logLevel&&a.LogLevel[t.logLevel]?this.logLevel=t.logLevel:this.logLevel=a.LogLevel.INFO:this.logLevel=a.LogLevel.OFF)}},{key:"log",value:function(t){var e=this.generateLevels();0<e&&e<=3&&console.log("LTSSDK:".concat(t))}},{key:"warn",value:function(t){var e=this.generateLevels();1<=e&&e<=2&&console.warn("LTSSDK:".concat(t))}},{key:"error",value:function(t){1===this.generateLevels()&&console.error("LTSSDK:".concat(t))}},{key:"generateLevels",value:function(){return this.logLevel===a.LogLevel.INFO?3:this.logLevel===a.LogLevel.WARN?2:this.logLevel===a.LogLevel.ERROR?1:0}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.webPrint=new n},function(t,r,e){"use strict";function o(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var r,o,i,a,c=[],u=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;u=!1}else for(;!(u=(r=i.call(n)).done)&&(c.push(r.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){var n;if(t)return"string"==typeof t?i(t,e):"Map"===(n="Object"===(n=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(t,e):void 0}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function i(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}Object.defineProperty(r,"__esModule",{value:!0}),r.checkConfigKey=r.checkLabels=r.checkObjectTypeAndLength=r.checkPattern=r.checkLogParamString=r.excludeObject=r.isValidString=r.isEmpty=void 0;var c=e(0),u="The length of $1 exceeds the maximum value of $2.",l="$1 is null.",f="$1 is invalid.";function n(t){return!t||""===t}r.isEmpty=n;r.isValidString=function(t,e){return n(t)?(c.webPrint.warn("LTS.0001|".concat(l.replace("$1",e))),!1):"string"==typeof t||(c.webPrint.warn("LTS.0002|".concat(f.replace("$1",e))),!1)},r.excludeObject=function(t,e){return"object"!==a(t)||(c.webPrint.warn("LTS.0002|".concat(f.replace("$1",e))),!1)},r.checkLogParamString=function(t,e,n){return e?!(String(e).length>n&&(c.webPrint.warn("LTS.0003|".concat(u.replace("$1",t).replace("$2",n))),1)):(c.webPrint.warn("LTS.0001|".concat(l.replace("$1",t))),!1)},r.checkPattern=function(t,e,n){return!!n.test(e)||(c.webPrint.warn("LTS.0006|".concat(t," doesn't match pattern.")),!1)},r.checkObjectTypeAndLength=function(t,e,n){return t&&"object"!==a(t)||"boolean"==typeof t?(c.webPrint.warn("LTS.0002|".concat(f.replace("$1",e))),!1):!(Object.keys(t).length>n&&(c.webPrint.warn("LTS.0003|".concat(u.replace("$1",e).replace("$2",n))),1))},r.checkLabels=function(t){var n;return!t||(n=!0,Object.entries(t||{}).forEach(function(t){var t=o(t,2),e=t[0],t=t[1];(0,r.checkLogParamString)("labels key:".concat(e),e,64)||(n=!1),(0,r.checkPattern)("labels key:".concat(e),e,/^[a-zA-Z][A-Za-z0-9_]*$/)||(n=!1),(0,r.checkLogParamString)("labels value",t,256)&&(0,r.excludeObject)(t,"labels value of :".concat(e))||(n=!1)}),n&&(0,r.checkObjectTypeAndLength)(t,"labels",50))};r.checkConfigKey=function(t){var e,n=new Set(["url","region","projectId","groupId","streamId","group","cacheThreshold","timeThreshold","debug","platform","logLevel","timeInterval"]);return!t||(e=!0,Object.keys(t).forEach(function(t){n.has(t)||(e=!1)}),e||c.webPrint.warn("LTS.0007|Invalid configuration parameters."),e)}},function(t,e,n){"use strict";function r(t){return function(t){if(Array.isArray(t))return o(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){var n;if(t)return"string"==typeof t?o(t,e):"Map"===(n="Object"===(n=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?o(t,e):void 0}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}Object.defineProperty(e,"__esModule",{value:!0}),e.getLabelMap=e.getHeader=e.getBodyArray=void 0;var i=n(11),a=n(12).version,c=(e.getBodyArray=function(t,e,n){return u(n,t)?c(e,n):null},function(t,e){return{labels:t&&JSON.parse(t)||{},logs:(e||[]).map(function(t){return{contents:[Object.assign(Object.assign({},null==t?void 0:t.content),{__client_time__:t.log_time_ns})]}})}}),u=function(t,e){return!(!t||0===t.length||(e=(t=e||{}).groupId,t=t.streamId,!e)||!t)},l=(e.getLabelMap=function(t){var e=new Map;return t.forEach(function(t){e.has(t.labels)?e.set(t.labels,[].concat(r(e.get(t.labels)),[t])):e.set(t.labels,[t])}),e},e.getHeader=function(t){var e="X-Sdk-date",n="Lts-Sdk-Version",r={"Content-Type":"application/json"};return r["Lts-Sdk-Request-Id"]=i.UUID.create(),r[n]=a,r[e]=l(),Object.assign(Object.assign({},r),t)},function(){try{return(new Date).toISOString().replace(/\.[\d]{3}Z/,"Z").replace(/(\:)|(\-)/g,"")}catch(t){return""}})},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.LogLevel=e.REGIONS=e.POLICY_CONSTANTS=void 0,e.POLICY_CONSTANTS={TIME_MIN:1,TIME_MAX:60,THRESHOLD_MIN:30,THRESHOLD_MAX:1e3},e.REGIONS={"cn-north-7":"cn-north-7","cn-north-4":"cn-north-4","cn-north-6":"cn-north-6","cn-north-5":"cn-north-5","cn-north-9":"cn-north-9"},e.LogLevel={OFF:"OFF",INFO:"INFO",WARN:"WARN",ERROR:"ERROR"}},function(t,e,n){t.exports=n(5)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,e=n(6);n=window,r="LTS_WEB_SDK",e=e.default,n[r]||Object.defineProperty(n,r,{writable:!0,enumerable:!0,configurable:!0,value:e})},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(7),c=n(13),u=n(0),l=n(1),n=function(){function e(t){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function");this.uploading=!1,this.sendService={reportFlag:!1,url:""},this.logRecords=[],this.config(t)}var t,n,r;return t=e,(n=[{key:"config",value:function(t){(0,l.checkObjectTypeAndLength)(t,"Configuration",11)&&(this.webConfig=Object.assign(Object.assign({},this.webConfig),t),u.webPrint.initLogLevel(this.webConfig),this.webConfig.timeThreshold=(null==(t=this.webConfig)?void 0:t.timeInterval)||(null==(t=this.webConfig)?void 0:t.timeThreshold)||30),c.default.checkConfig(this.webConfig)?(this.sendService.reportFlag=!0,this.sendService.url=c.default.updateUrl(this.webConfig),this.logUploadPolicyScheduleTime(),u.webPrint.log("init success")):(this.sendService.reportFlag=!1,u.webPrint.error("Failed to initialize the sdk."))}},{key:"reportImmediately",value:function(t,e){u.webPrint.initLogLevel(this.webConfig),this.sendService.reportFlag?(a.default.writeLog(Object.assign(Object.assign({},this.webConfig),this.sendService),this.logRecords,t,e),a.default.uploadData(Object.assign(Object.assign({},this.webConfig),this.sendService),this.logRecords)):u.webPrint.log("LTS.0100|The LTSSDK is not initialized.")}},{key:"report",value:function(t,e){u.webPrint.initLogLevel(this.webConfig),this.sendService.reportFlag?t?a.default.writeLog(Object.assign(Object.assign({},this.webConfig),this.sendService),this.logRecords,t,e):u.webPrint.warn("LTS.0001|Content is null."):u.webPrint.log("LTS.0100|The LTSSDK is not initialized.")}},{key:"logUploadPolicyScheduleTime",value:function(){var t;this.webConfig.timeThreshold&&!this.uploading&&(t=1e3*this.webConfig.timeThreshold,this.setHeartbeatFunc(t))}},{key:"setHeartbeatFunc",value:function(t){var e=this;this.sendTimer=setTimeout(function(){e.uploading=!0,e.logRecords=e.logRecords.filter(function(t){return"SUCCESS"!==t.sendState}),a.default.uploadData(Object.assign(Object.assign({},e.webConfig),e.sendService),e.logRecords).then(function(){e.uploading=!1,e.logUploadPolicyScheduleTime()}).catch(function(){e.uploading=!1,e.logUploadPolicyScheduleTime()})},t)}}])&&i(t.prototype,n),r&&i(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();e.default=n},function(t,e,n){"use strict";function j(t){return(j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function P(){/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */P=function(){return a};var a={},t=Object.prototype,u=t.hasOwnProperty,l=Object.defineProperty||function(t,e,n){t[e]=n.value},e="function"==typeof Symbol?Symbol:{},r=e.iterator||"@@iterator",n=e.asyncIterator||"@@asyncIterator",o=e.toStringTag||"@@toStringTag";function i(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{i({},"")}catch(t){i=function(t,e,n){return t[e]=n}}function c(t,e,n,r){var o,i,a,c,e=e&&e.prototype instanceof d?e:d,e=Object.create(e.prototype),r=new S(r||[]);return l(e,"_invoke",{value:(o=t,i=n,a=r,c="suspendedStart",function(t,e){if("executing"===c)throw new Error("Generator is already running");if("completed"===c){if("throw"===t)throw e;return O()}for(a.method=t,a.arg=e;;){var n=a.delegate;if(n){n=function t(e,n){var r=n.method,o=e.iterator[r];if(void 0===o)return n.delegate=null,"throw"===r&&e.iterator.return&&(n.method="return",n.arg=void 0,t(e,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),s;r=f(o,e.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,s;o=r.arg;return o?o.done?(n[e.resultName]=o.value,n.next=e.nextLoc,"return"!==n.method&&(n.method="next",n.arg=void 0),n.delegate=null,s):o:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,s)}(n,a);if(n){if(n===s)continue;return n}}if("next"===a.method)a.sent=a._sent=a.arg;else if("throw"===a.method){if("suspendedStart"===c)throw c="completed",a.arg;a.dispatchException(a.arg)}else"return"===a.method&&a.abrupt("return",a.arg);c="executing";n=f(o,i,a);if("normal"===n.type){if(c=a.done?"completed":"suspendedYield",n.arg===s)continue;return{value:n.arg,done:a.done}}"throw"===n.type&&(c="completed",a.method="throw",a.arg=n.arg)}})}),e}function f(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}a.wrap=c;var s={};function d(){}function h(){}function p(){}var e={},y=(i(e,r,function(){return this}),Object.getPrototypeOf),y=y&&y(y(L([]))),b=(y&&y!==t&&u.call(y,r)&&(e=y),p.prototype=d.prototype=Object.create(e));function g(t){["next","throw","return"].forEach(function(e){i(t,e,function(t){return this._invoke(e,t)})})}function v(a,c){var e;l(this,"_invoke",{value:function(n,r){function t(){return new c(function(t,e){!function e(t,n,r,o){var i,t=f(a[t],a,n);if("throw"!==t.type)return(n=(i=t.arg).value)&&"object"==j(n)&&u.call(n,"__await")?c.resolve(n.__await).then(function(t){e("next",t,r,o)},function(t){e("throw",t,r,o)}):c.resolve(n).then(function(t){i.value=t,r(i)},function(t){return e("throw",t,r,o)});o(t.arg)}(n,r,t,e)})}return e=e?e.then(t,t):t()}})}function m(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function w(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function S(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(m,this),this.reset(!0)}function L(e){if(e){var n,t=e[r];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length))return n=-1,(t=function t(){for(;++n<e.length;)if(u.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t}).next=t}return{next:O}}function O(){return{value:void 0,done:!0}}return l(b,"constructor",{value:h.prototype=p,configurable:!0}),l(p,"constructor",{value:h,configurable:!0}),h.displayName=i(p,o,"GeneratorFunction"),a.isGeneratorFunction=function(t){t="function"==typeof t&&t.constructor;return!!t&&(t===h||"GeneratorFunction"===(t.displayName||t.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,p):(t.__proto__=p,i(t,o,"GeneratorFunction")),t.prototype=Object.create(b),t},a.awrap=function(t){return{__await:t}},g(v.prototype),i(v.prototype,n,function(){return this}),a.AsyncIterator=v,a.async=function(t,e,n,r,o){void 0===o&&(o=Promise);var i=new v(c(t,e,n,r),o);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},g(b),i(b,o,"Generator"),i(b,r,function(){return this}),i(b,"toString",function(){return"[object Generator]"}),a.keys=function(t){var e,n=Object(t),r=[];for(e in n)r.push(e);return r.reverse(),function t(){for(;r.length;){var e=r.pop();if(e in n)return t.value=e,t.done=!1,t}return t.done=!0,t}},a.values=L,S.prototype={constructor:S,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(w),!t)for(var e in this)"t"===e.charAt(0)&&u.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function t(t,e){return i.type="throw",i.arg=n,r.next=t,e&&(r.method="next",r.arg=void 0),!!e}for(var e=this.tryEntries.length-1;0<=e;--e){var o=this.tryEntries[e],i=o.completion;if("root"===o.tryLoc)return t("end");if(o.tryLoc<=this.prev){var a=u.call(o,"catchLoc"),c=u.call(o,"finallyLoc");if(a&&c){if(this.prev<o.catchLoc)return t(o.catchLoc,!0);if(this.prev<o.finallyLoc)return t(o.finallyLoc)}else if(a){if(this.prev<o.catchLoc)return t(o.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<o.finallyLoc)return t(o.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&u.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var o=r;break}}var i=(o=o&&("break"===t||"continue"===t)&&o.tryLoc<=e&&e<=o.finallyLoc?null:o)?o.completion:{};return i.type=t,i.arg=e,o?(this.method="next",this.next=o.finallyLoc,s):this.complete(i)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),s},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),w(n),s}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n,r,o=this.tryEntries[e];if(o.tryLoc===t)return"throw"===(n=o.completion).type&&(r=n.arg,w(o)),r}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:L(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),s}},a}function o(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==j(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===j(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0});var i=n(8),a=n(9),c=n(2),u=n(1),l=n(0),n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.failTime=0,this.failTimer=null,this.censor=function(t,e){return e==1/0||e==-1/0?String(e):e}}var e,n,r;return e=t,(n=[{key:"uploadData",value:function(r,o){return i.__awaiter(this,void 0,void 0,P().mark(function t(){var e,n=this;return P().wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r.reportFlag){t.next=2;break}return t.abrupt("return");case 2:if(10<=this.failTime)return this.initTimeLock(),t.abrupt("return");t.next=5;break;case 5:if(e=o.filter(function(t){return"READY"===t.sendState||"RESEND"===t.sendState}),(e=(0,c.getLabelMap)(e))&&0!==e.size){t.next=9;break}return t.abrupt("return");case 9:e.forEach(function(t,e){t=n.getLogListSplitByBodyLimit(t);n.sendDataByPackage(t,e,r)});case 10:case"end":return t.stop()}},t,this)}))}},{key:"getLogListSplitByBodyLimit",value:function(e){var n,r,o,i;return e&&0!==e.length?(n=[],i=o=r=0,e.forEach(function(t){t=JSON.stringify(t).length;96256<=(i+=t)&&(n.push(e.slice(r,o)),i=t,r=o),o++}),r!==o&&n.push(e.slice(r,o)),n):[]}},{key:"sendDataByPackage",value:function(t,r,o){var i=this;t.forEach(function(e){var t,n=(0,c.getBodyArray)(o,r,e);"string"==typeof n&&(0,u.isEmpty)(n)||(t=(0,c.getHeader)({}),e.forEach(function(t){t.sendState="SENDING"}),(0,a.default)(n,t,o).then(function(){i.doUploadSuccess(e,o)},function(t){i.doUploadFail(o,e,t)}).catch(function(t){i.doUploadFail(o,e,t)}))})}},{key:"initTimeLock",value:function(){var t=this;this.failTimer||(this.failTimer=setTimeout(function(){t.failTime=0,t.failTimer=null},6e4))}},{key:"doUploadFail",value:function(t,e,n){this.failTime++,e.forEach(function(t){t.sendState="RESEND"}),l.webPrint.initLogLevel(t),l.webPrint.error("LTS.0300|Request failed，response code=".concat(n.status||"--","，error=").concat(n.statusText||"send fail","."))}},{key:"doUploadSuccess",value:function(t,e){this.failTime=0,t.forEach(function(t){t.sendState="SUCCESS"}),l.webPrint.initLogLevel(e),l.webPrint.log("senddata success.")}},{key:"logUploadPolicyThreshold",value:function(t,e){t.cacheThreshold&&e.filter(function(t){return"READY"===t.sendState||"RESEND"===t.sendState}).length>=t.cacheThreshold&&this.uploadData(t,e)}},{key:"writeLog",value:function(e,n,t,r){var o=this;t&&(0,u.checkLabels)(r)&&(Array.isArray(t)?t.forEach(function(t){o.addLogToList(e,n,t,r)}):this.addLogToList(e,n,t,r))}},{key:"addLogToList",value:function(t,e,n,r){(0,u.checkObjectTypeAndLength)(n,"content",300)?(r={labels:JSON.stringify(r,this.censor),content:this.formatLogContent(n),sendState:"READY",log_time_ns:(new Date).getTime()},e.push(r),this.logUploadPolicyThreshold(t,e)):(l.webPrint.initLogLevel(t),l.webPrint.warn("LTS.0002|Log content is invalid."))}},{key:"formatLogContent",value:function(t){var e=JSON.stringify(t,this.censor);return 30720<(null==e?void 0:e.length)?{content:e.substring(0,30720)}:t}}])&&o(e.prototype,n),r&&o(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.default=new n},function(t,e,n){"use strict";n.r(e),n.d(e,"__extends",function(){return o}),n.d(e,"__assign",function(){return i}),n.d(e,"__rest",function(){return a}),n.d(e,"__decorate",function(){return c}),n.d(e,"__param",function(){return u}),n.d(e,"__metadata",function(){return l}),n.d(e,"__awaiter",function(){return f}),n.d(e,"__generator",function(){return s}),n.d(e,"__createBinding",function(){return d}),n.d(e,"__exportStar",function(){return h}),n.d(e,"__values",function(){return p}),n.d(e,"__read",function(){return y}),n.d(e,"__spread",function(){return b}),n.d(e,"__spreadArrays",function(){return g}),n.d(e,"__await",function(){return v}),n.d(e,"__asyncGenerator",function(){return m}),n.d(e,"__asyncDelegator",function(){return w}),n.d(e,"__asyncValues",function(){return S}),n.d(e,"__makeTemplateObject",function(){return L}),n.d(e,"__importStar",function(){return O}),n.d(e,"__importDefault",function(){return j}),n.d(e,"__classPrivateFieldGet",function(){return P}),n.d(e,"__classPrivateFieldSet",function(){return _});
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */
var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]}instanceof Array?function(t,e){t.__proto__=e}:function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])}))(t,e)};function o(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var i=function(){return(i=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function a(t,e){var n={};for(o in t)Object.prototype.hasOwnProperty.call(t,o)&&e.indexOf(o)<0&&(n[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)e.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(n[o[r]]=t[o[r]]);return n}function c(t,e,n,r){var o,i=arguments.length,a=i<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,r);else for(var c=t.length-1;0<=c;c--)(o=t[c])&&(a=(i<3?o(a):3<i?o(e,n,a):o(e,n))||a);return 3<i&&a&&Object.defineProperty(e,n,a),a}function u(n,r){return function(t,e){r(t,e,n)}}function l(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function f(t,a,c,u){return new(c=c||Promise)(function(n,e){function r(t){try{i(u.next(t))}catch(t){e(t)}}function o(t){try{i(u.throw(t))}catch(t){e(t)}}function i(t){var e;t.done?n(t.value):((e=t.value)instanceof c?e:new c(function(t){t(e)})).then(r,o)}i((u=u.apply(t,a||[])).next())})}function s(r,o){var i,a,c,u={label:0,sent:function(){if(1&c[0])throw c[1];return c[1]},trys:[],ops:[]},t={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(t[Symbol.iterator]=function(){return this}),t;function e(n){return function(t){var e=[n,t];if(i)throw new TypeError("Generator is already executing.");for(;u;)try{if(i=1,a&&(c=2&e[0]?a.return:e[0]?a.throw||((c=a.return)&&c.call(a),0):a.next)&&!(c=c.call(a,e[1])).done)return c;switch(a=0,(e=c?[2&e[0],c.value]:e)[0]){case 0:case 1:c=e;break;case 4:return u.label++,{value:e[1],done:!1};case 5:u.label++,a=e[1],e=[0];continue;case 7:e=u.ops.pop(),u.trys.pop();continue;default:if(!(c=0<(c=u.trys).length&&c[c.length-1])&&(6===e[0]||2===e[0])){u=0;continue}if(3===e[0]&&(!c||e[1]>c[0]&&e[1]<c[3]))u.label=e[1];else if(6===e[0]&&u.label<c[1])u.label=c[1],c=e;else{if(!(c&&u.label<c[2])){c[2]&&u.ops.pop(),u.trys.pop();continue}u.label=c[2],u.ops.push(e)}}e=o.call(r,u)}catch(t){e=[6,t],a=0}finally{i=c=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}}}function d(t,e,n,r){t[r=void 0===r?n:r]=e[n]}function h(t,e){for(var n in t)"default"===n||e.hasOwnProperty(n)||(e[n]=t[n])}function p(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return{value:(t=t&&r>=t.length?void 0:t)&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function y(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,i=n.call(t),a=[];try{for(;(void 0===e||0<e--)&&!(r=i.next()).done;)a.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return a}function b(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(y(arguments[e]));return t}function g(){for(var t=0,e=0,n=arguments.length;e<n;e++)t+=arguments[e].length;for(var r=Array(t),o=0,e=0;e<n;e++)for(var i=arguments[e],a=0,c=i.length;a<c;a++,o++)r[o]=i[a];return r}function v(t){return this instanceof v?(this.v=t,this):new v(t)}function m(t,e,n){var o,i,a;if(Symbol.asyncIterator)return o=n.apply(t,e||[]),i=[],a={},r("next"),r("throw"),r("return"),a[Symbol.asyncIterator]=function(){return this},a;throw new TypeError("Symbol.asyncIterator is not defined.");function r(r){o[r]&&(a[r]=function(n){return new Promise(function(t,e){1<i.push([r,n,t,e])||c(r,n)})})}function c(t,e){try{(n=o[t](e)).value instanceof v?Promise.resolve(n.value.v).then(u,l):f(i[0][2],n)}catch(t){f(i[0][3],t)}var n}function u(t){c("next",t)}function l(t){c("throw",t)}function f(t,e){t(e),i.shift(),i.length&&c(i[0][0],i[0][1])}}function w(r){var o,t={};return e("next"),e("throw",function(t){throw t}),e("return"),t[Symbol.iterator]=function(){return this},t;function e(e,n){t[e]=r[e]?function(t){return(o=!o)?{value:v(r[e](t)),done:"return"===e}:n?n(t):t}:n}}function S(a){var t,e;if(Symbol.asyncIterator)return(t=a[Symbol.asyncIterator])?t.call(a):(a=p(a),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);throw new TypeError("Symbol.asyncIterator is not defined.");function n(i){e[i]=a[i]&&function(o){return new Promise(function(t,e){var n,r;o=a[i](o),n=t,t=e,r=o.done,e=o.value,Promise.resolve(e).then(function(t){n({value:t,done:r})},t)})}}}function L(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}function O(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}function j(t){return t&&t.__esModule?t:{default:t}}function P(t,e){if(e.has(t))return e.get(t);throw new TypeError("attempted to get private field on non-instance")}function _(t,e,n){if(e.has(t))return e.set(t,n),n;throw new TypeError("attempted to set private field on non-instance")}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(10);e.default=function(t,e,n){return(new r.default).webRequest(t,e,n)}},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0});var a=n(2),n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function")}var e,n,r;return e=t,(n=[{key:"webRequest",value:function(t,e,n){return n.url?(e=(0,a.getHeader)(e),null!==window&&void 0!==window&&window.XMLHttpRequest?this.sendByAjax(t,e,n):"undefined"!=typeof fetch?this.sendByBeacon(t,e,n):Promise.reject(new Error("not supported"))):Promise.reject(new Error("check the config."))}},{key:"sendByBeacon",value:function(t,r,e){var o;return"undefined"!=typeof fetch?(o=null==e?void 0:e.url,new Promise(function(e,n){fetch(o,{method:"POST",body:JSON.stringify(t),headers:r}).then(function(t){200===t.status?e():n(t)}).catch(function(t){n(t)})})):Promise.reject(new Error("missing fetch method"))}},{key:"sendByAjax",value:function(s,d,h){return new Promise(function(t,e){var n=null==h?void 0:h.url;if(window.XMLHttpRequest&&n){var r=new XMLHttpRequest;if(r.timeout=15e3,r.onabort=function(){e(new Error("The request was aborted!"))},r.onerror=function(){e(new Error("An error occurred during the transaction!"))},r.ontimeout=function(){e(new Error("Timeout!"))},r.onload=function(){t()},h&&"get"===String(h.method).toLocaleLowerCase()){r.open("GET",n+"?"+s,!0);for(var o=0,i=Object.keys(d);o<i.length;o++){var a=i[o];r.setRequestHeader(a,d[a])}r.send()}else{var c=h&&h.async;r.open("POST",n,null==c||c);for(var u=0,l=Object.keys(d);u<l.length;u++){var f=l[u];r.setRequestHeader(f,d[f])}r.send(JSON.stringify(s))}}else e(new Error("url is missing"))})}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.default=n},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0}),e.UUID=void 0;var r=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function")}var e,n,r;return e=t,(n=[{key:"create",value:function(){var t=Math.floor(Math.random()*(Math.pow(2,12)-1+1))+0,e=Math.floor(Math.random()*(Math.pow(2,32)-1+1))+0,n=Math.floor(Math.random()*(Math.pow(2,16)-1+1))+0,r=Math.floor(Math.random()*(Math.pow(2,6)-1+1))+0,o=Math.floor(Math.random()*(Math.pow(2,8)-1+1))+0,i=(0|Math.random()*(1<<30))+(0|Math.random()*(1<<18))*(1<<30);function a(t,e,n){n=n||"0";for(var r=e-(t=String(t)).length;0<r;r>>>=1,n+=n)1&r&&(t=n+t);return t}return[a(e.toString(16),8),a(n.toString(16),4),a((16384|t).toString(16),4),a((128|r).toString(16),2),a(o.toString(16),2),a(i.toString(16),12)].join("")}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.UUID=new r},function(t){t.exports=JSON.parse('{"name":"@cloud/lts-web-sdk","version":"1.0.15","description":"lts-web-sdk","main":"./websdk.min.js","license":"MIT","scripts":{"debug":"http-serve -p 8080","build":"rimraf dist && cross-env NODE_ENV=development webpack --config webpack.config.js","build:prod":"rimraf dist && tsc -p tsconfig.json && cross-env NODE_ENV=production webpack --config webpack.config.js","lint":"eslint --ext .js src/","jest":"jest --coverage --watch","fix-lint":"eslint --ext .js src/ --fix","build:baidu":"rimraf lib && npx babel src --out-dir dist/src","build:bd":"rimraf dist && tsc -p tsconfig.json && cross-env NODE_ENV=production webpack --config webpack.config.js","build:cdn":"rimraf dist && tsc -p tsconfig.json && cross-env NODE_ENV=production webpack --config webpack-cdn.config.js","tsc":"tsc -p tsconfig.json","test":"mocha --exit","test-report":"tsc -p tsconfig.json && nyc --reporter=html mocha --exit"},"devDependencies":{"@babel/cli":"^7.4.3","@babel/core":"^7.4.3","@babel/preset-env":"^7.4.3","@cloud/eslint-config-cbc":"^1.7.3","babel-eslint":"^10.1.0","babel-loader":"^8.0.5","babel-plugin-syntax-dynamic-import":"^6.18.0","cross-env":"^5.2.1","eslint":"^6.1.0","http-serve":"^1.0.1","jest":"^27.4.5","mocha":"^10.2.0","nyc":"^15.1.0","sinon":"^15.2.0","uglifyjs-webpack-plugin":"^2.1.2","webpack":"^4.29.6","webpack-cli":"^3.3.0"}}')},function(t,e,n){"use strict";function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,function(t){t=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0===n)return("string"===e?String:Number)(t);n=n.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}(t,"string");return"symbol"===o(t)?t:String(t)}(r.key),r)}}Object.defineProperty(e,"__esModule",{value:!0});var a=n(1),c=n(3),u=n(0),n=function(){function t(){var e=this;if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.checkConfig=function(t){return!!t&&e.checkReportPolices(t)&&e.checkLogConfig(t)&&e.checkUrl(t)&&e.checkRegion(t)&&e.checkProjectIdAndDebug(t)&&(0,a.checkConfigKey)(t)},this.checkProjectIdAndDebug=function(t){return null!=t&&t.debug&&"boolean"!=typeof t.debug?(u.webPrint.warn("LTS.0002|Debug value is invalid."),!1):(t=null==t?void 0:t.projectId,!(!(0,a.isValidString)(t,"projectId")||!(0,a.checkLogParamString)("projectId",t,128)))},this.checkRegion=function(t){return!(!(0,a.checkLogParamString)("region",null==t?void 0:t.region,128)||!c.REGIONS[null==t?void 0:t.region]&&(u.webPrint.warn("LTS.0009|Unsupported region."),1))},this.checkUrl=function(t){return!(null!=t&&t.url&&!(0,a.checkLogParamString)("url",null==t?void 0:t.url,128)||null!=t&&t.url&&("string"!=typeof(null==t?void 0:t.url)||!/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]+$/.test(null==t?void 0:t.url))&&(u.webPrint.warn("LTS.0006|Url doesn't match pattern."),1))},this.checkLogConfig=function(t){var e=null==t?void 0:t.groupId;return!!((0,a.isValidString)(e,"groupId")&&(0,a.checkLogParamString)("groupId",e,128)&&(e=null==t?void 0:t.streamId,(0,a.isValidString)(e,"streamId"))&&(0,a.checkLogParamString)("streamId",e,128))}}var e,n,r;return e=t,(n=[{key:"updateUrl",value:function(t){var e=null==t?void 0:t.url,e=(null!=(e=e||"https://lts-access.".concat(t.region,".myhuaweicloud.com"))?e:"").split(",")[0];return"".concat(e,"/v3/").concat(t.projectId,"/lts/groups/").concat(t.groupId,"/streams/").concat(t.streamId,"/logs")}},{key:"checkReportPolices",value:function(t){if(t)return this.checkThreshold(t,"timeThreshold",c.POLICY_CONSTANTS.TIME_MIN,c.POLICY_CONSTANTS.TIME_MAX,"timeInterval")&&this.checkThreshold(t,"cacheThreshold",c.POLICY_CONSTANTS.THRESHOLD_MIN,c.POLICY_CONSTANTS.THRESHOLD_MAX)}},{key:"checkThreshold",value:function(t,e,n,r,o){var i=t[e];if(void 0===i)t[e]=30;else{if("number"!=typeof i)return u.webPrint.warn("LTS.0002|".concat(o||e," is invalid.")),!1;if(i<n||r<i)return u.webPrint.warn("LTS.0004|The value of ".concat(o||e," must be between ").concat(n," and ").concat(r,".")),!1}return!0}}])&&i(e.prototype,n),r&&i(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();e.default=new n}],r={},o.m=n,o.c=r,o.d=function(t,e,n){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(o.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)o.d(n,r,function(t){return e[t]}.bind(null,r));return n},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=4);function o(t){var e;return(r[t]||(e=r[t]={i:t,l:!1,exports:{}},n[t].call(e.exports,e,e.exports,o),e.l=!0,e)).exports}var n,r});
!function(e,t){void 0===e.csdn&&(e.csdn={}),e.csdn.reportCryptoJS=function(){var e=e||function(e,t){var o;if("undefined"!=typeof window&&window.crypto&&(o=window.crypto),"undefined"!=typeof self&&self.crypto&&(o=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(o=globalThis.crypto),!o&&"undefined"!=typeof window&&window.msCrypto&&(o=window.msCrypto),!o&&"undefined"!=typeof global&&global.crypto&&(o=global.crypto),!o&&"function"==typeof require)try{o=require("crypto")}catch(e){}var r=function(){if(o){if("function"==typeof o.getRandomValues)try{return o.getRandomValues(new Uint32Array(1))[0]}catch(e){}if("function"==typeof o.randomBytes)try{return o.randomBytes(4).readInt32LE()}catch(e){}}throw new Error("Native crypto module could not be used to get secure random number.")},n=Object.create||function(){function e(){}return function(t){var o;return e.prototype=t,o=new e,e.prototype=null,o}}(),i={},a=i.lib={},s=a.Base=function(){return{extend:function(e){var t=n(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}}}(),c=a.WordArray=s.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=void 0!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,o=e.words,r=this.sigBytes,n=e.sigBytes;if(this.clamp(),r%4)for(var i=0;i<n;i++){var a=o[i>>>2]>>>24-i%4*8&255;t[r+i>>>2]|=a<<24-(r+i)%4*8}else for(var s=0;s<n;s+=4)t[r+s>>>2]=o[s>>>2];return this.sigBytes+=n,this},clamp:function(){var t=this.words,o=this.sigBytes;t[o>>>2]&=4294967295<<32-o%4*8,t.length=e.ceil(o/4)},clone:function(){var e=s.clone.call(this);return e.words=this.words.slice(0),e},random:function(e){for(var t=[],o=0;o<e;o+=4)t.push(r());return new c.init(t,e)}}),d=i.enc={},u=d.Hex={stringify:function(e){for(var t=e.words,o=e.sigBytes,r=[],n=0;n<o;n++){var i=t[n>>>2]>>>24-n%4*8&255;r.push((i>>>4).toString(16)),r.push((15&i).toString(16))}return r.join("")},parse:function(e){for(var t=e.length,o=[],r=0;r<t;r+=2)o[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new c.init(o,t/2)}},f=d.Latin1={stringify:function(e){for(var t=e.words,o=e.sigBytes,r=[],n=0;n<o;n++){var i=t[n>>>2]>>>24-n%4*8&255;r.push(String.fromCharCode(i))}return r.join("")},parse:function(e){for(var t=e.length,o=[],r=0;r<t;r++)o[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new c.init(o,t)}},l=d.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},p=a.BufferedBlockAlgorithm=s.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var o,r=this._data,n=r.words,i=r.sigBytes,a=this.blockSize,s=4*a,d=i/s;d=t?e.ceil(d):e.max((0|d)-this._minBufferSize,0);var u=d*a,f=e.min(4*u,i);if(u){for(var l=0;l<u;l+=a)this._doProcessBlock(n,l);o=n.splice(0,u),r.sigBytes-=f}return new c.init(o,f)},clone:function(){var e=s.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),g=(a.Hasher=p.extend({cfg:s.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,o){return new e.init(o).finalize(t)}},_createHmacHelper:function(e){return function(t,o){return new g.HMAC.init(e,o).finalize(t)}}}),i.algo={});return i}(Math);return e}()}(window),function(e,t){!function(e){(function(t){function o(e,t,o,r,n,i,a){var s=e+(t&o|~t&r)+n+a;return(s<<i|s>>>32-i)+t}function r(e,t,o,r,n,i,a){var s=e+(t&r|o&~r)+n+a;return(s<<i|s>>>32-i)+t}function n(e,t,o,r,n,i,a){var s=e+(t^o^r)+n+a;return(s<<i|s>>>32-i)+t}function i(e,t,o,r,n,i,a){var s=e+(o^(t|~r))+n+a;return(s<<i|s>>>32-i)+t}var a=e,s=a.lib,c=s.WordArray,d=s.Hasher,u=a.algo,f=[];!function(){for(var e=0;e<64;e++)f[e]=4294967296*t.abs(t.sin(e+1))|0}();var l=u.MD5=d.extend({_doReset:function(){this._hash=new c.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var a=0;a<16;a++){var s=t+a,c=e[s];e[s]=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8)}var d=this._hash.words,u=e[t+0],l=e[t+1],p=e[t+2],g=e[t+3],m=e[t+4],h=e[t+5],w=e[t+6],_=e[t+7],v=e[t+8],C=e[t+9],x=e[t+10],y=e[t+11],k=e[t+12],S=e[t+13],b=e[t+14],E=e[t+15],I=d[0],O=d[1],T=d[2],L=d[3];I=o(I,O,T,L,u,7,f[0]),L=o(L,I,O,T,l,12,f[1]),T=o(T,L,I,O,p,17,f[2]),O=o(O,T,L,I,g,22,f[3]),I=o(I,O,T,L,m,7,f[4]),L=o(L,I,O,T,h,12,f[5]),T=o(T,L,I,O,w,17,f[6]),O=o(O,T,L,I,_,22,f[7]),I=o(I,O,T,L,v,7,f[8]),L=o(L,I,O,T,C,12,f[9]),T=o(T,L,I,O,x,17,f[10]),O=o(O,T,L,I,y,22,f[11]),I=o(I,O,T,L,k,7,f[12]),L=o(L,I,O,T,S,12,f[13]),T=o(T,L,I,O,b,17,f[14]),O=o(O,T,L,I,E,22,f[15]),I=r(I,O,T,L,l,5,f[16]),L=r(L,I,O,T,w,9,f[17]),T=r(T,L,I,O,y,14,f[18]),O=r(O,T,L,I,u,20,f[19]),I=r(I,O,T,L,h,5,f[20]),L=r(L,I,O,T,x,9,f[21]),T=r(T,L,I,O,E,14,f[22]),O=r(O,T,L,I,m,20,f[23]),I=r(I,O,T,L,C,5,f[24]),L=r(L,I,O,T,b,9,f[25]),T=r(T,L,I,O,g,14,f[26]),O=r(O,T,L,I,v,20,f[27]),I=r(I,O,T,L,S,5,f[28]),L=r(L,I,O,T,p,9,f[29]),T=r(T,L,I,O,_,14,f[30]),O=r(O,T,L,I,k,20,f[31]),I=n(I,O,T,L,h,4,f[32]),L=n(L,I,O,T,v,11,f[33]),T=n(T,L,I,O,y,16,f[34]),O=n(O,T,L,I,b,23,f[35]),I=n(I,O,T,L,l,4,f[36]),L=n(L,I,O,T,m,11,f[37]),T=n(T,L,I,O,_,16,f[38]),O=n(O,T,L,I,x,23,f[39]),I=n(I,O,T,L,S,4,f[40]),L=n(L,I,O,T,u,11,f[41]),T=n(T,L,I,O,g,16,f[42]),O=n(O,T,L,I,w,23,f[43]),I=n(I,O,T,L,C,4,f[44]),L=n(L,I,O,T,k,11,f[45]),T=n(T,L,I,O,E,16,f[46]),O=n(O,T,L,I,p,23,f[47]),I=i(I,O,T,L,u,6,f[48]),L=i(L,I,O,T,_,10,f[49]),T=i(T,L,I,O,b,15,f[50]),O=i(O,T,L,I,h,21,f[51]),I=i(I,O,T,L,k,6,f[52]),L=i(L,I,O,T,g,10,f[53]),T=i(T,L,I,O,x,15,f[54]),O=i(O,T,L,I,l,21,f[55]),I=i(I,O,T,L,v,6,f[56]),L=i(L,I,O,T,E,10,f[57]),T=i(T,L,I,O,w,15,f[58]),O=i(O,T,L,I,S,21,f[59]),I=i(I,O,T,L,m,6,f[60]),L=i(L,I,O,T,y,10,f[61]),T=i(T,L,I,O,p,15,f[62]),O=i(O,T,L,I,C,21,f[63]),d[0]=d[0]+I|0,d[1]=d[1]+O|0,d[2]=d[2]+T|0,d[3]=d[3]+L|0},_doFinalize:function(){var e=this._data,o=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;o[n>>>5]|=128<<24-n%32;var i=t.floor(r/4294967296),a=r;o[15+(n+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),o[14+(n+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),e.sigBytes=4*(o.length+1),this._process();for(var s=this._hash,c=s.words,d=0;d<4;d++){var u=c[d];c[d]=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8)}return s},clone:function(){var e=d.clone.call(this);return e._hash=this._hash.clone(),e}});a.MD5=d._createHelper(l),a.HmacMD5=d._createHmacHelper(l)})(Math),e.MD5}(e.csdn.reportCryptoJS)}(window),function(e){var t,o,r,n,i,a,s,c,d,u,f,l,p,g,m,h;i=[],a=[],t={DELAY:500,DELAY_LTS_VIEW:200,API_VERSION:"0.6.0",SERVER_URL:"https://event.csdn.net/"},l=["utm_source"],t.SERVER_URL,t.SERVER_URL,t.API_VERSION,t.SERVER_URL,t.API_VERSION,t.SERVER_URL,n={SCROLL:"scroll",PV:"pv",VIEW:"view",DELAY_VIEW:"delay_view",CLICK:"click"},c={SKIPPED_AND_VISIBLE:"0",VISIBLE:"1"};var w={region:"cn-north-4",projectId:"06981375190026432f77c01bfca33e32",groupId:"dadde766-b087-42da-8e67-d2499a520ee7",tags:{userAgent:window.navigator.userAgent}},_={region:w.region,projectId:w.projectId,groupId:w.groupId,url:"https://eva2.csdn.net",debug:!0,logLevel:"ERROR",cacheThreshold:30,timeThreshold:2},v={PV:"5da0dae3-481b-478b-92ca-3caa293e4a29",CLICK:"099503df-60e8-4167-9a96-d6fb66f3fd01",VIEW:"ea448b34-0491-4546-87aa-059120b54c53",MOUSE:"a0119567-bf91-4314-ab75-f683ba6c0c0a"},C={PV:Object.assign({},w,{streamId:v.PV}),CLICK:Object.assign({},w,{streamId:v.CLICK}),VIEW:Object.assign({},w,{streamId:v.VIEW}),MOUSE:Object.assign({},w,{streamId:v.MOUSE})},x={PV:new LTS_WEB_SDK(Object.assign({},_,{streamId:v.PV})),CLICK:new LTS_WEB_SDK(Object.assign({},_,{streamId:v.CLICK})),VIEW:new LTS_WEB_SDK(Object.assign({},_,{streamId:v.VIEW})),MOUSE:new LTS_WEB_SDK(Object.assign({},_,{streamId:v.MOUSE}))},y={agcgw:{backurl:"eva.csdn.net",url:"eva.csdn.net"},agcgw_all:{CN:"eva.csdn.net",CN_back:"eva.csdn.net"},client:{cp_id:"2850086000385095620",product_id:"3629892982708167306",client_id:"616369439914343296",client_secret:"30380A55D88C6AB552ABEF7E0A7202B4CC2832366DDB85D2EAB4F07F45AF0641",project_id:"3629892982708167306",app_id:"243650030996486022",api_key:"CwEAAAAAmqEcCqKqyJnZCn/9gY9gr9axVLJKF2NCx9xcNUvx5gIc6VMyzUSH7tZvGnbf8qSf0W0vrUvQ4whevbzkbxJDuFZFlcA="},oauth_client:{client_id:"107532021",client_type:7},app_info:{app_id:"243650030996486022"},service:{analytics:{collector_url:"ev.csdn.net,ev.csdn.net",resource_id:"p1",channel_id:""}},region:"CN",configuration_version:"3.0"},k={region:w.region,projectId:w.projectId,groupId:w.groupId,tags:{userAgent:window.navigator.userAgent}},S={PV:"f5e1a97a-cfee-4925-aca4-63a2a882f03b",CLICK:"5b9d7a3e-e8f7-44e1-93ab-751c02ed7e10",VIEW:"f8e601e5-6b79-4d4d-8022-5b7c0650aa24",MOUSE:"9c85bfea-37fe-493a-abe2-5f8b6e6a73e3"},b={PV:Object.assign({},k,{streamId:S.PV}),CLICK:Object.assign({},k,{streamId:S.CLICK}),VIEW:Object.assign({},k,{streamId:S.VIEW}),MOUSE:Object.assign({},k,{streamId:S.MOUSE})};if(r={isCSDNApp:function(){var e=!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i),t=r.getCookie("X-App-ID")||"";return e||"CSDN-APP"==t||"CSDN-EDU"==t},isMobile:function(){var e=/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone|csdn)/i.test(navigator.userAgent);return/(MicroMessenger)/i.test(navigator.userAgent)?!/(WindowsWechat|MacWechat)/i.test(navigator.userAgent):e},guid:function(){return+new Date+"-xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})},setLogIdCookie:function(e){if(-1!==["pv","click","view"].indexOf(e)){var t="log_Id_"+e,o=r.getCookie(t)||0;try{o=parseInt(o),"number"==typeof o&&isNaN(o)||o>=1e8?r.setCookie(t,1,31536e7):r.setCookie(t,++o,31536e7)}catch(e){void 0}}},getRequest:function(){for(var e=new Object,t=window.location.href.split("?")[1]||"",o=t.split("&"),r=0;r<o.length;r++){var n=o[r].split("=")[0],i=o[r].split("=")[1];n&&i&&(e[n]=unescape(i))}return e},initUTM:function(){f={};var e=r.getRequest(),t=r.isCSDNApp();if("{}"!==JSON.stringify(e)){for(var o in e)0==o.indexOf("utm_")&&e.hasOwnProperty(o)&&(void 0,0==o.indexOf("utm_source")?(r.setCookie("c_"+o,e[o],864e5),t&&r.setCookie(o+"_app",e[o],864e5)):(r.setCookie("c_"+o,e[o],36e5),t&&r.setCookie(o+"_app",e[o],36e5)));for(var o in l)if(l.hasOwnProperty(o)){var n=l[o],i=e[l[o]];i?(r.setCookie(n,i,36e5),f[n]=i):f[n]=""}}else for(var o in l)if(l.hasOwnProperty(o)){var n=l[o],i=r.getCookie(n);f[n]=i}return f},initTraceInfo:function(){for(var e=["blog","bbs","download","ask","edu","biwen"],t=0;t<e.length;t++)window.location.host.indexOf(e[t]+".csdn.net")>-1&&(r.setCookie("c_page_id","",-1),r.setCookie("c_mod","",-1))},preserveTraceInfo:function(e){e.mod&&r.setCookie("c_mod",e.mod,36e5),e.page_id?r.setCookie("c_page_id",e.page_id,36e5):r.setCookie("c_page_id","default",36e5)},getTimestamp:function(){return Math.round(new Date/1e3)},getXPath:function(e){if(""!==e.id)return'//*[@id="'+e.id+'"]';if(e==document.body)return"/html/"+e.tagName.toLowerCase();if(!e.parentNode)return"";for(var t=1,o=e.parentNode.childNodes,r=0,n=o.length;r<n;r++){var i=o[r];if(i==e)return arguments.callee(e.parentNode)+"/"+e.tagName.toLowerCase()+"["+t+"]";1==i.nodeType&&i.tagName==e.tagName&&t++}},getScreen:function(){return window.screen.width+"*"+window.screen.height},getCookie:function(e){var t,o=new RegExp("(^| )"+e+"=([^;]*)(;|$)");return(t=document.cookie.match(o))?unescape(t[2]):""},getFuzzyCookie:function(e){var t,o=new RegExp(e+"[A-Za-z0-9_]+=([^;]*);","ig");return(t=document.cookie.match(o))?t.join(""):""},checkoutUtm:function(){var e=[],t=[],o=window.location.href.split("?")[1]||"";if(o.length){e=o.split("&");for(var r=0;r<e.length;r++)0==e[r].indexOf("utm_")&&t.push(e[r].split("=")[0])}return t},setCookie:function(e,t,o){var r=new Date;r.setTime(r.getTime()+o),document.cookie=e+"="+escape(t)+";expires="+r.toGMTString()+";path=/ ; domain=."+this.topDomain(window.location.host)},setUserSegment:function(){var e=(null!=(_ref1=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?_ref1[3]:void 0)||"",t=e?e.substring(e.length-6)%16:0;r.setCookie("c_segment",t)},setfirstPageInfo:function(){if(r.getCookie("c_first_ref")&&r.getCookie("c_first_ref").indexOf(".csdn.net")>-1)return void r.setCookie("c_first_ref","default");var e=new RegExp(/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/),t=window.document.referrer?window.document.referrer.match(e)[0]:"default";[".csdn.net","wx.tenpay.com","graph.qq.com","openapi.baidu.com","api.weibo.com","account.dcloud.net.cn/oauth","github.com/login/oauth","passport.gitcode.net"].some(function(e){return e.indexOf("/")>-1?(window.document.referrer||"default").indexOf(e)>-1:t.indexOf(e)>-1})&&(t="default");var o="11_"+(new Date).getTime()+"."+r.randomNum(6);return"default"!=t?(r.setCookie("c_first_ref",t),r.setCookie("c_first_page",window.location.href),void r.setCookie("c_dsid",o,18e5)):r.getCookie("c_first_ref")&&window.document.referrer?void 0:(r.setCookie("c_first_ref","default"),r.setCookie("c_first_page",window.location.href),void r.setCookie("c_dsid",o,18e5))},randomNum:function(e){for(var t=[],o=["0","1","2","3","4","5","6","7","8","9"],r=0;r<e;r++){var n=Math.floor(10*Math.random());t[r]=o[n]}return t.join("")},initDefaultCookie:function(){var e,t=(null!=(e=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?e[3]:void 0)||"",o=r.getCookie("dc_session_id"),n=r.getCookie("c_dsid");t||(t="11_"+r.randomNum(11)+"-"+(new Date).getTime()+"-"+r.randomNum(6),r.setCookie("uuid_tt_dd",t,15768e7)),o||(o="11_"+(new Date).getTime()+"."+r.randomNum(6),r.setCookie("dc_session_id",o,18e5)),n||(n="11_"+(new Date).getTime()+"."+r.randomNum(6),r.setCookie("c_dsid",n,18e5))},refreshDcSessionId:function(){var e=r.getCookie("dc_session_id");e&&r.setCookie("dc_session_id",e,18e5);var t=r.getCookie("c_dsid");return t&&r.setCookie("c_dsid",t,18e5),e},cCookieAppSuffix:function(){var e=r.getFuzzyCookie("c_dl"),t=r.isCSDNApp();if(t){!window.document.referrer&&r.getCookie("utm_medium_app")&&r.setCookie("c_utm_medium",r.getCookie("utm_medium_app"),36e5)}if(t&&e)try{for(var o=e.split(";").map(function(e){return e?e.split("=")[0]:""}).filter(function(e){return e}),n=0;n<o.length;){var i=o[n];(function(e,t){var o=e.length;return e.substring(o-t.length,o)===t})(i,"_app")||r.setCookie(i+"_app",r.getCookie(i),2592e6),n++}}catch(e){void 0}},initLTS:function(){if(navigator.cookieEnabled&&agconnect){agconnect.instance().configInstance(y),agconnect.analytics.InitSettings.logDisabled=!0,h=agconnect.analytics(),h.setReportPolicies({ON_SCHEDULED_TIME_POLICY:2});try{h.initPromise.then(function(){h.onReport()})}catch(e){}}},analyticsWriteLog:function(e,t,o){if(h&&e&&b[e]){if("PV"!==e){if(/spider|bot|crawler|scrapy|dnspod|ia_archiver|jiankongbao|slurp|transcoder|networkbench|oneapm|bophantomjst|bingpreview|headlesschrome/.test(window.navigator.userAgent.toLowerCase()))return}for(var r=o||["cid","uid","sid","dc_sid","did","utm","platform","un"],n={},i=JSON.parse(JSON.stringify(t)),a=0;a<r.length;a++)Object.prototype.hasOwnProperty.call(t,r[a])&&(n[r[a]]=t[r[a]],delete i[r[a]]);var s={config:Object.assign({},b[e],{tags:Object.assign({},b[e].tags,n)}),data:i};h.writeLog(s.config,JSON.stringify(s.data)),h.onReport();var c={tags:Object.assign({},C[e].tags,n),data:i};"VIEW"===e?x[e]&&x[e].report(Object.assign({},c.data,c.tags)):x[e]&&x[e].reportImmediately(Object.assign({},c.data,c.tags))}},initData:function(){r.initLTS(),r.initDefaultCookie(),r.setfirstPageInfo(),r.initTraceInfo(),r.setUserSegment();var t,o,n,i=(null!=(t=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?t[3]:void 0)||"",a=r.refreshDcSessionId();if(s={cid:i,sid:a||"",pid:window.location.host.split(".csdn.net")[0],uid:r.getCookie("UserName"),did:r.getCookie("X-Device-ID")||i||"",dc_sid:r.getCookie("dc_sid"),ref:window.document.referrer||("(null)"===r.getCookie("refer_app")?"":r.getCookie("refer_app")),curl:window.location.href,dest:"",cfg:{viewStrategy:c.VISIBLE}},n=r.initUTM(),r.cCookieAppSuffix(),e("meta[name=report]").attr("content"))try{o=JSON.parse(e("meta[name=report]").attr("content")),d=e.extend(!0,{},o);for(var u=Object.prototype.hasOwnProperty,f=["percent"],l=0;l<f.length;l++)u.call(o,f[l])&&delete o[f[l]]}catch(e){o={},d={},void 0}else o={};if(o.extra){var p=r.parseExtra(o);p&&(o.extra=p)}return s=e.extend({},s,{utm:n.utm_source},o),r.preserveTraceInfo(s),s},tos:function(){var e,t,o,r;e=+new Date/1e3|0,o=null!=(t=/\bdc_tos=([^;]*)(?:$|;)/.exec(document.cookie))?t[1]:void 0;try{r=e-parseInt(o,36)}catch(e){void 0,r=-1}return document.cookie="dc_tos="+e.toString(36)+" ; expires="+new Date(1e3*(e+14400)).toGMTString()+" ; max-age=14400 ; path=/ ; domain=."+this.topDomain(window.location.host),r},topDomain:function(e){return e.split(".").length>3?e.match(/^(?:https?:\/\/)?(?:[^@\n]+@)?(?:www\.)?([^:\/\n]+)/im)[1].split(".").slice(-3).join("."):/\.?([a-z0-9\-]+\.[a-z0-9\-]+)(:\d+)?$/.exec(e)[1]},copyArr:function(e){for(var t=[],o=0;o<e.length;o++)t.push(e[o]);return t},isView:function(e,t){var o=this;if(!e)return!1;var r=this.getElementBottom(e),n=r+e.offsetHeight;return c.VISIBLE==t?o.scrollTop()<r&&r<o.scrollTop()+o.windowHeight()||o.scrollTop()<n&&n<o.scrollTop()+o.windowHeight():c.SKIPPED_AND_VISIBLE==t?r<=o.scrollTop()+o.windowHeight()||(o.scrollTop()<r&&r<o.scrollTop()+o.windowHeight()||o.scrollTop()<n&&n<o.scrollTop()+o.windowHeight()):void 0},scrollTop:function(){return Math.max(document.body.scrollTop,document.documentElement.scrollTop)},windowHeight:function(){return"CSS1Compat"==document.compatMode?document.documentElement.clientHeight:document.body.clientHeight},getElementTop:function(t){if("undefined"!=typeof jQuery)return e(t).offset().top;var o=t.offsetTop;for(t=t.offsetParent;null!=t;)o+=t.offsetTop,t=t.offsetParent;return o},getElementBottom:function(t){if("undefined"!=typeof jQuery)return e(t).offset().top+e(t).height();var o=t.offsetTop;for(t=t.offsetParent;null!=t;)o+=t.offsetTop,t=t.offsetParent;return o},url2Obj:function(e){var t={},o=e.split("&");for(var r in o)t.hasOwnProperty(r)&&(t[o[r].split("=")[0]]=decodeURIComponent(o[r].split("=")[1]));return t},fixParamConTop:function(t,o){return t.con.split(",top_")>-1?t:(t.con=t.con+",top_"+e(o).offset().top,t)},urlParamsToObj:function(e){var t={};return e.replace(/([^=&#]+)=([^&#]*)/g,function(){t[arguments[1]]=arguments[2]}),t},objToUrlParams:function(t){var o="";return e.each(t,function(e){o+="&"+e+"="+t[e]}),o.substr(1)},trackOrderSource:function(){var e=document.referrer;if(e){var t=document.createElement("a");t.href=e;var o=["passport","order.csdn.net","wx.tenpay.com","cart.csdn.net"],n=[/(^https:\/\/mall\.csdn\.net(:[0-9]{1,5})?\/cart$)/],i=!1;try{for(var a=0;a<o.length;a++)t.hostname.indexOf(o[a])>-1&&(i=!0);for(var a=0;a<n.length;a++)n[a].test(e)&&(i=!0)}catch(e){i=!1}if(!i){if(r.getCookie("c_ref")===e)return;t.hostname.indexOf(".csdn.net")>-1?(r.setCookie("c_pref",r.getCookie("c_ref")),r.setCookie("c_ref",e)):(r.setCookie("c_pref",r.getCookie("c_ref")),r.setCookie("c_ref",e.split("?")[0]))}}else window.navigator.userAgent.toLowerCase().indexOf("csdn")<0&&(r.setCookie("c_pref","default"),r.setCookie("c_ref","default"))},parseExtra:function(e){if(!Object.prototype.hasOwnProperty.call(e,"extra"))return"";var t=Object.prototype.toString.call(e.extra).slice(8,-1);if("Object"===t)return e.extra;if("String"!==t||!e.extra)return{};try{return JSON.parse(e.extra)}catch(t){void 0}},assignExtra:function(t){if(t&&(t.extra||s.extra)){var o=r.parseExtra(t);return o=e.extend(!0,{},s.extra||{},o||{}),JSON.stringify(o)}},getSortObjectMD5:function(e){var o=JSON.parse(JSON.stringify(e));if("Object"!==Object.prototype.toString.call(o).slice(8,-1))return"";try{o.APIVersion=t.API_VERSION;var r=[];for(var n in o)o.hasOwnProperty(n)&&r.push(n);r=r.sort();var i=r.reduce(function(e,t){return e+="&"+t+"="+o[t]},"").substring(1);return csdn.reportCryptoJS.MD5(i).toString()}catch(e){return void 0,""}},reportScroll:function(t){var o=e.extend(!0,{},s,t);try{var n=r.assignExtra(t||{});n&&(o.extra=n)}catch(e){t&&t.extra&&(o.extra=t.extra),void 0}o.tos=r.tos()+"",o.adb=(r.getCookie("c_adb")||0)+"",o.curl=window.location.href;var i=r.getFuzzyCookie("c_");i&&(o.cCookie=i),o.t=r.getTimestamp()+"",o.screen=r.getScreen(),o.un=r.getCookie("UN")||r.getCookie("UserName"),o.urn=p||r.guid(),o.vType=r.getCookie("p_uid")||"",Object.prototype.hasOwnProperty.call(o,"eleTop")&&(o.eleTop=o.eleTop+""),delete o.cfg,delete o.dest;var a={__source__:"csdn",__logs__:[o]},c=window.navigator.userAgent,d="PC";c.toLowerCase().indexOf("csdnedu")>-1?d="CSDNEDU":c.toLowerCase().indexOf("csdnapp")>-1?d="CSDNApp":c.toLowerCase().indexOf("mobile")>-1&&(d="mobile"),a.__tags__={useragent:c,platform:d}},windowFocusChange:function(e){"loading"!==document.readyState?g=document.hasFocus():window.addEventListener("DOMContentLoaded",function(){g=document.hasFocus(),e()},!0);var t=function(o){o.target===window&&(g=!0,e()),window.removeEventListener("focus",t,!0)};return window.addEventListener("focus",t,!0),g&&e(),g},throttleTrailing:function(e,t){var o=0,r=null;return function(){var n=Date.now(),i=this,a=arguments;n-o>t?(e.apply(i,a),o=n,r=null):(r&&(clearTimeout(r),r=null),r=setTimeout(function(){var n=Date.now();n-o>t&&(e.apply(i,a),o=n,r=null)},t-(n-o)))}},mousePositionChange:function(){var t={x:0,y:0,ev:"move"},o={x:0,y:0,ev:"click"},i=function(t){var o=e.extend(!0,{},s,t);try{var i=r.assignExtra(t||{});i&&(o.extra=i)}catch(e){t&&t.extra&&(o.extra=t.extra)}o.tos=r.tos(),o.adb=r.getCookie("c_adb")||0,o.curl=window.location.href;var a=r.getFuzzyCookie("c_");a&&(o.cCookie=a),o.t=r.getTimestamp(),o.screen=r.getScreen(),o.un=r.getCookie("UN")||r.getCookie("UserName"),o.urn=p,o.vType=r.getCookie("p_uid")||"",delete o.cfg,delete o.dest,o.log_id=r.getCookie("log_Id_"+n.PV),o.sign=r.getSortObjectMD5(o),r.analyticsWriteLog("MOUSE",o)};r.isMobile()?e(document).on("touchmove",r.throttleTrailing(function(e){var o=~~e.originalEvent.changedTouches[0].clientX,r=~~e.originalEvent.changedTouches[0].clientY;o===t.x&&r===t.y||(t.x=o,t.y=r,i({extra:JSON.stringify(t)}))},1e4)):e(document).on("mousemove",r.throttleTrailing(function(e){e.clientX===t.x&&e.clientY===t.y||(t.x=e.clientX,t.y=e.clientY,i({extra:JSON.stringify(t)}))},1e4)),e(document).on("click",r.throttleTrailing(function(e){e.clientX===o.x&&e.clientY===o.y||(o.x=e.clientX,o.y=e.clientY,i({extra:JSON.stringify(o)}))},1e4))}},o={timer:0,timerLts:0,checkTimer:0,getFullSpm:function(e){var t=e.split(".").length;if(2===t||3===t){var o=document.querySelector('meta[name="report"]'),r=o&&o.getAttribute("content")||"{}",n=JSON.parse(r);return n.spm?n.spm+"."+e:e}return e},reportUserAction:function(t,o){var n=this;t=t||["1px"],m=!1,r.windowFocusChange(function(){g&&!m&&(r.reportScroll({eleTop:1}),m=!0)}),e(function(){setTimeout(function(){n.reportPercent(function(e){m||(r.reportScroll({eleTop:e}),m=!0)},{scrollBar:!0,range:t,el:o||"",scope:"tenPrecentScrollEvent"})},800)})},reportPercent:function(t,o){o=o||{};for(var n=o.range||[25,50,75,100],i={},a=0;a<n.length;a++)i[n[a]]=!1;o.scope=o.scope||"precentScrollEvent",!o.el&&window[o.scope]&&e(o.el||window).off("scroll",window[o.scope]),window[o.scope]=function(){var a,s=0,c=0,d=!!o.scrollBar||!1;if(o.el){var u=e(o.el);u.length&&(a=d?u.scrollTop():u.scrollTop()+u.height(),s=a/u[0].scrollHeight*100)}else a=d?r.scrollTop():r.scrollTop()+r.windowHeight(),s=a/document.documentElement.scrollHeight*100;for(var f=0;f<n.length;f++){var l=n[f],p=s;if("string"==typeof l&&l.indexOf("px")>-1&&(l=+l.replace("px",""),p=a),p>=l){if(f===n.length-1){c=l;break}if(p<("string"==typeof n[f+1]&&n[f+1].indexOf("px")>-1?+n[f+1].replace("px",""):n[f+1])){c=l;break}}}if(c)for(var g=0;g<n.length;g++){var m=n[g],h="string"==typeof m&&m.indexOf("px")>-1?+m.replace("px",""):m;h<=c&&!i[m]&&(i[m]=!0,t&&t(h))}n.every(function(e){return i[e]})&&e(o.el||window).off("scroll",window[o.scope])&&delete window[o.scope]},window[o.scope](),e(o.el||window).on("scroll",window[o.scope])},reportServerLts:function(e,o){r.refreshDcSessionId();var i=this;if(e&&o){if(r.setLogIdCookie(n.VIEW),e===n.VIEW||e===n.DELAY_VIEW){var s=window.navigator.userAgent,c="PC";s.toLowerCase().indexOf("csdnedu")>-1?c="CSDNEDU":s.toLowerCase().indexOf("csdnapp")>-1?c="CSDNApp":s.toLowerCase().indexOf("mobile")>-1&&(c="mobile"),o.platform=c,o.log_id=r.getCookie("log_Id_"+n.VIEW)}var d=function(){if(a.length){for(var e=0;e<a.length;e++)r.analyticsWriteLog("VIEW",a[e]);a=[]}};"COMPLETE"===i.timerLts?(r.analyticsWriteLog("VIEW",o),d()):a.push(o),i.timerLts||(i.timerLts=setTimeout(function(){d(),clearTimeout(i.timerLts),i.timerLts="COMPLETE"},t.DELAY_LTS_VIEW))}},reportServer:function(e,t){r.refreshDcSessionId(),void 0!==e&&void 0!==t&&i.push(t);var o=r.copyArr(i);if(0!=o.length){i=[];var a={__source__:"csdn",__logs__:o};if(r.setLogIdCookie(n.VIEW),e===n.VIEW||e===n.DELAY_VIEW){var s=window.navigator.userAgent,c="PC";s.toLowerCase().indexOf("csdnedu")>-1?c="CSDNEDU":s.toLowerCase().indexOf("csdnapp")>-1?c="CSDNApp":s.toLowerCase().indexOf("mobile")>-1&&(c="mobile"),a.__tags__={useragent:s,platform:c,log_id:r.getCookie("log_Id_"+n.VIEW)}}}},reportServerDelay:function(e,o){i.push(o);var r=this;r.timer&&clearTimeout(r.timer),r.timer=setTimeout(function(){r.reportServer(n.DELAY_VIEW)},t.DELAY)},reportView:function(t,o,i){if(!t)return void void 0;t.spm&&(t.spm=this.getFullSpm(t.spm));var a=e.extend(!0,{},s,t);try{var c=r.assignExtra(t);c&&(a.extra=c)}catch(e){t&&t.extra&&(a.extra=t.extra),void 0}var d=r.getFuzzyCookie("c_");a.t=r.getTimestamp()+"",a.eleTop=o?o.offset().top+"":"",delete a.cfg,d&&(a.cCookie=d),a.__time__=r.getTimestamp(),a.curl=window.location.href,a.urn=r.guid(),a.pv_urn=p,this.reportServerLts(n.VIEW,a),"function"==typeof csdn.afterReportView&&csdn.afterReportView(o,t)},reportClick:function(t,o){r.refreshDcSessionId();var i=e.extend(!0,{},s,t);t.spm||(i.spm="");try{var a=r.assignExtra(t);a&&(i.extra=a)}catch(e){t&&t.extra&&(i.extra=t.extra),void 0}i.spm=this.getFullSpm(i.spm),i.t=r.getTimestamp(),i.elePath=o?r.getXPath(o[0])+"":"",i.eleTop=void 0!==i.eleTop?i.eleTop:o?o.offset().top+"":"",i.trace&&r.preserveTraceInfo(i);var c=r.getFuzzyCookie("c_");c&&(i.cCookie=c),i.curl=window.location.href,delete i.cfg,r.setLogIdCookie(n.CLICK),i.log_id=r.getCookie("log_Id_"+n.CLICK),i.sign=r.getSortObjectMD5(i),i.pv_urn=p,r.analyticsWriteLog("CLICK",i)},reportPageView:function(t){var o=e.extend(!0,{},s,t),i=this;try{var a=r.assignExtra(t||{});a&&(o.extra=a)}catch(e){t&&t.extra&&(o.extra=t.extra),void 0}d&&d.percent&&(u&&clearTimeout(u),u=setTimeout(function(){i.reportPercent(function(e){i.reportClick({spm:"3001.7333",eleTop:e})})},1e3)),this.reportUserAction(),p=r.guid(),o.tos=r.tos(),o.adb=r.getCookie("c_adb")||0,o.curl=window.location.href;var c=r.getFuzzyCookie("c_");c&&(o.cCookie=c),o.t=r.getTimestamp(),o.screen=r.getScreen(),o.un=r.getCookie("UN")||r.getCookie("UserName"),o.urn=p,o.vType=r.getCookie("p_uid")||"",delete o.cfg,delete o.dest,r.setLogIdCookie(n.PV),o.log_id=r.getCookie("log_Id_"+n.PV),o.sign=r.getSortObjectMD5(o),r.analyticsWriteLog("PV",o)},viewCheck:function(){var t=this;clearTimeout(t.checkTimer),t.checkTimer=setTimeout(function(){r.refreshDcSessionId(),e("[data-report-view]").each(function(){var o=e(this),n=o.data("reportView"),i=e.extend({},s,n);n.spm||(i.spm=""),i.spm=t.getFullSpm(i.spm),i.curl=window.location.href,r.isView(o.get(0),i.cfg.viewStrategy)&&(csdn.report.reportView(i,o),o.removeData("reportView"),o.removeAttr("data-report-view"))})},200)},isView:function(e){return r.isView(e)},addSpmToHref:function(e){var t=e,o=this,n=t.data("reportQuery")||"",i=t.length&&t[0].hash?t[0].hash.split("#").map(function(e){return e.split("?")[0]}):[],a=i.length&&-1===i[i.length-1].indexOf("/");if(n){var s=t.attr("href"),c=s,d={};-1!==s.indexOf("?")&&(c=s.split("?")[0],d=r.urlParamsToObj(s.split("?")[1])),a&&(c=s.split("#")[0],-1!==c.indexOf("?")&&(d=Object.assign({},d,r.urlParamsToObj(c.split("?")[1])),c=s.split("?")[0]));var u=r.urlParamsToObj(n);if((n.indexOf("spm")>-1||n.indexOf("SPM")>-1)&&(u.spm=u.spm||u.SPM,u.spm=o.getFullSpm(u.spm)),a){var f=i.pop();c+=i.join("#")+"?"+r.objToUrlParams(Object.assign(d,u))+"#"+f}else c+="?"+r.objToUrlParams(Object.assign(d,u));t.attr("href",c)}}},void 0===window.csdn&&(window.csdn={}),csdn.report)return void void 0;r.trackOrderSource(),window.csdn.report=o,s=r.initData(),s.disabled||csdn.report.reportPageView(),r.mousePositionChange(),e(function(){var t=csdn.report;e(document).on("click","[data-report-click]",function(){var o=e(this).data("reportClick");t.reportClick(o,e(this))}),t.viewCheck(e("[data-report-view]")),e(window).on("scroll",function(){t.viewCheck(e("[data-report-view]"))}),e(document).on("contextmenu","a[data-report-query]",function(){t.addSpmToHref(e(this))}),e(document).on("click","a[data-report-query]",function(){t.addSpmToHref(e(this))}),e(document).on("click","a[href]",function(){var o=e(this),r=o.attr("href");if(function(e){return!(!/^https:\/\/|^http:\/\//gi.test(e)||"/"===e||e.indexOf(".csdn.net")>-1||e.indexOf(".iteye.com")>-1)}(r)){var n={mod:"1583921753_001",dest:r};t.reportClick(n,o)}})})}(jQuery);