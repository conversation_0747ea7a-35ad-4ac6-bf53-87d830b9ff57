"use strict";function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var _createClass=function(){function t(t,e){for(var n=0;n<e.length;n++){var s=e[n];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,s.key,s)}}return function(e,n,s){return n&&t(e.prototype,n),s&&t(e,s),e}}();!function(t,e){t.csdn=t.csdn||{},function(t){var e=document.createElement("link");e.rel="stylesheet",e.type="text/css",e.href="https://g.csdnimg.cn/user-accusation/1.0.5/user-accusation.css",document.getElementsByTagName("head")[0].appendChild(e)}();var n=function(){function t(e){_classCallCheck(this,t),this.option=e,this.accusationTypeList=[],this.selectId="",this.content="",this.maxlength=200,this.st_timer="",this.selectSecondId="",this.reportUrl="",this.index=0,this.isHasSecond=!1,this.accusationSecondTypeList=[],this.init()}return _createClass(t,[{key:"init",value:function(){this.getAccusationOption(),this.showInit()}},{key:"getAccusationOption",value:function(){var t=this,n={type:this.option.type,rtype:this.option.rtype};e.ajax({url:"https://mp-action.csdn.net/interact/wrapper/report/v1/api/getReportContent",type:"get",contentType:"application/json",data:n,dataType:"json",xhrFields:{withCredentials:!0},success:function(e){200===e.code&&(t.accusationTypeList=e.data,t.renderAccusation())},error:function(t){console.log("taodawang err ===>",t)}})}},{key:"renderAccusationSecond",value:function(){var t=this;if(e("#secondTypeBox").html(""),!t.accusationSecondTypeList.length)return!1;var n="";n=e("原文链接"==t.accusationSecondTypeList[0].reason?'<div class="accusation-type">\n          <h2 class="accusation-secondary-title">内容链接（必填）</h2>\n          <div class="content-input">\n            <input class="" type="text" id="reportOriginalurl" name="reportOriginalurl" placeholder="请输入被抄袭或刷量相关的内容链接">\n          </div>\n        </div>':'<div class="accusation-type">\n          <h2 class="accusation-secondary-title">请选择具体原因（选填）</h2>\n          <ul>\n            '+this.accusationSecondTypeList.map(function(t,e){return'<li class="accusation-item  accusation-item-second" data-content='+t.id+" data-index="+e+">"+t.reason+"</li>"}).join("")+"\n          </ul>\n        </div>"),e("#secondTypeBox").append(n)}},{key:"renderAccusation",value:function(){var t=this,n=this,s=e('<div class="user-accusation" id="user-accusation">\n        <div class="accusation-back"></div>\n        <div class="accusation-area">\n          <img class="accusation-close" src=https://img-home.csdnimg.cn/images/20210426071127.png />\n          <h1 class="accusation-title">举报反馈</h1>\n          <div class="accusation-type">\n            <h2 class="accusation-secondary-title">举报类型（必选）</h2>\n            <ul>\n              '+this.accusationTypeList.map(function(t,e){return'<li class="accusation-item accusation-item-first" data-content='+t.id+" data-index="+e+">"+t.content+"</li>"}).join("")+'\n            </ul>\n          </div>\n          <div id="secondTypeBox" class="accusation-second"></div>\n          <div class="accusation-reason">\n            <h2 class="accusation-secondary-title">举报详情（选填）</h2>\n            '+this.renderTextArea()+'\n          </div>\n          <div class="accusation-btn clearfix">\n            <button class="accusation-send">提交</button>\n            <button class="accusation-cancel">取消</button>\n          </div>\n        </div>\n      </div>');s.on("click",".accusation-close",function(){t.destoryAccusation()}).on("click",".accusation-cancel",function(){t.destoryAccusation()}).on("click",".accusation-send",function(){t.sendAccusationMessage()}).on("click",".accusation-type ul li",function(t){e(this).hasClass("active")||(e(this).siblings().removeClass("active"),e(this).addClass("active")),t.target.className.indexOf("accusation-item-first")>-1&&(n.selectId=e(t.target)[0].dataset.content,n.index=e(t.target)[0].dataset.index,n.accusationTypeList[n.index].reasonList&&n.accusationTypeList[n.index].reasonList.length?(n.accusationSecondTypeList=n.accusationTypeList[n.index].reasonList,n.isHasSecond=!0):(n.accusationSecondTypeList=[],n.isHasSecond=!1),n.selectSecondId="",n.renderAccusationSecond()),t.target.className.indexOf("accusation-item-second")>-1&&(n.selectSecondId=e(t.target)[0].dataset.content)}).on("input",".accusation-input > textarea",function(t){n.content=t.target.value,e(".accusation-input > p").text(n.content.length+"/"+n.maxlength)}),e("body").append(s)}},{key:"renderTextArea",value:function(){return'<div class="accusation-input" >\n        <textarea placeholder="请详细描述举报原因，我们将第一时间核实处理" maxlength='+this.maxlength+"/>\n        <p>"+(this.content.length||"0/"+this.maxlength)+"</p>\n      </div>"}},{key:"destoryAccusation",value:function(){e(".user-accusation").remove(),this.destory()}},{key:"sendAccusationMessage",value:function(){if(!this.selectId)return void this.showToast({text:"请选择举报类型",zindex:4e3});if(this.isHasSecond&&document.getElementById("reportOriginalurl")){if(this.reportUrl=document.getElementById("reportOriginalurl").value,""==this.reportUrl)return void this.showToast({text:"请填写举报链接",zindex:4e3});if(!this.validateUrl(this.reportUrl))return void this.showToast({text:"举报链接格式错误",zindex:4e3})}var t=this,n={type:this.option.type,rtype:this.option.rtype,rid:this.option.rid,reportContent:{id:this.selectId},reasonList:[{id:this.selectSecondId}],reportedName:this.option.reportedName,supplement:document.getElementById("reportOriginalurl")?this.content+"=>抄袭的地址链接："+this.reportUrl:this.content};this.option.submitOptions&&Object.assign(n,this.option.submitOptions),e.ajax({url:"https:///mp-action.csdn.net/interact/wrapper/report/v1/api/addReportResource",type:"post",contentType:"application/json",data:JSON.stringify(n),dataType:"json",xhrFields:{withCredentials:!0},crossDomain:!0,success:function(e){200===e.code&&(t.destoryAccusation(),t.option.callback&&t.option.callback())},error:function(t){console.log("taodawang err ===>",t)}})}},{key:"destory",value:function(){this.accusationTypeList=[],this.selectId="",this.content="",clearTimeout(this.st_timer),e("#report_toastBox").remove()}},{key:"showInit",value:function(){e("<div id='report_toastBox'></div>").appendTo("body").css({width:"100%",position:"fixed",left:"0",bottom:"10%","text-align":"center",display:"none"}),e("<span id='report_toastContent'></span>").appendTo("#report_toastBox").css({color:"#fff",background:"rgba(0,0,0,.8)",padding:"8px 24px","border-radius":"4px","max-width":"80%",display:"inline-block","font-size":"16px"})}},{key:"showToast",value:function(t){clearTimeout(this.st_timer),e("#report_toastBox").hide();var n=t.text,s=parseInt(t.time?t.time:2e3),i=t.speed?t.speed:"normal",a=t.bottom?t.bottom:"10%",o=t.zindex?t.zindex:"4000";e("#report_mask").css({"z-index":o-1}),e("#report_toastBox").css({"z-index":o}),e("#report_toastBox").css({bottom:a}),e("#report_toastContent").text(n),e("#report_toastBox").fadeIn(i),this.st_timer=setTimeout(function(){e("#report_toastBox").fadeOut(function(){})},s)}},{key:"validateUrl",value:function(t){return new RegExp("^(https?|http):\\/\\/[^\\/\\s$?#]+\\.[^\\/\\s?#]+(\\/[^\\/\\s?#]*)?(\\?[^#\\s]*)?(#.*)?").test(t)}}]),t}();t.csdn.feedback=function(t){new n(t)}}(window,jQuery);