"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};!function(){function e(e,a){var n=document.createElement("script");n.type="text/javascript",n.readyState?n.onreadystatechange=function(){"loaded"!=n.readyState&&"complete"!=n.readyState||(n.onreadystatechange=null,a&&a())}:n.onload=function(){a&&a()},n.src=e,document.getElementsByTagName("head")[0].appendChild(n)}function a(e){this.tabType=1,this.username=e.username,this.nickname=e.nickname,this.avatar=e.avatar,this.alreadyHaveCount=-1,this.allHaveCount=-1,this.haveListReady=this.allListReady=!1,this.options=e,this.self=this.isSelf(),this.allMedalList=[],this.haveMedalList=[],this.allTabIndex=0,this.getData()}function n(e){this.username=e.username,this.haveDataReady=!1,this.medalList=[],this.index=0,this.options=e,this.getData()}function t(e){this.medalId=e.medalId,this.level=e.level,this.shareType=e.shareType,this.haveDataReady=!1,this.msData={},this.mAllData=e.allData||[],this.options=e,this.getData()}var l="http"===location.protocol.substr(0,4)?"":"https:",d=l+"//g.csdnimg.cn/user-medal/2.0.0/user-medal.css",i="https://g-api.csdn.net/user-medal";!function(e){var a=document.createElement("link");a.rel="stylesheet",a.type="text/css",a.href=e,document.getElementsByTagName("head")[0].appendChild(a)}(d),window.QRCode||e("https://g.csdnimg.cn/lib/qrcode/1.0.0/qrcode.min.js"),window.html2canvas||e("https://g.csdnimg.cn/lib/html2canvas/1.4.1/html2canvas.min.js");a.prototype={constructor:a,init:function(){this.haveListReady&&this.allListReady&&(this.renderMain(),this.renderTab(),this.renderNav())},renderMain:function(){var e=this,a=$('<div id="user-medal">\n          <div class="user-medal-container">\n            <div class="user-medal-head"> \n              <img src="'+e.avatar+'">\n              <span class="user-medal-close"></span>\n            </div>\n            <div class="user-medal-body">\n              <div class="user-medal-content medal-clearfix">'+(e.self?'\n                <div class="medal-content-body"></div>\n                <div class="medal-geted-share '+(1==e.tabType&&e.alreadyHaveCount>0?"":"hide")+'"><span id="to-share-all">分享全部已解锁勋章</span></div>\n                ':'<div class="medal-content-body medal-content-body-others"></div>')+"</div>\n            </div>\n          </div>\n        </div>");e.$box=a,a.find(".user-medal-close").click(function(){e.close()}),a.on("click",function(a){$(a.target).find(".user-medal-container").length&&e.close()}),$("body").find("#user-medal").remove(),$("body").append(a),a.find("#to-share-all").click(function(){window.csdn.userMedal.share({shareType:"all",allData:e.haveMedalList})})},renderTab:function(){var e=this,a=$('<div class="user-medal-tab medal-clearfix">\n                <p class="medal-fl medal-user-info">\n                  <span class="medal-name" title="'+e.nickname+'">'+e.nickname+'</span>\n                  <span class="medal-count '+(e.alreadyHaveCount>0?"":" no-data")+'" >已经获得<em id="medal-count">'+(e.alreadyHaveCount||0)+"</em>种勋章啦~</span>\n                </p>"+(e.self?'<p class="medal-fr medal-user-btns">\n                  <span class="medal-btn medal-btn-tab" data-type=1>已解锁勋章</span>\n                  <span class="medal-btn medal-btn-tab" data-type=2>全部勋章</span>\n                </p>\n                ':" ")+"</div>");a.find(".medal-user-btns .medal-btn").click(function(a){$(this).hasClass("medal-btn-red")||(e.resetScrollTop(),e.tabType=$(this).data("type"),e.toggleNav(),1===e.tabType?(e.alreadyHaveCount>0&&(e.$box.find("#medal-count").html(e.alreadyHaveCount||0),e.$box.find(".medal-content-body").toggleClass("medal-content-body-geted"),e.$box.find(".medal-geted-share").show()),e.renderMedal(e.haveMedalList)):(e.$box.find("#medal-count").html(e.allHaveCount||0),e.$nav.find("li").removeClass("active"),e.$nav.find("li").first().click(),e.$box.find(".medal-geted-share").hide(),e.$box.find(".medal-content-body").removeClass("medal-content-body-geted")),$(this).addClass("medal-btn-red").siblings().removeClass("medal-btn-red"))}),this.$box.find(".user-medal-body").prepend(a),e.self?this.$box.find(".medal-btn-tab[data-type="+this.tabType+"]").click():e.renderMedalOthers(e.haveMedalList)},renderNav:function(){var e=this,a=e.allMedalList;if(a){var n=$('<ul class="medal-content-nav medal-fl"></ul>');a.forEach(function(e,a){n.append('<li class="medal-nav-item" data-list-index='+a+">"+e.classificationName+"<span>("+e.alreadyTotal+"/"+e.total+")</span></li>")}),n.on("click","li",function(a){if(!$(this).hasClass("active")){var n=$(this),t=n.data("list-index");e.allTabIndex=t,n.addClass("active").siblings().removeClass("active"),e.resetScrollTop(),e.renderMedal(e.allMedalList[t].medalData,"all")}}),e.$nav=n,e.$box.find(".user-medal-content").prepend(n),e.toggleNav()}},renderEmpty:function(){var e=this,a=$('<div class="user-medal-content-empty">\n                        <div class="no-medal-img">\n                          <img src="https://g.csdnimg.cn/user-medal/2.0.0/images/empty.png" alt="">\n                        </div>\n                        <p class="no-medal-p">\n                          你还没有解锁过勋章哦～\n                        </p>\n                        <div class="no-medal-btn">\n                          <span class="to-all">去解锁</span>\n                        </div>\n                      </div>');a.find(".to-all").click(function(a){e.tabType=2,e.$box.find(".medal-btn-tab[data-type="+e.tabType+"]").click()}),e.$box.find(".medal-content-body").html(a)},renderMedal:function(e,a){var n=this,t="";if(0===e.length&&"all"!==a)return void this.renderEmpty();"all"===a&&(e=e.map(function(e){return e.medalDetailDataList.forEach(function(a,n){a.ifGet&&(e.medalGetedMax=n)}),e.medalGetedMax=void 0!==e.medalGetedMax?e.medalGetedMax:0,e.getImageUrl=e.medalDetailDataList[e.medalGetedMax].ifGet?e.medalDetailDataList[e.medalGetedMax].getImageUrl:e.medalDetailDataList[e.medalGetedMax].noGetImageUrl,e.medalName=e.medalName,e.desc=e.medalDetailDataList[e.medalGetedMax].desc,e.getTime=e.medalDetailDataList[e.medalGetedMax].getTime,e.sourceUrl=e.medalDetailDataList[e.medalGetedMax].sourceUrl,e.getOne=e.medalDetailDataList[e.medalGetedMax].ifGet,e.medalId=e.id,e.getLevel=e.medalGetedMax+1,e})),e.forEach(function(e,l){t+='<div class="medal-detail-item">\n                  <div class="medal-detail-def">\n                    <div class="medal-detail-item-up medal-clearfix">\n                      <div class="item-img  medal-fl">\n                        <img src="'+e.getImageUrl+'">'+("all"===a?3==e.medalType?"<span class="+(e.getOne?"geted":"")+">Lv"+e.getLevel+"</span>":"":3==e.medalType?"<span class='geted'}>Lv"+e.getLevel+"</span>":"")+'\n                      </div>\n                      <div class="item-info  medal-fr">\n                        <h4>'+e.medalName+(e.medalType>1?"Lv"+e.getLevel:"")+'</h4>\n                        <p class="desc">'+e.desc+'</p>\n                        <p class="date">'+(e.getTime?e.getTime:"")+"</p>\n                      </div>\n                    </div>"+(2===n.tabType&&e.medalLevel>1?'\n                    <div class="medal-detail-item-down">'+(e.medalLevel>6?'<i class="btn-to-left" data-medal-total='+e.medalLevel+" data-medal-index="+l+'></i><i class="btn-to-right" data-medal-total='+e.medalLevel+" data-medal-index="+l+"></i>":"")+'\n                      <div class="level-box">\n                        <ul>'+Array(e.medalLevel).toString().split(",").map(function(a,n){return"<li data-medal-index="+l+" data-medal-level="+n+" data-medal-curlevel="+e.medalGetedMax+" data-medal-geted="+e.ifGet+'  class="'+(e.medalGetedMax===n&&e.ifGet?"geted-unchoosed geted-choosed":e.medalGetedMax>n&&e.ifGet?"geted-unchoosed":"")+'">Lv'+(n+1)+"</li>"}).join("")+"</ul>\n                      </div >\n                    </div > ":' <div class="medal-detail-item-down"></div>')+'</div >\n          <div class="medal-detail-hover">\n            <p>'+e.desc+'</p>\n            <div class="medal-clearfix">\n              '+("all"===a?e.getOne?'<span class="medal-fr to-share"  data-medal-id='+e.medalId+" data-medal-level="+e.getLevel+">去分享</span>":e.sourceUrl?'<a class="medal-fr btn-active ml16" href="'+e.sourceUrl+'" target="_blank">我想要</a>':"":'<span class="medal-fr to-share"  data-medal-id='+e.medalId+" data-medal-level="+e.getLevel+">去分享</span>")+"\n                    </div>\n          </div>\n                </div > "}),n.$box.find(".medal-content-body").html(t),n.$box.find(".medal-detail-item-down .btn-to-right").click(function(e){var a=$(this),t=n.allTabIndex,l=a.data("medal-index"),d=a.data("medal-total"),i=Math.floor(d/6),s=n.allMedalList[t].medalData[l],o=s._index?s._index:0;n.$levelBox=a.siblings(".level-box"),o<i&&(o++,s._index=o,o==i?n.$levelBox.animate({"scroll-left":300*(o-1)+d%6*50+"px"},"slow"):n.$levelBox.animate({"scroll-left":300*o+"px"},"slow"))}),n.$box.find(".medal-detail-item-down .btn-to-left").click(function(e){var a=$(this),t=n.allTabIndex,l=a.data("medal-index"),d=n.allMedalList[t].medalData[l],i=d._index?d._index:0;n.$levelBox=a.siblings(".level-box"),i>0&&(i--,d._index=i,void 0,n.$levelBox.animate({"scroll-left":300*i+"px"},"slow"))}),n.$box.find(".medal-detail-item-down li").click(function(e){var a=$(this),t=n.allTabIndex,l=a.data("medal-index"),d=a.data("medal-level"),i=a.data("medal-curlevel"),s=a.data("medal-geted");a.siblings().removeClass("geted-choosed ungeted"),i>=d&&s?a.addClass("geted-choosed"):a.addClass("ungeted");var o=n.allMedalList[t].medalData[l],c=o.medalDetailDataList[d];void 0;var r='<div class="item-img  medal-fl">\n                          <img src="'+(c.ifGet?c.getImageUrl:c.noGetImageUrl)+'">\n                          '+(3==o.medalType?"<span class="+(c.ifGet?"geted":"")+">Lv"+(d+1)+"</span>":"")+'\n                        </div>\n                        <div class="item-info  medal-fr">\n                          <h4>'+o.medalName+"Lv"+(d+1)+'</h4>\n                          <p class="desc">'+c.desc+'</p>\n                          <p class="date">'+(c.getTime?c.getTime:"")+"</p>\n                        </div>",m="\n                <p>"+c.desc+'</p>\n                <div class="medal-clearfix">\n                  '+(c.ifGet?'<span class="medal-fr to-share"  data-medal-id='+o.id+" data-medal-level="+(d+1)+" >去分享</span>":c.sourceUrl?'<a class="medal-fr btn-active ml16" href="'+c.sourceUrl+'" target="_blank">我想要</a>':"")+"\n                </div>",v=a.parents(".medal-detail-item");v.find(".medal-detail-def").find(".medal-detail-item-up").html(r),v.find(".medal-detail-hover").html(m),v.find(".medal-detail-hover .to-share").click(function(){var e=$(this),a=e.data("medal-id"),n=e.data("medal-level");window.csdn.userMedal.share({shareType:"single",medalId:a,level:n})}),v.find(".medal-detail-item-up .desc").mouseenter(function(){var e=$(this),a=e.parents(".medal-detail-item");a.find(".medal-detail-def").hide(),a.find(".medal-detail-hover").show()})}),n.$box.find(".medal-detail-item-up .desc").mouseenter(function(){var e=$(this),a=e.parents(".medal-detail-item");a.find(".medal-detail-def").hide(),a.find(".medal-detail-hover").show()}),n.$box.find(".medal-detail-hover").mouseleave(function(){var e=$(this);e.hide(),e.siblings(".medal-detail-def").show()}),n.$box.find(".to-share").click(function(){var e=$(this),a=e.data("medal-id"),n=e.data("medal-level");void 0,window.csdn.userMedal.share({shareType:"single",medalId:a,level:n})})},renderMedalOthers:function(e,a){var n=this,t="";0===e.length&&"all"!==a||(e.forEach(function(e){t+='\n          <div class="medal-other-item">\n            <div class="medal-detail-def">\n              <div class="medal-detail-item-up">\n                <div class="item-img ">\n                  <img src="'+e.getImageUrl+'">\n                  '+(3==e.medalType?'<span class="geted">Lv'+e.getLevel+"</span>":"")+'\n                </div>\n                <div class="item-info ">\n                  <p class="desc">'+e.medalName+(e.medalType>1?"Lv"+e.getLevel:"")+'</p>\n                </div>\n              </div>\n              <a class="get-btn" data-report-click=\'{"spm": "3001.9181"}\'  href="https://i.csdn.net/#/user-center/profile?openMedal=1" target="_blank">我也要</a> \n            </div>\n        </div>'}),n.$box.find(".medal-content-body").html(t))},toggleNav:function(){this.$nav&&this.$nav.toggle()},close:function(){this.$box&&this.$box.remove()},getCookie:function(e){var a={};return document.cookie.replace(/([^=;\s]+)=([^=;\s]+)/g,function(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];var l=n[1],d=n[2];a[l]=d}),e?a[e]:a},isSelf:function(){var e=this.getCookie("UserName");return this.username===e},resetScrollTop:function(){this.$box.find(".medal-content-body").scrollTop(0)},getHaveMedalList:function(){var e=this;$.ajax({url:i+"/web/userMedal/queryAlreadyMedal",type:"get",crossDomain:!0,xhrFields:{withCredentials:!0},data:{username:e.username},contentType:"application/json",dataType:"json",success:function(a){a.data&&(e.haveMedalList=a.data.list||[],e.alreadyHaveCount=a.data.total||0,e.haveListReady=!0,e.init())},error:function(e){}})},getAllMedalList:function(){var e=this;$.ajax({url:i+"/web/userMedal/queryAllMedal",type:"get",crossDomain:!0,xhrFields:{withCredentials:!0},data:{username:e.username},contentType:"application/json",dataType:"json",success:function(a){a.data&&(e.allMedalList=a.data.details,e.allHaveCount=a.data.collectionAlready,e.allListReady=!0,e.init())},error:function(e){}})},getData:function(){this.getHaveMedalList(),this.getAllMedalList()}},n.prototype={constructor:n,init:function(){this.haveDataReady&&this.medalList.length>0&&this.renderPop()},close:function(){this.$box&&this.$box.remove()},renderPop:function(){var e=this,a=$($.parseHTML('\n      <div id="user-medal-pop">\n         <div class="user-medal-pop-container">\n            <div class="header-box">\n              <span class="user-medal-close"></span>\n            </div>\n        <div class="main-box">'+(e.medalList.length>1?'\n          <span class="btn btn-to-right"></span>\n          <span class="btn btn-to-left"></span>\n          <ul class="dot-ul">'+e.medalList.map(function(e,a){return 0===a?'<li class="cur"></li>':"<li></li>"}).join("")+"\n          </ul>":"")+'\n          <h4>恭喜获得新勋章</h4>\n          <div class="medal-box">'+e.medalList.map(function(e,a){return'\n            <div class="medal-item">\n              <p class="date">'+e.getTime+'</p>\n              <div class="img-box">\n                <img src="'+e.getImageUrl+'" alt="">\n                '+(3==e.medalType?"<span>Lv"+e.getLevel+"</span>":"")+"\n              </div>\n              <h6>"+e.medalName+(e.medalType>1?"Lv"+e.getLevel:"")+'</h6>\n              <p class="notice">'+e.desc+"</p>\n            </div>\n            "}).join("")+'\n          </div>\n        </div>\n          <div class="footer-box">\n            <span class="to-share">去分享</span>\n          </div>\n      </div>\n      </div>\n      '));e.$box=a,a.find(".user-medal-close").click(function(){e.close()}),a.on("click",function(a){$(a.target).find(".user-medal-pop-container").length&&e.close()}),$("body").find("#user-medal-pop").remove(),$("body").append(a),a.find(".btn-to-right").click(function(){e.index<e.medalList.length-1&&(e.index++,a.find(".dot-ul li").siblings().removeClass("cur"),a.find(".dot-ul li").eq(e.index).addClass("cur"),e.$mainBox=a.find(".medal-box"),e.$mainBox.animate({"scroll-left":"+"+340*e.index+"px"},"slow"))}),a.find(".btn-to-left").click(function(){e.index>0&&(e.index--,a.find(".dot-ul li").siblings().removeClass("cur"),a.find(".dot-ul li").eq(e.index).addClass("cur"),e.$mainBox=a.find(".medal-box"),e.$mainBox.animate({"scroll-left":340*e.index+"px"},"slow"))}),a.find(".to-share").click(function(){var a=e.medalList[e.index].medalId,n=e.medalList[e.index].getLevel;e.close(),window.csdn.userMedal.share({shareType:"single",medalId:a,level:n})})},getCookie:function(e){var a={};return document.cookie.replace(/([^=;\s]+)=([^=;\s]+)/g,function(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];var l=n[1],d=n[2];a[l]=d}),e?a[e]:a},getNotShowMedalList:function(){var e=this;$.ajax({url:i+"/web/userMedal/getNotShowMedal",type:"get",crossDomain:!0,xhrFields:{withCredentials:!0},data:{username:e.username},contentType:"application/json",dataType:"json",success:function(a){a.data&&(e.haveDataReady=!0,e.medalList=a.data.list||[],e.init())},error:function(e){}})},getData:function(){this.getNotShowMedalList()}},t.prototype={constructor:t,init:function(){this.haveDataReady&&"single"===this.options.shareType?this.renderShareSingle():this.renderShareAll()},close:function(){this.$box&&this.$box.remove()},renderShareSingle:function(){var e=this,a=$($.parseHTML('\n        <div id="user-medal-share">\n           <div class="user-medal-share-container">\n              <div class="header-box">\n                <span class="user-medal-close"></span>\n              </div>\n              <div class="main-box2" id="main-box">\n              <div class="main-up medal-clearfix">\n               <div class="img-box medal-fl">\n                 <img src="'+e.msData.userInfo.avatarUrl+'">\n                 '+(!0===e.msData.userInfo.ifVip?'<span class="icon icon-vip"></span>':"")+'\n               </div>\n               <div class="info-box medal-fl">\n                 <p class="desc">'+e.msData.userInfo.nickname+'</p>\n                 <p class="title">获得CSDN勋章</p>\n               </div>\n              </div>\n              <div class="main-center">\n               <div class="img-box">\n                 <img src="'+e.msData.single.getImageUrl+'">\n                 '+(3==e.msData.single.medalType?"<span>Lv"+e.msData.single.getLevel+"</span>":"")+"\n               </div>\n               <h3>"+e.msData.single.medalName+(e.msData.single.medalType>1?"Lv"+e.msData.single.getLevel:"")+"</h3>\n               <p>"+e.msData.single.desc+'</p>\n              </div>\n              <div class="main-down medal-clearfix">\n               <div class="left medal-fl" id="qrcode">\n               </div>\n               <div class="right medal-fl">\n                 <p class="title">我在CSDN获得了'+e.msData.total+"种勋章</p>\n                 <div>\n                 "+e.msData.list.slice(0,5).map(function(e){return'<img src="'+e.getImageUrl+'">'}).join("")+'\n                 </div>\n                 <p class="sub_title">扫码来看看吧~</p>\n               </div>\n              </div>\n            </div>\n            <div class="footer-box">\n              <span id="to-save">下载图片</span>\n            </div>\n           </div>\n        </div>\n        '));e.$box=a,a.find(".user-medal-close").click(function(){e.close()}),a.on("click",function(a){$(a.target).find(".user-medal-share-container").length&&e.close()}),$("body").find("#user-medal-share").remove(),$("body").append(a),a.find("#to-save").click(function(){e.loading||(e.loading=!0,e.clickGeneratePicture())}),new QRCode(document.getElementById("qrcode"),{text:e.msData.codeUrl,width:80,height:80})},renderShareAll:function(){var e=this,a=$($.parseHTML('\n        <div id="user-medal-share">\n           <div class="user-medal-share-container">\n              <div class="header-box">\n                <span class="user-medal-close"></span>\n              </div>\n              <div class="main-box3" id="main-box">\n                <div class="main-up medal-clearfix">\n                <div class="img-box medal-fl">\n                    <img src="'+e.msData.userInfo.avatarUrl+'">\n                    '+(!0===e.msData.userInfo.ifVip?'<span class="icon icon-vip"></span>':"")+'\n                  </div>\n                  <div class="info-box medal-fl">\n                    <p class="desc">'+e.msData.userInfo.nickname+'</p>\n                    <p class="title">已获得'+e.mAllData.length+'种勋章～</p>\n                  </div>\n                </div>\n                <div class="main-center medal-clearfix">\n                  '+e.mAllData.map(function(e){return'<div class="img-item medal-fl">\n                  <div class="img-box">\n                    <img src="'+e.getImageUrl+'">\n                    '+(3==e.medalType?"<span>Lv"+e.getLevel+"</span>":"")+"\n                  </div>\n                  <p>"+e.medalName+(e.medalType>1?"Lv"+e.getLevel:"")+"</p>\n                </div>"}).join("")+'\n                </div>\n                <div class="main-down medal-clearfix">\n                  <div class="left medal-fl" id="qrcode">\n                  </div>\n                  <div class="right medal-fl">\n                   '+(e.mAllData.length>9?'<p class="more">还有更多我的勋章<br />扫码来CSDN关注我～</p>':'<p class="less">扫码来CSDN关注我～</p>')+'\n                  </div>\n                </div>\n              </div>\n              <div class="footer-box">\n                <span id="to-save2">下载图片</span>\n              </div>\n           </div>\n        </div>\n        '));e.$box=a,a.find(".user-medal-close").click(function(){e.close()}),a.on("click",function(a){$(a.target).find(".user-medal-share-container").length&&e.close()}),$("body").find("#user-medal-share").remove(),$("body").append(a),a.find("#to-save2").click(function(){e.loading||(e.loading=!0,e.clickGeneratePicture())}),new QRCode(document.getElementById("qrcode"),{text:e.msData.codeUrl,width:80,height:80})},getCookie:function(e){var a={};return document.cookie.replace(/([^=;\s]+)=([^=;\s]+)/g,function(){for(var e=arguments.length,n=Array(e),t=0;t<e;t++)n[t]=arguments[t];var l=n[1],d=n[2];a[l]=d}),e?a[e]:a},clickGeneratePicture:function(){var e=this;window.scroll(0,0);var a=document.getElementById("main-box");html2canvas(a,{useCORS:!0}).then(function(a){var n=a.toDataURL("image/png"),t=document.createElement("a");document.body.appendChild(t),t.href=n,t.download="快分享给好友吧~",t.click(),e.toast("图片下载成功，快分享给好友吧~",1e3),e.loading=!1}).catch(function(a){e.loading=!1})},toast:function(e,a){var n=this;a=isNaN(a)?3e3:a;var t=document.createElement("div");t.innerHTML=e,t.style.cssText="padding:0px 10px;opacity: 0.8;height: 32px;background:rgba(34,34,38,1);color: rgb(255, 255, 255);line-height: 32px;text-align: center;border-radius: 4px;position: fixed;top: 14%;left:50%;transform:translate(-50%, -50%);z-index: 999999;font-size: 14px;",document.getElementById("user-medal-share").appendChild(t),setTimeout(function(){t.style.webkitTransition="-webkit-transform 0.5s ease-in, opacity 0.5s ease-in",t.style.opacity="0",setTimeout(function(){document.getElementById("user-medal-share").removeChild(t),n.close()},500)},a)},shareMedal:function(){var e=this,a={};a="single"===e.options.shareType?{medalId:e.medalId,level:e.level,shareType:e.shareType}:{shareType:e.shareType},$.ajax({url:i+"/web/userMedal/shareMedal",type:"get",crossDomain:!0,xhrFields:{withCredentials:!0},data:a,contentType:"application/json",dataType:"json",success:function(a){a.data&&(e.haveDataReady=!0,e.msData=a.data||{},e.init())},error:function(e){}})},getData:function(){this.shareMedal()}},window.csdn=window.csdn||{},window.csdn.userMedal={show:function(e){return new a(e)},pop:function(e){return new n(e)},share:function(e){return new t(e)}},$(document).on("click","[csdn-user-medal-btn=true], .csdn-user-medal-btn",function(e){var a=$(this),n=a.data("username"),t=a.data("nickname"),l=a.data("avatar");void 0,t&&n&&l&&window.csdn.userMedal.show({username:n,avatar:l,nickname:t})})}();