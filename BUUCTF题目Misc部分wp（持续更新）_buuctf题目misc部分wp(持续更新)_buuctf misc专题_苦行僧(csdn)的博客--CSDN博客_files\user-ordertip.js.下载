"use strict";!function(t,i,s){function o(){for(var t={},i=0;i<arguments.length;i++)s.extend(!0,t,arguments[i]);return t}function e(t){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1500,o=i.createElement("div");o.innerHTML=t,o.style.cssText="min-width:124px;padding:0 8px;opacity: 0.8;height: 40px;background:rgba(34,34,38,1);color: rgb(255, 255, 255);line-height: 40px;text-align: center;border-radius: 4px;position: fixed;top: 35%;left:50%;transform: translateX(-50%);z-index: 999999;font-size: 16px;",i.getElementById("user-ordertip").appendChild(o),setTimeout(function(){o.style.webkitTransition="-webkit-transform 0.5s ease-in, opacity 0.5s ease-in",o.style.opacity="0",setTimeout(function(){i.getElementById("user-ordertip")&&i.getElementById("user-ordertip").removeChild(o)},500)},s)}function r(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1500,i='\n      <div class="loading_warp" id="user-ordertip-loading">\n        <div class="icon_box">\n          <img class="rotating" src="'+m+'/icon-paying.png"/>\n        </div>\n        <div class="pay_msg">查询中...</div>\n      </div>\n    ';s(".ordertip_dialog").append(i).find(".ordertip_content").addClass("noScroll"),setTimeout(function(){s("#user-ordertip-loading").remove(),s(".ordertip_dialog .ordertip_content").removeClass("noScroll")},t)}function n(){var t=a("api_env")||"",i="https://mall.csdn.net/",s=/^beta|test|loc[a-z]*/;return t.match(s)?i="https://test-mall.csdn.net/":t.match(/^pre-|pre[a-z]*/)&&(i="https://pre-mall.csdn.net/"),i}function a(t){var i=new RegExp("(^|&)"+t+"=([^&]*)(&|$)","i"),s=window.location.search.substr(1).match(i);return null!=s?unescape(s[2]):""}function d(t,o){QRCode?o.pay_url&&(k.priceInfo=o,s("#ordertip_qr_code").html(""),s("#ordertip_notify").hide(),s("#pay_btn").attr("href",o.pay_url),new QRCode(i.getElementById("ordertip_qr_code"),{text:o.pay_url,width:120,height:120}),s("#user-ordertip .other_pay").removeClass("none").find("a").attr("href",o.pay_url)):void 0}function c(){_("pay_error","获取失败,点击重试","code_2")}function l(){_("pay_time_out","点击重新获取","")}function p(){_("pay_error","已扫码<br>请在手机端操作","")}function g(t){var i=!(!1===k.show_params.needLoading),s=k.show_params.successProcess||"reload";i&&r(),setTimeout(function(){e("支付成功","1000"),setTimeout(function(){x?(k.close(),x(t)):"reload"===s?window.location.reload():"jump"===s&&(window.location.href=h(t))},1e3)},i?1500:0)}function h(t){return"success"===t.errorMessage&&t.jumpUrl&&1===t.status?t.jumpUrl:!t.need_third_pay&&t.paySuccessUrl?t.paySuccessUrl:"https://mall.csdn.net/myorder"}function _(t,i,o){s("#ordertip_notify").show().html('<img class="pay_icon" src="https://csdnimg.cn/release/download/images/'+t+'.png"/><span class="pay_tip">'+i+"</span>"),s("#ordertip_qr_code").html('<img src="https://csdnimg.cn/public/static/img/csdn-userimg250.gif" width="145" height="145"/>'),"pay_time_out"==t||"pay_error"==t?s("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").on("click",function(){k.getPayCode()}):s("#ordertip_notify .pay_icon,#ordertip_notify .pay_tip").off("click")}function u(){this.userInfo={},this.tabList=[{icon:m+"/tab1-icon.png",title:"CSDN会员",desc:"下载",key:"vipForPopup",list:[]},{icon:m+"/tab2-icon.png",title:"超级会员",desc:"专栏、课程、下载",key:"superVipForPopup",list:[]}],this.activeTab="vipForPopup",this.goodsInfo={},this.goodsList=[],this.activeGoodsId="",this.isUseBalance=!0,this.priceInfo={},this.errType="",this.reportExt={},this.navList=[],this.payMethods=[],this.price=0,this.payUrl="",this.params={},this._cart=null,this.show_params={},this.goodsTransX=0,this.listBoxWidth=626,this.listScrollWidth=0,this.goodsTabWidth=176,this.rightsTransX=0,this.rightsBoxWidth=626,this.rightsScrollWidth=0,this.rightsTabWidth=136,this.vipRightsObj={},b=this}function f(){k.close()}var v="https://g.csdnimg.cn/user-ordertip/",m=v+"5.0.1/images",b=null,y=!1,x=null,w="",T=!0,I=function(t){var s=i.cookie;return s&&function(){var i,o={};s=s.split("; ");for(var e=0,r=s.length;e<r&&(i=s[e].split("="),!(i.length>0&&(i[0]===t&&(o.key=i[0],o.value=i[1],o.status=!0),"key"in o)));e++);return"key"in o&&o}()};!function(t){var s=i.createElement("link");s.rel="stylesheet",s.type="text/css",s.href=t,i.getElementsByTagName("head")[0].appendChild(s)}("https://g.csdnimg.cn/user-ordertip/5.0.1/user-ordertip.css");u.prototype={constructor:u,close:function(){y=!1,this._cart&&this._cart.clearTimer(),s("#user-ordertip-box").remove()},show:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return I("UserName").value?window.csdn.cart?window.csdn.cartClass?(this.show_params=t,"function"==typeof t.get_pay_success_callback&&(x=t.get_pay_success_callback),this.sale=t.sale_source||a("sale_source"),this.reportExt=t.report_ext||{},t.tabList&&t.tabList.length>0&&(this.tabList=t.tabList),t.tabs&&(this.activeTab=t.tabs[0],this.tabList=this.tabList.filter(function(i){return-1!==t.tabs.indexOf(i.key)})),0===this.tabList.length?void void 0:void this.init()):void void 0:void void 0:void void 0},init:function(){this._cart=new window.csdn.cartClass,this.getVipGoodsList(function(t){b.getGoodsInfo(t,function(t){b.goodsInfo=t,b.goodsCodeData(t.new_price,t.available_amount),b.activeGoodsId=b.goodsInfo.goods_id,b.initDialog(),b.getPayCode(),b.getUserInfo()},!1)})},bindEvents:function(){s(i).off("click",".ordertip_dialog .user_balance").on("click",".ordertip_dialog .user_balance",function(){if(s(".ordertip_dialog .user_balance").hasClass("disable"))return void(b.isUseBalance=!1);s(".ordertip_dialog .user_balance").hasClass("active")?(s(".ordertip_dialog .user_balance").removeClass("active"),b.isUseBalance=!1):(s(".ordertip_dialog .user_balance").addClass("active"),b.isUseBalance=!0),b.getGoodsInfo(b.goodsInfo,function(t){b.goodsInfo=t,b.goodsCodeData(b.goodsInfo.new_price,b.goodsInfo.available_amount),b.changePriceHtml(b.price),b.getPayCode(b.goodsInfo)})}),s(i).off("click","#pay_btn").on("click","#pay_btn",function(t){var i=I("UserName").value;if(T){T=!1;var o={product_id:b.goodsInfo.product_id,goods_id:b.goodsInfo.goods_id,flag:b.goodsInfo.flag,goodsSource:b.goodsInfo.goodsSource||"",is_use_balance:2,sale_source:b.sale,request_id:i+"_"+w+"_"+b.goodsInfo.product_id+"_"+b.goodsInfo.goods_id+"_"+b.goodsInfo.flag};return void b._cart.quickBuy({params:o,get_pay_success_callback:function(t){T=!0,200==t.code&&!1===t.data.need_third_pay&&g(t.data)},error_function:function(t){T=!0,400103012==t.status&&s(".ordertip_item.active").trigger("click"),void 0}})}}),s(i).on("click",".ordertip_header_btn",function(t){b.close()}),s(i).off("click",".user-ordertip .list_btn.btn_left").on("click",".user-ordertip .list_btn.btn_left",function(t){b.moveTab("left")}),s(i).off("click",".user-ordertip .list_btn.btn_right").on("click",".user-ordertip .list_btn.btn_right",function(t){b.moveTab("right")}),s(i).off("click",".user-ordertip .rights_btn.rights_btn_left").on("click",".user-ordertip .rights_btn.rights_btn_left",function(t){b.moveRightsCard("left")}),s(i).off("click",".user-ordertip .rights_btn.rights_btn_right").on("click",".user-ordertip .rights_btn.rights_btn_right",function(t){b.moveRightsCard("right")}),s(i).off("click",".ordertip_tab_item").on("click",".ordertip_tab_item",function(t){var i=s(this).attr("data-key")||"";if(!s(this).hasClass("active")){s(this).addClass("active").siblings().removeClass("active"),b.goodsList=b.tabList.find(function(t){return t.key===i}).list||[],s(".ordertip_dialog .ordertip_c_goodslist_scroll").html(b.setGoodsListHtml()),b.dealGoodsListJt();var o=b.findDefaultGood(b.goodsList).goodsId;s(".ordertip_item[data-id="+o+"]").trigger("click")}}),s(i).off("click",".ordertip_item").on("click",".ordertip_item",function(t){var i=JSON.parse(s(this).attr("data-goods")||"{}");if(i=o(i,b.goodsList[i.index].ext),!s(this).hasClass("active"))return s(this).addClass("active").siblings().removeClass("active"),void b.getGoodsInfo(i,function(t){b.goodsInfo=t,1==b.goodsInfo.isContract&&t.available_amount>t.new_price?(b.isUseBalance=!1,b.goodsInfo.hideBalance=!0):t.available_amount>0&&(b.isUseBalance=!0),b.activeGoodsId=t.goods_id,b.goodsCodeData(t.new_price,t.available_amount),b.goodsInfo.contractDesc?s(".ordertip_dialog .ordertip_c_activity").html(decodeURIComponent(b.goodsInfo.contractDesc||"")).removeClass("none").show():s(".ordertip_dialog .ordertip_c_activity").hide(),s(".ordertip_dialog .commodity_box").html("").append(b.setPayPriceHtml()),s(".ordertip_dialog .scan_code").html("").append(b.setPaylistHtml())})})},initDialog:function(){this.bindEvents(),this.renderDialog()},setUserInfoHtml:function(){return b.userInfo.nickname?'<div class="ordertip_user">\n          <img class="ordertip_user_head" src="'+b.userInfo.avatar+'" />\n          <div class="ordertip_user_info">\n            <div class="ordertip_user_name '+(4==b.userInfo.userStatus?"vip":"")+'">'+b.userInfo.nickname+'<img src="'+m+'/vip-icon.png" /></div>\n            <div class="ordertip_user_vipdesc">'+b.userInfo.vipTips+"</div>\n          </div>\n        </div>":""},setVipTabHtml:function(){var t=this,i="";if(this.tabList.length>1){var s="";this.tabList.forEach(function(i){s+='<div class="ordertip_tab_item '+(t.activeTab===i.key?"active":"")+'" data-key="'+i.key+'">\n            <div class="ordertip_tab_title">'+(i.icon?'<img src="'+i.icon+'" />':"")+i.title+'</div>\n            <div class="ordertip_tab_desc">'+i.desc+"</div>\n          </div>"}),i='<div class="ordertip_tab_list">'+s+"</div>"}return i},setGoodsListHtml:function(){var t=this,i="";return this.goodsList.forEach(function(s,o){i+='<div class="ordertip_c_l_goodsitem ordertip_item '+(t.activeGoodsId==s.ext.goodsId?"active":"")+" \" data-goods='"+JSON.stringify({index:o,type:"other"})+"' data-id='"+s.ext.goodsId+"'>\n          <span class=\"ordertip_c_l_b_tips "+(s.ext.activityContent?"":"none")+'" >'+s.ext.activityContent+'</span>\n          <div class="ordertip_c_l_b_name">'+(s.name||"").split("：")[0]+'</div>\n          <div class="ordertip_c_l_b_price"><span>¥</span>'+s.ext.unitPrice+"<span>/"+s.ext.availableUnit+'</span></div>\n          <div class="ordertip_c_l_b_activity '+(s.title?"":"none")+'">'+s.title+"</div>\n        </div>"}),i},setPaylistHtml:function(){var t=b.goodsInfo,i=[];t.payTypeList&&t.payTypeList.forEach(function(t){"unionpay"!==t.name&&i.push(t)});for(var s="",o=0;o<i.length;o++)s+='<img class="icon_item '+i[o].name+'" src='+JSON.stringify(i[o].image)+' alt="img">';return s+'<span class="pay_intro">扫码支付</span>'},setPayPriceHtml:function(){var t=(b.priceInfo,b.goodsInfo);return'<ul class="commodity_desc">\n                    <li class="amount_actually">\n                      实付：<span class="num"><b>'+b.price+'</b>元</span><span class="num '+(t.totalDiscountPrice>0?"":"none")+'">已优惠'+t.totalDiscountPrice+'元!</span>\n                    </li>\n                    <li class="voucher '+(t.cashCouponVoList&&t.cashCouponVoList.length>0?"":"none")+'" id="useVoucherBtn"><div class="voucher-title ">有'+t.cashCouponVoList.length+'张代金券可用，选择代金券</div>\n                    </li>\n                    <li class="gift none '+(t.discount_msg?"block":"none")+'">\n                      <img src="'+m+'/enjoy.png" alt="">\n                      <span>'+t.discount_msg+'</span>\n                    </li>\n\n                    <li class="user_balance '+(t.hideBalance?"none":"")+" "+(t.available_amount>0?b.isUseBalance?"active":"":"disable")+" "+(19===t.flag?"none":"")+'" >\n                      <span class="unchecked"></span>\n                      <img src="'+m+'/checked.png" alt="" class="checked">\n                      余额抵扣 <b class="num">'+t.available_amount+'</b>\n                    </li>\n                    <li class="user_balance tips '+(t.hideBalance?"":"none")+'">\n                      连续包月暂不支持余额抵扣，请使用微信或支付宝付款，请您谅解！\n                    </li>\n                  </ul>'},setVipRightsHtml:function(t){if(!b.vipRightsObj[t]||0===b.vipRightsObj[t].length)return"";var i="";return b.vipRightsObj[t].forEach(function(t){i+='<a href="'+t.permissionUrl+'" target="_blank" class="ordertip_rights_item">\n          <img src="'+t.permissionIcon+'" class="ordertip_r_item_img">\n          <div class="ordertip_r_item_info">\n            <div class="ordertip_r_item_title ellipis">'+t.permissionName+'</div>\n            <div class="ordertip_r_item_desc ellipis">'+t.permissionTips+"</div>\n          </div>\n        </a>"}),'<div class="ordertip_rights_head">CSDN会员特权</div>\n        <span class="rights_btn rights_btn_right">\n          <img src="'+m+'/icon-right.png">\n        </span>\n        <span class="rights_btn rights_btn_left">\n          <img src="'+m+'/icon-left.png">\n        </span>\n        <div class="ordertip_rights_scroll">\n          '+i+"\n        </div>"},renderDialog:function(){this.createMask();var i=this.setVipTabHtml(),o=this.setGoodsListHtml(),e=this.setPaylistHtml(),r=this.setPayPriceHtml(),n='<div id="user-ordertip" class="user-ordertip noselect">\n          <div class="ordertip_dialog">\n              <div class="ordertip_header">\n                \n                <span class="ordertip_header_btn"> + </span>\n              </div>\n              '+i+'\n              <div class="ordertip_content">\n                <div class="ordertip_c_goodslist ">\n                  <span class="list_btn btn_left">\n                    <img src="'+m+'/icon-left.png">\n                  </span>\n                  <span class="list_btn btn_right">\n                    <img src="'+m+'/icon-right.png">\n                  </span>\n                  <div class="ordertip_c_goodslist_scroll">\n                    '+o+'\n                  </div>\n                </div>\n                <div class="ordertip_c_activity '+(this.goodsInfo.contractDesc?"":"none")+'">'+(decodeURIComponent(this.goodsInfo.contractDesc)||"")+'</div>\n                <div class="ordertip_paybox">\n                  <div class="recharge_mode '+(0!=b.price?"block":"none")+'">\n                    <div class="recharge_mode_qr_code" id="ordertip_qr_code">\n                      <img class="loading" src="'+m+'/loading.gif" width="50" height="50">\n                    </div>\n                    <div id="ordertip_notify" class="pay_notify"></div>\n                    <p class="scan_code">\n                      '+e+'\n                    </p>\n                  </div>\n                  <div class="recharge_mode_btn '+(0==b.price?"block":"none")+'">\n                    <div class="pay_btn" id="pay_btn">确定支付</div>\n                  </div>\n                  <div class="commodity_box">\n                    '+r+'\n                  </div>\n                </div>\n                <div class="ordertip_rights_box">\n                </div>\n                <div class="ordertip_agreement">\n                  购买即同意<a href="https://blog.csdn.net/blogdevteam/article/details/111173049" target="_blank">《CSDN会员服务协议》</a>\n                </div>\n              </div>\n            </div>\n          <div class="ordertip_mask"></div>\n      </div>\n      ',a=s(n);s("#user-ordertip-box").append(a),b.dealGoodsListJt(),b.dealRightsListJt(),t.report&&window.csdn.report.viewCheck()},dealGoodsListJt:function(){b.goodsTransX=0,b.listScrollWidth=s(".ordertip_c_goodslist_scroll").width()||0,b.listBoxWidth=s(".ordertip_c_goodslist").width()||626,b.listBoxWidth>=b.listScrollWidth?s("#user-ordertip-box .list_btn").hide():s("#user-ordertip-box .btn_left").hide().siblings(".btn_right").show(),s(".ordertip_c_goodslist_scroll").css("transform","translateX(0px)")},dealRightsListJt:function(){b.rightsTransX=0,b.rightsScrollWidth=s(".ordertip_rights_scroll").width()||0,b.rightsBoxWidth=s(".ordertip_rights_box").width()||626,b.rightsBoxWidth>=b.rightsScrollWidth?s("#user-ordertip-box .rights_btn").hide():s("#user-ordertip-box .rights_btn_left").hide().siblings(".rights_btn_right").show(),s(".ordertip_rights_scroll").css("transform","translateX(0px)")},createMask:function(){var t=i.createElement("div");t.id="user-ordertip-box",i.body.appendChild(t)},getVipGoodsList:function(t){s.ajax({url:n()+"mp/mallorder/api/internal/goods/showListV2?abTest=Y&showType=vipForPopup,superVipForPopup",type:"GET",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(i){i.data?(b.tabList.forEach(function(t){t.list=i.data[t.key]||[]}),b.goodsList=b.tabList.find(function(t){return t.key===b.activeTab}).list||[],t&&t(b.findDefaultGood(b.goodsList))):void 0},error:function(t){void 0}})},getGoodsInfo:function(t,i){var e=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];t.ext&&delete t.ext,s.ajax({url:n()+"mp/mallorder/api/internal/goods/getGoodsInfo",type:"GET",dataType:"json",data:o(t,{goods_id:t.goodsId,product_id:t.productId}),contentType:"application/json",xhrFields:{withCredentials:!0},success:function(s){200==s.code&&s.data?(19===s.data.flag&&(b.isUseBalance=!1),i&&i(s.data),b.changePriceHtml(b.price),e&&b.getPayCode(s.data),b.getVipRights(t.rightsGoodsId)):void 0},error:function(t){void 0}})},getVipRights:function(t){if(t)return b.vipRightsObj[t]?(s("#user-ordertip .ordertip_rights_box").html(b.setVipRightsHtml(t)),void b.dealRightsListJt()):void s.ajax({url:n()+"mp/mallorder/vip_plugin/vip_card/get_permission?goodsId="+t,type:"GET",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(i){i.data?(b.vipRightsObj[t]=i.data,s("#user-ordertip .ordertip_rights_box").html(b.setVipRightsHtml(t)),b.dealRightsListJt()):void 0},error:function(t){void 0}})},getUserInfo:function(){s.ajax({url:n()+"mp/mallorder/vip_plugin/vip_buy/get_user_info",type:"GET",contentType:"application/json",xhrFields:{withCredentials:!0},success:function(t){t.data?(b.userInfo=t.data,s("#user-ordertip .ordertip_header").prepend(b.setUserInfoHtml())):void 0},error:function(t){void 0}})},goodsCodeData:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=0;o=this.isUseBalance?t-s-i<0?0:(100*t-100*s-100*i)/100:t-s<0?0:(100*t-100*s)/100,this.price=Number(o).toFixed(2)},changePriceHtml:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;Number(t)<=0?(s(".ordertip_dialog .recharge_mode").addClass("none"),s(".ordertip_dialog .recharge_mode_btn").addClass("show").removeClass("none")):(s(".ordertip_dialog .recharge_mode").removeClass("none"),s(".ordertip_dialog .recharge_mode_btn").removeClass("show").addClass("none")),s(".ordertip_dialog .amount_actually .num b").html(t)},getPayCode:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:b.goodsInfo,i={product_id:t.product_id,goods_id:t.goods_id,goodsSource:t.goodsSource||"",flag:t.flag,sale_source:b.sale,report_ext:b.reportExt,is_use_balance:Number(b.isUseBalance),coupon_key:t.coupon_key,cash_coupon_keys:t.cash_coupon_keys,use_cache:!0,success_function:d,error_function:c,timeout_function:l,payment_function:p,get_pay_success_callback:g};b._cart.qrPay(i)},findDefaultGood:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],i=t.find(function(t,i,s){return s[i].index=i,1===t.ext.default})||t[0];return o({goods_id:i.ext.goodsId||"",flag:i.ext.flag||"",product_id:i.ext.productId||""},i.ext)},moveTab:function(t){var i=s(".ordertip_c_goodslist_scroll");"left"==t?(b.goodsTransX>=0||Math.abs(b.goodsTransX)<2.5*b.goodsTabWidth?(b.goodsTransX=0,s("#user-ordertip .btn_left").hide()):b.goodsTransX+=b.goodsTabWidth,s("#user-ordertip .btn_right").show(),i.css("transform","translateX("+b.goodsTransX+"px)")):(Math.abs(b.goodsTransX)+b.listBoxWidth>=b.listScrollWidth||b.listScrollWidth-b.listBoxWidth-Math.abs(b.goodsTransX)<2.5*b.goodsTabWidth?(b.goodsTransX=-(b.listScrollWidth-b.listBoxWidth),s("#user-ordertip .btn_right").hide()):b.goodsTransX-=b.goodsTabWidth,s("#user-ordertip .btn_left").show(),b.goodsTransX-=10,i.css("transform","translateX("+b.goodsTransX+"px)"))},moveRightsCard:function(t){var i=s(".ordertip_rights_scroll");"left"==t?(b.rightsTransX>=0||Math.abs(b.rightsTransX)<2.5*b.rightsTabWidth?(b.rightsTransX=0,s("#user-ordertip .rights_btn_left").hide()):b.rightsTransX+=b.rightsTabWidth,s("#user-ordertip .rights_btn_right").show(),i.css("transform","translateX("+b.rightsTransX+"px)")):(Math.abs(b.rightsTransX)+b.rightsBoxWidth>=b.rightsScrollWidth||b.rightsScrollWidth-b.rightsBoxWidth-Math.abs(b.rightsTransX)<2.5*b.rightsTabWidth?(b.rightsTransX=-(b.rightsScrollWidth-b.rightsBoxWidth),s("#user-ordertip .rights_btn_right").hide()):b.rightsTransX-=b.rightsTabWidth,s("#user-ordertip .rights_btn_left").show(),b.rightsTransX-=10,i.css("transform","translateX("+b.rightsTransX+"px)"))}};var k=void 0,C=function(t,i){var s=null;return function(){var o=this,e=arguments;s&&(clearTimeout(s),s=null),s=setTimeout(function(){t.apply(o,e)},i)}}(function(t){void 0,y||(y=!0,w=(new Date).getTime(),k=new u,k.show(t),window._userOrderTip=k)},200);window.csdn.userOrderTip={show:C,close:f}}(window.csdn=window.csdn||{},document,jQuery);