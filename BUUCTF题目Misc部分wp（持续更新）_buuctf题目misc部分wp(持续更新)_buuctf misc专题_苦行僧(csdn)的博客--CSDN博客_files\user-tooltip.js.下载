"use strict";!function(e,s,n){var t,o="https://g.csdnimg.cn/user-tooltip/2.6/",a={company:"<img src="+o+"images/icon-company.png />",domainGrey:"<img src="+o+"images/icon-domain-grey.png />",domain:"<img src="+o+"images/icon-domain.png />",vip:"<img src="+o+"images/icon-vip.png />",writer:"<img src="+o+"images/icon-writer.png />"},i={getUserProfileUrl:"https://www.csdn.net/community/personal-api/v1/getProfileByUsername",checkFollowUrl:"https://mp-action.csdn.net/interact/wrapper/pc/fans/v1/api/checkFollow",doFollowUrl:"https://mp-action.csdn.net/interact/wrapper/pc/fans/v1/api/follow",doUnfollowUrl:"https://mp-action.csdn.net/interact/wrapper/pc/fans/v1/api/unFollow",ref:"miniprofile",defaultUserProfile:{username:"yanlinpu",avatarurl:"https://profile.csdnimg.cn/1/1/B/1_yanlinpu",vip:!1,expert:!1,nickname:"木林森001",fans_num:"2万",identity:[{name:"专家"},{name:"版主"}],medal:[{type:"203",codename:"111",image:"https://g.csdnimg.cn/static/user-medal/1024huodong.svg"}]}},l={ask:"ASK",bbs:"BBS",me:"ME",download:"DOWNLOAD",blog_detail:"BLOG_DETAIL",blog:"BLOG",live:"LIVE"};if(void 0===e.csdn&&(e.csdn={}),!jQuery)return!1;!function(e){var n=s.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=e,s.getElementsByTagName("head")[0].appendChild(n)}(o+"user-tooltip.css"),e.csdn.userTooltip=function(o){function r(){_[f].flag&&(n("#csdn-userTooltip .person_deliver").mouseover(function(){n(this).hasClass("person_deliver_letter")&&n(this).html("取&nbsp;&nbsp;&nbsp;消")}),n("#csdn-userTooltip .person_deliver").mouseout(function(){n(this).hasClass("person_deliver_letter")&&n(this).html("已关注")})),n("#csdn-userTooltip .person_deliver").click(function(){var s,o;if(b){var a=n(this);if(t||(t=e.location.hostname.indexOf("blog.csdn.net")>-1?e.location.href.indexOf("/article/details")>-1?l.blog_detail:l.blog:l[e.location.hostname.split(".")[0]]?l[e.location.hostname.split(".")[0]]:"ME"),0==_[f].flag){if(!a.hasClass("person_deliver_letter_un"))return!1;s=i.doFollowUrl,n.ajax({type:"post",url:s,dataType:"json",xhrFields:{withCredentials:!0},contentType:"application/json",crossDomain:!0,data:JSON.stringify({follow:f,source:t,username:b}),success:function(s){200==parseInt(s.code)&&(a.html("已关注").removeClass("person_deliver_letter_un").addClass("person_deliver_letter"),a.mouseover(function(){a.html("取&nbsp;&nbsp;&nbsp;消")}),a.mouseout(function(){a.html("已关注")})),e.csdn&&e.csdn.watchBtnChange&&e.csdn.watchBtnChange(1,f),_[f].flag=!0,_[f].followClass="person_deliver person_deliver_letter",_[f].followStr="已关注"}})}else o=i.doUnfollowUrl,n.ajax({type:"post",url:o,xhrFields:{withCredentials:!0},contentType:"application/json",crossDomain:!0,data:JSON.stringify({follow:f,source:t,username:b}),dataType:"json",success:function(s){200==parseInt(s.code)&&(a.html("关注").removeClass("person_deliver_letter").addClass("person_deliver_letter_un"),a.unbind("mouseover"),a.unbind("mouseout")),e.csdn&&e.csdn.watchBtnChange&&e.csdn.watchBtnChange(0,f),_[f].flag=!1,_[f].followClass="person_deliver person_deliver_letter_un",_[f].followStr="关注"}})}else e.location.href="https://passport.csdn.net/"})}function c(e){var s,n=e.offset(),t=n.left,o=n.top,a=e.width(),i=e.height(),l=e[0].getBoundingClientRect(),r={};return l&&Number(l.top,10)<162?(s="left:"+(t-50+a/2)+"px;top:"+(o+i+15)+"px",r.isBottom=!0):(s="left:-999999999px;top:"+(o-165)+"px",r.realPos={left:t-50+a/2,top:o}),r.position=s,r}function d(){n("#csdn-userTooltip").remove()}o&&n.extend(i,o);var p,m,u,f,h,v,g={},_={},w="",y=!1,T='<svg id="csdnc-m-blank" viewBox="0 0 1024 1024" width="100%" height="100%" fill="#ccc"><path d="M516.266311 77.945505a8.788601 8.788601 0 0 0-8.532622 0L121.461878 292.455629a8.319307 8.319307 0 0 0-4.351637 7.252729v424.583284c0 2.986418 1.663861 5.75952 4.351637 7.252729l386.271811 214.510124a8.788601 8.788601 0 0 0 8.532622 0l386.271811-214.510124a8.319307 8.319307 0 0 0 4.351637-7.252729V299.708358a8.319307 8.319307 0 0 0-4.351637-7.252729L516.266311 77.945505z m37.671528-67.066412l386.314473 214.552788a85.027581 85.027581 0 0 1 43.900342 74.276477v424.583284c0 30.802766-16.809266 59.216399-43.943005 74.276477l-386.27181 214.552788c-26.024498 14.462795-57.808516 14.462795-83.875678 0l-386.314473-214.552788a85.027581 85.027581 0 0 1-43.900342-74.276477V299.708358c0-30.802766 16.809266-59.216399 43.943005-74.276477L470.019498 10.879093c26.024498-14.462795 57.808516-14.462795 83.875677 0z"></path></svg>';s.domain="csdn.net";var b=function(e){for(var n=s.cookie.split("; "),t=0;t<n.length;t++){var o=n[t].split("=");if(o[0]==e&&"UD"==e)return decodeURIComponent(o[1]);if(o[0]==e)return decodeURI(o[1])}}("UserName");n(s).on("mouseenter","img[username], #csdn-userTooltip",function(s){function o(e,s,t,o,a,l,r,c,d,p,m,u,h,v,g,_){g=g||"<a href='javascript:void(0)' class='person_deliver person_deliver_letter_un' style='float:right;'>关注</a>";var w="<div id='csdn-userTooltip' style='"+u.position+"' data-mod='popu_60' class='tracking-ad"+(u.isBottom?" csdn-userTooltip-pos":" csdn-userTooltip-default")+"'><div class='person-info-div clearfix'><dt class='touxiang'><img src='"+e+"'></dt><div class='csdn-left-info'><dd class='person_info clearfix'><em class='name"+(s?" name_short":"")+(o?" vipname":"")+"'>"+t+"</em>"+v+"<span class='person-age'>码龄"+_+"年</span></dd>"+d+h+"</div></div><div class='follow-div clearfix'>"+(l?"<em class='fav'>粉丝 "+l+"</em>":"")+g+"<a class='visoter'  target='_blank' href='https://blog.csdn.net/"+f+"?ref="+i.ref+"'>访问主页</a><a class='visoter' target='_blank' href='https://im.csdn.net/im/main.html?userName="+f+"'>私信</a></div>";if(n("body").append(w),u.realPos){var y=n("#csdn-userTooltip"),T=y.height(),b=u.realPos.left,C=u.realPos.top-T-35;y.css({left:b,top:C})}}function l(){n("#csdn-userTooltip").length&&n("#csdn-userTooltip").remove(),f=h.attr("username");var s,t,l,d,p,m,C,x,U,k,B,j,D="";if(!(u.closest("#csdn-userTooltip").length<=0))return!1;n("#csdn-userTooltip").remove(),_[f]?(s=_[f].followClass,t=_[f].followStr,l=_[f].avatarurl,d=_[f].nickname,p=_[f].isVip,m=_[f].hasDomain,C=_[f].isBlogWriter,x=_[f].favsCount,U=_[f].interestname,k=_[f].huistr,j=_[f].age,D=c(h),v=p?"<span class='icon_vip'>"+a.vip+"</span>":"",v+=C?"<span class='icon_writer'>"+a.writer+"</span>":"",w=m?"<li class='has-domain'>"+a.domain+"<a href='https://"+m+".blog.csdn.net'>"+m+".blog.csdn.net</a></li>":"<li class='medal no-domain'>"+a.domainGrey+"暂未设置自定义域名</li>",w="<dd class='csdn-domain'>"+w+"</dd>",b?(s&&t&&(B="<a href='javascript:void(0)' class='"+s+"'>"+t+"</a>"),b===f&&(B=""),o(l,U,d,p,C,x,s,t,k,void 0,y,D,w,v,B,j),r()):(o(l,U,d,p,C,x,s,t,k,void 0,y,D,w,v,B,j),n("#csdn-userTooltip .person_deliver").click(function(){e.location.href="https://passport.csdn.net/"}))):n.ajax({type:"get",url:i.getUserProfileUrl+"?username="+f,contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},crossDomain:!0,success:function(s){var t,l,d,p,m,u,f,C,x,U,k,j,F,P="",L="",N="",O="",S=!1;if(200===Number(s.code,10)){if(t=s.data||i.defaultUserProfile,!(j=t.username))return!1;if(l=t.avatarurl,d=t.nickname||j,F=t.years,p=t.fans_num,m=t.vip,u=t.domain,f=t.expert,C=t.medal?t.medal:[],x=C.length,x>0&&(S=!0),v=m?"<span class='icon_vip'>"+a.vip+"</span>":"",v+=f?"<span class='icon_writer'>"+a.writer+"</span>":"",w=u?"<li class='has-domain'>"+a.domain+"<a href='https://"+u+".blog.csdn.net'>"+u+".blog.csdn.net</a></li>":"<li class='medal no-domain'>"+a.domainGrey+"暂未设置自定义域名</li>",w="<dd class='csdn-domain'>"+w+"</dd>",S){C.length>99999?(U=99999,k=!0):U=C.length;for(var E=0;E<U;E++)N+="<li class='medal' title='"+C[E].codename+"'><img src='"+C[E].image+"' alt='胸章'></li>";k&&(N+='<li class="medal medal-icon">'+T+C.length+"</li>"),N="<dd class='medals'><ul>"+N+"</ul></dd>"}else N="<li class='medal no-medals'>一个勋章都没有...</li>",N="<dd class='medals'><ul>"+N+"</ul></dd>";if(null!=t.identity)for(var I=0;I<t.identity.length;I++){t.identity[I].name&&(I<t.identity.length-1?O+=t.identity[I].name+"<em>|</em>":O+=t.identity[I].name)}if(b){var V=i.checkFollowUrl+"?username="+b+"&follows="+j;n.ajax({type:"get",url:V,xhrFields:{withCredentials:!0},dataType:"json",success:function(e){var s;200===e.code&&e.data&&(0===e.data[j]?(P="person_deliver person_deliver_letter_un",L="关注",y=!1):(P="person_deliver person_deliver_letter",L="已关注",y=!0)),D=c(h),P&&P&&(s="<a href='javascript:void(0)' class='"+P+"'style='float:right;'>"+L+"</a>"),b===j&&(s=""),o(l,O,d,m,f,p,P,L,N,x,y,D,w,v,s,F),g[j]={},g[j].avatarurl=l,g[j].nickname=d,g[j].isVip=m,g[j].hasDomain=u,g[j].isBlogWriter=f,g[j].favsCount=p,g[j].interestname=O,g[j].followClass=P,g[j].followStr=L,g[j].huistr=N,g[j].huicount=x,g[j].flag=y,g[j].age=F,_[j]=g[j],r()}})}else D=c(h),o(l,O,d,m,f,p,P,L,N,x,y,D,w,v,B,F),n("#csdn-userTooltip .person_deliver").click(function(){e.location.href="https://passport.csdn.net/"}),g[j]={},g[j].avatarurl=l,g[j].nickname=d,g[j].isVip=m,g[j].hasDomain=u,g[j].isBlogWriter=f,g[j].favsCount=p,g[j].interestname=O,g[j].huistr=N,g[j].huicount=x,g[j].age=F,_[j]=g[j]}},error:function(e){console.warn(e)}})}var s=s||e.event;return u=n(s.target||s.srcElement),h=n(this),clearTimeout(m),clearTimeout(p),null!=n(this).attr("username")&&(p=setTimeout(l,200)),n(this).attr("followtype")&&(t=n(this).attr("followtype")),!1}),n(s).on("mouseleave","img[username]",function(e){return clearTimeout(p),m=setTimeout(d,200),!1}),n(s).on("mouseleave","#csdn-userTooltip",function(e){return clearTimeout(m),d(),!1})}}(window,document,jQuery),$(function(){window.csdn.userTooltip()});