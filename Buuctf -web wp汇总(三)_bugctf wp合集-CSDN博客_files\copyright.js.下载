!function(){function t(){this.textData=null,this.htmlData=null,a=this}function e(t){if(window.getSelection().getRangeAt(0).toString().length>140){t.preventDefault();var e;e=(window.getSelection()+a.textData).replace(/[\u00A0]/gi," ");window.getSelection(),a.htmlData;if(t.clipboardData)t.clipboardData.setData("text/plain",e);else{if(window.clipboardData)return window.clipboardData.setData("text",e);n(e)}}}function n(t){var e=document.createElement("textarea");e.style.cssText="position: fixed;z-index: -10;top: -50px;left: -50px;",e.innerHTML=t,document.getElementsByTagName("body")[0].appendChild(e),e.select(),document.execCommand("copy")}var a=null,i=function(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent?t.attachEvent("on"+e,n):t["on"+e]=n};t.prototype.init=function(t,n,a){this.textData=n,this.htmlData=a,i(t,"copy",e)},window.csdn=window.csdn?window.csdn:{},csdn.copyright=new t}();