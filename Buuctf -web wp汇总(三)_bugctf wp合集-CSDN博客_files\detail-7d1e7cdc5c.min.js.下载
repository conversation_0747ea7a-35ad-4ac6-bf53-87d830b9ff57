function setCopyBtn(){$(".CopyToClipboard").each(function(){var e=new ZeroClipboard.Client;e.setHandCursor(!0),e.addEventListener("load",function(e){}),e.addEventListener("mouseOver",function(e){var t=e.movie.parentNode.parentNode.parentNode.parentNode.parentNode.nextSibling.innerHTML;t=t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&"),e.setText(t)}),e.addEventListener("complete",function(e,t){alert("代码已经复制到你的剪贴板。")}),e.glue(this,this.parentNode)})}function computePos(){"object"==("undefined"==typeof toolBar?"undefined":_typeof(toolBar))&&(toolBar.setPosX(),toolBar.computePositon())}function getRecommendListUrl(){return null!==getRecommendListUrlArr?getRecommendListUrlArr:(getRecommendListUrlArr=[[],[]],$(".recommend-box div.recommend-item-box").each(function(e,t){if($(t).data("url")){var o=$(t).data("url").toLowerCase().split("://"),n=$(t).data("url").toLowerCase().split("article/details/");getRecommendListUrlArr[0].push(2==o.length?o[1]:o[0]),getRecommendListUrlArr[1].push(2==n.length?"article/details/"+n[1]:n[0])}}),getRecommendListUrlArr)}function baidudatatemp(e,t,o,n){var i="blog",a='<span class="type"><img src="'+blogStaticHost+'dist/components/img/blogType.png" alt=""><span class="tip">博客</span></span>';e.linkUrl.indexOf("download.csdn.net")>-1?(i="download",a='<span class="type"><img src="'+blogStaticHost+'dist/components/img/downloadType.png" alt=""><span class="tip">下载</span></span>'):e.linkUrl.indexOf("edu.csdn.net")>-1&&(i="edu",a='<span class="type"><img src="'+blogStaticHost+'dist/components/img/eduType.png" alt=""><span class="tip">课程</span></span>');var s=highlight.map(function(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")});10==setBaiduJsCount&&(a=""),s="("+s.join("|")+")";var c=new RegExp(s,"gi"),r=/<[^>]*>/g,l=e["abstract"].replace(r,"").replace(c,"<em>$1</em>"),d=e.dispTime.replace(/^(\d*)-/,""),m=e.linkUrl,p=e.title.replace(r,"").replace(c,"<em>$1</em>"),u=m.split("?")[0].split("/").splice(-1)[0],h='"extra":"{\\"utm_medium\\":\\"distribute.'+baiduSearchChannel+".none-task-"+i+"-2~default~baidujs_"+baiduSearchType+"~default-"+t+"-"+u+"-blog-"+articleId+baiduSearchIdentification+'\\",\\"dist_request_id\\":\\"'+distRequestId+'\\",\\"parent_index\\":\\"'+o+'\\"}",',f='<div class="recommend-item-box baiduSearch clearfix" data-url="'+m+'" data-type="'+i+'" data-report-view=\'{"mod":"popu_387",'+h+'"spm":"1001.2101.3001.4242.'+n+'","dest":"'+m+'","strategy":"2~default~baidujs_'+baiduSearchType+'~default","ab":"new","index":"'+t+'"}\'>\t                <div class="content-box">\t\t                <div class="content-blog display-flex">\t\t\t                  <div class="title-box">'+a+'\t\t\t\t                  <a class="tit" href="'+m+'" target="_blank" data-report-click=\'{"mod":"popu_387",'+h+'"spm":"1001.2101.3001.4242.'+n+'","dest":"'+m+'","strategy":"2~default~baidujs_'+baiduSearchType+'~default","ab":"new","index":"'+t+'"}\' data-report-query="utm_medium=distribute.'+baiduSearchChannel+".none-task-"+i+"-2~default~baidujs_"+baiduSearchType+"~default-"+t+"-"+u+"-blog-"+articleId+baiduSearchIdentification+"&spm=1001.2101.3001.4242."+n+'">\t\t\t\t\t                <div class="left ellipsis-online ellipsis-online-1">'+p+'</div>\t\t\t\t                  </a>\t\t\t                  </div>                    <div class="info-box display-flex">                      <div class="info display-flex">                        <span class="info-block">'+d+'</span>                      </div>                    </div>                  </div>                  <div class="desc-box">                    <a href="'+m+'" target="_blank" data-report-click=\'{"mod":"popu_387",'+h+'"spm":"1001.2101.3001.4242.'+n+'","dest":"'+m+'","strategy":"2~default~baidujs_'+baiduSearchType+'~default","ab":"new","index":"'+t+'"}\' data-report-query="utm_medium=distribute.'+baiduSearchChannel+".none-task-"+i+"-2~default~baidujs_"+baiduSearchType+"~default-"+t+"-"+u+"-blog-"+articleId+baiduSearchIdentification+"&spm=1001.2101.3001.4242."+n+'">                      <div class="desc ellipsis-online ellipsis-online-1">'+l+"</div>                    </a>                  </div>                </div>              </div>";return f}function showDownRecommend(e){var t=highlight.map(function(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")});t="("+t.join("|")+")";var o=new RegExp(t,"gi"),n=/<[^>]*>/g,i=e.linkUrl,a=e.title.replace(n,"").replace(o,"<em>$1</em>"),s='<div class="recommend_down">  相关资源：<a class="recommend_down_link" href="'+i+'?spm=1001.2101.3001.5697" target="_blank" data-report-view=\'{"mod":"1612247418_001","spm":"1001.2101.3001.5697","dest":"'+i+'","extend1":"pc"}\' data-report-click=\'{"mod":"1612247418_001","spm":"1001.2101.3001.5697","dest":"'+i+'","extend1":"pc"}\'>'+a+"</a>  </div>";$("#recommendDown").html(s)}function showResult(e,t){var o=[],n=recommendRegularDomainArr||["blog.csdn.net/.+/article/details/","download.csdn.net/download/","edu.csdn.net/course/detail/"],i=new RegExp(n.join("|"));if(!e||e.length<=0)return!1;if(e&&e.length>0){for(var a=0;a<e.length;a++)e[a].noRepeatTit=e[a].title.split("_")[0].replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、<em><\/em>&quot;]/g,"").replace(/\s/g,"").toLowerCase();for(var a=0;a<e.length;a++)for(var s=a+1;s<e.length;s++)e[a].noRepeatTit==e[s].noRepeatTit&&(e.splice(s,1),s--)}if(e&&e.length>0)for(var c=[],a=0;a<e.length;a++)if(e[a].linkUrl.split("?")[0].toLowerCase().indexOf(curentUrl.split("://")[2==curentUrl.split("://").length?1:0].toLowerCase())===-1&&i.test(e[a].linkUrl)&&""!==e[a].title){var r=e[a].linkUrl.split("?")[0].split("//"),l=2==r.length?r[1]:r[0],d="";l=l.replace(/\/$/,"");var m=e[a].linkUrl.split("?")[0].replace("/",""),p=e[a].title.split("_")[0].replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、<em><\/em>&quot;]/g,"").replace(/\s/g,"").toLowerCase();if(2==l.split("article/details/").length){var u=l.split("article/details/")[1];u=u.replace("/",""),d=u,u&&c.indexOf(u)===-1&&(c.push(u),m="article/details/"+u)}$.inArray(l.toLowerCase(),getRecommendListUrl()[0])==-1&&$.inArray(m.toLowerCase(),getRecommendListUrl()[1])==-1&&$.inArray(d,getRecommendListUrl()[1])==-1&&a<10&&p!==articleTitleContent&&articleId!=d&&t!==e[a].linkUrl&&o.push(e[a])}var h=$(".insert-baidu-box").children().not("script").not("dl"),f="",g=0,v=0,w=1;o=o.slice(0,setBaiduJsCount),(!baiduCount||baiduCount<=0)&&(baiduCount=2);for(var a=isBaiduPre?0:baiduCount;a<h.length;a+=baiduCount){for(var s=a;s<a+baiduCount;s++)if(isBaiduPre?o[s]:o[s-baiduCount]){var b=isBaiduPre?2*baiduCount*v-1>0?2*baiduCount*v:0:2*baiduCount*v+baiduCount,x=$(".insert-baidu-box .recommend-item-box").eq(b),y=0;$(x).children().length&&$(x).find("a.tit").attr("data-report-click")&&(y=JSON.parse($(x).find("a.tit").attr("data-report-click")).index),f+=baidudatatemp(isBaiduPre?o[s]:o[s-baiduCount],isBaiduPre?a+s:s+v*baiduCount,y,w),w+=1}$(h[a]).length&&($(h[a]).after(f),v+=1),f="",g=v*baiduCount}if(g<o.length){var k=(isBaiduPre?2*v-1:2*v)*baiduCount+(h.length-1)%baiduCount;if(h.length&&$(h[h.length-1]).find("a.tit")&&$(h[h.length-1]).find("a.tit").attr("data-report-click"))var y=JSON.parse($(h[h.length-1]).find("a.tit").attr("data-report-click")).index;else var y=0;for(var a=g;a<o.length;a++)$(".insert-baidu-box").append(baidudatatemp(o[a],k,y,w)),w+=1,k+=1,g+=1}}function getQueryIdx(){var e=$("div[class^='recommend-item-box type_'],div.recommend-item-box.baiduSearch");$(e).each(function(e,t){var o=$(t).find("a"),n=o.attr("data-report-query");n+="&utm_relevant_index="+(e+1),o.attr("data-report-query",n)})}function reportTop10(){var e=Array.from(document.querySelectorAll(".recommend-box .recommend-item-box.clearfix")).slice(0,10),t=e.map(function(e){return e.dataset.url}),o={spm:"1018.2226.3001.8548",extra:{urlList:t}};window.csdn&&window.csdn.report&&window.csdn.report.reportView(o)}var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ZeroClipboard={version:"1.0.7",clients:{},moviePath:"https://csdnimg.cn/public/highlighter/ZeroClipboard.swf",nextId:1,$:function(e){return"string"==typeof e&&(e=document.getElementById(e)),e.hide=function(){this.style.display="none"},e.show=function(){this.style.display="block"},e.addClass=function(e){this.removeClass(e),this.className+=" "+e},e.removeClass=function(e){for(var t=this.className.split(/\s+/),o=-1,n=0;n<t.length;n++)t[n]==e&&(o=n,n=t.length);return o>-1&&(t.splice(o,1),this.className=t.join(" ")),this},e.hasClass=function(e){return!!this.className.match(new RegExp("\\s*"+e+"\\s*"))},e},setMoviePath:function(e){this.moviePath=e},dispatch:function(e,t,o){var n=this.clients[e];n&&n.receiveEvent(t,o)},register:function(e,t){this.clients[e]=t},getDOMObjectPosition:function(e,t){for(var o={left:0,top:0,width:e.width?e.width:e.offsetWidth,height:e.height?e.height:e.offsetHeight};e&&e!=t;)o.left+=e.offsetLeft,o.top+=e.offsetTop,e=e.offsetParent;return o},Client:function(e){this.handlers={},this.id=ZeroClipboard.nextId++,this.movieId="ZeroClipboardMovie_"+this.id,ZeroClipboard.register(this.id,this),e&&this.glue(e)}};ZeroClipboard.Client.prototype={id:0,ready:!1,movie:null,clipText:"",handCursorEnabled:!0,cssEffects:!0,handlers:null,glue:function(e,t,o){this.domElement=ZeroClipboard.$(e);var n=99;this.domElement.style.zIndex&&(n=parseInt(this.domElement.style.zIndex,10)+1),"string"==typeof t?t=ZeroClipboard.$(t):"undefined"==typeof t&&(t=document.getElementsByTagName("body")[0]);var i=ZeroClipboard.getDOMObjectPosition(this.domElement,t);this.div=document.createElement("div");var a=this.div.style;if(a.position="absolute",a.left=""+i.left+"px",a.top=""+i.top+"px",a.width=""+i.width+"px",a.height=""+i.height+"px",a.zIndex=n,"object"==("undefined"==typeof o?"undefined":_typeof(o)))for(addedStyle in o)a[addedStyle]=o[addedStyle];t.appendChild(this.div),this.div.innerHTML=this.getHTML(i.width,i.height)},getHTML:function(e,t){var o="",n="id="+this.id+"&width="+e+"&height="+t;if(navigator.userAgent.match(/MSIE/)){var i=location.href.match(/^https/i)?"https://":"http://";o+='<object classid="clsid:d27cdb6e-ae6d-11cf-96b8-444553540000" codebase="'+i+'download.macromedia.com/pub/shockwave/cabs/flash/swflash.cab#version=9,0,0,0" width="'+e+'" height="'+t+'" id="'+this.movieId+'" align="middle"><param name="allowScriptAccess" value="always" /><param name="allowFullScreen" value="false" /><param name="movie" value="'+ZeroClipboard.moviePath+'" /><param name="loop" value="false" /><param name="menu" value="false" /><param name="quality" value="best" /><param name="bgcolor" value="#ffffff" /><param name="flashvars" value="'+n+'"/><param name="wmode" value="transparent"/></object>'}else o+='<embed id="'+this.movieId+'" src="'+ZeroClipboard.moviePath+'" loop="false" menu="false" quality="best" bgcolor="#ffffff" width="'+e+'" height="'+t+'" name="'+this.movieId+'" align="middle" allowScriptAccess="always" allowFullScreen="false" type="application/x-shockwave-flash" pluginspage="http://www.macromedia.com/go/getflashplayer" flashvars="'+n+'" wmode="transparent" />';return o},hide:function(){this.div&&(this.div.style.left="-2000px")},show:function(){this.reposition()},destroy:function(){if(this.domElement&&this.div){this.hide(),this.div.innerHTML="";var e=document.getElementsByTagName("body")[0];try{e.removeChild(this.div)}catch(t){}this.domElement=null,this.div=null}},reposition:function(e){if(e&&(this.domElement=ZeroClipboard.$(e),this.domElement||this.hide()),this.domElement&&this.div){var t=ZeroClipboard.getDOMObjectPosition(this.domElement),o=this.div.style;o.left=""+t.left+"px",o.top=""+t.top+"px"}},setText:function(e){this.clipText=e,this.ready&&this.movie.setText(e)},addEventListener:function(e,t){e=e.toString().toLowerCase().replace(/^on/,""),this.handlers[e]||(this.handlers[e]=[]),this.handlers[e].push(t)},setHandCursor:function(e){this.handCursorEnabled=e,this.ready&&this.movie.setHandCursor(e)},setCSSEffects:function(e){this.cssEffects=!!e},receiveEvent:function(e,t){switch(e=e.toString().toLowerCase().replace(/^on/,"")){case"load":if(this.movie=document.getElementById(this.movieId),!this.movie){var o=this;return void setTimeout(function(){o.receiveEvent("load",null)},1)}if(!this.ready&&navigator.userAgent.match(/Firefox/)&&navigator.userAgent.match(/Windows/)){var o=this;return setTimeout(function(){o.receiveEvent("load",null)},100),void(this.ready=!0)}this.ready=!0,this.movie.setText(this.clipText),this.movie.setHandCursor(this.handCursorEnabled);break;case"mouseover":this.domElement&&this.cssEffects&&(this.domElement.addClass("hover"),this.recoverActive&&this.domElement.addClass("active"));break;case"mouseout":this.domElement&&this.cssEffects&&(this.recoverActive=!1,this.domElement.hasClass("active")&&(this.domElement.removeClass("active"),this.recoverActive=!0),this.domElement.removeClass("hover"));break;case"mousedown":this.domElement&&this.cssEffects&&this.domElement.addClass("active");break;case"mouseup":this.domElement&&this.cssEffects&&(this.domElement.removeClass("active"),this.recoverActive=!1)}if(this.handlers[e])for(var n=0,i=this.handlers[e].length;n<i;n++){var a=this.handlers[e][n];"function"==typeof a?a(this,t):"object"==("undefined"==typeof a?"undefined":_typeof(a))&&2==a.length?a[0][a[1]](this,t):"string"==typeof a&&window[a](this,t)}}},$(document).ready(function(){$(".article_content pre").each(function(){var e=$(this);if(void 0!=e.attr("class")){if(e.attr("class").indexOf("brush:")!=-1){var t=e.attr("class").split(";")[0].split(":")[1];e.attr("name","code"),e.attr("class",t)}e.attr("class")&&e.attr("name","code")}}),$(".article_content textarea[name=code]").each(function(){var e=$(this);e.attr("class").indexOf(":")!=-1&&e.attr("class",e.attr("class").split(":")[0])}),window.clipboardData||setTimeout("setCopyBtn()",500)}),function(e){function t(e,t){var o=t-e+1;return Math.floor(Math.random()*o+e)}e.fn.extend({selection:function(){var t="",o=this[0];if(document.selection){var n=document.selection.createRange();t=n.text}else if("number"==typeof o.selectionStart){var i=o.selectionStart,a=o.selectionEnd;i!=a&&(t=o.value.substring(i,a))}return e.trim(t)},parseHtml:function(t){var o=this[0],n=e(this).val();if(document.selection){var i=document.selection.createRange();i.text?i.text=t:e(this).val(n+t)}else if("number"==typeof o.selectionStart){var a=o.selectionStart,s=o.selectionEnd,c=n.substring(0,a),r=n.substring(s);e(this).val(c+t+r)}else e(this).val(n+t);o.selectionStart=o.selectionEnd=e(this).val().length,o.focus()}});var o=e("div.pulllog-box");o.find("button.btn-close").click(function(){o.remove()}),o.find(".pulllog-login").click(function(e){getCookie("UserName")?window.location.reload():(window.csdn.loginBox.show(),e.preventDefault())});var n=function(t){function o(){i.indexOf("windows ")>-1?e("body").css({overflow:"auto","margin-left":"0"}):e("body").css({overflow:"auto"}),e(".imgViewDom").fadeOut(300)}t=t?t:"#content_views";var n=e(t+" img:not(.contentImg-no-view)"),i=navigator.userAgent.toLowerCase();if(0===n.length)return!1;for(var a="",s=0;s<n.length;s++){var c=n[s];a+='<div class="swiper-slide"><img src="'+c.src+'"/></div>'}if(0===e(".imgViewDom").length){e("body").append('<div class="imgViewDom">        <div class="swiper">          <a class= "close-btn">            <img src="'+blogStaticHost+'dist/pc/img/quoteClose1White.png">          </a>          <div class="swiper-wrapper">'+a+'</div>          <div class="swiper-button-prev"></div>          <div class="swiper-button-next"></div>        </div>      </div>');var r=new Swiper(".imgViewDom .swiper",{autoplay:!1,loop:!1,mousewheel:!0,keyboard:{enabled:!0,pageUpDown:!1},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"}});e(".swiper-wrapper").on("click",function(){o()}),e(".imgViewDom a.close-btn").on("click",function(){o()}),e(document).keyup(function(t){if("none"!==e(".imgViewDom").css("display")){var n=t||event,i=n.which||n.keyCode;27==i&&o()}}),e(".imgViewDom").click(function(t){0===e(".imgViewDom>.swiper").has(t.target).length&&o()})}return r};window.CSDNviewImg=n,window.csdn=window.csdn||{},window.csdn.random_num=t}(jQuery),$(function(){function e(e){if(l&&c){var t=$(window).height(),o=$(document).scrollTop(),i=e.offset().top,a=e.height();i<o+t&&i+a>o&&(n(e),c=!1)}}function t(e){return'<a target="_blank" href="'+e.clickUrl+'">                    <div class="ad-top">                        <div class="ad-top-tit">                            <span class="ad-top-topic">'+e.title+'</span>                            <span class="ad-top-type">'+e.titleRight+'</span>                            <span class="ad-top-num">'+e.topCenter+'</span>                        </div>                        <div class="ad-top-tag">                            <span class="ad-top-count">'+e.topRightNumber+'</span>                            <span class="ad-top-text">'+e.topRight+'</span>                        </div>                    </div>                    <div class="ad-con">                        <div class="ad-con-tit">'+e.introTitle+'</div>                        <div class="ad-con-box">                            <span class="ad-con-txt">'+e.intro+'</span>                            <span class="ad-con-go">'+e.buttonText+"</span>                        </div>                    </div>                </a>"}function o(o){if(o)var n={authorUserName:username,contentKey:articleTitle,pageSpm:JSON.parse(articleReport).spm,positions:542};else var n={authorUserName:username,contentKey:articleTitle,pageSpm:JSON.parse(articleReport).spm,positions:544};$.ajax({type:"GET",url:"https://kunpeng.csdn.net/ad/json/list",dataType:"json",data:n,xhrFields:{withCredentials:!0},success:function(n){200==n.code&&n.data.length?(l="articleContentAd",o?$("#content_views").append("<div id="+l+">"+t(n.data[0])+"</div>"):$(i[Math.floor(a/2)]).after("<div id="+l+">"+t(n.data[0])+"</div>"),r=n.data[0].exposureUrl,e($("#"+l))):l=""},error:function(e){l=""}})}function n(e){e.append('<img src="'+r+'" style="display:none;width:0px;height:0px" alt="">')}var i=$("#content_views").children(),a=i.length,s=$("#content_views").height(),c=!0,r="",l="";if(4===articleSource&&a>8&&s>300){var d=(null!=(_ref1=/(; )?(uuid_tt_dd|_javaeye_cookie_id_)=([^;]+)/.exec(window.document.cookie))?_ref1[3]:void 0)||"",m=d?d.substring(d.length-6)%16:0;o(m<=7)}$(document).on("scroll",function(){e($("#"+l))}),a>contentViewsCount&&s>contentViewsHeight}),$(function(){function e(e,t,o){clearTimeout(d),document.selection?l=document.selection.createRange().text:window.getSelection()&&(l=window.getSelection()),l=l.toString().replace(/\n/g,"").trim();var a=!0;if(""!=l){(l.length<=2||$.isNumeric(l)||null!==n(l)||null!==i(l))&&(a=!1);var s="//so.csdn.net/so/search?from=pc_blog_select&q="+encodeURIComponent(l.toString()),c='<div id="articleSearchTip" class="article-search-tip">          '+(a?'<a class="article-href article-search" href="'+s+'" target="_blank" ><img src="'+blogStaticHost+'dist/pc/img/newarticleSearchWhite.png"><span class="article-text">搜索</span></a>':"")+'          <a data-report-click=\'{"spm":"1001.2101.3001.6773"}\' class="article-href article-comment" href="javascript:;" data-type="comment"><img src="'+blogStaticHost+'dist/pc/img/newarticleComment1White.png"><span class="article-text">评论</span></a>          <a class="article-href cnote" href="javascript:;" data-type="cnote"><img src="'+blogStaticHost+'dist/pc/img/newcNoteWhite.png"><span class="article-text">笔记</span></a>          </div > ';$("body").append(c),$(document).on("click","#articleSearchTip .article-search",function(){window.csdn&&window.csdn.report&&window.csdn.report.reportClick({spm:"1001.2101.3001.6607",extra:JSON.stringify({searchword:l.toString()})})}),window.csdn.report&&"function"==typeof window.csdn.report.reportView&&(window.csdn.report.reportView({spm:"1001.2101.3001.6607",extra:JSON.stringify({searchword:l.toString()})}),window.csdn.report.reportView({spm:"1001.2101.3001.6773"})),$("#articleSearchTip").css({top:o.pageY-t-8,left:e})}}function t(e){var t="";e.download_url&&(t='<p id="git-hub-download" data-report-click=\'{"spm":"3001.9234"}\'>下载</p>');var o='<div class="git-hub-box" data-report-view=\'{"spm":"3001.9232"}\' id="git-hub-box-bt">        <div class="git-hub-top">          <div class="git-hub-info">            <p class="info"> <img src="https://gitcode.net/uploads/-/system/appearance/favicon/1/icon.png" alt=""> <span class="text" title="'+e.format_path+'">'+e.format_path+'</span></p>            <p class="next">已加入 <span class="tag">GitHub加速计划</span> 每天同步更新</p>          </div>          <div class="git-hub-btn">            <p>收藏</p>            '+t+'          </div>        </div>        <div class="git-hub-bottom">          <div class="git-hub-desc">            '+e.description+'          </div>          <div class="git-hub-newnps">            <span>'+e.language+'</span>            <p><img src="https://img-home.csdnimg.cn/images/20230307102412.png" alt=""><span>'+e.star_count+'</span></p>            <p><img src="https://img-home.csdnimg.cn/images/20230307102356.png" alt=""><span>'+e.fork_count+"</span></p>            <span>"+e.updated_at_format+"更新</span>          </div>        </div>      </div>";return o}function o(e,o,n){$.ajax({url:"https://gitcode.net/api/v4/project_card/base_info",type:"get",data:{url:e},xhrFields:{withCredentials:!1},success:function(i){200==i.status&&i.data&&!$.isEmptyObject(i.data)?($("body").append(t(i.data)),"none"===$(".git-hub-box").css("display")&&$(".git-hub-box").show(),m=e,$(".git-hub-box").css({top:o+"px",left:n+"px"}),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportView({spm:"3001.9232"}),$(".git-hub-box").click(function(e){window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"3001.9232"}),window.open(i.data.link)}),$("#git-hub-download").click(function(e){window.open(i.data.download_url)}),$(".git-hub-box").mouseleave(function(){$(".git-hub-box").hide(),u=!1}),$(".git-hub-box").mouseenter(function(){u=!0})):m=""},error:function(e){m=""}})}function n(e){var t="(https?|http|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]";return s=e.match(t),s}function i(e){var t=/[Γ∫∞​∀∈∋N∏∑ϕ⎡⎤⇒⇏⟹⇑⇓⇕⨿%†‡…⋯◯△▽⪇≦≰≨≯⋍≂≅≆∮]/g;return s=e.match(t),s}function a(e){var t=$("#commentQuote"),o=$("#pcCommentSideBox"),n='<span class="comment-quote-bar"></span>'+e+'<span data-report-click=\'{"spm":"1001.2101.3001.6774"}\' class="comment-quote-close"><img src="'+blogStaticHost+"dist/pc/img/quoteClose1"+skinStatus+'.png"></span>';t.length?t.html(n):(o.prepend('<div id="commentQuote" class="comment-quote">'+n+"</div>"),$("#commentSideBoxshadow").fadeIn()),$("input.bt-comment-show").trigger("click")}var c=0,r=0,l="",d=null;$("#content_views").mouseup(function(t){$(".article-search-tip").remove();var o=t.pageY-r,n=Math.min(t.pageX,c);return Math.abs(n)<=5&&Math.abs(o)<=5?(clearTimeout(d),!1):(o=o>0?o:0,clearTimeout(d),void(d=setTimeout(function(){e(n,o,t)},1e3)))}).mousedown(function(e){c=e.pageX,r=e.pageY,l="",$(".git-hub-box").hide(),$("#articleSearchTip").remove()});var m="",p=0,u=!1,h=null;$(document).on("mouseenter mouseleave","#article_content a",function(e){if("mouseenter"==e.type){var t=$(this).offset().top+20,n=$(this).offset().left;$(this).offset().top-p>246&&(t=$(this).offset().top-226),clearTimeout(h),$(this).attr("href").indexOf("github.com")==-1&&$(this).attr("href").indexOf("gitcode.net")==-1||(m!==$(this).attr("href")?($(".git-hub-box").length>=1&&$(".git-hub-box").remove(),o($(this).attr("href"),t,n)):($(".git-hub-box").show(),$(".git-hub-box").css({top:t+"px",left:n+"px"})))}else"mouseleave"==e.type&&(h=setTimeout(function(){u||$(".git-hub-box").hide()},300))}),$(window).scroll(function(){p=$(window).scrollTop()}),$(document).on("click",".article-href",function(e){var t=$(this).data("type");if(void 0!==t)switch(t){case"comment":if(getCookie("UserName"))if(l.toString().length){var o=$("#csdn-toolbar").height(),n=$(document).scrollTop();n<52&&(o=2*o),a(l.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;")),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}else $("#articleSearchTip").remove(),showToast({text:"选中内容为空，请重新选中",bottom:"10%",zindex:9e3,speed:500,time:1500});else window.csdn.loginBox.show();break;case"cnote":if(getCookie("UserName"))if(l.toString().length){l=l.toString().replace(/</g,"&lt;").replace(/>/g,"&gt;");var i=new Date,s=i.getMonth()+1,c=i.getDate(),r=i.getFullYear()+"年"+s+"月"+c+"日",d={noteTabName:"博客笔记",topicTitle:"博客摘录「 "+articleTitle+"」 "+r,content:articleDetailUrl?"文章来源：<a href='"+articleDetailUrl+"'>"+articleDetailUrl+"</a><br/>":"",source:{hostname:"blog.csdn.net",url:articleDetailUrl,title:articleTitle,selection:l}};$.ajax({url:"https://note-ccloud.csdn.net/v1/community/content/sendNote",type:"post",contentType:"application/json",dataType:"json",data:JSON.stringify(d),xhrFields:{withCredentials:!0},success:function(e){},error:function(e){}});var m=articleTitle;articleTitle&&articleTitle.length>=80&&(m=articleTitle.slice(0,77)+"..."),blogApiAxios({article_id:"",title:"博客摘录「 "+m+"」"+r,description:"",content:"<p>"+l+"</p>\n",type:"original",status:2,read_type:"public",authorized_status:!1,check_original:!1,source:"pc_postedit",not_auto_saved:1,cover_type:1,vote_id:0,scheduled_time:0,is_new:1,tags:"笔记"}),e.stopPropagation?e.stopPropagation():e.cancelBubble=!0}else $("#articleSearchTip").remove(),showToast({text:"选中内容为空，请重新选中",bottom:"10%",zindex:9e3,speed:500,time:1500});else window.csdn.loginBox.show()}}),$(document).on("click",".comment-quote-close",function(e){var t=$("#commentQuote");t.animate({height:0},300,function(){t.remove()})})}),window.apiOpenEditor=function(e){console.log(e,8886666),showToast({text:"请稍后再试",bottom:"10%",zindex:9e3,speed:500,time:2e3})},function(){function e(e){o.hasClass("no-login")||($("div.article_content").removeAttr("style"),0==$(".column-mask").length&&$(".hide-article-box").hide(),o.hasClass("fans_read_more")&&($("#btnAttent").hasClass("attented")||e.originalEvent&&($(".tool-attend").trigger("click"),window.csdn.report.reportClick({mod:"popu_376",spm:"1001.2101.3001.4248",extend1:"粉丝登录阅读更多"}))))}function t(t,o){var n=($(window).height(),$("div.article_content")),i=n.height();$("#btn-readmore").attr("height",o),i>2e3?(n.css({height:"2000px",overflow:"hidden"}),t.click(e)):t.parent().hide()}var o=$(".btn-readmore");o.length>0||$(".vip-mask").length>0?currentUserName?t(o,3):t(o,2):0==$(".column-mask").length&&$(".hide-article-box").addClass("hide-article-style");var n=window.location.hash,i=RegExp(/\#/);n.match(i)&&($(".btn-readmore").parent().hide(),$("div.article_content").removeAttr("style")),window.csdn=window.csdn?window.csdn:{},window.csdn.clearReadMoreBtn=e}(),$(function(){function e(){var e=$(".recommend-ask-box").find("a.btn-link-toask").data("href");e=e?e:$(".recommend-ask-box").find(".btn-gochat").data("href");var t=$(".recommend-ask-box").find("#txtChat").val();t=t&&t.length?t.trim():$(".recommend-ask-box").find("a.btn-link-toask").data("defaultvalue"),t=t?t:$(".recommend-ask-box").find("#txtChat").data("defaultvalue");var o=document.createElement("a");o.style.display="none",o.href=e+"&query="+encodeURIComponent(t),o.target="_blank",document.body.appendChild(o),o.click(),document.body.removeChild(o)}$(".recommend-ask-box").find("#txtChat").on("keyup",function(t){var o=t.which||t.keyCode;13===o&&e()}),$(".recommend-ask-box").find(".btn-gochat").on("click",function(){e()}),$(".recommend-ask-box").find(".btn-link-toask").on("click",function(){e()})}),$(function(){function e(e){e.list&&e.list.length&&($.each(e.list,function(e,t){var o="",n="";articleId==t.articleId&&(o="active"),t.preView&&(n='<span class="try-read">试读</span>'),i.columnlistStr+='<div class="columnlist-list-item" title="'+t.title+'"><a href="'+t.url+'" class="columnlist-list-href" data-report-view=\'{"spm":"1001.2101.3001.6326"}\' data-report-click=\'{"spm":"1001.2101.3001.6326"}\'><span class="text '+o+'">'+t.title+"</span>"+n+"</a></div>"}),e.total-e.page*e.size>0?(i.columnPage+=1,i.columnlistStr+='<p class="look-more-article active">查看更多<img class="look-more-img" src="'+blogStaticHost+"dist/pc/img/newLookMore1"+skinStatus+'.png" title="查看更多"></p>'):i.columnlistStr+='<p class="look-more-article">暂无更多内容</p>',$(document).find(i.columnlistHeight).append(i.columnlistStr),i.columnlistStr="")}function t(e,t,o,n){$.ajax({type:"GET",url:blogUrl+e,dataType:"json",xhrFields:{withCredentials:!0},data:t,success:function(e){200==e.code&&o(e.data)},error:function(e){}})}function o(e,t){if(getCookie("UserName")){var o=blogUrl+"phoenix/web/v1/subscribe/subscribe";$.ajax({url:o,type:"post",dataType:"json",data:e,xhrFields:{withCredentials:!0},success:function(e){200===e.code&&(e.data.status&&showToast({text:e.data.msg,bottom:"10%",zindex:9003,speed:500,time:1500}),t.removeClass("articleColumnFreeBt").html("已订阅"))}})}else window.csdn.loginBox.show({spm:"1001.2101.3001.8607"})}function n(e,t,o){if($(document).find(e).length){var n=$(document).find(e).outerHeight(!0),i=$(document).find(t).outerHeight(!0),a=$(document).find(o).outerHeight(!0);$(document).find(o).css({height:n-i+a})}}var i={columnlistBox:"#columnlistBox",columnlistClose:".columnlist-close",columnlistContent:".columnlist-content-box",columnlistHeight:".columnlist-list-box",columnlistShow:$(".bt-columnlist-show"),columnlistShadow:$(".directory-boxshadow"),columnlistItem:$(".column-group"),columnlistUrl:"phoenix/web/v1/column/article-list",columnId:"",columnPage:1,columnlistStr:""};i.columnlistItem.on("click",function(e){$(this).children().length>1&&!$(e.target).is(".item-target")&&($(this).hasClass("open")?($(this).removeClass("open"),$(this).find(".dec").show()):($(this).addClass("open"),$(this).find(".dec").hide()))});var a="";studyLearnWord&&(a='<span class="column_studyvip_free-active">'+studyLearnWord+"</span>"),i.columnlistShow.on("click",function(o){i.columnId=$(this).data("id");var s="",c="",r="",l="",d="",m="";isOwner||($(this).data("free")?s=$(this).data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>':'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnFreeBt" data-id="'+$(this).data("id")+'">订阅专栏</a>':($(this).data("join")?$(this).data("studyvip")?$(this).data("subscribe")?s='<a class="columnlist-con-btn article-column-subscribe">已订阅</a>            <a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6331"}\' data-report-click=\'{"spm":"1001.2101.3001.6331"}\'>8折续费 '+a+"</a>":(s='<a data-report-view=\'{"spm":"1001.2101.3001.6328","extend1":"会员已订阅"}\' data-report-click=\'{"spm":"1001.2101.3001.6328","extend1":"会员已订阅"}\' style="'+($(this).data("studysubscribe")?"display:inline-block":"display:none")+'" data-type="true" class="columnlist-con-btn article-column-subscribe" data-id="'+$(this).data("id")+'">              会员已订阅            </a>',s+='<a data-report-view=\'{"spm":"1001.2101.3001.6328","extend1":"会员免费订"}\' data-report-click=\'{"spm":"1001.2101.3001.6328","extend1":"会员免费订"}\' style="'+($(this).data("studysubscribe")?"display:none":"display:inline-block")+'" data-type="false" class="column-studyvip-free studyvip-unsubscribe column-studyvip-ajax" data-id="'+$(this).data("id")+'">              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">会员免费订            </a>',s+=$(this).data("studysubscribe")?'<a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6330"}\' data-report-click=\'{"spm":"1001.2101.3001.6330"}\'>8折续费 '+a+"</a>":'<a class="column-studyvip-discount column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6329"}\' data-report-click=\'{"spm":"1001.2101.3001.6329"}\'>8折续费 '+a+"</a>"):s=$(this).data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>            <a class="column-studyvip-free column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6327"}\' data-report-click=\'{"spm":"1001.2101.3001.6327"}\'>              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">超级会员免费看              '+a+"            </a>":'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnBt" data-id="'+$(this).data("id")+'" data-price="'+$(this).data("price")+'" data-oldprice="'+$(this).data("oldprice")+'" data-report-view=\'{"spm":"1001.2101.3001.6324"}\' data-report-click=\'{"spm":"1001.2101.3001.6324"}\'>订阅专栏</a>            <a class="column-studyvip-free column-studyvip-pass" data-report-view=\'{"spm":"1001.2101.3001.6325"}\' data-report-click=\'{"spm":"1001.2101.3001.6325"}\'>              <img class="column-studyvip-icon" src="'+blogStaticHost+'dist/components/img/studyVipIcon.png">超级会员免费看              '+a+"            </a>":s=$(this).data("subscribe")?'<a class="columnlist-con-btn article-column-subscribe">已订阅</a>':'<a class="columnlist-con-btn columnlist-con-subscribe articleColumnBt" data-id="'+$(this).data("id")+'" data-price="'+$(this).data("price")+'" data-oldprice="'+$(this).data("oldprice")+'" data-report-view=\'{"spm":"1001.2101.3001.6324"}\' data-report-click=\'{"spm":"1001.2101.3001.6324"}\'>订阅专栏</a>',
c='<div class="columnlist-con-price">¥'+$(this).data("price")+"</div>")),l='<div class="columnlist-head">                  <div class="columnlist-head-l">                      <a href="'+$(this).data("url")+'" target="_blank"><img class="columnlist-head-img" src="'+$(this).data("img")+'" alt=""></a>                  </div>                  <div class="columnlist-head-r">                      <div class="columnlist-head-tit"><a href="'+$(this).data("url")+'" target="_blank">'+$(this).data("title")+'</a></div>                      <div class="columnlist-head-con">                          <span>'+$(this).data("sum")+" 篇文章</span>                          <span>"+$(this).data("people")+" 订阅</span>                      </div>                  </div>                </div>",d='<div class="columnlist-con">                  <div class="columnlist-con-autor">作者：'+nickName+"</div>"+c+'<div class="columnlist-con-bt">                      <a class="columnlist-con-btn columnlist-con-look" target="_blank" href="'+$(this).data("url")+'" data-report-view=\'{"spm":"1001.2101.3001.6323"}\' data-report-click=\'{"spm":"1001.2101.3001.6323"}\'>查看详情</a>'+s+"</div>                </div>",m='<div class="columnlist-list">                <div class="columnlist-list-tit">专栏目录</div>                <div class="columnlist-list-box"></div></div>',r='<div class="columnlist-box" id="columnlistBox">                <div class="columnlist-content-box">                <div class="columnlist-content">'+l+d+m+'</div>                <div class="columnlist-close">                    <img src="'+blogStaticHost+"dist/pc/img/nerClose"+skinStatus+'.png" alt="">                </div>                </div>              </div>',$("body").append(r),i.columnlistShadow.fadeIn(function(){$("html,body").addClass("forbidden-htmlbody-scroll"),$(document).find(i.columnlistBox).addClass("open"),n(i.columnlistBox,i.columnlistContent,i.columnlistHeight)}),t(i.columnlistUrl,{columnId:i.columnId,page:i.columnPage},e,i.columnlistBox);var p=o||window.event;p.stopPropagation?p.stopPropagation():p.cancelBubble=!0}),i.columnlistShadow.click(function(){$(i.columnlistClose).trigger("click")}),$(document).on("click",i.columnlistClose,function(){$(document).find(i.columnlistBox).removeClass("open"),i.columnlistShadow.fadeOut(function(){$("html,body").removeClass("forbidden-htmlbody-scroll"),$(document).find(i.columnlistBox).remove(),i.columnPage=1,i.columnId="",i.columnlistStr=""})}),$(document).on("click",".look-more-article",function(o){if($(this).hasClass("active")){$(this).fadeOut(function(){$(this).remove()}),t(i.columnlistUrl,{columnId:i.columnId,page:i.columnPage},e,i.columnlistBox);var n=o||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}}),$(document).on("click",".articleColumnFreeBt",function(e){var t=$(this).data("id");o({columnId:t},$(this));var n=e||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}),$(window).resize(function(){n(i.columnlistBox,i.columnlistContent,i.columnlistHeight)})}),$(function(){function e(){this.rewardSettingJson=null,this.rewardInfo={name:"",sendAmount:"",money:""},this.minSendAmount=10,this.minMoney=5,this.availableAmount=0,this.presetTitle=[],this.currentRedEnvolope=null,this.hasShowGuideBottom=getCookie("hasShowRRGuideBottom"),this.hasShowGuideSide=getCookie("hasShowRRGuideSide"),this.redEnvolope=$("#redEnvolope"),this.formRewardBox=$(".comment-reward-box"),this.dialogRewardModal=$(".comment-rewarddialog-box"),this.commentRewardForm=this.dialogRewardModal.find("#commentRewardForm"),this.txtName=this.commentRewardForm.find("#txtName"),this.txtSendAmount=this.commentRewardForm.find("#txtSendAmount"),this.txtMoney=this.commentRewardForm.find("#txtMoney"),this.getSetting()}function t(){this.commentForm=$("#commentform"),this.txtComment=$("#comment_content"),this.commentCountObj=this.commentForm.find("em"),this.commtCode=$("#commentCode"),this.domLi="",this.btnShowMore=$("#btnMoreComment"),this.commentBox=$("div.comment-list-box"),this.commentBoxDefault=$("div.has-comment-con"),this.commentPagination=$("#commentPage"),this.commentLineBox="",this.commentTxt=$("#comment_replyId"),this.cancelBtn=this.commentForm.find(".btn-cancel"),this.articleId=$("article_id"),this.curH=0,this.pageCount=0,this.pageIndex=1,this.curFloor=1,this.commentCount=0,this.commentPageObj=null,this.commentFontLimit=1e3,this.firstLoad=!0,this.pageSize=10,this.showAll=!1,this.commentFold={pageSize:10,pageIndex:1,pageCount:0,total:0,fold:"fold"},this.lookBadComment=$("#lookBadComment"),this.lookUnFlodComment=$("#lookUnFlodComment"),this.commentUnFold={pageSize:10,pageIndex:1,pageCount:0,total:0,fold:"unfold"},this.lookFlodComment=$("#lookFlodComment"),this.lookGoodComment=$("#lookGoodComment"),this.isCommentFold=!1,this.initHotComment=!0,this.bindBtnFoldMore(),this.commentFoldTextareaStr(),this.initTxt(),this.init(),this.bindBtn(),this.bindTxt(),this.commentCode(),this.comment(),this.lookMoreComment(),this.lookMoreFlodComment(),this.gosideComment(),this.commentSideTitClose(),this.cancelBtnBindEvent(),this.getPagination=function(e){var t=e,o=function(o,n){o&&(o.list&&0==o.list.length&&e-1>=1&&(t=e-1,n.commentPageObj.go(t),1==t&&$("#commentPage").hide()),n.pageCount=o.pageCount,n.commentCount=o.floorCount,n.commentPageObj.render({count:o.floorCount,current:t}),n.renderData(o.list))};this.getData(t,o)},this.bindLikedClick(),this.bindRedRewardClick()}function o(e){username===e.userName?window.isBloger='<span class="is_bloger comment_status_tip">作者</span>':window.isBloger=""}function n(){$("pre.code2").each(function(e,t){hljs.highlightBlock(t),hljs.getLines(t.innerHTML).length>1?hljs.lineNumbersBlock(t):hljs.lineNumbersBlock(t,{singleLine:!0}),$(t).removeClass("code2")})}function i(){$(document).on("click",".comment-reward-item",function(e){if(!getCookie("UserName"))return window.csdn.loginBox.show(),!1;var t=$(this).data("id");w.showRedEnvolope(t)})}function a(){$(document).on("click",".comment-like",function(e){if(!getCookie("UserName"))return window.csdn.loginBox.show(),!1;var t=$(this).hasClass("liked")?"undigg":"digg",o=$(this).data("commentid"),n=this,i={articleId:articleId,commentId:o};$.ajax({url:blogUrl+"phoenix/web/v1/comment/"+t,type:"post",dataType:"json",data:i,xhrFields:{withCredentials:!0},success:function(e){var t=$(n).find("span").text()?parseInt($(n).find("span").text()):0;if($(n).hasClass("liked")){var o=t-1;o=o>0?o:"",$(n).removeClass("liked"),$(n).find(".unclickImg").css("display","inline-block"),$(n).find(".clickedImg").css("display","none"),$(n).find("span").text(o).css("color","#999AAA"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6253",extend1:'{"praise":0}'})}else $(n).addClass("liked"),$(n).find(".unclickImg").css("display","none"),$(n).find(".clickedImg").css("display","inline-block"),$(n).find("span").text(t+1).css("color","#FC5531"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6253",extend1:'{"praise":1}'});$(n).parents(".right-box").children(".comment-like").html($(n).html()),$(".comment-like-img-hover").css("display","none")}})})}function s(e){var t="";return 0==e.length?"":(t=e.replace(/</g,"&lt;"),t=t.replace(/>/g,"&gt;"))}function c(){for(var e="",t=1;t<=72;t++){var o="";o=t<10?"00"+t:t>=10&&t<100?"0"+t:t,e+='<img class="emoticon-monkey-img" data-emoticon="[face]emoji:'+o+'.png[/face]" src="https://g.csdnimg.cn/static/face/emoji/'+o+'.png">'}return e}function r(e){var t=window.location.href.split(e)[1];return null!=t?t:""}var l="https://liveapi.csdn.net/";e.prototype.bindInit=function(){var e=this;this.rewardInfo.money=this.rewardSettingJson["default"].variables.DEFAULT_AMOUNT,this.rewardInfo.sendAmount=this.rewardSettingJson["default"].variables.DEFAULT_NUM;var t=$(".guide-rr-first");t.remove();var o=$(".rr-guide-box");o.remove();var n=function(t,o){var n=t.parents(".ipt-box"),i=n.find(".notice"),a=t.val().trim();if(0===a.length)"sendAmount"===o?i.text("请输入红包数量"):"money"===o&&i.text("红包金额最低"+e.minMoney+"元"),n.addClass("error");else{var s=!0;"sendAmount"===o?parseInt(a)<e.minSendAmount&&(i.text("红包个数最小为"+e.minSendAmount+"个"),s=!1):"money"===o&&(parseFloat(a)<e.minMoney?(i.text("红包金额最低"+e.minMoney+"元"),e.commentRewardForm.find(".balance-info-box").removeClass("error"),e.commentRewardForm.find(".link-charge").text("前往充值 >"),s=!1):parseFloat(a)>parseFloat(e.availableAmount)?(e.commentRewardForm.find(".balance-info-box").addClass("error"),e.commentRewardForm.find(".link-charge").text("余额不足请充值")):(e.commentRewardForm.find(".balance-info-box").removeClass("error"),e.commentRewardForm.find(".link-charge").text("前往充值 >"))),s?n.removeClass("error"):n.addClass("error");var c=e.txtSendAmount.parents(".ipt-box"),r=e.txtSendAmount.val(),l=e.txtMoney.val();if(r&&parseFloat(l)>=5&&.1*parseFloat(r)>parseFloat(l)){var d=Math.floor(parseFloat(l)/.1);c.find(".notice").text("红包个数范围为"+e.minSendAmount+"-"+d+"个"),c.addClass("error")}else parseInt(r)>10&&c.removeClass("error")}},i=function(e,t,o){var n=e.indexOf(t),i="";return n===-1?e:"-"===t&&0!==n?e.slice(0,n):("."===t&&e.match(/^(\.|-\.)/)&&(i=n?"-0":"0"),i+e.slice(0,n+1)+e.substr(n+1,2).replace(o,""))},a=function(e,t){e=t?i(e,".",/\./g):e.split(".")[0];var o=t?/[^-0-9.]/g:/[^-0-9]/g;return e.replace(o,"")},s=function(){var t=e.txtName.val(),o=e.txtSendAmount.val(),n=e.txtMoney.val();""!==t&&""!==o&&""!==n&&0===e.commentRewardForm.find(".ipt-box.error,.balance-info-box.error").length?(e.commentRewardForm.find(".btn-submit").removeAttr("disabled"),e.commentRewardForm.find(".pay-info").find(".price").text(parseFloat(n).toFixed(2)),e.commentRewardForm.find(".pay-info").show()):(e.commentRewardForm.find(".btn-submit").attr("disabled",!0),e.commentRewardForm.find(".pay-info").hide())};this.txtName.val(this.rewardInfo.name),this.commentRewardForm.find(".btn-random").click(function(){e.randomTitle()}),this.dialogRewardModal.find("a.btn-form-close").click(function(){e.hideRewardForm()}),this.commentRewardForm.find("input:text").keyup(function(){var e=$(this).attr("name");"sendAmount"===e?$(this).val(a($(this).val(),!1)):"money"===e&&$(this).val(a($(this).val(),!0)),n($(this),e),s()}),this.commentRewardForm.find("input:text").blur(function(){var e=$(this).attr("name");n($(this),e),s()}),this.formRewardBox.find(".btn-remove-reward").click(function(){e.formRewardBox.hide(),e.rewardInfo={name:"",sendAmount:"",money:""},e.txtName.val(e.rewardSettingJson["default"].defaultTitle),e.txtSendAmount.val(""),e.txtMoney.val(""),e.commentRewardForm.find(".error").removeClass("error")}),this.commentRewardForm.find(".btn-submit").click(function(){e.rewardInfo.name=e.txtName.val(),e.rewardInfo.sendAmount=e.txtSendAmount.val(),e.rewardInfo.money=e.txtMoney.val(),e.formRewardBox.find(".info").text(e.rewardInfo.name);var t=new Number(e.rewardInfo.money).toFixed(2);e.formRewardBox.find(".price").text(t+"元"),e.formRewardBox.show(),e.dialogRewardModal.hide()}),this.commentRewardForm.find(".btn-cancel").click(function(){e.hideRewardForm()})},e.prototype.getSetting=function(){var e=this;$.get("https://img-home.csdnimg.cn/data_json/jsconfig/redPacket.json",function(t){e.rewardSettingJson=t,e.rewardInfo.name=t["default"].defaultTitle,e.presetTitle=t["default"].presetTitle,e.bindInit(),e.formRewardBox.css(t["default"].redCardSty),e.redEnvolope.find(".top").css(t["default"].preOpenSty),e.redEnvolope.find(".red-openbtn").css(t["default"].preOpenBtnSty),e.redEnvolope.find("footer").css(t["default"].preFooterSty),e.redEnvolope.find("a.rule").attr("href",t.blog.ruleUrl)})},e.prototype.getRedPacketVerifyBatch=function(){var e=$(".comment-reward-item[data-uncheck='1']");if(e.length>0){for(var t=[],o=0;o<e.length;o++)t.push($(e[o]).data("id"));$.ajax({type:"GET",url:l+"mp/live-gift/wrapper/v1/redpacket/verify/batch",dataType:"json",data:{orderNoList:t.join(","),username:currentUserName},xhrFields:{withCredentials:!0},success:function(e){if(200==e.code){var t=e.data.records;if(t&&t.length>0)for(var o=0;o<t.length;o++){var n=t[o],i=$('.comment-reward-item[data-id="'+n.orderNo+'"]');i.removeAttr("data-uncheck"),i.attr("data-status",n.status)}}},error:function(){showToast({text:"用户信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})}},e.prototype.getUserAmount=function(e){var t=this;$.ajax({type:"GET",url:l+"mp/live-gift/wrapper/v1/wallet/balance",xhrFields:{crossDomain:!0,withCredentials:!0},success:function(o){200==o.code?(t.availableAmount=o.data.availableAmount,t.commentRewardForm.find(".balance").text(o.data.availableAmount),""!==t.rewardInfo.sendAmount&&""===t.txtSendAmount.val()&&(t.txtSendAmount.val(t.rewardInfo.sendAmount).attr("placeholder","请填写红包数量(最小"+t.minSendAmount+"个)"),t.txtSendAmount.trigger("blur")),""!==t.rewardInfo.money&&""===t.txtMoney.val()&&(t.txtMoney.val(t.rewardInfo.money).attr("placeholder","请填写总金额(最低"+t.minMoney+"元)"),t.txtMoney.trigger("blur")),e()):showToast({text:"用户信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})},error:function(){showToast({text:"用户信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})},e.prototype.showRewardForm=function(){var e=this;this.getUserAmount(function(){e.dialogRewardModal.show()})},e.prototype.hideRewardForm=function(){var e=this;e.txtName.val(e.rewardInfo.name),e.txtSendAmount.val(e.rewardInfo.sendAmount),e.txtMoney.val(e.rewardInfo.money),e.dialogRewardModal.hide()},e.prototype.randomTitle=function(){var e=this,t=e.presetTitle.filter(function(t){return t!==e.rewardInfo.name}),o=t[Math.floor(Math.random()*t.length)];return e.txtName.val(o),e.rewardInfo.name=o,e.txtName.trigger("blur"),o},e.prototype.getReceiveList=function(e,t){var o=this;$.ajax({type:"GET",url:l+"mp/live-gift/wrapper/v1/redpacket/receivedInfos",data:{orderNo:e},dataType:"json",xhrFields:{withCredentials:!0},success:function(n){if(200==n.code){o.currentRedEnvolope=n.data;var i=$('.comment-reward-item[data-id="'+e+'"]');i.attr("data-status",n.data.redPacketStatus),o.buildRedEnvolopeHtml(e,n.data,t)}else showToast({text:n.message,bottom:"10%",zindex:9e3,speed:500,time:2e3})},error:function(){showToast({text:"红包信息获取失败，请重试。",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})},e.prototype.buildRedEnvolopeHtml=function(e,t,o){var n=this,i=t.redPacketInfo,a=this.redEnvolope.find("#preOpen");a.find(".clearTpaErr").attr("src",i.creatorAvatar),a.find(".author").text(i.title),a.find(".red-openbtn").off("click"),a.find(".red-openbtn").on("click",function(){n.receiveRedPacket(e)});var s=this.redEnvolope.find("#opened");s.find(".creatorUrl").attr("href","https://blog.csdn.net/"+i.creatorUserName),s.find(".clearTpaErr").attr("src",i.creatorAvatar);var c=i.creatorNickName?i.creatorNickName:i.creatorUserName;s.find(".author > .tt").text(c),s.find(".author > .tt").data("creatorUserName",i.creatorUserName);var r='<a href="'+this.rewardSettingJson.blog.ruleUrl+'" class="rule" target="_blank">红包规则</a>',l="";switch(t.redPacketStatus){case"received":l='<div class="my-receive">'+t.currentUserReceivedInfo.receivedMoney+'</div>          <div div class="wallet-msg">已放入你的CSDN钱包，<a href="https://i.csdn.net/#/wallet/index" target="_blank">            点击查看            </a>          </div>          <div class="receive-msg">已领取'+i.receivedAmount+"/"+i.totalAmount+"个，共"+i.receivedMoney+"/"+i.totalMoney+"元</div>"+r;break;case"completed":l='<div class="receive-msg">          <span>'+i.totalAmount+"个红包，"+i.completeTimeInterval+"被抢光</span>        </div>"+r;break;case"expired":l='<div class="receive-msg">          <span>该红包已过期，</span>已领取'+i.receivedAmount+"/"+i.totalAmount+"个，共"+i.receivedMoney+"/"+i.totalMoney+"元</div>"+r}s.find(".receive-box header").html(l);var d=function(e){return e=e<10?"0"+e:e},m=function(e){var t=new Date(e);return d(t.getMonth()+1)+"-"+d(t.getDate())+" "+d(t.getHours())+":"+d(t.getMinutes())};if(t.receivedInfos&&t.receivedInfos.length>0){for(var p="",u=0;u<t.receivedInfos.length;u++){var h=t.receivedInfos[u],f="completed"!==t.redPacketStatus&&"expired"!==t.redPacketStatus||!h.lucky?"":'<img class="best" src="'+blogStaticHost+'dist/pc/img/best.png" title="手气最佳">';p+='<div class="user-item">          <div class="user-info">            <a href="https://blog.csdn.net/'+h.receiverUserName+'" target="_blank">              <img src="'+h.receiverAvatar+'" alt="" />            </a>            <div class="u-info">              <div class="name">'+h.receiverNickName+'</div>              <div class="time">'+m(h.receiveTime)+'</div>            </div>          </div>          <div class="money">            <span>'+h.receivedMoney+"元</span>"+f+"</div>        </div>"}s.find(".receive-box .receive-list").html(p)}o()},e.prototype.receiveRedPacket=function(e){var t=this.redEnvolope.find("#preOpen"),o=this.redEnvolope.find("#opened"),n=this;t.find(".red-openbtn").addClass("rotate-start"),$.ajax({type:"POST",url:l+"mp/live-gift/v1.0/redPacket/receiveRedPacket",contentType:"application/json; charset=utf-8",headers:{requestID:(new Date).getTime(),xappid:"pc_blog",xdeviceid:"pc_blog"},data:JSON.stringify({orderNo:e,username:currentUserName}),xhrFields:{withCredentials:!0},success:function(i){if(500===i.code)showToast({text:i.message,bottom:"10%",zindex:9e3,speed:500,time:2e3}),t.find(".red-openbtn").removeClass("rotate-start");else{200!==i.code&&i.message&&(400108103===i.code?showToast({html:i.message+"<a style='color: #4ea1db;text-decoration: underline;' href='https://bbs.csdn.net/?type=4&header=0&utm_source=empty_dialog' target='_blank'>更多红包</a>",bottom:"10%",zindex:9e3,speed:500,time:2e3}):showToast({text:i.message,bottom:"10%",zindex:9e3,speed:500,time:2e3}));var a=o.find(".author > .tt").data("creatorUserName");username!==currentUserName&&a!==username&&($("#btnAttent").hasClass("attented")||(window.followByRR=!0,$("#btnAttent").trigger("click"))),n.getReceiveList(e,function(){setTimeout(function(){t.find(".red-openbtn").addClass("red-hidebtn"),t.find(".top").addClass("slide-top"),t.find("footer").addClass("slide-bottom"),setTimeout(function(){t.css("display","none"),o.css("display","flex"),t.find(".top").removeClass("slide-top"),t.find("footer").removeClass("slide-bottom"),t.find(".red-openbtn").removeClass("rotate-start").removeClass("red-hidebtn")},800)},800)})}},error:function(){showToast({text:"请求失败，请重试",bottom:"10%",zindex:9e3,speed:500,time:2e3})}})},e.prototype.showRedEnvolope=function(e){var t=this;this.getReceiveList(e,function(){"received"!==t.currentRedEnvolope.redPacketStatus&&"expired"!==t.currentRedEnvolope.redPacketStatus&&"completed"!==t.currentRedEnvolope.redPacketStatus||(t.redEnvolope.find("#preOpen").hide(),t.redEnvolope.find("#opened").css("display","flex")),t.redEnvolope.show(),t.redEnvolope.find(".close-btn").one("click",function(){t.redEnvolope.hide(),t.redEnvolope.find("#preOpen").removeAttr("style"),t.redEnvolope.find("#opened").removeAttr("style")})})};'<ul class="comment-list">                <li class="comment-line-box">                <div style="display: flex;width: 100%;">                <a target="_blank" href="'+blogUrl+username+'">                <img src="'+avatar+'" username="'+username+'" alt="'+username+'" class="avatar">                </a>                <div class="right-box "><div class="new-info-box clearfix">                <a target="_blank" href="'+blogUrl+username+'"><span class="name ">'+nickName+'<img class="is_bloger" src="'+blogStaticHost+'dist/components/img/<EMAIL>"></span></a><span class="colon">:</span><span class="floor-num"></span><span class="new-comment">这篇文章对你有帮助吗？作为一名程序工程师，在评论区留下你的困惑或你的见解，大家一起来交流吧！</span>                </div></div></div></li></ul>','<img class="read-reply-img" src="'+blogStaticHost+'dist/pc/img/replyComment.png">';t.prototype.lookMoreComment=function(){var e=this;$(document).on("click","#lookGoodComment",function(t){e.getPagination(e.commentUnFold.pageIndex)})},t.prototype.lookMoreFlodComment=function(){var e=this;$(document).on("click","#lookBadComment",function(t){e.getFoldList(e.commentFold)})},t.prototype.gosideComment=function(){$(document).on("click",".go-side-comment",function(){getCookie("UserName")?($("#commentSideBoxshadow").fadeIn(),$(this).hasClass("focus")&&$("#comment_content").focus()):window.csdn.loginBox.show()})},t.prototype.commentSideTitClose=function(){var e=this;$(document).on("click",".comment-side-tit-close",function(t){t.target.className.indexOf("comment-side-tit-close")>-1&&($("#commentSideBoxshadow").fadeOut(function(){e.isCommentFold&&(e.isCommentFold=!1,e.commentUnFold.pageIndex=1,e.getPagination(e.commentUnFold.pageIndex),$("#pcFlodCommentSideBox").hide(),$("#pcCommentSideBox").show(),$("#pcFlodCommentSideBox .comment-fold-content").html(""))}),$("#commentQuote").remove())}),$(document).keyup(function(e){var t=e.which,o=$("#commentSideBoxshadow");27==t&&"block"==o.css("display")&&($(e.target).is("#comment_content")||o.trigger("click"))})},t.prototype.getData=function(e,t){if(!getCookie("UserName"))return!1;var o=this;o.pageIndex=void 0!==e?e:o.pageIndex;var n=null,i="&commentId="+u;o.pageIndex>1&&(i=""),$.ajax({url:blogUrl+"phoenix/web/v1/comment/list/"+articleId+"?page="+o.pageIndex+"&size="+o.pageSize+"&fold=unfold"+i,type:"post",xhrFields:{withCredentials:!0},success:function(e){200===e.code&&(n=e.data,n&&(o.curFloor=n.floorCount-(o.pageIndex-1)*o.pageSize,o.commentUnFold.pageIndex+=1,o.commentUnFold.pageCount=n.pageCount,o.commentUnFold.total=n.floorCount,o.commentFold.total=n.foldCount,o.commentUnFold.pageIndex>o.commentUnFold.pageCount?(o.lookGoodComment.hide(),o.commentFold.total>0?o.lookFlodComment.show():o.lookFlodComment.hide()):(o.lookGoodComment.show(),o.lookFlodComment.hide()),$(".tool-item-comment .count").html(n.count),$(".comment-side-tit-count .count").html(n.count),$(".has-comment-tit .count").html(n.count),$(".has-comment-bt-left .count").html(n.count),$(".look-flod-comment .count").html(o.commentFold.total),$(".comment-fold-tit .count").html(o.commentFold.total),o.commentFold.total>0||o.lookFlodComment.hide(),n.count>0?(getCookie("UserName")||$("#pcCommentBox .unlogin-comment-tit").text(n.count+" 条评论"),this.firstLoad&&($("#pcCommentBox").removeClass("comment-box-nostyle"),$("#pcCommentBox .has-comment").css({display:"block"}))):getCookie("UserName")?($("#pcCommentBox").addClass("comment-box-nostyle"),$("#pcCommentBox .has-comment").css({display:"none"})):($("#pcCommentBox .unlogin-comment-tit").text("参与评论"),$("#pcCommentBox").removeClass("comment-box-nostyle"),$("#pcCommentBox .has-comment").css({display:"none"}))))},complete:function(){t&&t(n,o)}})},t.prototype.init=function(){window.localStorage.getItem("AM_comment_data")&&window.localStorage.getItem("AM_comment_id")==articleId&&(this.txtComment[0].focus(),this.txtComment.val(window.localStorage.getItem("AM_comment_data")),this.commentTxt.val(window.localStorage.getItem("AM_comment_replyId")),$(window).scrollTop(this.txtComment.offset().top));var e=function(e,t){if(null!==e){var o="";if(getCookie("UserName")||(o="登录 "),t.pageCount=e.pageCount,t.curFloor=e.floorCount,t.commentCount=e.floorCount,t.btnShowMore.html("<span>"+o+"查看 "+e.count+' 条热评</span><img class="look-more-comment" src="'+blogStaticHost+'dist/pc/img/arrowDownComment.png">'),t.renderData(e.list),getCookie("UserName")?(t.btnShowMore.parent("div.opt-box").remove(),t.showAll=!0,e.floorCount<=t.pageSize?t.commentPagination.addClass("d-none"):0===$("#btnMoreComment").length&&t.commentPagination.removeClass("d-none")):e.floorCount<=3?(t.btnShowMore.parent("div.opt-box").remove(),t.showAll=!0):e.floorCount<=t.pageSize?t.commentPagination.addClass("d-none"):0===$("#btnMoreComment").length&&t.commentPagination.removeClass("d-none"),null===t.commentPageObj?e.pageCount>0&&(t.commentPageObj=new Paging,t.commentPageObj.init({target:t.commentPagination,pagesize:t.pageSize,count:t.commentCount,current:1,firstTpl:"",lastTpl:"",callback:function(e,o,n){t.getPagination(e)}})):t.commentPageObj.render({count:t.commentCount}),""!==u){var n=$("div.has-comment-con ul.comment-list:first-child").find("li.comment-line-box");$(n).each(function(e,t){if(u==$(t).data("commentid")){var o=$(document).scrollTop(),i=$("#csdn-toolbar").height(),a=16;o<52&&(i=2*i);var s=$(n)[e].offsetTop-i-a;return setTimeout(function(){$("html,body").animate({scrollTop:s},200)},850),!1}})}}else t.btnShowMore.parent("div.opt-box").remove(),t.showAll=!0};this.firstLoad?this.getData(1,e):blogMoveHomeArticle&&this.btnShowMore.parent("div.opt-box").remove()},t.prototype.initTxt=function(){var e=this;$("#comment_content").length&&$("#comment_content").focus(function(){e.addId(e);var t=e.commentFontLimit-parseInt($(this).val().length);e.commentCountObj.text(t>=0?t:0),e.cancelBtnToggle(!1),e.commentLineBox.length>0&&($("#commentEditBox").siblings(".comment-list-item").find("a.btn-reply").html("回复"),e.commentLineBox.find(".comment-edit-box").remove())}),getCookie("UserName")&&($(document).click(function(t){if(!$(t.target).is("div.comment-edit-box *")&&!$(t.target).is(".right_recommend_comment_button *")&&!$(t.target).is(".reply")&&($(".comment-operate-isshow").hide(),e.txtComment.length>0)){var o=e.commentFontLimit-e.txtComment.val().length;e.commentCountObj.text(o>=0?o:0)}}),$(document).click(function(e){$(e.target).is(".comment-line-box *")||($("#commentEditBox").siblings(".comment-list-item").find("a.btn-reply").html("回复"),$("#commentEditBox").remove())})),$("input.btn-comment-defualt").click(function(){$(".tool-item-comment").trigger("click")}),$(".tool-item-comment").click(function(){setTimeout(function(){$("#pcCommentBox").show(),$("#comment_content").focus()},0)})},t.prototype.bindBtn=function(){function e(e){return"svg"===this.nodeName||getCookie("UserName")?(t.commentCount>t.pageSize&&t.commentPagination.removeClass("d-none"),$(this).parent("div.opt-box").remove(),$(this).parent().parent("div.opt-box").remove(),t.showAll=!0,t.curH=0,void(window.event?window.event.cancelBubble=!0:e.stopPropagation())):(csdn.loginBox.show(),!1)}var t=this;$(document).on("click",".comment-operate-item",function(e){var o=$(e.target).data("type");if(void 0!==o)switch(o){case"report":var n=$(e.target).parents("li.comment-line-box"),i=n.data("commentid"),a=n.data("replyname");window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){window.csdn.feedback({type:"blog",rtype:"comment",rid:i,reportedName:a,submitOptions:{contentUrl:articleDetailUrl},callback:function(){showToast({text:"感谢您的举报，我们会尽快审核！",bottom:"10%",zindex:9e3,speed:500,time:1500})}})}});break;case"reply":var s=$(e.target)[0].innerText;if($(this).find("a.btn-reply").text("回复"),$(e.target)[0].innerText="回复"==s?"收起":"回复",t.isCommentFold){if(!getCookie("UserName"))return window.csdn.loginBox.show(),!1;$("#commentEditBox").remove();var c=t.commentFoldTextareaStr("commentEditBox","New");if(!$("#commentEditBox").length&&"回复"==s){$(e.target).parents(".comment-list-item").after(c);var r=$(e.target).parents(".comment-line-box");t.addId(t,"new"),t.comment(),t.bindTxt(),curComment={},curComment.Id=r.data("commentid"),curComment.User=r.data("replyname"),curComment.text=r.find(".name").text()+(r.find(".nick-name").length?" 回复 "+r.find(".nick-name").text():"")+r.find(".comment").text(),t.cancelBtnToggle(!0),t.replayComment(curComment),t.txtComment.trigger("focus"),t.commentCode()}}else"回复"==s?"":$("#commentEditBox").remove();break;case"delete":var l=$(e.target).data("nickname"),d=$(e.target).parents("li.comment-line-box"),i=d.data("commentid");showConfirm({tit:"删除评论",text:'确认将 "'+l+'" 的评论删除吗？',isBlack:"Black"==skinStatus,rightText:"确定",zindex:9e3,success:function(){t.commentIsDelete(i)},cancel:function(){}});break;case"istop":var l=$(e.target).data("nickname"),m="false"!=$(e.target).attr("data-status"),p=$(e.target).parents("li.comment-line-box"),i=p.data("commentid");showConfirm({tit:m?"取消置顶":"置顶评论",text:m?'确认将 "'+l+'" 的评论取消置顶吗？':'确认将 "'+l+'" 的评论置顶吗？',isBlack:"Black"==skinStatus,rightText:"确定",zindex:9e3,success:function(){t.commentIsTop(i,m)},cancel:function(){}});break;case"blacklist":var m="false"!=$(e.target).attr("data-status"),u=$(e.target).data("username"),l=$(e.target).data("nickname");showConfirm({tit:m?"取消屏蔽":"屏蔽用户",text:isCurrentUserVip?m?'确认将 "'+l+'" 取消屏蔽吗？取消后对方即可在您的内容下进行评论':'确认将 "'+l+'" 屏蔽吗？屏蔽后对方将无法在您的内容下进行评论':"屏蔽用户为会员权益，您可加入会员行使该权益",isBlack:"Black"==skinStatus,rightText:isCurrentUserVip?"确定":"开通会员",zindex:9e3,success:function(){var e="";isCurrentUserVip?(e=m?"1001.2101.3001.6915":"1001.2101.3001.6913",t.commentIsBlack(username,u,m)):(window.open("https://www.csdn.net/vip?utm_source=commentblocking","_blank"),e="1001.2101.3001.6917"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:e})},cancel:function(){}})}}),this.btnShowMore.click(e),this.btnShowMore.find("svg").click(e)},t.prototype.bindTxt=function(){var e=this;this.txtComment.keyup(function(){var t=e.commentCountObj,o=e.commentFontLimit-$(this).val().length;o<0?t.text(0):t.text(o),$(this).val().length||$(this).data("replayUser")?e.cancelBtnToggle(!0):e.cancelBtnToggle(!1)})},t.prototype.renderData=function(e){var t=this,o=t.commentFoldTextareaStr("commentEditBox","New");t.buildListDom(e,!1,this,!0),$(".comment-list li.comment-line-box").on("click",function(e){if($(e.target).data("flag")&&(t.commentLineBox&&t.commentLineBox[0]!=$(this)[0]&&t.commentLineBox.find(".comment-edit-box").remove(),0==$(this).find(".comment-edit-box").length)){if(getCookie("UserName")){getCookie("UserName")&&($(this).append(o),$("#closeNew").on("click",function(e){$(this).parents(".comment-edit-box").remove()}),t.addId(t,"new"),t.comment(),t.bindTxt(),curComment={},curComment.Id=$(this).data("commentid"),curComment.User=$(this).data("replyname"),curComment.text=$(this).find(".name").text()+($(this).find(".nick-name").length?" 回复 "+$(this).find(".nick-name").text():"")+$(this).find(".comment").text(),t.cancelBtnToggle(!0),t.replayComment(curComment),t.txtComment.trigger("focus"),t.commentCode());var n=$(document).scrollTop(),i=$("#csdn-toolbar").height();n<52&&(i=2*i)}else csdn.loginBox.show();t.commentLineBox=$(this)}}),n()},t.prototype.commentFoldTextareaStr=function(e,t){if($("#isShowCommentAuth").length&&1==$("#isShowCommentAuth").data("type"))var o='<span class="tip">&nbsp;|&nbsp;博主筛选后可见</span>';else var o="";var n='<div class="comment-edit-box d-flex" id="'+e+'">            <form id="commentform'+t+'">                <textarea class="comment-content" name="comment_content" id="comment_content'+t+'" placeholder="请发表有价值的评论， 博客评论不欢迎灌水，良好的社区氛围需大家一起维护。" maxlength="1000"></textarea>                <div class="comment-operate-box">                    <div class="comment-operate-l">                        <span id="tip_comment" class="tip">还能输入<em>1000</em>个字符</span>'+o+'                    </div>                    <div class="comment-operate-r">                        <div class="comment-operate-item comment-emoticon">                            <img class="comment-operate-img" data-url="" src="'+blogStaticHost+'dist/pc/img/commentEmotionIcon.png" alt="表情包">                            <span class="comment-operate-tip">插入表情</span>                            <div class="comment-operate-isshow comment-emoticon-box comment-emoticon'+t+'" style="display: none;">                                <div class="comment-emoticon-img-box"></div>                            </div>                        </div>                        <div class="comment-operate-item comment-code comment-code'+t+'">                            <img class="comment-operate-img" data-url="" src="'+blogStaticHost+'dist/pc/img/commentCodeIcon.png" alt="代码片">                            <span class="comment-operate-tip">代码片</span>                            <div class="comment-operate-isshow comment-code-box">                                <ul id="commentCode'+t+'">                                    <li><a data-code="html">HTML/XML</a></li>                                    <li><a data-code="objc">objective-c</a></li>                                    <li><a data-code="ruby">Ruby</a></li>                                    <li><a data-code="php">PHP</a></li>                                    <li><a data-code="csharp">C</a></li>                                    <li><a data-code="cpp">C++</a></li>                                    <li><a data-code="javascript">JavaScript</a></li>                                    <li><a data-code="python">Python</a></li>                                    <li><a data-code="java">Java</a></li>                                    <li><a data-code="css">CSS</a></li>                                    <li><a data-code="sql">SQL</a></li>                                    <li><a data-code="plain">其它</a></li>                                </ul>                            </div>                        </div>                        <div class="comment-operate-item">                            <input type="hidden" id="comment_replyId'+t+'" name="comment_replyId'+t+'">                            <input type="hidden" id="article_id'+t+'" name="article_id'+t+'" value="'+$("#article_id").val()+'" >                            <input type="hidden" id="comment_userId'+t+'" name="comment_userId'+t+'" value="">                            <input type="hidden" id="commentId'+t+'" name="commentId'+t+'" value="">                            <a data-report-click=\'{"spm":"1001.2101.3001.4227"}\'><input type="submit" class="btn-comment btn-comment-input" value="评论"></a>                        </div>                    </div>                </div>            </form>        </div>';
return n},t.prototype.buildListDom=function(e,t,o,n){for(var i="",a="",s="",c=0;c<e.length;c++){var r=e[c].info,l=e[c].sub,d=void 0!==l?l.length:0;if(i='<ul class="comment-list">',i+=o.buildHtml(r,d,!0,r.userName?r.userName:"",n),d>0){i+='<li class="replay-box" style="display:block">',i+='<ul class="comment-list">';for(var m=0;m<d;m++){var p=l[m];null!==p&&(i+=this.buildHtml(p,0,!1,p.parentUserName?p.parentUserName:"",n))}d>1&&(i+='<div class="second-look-more">查看全部 '+d+' 条回复<img src="'+blogStaticHost+"dist/pc/img/commentArrowDown"+skinStatus+'.png"></div>'),i+="</ul>",i+="</li>"}if(i+="</ul>",t)a+=i;else{if(o.commentBox.append($(i)),2==o.commentUnFold.pageIndex&&0==c&&o.initHotComment&&e.length){var u=e[0].info,h="";u.content=this.replaceQuote(u.content);var f=this.getQuoteInfo(u.content);f.length>1?(quotename=f[1],quotecontent=this.getCodeInfo(f[2]),quotecontent=this.replaceNewUBB(quotecontent),s=this.getCodeInfo(f[3]),s=this.replaceNewUBB(s),s='<span class="quote">引用“<font color="black">'+quotename+"</font>”的评论：</span><blockquote>"+quotecontent+"</blockquote>"+s):(s=this.replaceNewUBB(f[0]),s=this.getCodeInfo(s));var g,v=null!==u.redEnvelopeInfo,b=v?JSON.parse(u.redEnvelopeInfo):null;v&&(g=JSON.stringify({spm:"3001.9224",extend1:{orderNo:u.orderNo}}),b.money=parseFloat(parseInt(b.money)/100).toFixed(2)),h='<div class="hot-comment-box" data-commentid="'+u.commentId+'"><a class="hot-comment-href" target="_blank" href="'+blogUrl+u.userName+'"><img src="'+u.avatar+'">'+u.nickName+'</a><span class="hot-comment-tag">热评</span>'+(v?'<div class="comment-reward-item" data-report-click=\''+g+'\' data-uncheck="1" data-commentid="'+u.commentId+'" data-id="'+u.orderNo+'">        <span span class="amount">'+b.money+"元</span>        </div > ":"")+'<div class="hot-comment-con">'+s+"</div></div>",o.commentBoxDefault.html($(h)),o.initHotComment=!1,$("#pcCommentBox").show()}o.curFloor--}}return!e.length&&o.commentUnFold.total<=0&&o.commentBoxDefault.html(""),t?a:void w.getRedPacketVerifyBatch()},t.prototype.addId=function(e,t){"new"==t?(e.commentForm=$("#commentformNew"),e.txtComment=$("#comment_contentNew"),e.commentCountObj=e.commentForm.find("em"),e.commtCode=$("#commentCodeNew"),e.commentTxt=$("#comment_replyIdNew"),e.articleId=$("#article_idNew")):(e.commentForm=$("#commentform"),e.txtComment=$("#comment_content"),e.commentCountObj=e.commentForm.find("em"),e.commtCode=$("#commentCode"),e.commentTxt=$("#comment_replyId"),e.articleId=$("#article_id"))},t.prototype.bindBtnFoldMore=function(){var e=this;$(document).on("click","#lookFlodComment",function(){e.isCommentFold=!0,e.commentFold.pageIndex=1,e.getFoldList(e.commentFold),$("#pcFlodCommentSideBox").show(),$("#pcCommentSideBox").hide(),$("#pcCommentSideBox .comment-list-box").html("")}),$(document).on("click","#lookUnFlodComment",function(){e.isCommentFold=!1,e.commentUnFold.pageIndex=1,e.getPagination(e.commentUnFold.pageIndex),$("#pcFlodCommentSideBox").hide(),$("#pcCommentSideBox").show(),$("#pcFlodCommentSideBox .comment-fold-content").html("")}),t.prototype.getFoldList=function(t){$.ajax({url:blogUrl+"phoenix/web/v1/comment/list/"+articleId+"?page="+e.commentFold.pageIndex+"&size="+e.commentFold.pageSize+"&fold="+e.commentFold.fold,type:"post",xhrFields:{withCredentials:!0},success:function(t){if(200===t.code){result=t.data;result.foldCount;if($(".tool-item-comment .count").html(result.count),$(".comment-side-tit-count .count").html(result.count),$(".has-comment-tit .count").html(result.count),$(".has-comment-bt-left .count").html(result.count),$(".look-flod-comment .count").html(result.foldCount),$(".comment-fold-tit .count").html(result.foldCount),result.foldCount>0?e.lookFlodComment.show():e.lookFlodComment.hide(),result.list.length){var o=e.buildListDom(result.list,!0,e,!1),n=document.createElement("div");n.classList.add("comment-fold-con","comment-list-box","comment-operate-item"),n.innerHTML=o,$("#pcFlodCommentSideBox .comment-fold-content").append(n),e.commentFold.pageIndex+=1,e.commentFold.pageCount=result.pageCount,e.commentFold.pageIndex>e.commentFold.pageCount?e.lookBadComment.hide():e.lookBadComment.show()}}}})}};var d="";if(t.prototype.buildHtml=function(e,t,n,i,a){var s="",c="",r="",l="",m=!1,p="",u="";if(e.content=this.replaceUrl(e.content),n){d=i,p=this.getQuote(e.content),e.content=this.replaceQuote(e.content);var h=this.getQuoteInfo(e.content);h.length>1?(r=h[1],l=this.getCodeInfo(h[2]),l=this.replaceNewUBB(l),s=this.getCodeInfo(h[3]),s=this.replaceNewUBB(s),s='<span class="quote">引用“<font color="black">'+r+"</font>”的评论：</span><blockquote>"+l+"</blockquote>"+s):(s=this.replaceNewUBB(h[0]),s=this.getCodeInfo(s)),username===getCookie("UserName")&&(m=!0)}else{var h=this.getReplyInfo(e.content);c=h[1];var f=h.length>2?this.getQuoteInfo(h[2]):this.getQuoteInfo(h[0]);f.length>1?(r=f[1],l=this.getCodeInfo(f[2]),l=this.replaceNewUBB(l),s=this.getCodeInfo(f[3]),s=this.replaceNewUBB(s),s='<span class="quote">引用“<font color="black">'+r+"</font>”的评论：</span><blockquote>"+l+"</blockquote>"+s):(s=this.replaceNewUBB(f[0]),s=this.getCodeInfo(s))}if(o(e),n&&p&&""!=p&&(u='<div class="comment-quote-item">'+p+"</div>"),n)var g="";else var g="comment-line-box-hide";var v,w=n&&null!==e.redEnvelopeInfo,b=w?JSON.parse(e.redEnvelopeInfo):null;w&&(v=JSON.stringify({spm:"3001.9224",extend1:{orderNo:e.orderNo}}),b.money=parseFloat(parseInt(b.money)/100).toFixed(2));var x="匿名用户"!=e.userName?blogUrl+e.userName:"javascript:void(0);",$='<li class="comment-line-box '+g+'" data-commentid="'+e.commentId+'" data-replyname="'+e.userName+'">            <div class="comment-list-item">                <a class="comment-list-href" target="_blank" href="'+x+'"><img src="'+e.avatar+'" username="'+e.userName+'" alt="'+e.userName+'" class="avatar"></a>                <div class="right-box '+(c?"reply-box":"")+'" >                    <div class="new-info-box clearfix"><div class="comment-top"><div class="user-box"><a class="name-href" target = "_blank" href = "'+x+'" > <span class="name '+(n?"":"mr-8")+'">'+(n?e.nickName+"</span></span>"+isBloger+"</a> "+(e.isTop&&a?' <span class="is_top comment_status_tip"> 置顶</span> ':""):e.nickName+"</span>"+isBloger+'</a> <span class="text">回复</span> <span class="nick-name"> '+(e.parentNickName?e.parentNickName:"")+"</span>")+'<span class="date"  title="'+e.postTime+'">'+e.dateFormat+'</span><div class="opt-comment"><a class="btn-bt  btn-report"><img class="btn-report-img" src="'+blogStaticHost+'dist/pc/img/commentLookMore.png" title=""><div class="hide-box">'+(m&&a?' <span class="hide-item hide-top" data-type="istop" data-nickname="'+e.nickName+'" data-status="'+!!e.isTop+'" data-report-click=\'{"spm": '+(e.isTop?'"1001.2101.3001.6901"':'"1001.2101.3001.6511"')+',"extend1":'+(e.isTop?'"取消置顶"':'"置顶评论"')+"}'>"+(e.isTop?"取消置顶":"置顶评论")+"</span>":"")+(a&&getCookie("UserName")===username&&e.userName!==getCookie("UserName")?' <span class="hide-item hide-blacklist" data-type="blacklist" data-username="'+e.userName+'" data-nickname="'+e.nickName+'" data-status="'+!!e.isBlack+'" data-report-click=\'{"spm": '+(e.isBlack?'"1001.2101.3001.6914"':'"1001.2101.3001.6912"')+',"extend1":'+(e.isTop?'"取消屏蔽"':'"屏蔽用户"')+"}'> "+(e.isBlack?"取消屏蔽":"屏蔽用户")+"</span> ":"")+(isOwner||getCookie("UserName")===e.userName?'<span class="hide-item hide-delete" data-type="delete" data-nickname="'+e.nickName+'">删除</span>':"")+'<span data-type="report" class="hide-item hide-report"> 举报</span></div></a><img class="comment_img_replay" src="'+blogStaticHost+"dist/pc/img/newCommentReply"+skinStatus+'.png"><a class="btn-bt  btn-reply" data-type="reply" data-flag="true">回复</a></div></div >'+(w?'<div class="comment-reward-item" data-report-click=\''+v+'\' data-uncheck="1" data-commentid="'+e.commentId+'" data-id="'+e.orderNo+'">        <span span class="amount">'+b.money+"元</span>        </div > ":"")+'<div class="comment-like '+(e.diggArr.indexOf(getCookie("UserName"))!=-1?"liked":"")+'" data-commentid='+e.commentId+" >"+(0!==parseInt(e.digg)?"<span>"+e.digg+"</span>":"<span></span>")+'<img class="comment-like-img unclickImg" src="'+blogStaticHost+"dist/pc/img/commentLike"+skinStatus+'.png" title="点赞"><img class="comment-like-img comment-like-img-hover" style="display:none" src="'+blogStaticHost+'dist/pc/img/commentLikeHover.png" title="点赞"><img class="comment-like-img clickedImg" src="'+blogStaticHost+'dist/pc/img/commentLikeActive.png" title="取消点赞"></div></div><div class="comment-center">'+u+'<div class="new-comment">'+this.setNewLine(s)+"</div></div></div></div></div></li>";return $},t.prototype.setNewLine=function(e){var t=/\<pre name="code2" ([\s\S]*?)\>([\s\S]*?)\<\/pre\>/gi,o=[];if(e.replace(t,function(e){o.push(e)}),o.length)for(var n=e.replace(t,function(e){return"[csdnCommentPreCode]"}),i=n.replace(/\n/g,"</br>"),a=0;a<o.length;a++)i=i.replace("[csdnCommentPreCode]",o[a]);else var i=e.replace(/\n/g,"<br/>");return i},t.prototype.getReplyInfo=function(e){var t=e.split(/\[reply]([\s\S]*?)\[\/reply\][\r\n]{0,1}/gi);return t},t.prototype.getQuoteInfo=function(e){var t=e.split(/\[quote=([\w#\.]+)\]([\s\S]*?)\[\/quote\][\r\n]{0,2}/gi);return t},t.prototype.getCodeInfo=function(e){var t=e.replace(/\[code=([\w#\.]+)\]([\s\S]*?)\[\/code\]/gi,function(e,t,o){return""==$.trim(o)?"":'<pre name="code2" class="code2 '+t+'"><code>'+s(o.trim())+"</code></pre>"});return t},t.prototype.replaceNewUBB=function(e){return e=e.replace(/\[face\]([^\]]+):([^\]]+)\[\/face\]/gi,'<img src="//g.csdnimg.cn/static/face/$1/$2" alt="表情包"/>')},t.prototype.replaceUrl=function(e){var t=/(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#\/%?=~_|!:,.;]+[-A-Za-z0-9+&@#\/%=~_|]/g,o=/\[code=([\w#\.]+)\]([\s\S]*?)\[\/code\]/gi,n=[];if(e.replace(o,function(e){n.push(e)}),n.length)for(var i=e.replace(o,function(e){return"[csdnCommentCode]"}),a=i.replace(t,function(e){return'<a class="comment-match-url" target="_blank" href="'+e+'"><img src="https://img-home.csdnimg.cn/images/20230310025252.png" alt=""> 网页链接</a>'}),s=0;s<n.length;s++)a=a.replace("[csdnCommentCode]",n[s]);else var a=e.replace(t,function(e){return'<a class="comment-match-url" target="_blank" href="'+e+'"><img src="https://img-home.csdnimg.cn/images/20230310025252.png" alt=""> 网页链接</a>'});return a},t.prototype.replaceQuote=function(e){return e=e.replace(/\[quote\]([\s\S]*?)\[\/quote\]/gi,"")},t.prototype.getQuote=function(e){var t="";return e.replace(/\[quote\]([\s\S]*?)\[\/quote\]/gi,function(e){t=e}),t=t.replace("[quote]","").replace("[/quote]","")},t.prototype.replayComment=function(e){var t=e.Id,o=e.User;this.txtComment.attr("placeholder","回复："+e.text).data("replayUser",o),this.commentTxt.val(t)},t.prototype.commentCode=function(){var e=this;e.commtCode.find("a").click(function(t){var o="[code="+$(this).data("code")+"]\n\n[/code]",n=e.txtComment.val();(o+n).length<=e.commentFontLimit?(e.txtComment.val(n+o),"comment_content"==e.txtComment[0].id&&$("#comment-content").val(n+o),e.commtCode.parents(".comment-code-box").fadeOut()):showToast({text:"您输入的字数已经超过限定了",bottom:"10%",zindex:9e3,speed:500,time:1500});var i=t||window.event;i.stopPropagation?i.stopPropagation():i.cancelBubble=!0})},t.prototype.comment=function(){function e(e){if(!getCookie("UserName"))return t.setStorage();window.localStorage.removeItem("AM_comment_data");var o=$(e).find(":submit"),n={commentId:t.commentTxt.val(),content:t.txtComment.val(),articleId:articleId};if("block"===w.formRewardBox.css("display")&&Object.assign(n,w.rewardInfo),n.replyId&&$.trim(n.content)&&(n.content="[reply]"+t.txtComment.data("replayUser")+"[/reply]"+n.content),$(e).find("#comment_content").length&&$("#commentQuote").length&&""!==$("#commentQuote").text()&&(n.content="[quote]"+$("#commentQuote").text().substr(0,50)+"[/quote]"+n.content),""===$.trim(n.content)||""===t.txtComment.val())showToast({text:"请填写评论内容",bottom:"10%",zindex:9e3,speed:500,time:1500});else{var i=blogUrl+"phoenix/web/v1/comment/submit";o.prop("disabled",!0).val("提交中"),$.ajax({url:i,type:"post",dataType:"json",data:n,xhrFields:{withCredentials:!0},success:function(e){if(200===e.code){if(t.isCommentFold)t.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),t.getFoldList(t.commentFold);else{t.commentForm.trigger("reset"),t.commentTxt.val(""),t.txtComment.data("replayUser","").attr("placeholder","想对作者说点什么"),t.cancelBtnToggle(!1),u=e.data,t.init(),t.removeStorage();var o=$("#commentQuote");o.length&&o.animate({height:0},300,function(){o.remove()}),t.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html("")}"block"===w.formRewardBox.css("display")&&w.formRewardBox.find(".btn-remove-reward").trigger("click"),showToast({text:"评论成功,审核后显示",bottom:"10%",zindex:9e3,speed:500,time:2e3})}else showToast({text:e.message,bottom:"10%",zindex:9e3,speed:500,time:2e3})},complete:function(){o.prop("disabled",!1).val("评论")}})}return!1}var t=this;t.commentForm.submit(function(t){t.preventDefault();var o=this;try{window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){e(o)}})}catch(n){e(o)}})},t.prototype.insertRightCommnetPrompt=function(){if(window.localStorage.getItem("AM_comment_Prompt_show"))return!1;window.localStorage.setItem("AM_comment_Prompt_show",!0);var e=$(".right_recommend_comment"),t=$(".right_recommend_comment_Prompt");t.length>0?t.fadeIn(300):e.prepend('<div class="right_recommend_comment_Prompt"><div class="text">新的评论在这里</div><div class="arrow"></div></div>'),setTimeout(function(){$(".right_recommend_comment_Prompt").fadeOut(500)},4e3)},t.prototype.setStorage=function(){return window.localStorage.setItem("AM_comment_data",this.txtComment.val()),window.localStorage.setItem("AM_comment_replyId",this.commentTxt.val()),window.localStorage.setItem("AM_comment_id",articleId),!!getCookie("UserName")||(window.csdn.loginBox.show(),!1)},t.prototype.removeStorage=function(){window.localStorage.removeItem("AM_comment_data"),window.localStorage.removeItem("AM_comment_replyId"),window.localStorage.removeItem("AM_comment_id")},t.prototype.bindLikedClick=a,t.prototype.bindRedRewardClick=i,t.prototype.cancelBtnToggle=function(e){e?this.cancelBtn.removeClass("d-none"):this.cancelBtn.addClass("d-none")},t.prototype.cancelBtnBindEvent=function(){var e=this;this.cancelBtn.on("click",function(){e.txtComment.attr("placeholder","想对作者说点什么").data("replayUser","").val(""),e.commentTxt.val(""),e.cancelBtnToggle(!1)})},t.prototype.commentIsBlack=function(e,t,o){var n=this,i="https://mp-action.csdn.net/interact/wrapper/pc/black/"+(o?"cancel":"save"),a=o?{username:e,usernameList:[t],bizNo:"blog"}:{username:e,blackUsername:t,bizNo:"blog"};$.ajax({url:i,type:"post",data:JSON.stringify(a),contentType:"application/json",dataType:"json",xhrFields:{withCredentials:!0},success:function(e){200===e.code?(setTimeout(function(){n.isCommentFold?(n.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),n.getFoldList(n.commentFold)):(n.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html(""),n.getPagination(n.commentUnFold.pageIndex))},100),showToast({text:o?"取消屏蔽成功！":"屏蔽用户成功！",bottom:"10%",zindex:9e3,speed:500,time:1500})):showToast({text:o?"取消屏蔽失败，请刷新页面重新操作！":"屏蔽用户失败，请刷新页面重新操作！",bottom:"10%",zindex:9e3,speed:500,time:1500})}})},t.prototype.commentIsTop=function(e,t){var o=this,n=blogUrl+"phoenix/web/v1/comment/top",i={commentId:e,articleId:articleId,action:t?0:1};$.ajax({url:n,type:"post",dataType:"json",data:i,xhrFields:{withCredentials:!0},success:function(e){200===e.code?(setTimeout(function(){o.isCommentFold?(o.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),o.getFoldList(o.commentFold)):(o.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html(""),o.getPagination(o.commentUnFold.pageIndex))},100),showToast({text:t?"取消置顶成功！":"置顶评论成功！",bottom:"10%",zindex:9e3,speed:500,time:1500})):showToast({text:t?"取消置顶失败，请重新操作！":"置顶评论失败，请重新操作！",bottom:"10%",zindex:9e3,speed:500,time:1500})}})},t.prototype.commentIsDelete=function(e){var t=this,o=$("#article_id").val(),n=blogUrl+"phoenix/web/v1/comment/delete",i={commentId:e,articleId:o};$.ajax({url:n,type:"post",dataType:"json",data:i,xhrFields:{withCredentials:!0},success:function(o){200===o.code?(setTimeout(function(){t.isCommentFold?(t.commentFold.pageIndex=1,$("#pcFlodCommentSideBox .comment-fold-content").html(""),t.getFoldList(t.commentFold)):(t.commentUnFold.pageIndex=1,$("#pcCommentSideBox .comment-list-box").html(""),t.getPagination(t.commentUnFold.pageIndex));var o=$("#pcCommentBox").find(".hot-comment-box");o.length>0&&o.data("commentid")===e&&(o.html(""),o.data("commentid",""),$("#pcCommentBox").hide())},100),showToast({text:"删除成功!",bottom:"10%",zindex:9e3,speed:500,time:1500})):showToast({text:"删除失败，请重新操作!",bottom:"10%",zindex:9e3,speed:500,time:1500})}})},$(document).on("keydown",".comment-content",function(e){var t=e||event;console.log(e),(t.ctrlKey&&13==t.which||13==t.which&&t.metaKey)&&("comment_content"==$(this).attr("id")?$("#commentform .btn-comment").trigger("click"):$("#commentformNew .btn-comment").trigger("click"))}),$(document).on("click",".second-look-more",function(e){$(this).hide(),$(this).siblings(".comment-line-box-hide").show()}),$(document).on("click",".comment-operate-item",function(e){if(getCookie("UserName")){if($(".comment-operate-isshow").fadeOut(),$(this).hasClass("comment-emoticon")){""==$(".comment-emoticon-img-box").html()&&$(".comment-emoticon-img-box").html(c()),$(this).find(".comment-emoticonNew").length&&$(this).find(".comment-operate-isshow").html($("#commentform .comment-emoticon-box").html());var t=$(this).find(".comment-emoticon-box");"block"===t.css("display")?t.fadeOut():($("#comment_content").one("focus",function(){t.fadeOut()}),t.fadeIn())}else if($(this).hasClass("comment-code")){var o=$(this).find(".comment-code-box");"block"===o.css("display")?o.fadeOut():($("#comment_content").one("focus",function(){o.fadeOut()}),o.fadeIn()),!$(this).hasClass("comment-codeNew")}else $(this).hasClass("comment-reward")&&w.showRewardForm();var n=e||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}else csdn.loginBox.show()}),$(document).on("click",".emoticon-monkey-img",function(e){var t=$(this).data("emoticon");if($(this).parents(".comment-emoticon-box").is(".comment-emoticonNew")){var o=$("#comment_contentNew").val()+t;o.length<=1e3?textareaPointLocation.insertAtCaret($("#comment_contentNew")[0],t):showToast({text:"您输入的字数已经超过限定了",bottom:"10%",zindex:9e3,speed:500,time:1500})}else{var o=$("#comment_content").val()+t;o.length<=1e3?textareaPointLocation.insertAtCaret($("#comment_content")[0],t):showToast({text:"您输入的字数已经超过限定了",bottom:"10%",zindex:9e3,speed:500,time:1500})}$(this).parents(".comment-emoticon-box").fadeOut();var n=e||window.event;n.stopPropagation?n.stopPropagation():n.cancelBubble=!0}),$(document).on("mouseenter",".comment-like",function(){$(this).hasClass("liked")?($(this).find(".clickedImg").css("display","none"),$(this).find(".comment-like-img-hover").css("display","block")):($(this).find(".unclickImg").css("display","none"),$(this).find(".comment-like-img-hover").css("display","block"))}),$(document).on("mouseleave",".comment-like",function(){$(this).hasClass("liked")?($(this).find(".clickedImg").css("display","block"),$(this).find(".comment-like-img-hover").css("display","none")):($(this).find(".unclickImg").css("display","block"),$(this).find(".comment-like-img-hover").css("display","none"))}),$(document).on("click",".unlogin-comment-box-new .unlogin-comment-bt",function(){window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6916"}),getCookie("UserName")||window.csdn.loginBox.show({spm:"1001.2101.3001.8611"})}),$(".blog-postTime").length>0){var m=$(".blog-postTime").data("time"),p="于 "+m+" 发布";$(".blog-postTime").html(p)}var u=r("#comments_");if(u&&!getCookie("UserName")){$("#pcCommentBox").show();var h=$(document).scrollTop(),f=$("#csdn-toolbar").height(),g=2*$("#pcCommentBox").height();h<52&&(f=2*f);var v=$("#pcCommentBox").offset().top-f-g;return setTimeout(function(){$("html,body").animate({scrollTop:v},200)},850),!1}var w=new e;window.csdn.comments?window.csdn.comments:{},window.csdn.Comments=t,window.csdn.comments=new window.csdn.Comments}),$(function(){if(1===articleType){var e=$("main .blog-content-box")[0],t=document.querySelector(".creativecommons span a");t=t?t.innerText:"CC 4.0 BY-SA";var o="\r\n————————————————\r\n版权声明：本文为CSDN博主「"+nickName+"」的原创文章，遵循"+t+"版权协议，转载请附上原文出处链接及本声明。\r\n原文链接："+curentUrl;csdn.copyright.init(e,o)}if(copyPopSwitch&&!getCookie("UserName")){var n=function(){window.csdn.loginBox.show({spm:"1001.2101.3001.9440"})};$("#content_views").unbind("keydown").bind("keydown",function(e){if(e.ctrlKey&&67==e.keyCode)return n(),!1}),$("#content_views").unbind("copy").bind("copy",function(e){return n(),!1})}}),$(function(){function e(e,o){if(!(o.length<2)){var n="";n+="<ol>";for(var i=0,a=0,s=0,c=0,r=0,l=0,d=0;d<o.length;d++){var m=parseInt(o[d].tagName.substr(1),10);if(i||(i=m),m>i?(n+='<li class="sub-box"><ol>',a++):m<i&&a>0&&(n+="</ol></li>",a--),m==p)for(;a>0;)n+="</ol>",a--;i=m;var u=o.eq(d).text();if(u=/^[\s]+$/.test(u)?u:u.replace(/^[\s]+/g,""),u=t(u),u.length>0)switch(m){case 1:++s,c=0,r=0,l=0;var h="";1===s&&(h="active"),n+="<li class="+h+'><a href="#t'+d+'">'+u+"</a></li>";break;case 2:++c,r=0,l=0,n+='<li><a href="#t'+d+'">'+u+"</a></li>";break;case 3:++r,l=0,n+='<li><a href="#t'+d+'">'+u+"</a></li>";break;case 4:++l,n+='<li><a href="#t'+d+'">'+u+"</a></li>"}}for(;a>0;)n+="</ol>",a--;return n}}function t(e){return e.replace(/[<>&"]/g,function(e){return{"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;"}[e]})}function o(e){function t(e){return $(e).each(function(e,n){n.children&&t(n.children),++o}),o}var o=0;return t(e)}function n(e,t,o){var n=$("div.groupfile"),i=n.find("div.toc-box"),a=n.find("div.opt-box");if(t<=2&&$("#liTocBox").remove(),t>2&&"0"==d&&$("#liTocBox").css("display","block"),t<=2&&"1"==d?($(".groupfile").remove(),$("#rightAsideConcision").remove()):isShowConcision?(isCookieConcision&&$(".recommend-right1").addClass("show-directory"),isHasDirectoryModel=!0):$("#rightAsideConcision").remove(),t<=2&&($("#blog_artical_directory").hide(),$(".left_menu .menu_con").hide()),!$(".first_li")[0]){i.html(o(e,m)),s(t,a,i);var c=!1;$("#liTocBox").hover(function(){$("#liTocBox .toc-container").finish().fadeIn(500),$(this).find("button.btn-toc").addClass("active"),c=!1},function(e){c=!0;var t=$(this);setTimeout(function(){c&&($("#liTocBox .toc-container").finish().fadeOut(500),t.find("button.btn-toc").removeClass("active"))},300)})}}function i(){var e=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop;e+=10;for(var t=0;t<f.length-1;t++){if(e<=f[0]){var o="#"+$(h[0]).find("a")[0].name,n=$("#groupfile").find('a[href="'+o+'"]')[0],i=$("#directory").find('a[href="'+o+'"]')[0],a=$("#groupfileConcision").find('a[href="'+o+'"]')[0];$(n).parent().addClass("active"),$(i).parent().addClass("active"),$(a).parent().addClass("active")}if(f[t]<=e&&e<=f[t+1]){var o="#"+$(h[t]).find("a")[0].name,n=$("#groupfile").find('a[href="'+o+'"]')[0],i=$("#directory").find('a[href="'+o+'"]')[0],a=$("#groupfileConcision").find('a[href="'+o+'"]')[0];$(n).parent().addClass("active"),$(i).parent().addClass("active"),$(a).parent().addClass("active"),$(n).length&&$("#groupfile .align-items-stretch").animate({scrollTop:$(n)[0].offsetTop+"px"},0),$(i).length&&$("#directory .align-items-stretch").animate({scrollTop:$(i)[0].offsetTop+"px"},0)}}}function a(){var e=$("#csdn-toolbar").height()+8;b+$(document).scrollTop()-($("#recommend-right").height()+$("#csdn-toolbar").height()+8)>=180?($("#recommend-right").css({position:"fixed",top:e}),$("#recommend-right-concision").css({position:"fixed",top:e})):($("#recommend-right").removeAttr("style"),$("#recommend-right-concision").removeAttr("style")),0==$(document).scrollTop()&&($("#recommend-right").removeAttr("style"),$("#recommend-right-concision").removeAttr("style"))}function s(e,t,o){if(e>9){var n=25*$("div.toc-box").find("li:not(.sub-box)").length,i=225,a=n-i,s=0;t.find("button.btn-opt").click(function(){$(this).hasClass("nomore")||($(this).hasClass("prev")?(s-=25,o.scrollTop(s),s<=0&&$(this).addClass("nomore")):(s+=25,s>=25*(e-17)&&(s=25*(e-17)),o.scrollTop(s),s>=a&&$(this).addClass("nomore")),$(this).siblings().removeClass("nomore"))})}else t.remove()}function c(){function e(t){$(".openvippay")[0]||window.csdn.clearReadMoreBtn(),o.off("click",e)}function t(){var e=window.location.href,t=document.title;setTimeout(function(){history.pushState(null,t,e)},0)}var o=$(".toc-container .toc-box ol li a");return 0!==o.length&&(o.click(e),void o.click(t))}function r(e){var t="<ol>";return $(e).each(function(e,o){t+=l(o,e)}),t+"</ol>"}function l(e,t){return e.children.length?"<li><a>"+e.title+'</a></li><li class="sub-box">'+r(e.children)+"</li>":"<li><a>"+e.title+"</a></li>"}for(var d=1,m=$("#article_content").find("h1,h2,h3,h4"),p=$("#article_content").find("h1").length>0?1:2,u=0;u<m.length;u++)m.eq(u).html('<a name="t'+u+'"></a>'+m.eq(u).html());$(".vip_article")[0]?($.ajax({type:"GET",url:blogUrl+"/phoenix/web/v1/get-article-catalog?articleId="+articleId,dataType:"json",xhrFields:{withCredentials:!0},success:function(e){200==e.code&&(n(e.data,o(e.data),r),c(),$("#groupfile .toc-box ol li a").each(function(e,t){t.href="#t"+e}),$("#directory .toc-box ol li a").each(function(e,t){t.href="#t"+e}),""===String($(".groupfile .toc-box").text()).replace(/(^\s*)|(\s*$)/g,"")?($("#asidedirectory").remove(),$("#groupfile").remove(),$("#rightAsideConcision").remove(),isHasDirectoryModel=!1):$("#groupfile").show())}}),$("#downloadEduApp").click(function(){getCookie("UserName")?window.open("https://vip.csdn.net/studyvip?utm_source=zhuanlan"):window.csdn.loginBox.show()})):(n("",m.length,e),c(),""===String($(".groupfile .toc-box").text()).replace(/(^\s*)|(\s*$)/g,"")?($("#asidedirectory").remove(),$("#groupfile").remove(),$("#rightAsideConcision").remove(),isHasDirectoryModel=!1):$("#groupfile").show()),$(document).on("toolbarHeightChange",function(e){var t=$("#csdn-toolbar").height()+8;$("#recommend-right").css("position")&&$("#recommend-right").css({top:t})});var h=$("#content_views").find("h1,h2,h3,h4"),f=[],g=$("#content_views").offset().top,v=$("#content_views").height(),w=$("#csdn-toolbar").height();$(h).each(function(e,t){f.push($(t)[0].offsetTop+g-2*w)}),f.push(v+g),$(window).resize(function(){f=[],g=$("#content_views").offset().top,v=$("#content_views").height(),w=$("#csdn-toolbar").height(),$(h).each(function(e,t){f.push($(t)[0].offsetTop+g-2*w)}),f.push(v+g)}),$(window).scroll(function(){$(".groupfile .toc-box li").removeClass("active"),i()}),i(),$(document).on("click",".groupfile .toc-box li a",function(e){$(".groupfile .toc-box li").removeClass("active"),$(this).parent().addClass("active")}),$(window).resize(function(){});var b=document.body.clientHeight||document.documentElement.clientHeight;if($("#groupfile").css("max-height",b/2+"px"),$(".groupfile-div").css("max-height",b/2+"px"),$(".groupfile-div1").css("max-height",b-48+"px"),$(document).scroll(function(){a()}),a(),$(document).on("click",".csdn-side-toolbar .option-box",function(e){isShowConcision&&setTimeout(function(){a()},100)}),$(".hide-article-box").length&&"none"!==$(".hide-article-box").css("display")){for(var x=$(".hide-article-box").offset().top,y=0,k=0;k<f.length-1;k++)f[k]<=x&&x<=f[k+1]&&(y=k+1);var C=h.slice(y,h.length);$(".vip_article")[0]&&$(document).click(".groupfile .toc-box ol a",function(e){if($(e.target)[0].hash&&$(e.target)[0].hash.split("#t")&&parseInt($(e.target)[0].hash.split("#t")[1])>y){var t=$("#getVipUrl").attr("href");t.indexOf("vip")>-1?window.open(t+"?utm_source=brv&sale_source=BgsN7mSaYF"):window.open(t)}}),$(".btn-readmore")[0]&&C.each(function(e,t){$(".groupfile .toc-box ol").find('a[href="#'+$(C[e]).find("a")[0].name+'"]').click(function(){$(".btn-readmore").trigger("click"),$(".btn-readmore").hasClass("no-login")?window.open("https://passport.csdn.net/account/login"):$("#btnAttent").trigger("click")})})}$(document).ready(function(){var e=$(".htmledit_views #main-toc").prevAll("h1,h2,h3,h4"),t=e.length,o=$('.htmledit_views p[id*="-toc"]').slice(1).filter(function(e,t){return $(t).find("a").length||$(t).css({margin:"0"}),$(t).find("a").length});o.each(function(e,o){$(o).attr("id","");var n=$(o).css("margin-left").replace("px","")/40,i=0==n?0:48*n;if($(o).css({"padding-left":"24px",margin:"0","margin-left":i+"px","margin-bottom":"2px"}),t>0)$(o).find("a").attr("href","#t"+t),t++;else{var a=$(o).find("a").attr("href").replace("#","");$('[id="'+a+'"]').is("h1,h2,h3,h4")&&$(o).find("a").attr("href","#t"+e)}});var n=$(".markdown_views .toc a");n.length&&n.each(function(e,t){var o=$(t).attr("href");o&&$(t).attr("href",o.replace(/%/g,"_"))});var i=$(".markdown_views").find("h1 a,h2 a,h3 a,h4 a");i.length&&i.each(function(e,t){var o=$(t).attr("id");o&&$(t).attr("id",o.replace(/%/g,"_"))}),$(document).on("click","#content_views a[href*=#],div.toc-box a[href*=#],li.tool-item-comment a[href*=#]",function(){var e=$("#csdn-toolbar").height(),t=$(document).scrollTop();if(t<52&&(e=2*e),location.pathname.replace(/^\//,"")==this.pathname.replace(/^\//,"")&&location.hostname==this.hostname){var o=$('[id="'+this.hash.slice(1)+'"]');if(o=o.length&&o||$('[name="'+this.hash.slice(1)+'"]'),o.length){var n=o.offset().top;return $("html,body").animate({scrollTop:n-e},500),!1}}}),setTimeout(function(){if(window.location.href.indexOf("#comments")>-1){var e=$("#csdn-toolbar").height();$("html,body").animate({scrollTop:$("#commentBox").offset().top-e-48},500)}},1e3)})}),$(function(){function e(e){var t=$(".article-resource-info-box");$.ajax({type:"GET",url:"https://download-console-api.csdn.net/plugin/blog/source/get",dataType:"json",data:{sid:e},xhrFields:{withCredentials:!0},success:function(e){if(200==e.code&&e.data&&e.data.hasOwnProperty("sid")){var o=e.data,n='\n            <a href="'+o.url+'" target="_blank" class="img-box">\n              <img src="'+o.fileTypeIcon+'" alt="'+o.title+'" class="file-icon">\n            </a>\n            <div class="info-box">\n              <a data-report-view=\'{"spm":"3001.9500"}\' data-report-click=\'{"spm":"3001.9500"}\' data-report-query=\'spm=3001.9500\' href="'+o.url+'" target="_blank">\n                <p class="title" title="'+o.title+'">'+o.title+'</p>\n                <p class="desc" title="'+o.description+'">'+o.description+'</p>\n              </a>\n            </div>\n            <div class="opt-box">\n              <a data-report-view=\'{"spm":"3001.9499"}\' data-report-click=\'{"spm":"3001.9499"}\' data-report-query=\'spm=3001.9499\' href="'+o.url+'" class="btn-resource-link" target="_blank">立即下载</a>\n            </div>\n          ';t.append(n),t.css({display:"flex"}),window.csdn.report.viewCheck()}else t.remove()},error:function(){t.remove()}})}resourceId&&e(resourceId)}),$(function(){var e=$('#article_content a[href^="#"]');e.each(function(e){$(this).attr("target","_self")})}),function(){function e(e){var t="";i&&(t="active");var o='<div class="column-group '+t+'" data-id="'+e.id+'" data-url="'+e.nsurl+'" data-report-click=\'{"spm":"1001.2101.3001.8542"}\' data-report-view=\'{"spm":"1001.2101.3001.8542"}\'>                    <div class="column-group-item">                      <div class="item-l">                        <a class="item-target" href="javascript:;" title="'+e.title+'">                          <img class="item-target" src="'+e.logo+'" alt="">                          <span class="title item-target">                              <span>                              <span class="tit">'+e.title+'</span>                              <span class="dec">'+e.desc+'</span>                              </span>                          </span>                        </a>                      </div>                      <div class="item-r">                        <a class="item-target article-column-bt join-huawei-community">加入社区</a>                      </div>                    </div>                  </div>';
n.innerHTML=o}function t(){$.ajax({url:"https://devpress-api.csdn.net/api/internal/blog/nsInfo/blog/"+articleId,type:"GET",xhrFields:{withCredentials:!0},success:function(t){200==t.code&&t.data?e(t.data):n.remove()}})}function o(e,t){$.ajax({type:"POST",url:"https://devpress-api.csdn.net/v1/user/follow",contentType:"application/json; charset=utf-8",data:JSON.stringify({followTarget:e,type:1}),xhrFields:{withCredentials:!0},success:function(e){window.open(t)},error:function(e){window.open(t)}})}var n=document.getElementById("blogHuaweiyunAdvert"),i=document.getElementById("blogColumnPayAdvert");n&&(t(),$(document).on("click","#blogHuaweiyunAdvert .column-group",function(){if(getCookie("UserName")){var e=$(this).data("id"),t=$(this).data("url");o(e,t)}else window.csdn.loginBox.show()}))}(),$(".comment-box textarea.comment-content").on("select",function(){textareaPointLocation.setCaret(this)}).on("click",function(){textareaPointLocation.setCaret(this)}).on("keyup",function(){textareaPointLocation.setCaret(this)});var textareaPointLocation={setCaret:function(e){e.createTextRange&&(e.caretPos=document.selection.createRange().duplicate())},insertAtCaret:function(e,t){if(document.all)if(e.createTextRange&&e.caretPos){var o=e.caretPos;o.text=" "==o.text.charAt(o.text.length-1)?t+" ":t}else e.value=t;else if(e.setSelectionRange){var n=e.selectionStart,i=e.selectionEnd,a=e.value.substring(0,n),s=e.value.substring(i);e.value=a+t+s}else showToast({text:"This version of Mozilla based browser does not support setSelectionRange",bottom:"10%",zindex:9e3,speed:500,time:1500})}};$(function(){var e=$("#asideNewNps"),t=$("#asideNewNps .newnps-item"),o=$("#asideNewNps .newnps-form-box");getCookie("blog_details_nps")?e.remove():(e.fadeIn(),t.on("click",function(){$(this).siblings().removeClass("active"),$(this).siblings().find("img.default").show(),$(this).siblings().find("img.active").hide(),$(this).addClass("active"),$(this).find("img.default").hide(),$(this).find("img.active").show(),o.slideDown()}),o.on("click",".newnps-btn",function(t){if(getCookie("UserName")){o.removeClass("active"),showToast({text:"感谢您的意见，我们尽快改进",bottom:"10%",zindex:9002,speed:500,time:1500});var n={spm:"1001.2101.3001.6242",extend1:"博客详情页满意度调查",extra:JSON.stringify({radio:e.find(".newnps-item.active").data("type"),input:o.find(".newnps-input").val()})};setCookieBaseHour("blog_details_nps",(new Date).getTime(),720),window.csdn&&window.csdn.report&&window.csdn.report.reportClick(n),e.slideUp(600,function(){e.remove()})}else window.csdn.loginBox.show()}),o.on("focus",".newnps-input",function(){o.addClass("active")}))}),$(function(){function e(){function e(){$("#publicPrompt").remove(),$(document).off("click",".publicPrompt-close",e),$(document).off("click",".publicPrompt-mask",e)}var t={success:'<svg class="icon success" aria-hidden="true"><use xlink:href="#csdnc-check"></use></svg>'};this.data={},this.data.status="success",this.data.titleStr="收藏成功",this.data.textStr='已收藏至 <a href="https://i.csdn.net/#/uc/favorite-list" target="_blank">个人中心</a>',this.data.imgUrl="",this.init=function(e){return $.extend(this.data,e),this.addStyle(),this.bindDom(),this},this.show=function(e){$.extend(this.data,e),this.insetStructure(this.data)},this.bindDom=function(){$(document).on("click",".publicPrompt-close",e),$(document).on("click",".publicPrompt-mask",e)},this.insetStructure=function(e){var o='<div id="publicPrompt"><div class="publicPrompt-mask"></div><div class="publicPrompt-content"><div class="publicPrompt-title-box"><h3 class="publicPrompt-title">'+t[e.status]+e.titleStr+'</h3><span class="publicPrompt-close"><svg class="icon" aria-hidden="true"><use xlink:href="#csdnc-times"></use></svg></span><div class="publicPrompt-text">'+e.textStr+'</div><img class="publicPrompt-qr" src="'+e.imgUrl+'" alt=""><div class="publicPrompt-footer-text">下载APP随时查看</div></div></div></div>';$("body").append(o)},this.addStyle=function(){var e=document.createElement("style");e.type="text/css",e.innerHTML=["#publicPrompt{","position: fixed;z-index: 99999;top: 0;left: 0;width: 100%;height: 100%;","}","#publicPrompt .publicPrompt-mask{","position: absolute;top: 0;left: 0;width: 100%;height: 100%;","background-color: rgba(0,0,0,0.5);","}","#publicPrompt .publicPrompt-content{","position: absolute;z-index: 2;top: 30%;left: 50%;margin-left: -175px;","background-color: #fff;width: 350px;padding: 16px;","}","#publicPrompt .publicPrompt-content .publicPrompt-title{","font-size: 16px;color: #3D3D3D;","}","#publicPrompt .publicPrompt-title svg{margin-right:4px;}","#publicPrompt .publicPrompt-title .success{fill:#89cb62;width: 20px;height: 20px;vertical-align: sub;}","#publicPrompt .publicPrompt-close{","position: absolute;top: 16px;right: 16px;cursor: pointer;","}","#publicPrompt .publicPrompt-text{","text-align: center;margin-top: 16px;margin-bottom: 16px;font-size: 14px;","}","#publicPrompt .publicPrompt-text a{","color: #3399EA;text-decoration: underline;","}","#publicPrompt .publicPrompt-qr{","display: block;width: 116px;height: 116px;padding: 8px;margin: 0 auto;border: 1px solid #EBEBEB;","}",".publicPrompt-footer-text{","font-size: 14px;color: #4D4D4D;margin-top:8px;text-align: center;","}"].join(""),document.getElementsByTagName("head")[0].appendChild(e)}}window.csdn?window.csdn:{},window.csdn.publicPrompt=e}),$(function(){var e=$("#recommendNps"),t=$("#recommendNps .newnps-item"),o=$("#recommendNps .newnps-form-box");getCookie("blog_details_recommend_nps")?e.remove():(e.fadeIn(),t.on("click",function(){$(this).siblings().removeClass("active"),$(this).siblings().find("img.default").show(),$(this).siblings().find("img.active").hide(),$(this).addClass("active"),$(this).find("img.default").hide(),$(this).find("img.active").show(),o.slideDown()}),o.on("click",".newnps-btn",function(t){if(getCookie("UserName")){o.removeClass("active"),showToast({text:"感谢您的意见，我们尽快改进",bottom:"10%",zindex:9002,speed:500,time:1500});var n={spm:"1001.2101.3001.7836",extend1:"博客详情页相关推荐满意度调查",extra:JSON.stringify({radio:e.find(".newnps-item.active").data("type"),input:o.find(".newnps-input").val()})};setCookieBaseHour("blog_details_recommend_nps",(new Date).getTime(),720),window.csdn&&window.csdn.report&&window.csdn.report.reportClick(n),e.slideUp(600,function(){e.remove()})}else window.csdn.loginBox.show()}),o.on("focus",".newnps-input",function(){o.addClass("active")}))}),$(function(){function e(e,t){l.hide(),m=e,m?c.text("您举报的评论来自文章："):c.text("举报内容："),r.css({"z-index":9999}).show(),s.css({"z-index":1e4}).show()}function t(){s.find("#frmReport").trigger("reset"),r.css({"z-index":150}).hide(),s.hide(),$(".ipt-textarea").val(""),$(".box-botoom ul li").removeClass("box-active"),$(".box-content-bottom ul li").removeClass("box-active"),$(".box-content").eq(0).show().siblings().hide(),$("#cllcont").hide(),$(".content-input").val(""),flag=!1,i="",a=""}function o(e){showToast({text:e,bottom:"10%",zindex:9999,speed:500,time:1500})}var n=' <div class="report-box">  <div class="pos-boxer">      <div class="pos-content">          <div class="box-title">              <p>举报</p>              <img class="icon btn-close" src="'+blogStaticHost+'dist/pc/img/closeBlack.png">          </div>          <div class="box-header">              <div class="box-top"><span>选择你想要举报的内容（必选）</span></div>              <div class="box-botoom">                  <ul>                      <li data="1"  type="nei">内容涉黄</li>                      <li data="2" type="nei">政治相关</li>                      <li data="3" type="nei">内容抄袭</li>                      <li data="4" type="nei">涉嫌广告</li>                      <li data="5" type="nei">内容侵权</li>                      <li data="6" type="nei">侮辱谩骂</li>                      <li data="8" type="nei">样式问题</li>                      <li data="7" type="nei">其他</li>                  </ul>              </div>          </div>          <div>          <div class="box-content" >          </div>          <div class="box-content" >          </div>                    <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>原文链接（必填）</span>                      </div>                      <div class="box-content-bottom" style="padding-bottom: 16px;">                        <div class="box-input" style="height: 32px;line-height: 32px;">                        <input class="content-input" type="text" id="originalurl" name="originalurl" placeholder="请输入被侵权原文链接">                        </div>                      </div>          </div>          <div class="box-content" >          </div>          <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>请选择具体原因（必选）</span>                      </div>                  <div class="box-content-bottom">                          <ul>                              <li sub_type="1">包含不实信息</li>                              <li sub_type="2">涉及个人隐私</li>                          </ul>                      </div>          </div>          <div class="box-content" style="display:none;">                  <div class="box-content-top">                          <span>请选择具体原因（必选）</span>                      </div>                  <div class="box-content-bottom">                          <ul>                              <li sub_type="1">侮辱谩骂</li>                              <li sub_type="2">诽谤</li>                          </ul>                  </div>          </div>          <div class="box-content" style="display:none;">                <div class="box-content-top">                        <span>请选择具体原因（必选）</span>                    </div>                <div class="box-content-bottom">                        <ul>                            <li sub_type="1">搬家样式</li>                            <li sub_type="2">博文样式</li>                        </ul>                </div>          </div>          <div class="box-content" style="display:none;">          </div>          </div>            <div id="cllcont" style="display:none;">            <div class="box-content-top">              <span class="box-content-span">补充说明（选填）</span>            </div>                <div class="box-content-bottom">                  <div class="box-input" >                    <textarea class="ipt ipt-textarea" style="padding:0;"  name="description"  placeholder="请详细描述您的举报内容"></textarea>                  </div>                </div>            </div>            </div>      <div class="pos-footer">          <p class="btn-close">取消</p>          <p class="box-active">确定</p>      </div>  </div></div>';$("body").append(n);var i="",a="";$(".box-botoom ul li").on("click",function(){flag=!1,$(this).addClass("box-active").siblings().removeClass("box-active"),i=$(this).attr("data"),$(".content-input").val(""),$(".box-content").eq($(this).index()).show().siblings().hide(),"6"==$(this).attr("data")?$("#cllcont").hide():$("#cllcont").show(),"8"==$(this).attr("data")?$("#cllcont").hide():$("#cllcont").show(),$(".ipt-textarea")[0].value="",$(".box-content-bottom ul li").removeClass("box-active"),"7"==$(this).attr("data")?$(".box-content-span").html("补充说明（必填）"):$(".box-content-span").html("补充说明（选填）"),a=""}),$(".box-content-bottom ul li").on("click",function(){$(this).addClass("box-active").siblings().removeClass("box-active"),a=$(this).attr("sub_type"),flag=!0});var s=$("div.report-box"),c=s.find("#reptTit"),r=$("div.mask-dark"),l=$("#rptOriginalurl"),d=s.find('textarea[name="description"]'),m=!1,p=0;s.find(".btn-close").click(t),r.click(t);var u="";$(".box-active").on("click",function(){if(!i)return o("请选择你想要举报的内容！"),!1;switch(i){case"3":if(u=$("#originalurl").val(),""==u||"http://"==u)return o("举报抄袭必须提供原创文章地址！"),$("#originalurl").focus(),!1;break;case"7":if(u=d.val(),!u)return o("请填写补充说明！"),d.focus(),!1;break;case"5":if(!flag)return o("请选择具体原因"),!1;break;case"6":if(!flag)return o("请选择具体原因"),!1;break;case"8":if(!flag)return o("请选择具体原因"),!1}var e={articleId:articleId,commentId:m?p:"",subType:a,type:i,originalUrl:u,description:d.val()},n=m?"report-comment":"report-article";$.ajax({url:blogUrl+"/phoenix/web/v1/"+n,type:"post",dataType:"json",data:e,xhrFields:{withCredentials:!0},success:function(e){200==e.code?(t(),sessionStorage.removeItem("usename"),sessionStorage.removeItem("articleId"),o("感谢您的举报，我们会尽快审核！"),$(".ipt-textarea").val(""),$(".box-botoom ul li").removeClass("box-active"),$(".box-content-bottom ul li").removeClass("box-active"),$(".box-content").eq(0).show().siblings().hide(),$("#cllcont").hide(),$(".content-input").val(""),flag=!1,i="",a="",u="",t()):o("举报失败")}})}),$("div.comment-box").on("click","a.btn-report",function(){p=$(this).parents("li.comment-line-box").data("commentid")}),window.showReport=e}),$(function(){}),$(function(){function e(){$("code.has-numbering").each(function(e,t){$(t).css("position","absolute"),$(t).parent("pre.prettyprint").css({position:"relative",height:$(t).outerHeight()+20+"px"})})}window.csdn=window.csdn?window.csdn:{},window.csdn.setSafariCodestyle=e}),$(function(){0==$(".btn-readmore").length&&($("#content_views").find("pre").each(function(e,t){t=$(t),t.find("code").height()>340&&(t.addClass("set-code-hide"),t.append('<div class="hide-preCode-box"><span class="hide-preCode-bt" data-report-view=\'{"spm":"1001.2101.3001.7365"}\'><img class="look-more-preCode contentImg-no-view" src="'+blogStaticHost+"dist/pc/img/newCodeMore"+skinStatus+'.png" alt="" title=""></span></div>'))}),$(window).resize().scroll(),$(document).on("click",".hide-preCode-bt",function(){return $(this).parents("pre").removeClass("set-code-hide").addClass("set-code-show"),$(this).parents(".hide-preCode-box").hide().remove(),$(window).resize().scroll(),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.7365"}),!1}))}),$(function(){}),window.csdn.insertcallbackBlock=computePos,$(function(){function e(){var e=document.referrer;if(e&&e.indexOf("so.csdn.net")>-1){var o='<a class="option-box search" data-type="search">        <img src="'+blogStaticHost+'dist/pc/img/sideSearch.png" alt="" srcset="">        <span class="show-txt">搜索</span>      </a>';$(".csdn-side-toolbar").prepend(o);var n='<div class="side-search-box"><div class="side-search-content">          <span class="search-txt">'+r+'</span><span class="search-bt no" data-type="no">否</span><span class="search-bt yes" data-type="yes">是</span>        </div><div>';if($(".csdn-side-toolbar .search").append(n),!getCookie("referrer_search")){var i=0;timer=setInterval(function(){i++,3==i&&$(".side-search-box").fadeIn(1500),7==i&&($(".side-search-box").fadeOut(1500),clearInterval(timer))},1e3),t("referrer_search",(new Date).getTime(),2)}$(".csdn-side-toolbar .search").on("click",function(e){getCookie("referrer_search")||clearInterval(timer),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.5543",dest:articleDetailUrl,extend1:r}),$(".side-search-box").fadeIn(500),setTimeout(function(){$(".side-search-box").fadeOut(1500)},1e4)}),$(".side-search-box .search-bt").on("click",function(e){getCookie("referrer_search")||clearInterval(timer);var t="";t="yes"===$(e.target).data("type")?"yes":"no",window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&(window.csdn.report.reportClick({spm:"1001.2101.3001.5543",dest:articleDetailUrl,extend1:r,ab:t}),e.stopPropagation()),$(".side-search-box .side-search-content").html("感谢您的反馈!").css({width:"92px"}),setTimeout(function(){$(".csdn-side-toolbar .search").fadeOut(800,function(){$(this).remove()})},1e3)})}}function t(e,t,o){var n=new Date;n.setTime(n.getTime()+36e5*o),document.cookie=e+"="+escape(t)+";expires="+n.toGMTString()+";domain=.csdn.net;path=/"}function o(e,t){l&&e>.5*t&&$(".csdn-side-toolbar .side-question-box").length>0&&(getCookie("blog_ask_question")||($(".csdn-side-toolbar .side-question-box").fadeIn(),m&&(window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"1001.2101.3001.6041"}),m=!1)))}function n(){if(isShowConcision){var e='<a class="option-box sidecolumn sidecolumn-show" data-type="show" style="display:'+(isShowSideModel?"flex":"none")+'" data-report-view=\'{"spm":"1001.2101.3001.7788"}\' data-report-click=\'{"spm":"1001.2101.3001.7788"}\'>        <img src="'+blogStaticHost+'dist/pc/img/iconShowSide.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="'+blogStaticHost+'dist/pc/img/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">显示</br>侧栏</span>      </a>',t='<a  class="option-box sidecolumn sidecolumn-hide" data-type="hide" style="display:'+(isShowSideModel?"none":"flex")+'" data-report-view=\'{"spm":"1001.2101.3001.7789"}\' data-report-click=\'{"spm":"1001.2101.3001.7789"}\'>        <img src="'+blogStaticHost+'dist/pc/img/iconHideSide.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="'+blogStaticHost+'dist/pc/img/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">隐藏</br>侧栏</span>      </a>';$(".csdn-side-toolbar").prepend(e+t)}}function i(){if(isShowConcision&&isHasDirectoryModel){var e='<a class="option-box directory directory-show" data-type="show" style="display:'+(isShowDirectoryModel?"flex":"none")+'" data-report-view=\'{"spm":"1001.2101.3001.7790"}\' data-report-click=\'{"spm":"1001.2101.3001.7790"}\'>        <img src="'+blogStaticHost+'dist/pc/img/iconShowDirectory.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="'+blogStaticHost+'dist/pc/img/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">只看</br>目录</span>      </a>',t='<a  class="option-box directory directory-hide" data-type="hide" style="display:'+(isShowDirectoryModel?"none":"flex")+'" data-report-view=\'{"spm":"1001.2101.3001.7791"}\' data-report-click=\'{"spm":"1001.2101.3001.7791"}\'>        <img src="'+blogStaticHost+'dist/pc/img/iconHideDirectory.png" alt="" srcset=""><img style="display:block" class="icon-option-beta" src="'+blogStaticHost+'dist/pc/img/iconSideBeta.png" alt="" srcset="">        <span class="show-txt">隐藏</br>目录</span>      </a>';$(".csdn-side-toolbar").prepend(e+t)}}function a(){if(robotModule){var e=document.createElement("script");e.onload=function(){var e='\n          <div class="pos-box animate__animated animate__fadeOutRight">\n            <button id="btnChatDocMove">\n              <img src="'+blogStaticHost+'dist/pc/img/DragIcon.png" alt="" srcset="">\n            </button>\n            <div class="chatdoc-content-box"></div>\n            <button id="btnChatDocClose" data-report-view=\'{"spm":"1001.2101.3001.9607"}\' data-report-click=\'{"spm":"1001.2101.3001.9607"}\'>\n              <img src="'+blogStaticHost+'dist/pc/img/nerCloseWhite.png" alt="" srcset="">\n            </button>\n          </div>\n        ',t=document.createElement("div");t.className="chatdoc-robot-box",t.innerHTML=e,document.body.append(t);var o="Black"===skinStatus;window.chatxx=new ChatModel({el:".chatdoc-robot-box .chatdoc-content-box",model:"chatdoc",title:"AI学习助手",keycode:"blog",modelId:robotModule,darkMode:o,simpleMode:!0,getModelEnable:function(e){if(console.log("getModelEnable",e),e){var t=($("#articleContentId").text(),'<div class="btn-side-chatdoc-contentbox"><button class="btn-side-chatdoc" data-report-view=\'{"spm":"1001.2101.3001.9551"}\' data-report-click=\'{"spm":"1001.2101.3001.9551"}\'>                <img src="'+blogStaticHost+'dist/pc/img/iconSideChatDoc.png" alt="" srcset="">              </button><div class="side-chatdoc-desc-box">点击我，总结本文并提问<img id="side-chatdoc-desc-close" src="https://img-home.csdnimg.cn/images/20230815093114.png" alt="" srcset=""></div></div>');$(".csdn-side-toolbar").find(".sidetool-writeguide-box").length>0?$(".csdn-side-toolbar").find(".sidetool-writeguide-box").after(t):$(".csdn-side-toolbar").prepend(t),c(),$("#side-chatdoc-desc-close").on("click",function(){$(".side-chatdoc-desc-box").hide(),$("#side-chatdoc-desc-close").remove(),$(".btn-side-chatdoc-contentbox").hover(function(){$(".side-chatdoc-desc-box").show()},function(){$(".side-chatdoc-desc-box").hide()})})}else $(".chatdoc-robot-box").remove();getCookie("SidecHatdocDescBoxNum")?($("#side-chatdoc-desc-close").remove(),$(".btn-side-chatdoc-contentbox").hover(function(){$(".side-chatdoc-desc-box").show()},function(){$(".side-chatdoc-desc-box").hide()})):($(".side-chatdoc-desc-box").show(),setCookieBaseHour("SidecHatdocDescBoxNum","true",24))}})},e.src="https://cdn-static-devbit.csdn.net/ai100/chat/index.runtime.js",document.querySelector("head").append(e)}}function s(){u=setInterval(function(){if(window.csdn&&window.csdn.toolbarData&&window.csdn.toolbarData.topicData&&(clearInterval(u),window.csdn.toolbarData.topicData.length>0)){for(var e=blogStaticHost+"dist/pc/img/"+("Black"!==skinStatus?"btnGuideSide1":"btnGuideSide2")+".gif",t='href="https://mp.csdn.net/mp_blog/manage/creative"',o="3001.9732",n="",i=0;i<window.csdn.toolbarData.topicData.length;i++){var a=window.csdn.toolbarData.topicData[i];n+='\n            <div class="swiper-slide">\n              <a class="activity-item" data-report-click=\'{"spm":"3001.9733","dest":"'+a.url+'","extra": '+JSON.stringify({index:i,type:"title"})+'}\' data-report-query="spm=3001.9733" href="'+a.url+'" target="_blank">'+a.title+'</a>\n              <a class="btn-go-activity" data-report-click=\'{"spm":"3001.9733","dest":"https://mp.csdn.net/edit?activity_id='+a.id+'","extra": '+JSON.stringify({index:i,type:"button"})+'}\' data-report-query="spm=3001.9733" href="https://mp.csdn.net/edit?activity_id='+a.id+'" target="_blank">去创作</a>\n            </div>\n            '}var s='<div class="sidetool-writeguide-box">\n            <a class="btn-sidetool-writeguide" data-report-view=\'{"spm":"'+o+'"}\' data-report-query="spm='+o+'" '+t+' target="_blank" data-report-click=\'{"spm":"'+o+'","extra": '+JSON.stringify({type:"monkey"})+"}'>\n              <img src=\""+e+'" alt="创作活动">\n            </a>\n            <div class="tip-box">\n              <a class="to-activity-list" data-report-click=\'{"spm":"'+o+'","extra": '+JSON.stringify({type:"tip"})+"}' data-report-query=\"spm="+o+'" '+t+' target="_blank">点我去创作中心查看更多活动~</a>\n            </div>\n            <div class="activity-swiper-box-act">\n             <div class="activity-swiper-box">\n              <button class="btn-close">\n                <img src="'+blogStaticHost+'dist/pc/img/nerCloseWhite.png" />\n              </button>\n              <p class="title">创作话题</p>\n              <div class="swiper-box swiper">\n                <div class="swiper-wrapper">\n                  '+n+'\n                </div>\n                <div class="swiper-button-define-prev"></div>\n                <div class="swiper-button-define-next"></div>\n              </div>\n             </div>\n            </div>\n          </div>';$(".csdn-side-toolbar").prepend(s),window.csdn.report.viewCheck();var c=null,r=!1;$(".sidetool-writeguide-box .btn-close").click(function(){$(".activity-swiper-box-act").hide()}),$(".sidetool-writeguide-box").hover(function(){$(".activity-swiper-box-act").show(),r||(r=!0,window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:"3001.9733"})),null===c&&(c=new Swiper(".sidetool-writeguide-box .swiper-box",{navigation:{nextEl:".swiper-button-define-next",prevEl:".swiper-button-define-prev"},loop:!0,autoplay:!0}),c.el.onmouseover=function(){c.autoplay.stop()},c.el.onmouseout=function(){c.autoplay.start()})},function(){$(".activity-swiper-box-act").hide()}),setTimeout(function(){$(".sidetool-writeguide-box .tip-box").remove()},5e3)}window.csdn&&window.csdn.toolbarData&&window.csdn.toolbarData.remunerationData&&$("#remuneration").length>0&&($("#remuneration img").attr("src",window.csdn.toolbarData.remunerationData.mphome),$("#remuneration").attr("href",window.csdn.toolbarData.remunerationData.url),$("#remuneration").show(),window.csdn&&window.csdn.report&&window.csdn.report.reportView({spm:"1001.2101.3001.9809"}))},500)}function c(){var e=document.querySelector(".chatdoc-robot-box .pos-box");e.onclick=function(e){e.stopPropagation()};var t=function(){e.classList.remove("animate__fadeInRight"),setTimeout(function(){document.querySelector(".chatdoc-robot-box").style.display="none"},1100)};document.getElementById("btnChatDocClose").onclick=t,document.querySelector(".chatdoc-robot-box").onclick=t,document.querySelector(".csdn-side-toolbar button.btn-side-chatdoc").onclick=function(){getCookie("UserName")?(document.querySelector(".chatdoc-robot-box").style.display="block",e.classList.add("animate__fadeInRight")):window.csdn.loginBox.show()};var o=document.getElementById("btnChatDocMove");o.onmousedown=function(e){e.preventDefault(),e.stopPropagation(),h.mouseX=e.clientX,h.offsetX=e.target.offsetLeft,h.triggerFlag=!0},document.querySelector(".chatdoc-robot-box").onmousemove=function(t){var o=t.clientX-h.mouseX;if(h.triggerFlag){if(h.objX-o<=340)return;if(h.objX-o>=document.documentElement.clientWidth-20)return;h.objX-=o}h.mouseX=t.clientX,e.style.width=h.objX+"px"},document.querySelector(".chatdoc-robot-box").onmouseup=function(){h.triggerFlag=!1}}window.getSideToolbarTime=setInterval(function(){$(".csdn-side-toolbar").length>0&&(clearInterval(getSideToolbarTime),e(),n(),i(),a(),s())},1e3);var r="此内容解决你搜索的问题？";"control"===showSearchText&&(r="此内容是您要找的内容？"),"secondText"===showSearchText&&(r="此内容解决你搜索的问题？");var l=!0,d=!0,m=!0,p=document.documentElement.clientHeight;window.onscroll=function(){var e=document.documentElement.scrollTop||document.body.scrollTop;o(e,p)},$(document).on("click",".csdn-side-toolbar .fade-question-box",function(){$(".csdn-side-toolbar .side-question-box").fadeOut(),t("blog_ask_question",(new Date).getTime(),72),l=!1}),$(document).on("mouseenter",".csdn-side-toolbar .question",function(e){$(".csdn-side-toolbar .side-question-box").fadeIn(),$(e.target).hasClass("ask_question_img")&&d&&"block"==$(".csdn-side-toolbar .side-question-box").css("display")&&(window.csdn.report&&"function"==typeof window.csdn.report.reportView&&window.csdn.report.reportView({spm:"1001.2101.3001.6041"}),d=!1)}),$(document).on("mouseleave",".csdn-side-toolbar .question",function(e){d=!0}),$(document).on("click",".csdn-side-toolbar .question",function(e){window.open("https://ask.csdn.net/new?spm=1001.2101.3001.6040")}),$(document).on("click",".csdn-side-toolbar .side-question-box",function(e){e.stopPropagation()}),$(document).on("click",".csdn-side-toolbar .sidecolumn",function(e){var t=$(this).data("type");"show"==t?(isShowDirectoryModel||($("#rightAsideConcision").removeClass("show-directory"),$(".directory.directory-hide").hide(),$(".directory.directory-show").show()),isShowSideModel=!1,$(".main_father").removeClass("mainfather-concision"),$(".main_father .container").removeClass("container-concision"),$(".sidecolumn.sidecolumn-show").hide(),$(".sidecolumn.sidecolumn-hide").show(),setCookieBaseHour("blog_details_concision",(new Date).getTime(),24)):(isShowDirectoryModel||($("#rightAsideConcision").addClass("show-directory"),$(".directory.directory-hide").show(),$(".directory.directory-show").hide()),$(".main_father").addClass("mainfather-concision"),$(".main_father .container").addClass("container-concision"),isShowSideModel=!0,$(".sidecolumn.sidecolumn-hide").hide(),$(".sidecolumn.sidecolumn-show").show(),setCookieBaseHour("blog_details_concision",0,24)),window.csdn.fixedSidebar.stopListener=!$(".main_father").hasClass("mainfather-concision")}),$(document).on("click",".csdn-side-toolbar .directory",function(e){var t=$(this).data("type");"show"==t?(isShowSideModel||($("#rightAsideConcision").addClass("show-directory"),$(".main_father").addClass("mainfather-concision"),$(".main_father .container").addClass("container-concision"),$(".sidecolumn.sidecolumn-hide").hide(),$(".sidecolumn.sidecolumn-show").show()),isShowDirectoryModel=!1,$(".directory.directory-show").hide(),$(".directory.directory-hide").show(),$("#rightAsideConcision").addClass("show-directory")):(isShowDirectoryModel=!0,$(".directory.directory-hide").hide(),$(".directory.directory-show").show(),$("#rightAsideConcision").removeClass("show-directory")),window.csdn.fixedSidebar.stopListener=!$(".main_father").hasClass("mainfather-concision"),setCookieBaseHour("blog_details_concision",0,24)});var u,h={objX:490,mouseX:0,offsetX:0,triggerFlag:!1}});var getRecommendListUrlArr=null;$("#recommend-item-box-tow").children().length<=0&&$("#recommend-item-box-tow").remove(),baiduKey?csdn.afterCasInit=function(e,t){if(!e)return!1;var o="";4==articleSource&&$("#recommendDown .recommend_down").length<=0?csdn.baiduSearch(e,function(t){if(needInsertBaidu&&isRecommendModule)if(t&&t.length)showDownRecommend(t[0]);else{var n=$(".recommend-box .type_download")[0];if(n&&$(n).length){var i=$(n).find("a")[0],a={linkUrl:$(n).data("url"),title:$(i).text()};showDownRecommend(a)}}csdn.baiduSearch(e,function(e){needInsertBaidu&&isRecommendModule?(showResult(e,o),getQueryIdx(),reportTop10()):(getQueryIdx(),reportTop10())})},"download.csdn.net"):csdn.baiduSearch(e,function(e){needInsertBaidu&&isRecommendModule?(showResult(e,o),getQueryIdx(),reportTop10()):(getQueryIdx(),reportTop10())})}:(getQueryIdx(),reportTop10());var articleTitleContent=$("#articleContentId").text().replace(/[`~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘'，。、<em><\/em>&quot;]/g,"").replace(/\s/g,"").toLowerCase();$(function(){function e(){var e=$("#toolBarBox").offset().top,t=$("#toolBarBox").offset().left,o=$(".left-toolbox").height(),n=$("#toolBarBox").width(),i=window.innerHeight,a=document.documentElement.scrollTop||window.pageYOffset||document.body.scrollTop;if(e+o-i-a>0){if($("#toolBarBox .left-toolbox").css({position:"fixed","z-index":"999",left:t+"px",bottom:"0",width:n+"px"}),$("#toolBarBox").addClass("more-toolbox-active"),$("#toolBarBox .write-guide-buttom-box").length>0&&S){var s=$("#toolBarBox .write-guide-buttom-box").width(),c=t+parseInt((n-s)/2);$("#toolBarBox .write-guide-buttom-box").removeAttr("style").addClass("fixed").css({position:"fixed",display:"flex",left:c+"px"})}}else $("#toolBarBox .left-toolbox").css({position:"relative","z-index":"999",left:"0px",bottom:"0",width:n+"px"}),$("#toolBarBox .write-guide-buttom-box").length>0&&S&&$("#toolBarBox .write-guide-buttom-box").removeClass("fixed").css({display:"flex",position:"relative","z-index":"1000",left:"0px","margin-left":"auto",bottom:"-16px","margin-top":"-48px"}),$("#toolBarBox").removeClass("more-toolbox-active")}function t(e,t){if(E.skinBoxshadow.html(""),e){var o='<div class="reward-success reward-tip">    <svg t="1513153231313" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4182" id="mx_n_1513153231314" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m244.8 392L467.2 691.2c-8 9.6-24 12.8-36.8 12.8-12.8 0-27.2-3.2-36.8-12.8L267.2 560c-16-16-16-43.2 0-59.2s41.6-16 57.6 0l105.6 110.4 267.2-278.4c16-16 41.6-16 57.6 0s16 43.2 1.6 59.2z" p-id="4183" fill=""></path></svg>    <span>'+t+"</span>    </div>";E.skinBoxshadow.append(o)}else{var n='<div class="reward-error reward-tip">    <svg t="1513590447537" class="icon" style="" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9504" xmlns:xlink="http://www.w3.org/1999/xlink" width="16" height="16"><defs><style type="text/css"></style></defs><path d="M512 0C228.8 0 0 228.8 0 512s228.8 512 512 512 512-228.8 512-512S795.2 0 512 0z m0 832c-35.2 0-64-28.8-64-64s28.8-64 64-64 64 28.8 64 64-28.8 64-64 64z m64-320c0 35.2-28.8 64-64 64s-64-28.8-64-64V256c0-35.2 28.8-64 64-64s64 28.8 64 64v256z" p-id="9505" fill=""></path></svg>    <span>'+t+"</span>    </div>";E.skinBoxshadow.append(n)}E.skinBoxshadow.fadeIn(200),setTimeout(function(){
E.skinBoxshadow.fadeOut(200),E.skinBoxshadow.html("")},1500)}function o(e,t){t?e.addClass("active"):e.removeClass("active")}function n(e){getCookie("UserName")?$.ajax({url:blogUrl+"/phoenix/web/v1/article/like",type:"post",dataType:"json",data:{articleId:articleId},xhrFields:{withCredentials:!0},success:function(n){if(200==n.code){if(n.data.status)$("#is-like-img").hide(),$("#is-like-img-new").hide(),$("#is-like-imgactive").show(),$("#is-like-imgactive-new").show(),isLikeStatus=!0,isUnLikeStatus&&e&&i(!1),o($("#spanCount"),!0),$("#spanCount")[0].style="color:#fc5531 !important;",window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1582594662_001",spm:"1001.2101.3001.4241",dest:"",extend1:'{"praise":1}'});else{$("#is-like-imgactive").hide(),$("#is-like-imgactive-new").hide(),$("#is-like-img").show(),$("#is-like-img-new").show(),isLikeStatus=!1,e&&i(!1),o($("#spanCount"),!1);var a="color:#999999 !important;";"Black"===skinStatus&&(a="color:#ccccd8 !important;"),$("#spanCount")[0].style=a,window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1582594662_001",spm:"1001.2101.3001.4241",dest:"",extend1:'{"praise":0}'})}$("#spanCount").text(n.data.like_num>0?n.data.like_num:""),$("#blog-digg-num").text(n.data.like_num>0?"点赞数"+actionNumberFormat(n.data.like_num):"点赞数0")}else t(!1,n.message);y()}}):window.csdn.loginBox.show({spm:"1001.2101.3001.8604"})}function i(e){getCookie("UserName")?$.ajax({url:blogUrl+"/phoenix/web/v1/article/bury",type:"post",dataType:"json",data:{articleId:articleId},xhrFields:{withCredentials:!0},success:function(o){200==o.code?o.data.status?($("#is-unlike-img").hide(),$("#is-unlike-imgactive").show(),isUnLikeStatus=!0,isLikeStatus&&e&&n(!1),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6669",extend1:'{"praise":1}'})):($("#is-unlike-imgactive").hide(),$("#is-unlike-img").show(),isUnLikeStatus=!1,e&&n(!1),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.6669",extend1:'{"praise":0}'})):t(!1,o.message)}}):window.csdn.loginBox.show({spm:"1001.2101.3001.8605"})}function a(){window.csdn.loginBox.key({biz:"blog",subBiz:"other_service",cb:function(){$.ajax({url:blogUrl+"/phoenix/web/v1/collect",dataType:"json",type:"POST",xhrFields:{withCredentials:!0},data:{articleId:articleId},success:function(e){200===e.code&&s(e.data.status)},error:function(){}})}})}function s(e,t){if(e){if($("#is-collection-imgactive").show(),$("#is-collection-img").hide(),o($("#get-collection"),!0),$("#blog_detail_zk_collection .un-collect-status").hide(),$("#blog_detail_zk_collection .collect-status").show(),A){if(A=!1,t)return;$(".get-collection").text()?$(".get-collection").text(actionNumberFormat(1*$("#get-collection").text()+1)):$(".get-collection").text("1")}}else{if($("#is-collection-imgactive").hide(),$("#is-collection-img").show(),o($("#get-collection"),!1),"White"==skinStatus?$(".get-collection").css({color:"#999aaa"}):$(".get-collection").css({color:"#999999"}),$("#blog_detail_zk_collection .un-collect-status").show(),$("#blog_detail_zk_collection .collect-status").hide(),A=!0,t)return;$(".get-collection").text()?1*$("#get-collection").text()-1<=0?$(".get-collection").text(""):$(".get-collection").text(actionNumberFormat(1*$("#get-collection").text()-1)):$(".get-collection").text("")}y()}function c(){getCookie("UserName")&&$.ajax({url:blogUrl+"phoenix/web/v1/isCollect",type:"GET",xhrFields:{withCredentials:!0},data:{articleId:articleId},success:function(e){200===e.code&&s(e.data.status,!0),y()}})}function r(e,t,o){o.length&&$.ajax({type:"GET",url:blogUrl+e,dataType:"json",xhrFields:{withCredentials:!0},data:{articleId:articleId},success:function(e){200==e.code&&t(e.data,o)},error:function(e){}})}function l(e,t){var o="";if(e.list&&e.list.length){if(o='<div class="reward-left">',$.each(e.list,function(e,t){e<10&&(o+='<a class="reward-href" target="_blank" href="'+blogUrl+t.username+'"><img src="'+t.avatarUrl+'" alt=""></a>')}),o+="</div>",o+='<div class="reward-right">'+(e.total>10?"等":"")+'<span class="count">'+e.total+"</span>人已打赏</div>",t.html(o),E.rewardContent.show(),E.initHeighgt=50,m())var n=422;else var n=480;$(".tool-item-reward .count").html(e.total),E.rewardNew.css({"max-height":n+E.initHeighgt+"px"})}else E.rewardContent.hide(),E.initHeighgt=0}function d(e,t){Number(e)-Number(t)<0?E.sureBoxBlance.addClass("active"):E.sureBoxBlance.removeClass("active")}function m(){var e=0;return E.payType.each(function(t,o){if($(o).hasClass("active"))return void(e=t)}),"blance"===E.payType.eq(e).data("type")}function p(e){E.sureBoxBlance.find(".tip").removeAttr("style",""),e.value||(e.value=2),E.moneyNum=e.value,d(E.blance,E.moneyNum),E.codeNum.html("¥"+E.moneyNum),0<Number(E.moneyNum)<=500?m()||u():E.customizeTip.fadeIn(1e3,function(){E.customizeTip.fadeOut(1e3)})}function u(){if(initRewardObject&&""!=initRewardObject.sign){var e={product_id:72029,goods_id:77957,num:Number(E.moneyNum),flag:39,request_type:4,is_use_balance:0,ext:initRewardObject,stringExt:JSON.stringify(initRewardObject),success_function:w,error_function:f,timeout_function:g,get_pay_success_callback:b,payment_function:h};E.codeImgBox.html(E.payrun),cart.qrPay(e)}else showToast({text:"打赏信息失效，请刷新页面重试",bottom:"10%",zindex:9002,speed:500,time:1500})}function h(){imgsrc="https://csdnimg.cn/release/download/images/pay_error.png",imgtext="已扫码<br>请在手机端操作",E.codeImgBox.html('<div class="renovate"><img src="'+imgsrc+'"><span>'+imgtext+"</span></div>")}function f(e){showToast({text:e.errorMessage,bottom:"10%",zindex:9002,speed:500,time:1500})}function g(){E.codeImgBox.html(E.repeatAgain)}function v(e,t){var o=qrcode(6,"M");o.addData(e),o.make(),t.html(o.createImgTag(3,3))}function w(e,t){v(t.pay_url,E.codeImgBox)}function b(){E.rewardNew.fadeOut(),E.skinBoxshadow.html("");var e='<img style="position:fixed;margin:auto;left:0px;top:0px;right:0px;bottom:0px" src="'+blogStaticHost+'dist/pc/img/newRewardSuccess.gif" alt="打赏" title="打赏">';E.skinBoxshadow.append(e),setTimeout(function(){r(E.ajaxRewardUrl,l,E.rewardContent),E.skinBoxshadow.fadeOut(200),E.skinBoxshadow.html("")},4e3)}function x(e){if(getCookie("UserName")){var t=$(".tool-attend");t.is(".tool-unbt-attend")?(t.removeClass("tool-unbt-attend").addClass("tool-bt-attend").text("关注"),M&&(window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1592215036_002",spm:"1001.2101.3001.4132",extend1:"已关注"}),M=!1)):(t.removeClass("tool-bt-attend").addClass("tool-unbt-attend").text("已关注"),M&&(window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1592215036_002",spm:"1001.2101.3001.4132",extend1:"关注"}),M=!1))}else window.csdn.loginBox.show({spm:"1001.2101.3001.8606"});y()}function y(){if("block"===$("#is-like-imgactive").css("display")&&"block"===$("#is-collection-imgactive").css("display")&&$(".tool-unbt-attend").length>0){if($("#health-companies").length){$("#health-companies").removeClass("active");var e=$("#health-companies").attr("src");e.indexOf("Default")>-1?$("#health-companies").attr("src",e):$("#health-companies").attr("src",e.replace("Active","Default"))}}else if($("#health-companies").length){$("#health-companies").addClass("active");var e=$("#health-companies").attr("src");e.indexOf("Default")>-1?$("#health-companies").attr("src",e.replace("Default","Active")):$("#health-companies").attr("src",e)}}$(document).on("mouseover",".article-bar-top .time",function(){if($(".article-info-box .up-time").length){var e=$(this).offset().left,t=($(this).width(),$(this).parents().find(".article-bar-top").offset().left);$(".article-info-box .up-time").css({left:e-t-20}).show()}}),$(document).on("mouseout",".article-bar-top .time",function(){$(".article-info-box .up-time").length&&$(".article-info-box .up-time").hide()});var k=!0;$(document).on("click",".article-info-box .slide-toggle",function(){k?$(this).text("收起"):$(this).text("版权"),$(this).parents(".article-info-box").find(".slide-content-box").slideToggle(),k=!k});var C='{"mod":"1585297308_001","spm":"1001.2101.3001.6548","dest":"'+articleDetailUrl+'","extend1":"pc","ab":"new"}',S=!1;if(canRead){var _=getCookie("write_guide_show");_=_?parseInt(_):0;var B=$("<div data-report-view="+C+"><div>");if($(".hide-article-box").length?$(document).on("click",".hide-article-box .btn-readmore",function(){$("#content_views").after(B)}):$("#content_views").after(B),_<3){_+=1;var T=new Date;T.setHours(23,59,59,0);var I=T.toGMTString();document.cookie="write_guide_show="+_+";expires="+I+";domain=.csdn.net;path=/",$(".write-guide-buttom-box .btn-close").click(function(){$(".write-guide-buttom-box").remove()});var N=B[0],F=new MutationObserver(function(e,t){var o=!0,n=!1,i=void 0;try{for(var a,s=e[Symbol.iterator]();!(o=(a=s.next()).done);o=!0){var c=a.value;"attributes"===c.type&&"data-report-view"===c.attributeName&&(N.getAttribute("data-report-view")||($(".write-guide-buttom-box").css("display","flex"),S=!0,t.disconnect()))}}catch(r){n=!0,i=r}finally{try{!o&&s["return"]&&s["return"]()}finally{if(n)throw i}}}),U={attributes:!0,attributeFilter:["data-report-view"]};F.observe(N,U)}else $(".write-guide-buttom-box").remove()}else $(".write-guide-buttom-box").remove();$("#blog_detail_zk_collection").click(function(){getCookie("UserName")?window.csdn.collectionBox.show(window.csdn.collectionBox.params):window.csdn.loginBox.show({spm:"1001.2101.3001.4130"})}),$("#tool-reward").on("click",function(){$("#reward").show()});var P=$("#tool-downloadpdf");P.length>0&&P.on("click",function(){setTimeout(function(){$(".blog-content-box").print()},500)}),$("#is-like").on("click",function(){isUnLikeStatus?i(!0):n(!1)}),$("#is-unlike").on("click",function(){isLikeStatus?n(!0):i(!1)}),$("#getVipUrl").on("click",function(){$(this).hasClass("unlogin")?window.csdn.loginBox.show({spm:"1001.2101.3001.4249"}):window.csdn&&window.csdn.userOrderTip&&window.csdn.userOrderTip.show({product_id:1151,goods_id:2076,flag:6,tags:["6"]})}),$("#btn-readmore-zk").on("click",function(){$("#btn-readmore").show()}),$("#btn-no-login").on("click",function(){window.csdn.loginBox.show({spm:"1001.2101.3001.9442"})}),$("#btn-readmore").on("click",function(){var t=$(window).height();t*$(this).attr("height");$(".hide-article-box").show(),$("div.article_content")[0].style="height:2000px; overflow: hidden;",e(),$("#btn-readmore").hide(),$(".btn-readmore-gz")&&$(".btn-readmore-gz span").text("阅读全文")}),$(document).on("click",".is-collection > a.tool-item-href",function(){getCookie("UserName")?window.csdn.collectionBox.show(window.csdn.collectionBox.params):window.csdn.loginBox.show({spm:"1001.2101.3001.4130"})}),$(document).on("click",".btn-change-collect",function(){getCookie("UserName")?window.csdn.collectionBox.show(window.csdn.collectionBox.params):window.csdn.loginBox.show({spm:"1001.2101.3001.4130"})}),$(document).on("click",".tool-more .article-report",function(){getCookie("UserName")?showReportNew(!1,articleTitles):window.csdn.loginBox.show()}),e(),$(window).scroll(function(){e()}),$(window).resize(function(){e()}),$(document).on("click",".csdn-side-toolbar .option-box",function(t){isShowConcision&&setTimeout(function(){e()},100)}),$(".btn-readmore").click(e);var A=!0;window.csdn.collectionBox.params={url:curentUrl,title:articleTitle,description:articleDesc,author:username,source_id:articleId,source:"blog",collectionCallBack:function(e){s(e)}},c();var E={blance:"",rewardBtNew:$("#rewardBtNew"),rewardNew:$("#rewardNew"),rewardContent:$(".reward-box-new .reward-content"),skinBoxshadow:$(".skin-boxshadow"),rewardClose:$(".reward-popupbox-new .reward-close"),sureBoxBlance:$(".reward-popupbox-new .sure-box-blance"),moneyNum:1,payType:$(".reward-popupbox-new .pay-type"),domBlance:$(".reward-popupbox-new .pay-type-num"),rewardBt:$(".reward-popupbox-new .reward-sure"),chooseMoney:$(".reward-popupbox-new .choose-money"),customizeMoney:$(".reward-popupbox-new .customize-money"),customizeTip:$(".reward-popupbox-new .customize-tip"),isShowCode:$(".reward-popupbox-new .sure-box-money"),codeNum:$(".reward-popupbox-new .code-num"),codeImgBox:$(".reward-popupbox-new .code-img-box"),payrun:'<div class="renovate"><img src="'+blogStaticHost+'dist/pc/img/pay-time-out.png"><span>获取中</span></div>',repeatAgain:'<div class="renovate"><img src="'+blogStaticHost+'dist/pc/img/pay-time-out.png"><span>点击重新获取</span></div>',ajaxRewardUrl:"phoenix/web/v1/reward/article-users",initHeighgt:0};E.rewardBtNew.on({click:function(){getCookie("UserName")?getCookie("UserName")!==username?(r(E.ajaxRewardUrl,l,E.rewardContent),E.codeNum.html("¥"+E.moneyNum),E.sureBoxBlance.find(".tip").html(""),E.isShowCode.slideDown(500),E.sureBoxBlance.slideUp(500),E.rewardNew.animate({"max-height":480+E.initHeighgt+"px"},500),u(),E.skinBoxshadow.fadeIn(),E.rewardNew.fadeIn()):showToast({text:"自己不能打赏自己",bottom:"10%",zindex:9002,speed:500,time:1500}):window.csdn.loginBox.show({spm:"1001.2101.3001.4237"})}}),E.rewardClose.on("click",function(e){E.rewardNew.fadeOut(),E.skinBoxshadow.fadeOut();var t=e||window.e;t&&t.stopPropagation?t.stopPropagation():t.cancelBubble=!0}),E.payType.click(function(){E.payType.removeClass("active"),$(this).addClass("active"),m()?(d(E.blance,E.moneyNum),E.isShowCode.slideUp(500),E.sureBoxBlance.slideDown(500,function(){E.sureBoxBlance.find(".tip").html('您的余额不足，请更换扫码支付或<a target="_blank" data-report-click=\'{"mod":"1597646289_003","spm":"1001.2101.3001.4302"}\' href="https://i.csdn.net/#/wallet/balance/recharge?utm_source=RewardVip" class="go-invest">充值</a>')}),E.rewardNew.animate({"max-height":422+E.initHeighgt+"px"},500)):(E.codeNum.html("¥"+E.moneyNum),E.sureBoxBlance.find(".tip").html(""),E.isShowCode.slideDown(500),E.sureBoxBlance.slideUp(500),E.rewardNew.animate({"max-height":480+E.initHeighgt+"px"},500),u())}),E.chooseMoney.click(function(){E.chooseMoney.removeClass("choosed"),E.customizeMoney.removeClass("active"),E.customizeMoney.attr("placeholder","自定义"),E.customizeMoney.val(""),$(this).addClass("choosed"),E.moneyNum=$(this).data("id"),E.codeNum.html("¥"+E.moneyNum),m()?d(E.blance,E.moneyNum):u()}),E.customizeMoney.on({focus:function(){E.chooseMoney.removeClass("choosed"),$(this).addClass("active"),$(this).attr("placeholder","1-500"),m()&&(E.sureBoxBlance.addClass("active"),E.sureBoxBlance.find(".tip").css({display:"none"}))},input:function(){$(this)[0].value=$(this)[0].value.replace(/^(0+)|[^\d]+/g,""),$(this)[0].value?($(this)[0].value>500&&($(this)[0].value=2,E.customizeTip.fadeIn(1e3,function(){E.customizeTip.fadeOut(1e3)})),d(E.blance,$(this)[0].value),E.sureBoxBlance.find(".tip").removeAttr("style","")):m()&&(E.sureBoxBlance.addClass("active"),E.sureBoxBlance.find(".tip").css({display:"none"}))},blur:function(){p($(this)[0])}}),E.customizeMoney.on("keydown",function(e){var t=e||event;13==t.which&&p($(this)[0])}),E.rewardBt.on("click",function(e){if(getCookie("UserName"))if(initRewardObject&&""!=initRewardObject.sign){if(!E.sureBoxBlance.hasClass("active")&&"none"!==E.sureBoxBlance.css("display")&&m&&0<Number(E.moneyNum)<500){var t={product_id:72029,goods_id:77957,num:Number(E.moneyNum),flag:39,request_type:4,is_use_balance:2,ext:initRewardObject,stringExt:JSON.stringify(initRewardObject)};$.ajax({url:"https://mall.csdn.net/mp/mallorder/order/quickBuy",type:"POST",dataType:"json",contentType:"application/json",data:JSON.stringify(t),xhrFields:{withCredentials:!0},success:function(e){e.code?b():400103012===res.code&&showToast({text:"余额不足，请选择其他支付方式",bottom:"10%",zindex:9002,speed:500,time:1200})}})}}else showToast({text:"打赏信息失效，请刷新页面重试",bottom:"10%",zindex:9002,speed:500,time:1500});else window.csdn.loginBox.show({spm:"1001.2101.3001.4237"})}),E.codeImgBox.on("click",".renovate",function(){u()});var M=!1;$("#btnAttent").on("click",function(){x()}),$(".tool-attend").on("click",function(e){e.originalEvent&&(M=!0),$("#btnAttent").trigger("click")}),$("#health-companies").on("click",function(){getCookie("UserName")?$(this).hasClass("active")&&(window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({spm:"1001.2101.3001.4429"}),"block"==$("#is-like-img").css("display")&&(isUnLikeStatus?i(!0):n(!1)),"block"==$("#is-collection-img").css("display")&&a(),$(".tool-bt-attend").length>0&&($(".tool-attend").trigger("click"),window.csdn.report&&"function"==typeof window.csdn.report.reportClick&&window.csdn.report.reportClick({mod:"1592215036_002",spm:"1001.2101.3001.4132",extend1:"关注"})),$("#is-like-imgactive-animation-like").show().addClass("active-animation"),$("#is-collection-img-collection").show().addClass("active-animation"),$(".tool-item-follow").show().addClass("active-animation"),setTimeout(function(){$("#is-like-imgactive-animation-like").hide().removeClass("active-animation"),$("#is-collection-img-collection").hide().removeClass("active-animation"),$(".tool-item-follow").hide().removeClass("active-animation")},800)):window.csdn.loginBox.show()});var R=!0;$(".toolbox-list a.tool-item-href").on({mouseover:function(){if(R){var e=$(this).find("img.isdefault"),t=$(this).find("span.count");e.attr("src");"block"==e.css("display")?"White"==skinStatus?t.css({color:"#999999"}):t.css({color:"#999aaa"}):t.css({color:"#fc5531"}),R=!R}},mouseout:function(){if(!R){var e=$(this).find("img.isdefault"),t=$(this).find("span.count");e.attr("src");"block"==e.css("display")?"White"==skinStatus?t.css({color:"#999aaa"}):t.css({color:"#999999"}):t.css({color:"#fc5531"}),R=!R}}});var j=$("#shareBgIcon");if(j.length&&j.addClass("icon"+(Math.floor(5*Math.random())+1)),!getCookie("UserName")){var L=$("div.article_content"),D=L.offset().top,O=L.height(),H=document.body.clientHeight||document.documentElement.clientHeight,z=$(document).scrollTop(),q=!0;$(document).scroll(function(){z=$(document).scrollTop(),(z+H-D>O/2||z+H-D>2*H)&&q&&$(".tool-active-list").show()}),$("#tool-active-list-collection").on("click",function(){window.csdn.loginBox.show({spm:"1001.2101.3001.9443"})}),$("#tool-active-list-close").on("click",function(){$(".tool-active-list").hide(),q=!1})}}),$(function(){function e(e,t){var o=6,n=new Date;n.setTime(n.getTime()+36e5*o),document.cookie=e+"="+escape(t)+";expires="+n.toGMTString()+";domain=.csdn.net;path=/"}function t(e){var t,o=new RegExp("(^| )"+e+"=([^;]*)(;|$)");return(t=document.cookie.match(o))?unescape(t[2]):null}if(!currentUserName){var o=$("div.article_content"),n=o.offset().top,i=o.height(),a=document.body.clientHeight||document.documentElement.clientHeight,s=$(document).scrollTop();$(document).scroll(function(){s=$(document).scrollTop(),t("loginbox_strategy")||t("unlogin_scroll_step")||t("UserName")||(s+a-n>i/2||s+a-n>2*a)&&(window.csdn.loginBox.show({spm:"1000.2115.3001.9454"}),e("unlogin_scroll_step",(new Date).getTime()))})}}),"undefined"!=typeof document.addEventListener&&document.addEventListener("DOMContentLoaded",function(){return"undefined"==typeof Chart?void("undefined"!=typeof console&&console.log("ERROR: You must include chart.min.js on this page in order to use Chart.js")):void[].forEach.call(document.querySelectorAll("div.chartjs"),function(e){var t,o;t="undefined"!=typeof chartjs_colors?chartjs_colors:"undefined"!=typeof chartjs_colors_json?JSON.parse(chartjs_colors_json):{fillColor:"rgba(151,187,205,0.5)",strokeColor:"rgba(151,187,205,0.8)",highlightFill:"rgba(151,187,205,0.75)",highlightStroke:"rgba(151,187,205,1)",data:["#B33131","#B66F2D","#B6B330","#71B232","#33B22D","#31B272","#2DB5B5","#3172B6","#3232B6","#6E31B2","#B434AF","#B53071"]},o="undefined"!=typeof chartjs_config?chartjs_config:"undefined"!=typeof chartjs_config_json?JSON.parse(chartjs_config_json):{Bar:{animation:!1},Doughnut:{animateRotate:!1},Line:{animation:!1},Pie:{animateRotate:!1},PolarArea:{animateRotate:!1}};var n=e.getAttribute("data-chart"),i=JSON.parse(e.getAttribute("data-chart-value"));if(i&&i.length&&n){e.innerHTML="";var a=document.createElement("canvas");a.height=e.getAttribute("data-chart-height"),e.appendChild(a);var s=document.createElement("div");s.setAttribute("class","chartjs-legend"),e.appendChild(s);var c,r=a.getContext("2d"),l=new Chart(r);if("bar"!=n)for(c=0;c<i.length;c++)i[c].color=t.data[c],i[c].highlight=t.data[c];if("bar"==n||"line"==n){var d={datasets:[{label:"",fillColor:t.fillColor,strokeColor:t.strokeColor,highlightFill:t.highlightFill,highlightStroke:t.highlightStroke,data:[]}],labels:[]};for(c=0;c<i.length;c++)i[c].value&&(d.labels.push(i[c].label),d.datasets[0].data.push(i[c].value));s.innerHTML=""}"bar"==n?l.Bar(d,o.Bar):"line"==n?l.Line(d,o.Line):"polar"==n?s.innerHTML=l.PolarArea(i,o.PolarArea).generateLegend():"pie"==n?s.innerHTML=l.Pie(i,o.Pie).generateLegend():s.innerHTML=l.Doughnut(i,o.Doughnut).generateLegend()}})}),$(function(){var e=setInterval(function(){if(window.Swiper){clearInterval(e);var t=new CSDNviewImg("#content_views");window.t=t,$("#content_views").on("click","img",function(e){if(e.currentTarget.src&&e.currentTarget.className.indexOf("contentImg-no-view")<0){var o=$(this).index("#content_views img:not(.contentImg-no-view)");$(".imgViewDom").fadeIn(300,function(){t.update(),t.slideTo(o,0)}),$("body").css({overflow:"hidden"})}})}},1e3);window.csdn.middleJump?window.csdn.middleJump({el:"#content_views",url:"https://link.csdn.net"}):$("#content_views").find("a").click(function(e){if(this.href&&"_self"!==this.target){e.preventDefault();var t=window.open(this.href,"_blank");t.focus()}})}),$(function(){function t(e,t,n){$.get(blog_address+"/phoenix/article/privacy?articleId="+e+"&index="+t+"&reason="+n,function(e){var t=e;t.result?(alert("文章已私密！"),location.reload()):t.content?o(t.content):alert("无法私密，请到后台私密！")})}function o(e){$(".super-private").hide(),$(".private-error").height(126).show().children(".private-content").text(e)}function n(){$(".private-form").removeClass("active").addClass("no-active")}var i={markdown_line:function(){$(".markdown_views pre").addClass("prettyprint"),$("pre.prettyprint code").each(function(){var e=$(this).text().split("\n").length+($(this).hasClass("hljs")?1:0),t=$("<ul/>").addClass("pre-numbering").hide();$(this).addClass("has-numbering").parent().append(t);for(var o=1;o<e;o++)t.append($("<li/>").text(o));t.fadeIn(1700)}),$(".pre-numbering li").css("color","#999"),setTimeout(function(){$(".math").each(function(e,t){$(this).find("span").last().css("color","#fff")})}),setTimeout(function(){$(".toc a[target='_blank']").attr("target",""),$("a.reversefootnote,a.footnote").attr("target","")},500)},html_line:function(){function e(){$(".CopyToClipboard").each(function(){var e=new ZeroClipboard.Client;e.setHandCursor(!0),e.addEventListener("load",function(e){}),e.addEventListener("mouseOver",function(e){var t=e.movie.parentNode.parentNode.parentNode.parentNode.nextSibling.innerHTML;t=t.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&"),e.setText(t)}),e.addEventListener("complete",function(e,t){alert("代码已经复制到你的剪贴板。")}),e.glue(this,this.parentNode)})}$(".article_content pre").each(function(){var e=$(this);try{if(e.attr("class").indexOf("brush:")!=-1){var t=e.attr("class").split(";")[0].split(":")[1];e.attr("name","code"),e.attr("class",t)}e.attr("class")&&e.attr("name","code")}catch(o){}}),$(".article_content textarea[name=code]").each(function(){var e=$(this);e.attr("class").indexOf(":")!=-1&&e.attr("class",e.attr("class").split(":")[0])}),$(".highlighter").each(function(e,t){hljs.highlightBlock(t),hljs.lineNumbersBlock(t)}),window.clipboardData||setTimeout(e,1e3)}},a=$(".markdown_views")[0];a?(i.markdown_line(),/Safari/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&window.csdn.setSafariCodestyle()):i.html_line(),$(document).on("click",".input-mod",function(){$(".select-option").show(),$(".select-button").removeClass("rotate0").addClass("rotate180")}),$(document).on("input porpertychange",".private-input",function(){n(),$(this).val().length>120&&$(this).val($(this).val().substr(0,120)),$(".textarea-box .number").text(120-$(this).val().length),$(this).val().length>0&&$(".private-form").removeClass("no-active").addClass("active")}),$(document).on("click",".select-option li",function(){$(".super-private").height(126),$(".private-content .other").hide(),n(),$(".select-active").text($(this).text()).data("index",$(this).data("index")),$(this).data("isinput")?($(".super-private").height(240),$(".private-content .other").show()):$(".private-form").removeClass("no-active").addClass("active"),$(".select-button").addClass("rotate0").removeClass("rotate180"),$(".select-option").fadeOut()}),$(document).on("click",".select-button",function(){"block"===$(".select-option").css("display")?($(".select-option").fadeOut(),$(this).removeClass("rotate180").addClass("rotate0")):($(this).removeClass("rotate0").addClass("rotate180"),$(".select-option").fadeIn()),e.stopPropagation(),e.cancelBubble=!0}),$(document).on("click",".private-close, .close-active",function(){$(".super-private").hide(),$(".mask-dark").hide()}),$(document).on("click",".private-footer .active",function(){var e=$(".select-active").data("index"),o=$(".reason").val();e||alert("请选择原因"),text="text",t(articleId,e,o)})}),$(function(){$("article").find("table").map(function(){$(this).wrap('<div class="table-box" />')})}),$(function(){window.addEventListener("message",function(e){"https://inscode.csdn.net"===e.origin&&"needLogin"===e.data&&window.csdn.loginBox.show()})}),!function(){function e(){var e=navigator.userAgent.toLowerCase();return window.ActiveXObject||"ActiveXObject"in window?"ie":e.indexOf("firefox")>=0?"firefox":e.indexOf("chrome")>=0?"chrome":e.indexOf("opera")>=0?"opera":e.indexOf("safari")>=0?"safari":void 0}var t=function(e,t,o,n,i,a){function s(o,n,i){var r=(o+n)/2;if(i<=0||n-o<a)return r;var l="("+e+":"+r+t+")";return c(l).matches?s(r,n,i-1):s(o,r,i-1)}var c,r,l,d;window.matchMedia?c=window.matchMedia:(r=document.getElementsByTagName("head")[0],l=document.createElement("style"),r.appendChild(l),d=document.createElement("div"),d.className="mediaQueryBinarySearch",d.style.display="none",document.body.appendChild(d),c=function(e){l.sheet.insertRule("@media "+e+"{.mediaQueryBinarySearch {text-decoration: underline} }",0);var t="underline"==getComputedStyle(d,null).textDecoration;return l.sheet.deleteRule(0),{matches:t}});var m=s(o,n,i);return d&&(r.removeChild(l),document.body.removeChild(d)),m},o={};o.ie=function(){return window.screen.deviceXDPI/window.screen.logicalXDPI},o.firefox=function(){return window.devicePixelRatio?window.devicePixelRatio:t("min--moz-device-pixel-ratio","",0,10,20,1e-4)},o.opera=function(){return window.outerWidth/window.innerWidth},o.chrome=function(){if(window.devicePixelRatio)return window.devicePixelRatio;var e=document.createElement("div");e.innerHTML="1",e.setAttribute("style","font:100px/1em sans-serif;-webkit-text-size-adjust:none;position: absolute;top:-100%;"),document.body.appendChild(e);var t=1e3/e.clientHeight;return t=Math.round(100*t)/100,document.body.removeChild(e),t},o.safari=function(){return window.outerWidth/window.innerWidth},window.detectZoom=function(){return o[e()]()}}(void 0),$(function(){function e(){var e=navigator.userAgent.toLowerCase();return e.indexOf("win")>=0?"win":e.indexOf("mac")>=0?"mac":void 0}function t(e,t){return Math.round(e*Math.pow(10,t))/Math.pow(10,t)}function o(e){return"win"==e.system&&1!=e.zoom||("mac"==e.system&&e.zoom%1!=0&&e.zoom%2!=0||void 0)}function n(n){var i={win:{add:187,minus:189,origin:48},mac:{add:187,minus:189,origin:48}},a=i[e()];if(n.ctrlKey||n.metaKey)if(n.keyCode==a.add||n.keyCode==a.minus){var s={zoom:t(detectZoom(),2),system:e()};l(o(s),r,u)}else n.keyCode!=a.add&&n.keyCode!=a.origin||d.animate(m,2e3)}function i(e){setTimeout(n,300,e)}function a(e,t){return localStorage.setItem(e,t)}function s(e){return localStorage.getItem(e)}function c(e){var t=document.createElement("style");t.type="text/css",t.innerHTML=[".leftPop{width:330px;position: fixed;font-size: 12px;","box-shadow: 0 4px 8px 0 rgba(0,0,0,0.10);padding:16px 40px 16px 16px;z-index: 100;","}",".leftPop .leftPop-close{position: absolute;right: 20px;"+e,"cursor: pointer;","}"].join(""),document.getElementsByTagName("head")[0].appendChild(t)}function r(e){var t=$(".leftPop");if(t.length>0)return t.stop(!0,!1).animate(e.animate,e.animateTime),!1;var o='<svg t="1536830466687" class="icon leftPop-close" viewBox="0 0 1024 1024" version="1.1" ><title>不再显示</title><path d="M512 438.378667L806.506667 143.893333a52.032 52.032 0 1 1 73.6 73.621334L585.621333 512l294.485334 294.485333a52.074667 52.074667 0 0 1-73.6 73.642667L512 585.621333 217.514667 880.128a52.053333 52.053333 0 1 1-73.621334-73.642667L438.378667 512 143.893333 217.514667a52.053333 52.053333 0 1 1 73.621334-73.621334L512 438.378667z" fill="" p-id="15859"></path></svg>';t=$('<div class="leftPop">'+o+"</div>"),c(e.closeColor),t.append(e.template).css(e.style),t.appendTo($("body")).delay(2e3).animate(e.animate,e.animateTime),d=t,$(".leftPop-close").on("click",function(){t.stop(!0,!1).animate(e.closeAnimate,e.animateTime),a("leftPop",0)})}function l(e,t,o){var n=s("leftPop");e&&null==n?t(o):d.stop(!0,!1).animate(o.closeAnimate,o.animateTime)}var d=({zoom:t(detectZoom(),2),system:e()},$("leftPop")),m={right:"-100%"},p={win:{even:"keyup",fun:n},mac:{even:"keydown",fun:i}};$(window).on(p[e()].even,p[e()].fun);var u={template:"<span>你的浏览器目前处于缩放状态，页面可能会出现错位现象，建议100%大小显示。</span>",style:{right:"-100%","background-color":"#EBF5FD",top:"100px","border-left":"4px solid #70B8F0"},closeColor:"fill:#70B8F0;",animate:{right:0},closeAnimate:m,animateTime:2e3}});