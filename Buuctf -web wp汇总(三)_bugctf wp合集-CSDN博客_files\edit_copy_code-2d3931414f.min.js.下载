!function(e){"use strict";function t(){return!!window.navigator.userAgent.toLowerCase().match(/(phone|pad|pod|iphone|ipod|ios|ipad|android|mobile|blackberry|iemobile|mqqbrowser|juc|fennec|wosbrowser|browserng|webos|symbian|windows phone)/i)}function n(){return!!window.navigator.userAgent.toLowerCase().match(/(csdn)/i)}function o(e){for(var t=document.cookie.split("; "),n=0;n<t.length;n++){var o=t[n].split("=");if(e==o[0])return o[1]}return null}function r(e){var t=e.target||e.srcElement,n=document.documentElement.scrollTop;if(t.className.indexOf(w)>-1){e.preventDefault();var o=document.getElementById("hljs-copy-el");o||(o=document.createElement("textarea"),o.style.position="absolute",o.style.left="-9999px",o.style.top=n+"px",o.id="hljs-copy-el",document.body.appendChild(o)),o.textContent=e.currentTarget.innerText.replace(/[\u00A0]/gi," "),m($(e.currentTarget.parentNode).attr("data-index")),a("#hljs-copy-el");try{var r=document.execCommand("copy");o.remove(),t.dataset.title=r?y:x,r&&setTimeout(function(){t.dataset.title=h},3e3)}catch(i){t.dataset.title=x}}}function i(e){var t=e.target||e.srcElement,n=document.documentElement.scrollTop;if(t.className.indexOf(w)>-1){e.preventDefault();var o=document.getElementById("hljs-copy-el");o||(o=document.createElement("textarea"),o.style.position="absolute",o.style.left="-9999px",o.style.top=n+"px",o.id="hljs-copy-el",document.body.appendChild(o)),o.textContent=e.currentTarget.parentNode.innerText.replace(/[\u00A0]/gi," "),m($(e.currentTarget.parentNode).attr("data-index")),a("#hljs-copy-el");try{var r=document.execCommand("copy");o.remove(),t.dataset.title=r?y:x,r&&setTimeout(function(){t.dataset.title=h},3e3)}catch(i){t.dataset.title=x}}}function a(e){if(e="string"==typeof e?document.querySelector(e):e,navigator.userAgent.match(/ipad|ipod|iphone/i)){var t=e.contentEditable,n=e.readOnly;e.contentEditable=!0,e.readOnly=!0;var o=document.createRange();o.selectNodeContents(e);var r=window.getSelection();r.removeAllRanges(),r.addRange(o),e.setSelectionRange(0,999999),e.contentEditable=t,e.readOnly=n}else e.select()}function d(){var e=document.createElement("style");e.type="text/css",window.navigator.userAgent.toLowerCase().match(/(csdn)/i)&&isShowCodeFull?e.innerHTML=["pre{position: relative}","pre:hover .code-full-screen{display:block !important;}",".code-full-screen{","display: none !important;","position: absolute;","right: 4px;","top: 4px;","width: 24px !important;","height: 24px !important;","margin: 4px !important;","}","pre:hover .{0}{display: block}",".{0}{","display: none;","position: absolute;","right: 34px;","top: 4px;","font-size: 12px;","color: #ffffff;","background-color: #9999AA;","padding: 2px 8px;","margin: 4px !important;","border-radius: 4px;","cursor: pointer;"," box-shadow: 0 2px 4px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.05);","}",".{0}:after{","content: attr(data-title)","}","code .{0}{","margin: 2px 8px;","}"].join("").format(w):e.innerHTML=["pre{position: relative}","pre:hover .code-full-screen{display:none !important;}",".code-full-screen{","display: none !important;","position: absolute;","right: 4px;","top: 3px;","width: 24px !important;","height: 24px !important;","margin: 4px !important;","}","pre:hover .{0}{display: block}",".{0}{","display: none;","position: absolute;","right: 4px;","top: 4px;","font-size: 12px;","color: #ffffff;","background-color: #9999AA;","padding: 2px 8px;","margin: 8px;","border-radius: 4px;","cursor: pointer;"," box-shadow: 0 2px 4px rgba(0,0,0,0.05), 0 2px 4px rgba(0,0,0,0.05);","}",".{0}:after{","content: attr(data-title)","}","code .{0}{","margin: 2px 8px;","}"].join("").format(w),document.getElementsByTagName("head")[0].appendChild(e)}function c(){"complete"===document.readyState?s():e.addEventListener("DOMContentLoaded",s)}function s(){try{var e;if("ckeditor"==v){e=document.querySelectorAll("code.hljs");for(var t in e)e.hasOwnProperty(t)&&l(e[t].parentNode)}else{e=A;for(var t in e)e.hasOwnProperty(t)&&l(e[t])}}catch(n){console.error("CopyButton error: ",n)}}function p(e){var r=e.target||e.srcElement;r.className.indexOf(w)>-1&&(n()?window.location.href="https://passport.csdn.net/account/login?ref=codecopy":t()?toobarFlag(20,21)?$(".ab-app-shadowbox").show():window.csdn.loginBox.show({spm:"1001.2101.3001.7759"}):o("UserName")||window.csdn.loginBox.show({spm:"1001.2101.3001.4334"}))}function l(e){var t="";if("object"==typeof e&&null!==e){var n=".signin(event)",o="hljs",r=".copyCode(event)";"mdeditor"===v&&(o="mdcp"),r=o+r,C&&(r=o+n,t='data-report-click=\'{"spm":"1001.2101.3001.4334"}\''),window.navigator.userAgent.toLowerCase().match(/(csdn)/i)&&isShowCodeFull?e.innerHTML=e.innerHTML+('<img class="code-full-screen app_remove_content no-enlarge-img" src="'+blogStaticHost+'dist/app/img/codeAmplify.png"><div class="{0} {2}" data-title="{1}" '+t+"></div>").format(w,h,b):e.innerHTML=e.innerHTML+('<div class="{0} {2}" data-title="{1}" '+t+"></div>").format(w,h,b),"hljs"===o?e.querySelector(".hljs-button").setAttribute("onclick",r):(e.setAttribute("onclick",r),e.style.position="unset")}}function m(e){var t="1001.2101.3001.4259",n=JSON.stringify({codeId:e});if(window.csdn.report&&"function"==typeof window.csdn.report.reportClick)window.csdn.report.reportClick({spm:t,extra:n});else if(window.isApp){var o={spm:t,extra:n},r={trackingInfo:JSON.stringify(o)};f&&window.jsCallBackListener.csdntrackclick(JSON.stringify(r)),g&&window.webkit.messageHandlers.csdntrackclick.postMessage(JSON.stringify(r))}}if(window.ActiveXObject||"ActiveXObject"in window)return!1;var u=navigator.userAgent,f=u.indexOf("Android")>-1||u.indexOf("Adr")>-1,g=!!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/),w="hljs-button",h="复制",x="复制失败",y="复制成功",v=void 0,b="",C=!1;n()||o("UserName")||(C=!0),C&&(h="登录后复制",b="signin"),$("#content_views").find("pre").each(function(e,t){$(t).attr("data-index",e)}),String.prototype.format=String.prototype.f=function(){var e=arguments;return!!this&&this.replace(/\{(\d+)\}/g,function(t,n){return e[n]?e[n]:t})};var A=document.querySelectorAll("pre code");document.querySelectorAll("div.htmledit_views").length>0?(e.hljs.initCopyButtonOnLoad=c,e.hljs.addCopyButton=l,e.hljs.copyCode=i,e.hljs.signin=p,d(),v="ckeditor"):A.length>0&&(window.mdcp?window.mdcp:window.mdcp={},window.mdcp.copyCode=r,window.mdcp.signin=p,c(),d(),v="mdeditor")}(window);