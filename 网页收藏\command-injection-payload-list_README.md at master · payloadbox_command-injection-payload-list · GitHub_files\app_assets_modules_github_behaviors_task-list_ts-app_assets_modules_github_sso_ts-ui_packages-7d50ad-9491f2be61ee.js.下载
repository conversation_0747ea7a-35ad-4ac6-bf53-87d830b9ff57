"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-7d50ad"],{48804:(e,t,n)=>{n.d(t,{L$:()=>u,SE:()=>T,nj:()=>d});var s,l=n(56959),i=n(59753),r=n(40987),o=n(36071),c=n(65935),a=n(58700);function u(e){if(e.querySelector(".js-task-list-field")){let t=e.querySelectorAll("task-lists");for(let e of t)if(e instanceof r.Z){e.disabled=!1;let t=e.querySelectorAll("button");for(let e of t)e.disabled=!1}}}function d(e){for(let t of e.querySelectorAll("task-lists"))if(t instanceof r.Z){t.disabled=!0;let e=t.querySelectorAll("button");for(let t of e)t.disabled=!0}}function m(e,t,n){let s=e.querySelector(".js-comment-update");d(e),I(e);let l=s.elements.namedItem("task_list_track");l instanceof Element&&l.remove();let i=s.elements.namedItem("task_list_operation");i instanceof Element&&i.remove();let r=document.createElement("input");r.setAttribute("type","hidden"),r.setAttribute("name","task_list_track"),r.setAttribute("value",t),s.appendChild(r);let o=document.createElement("input");if(o.setAttribute("type","hidden"),o.setAttribute("name","task_list_operation"),o.setAttribute("value",JSON.stringify(n)),s.appendChild(o),!s.elements.namedItem("task_list_key")){let e=s.querySelector(".js-task-list-field"),t=e.getAttribute("name"),n=t.split("[")[0],l=document.createElement("input");l.setAttribute("type","hidden"),l.setAttribute("name","task_list_key"),l.setAttribute("value",n),s.appendChild(l)}e.classList.remove("is-comment-stale"),(0,a.Bt)(s)}(0,o.N7)(".js-task-list-container .js-task-list-field",function(e){let t=e.closest(".js-task-list-container");u(t),I(t)}),(0,o.N7)(".js-convert-tasklist-to-block-enabled .contains-task-list",function(e){let t=$(e);if(!t)return;let n=Array.from(t.children).some(e=>e.classList.contains("task-list-item-convert-container"));if(n)return;let s=e.ownerDocument.querySelector(".js-convert-to-block-template"),l=s?.content.cloneNode(!0);l&&t.appendChild(l)}),(0,i.on)("task-lists-move","task-lists",function(e){let{src:t,dst:n}=e.detail,s=e.currentTarget.closest(".js-task-list-container");m(s,"reordered",{operation:"move",src:t,dst:n})}),(0,i.on)("task-lists-check","task-lists",function(e){let{position:t,checked:n}=e.detail,s=e.currentTarget.closest(".js-task-list-container");m(s,`checked:${n?1:0}`,{operation:"check",position:t,checked:n})}),(0,i.on)("click",".js-convert-to-block-button",function(e){let t=$(e.target);if(!t)return;let n=t.closest("task-lists");if(!n)throw Error("parent not found");let s=T(t),l=e.currentTarget.closest(".js-task-list-container");m(l,"converted",{operation:"convert_to_block",position:s})}),(0,c.AC)(".js-task-list-container .js-comment-update",async function(e,t){let n;let s=e.closest(".js-task-list-container"),l=e.elements.namedItem("task_list_track");l instanceof Element&&l.remove();let i=e.elements.namedItem("task_list_operation");i instanceof Element&&i.remove();try{n=await t.json()}catch(t){let e;try{e=JSON.parse(t.response.text)}catch(e){}if(e&&e.stale){let e=s.querySelector(".js-task-list-field");e.classList.add("session-resumable-canceled"),e.classList.remove("js-session-resumable")}else 422===t.response.status||window.location.reload()}n&&(i&&n.json.source&&(s.querySelector(".js-task-list-field").value=n.json.source),u(s),requestAnimationFrame(()=>I(s)))});let f=!1,p=!1,b=null;function h(e){let t="insertLineBreak"===e.inputType;f=!!t}function k(e){if(!f){let t="insertLineBreak"===e.inputType;if(!t)return}let t=e.target;g(t),f=!1}function g(e){let t=q(e.value,[e.selectionStart,e.selectionEnd]);void 0!==t&&y(e,t)}function y(e,t){if(null===b||!0===b){e.contentEditable="true";try{let n;f=!1,t.commandId===s.insertText?(n=t.autocompletePrefix,null!==t.writeSelection[0]&&null!==t.writeSelection[1]&&(e.selectionStart=t.writeSelection[0],e.selectionEnd=t.writeSelection[1])):e.selectionStart=t.selection[0],b=document.execCommand(t.commandId,!1,n)}catch(e){b=!1}e.contentEditable="false"}if(!b){try{document.execCommand("ms-beginUndoUnit")}catch(e){}e.value=t.text;try{document.execCommand("ms-endUndoUnit")}catch(e){}e.dispatchEvent(new CustomEvent("input",{bubbles:!0,cancelable:!0}))}null!=t.selection[0]&&null!=t.selection[1]&&(e.selectionStart=t.selection[0],e.selectionEnd=t.selection[1])}function w(e){if(!p&&"Enter"===e.key&&e.shiftKey&&!e.metaKey){let t=e.target,n=x(t.value,[t.selectionStart,t.selectionEnd]);void 0!==n&&(y(t,n),e.preventDefault(),(0,i.f)(t,"change"))}}function S(){p=!0}function v(){p=!1}function E(e){if(p)return;if("Escape"===e.key){C(e);return}if("Tab"!==e.key)return;let t=e.target,n=L(t.value,[t.selectionStart,t.selectionEnd],e.shiftKey);void 0!==n&&(e.preventDefault(),y(t,n))}(0,o.N7)(".js-task-list-field",{subscribe:e=>(0,l.qC)((0,l.RB)(e,"keydown",E),(0,l.RB)(e,"keydown",w),(0,l.RB)(e,"beforeinput",h),(0,l.RB)(e,"input",k),(0,l.RB)(e,"compositionstart",S),(0,l.RB)(e,"compositionend",v))}),function(e){e.insertText="insertText",e.delete="delete"}(s||(s={}));let A=/^(\s*)?/;function x(e,t){let n=t[0];if(!n||!e)return;let l=e.substring(0,n).split("\n"),i=l[l.length-1],r=i?.match(A);if(!r)return;let o=r[1]||"",c=`
${o}`;return{text:e.substring(0,n)+c+e.substring(n),autocompletePrefix:c,selection:[n+c.length,n+c.length],commandId:s.insertText,writeSelection:[null,null]}}let _=/^(\s*)([*-]|(\d+)\.)\s(\[[\sx]\]\s)?/;function j(e,t){let n=e.split("\n");return(n=n.map(e=>{if(e.replace(/^\s+/,"").startsWith(`${t}.`)){let n=e.replace(`${t}`,`${t+1}`);return t+=1,n}return e})).join("\n")}function q(e,t){let n=t[0];if(!n||!e)return;let l=e.substring(0,n).split("\n"),i=l[l.length-2],r=i?.match(_);if(!r)return;let o=r[0],c=r[1],a=r[2],u=parseInt(r[3],10),d=Boolean(r[4]),m=!isNaN(u),f=m?`${u+1}.`:a,p=`${f} ${d?"[ ] ":""}`,b=e.indexOf("\n",n);b<0&&(b=e.length);let h=e.substring(n,b);h.startsWith(p)&&(p="");let k=i.replace(o,"").trim().length>0||h.trim().length>0;if(k){let t=`${c}${p}`,l=e.substring(n),i=t.length,r=[null,null],o=e.substring(0,n)+t+l;return m&&!e.substring(n).match(/^\s*$/g)&&(t+=l=j(e.substring(n),u+1),r=[n,n+t.length],o=e.substring(0,n)+t),{text:o,autocompletePrefix:t,selection:[n+i,n+i],commandId:s.insertText,writeSelection:r}}{let t=n-`
${o}`.length;return{autocompletePrefix:"",text:e.substring(0,t)+e.substring(n),selection:[t,t],commandId:s.delete,writeSelection:[null,null]}}}function L(e,t,n){let l=t[0]||0,i=t[1]||l;if(null===t[0]||l===i)return;let r=e.substring(0,l).lastIndexOf("\n")+1,o=e.indexOf("\n",i-1),c=o>0?o:e.length-1,a=e.substring(r,c).split("\n"),u=!1,d=0,m=0,f=[];for(let e of a){let t=e.match(/^\s*/);if(t){let s=t[0],l=e.substring(s.length);if(n){let e=s.length;s=s.slice(0,-2),d=u?d:s.length-e,u=!0,m+=s.length-e}else s+="  ",d=2,m+=2;f.push(s+l)}}let p=f.join("\n"),b=e.substring(0,r)+p+e.substring(c),h=[Math.max(r,l+d),i+m];return{text:b,selection:h,autocompletePrefix:p,commandId:s.insertText,writeSelection:[r,c]}}function T(e){let t=e.closest("task-lists");if(!t)throw Error("parent not found");let n=Array.from(t.querySelectorAll("ol, ul")).filter(e=>!e.closest("tracking-block"));return n.indexOf(e)}function C(e){let t=e.target;"backward"===t.selectionDirection?t.selectionEnd=t.selectionStart:t.selectionStart=t.selectionEnd}function I(e){if(0===document.querySelectorAll("tracked-issues-progress").length)return;let t=e.closest(".js-timeline-item");if(t)return;let n=e.querySelectorAll(".js-comment-body [type=checkbox]"),s=n.length,l=Array.from(n).filter(e=>e.checked).length,i=document.querySelectorAll("tracked-issues-progress[data-type=checklist]");for(let e of i)e.setAttribute("data-completed",String(l)),e.setAttribute("data-total",String(s))}function $(e){let t=e.closest(".contains-task-list"),n=t;for(;(n=n.parentElement.closest(".contains-task-list"))!==t&&null!==n;)t=n;return t}},40458:(e,t,n)=>{n.d(t,{Z:()=>f});var s=n(19146),l=n(34892);function i(e){return new Promise(t=>{e.addEventListener("dialog:remove",t,{once:!0})})}function r(e){let t=document.querySelector(".sso-modal");t&&(t.classList.remove("success","error"),e?t.classList.add("success"):t.classList.add("error"))}function o(e){let t=document.querySelector("meta[name=sso-expires-around]");t&&t.setAttribute("content",e)}async function c(){let e=document.querySelector("link[rel=sso-modal]"),t=await (0,s.W)({content:(0,l.a_)(document,e.href),dialogClass:"sso-modal"}),n=null,c=window.external;if(c.ssoComplete=function(e){e.error?r(n=!1):(r(n=!0),o(e.expiresAround),window.focus()),c.ssoComplete=null},await i(t),!n)throw Error("sso prompt canceled")}function a(e){if(!(e instanceof HTMLMetaElement))return!0;let t=parseInt(e.content),n=new Date().getTime()/1e3;return n>t}async function u(){let e=document.querySelector("link[rel=sso-session]"),t=document.querySelector("meta[name=sso-expires-around]");if(!(e instanceof HTMLLinkElement)||!a(t))return!0;let n=e.href,s=await fetch(n,{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}}),l=await s.json();return l}(0,n(36071).N7)(".js-sso-modal-complete",function(e){if(window.opener&&window.opener.external.ssoComplete){let t=e.getAttribute("data-error"),n=e.getAttribute("data-expires-around");window.opener.external.ssoComplete({error:t,expiresAround:n}),window.close()}else{let t=e.getAttribute("data-fallback-url");t&&(window.location.href=t)}});let d=null;function m(){d=null}async function f(){let e=await u();e||(d||(d=c().then(m).catch(m)),await d)}},67044:(e,t,n)=>{n.d(t,{EL:()=>s.EL,N9:()=>s.N9,Tz:()=>s.Tz});var s=n(11793)},76134:(e,t,n)=>{n.d(t,{Ty:()=>l,YE:()=>i,Zf:()=>r});var s=n(67044);let l=()=>{let e=document.querySelector("meta[name=keyboard-shortcuts-preference]");return!e||"all"===e.content},i=e=>/Enter|Arrow|Escape|Meta|Control|Esc/.test(e)||e.includes("Alt")&&e.includes("Shift"),r=e=>{let t=(0,s.EL)(e);return!!l()||i(t)}},56959:(e,t,n)=>{n.d(t,{RB:()=>s,qC:()=>l,w0:()=>Subscription});let Subscription=class Subscription{constructor(e){this.closed=!1,this.unsubscribe=()=>{e(),this.closed=!0}}};function s(e,t,n,s={capture:!1}){return e.addEventListener(t,n,s),new Subscription(()=>{e.removeEventListener(t,n,s)})}function l(...e){return new Subscription(()=>{for(let t of e)t.unsubscribe()})}}}]);
//# sourceMappingURL=app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-7d50ad-01b6dfbd688a.js.map