"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_github_blob-anchor_ts-app_assets_modules_github_filter-sort_ts-app_assets_-681869"],{56334:(e,t,n)=>{function r(e){let t=e.match(/#?(?:L)(\d+)((?:C)(\d+))?/g);if(t){if(1===t.length){let e=o(t[0]);if(!e)return;return Object.freeze({start:e,end:e})}if(2!==t.length)return;{let e=o(t[0]),n=o(t[1]);if(!e||!n)return;return d(Object.freeze({start:e,end:n}))}}}function i(e){let{start:t,end:n}=d(e);return null!=t.column&&null!=n.column?`L${t.line}C${t.column}-L${n.line}C${n.column}`:null!=t.column?`L${t.line}C${t.column}-L${n.line}`:null!=n.column?`L${t.line}-L${n.line}C${n.column}`:t.line===n.line?`L${t.line}`:`L${t.line}-L${n.line}`}function s(e){let t=e.length<5e3&&e.match(/(file-.+?-)L\d+?/i);return t?t[1]:""}function a(e){let t=r(e),n=s(e);return{blobRange:t,anchorPrefix:n}}function l({anchorPrefix:e,blobRange:t}){return t?`#${e}${i(t)}`:"#"}function o(e){let t=e.match(/L(\d+)/),n=e.match(/C(\d+)/);return t?Object.freeze({line:parseInt(t[1]),column:n?parseInt(n[1]):null}):null}function c(e,t){let[n,r]=u(e.start,!0,t),[i,s]=u(e.end,!1,t);if(!n||!i)return;let a=r,l=s;if(-1===a&&(a=0),-1===l&&(l=i.childNodes.length),!n.ownerDocument)throw Error("DOMRange needs to be inside document");let o=n.ownerDocument.createRange();return o.setStart(n,a),o.setEnd(i,l),o}function u(e,t,n){let r=[null,0],i=n(e.line);if(!i)return r;if(null==e.column)return[i,-1];let s=e.column-1,a=h(i);for(let e=0;e<a.length;e++){let n=a[e],r=s-(n.textContent||"").length;if(0===r){let r=a[e+1];if(t&&r)return[r,0];return[n,s]}if(r<0)return[n,s];s=r}return r}function h(e){if(e.nodeType===Node.TEXT_NODE)return[e];if(!e.childNodes||!e.childNodes.length)return[];let t=[];for(let n of e.childNodes)t=t.concat(h(n));return t}function d(e){let t=[e.start,e.end];return(t.sort(f),t[0]===e.start&&t[1]===e.end)?e:Object.freeze({start:t[0],end:t[1]})}function f(e,t){return e.line===t.line&&e.column===t.column?0:e.line===t.line&&"number"==typeof e.column&&"number"==typeof t.column?e.column-t.column:e.line-t.line}n.d(t,{Dw:()=>l,G5:()=>r,M9:()=>c,n6:()=>a})},41982:(e,t,n)=>{function*r(e,t){for(let n of e){let e=t(n);null!=e&&(yield e)}}function i(e,t,n){let i=e=>{let n=t(e);return null!=n?[e,n]:null};return[...r(e,i)].sort((e,t)=>n(e[1],t[1])).map(([e])=>e)}n.d(t,{W:()=>i})},87738:(e,t,n)=>{function r(e,t,n=.1){let r=l(e,t,n);if(r&&-1===t.indexOf("/")){let i=e.substring(e.lastIndexOf("/")+1);r+=l(i,t,n)}return r}function i(e){let t=e.toLowerCase().split(""),n="";for(let e=0;e<t.length;e++){let r=t[e],i=r.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&");0===e?n+=`(.*)(${i})`:n+=`([^${i}]*?)(${i})`}return RegExp(`${n}(.*?)$`,"i")}function s(e,t,n){if(t){let r=e.innerHTML.trim().match(n||i(t));if(!r)return;let s=!1,a=[];for(let e=1;e<r.length;++e){let t=r[e];t&&(e%2==0?s||(a.push("<mark>"),s=!0):s&&(a.push("</mark>"),s=!1),a.push(t))}e.innerHTML=a.join("")}else{let t=e.innerHTML.trim(),n=t.replace(/<\/?mark>/g,"");t!==n&&(e.innerHTML=n)}}n.d(t,{EW:()=>r,Qw:()=>s,qu:()=>o});let a=new Set([" ","-","_"]);function l(e,t,n=.1){let r=e;if(r===t)return 1;let i=r.length,s=0,l=0;for(let e=0;e<t.length;e++){let o=t[e],c=r.indexOf(o.toLowerCase()),u=r.indexOf(o.toUpperCase()),h=Math.min(c,u),d=h>-1?h:Math.max(c,u);if(-1===d)return 0;s+=.1,r[d]===o&&(s+=.1),0===d&&(s+=.9-n,0===e&&(l=1)),a.has(r.charAt(d-1))&&(s+=.9-n),r=r.substring(d+1,i)}let o=t.length,c=s/o,u=(c*(o/i)+c)/2;return l&&u+n<1&&(u+=n),u}function o(e,t){return e.score>t.score?-1:e.score<t.score?1:e.text<t.text?-1:e.text>t.text?1:0}},89359:(e,t,n)=>{function r(e){let t=document.querySelectorAll(e);if(t.length>0)return t[t.length-1]}function i(){let e=r("meta[name=analytics-location]");return e?e.content:window.location.pathname}function s(){let e=r("meta[name=analytics-location-query-strip]"),t="";e||(t=window.location.search);let n=r("meta[name=analytics-location-params]");for(let e of(n&&(t+=(t?"&":"?")+n.content),document.querySelectorAll("meta[name=analytics-param-rename]"))){let n=e.content.split(":",2);t=t.replace(RegExp(`(^|[?&])${n[0]}($|=)`,"g"),`$1${n[1]}$2`)}return t}function a(){return`${window.location.protocol}//${window.location.host}${i()+s()}`}n.d(t,{S:()=>a})},92059:(e,t,n)=>{n.d(t,{Ny:()=>s.Ny,bm:()=>i.bm,cR:()=>r.cR,e7:()=>i.e7,v:()=>i.v});var r=n(51082),i=n(59679),s=n(70049),a=n(17784)},59679:(e,t,n)=>{n.d(t,{bm:()=>CodeNavigationInfo,e7:()=>f,v:()=>d});var r=n(78212),i=n(89445),s=n(51082),a=n(70049),l=n(17784);let CodeNavigationInfo=class CodeNavigationInfo{initCodeSections(){let e=new Map,t=new Map;for(let n=0;n<this.symbols.length;n++)if(this.symbols[n].lineNumber<this.symbols[n].extent.end.line-2){let r={startLine:this.symbols[n].lineNumber,endLine:this.symbols[n].extent.end.line,index:n,collapsed:!1,level:this.symbols[n].depth};if(e.has(r.startLine)){let t=e.get(r.startLine);t.push(r),e.set(r.startLine,t)}else e.set(r.startLine,[r]);if(e.has(r.endLine)){let t=e.get(r.endLine);t.push(r),e.set(r.endLine,t)}else e.set(r.endLine,[r]);for(let e=r.startLine+1;e<r.endLine;e++)if(t.has(e)){let n=t.get(e);n.push(r),t.set(e,n)}else t.set(e,[r])}this.lineToSectionMap=t,this.codeSections=e}initSymbols(e){return e.map(e=>{let t=this.blobLines[e.identUtf16.start.lineNumber]||"",n=(0,l.sm)(e,t,{stylingDirectives:this.stylingDirectives,repo:this.repo,refInfo:this.refInfo,path:this.path});return this.lineIndexedSymbols[n.lineNumber]=n,n})}getBlobLine(e){return this.blobLines[e]||""}getSymbolOnLine(e){return this.lineIndexedSymbols[e]}initSymbolTree(){if(this.symbols){let e=[],t=this.symbols.filter(e=>"field"!==e.kind.fullName).map(t=>{let n=0;for(let r=e.length-1;r>=0;r--){let i=e[r];if(c(t,i))e.pop();else{n=e.length;break}}return e.push(t),t.setSymbolDepth(n),{symbol:t,depth:n}});this.symbolTree=[];for(let e=0;e<t.length;e++){let n=t[e];if(e+1<t.length){let r=t[e+1];if(r.depth>n.depth){let r=u(t,e);e+=h(r),this.symbolTree.push({symbol:n.symbol,isParent:!0,children:r});continue}}this.symbolTree.push({symbol:n.symbol,isParent:!1,children:[]})}}}createReferences(e){return e.map(e=>{let t=new s.i6({ident:e,repo:this.repo,refInfo:this.refInfo,path:this.path,isPlain:this.isPlain,source:s.sH.BLOB_CONTENT});return t.setSnippet(void 0,this.stylingDirectives?.[e.start.line],this.blobLines[e.start.line],void 0),t})}getReferencesToSymbol(e){let t=d(this.blobLines,(0,a.tb)(e));return this.createReferences(t)}getReferencesToSearch(e){let t=d(this.blobLines,(0,a.Ny)(e));return this.createReferences(t)}getDefinitionsAndReferences(e,t,n){this.setLoading(!0);let r=this.getAlephDefinitions(e,t,n,this.loggedIn),i=this.getAlephReferences(e,t,n,this.loggedIn),a=(async()=>{let[t,n]=await r;if("search"===n){let r=this.getLocalDefinitions(e);r.length>0&&(t=r);let i=t.find(e=>e.path===this.path&&e.repo===this.repo);i&&(t=[i]),n="search"}else{let n=this.getLocalDefinitions(e,!0);for(let r of t)""===r.kind.fullName&&r.name===e&&(r.kind=n[0]?n[0].kind:new s.cR({kind:""}))}return{definitions:t,backend:n}})(),l=(async()=>{let{definitions:t}=await a,n=t.map(e=>e.lineNumber),r=this.getReferencesToSymbol(e).filter(e=>!n.includes(e.lineNumber));return{references:r,backend:"search"}})(),o=(async()=>{let[e,t]=await i;return{references:e,backend:t}})();return{definitions:a,localReferences:l,crossReferences:o,setLoading:this.setLoading}}getLocalDefinitions(e,t=!1){let n=9,r=[];for(let i of this.symbols)i.name===e&&(i.kind.rank<n||t)&&(n=i.kind.rank,r=[i]);return r}async getAlephDefinitions(e,t,n,s){let a,c,u="search";if(""===e&&-1===t&&-1===n||!s)return[[],u];let h=(0,r.UY)({repo:this.repo,type:"definition",q:e,language:this.language,row:t,column:n,ref:this.refInfo.name,path:this.path,codeNavContext:"BLOB_VIEW"});try{a=await (0,i.v)(h)}catch(e){return[[],u]}if(!a.ok)return[[],u];try{c=await a.json()}catch(e){return[[],u]}u=o(c.backend)??"search";let d=c.payload.flatMap(e=>e).map(t=>(0,l.bo)(t,{stylingDirectives:this.stylingDirectives,repo:this.repo,refInfo:this.refInfo,path:this.path,symbol:e,backend:u}));return[d,u]}async getAlephReferences(e,t,n,s){let a,c="search";if(""===e&&-1===t&&-1===n||!s)return[[],c];let u=(0,r.UY)({repo:this.repo,type:"references",q:e,language:this.language,row:t,column:n,ref:this.refInfo.name,path:this.path,codeNavContext:"BLOB_VIEW"}),h=await (0,i.v)(u);if(!h.ok)return[[],c];try{a=await h.json()}catch(e){return[[],c]}c=o(a.backend)??"search";let d=new Set,f=e=>!!d.has(e)||(d.add(e),!1),m=a.payload.flatMap(e=>e).reduce((e,t)=>{if(t.path===this.path)return e;let n=(0,l.Rc)(t,{stylingDirectives:this.stylingDirectives,repo:this.repo,refInfo:this.refInfo,path:this.path,backend:c});return f(n.lineNumber)||e.push(n),e},[]).sort((e,t)=>e.lineNumber-t.lineNumber);return[m,c]}constructor(e,t,n,r,i,s,a,l,o,c){this.lineIndexedSymbols={},this.setLoading=c,this.setLoading(!0),this.repo=e,this.refInfo=t,this.path=n,this.loggedIn=r,this.language=l,this.blobLines=i,this.stylingDirectives=a,this.isPlain=o,this.symbols=this.initSymbols(s),this.initSymbolTree(),this.initCodeSections(),this.setLoading(!1)}};function o(e){switch(e){case"ALEPH_PRECISE":case"ALEPH_PRECISE_PREVIEW":case"ALEPH_PRECISE_DEVELOPMENT":return"precise";case"BLACKBIRD":return"search";default:return null}}function c(e,t){return e.extent.start.line===t.extent.end.line?e.extent.start.column>t.extent.end.column:e.extent.start.line>t.extent.end.line}function u(e,t){let n=[],r=e[t];for(let i=t+1;i<e.length;i++){let t=e[i];if(t.depth>r.depth){let r=u(e,i);i+=h(r),n.push({symbol:t.symbol,children:r,isParent:r.length>0})}else break}return n}function h(e){let t=e.length;for(let n=0;n<e.length;n++){let r=e[n];r.isParent&&(t+=h(r.children))}return t}function d(e,t){let n=[],r=(0,a.Pc)(t,e),i=r.next();for(;!i.done&&n.length<200;){let{column:e,columnEnd:t,line:s}=i.value;n.push({start:{line:s,column:e},end:{line:s,column:t}}),i=r.next()}return n}function f(e,t,n){if(0===e.length)return[];let r=e.length>=200,i={},s=e.reduce((e,r)=>{if(i[r.ident.start.line])return e;i[r.ident.start.line]=!0;let s=(0,a.Pc)(n,[t[r.ident.start.line]]),l=s.next();for(;!l.done&&e.length<200;){let{column:t,columnEnd:n}=l.value;e.push({start:{line:r.ident.start.line,column:t},end:{line:r.ident.start.line,column:n}}),l=s.next()}return e},[]);if(s.length<200&&r){let r=e[e.length-1].ident.start.line,i=(0,a.Pc)(n,t,r),l=i.next();for(;!l.done&&s.length<200;){let{line:e,column:t,columnEnd:n}=l.value;s.push({start:{line:e,column:t},end:{line:e,column:n}}),l=i.next()}}return s}},51082:(e,t,n)=>{n.d(t,{cR:()=>SymbolKind,i6:()=>CodeReference,lt:()=>CodeSymbol,sH:()=>r});var r,i=n(78212);!function(e){e.BLACKBIRD_SEARCH="blackbird-search",e.BLACKBIRD_ANALYSIS="blackbird-analysis",e.ALEPH_PRECISE="aleph-precise",e.BLOB_CONTENT="blob-content-search"}(r||(r={}));let SymbolKind=class SymbolKind{constructor({kind:e}){let t=s(e);this.fullName=t,this.shortName=a(t),this.plColor=l(t),this.rank=o(t)}};function s(e){switch(e){case"SYMBOL_KIND_FILE_DEF":case 12:return"file";case"SYMBOL_KIND_MODULE_DEF":case 4:return"module";case"SYMBOL_KIND_NAMESPACE_DEF":case 13:return"namespace";case"SYMBOL_KIND_PACKAGE_DEF":case 14:return"package";case"SYMBOL_KIND_CLASS_DEF":case 3:return"class";case"SYMBOL_KIND_METHOD_DEF":case 2:return"method";case"SYMBOL_KIND_PROPERTY_DEF":case 15:return"property";case"SYMBOL_KIND_FIELD_DEF":case 9:return"field";case"SYMBOL_KIND_CONSTRUCTOR_DEF":case 16:return"constructor";case"SYMBOL_KIND_ENUM_DEF":case 17:return"enum";case"SYMBOL_KIND_INTERFACE_DEF":case 7:return"interface";case"SYMBOL_KIND_FUNCTION_DEF":case 1:return"function";case"SYMBOL_KIND_VARIABLE_DEF":case 18:return"variable";case"SYMBOL_KIND_CONSTANT_DEF":case 10:return"constant";case"SYMBOL_KIND_STRING_DEF":case 19:return"string";case"SYMBOL_KIND_NUMBER_DEF":case 20:return"number";case"SYMBOL_KIND_BOOLEAN_DEF":case 21:return"boolean";case"SYMBOL_KIND_ARRAY_DEF":case 22:return"array";case"SYMBOL_KIND_OBJECT_DEF":case 23:return"object";case"SYMBOL_KIND_KEY_DEF":case 24:return"key";case"SYMBOL_KIND_NULL_DEF":case 25:return"null";case"SYMBOL_KIND_ENUM_MEMBER_DEF":case 26:return"enum member";case"SYMBOL_KIND_STRUCT_DEF":case 27:return"struct";case"SYMBOL_KIND_EVENT_DEF":case 28:return"event";case"SYMBOL_KIND_OPERATOR_DEF":case 29:return"operator";case"SYMBOL_KIND_TYPE_PARAMETER_DEF":case 30:return"type param";case"SYMBOL_KIND_TYPE_DEF":case 6:return"type";case"SYMBOL_KIND_IMPLEMENTATION_DEF":case 8:return"implementation";case"SYMBOL_KIND_MACRO_DEF":case 11:return"macro";case"SYMBOL_KIND_TRAIT_DEF":case 31:return"trait";case"SYMBOL_KIND_UNION_DEF":case 32:return"union";case"SYMBOL_KIND_SECTION_1_DEF":case 50:return"h1";case"SYMBOL_KIND_SECTION_2_DEF":case 51:return"h2";case"SYMBOL_KIND_SECTION_3_DEF":case 52:return"h3";case"SYMBOL_KIND_SECTION_4_DEF":case 53:return"h4";case"SYMBOL_KIND_SECTION_5_DEF":case 54:return"h5";case"SYMBOL_KIND_SECTION_6_DEF":case 55:return"h6";case"SYMBOL_KIND_CALL_REF":case 100:return"call";case"SYMBOL_KIND_FIELD_REF":case 101:return"field ref";case"SYMBOL_KIND_PROPERTY_REF":case 102:return"property ref";case"SYMBOL_KIND_ENUM_MEMBER_REF":case 103:return"enum member ref";default:return e.toString()}}function a(e){switch(e){case"function":case"method":return"func";case"interface":return"intf";case"implementation":return"impl";case"constant":return"const";case"module":return"mod";case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"class":case"call":case"enum":case"field":case"macro":case"struct":case"trait":case"type":case"union":return e;default:return e.substring(0,1)}}function l(e){switch(e){case"function":case"method":default:return"prettylights.syntax.entity";case"class":case"enum":case"struct":case"union":return"prettylights.syntax.constant";case"interface":case"trait":return"prettylights.syntax.keyword";case"constant":case"field":case"enum member":return"prettylights.syntax.variable";case"implementation":return"prettylights.syntax.string"}}function o(e){return({class:1,struct:1,enum:1,type:2,interface:3,trait:3,module:4,implementation:5,function:6,method:7,call:8,field:9})[e]||9}let CodeSymbol=class CodeSymbol{setSymbolDepth(e){this.depth=e}setFileInfo(e,t,n){this.repo=e,this.refInfo=t,this.path=n}get lineNumber(){return this.ident.start.line+1}setSnippet(e,t,n,r){this.highlightedText=e,this.stylingDirectives=t,this.bodyText=n,this.leadingWhitespace=r}href(){if(!this.repo||!this.refInfo||!this.path)return`/${window.location.pathname}#L${this.lineNumber}`;let e=this.source===r.BLACKBIRD_SEARCH?this.repo.defaultBranch:this.refInfo.name||this.refInfo.currentOid;return(0,i.C9)({owner:this.repo.ownerLogin,repo:this.repo.name,commitish:e,filePath:this.path,lineNumber:this.lineNumber})}pathKey(){return`${this.repo.ownerLogin}/${this.repo.name}/${this.refInfo.currentOid}/${this.path}`}constructor({ident:e,extent:t,kind:n,name:r,fullyQualifiedName:i,source:s}){this.ident=e,this.extent=t,this.kind=new SymbolKind({kind:n}),this.name=r,this.fullyQualifiedName=i,this.source=s}};let CodeReference=class CodeReference{get lineNumber(){return this.ident.start.line+1}href(e){if(!this.repo||!this.refInfo||!this.path)return`/${window.location.pathname}#L${this.lineNumber}`;let t=this.source===r.BLACKBIRD_SEARCH?this.repo.defaultBranch:this.refInfo.name||this.refInfo.currentOid,n={owner:this.repo.ownerLogin,repo:this.repo.name,commitish:t,filePath:this.path,lineNumber:this.lineNumber,plain:this.isPlain?1:void 0};return e?(0,i.t4)(n):(0,i.C9)(n)}setSnippet(e,t,n,r){this.highlightedText=e,this.stylingDirectives=t,this.bodyText=n,this.leadingWhitespace=r}pathKey(){return`${this.repo.ownerLogin}/${this.repo.name}/${this.refInfo.currentOid}/${this.path}`}constructor({ident:e,repo:t,refInfo:n,path:r,isPlain:i,source:s}){this.ident=e,this.repo=t,this.refInfo=n,this.path=r,this.isPlain=i??!1,this.source=s}}},70049:(e,t,n)=>{var r;function i(e){let t=`(\\W|^)${e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(\\W|$)`;return{kind:r.Symbol,regexp:RegExp(t,"g")}}function s(e){let t=e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");return{kind:r.Text,regexp:RegExp(t,"gi")}}function*a(e,t,n=0){for(let i=n;i<t.length;i++){let n;let s=t[i];for(;null!==(n=e.regexp.exec(s));){let t=n[0],a=n.index;e.kind===r.Symbol&&(t.length>0&&/\W/.test(t[0])&&(t=t.substring(1),a+=n[0].length-t.length),t.length>0&&/\W/.test(t[t.length-1])&&(t=t.substring(0,t.length-1))),yield{line:i,column:a,columnEnd:a+t.length,text:s}}}}n.d(t,{Ny:()=>s,Pc:()=>a,tb:()=>i}),function(e){e[e.Text=0]="Text",e[e.Symbol=1]="Symbol"}(r||(r={}))},17784:(e,t,n)=>{n.d(t,{Rc:()=>a,bo:()=>s,sm:()=>i});var r=n(51082);function i(e,t,{stylingDirectives:n,repo:i,refInfo:s,path:a}){let{extentUtf16:l,identUtf16:o}=e,c=new r.lt({kind:e.kind,fullyQualifiedName:e.fullyQualifiedName,name:e.name,extent:{start:{line:l.start.lineNumber,column:l.start.utf16Col},end:{line:l.end.lineNumber,column:l.end.utf16Col}},ident:{start:{line:o.start.lineNumber,column:o.start.utf16Col},end:{line:o.end.lineNumber,column:o.end.utf16Col}},source:r.sH.BLACKBIRD_ANALYSIS});return c.setSnippet(void 0,n?.[o.start.lineNumber],t,void 0),c.setFileInfo(i,s,a),c}function s(e,{symbol:t,refInfo:n,repo:i,path:s,backend:a}){let l=e.ident?.start?.character,o=e.ident?.end?.character??e.ident?.start?.character,c={start:{line:e.ident.start.line,column:l?l-e.leadingWhitespace:0},end:{line:e.ident?.end?.line??e.ident.start.line,column:o?o-e.leadingWhitespace:0}},u=e.extent?.start?.character,h=e.extent?.end?.character??e.extent?.start?.character,d={start:{line:e.extent.start.line,column:u||0},end:{line:e.extent.end?.line??e.extent.start.line,column:h||0}},f=new r.lt({ident:c,extent:d,kind:e.kind||"",name:t,fullyQualifiedName:t,source:"search"===a?r.sH.BLACKBIRD_SEARCH:r.sH.ALEPH_PRECISE});if(e.local)f.setFileInfo(i,n,s);else if(e.commitOid&&e.path){let t=n;e.commitOid!==n.currentOid&&(t={name:"",listCacheKey:e.commitOid,currentOid:e.commitOid,canEdit:!1}),f.setFileInfo(e.repo??i,t,e.path)}let m=Array(e.leadingWhitespace).fill(" ").join(""),p=m+(e.firstLine||"");return f.setSnippet(e.highlightedText,void 0,p,e.leadingWhitespace),f}function a(e,{refInfo:t,path:n,repo:i,backend:s}){let a=t,l=n;!e.local&&e.commitOid&&e.path&&(a=t,l=e.path,e.commitOid!==t.currentOid&&(a={name:"",listCacheKey:e.commitOid,currentOid:e.commitOid,canEdit:!1}));let o=e.ident.start.character,c=e.ident.end?.character,u=new r.i6({repo:i,refInfo:a,path:l,ident:{start:{line:e.ident.start.line,column:o?o-e.leadingWhitespace:0},end:{line:e.ident.end?.line||e.ident.start.line,column:c?c-e.leadingWhitespace:0}},source:"search"===s?r.sH.BLACKBIRD_SEARCH:r.sH.ALEPH_PRECISE}),h=Array(e.leadingWhitespace).fill(" ").join("")+(e.firstLine||"");return u.setSnippet(e.highlightedText,void 0,h,e.leadingWhitespace),u}},24601:(e,t,n)=>{n.d(t,{aJ:()=>D,cI:()=>S,eK:()=>y});var r=n(82918),i=n(49237),s=n(28382),a=n(89359),l=n(68202),o=n(53729),c=n(86283),u=n(46426);let h=!1,d=0,f=Date.now(),m=new Set(["Failed to fetch","NetworkError when attempting to fetch resource."]);function p(e){return e instanceof Error||"object"==typeof e&&null!==e&&"name"in e&&"string"==typeof e.name&&"message"in e&&"string"==typeof e.message}function g(e){try{return JSON.stringify(e)}catch{return"Unserializable"}}function _(e){return!!("AbortError"===e.name||"TypeError"===e.name&&m.has(e.message)||e.name.startsWith("ApiError")&&m.has(e.message))}function y(e,t={}){if((0,u.c)("FAILBOT_HANDLE_NON_ERRORS")){if(!p(e)){if(w(e))return;let n=Error(),r=g(e),i={type:"UnknownError",value:`Unable to report error, due to a thrown non-Error type: ${typeof e}, with value ${r}`,stacktrace:S(n)};E(L(i,t));return}_(e)||E(L(b(e),t))}else _(e)||E(L(b(e),t))}async function E(e){if(!T())return;let t=document.head?.querySelector('meta[name="browser-errors-url"]')?.content;if(t){if(I(e.error.stacktrace)){h=!0;return}d++;try{await fetch(t,{method:"post",body:JSON.stringify(e)})}catch{}}}function b(e){return{type:e.name,value:e.message,stacktrace:S(e)}}function L(e,t={}){return Object.assign({error:e,sanitizedUrl:(0,a.S)()||window.location.href,readyState:document.readyState,referrer:(0,l.wP)(),timeSinceLoad:Math.round(Date.now()-f),user:D()||void 0,bundler:o.A7,ui:Boolean(document.querySelector('meta[name="ui"]'))},t)}function S(e){return(0,s.Q)(e.stack||"").map(e=>({filename:e.file||"",function:String(e.methodName),lineno:(e.lineNumber||0).toString(),colno:(e.column||0).toString()}))}let N=/(chrome|moz|safari)-extension:\/\//;function I(e){return e.some(e=>N.test(e.filename)||N.test(e.function))}function D(){let e=document.head?.querySelector('meta[name="user-login"]')?.content;if(e)return e;let t=(0,r.b)();return`anonymous-${t}`}let O=!1;function T(){return!O&&!h&&d<10&&(0,i.Gb)()}if(c.iG?.addEventListener("pageshow",()=>O=!1),c.iG?.addEventListener("pagehide",()=>O=!0),"function"==typeof BroadcastChannel){let e=new BroadcastChannel("shared-worker-error");e.addEventListener("message",e=>{y(e.data.error)})}let R=["Object Not Found Matching Id","Not implemented on this platform","provider because it's not your default extension"];function w(e){if(!e||"boolean"==typeof e||"number"==typeof e)return!0;if("string"==typeof e){if(R.some(t=>e.includes(t)))return!0}else if("object"==typeof e&&"string"==typeof e.message&&"number"==typeof e.code)return!0;return!1}},7180:(e,t,n)=>{n.d(t,{O:()=>u,d:()=>TrustedTypesPolicyError});var r=n(46426),i=n(71643),s=n(24601),a=n(27856),l=n.n(a),o=n(95253);let TrustedTypesPolicyError=class TrustedTypesPolicyError extends Error{};function c({policy:e,policyName:t,fallback:n,fallbackOnError:a=!1,sanitize:c,silenceErrorReporting:u=!1}){try{if((0,r.c)("BYPASS_TRUSTED_TYPES_POLICY_RULES"))return n;(0,i.b)({incrementKey:"TRUSTED_TYPES_POLICY_CALLED",trustedTypesPolicyName:t},!1,.1);let s=e();return c&&new Promise(e=>{let n=window.performance.now(),r=l().sanitize(s,{FORBID_ATTR:[]}),i=window.performance.now();if(s.length!==r.length){let a=Error("Trusted Types policy output sanitized"),l=a.stack?.slice(0,1e3),c=s.slice(0,250);(0,o.qP)("trusted_types_policy.sanitize",{policyName:t,output:c,stack:l,outputLength:s.length,sanitizedLength:r.length,executionTime:i-n}),e(s)}}),s}catch(e){if(e instanceof TrustedTypesPolicyError||(u||(0,s.eK)(e),(0,i.b)({incrementKey:"TRUSTED_TYPES_POLICY_ERROR",trustedTypesPolicyName:t}),!a))throw e}return n}let u={apply:c}},22490:(e,t,n)=>{n.d(t,{ZO:()=>u});var r=n(86283),i=n(71643);function s(e){return()=>{throw TypeError(`The policy does not implement the function ${e}`)}}let a={createHTML:s("createHTML"),createScript:s("createScript"),createScriptURL:s("createScriptURL")},l={createPolicy:(e,t)=>({name:e,...a,...t})},o=new Map,c=globalThis.trustedTypes??l,u={createPolicy:(e,t)=>{if(o.has(e))return(0,i.b)({incrementKey:"TRUSTED_TYPES_POLICY_INITIALIZED_TWICE"}),o.get(e);{let n=c.createPolicy(e,t);return o.set(e,n),n}}},h=!1;r.n4?.addEventListener("securitypolicyviolation",e=>{"require-trusted-types-for"!==e.violatedDirective||h||(console.warn(`Hi fellow Hubber!
    You're probably seeing a Report Only Trusted Types error near this message. This is intended behaviour, staff-only,
    does not impact application control flow, and is used solely for statistic collection. Unfortunately we
    can't gather these statistics without adding the above warnings to your console. Sorry about that!
    Feel free to drop by #pse-architecture if you have any additional questions about Trusted Types or CSP.`),h=!0)})},89445:(e,t,n)=>{function r(e,t={}){if(e.match(/^(https?:|\/\/)/))throw Error("Can not make cross-origin requests from verifiedFetch");let n={...t.headers,"GitHub-Verified-Fetch":"true","X-Requested-With":"XMLHttpRequest"};return fetch(e,{...t,headers:n})}function i(e,t){let n=t?.headers??{},i={...n,Accept:"application/json","Content-Type":"application/json"},s=t?.body?JSON.stringify(t.body):void 0;return r(e,{...t,body:s,headers:i})}n.d(t,{Q:()=>r,v:()=>i})}}]);
//# sourceMappingURL=app_assets_modules_github_blob-anchor_ts-app_assets_modules_github_filter-sort_ts-app_assets_-681869-688ed387460a.js.map