"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_github_onfocus_ts-app_assets_modules_github_sticky-scroll-into-view_ts"],{70763:(t,e,n)=>{n.d(e,{O4:()=>w,jo:()=>v,Qp:()=>h});var i=n(81574),o=n(59753);let l="ontransitionend"in window;function s(t,e){if(!l){e();return}let n=Array.from(t.querySelectorAll(".js-transitionable"));for(let e of(t.classList.contains("js-transitionable")&&n.push(t),n)){let t=r(e);e instanceof HTMLElement&&(e.addEventListener("transitionend",()=>{e.style.display="",e.style.visibility="",t&&a(e,function(){e.style.height=""})},{once:!0}),e.style.boxSizing="content-box",e.style.display="block",e.style.visibility="visible",t&&a(e,function(){e.style.height=getComputedStyle(e).height}),e.offsetHeight)}for(let t of(e(),n))if(t instanceof HTMLElement&&r(t)){let e=getComputedStyle(t).height;t.style.boxSizing="","0px"===e?t.style.height=`${t.scrollHeight}px`:t.style.height="0px"}}function r(t){return"height"===getComputedStyle(t).transitionProperty}function a(t,e){t.style.transition="none",e(),t.offsetHeight,t.style.transition=""}var c=n(96776);function u(t,e){e.find(e=>{let n=Array.from(t.querySelectorAll(e)),i=n.findLast(t=>"none"!==window.getComputedStyle(t).display);if(i&&document.activeElement!==i)return i.focus(),!0})}function d(t){u(t,[".js-focus-on-dismiss","input[autofocus], textarea[autofocus]"])}function f(t){t.classList.contains("tooltipped")&&(t.classList.remove("tooltipped"),t.addEventListener("mouseleave",()=>{t.classList.add("tooltipped"),t.blur()},{once:!0}))}function p(t){return[...document.querySelectorAll(".js-details-container")].filter(e=>e.getAttribute("data-details-container-group")===t)}function m(t){return[...t.querySelectorAll(".js-details-target")].filter(e=>e.closest(".js-details-container")===t)}function g(t,e){let n=t.getAttribute("data-details-container-group");return n?((0,c.uQ)(t,()=>{for(let i of p(n))i!==t&&y(i,e)}),n):null}function y(t,e){for(let n of(t.classList.toggle("open",e),t.classList.toggle("Details--on",e),m(t)))n.setAttribute("aria-expanded",e.toString())}function h(t,e){let n=t.getAttribute("data-details-container")||".js-details-container",i=t.closest(n),o=e?.force??!i.classList.contains("open"),l=e?.withGroup??!1;s(i,()=>{y(i,o);let e=l?g(i,o):null;Promise.resolve().then(()=>{d(i),f(t),i.dispatchEvent(new CustomEvent("details:toggled",{bubbles:!0,cancelable:!1,detail:{open:o}})),e&&i.dispatchEvent(new CustomEvent("details:toggled-group",{bubbles:!0,cancelable:!1,detail:{open:o,group:e}}))})})}function v(t){let e=t.getAttribute("data-details-container")||".js-details-container",n=t.closest(e),i=n.classList;return i.contains("Details--on")||i.contains("open")}function E(t){let e=t.altKey,n=t.currentTarget;h(n,{withGroup:e}),t.preventDefault()}function w(t){let e=!1,n=t.parentElement;for(;n;)n.classList.contains("Details-content--shown")&&(e=!0),n.classList.contains("js-details-container")&&(n.classList.toggle("open",!e),n.classList.toggle("Details--on",!e),e=!1),n=n.parentElement}(0,o.on)("click",".js-details-target",E),(0,i.Z)(function({target:t}){t&&w(t)})},81574:(t,e,n)=>{n.d(e,{Z:()=>r});var i=n(4412),o=n(68202);let l=[],s=0;function r(t){!async function(){l.push(t),await i.x,a()}()}function a(){let t=s;s=l.length,c(l.slice(t),null,window.location.href)}function c(t,e,n){let i=window.location.hash.slice(1),o=i?document.getElementById(i):null,l={oldURL:e,newURL:n,target:o};for(let e of t)e.call(null,l)}r.clear=()=>{l.length=s=0};let u=window.location.href;window.addEventListener("popstate",function(){u=window.location.href}),window.addEventListener("hashchange",function(t){let e=window.location.href;try{c(l,t.oldURL||u,e)}finally{u=e}});let d=null;document.addEventListener(o.QE.START,function(){d=window.location.href}),document.addEventListener(o.QE.SUCCESS,function(){c(l,d,window.location.href)})},20332:(t,e,n)=>{n.d(e,{h:()=>l});var i=n(59753),o=n(70763);function l(){let t=!1,e=document.getElementById("start-of-content");if(e){let n=e.nextElementSibling;if(n instanceof HTMLElement)return(t="1"===n.getAttribute("data-skipped-to-content"))&&n.removeAttribute("data-skipped-to-content"),t}}(0,i.on)("click",".js-skip-to-content",function(t){let e=document.getElementById("start-of-content");if(e){let t=e.nextElementSibling;t instanceof HTMLElement&&(t.setAttribute("tabindex","-1"),t.setAttribute("data-skipped-to-content","1"),t.focus())}t.preventDefault()});let s="ontouchstart"in document,r=document.querySelectorAll(".js-header-menu-item");for(let t of r)t.addEventListener("details:toggled",t=>{let e=t.target;if(t instanceof CustomEvent&&t.detail.open)for(let t of r)t!==e&&(0,o.Qp)(t,{force:!1})}),s||t.addEventListener("mouseleave",t=>{let e=t.target;e.classList.contains("open")&&(0,o.Qp)(e,{force:!1})});document.addEventListener("context-region-label:update",t=>{if(!(t instanceof CustomEvent&&t.detail.label))return;let e=document.querySelectorAll(".js-context-region-label");for(let n of e)n.textContent=t.detail.label}),document.addEventListener("turbo:before-cache",t=>{for(let e of t.target.querySelectorAll("modal-dialog[open]"))e.close()})},80860:(t,e,n)=>{n.d(e,{H:()=>y});var i=n(20332),o=n(4412),l=n(36071);let s=0,r=new Set;function a(){return s}function c(t){for(let e of(s=t,t?document.body.style.setProperty("--base-sticky-header-height",`${t}px`):document.body.style.removeProperty("--base-sticky-header-height"),r))e(t)}let u=!1,d=!1,f=[];function p(){f.length?m():g()}function m(){u||(window.addEventListener("resize",h),document.addEventListener("scroll",h),u=!0)}function g(){window.removeEventListener("resize",h),document.removeEventListener("scroll",h),u=!1}function y(){v(!0)}function h(){v()}function v(t=!1){for(let e of f)if(e.element.offsetHeight>0){let{element:n,placeholder:i,top:o}=e,l=n.getBoundingClientRect();if(i){let s=i.getBoundingClientRect();n.classList.contains("is-stuck")?s.top>H(n,o)?b(e):L(e):l.top<=H(n,o)?w(e):t&&L(e)}else l.top-H(n,o)<.1?w(e):b(e)}}function E(t){let{position:e}=window.getComputedStyle(t);return/sticky/.test(e)}function w({element:t,placeholder:e,top:n}){if(e){let i=t.getBoundingClientRect();R(t,H(t,n)),t.style.left=`${i.left}px`,t.style.width=`${i.width}px`,t.style.marginTop="0",t.style.position="fixed",e.style.display="block"}t.classList.add("is-stuck")}function b({element:t,placeholder:e}){e&&(t.style.position="static",t.style.marginTop=e.style.marginTop,e.style.display="none"),t.classList.remove("is-stuck")}function L({element:t,placeholder:e,offsetParent:n,top:o}){if(e&&!(0,i.h)()){let i=t.getBoundingClientRect(),l=e.getBoundingClientRect();if(R(t,H(t,o)),t.style.left=`${l.left}px`,0!==l.width&&(t.style.width=`${l.width}px`),n){let e=n.getBoundingClientRect();e.bottom<i.height+parseInt(String(o))&&(t.style.top=`${e.bottom-i.height}px`)}}}function C(t){if(E(t))return null;let e=t.previousElementSibling;if(e&&e.classList.contains("is-placeholder"))return e;let n=document.createElement("div");return n.style.visibility="hidden",n.style.display="none",n.style.height=window.getComputedStyle(t).height,n.className=t.className,n.classList.remove("js-sticky"),n.classList.add("is-placeholder"),t.parentNode.insertBefore(n,t)}function S(t){let e=C(t),n=window.getComputedStyle(t).position;t.style.position="static";let i=t.offsetParent;t.style.position="fixed";let o=B(t),l={element:t,placeholder:e,offsetParent:i,top:"auto"===o?0:parseInt(o||"0")};t.style.position=n,f.push(l)}function k(t){let e=f.map(t=>t.element).indexOf(t);f.splice(e,1)}async function A(t){await o.C,requestAnimationFrame(()=>{t.isConnected&&(S(t),v(),p())}),d||(window.dispatchEvent(new CustomEvent("sticky-header-rendered")),d=!0)}async function x(t){if(null===t.offsetParent)return;await o.C;let e=Math.floor(t.getBoundingClientRect().height);e>0&&(c(e),j(),y())}function j(){for(let t of document.querySelectorAll(".js-position-sticky, .js-notification-shelf-offset-top"))T(t)}function T(t){if(t.classList.contains("js-notification-top-shelf"))return;let e=parseInt(B(t))||0;R(t,e+a())}function B(t){let e=t.getAttribute("data-original-top");if(null!=e)return e;let n=window.getComputedStyle(t).top;return t.setAttribute("data-original-top",n),n}function H(t,e){return t.classList.contains("js-notification-top-shelf")?e:e+a()}function R(t,e){t.style.setProperty("top",`${e}px`,"important")}(0,l.N7)(".js-sticky",{constructor:HTMLElement,add(t){A(t)},remove(t){k(t),p()}}),(0,l.N7)(".js-notification-top-shelf",{constructor:HTMLElement,add(t){x(t)},remove(){a()>0&&(c(0),j(),y())}}),(0,l.N7)(".js-notification-shelf-offset-top, .js-position-sticky",{constructor:HTMLElement,add:T})},87098:(t,e,n)=>{function i(t,e=location.hash){return o(t,l(e))}function o(t,e){return""===e?null:t.getElementById(e)||t.getElementsByName(e)[0]}function l(t){try{return decodeURIComponent(t.slice(1))}catch{return""}}n.d(e,{$z:()=>l,Kt:()=>i,Q:()=>o})},254:(t,e,n)=>{n.d(e,{ZG:()=>r,q6:()=>c,w4:()=>a});var i=n(8439);let o=!1,l=new i.Z;function s(t){let e=t.target;if(e instanceof HTMLElement&&e.nodeType!==Node.DOCUMENT_NODE)for(let t of l.matches(e))t.data.call(null,e)}function r(t,e){o||(o=!0,document.addEventListener("focus",s,!0)),l.add(t,e),document.activeElement instanceof HTMLElement&&document.activeElement.matches(t)&&e(document.activeElement)}function a(t,e,n){function i(e){let o=e.currentTarget;o&&(o.removeEventListener(t,n),o.removeEventListener("blur",i))}r(e,function(e){e.addEventListener(t,n),e.addEventListener("blur",i)})}function c(t,e){function n(t){let{currentTarget:i}=t;i&&(i.removeEventListener("input",e),i.removeEventListener("blur",n))}r(t,function(t){t.addEventListener("input",e),t.addEventListener("blur",n)})}},3126:(t,e,n)=>{n.d(e,{kc:()=>s,lA:()=>r,zT:()=>l});var i=n(87098),o=n(80860);function l(t){if(t.hasAttribute("data-ignore-sticky-scroll"))return;let e=t.ownerDocument;setTimeout(()=>{e&&e.defaultView&&(t.scrollIntoView(),e.defaultView.scrollBy(0,-r(e)))},0)}function s(t){let e=(0,i.Kt)(t);e&&l(e)}function r(t){(0,o.H)();let e=t.querySelectorAll(".js-sticky-offset-scroll"),n=t.querySelectorAll(".js-position-sticky"),i=Math.max(0,...Array.from(e).map(t=>{let{top:e,height:n}=t.getBoundingClientRect();return 0===e?n:0}))+Math.max(0,...Array.from(n).map(t=>{let{top:e,height:n}=t.getBoundingClientRect(),i=parseInt(getComputedStyle(t).top);if(!t.parentElement)return 0;let o=t.parentElement.getBoundingClientRect().top;return e===i&&o<0?n:0})),l=t.querySelectorAll(".js-position-sticky-stacked"),s=Array.from(l).reduce((t,e)=>{let{height:n,top:i}=e.getBoundingClientRect(),o=e.classList.contains("is-stuck");return t+(!(i<0)&&o?n:0)},0);return i+s}}}]);
//# sourceMappingURL=app_assets_modules_github_onfocus_ts-app_assets_modules_github_sticky-scroll-into-view_ts-6d6a4abc12e3.js.map