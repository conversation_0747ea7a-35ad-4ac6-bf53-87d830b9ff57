"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["app_assets_modules_react-code-view_pages_CodeView_tsx"],{68588:(e,t,n)=>{n.d(t,{n:()=>r});function r({appendQuery:e,retainScrollPosition:t,returnTarget:n}){window.dispatchEvent(new CustomEvent("blackbird_monolith_append_and_focus_input",{detail:{appendQuery:e,retainScrollPosition:t,returnTarget:n}}))}},98950:(e,t,n)=>{function r(e){let t=document.createElement("pre");return t.style.width="1px",t.style.height="1px",t.style.position="fixed",t.style.top="5px",t.textContent=e,t}function i(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e.textContent||"");let t=getSelection();if(null==t)return Promise.reject(Error());t.removeAllRanges();let n=document.createRange();return n.selectNodeContents(e),t.addRange(n),document.execCommand("copy"),t.removeAllRanges(),Promise.resolve()}function o(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e);let t=document.body;if(!t)return Promise.reject(Error());let n=r(e);return t.appendChild(n),i(n),t.removeChild(n),Promise.resolve()}n.d(t,{z:()=>o})},67404:(e,t,n)=>{function r(e){return i(e)[0]}function i(e){let t=[];for(let n of o()){let[r,i]=n.trim().split("=");e===r&&void 0!==i&&t.push({key:r,value:i})}return t}function o(){try{return document.cookie.split(";")}catch{return[]}}function a(e,t,n=null,r=!1,i="lax"){let o=document.domain;if(null==o)throw Error("Unable to get document domain");o.endsWith(".github.com")&&(o="github.com");let a="https:"===location.protocol?"; secure":"",s=n?`; expires=${n}`:"";!1===r&&(o=`.${o}`);try{document.cookie=`${e}=${t}; path=/; domain=${o}${s}${a}; samesite=${i}`}catch{}}function s(e,t=!1){let n=document.domain;if(null==n)throw Error("Unable to get document domain");n.endsWith(".github.com")&&(n="github.com");let r=new Date().getTime(),i=new Date(r-1).toUTCString(),o="https:"===location.protocol?"; secure":"",a=`; expires=${i}`;!1===t&&(n=`.${n}`);try{document.cookie=`${e}=''; path=/; domain=${n}${a}${o}`}catch{}}n.d(t,{$1:()=>i,d8:()=>a,ej:()=>r,kT:()=>s})},87098:(e,t,n)=>{function r(e,t=location.hash){return i(e,o(t))}function i(e,t){return""===t?null:e.getElementById(t)||e.getElementsByName(t)[0]}function o(e){try{return decodeURIComponent(e.slice(1))}catch{return""}}n.d(t,{$z:()=>o,Kt:()=>r,Q:()=>i})},31174:(e,t,n)=>{n.d(t,{bx:()=>c,ln:()=>d,tW:()=>u});var r=n(62073),i=n(16685);let o={cursorNavigationHopWordLeft:{hotkey:"Alt+ArrowLeft,Ctrl+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"],modifierRequired:!0},cursorNavigationHopWordRight:{hotkey:"Alt+ArrowRight,Ctrl+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"],modifierRequired:!0},cursorNavigationTopOfPage:{hotkey:"Meta+ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"],modifierRequired:!0},cursorNavigationBottomOfPage:{hotkey:"Meta+ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"],modifierRequired:!0},cursorNavigationEnd:{hotkey:"End,Meta+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["End"]},cursorNavigationHome:{hotkey:"Home,Meta+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["Home"]},cursorNavigationPageUp:{hotkey:"PageUp",useWhileBlobFocused:!0,noModifierHotkey:["PageUp"]},cursorNavigationPageDown:{hotkey:"PageDown",useWhileBlobFocused:!0,noModifierHotkey:["PageDown"]},cursorNavigationArrowDown:{hotkey:"ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"]},cursorNavigationArrowUp:{hotkey:"ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"]},cursorNavigationArrowLeft:{hotkey:"ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"]},cursorNavigationArrowRight:{hotkey:"ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"]},cursorNavigationShiftHopWordLeft:{hotkey:"Alt+Shift+ArrowLeft,Ctrl+Shift+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"],modifierRequired:!0},cursorNavigationShiftHopWordRight:{hotkey:"Alt+Shift+ArrowRight,Ctrl+Shift+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"],modifierRequired:!0},cursorNavigationShiftTopOfPage:{hotkey:"Meta+Shift+ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"],modifierRequired:!0},cursorNavigationShiftBottomOfPage:{hotkey:"Meta+Shift+ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"],modifierRequired:!0},cursorNavigationShiftEnd:{hotkey:"Shift+End,Meta+Shift+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["End"],modifierRequired:!0},cursorNavigationShiftHome:{hotkey:"Shift+Home,Meta+Shift+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["Home"],modifierRequired:!0},cursorNavigationShiftPageUp:{hotkey:"Shift+PageUp",useWhileBlobFocused:!0,noModifierHotkey:["PageUp"],modifierRequired:!0},cursorNavigationShiftPageDown:{hotkey:"Shift+PageDown",useWhileBlobFocused:!0,noModifierHotkey:["PageDown"],modifierRequired:!0},cursorNavigationShiftArrowDown:{hotkey:"Shift+ArrowDown",useWhileBlobFocused:!0,noModifierHotkey:["ArrowDown"],modifierRequired:!0},cursorNavigationShiftArrowUp:{hotkey:"Shift+ArrowUp",useWhileBlobFocused:!0,noModifierHotkey:["ArrowUp"],modifierRequired:!0},cursorNavigationShiftArrowLeft:{hotkey:"Shift+ArrowLeft",useWhileBlobFocused:!0,noModifierHotkey:["ArrowLeft"],modifierRequired:!0},cursorNavigationShiftArrowRight:{hotkey:"Shift+ArrowRight",useWhileBlobFocused:!0,noModifierHotkey:["ArrowRight"],modifierRequired:!0},cursorNavigationHighlightLine:{text:"J",hotkey:"J",useWhileBlobFocused:!0,noModifierHotkey:["J"],modifierRequired:!0},cursorNavigationGoLineUp:{hotkey:"Ctrl+p",useWhileBlobFocused:!0,noModifierHotkey:["p"],modifierRequired:!0},cursorNavigationOpenHelpDialog:{hotkey:"Alt+F1,Control+Alt+\u02D9,Control+Alt+h",useWhileBlobFocused:!0,noModifierHotkey:["F1","h","\u02D9"],modifierRequired:!0},cursorNavigationGoLineDown:{hotkey:"Ctrl+n",useWhileBlobFocused:!0,noModifierHotkey:["n"],modifierRequired:!0},cursorNavigationEnter:{text:"\u2318 Enter",hotkey:"Meta+Enter",useWhileBlobFocused:!0,noModifierHotkey:["Enter"],modifierRequired:!0},cursorNavigationSpace:{hotkey:" ",useWhileBlobFocused:!0,noModifierHotkey:[" "],modifierRequired:!1},cursorNavigationShiftSpace:{hotkey:"Shift+ ",useWhileBlobFocused:!0,noModifierHotkey:[" "],modifierRequired:!0},expandAndFocusLineContextMenu:{text:"Shift Alt C",hotkey:"Alt+C,Alt+\xc7",useWhileBlobFocused:!0,noModifierHotkey:["C"],modifierRequired:!0},copyFilePathShortcut:{text:"\u2318 shift .",hotkey:"Meta+Shift+.",useWhileBlobFocused:!0,noModifierHotkey:["."],modifierRequired:!0},copyPermalinkShortcut:{text:"\u2318 shift ,",hotkey:"Meta+Shift+,",useWhileBlobFocused:!0,noModifierHotkey:[","],modifierRequired:!0},copyRawContentShortcut:{text:"\u2318 shift c",hotkey:"Meta+Shift+c",useWhileBlobFocused:!0,noModifierHotkey:["c"],modifierRequired:!0},downloadRawContentShortcut:{text:"\u2318 shift s",hotkey:"Meta+Shift+s",useWhileBlobFocused:!0,noModifierHotkey:["s"],modifierRequired:!0},editFileShortcut:{hotkey:"e,E",useWhileBlobFocused:!0,noModifierHotkey:["e","E"]},goToLineShortcut:{text:"l",hotkey:"l,L",ariaKeyShortcuts:"l",useWhileBlobFocused:!0,noModifierHotkey:["l","L"]},alternativeGoToLineShortcut:{text:"Control G",hotkey:"Control+g",ariaKeyShortcuts:"Control+g",useWhileBlobFocused:!0,noModifierHotkey:["g","G"]},findInFileShortcut:{hotkey:"Meta+f, F3",text:"\u2318 f",ariaKeyShortcuts:"Meta+F",useWhileBlobFocused:!0,noModifierHotkey:["f","F3"],modifierRequired:!0},findFilesShortcut:{hotkey:"t,T",useWhileBlobFocused:!0,noModifierHotkey:["t","T"]},findSelectionShortcut:{hotkey:"Meta+e",useWhileBlobFocused:!0,noModifierHotkey:["e"],modifierRequired:!0},findNextShortcut:{hotkey:"Control+g, Meta+g"},findPrevShortcut:{hotkey:"Control+Shift+g, Meta+Shift+g"},openWithGitHubDevShortcut:{hotkey:"., Meta+Shift+/",useWhileBlobFocused:!0,noModifierHotkey:["."]},openWithGitHubDevInNewWindowShortcut:{hotkey:"Shift+.,Shift+>,>",useWhileBlobFocused:!0,noModifierHotkey:[">"]},permalinkShortcut:{hotkey:"y,Y",useWhileBlobFocused:!0,noModifierHotkey:["y","Y"]},searchShortcut:{hotkey:"/",useWhileBlobFocused:!0,noModifierHotkey:["/"]},selectAllShortcut:{hotkey:"Meta+a",useWhileBlobFocused:!0,noModifierHotkey:["a"],modifierRequired:!0},selectEditTabShortcut:{hotkey:"Meta+Shift+p, Control+Shift+p"},submitCommitDialogShortcut:{hotkey:"Meta+Enter, Control+Enter"},refSelectorShortcut:{hotkey:"w",text:"w",useWhileBlobFocused:!0,noModifierHotkey:["w"]},escapeRightClickMenu:{hotkey:"Escape",useWhileBlobFocused:!0,noModifierHotkey:["Escape"]},toggleFocusedPaneShortcut:{hotkey:"Meta+F6,Meta+Shift+F6",useWhileBlobFocused:!0,noModifierHotkey:["F6"],modifierRequired:!0},toggleSymbolsShortcut:{hotkey:"Meta+i",useWhileBlobFocused:!0,noModifierHotkey:["i"],modifierRequired:!0},toggleTreeShortcut:{hotkey:"Meta+b",useWhileBlobFocused:!0,noModifierHotkey:["b"],modifierRequired:!0},viewBlameShortcut:{hotkey:"b,B,Meta+/ Meta+b",useWhileBlobFocused:!0,noModifierHotkey:["b"]},viewCodeShortcut:{hotkey:"Meta+/ Meta+c",useWhileBlobFocused:!0,modifierRequired:!0},viewPreviewShortcut:{hotkey:"Meta+/ Meta+p"},viewRawContentShortcut:{text:"\u2318 / \u2318 r",hotkey:"Meta+/ Meta+r",useWhileBlobFocused:!0,noModifierHotkey:["r"],modifierRequired:!0},findSymbolShortcut:{hotkey:"r,R",useWhileBlobFocused:!0,noModifierHotkey:["r","R"],modifierRequired:!1}};function a(e){return Object.keys(e).reduce((t,n)=>{let r=e[n];return t[n]={hotkey:r.hotkey?.replace(/Meta/g,"Control"),text:r.text?.replace(/⌘/g,"Ctrl").replace(/⇧/g,"Shift"),ariaKeyShortcuts:r.ariaKeyShortcuts?.replace(/Meta/g,"Control"),useWhileBlobFocused:r.useWhileBlobFocused,modifierRequired:r.modifierRequired,noModifierHotkey:r.noModifierHotkey},t},{})}function s(e){return Object.keys(e).reduce((t,n)=>{let r=e[n];return t[n]={hotkey:void 0,text:r.text?.replace(/⌘/g,"Ctrl").replace(/⇧/g,"Shift"),ariaKeyShortcuts:r.ariaKeyShortcuts?.replace(/Meta/g,"Control"),useWhileBlobFocused:r.useWhileBlobFocused,modifierRequired:r.modifierRequired,noModifierHotkey:r.noModifierHotkey},t},{})}let l=new Map;function c(){let e=(0,i.f)(["mac"]),[t]=(0,r.D)(()=>!1,!0,[]),n=0;if(e?n=1:t||(n=2),!l.has(n)){let r=o;e||t||(r=a(r)),t&&(r=s(r)),l.set(n,r)}return l.get(n)}function d(){let e=c();return Object.keys(e).reduce((t,n)=>{let r=e[n];if(r.useWhileBlobFocused&&r.noModifierHotkey&&r.modifierRequired)for(let e of r.noModifierHotkey)t.includes(e)||t.push(e);return t},[])}function u(){let e=c();return Object.keys(e).reduce((t,n)=>{let r=e[n];if(r.useWhileBlobFocused&&r.noModifierHotkey&&!r.modifierRequired)for(let e of r.noModifierHotkey)t.includes(e)||t.push(e);return t},[])}},77262:(e,t,n)=>{n.d(t,{G:()=>s});var r=n(46263),i=n(11117),o=n(67294),a=n(89250);function s(e,t=20){let[n,s]=(0,o.useState)(t),l=(0,o.useRef)(t),c=(0,i.O)().codeWrappingOption,d=(0,a.TH)();return(0,o.useLayoutEffect)(()=>{let n=document.getElementById("file-name-id-wide");if(!n)return;let i=new ResizeObserver((0,r.D)(()=>{let n=document.getElementsByClassName(e)[0]?.firstChild?.getBoundingClientRect().height??t;0===n||n===l.current||c.enabled||(s(n),l.current=n)}));return i.observe(n),()=>i.disconnect()},[d.key,c.enabled,e,t]),n}},86480:(e,t,n)=>{n.d(t,{NC:()=>I,O$:()=>y,RP:()=>Z,Sg:()=>p,Tw:()=>N,gk:()=>S,i$:()=>C,jn:()=>x,nJ:()=>k,nj:()=>R});var r=n(77417),i=n(51252),o=n(62073),a=n(11117),s=n(16685),l=n(67294),c=n(89250),d=n(56334),u=n(13816),h=n(73690),m=n(77262),f=n(57941);let p=7.2293,x=92,y=92,g=new r.fA(1),b=new r.fA(0),j=new r.fA(1),w=new r.fA(0),v=new r.fA(!1);function N(){return(0,i.iu)(g)}function C(){return(0,i.iu)(j)}function k(){return(0,i.iu)(v)}function S(){let e=(0,l.useRef)(null);function t(){e.current={start:{line:g.value,column:b.value+1},end:{line:j.value,column:w.value+1}}}return(0,i.mU)(g,t),(0,i.mU)(b,t),(0,i.mU)(j,t),(0,i.mU)(w,t),e}function I(e){v.value=e}function R(e){let t=(0,a.O)().codeWrappingOption.enabled,n=!!(0,h.Q)(),[r]=(0,o.D)(()=>!1,!0,[]);return!t&&!n&&!e&&!r}function Z(e,t,n,r,i,o,a,h,N,C,k,S){let I=(0,l.useRef)(0),R=(0,l.useRef)(0),Z=(0,l.useRef)(0),E=(0,l.useRef)(0),T=(0,l.useRef)(0),L=(0,l.useRef)(0),B=(0,l.useRef)(null),_=(0,l.useRef)(0),D=(0,l.useRef)(0),F=(0,l.useRef)(15),O=(0,l.useRef)(0),P=a?y:x,A=(0,s.f)(["windows"]),M=A?6.6:p,H=(0,m.G)("react-code-lines"),$=(0,l.useMemo)(()=>{let e=[];for(let t=0;t<i.length;t++)0===t?e.push(i[t].rawText?.length??0):e.push((i[t].rawText?.length??0)+e[t-1]+1);return e},[i]);function W(e,t){n(e),L.current=t}function z(e,t){r(e),T.current=t}let U=(0,l.useCallback)(e=>{let t=g.value!==j.value,n={start:{line:g.value,column:t?b.value+1:null},end:{line:j.value,column:t?w.value+1:null}},r={anchorPrefix:"L",blobRange:{start:n.start,end:n.end}},i=(0,d.Dw)(r);window.location.hash=i,h?.(e)},[h]),{hash:G}=(0,c.TH)();function q(e){let t=0,n=0,r=0,i=0,o=0,a=0,s=$.length-1;for(;a<=s;){let l=Math.floor((a+s)/2);if(r=$[l]+1,i=l>0?$[l-1]+1:0,o=l<$.length-1?$[l+1]+1:1/0,e>=i&&e<r){t=l,n=e-i;break}if(e<i)s=l-1;else if(e>=r&&e<o){t=l+1,n=e-r;break}else e>=o&&(a=l+1)}return{line:t,offset:n}}function V(e,t,n,r,i){I.current=e,R.current=n,Z.current=t,E.current=r,T.current=i?e:t,L.current=i?n:r,g.value=eo(e),b.value=n,j.value=eo(t),w.value=r}function K(){return I.current===T.current&&R.current===L.current&&(I.current!==Z.current||R.current!==E.current)?"start":Z.current===T.current&&E.current===L.current&&(I.current!==Z.current||R.current!==E.current)?"end":"same"}function Y(){if(N&&N.current){let e=N.current,t=I.current-1,n=Z.current-1,r=(-1!==t?$[t]+1:0)+R.current,i=(-1!==n?$[n]+1:0)+E.current;e.selectionStart=r,e.selectionEnd=i,D.current=r,O.current=i}}function Q(){en();let e=I.current,t=R.current;e+F.current>i.length?e=i.length-1:e+=F.current,t=ee(e,t),I.current=e,R.current=t,J(),z(e*H,e),er(70)}function X(){en();let e=I.current,t=R.current;e<F.current?e=0:e-=F.current,t=ee(e,t),I.current=e,R.current=t,J(),z(e*H,e),er(70)}function J(){Z.current=I.current,E.current=R.current}function ee(e,t){let n=t;if(e>i.length||!i[e])return n;let r=i[e].rawText;return r&&(t>r.length?(n=r.length,W((0,u.VC)(n,r,C),n)):n<_.current&&_.current<r.length?(n=_.current,W((0,u.VC)(n,r,C),n)):n<_.current&&_.current>=r.length&&(n=r.length,W((0,u.VC)(n,r,C),n))),n}function et(){let e=i[T.current];if(!e)return;let{rawText:n,stylingDirectivesLine:r}=e;if(!n||!r)return;let o=null;for(let e of r){if(e.start>L.current||e.end<L.current||!e.cssClass)continue;o=e;let t=(0,u.yk)(n.substring(e.start,e.end),e.cssClass);if(!t)return}o&&t?.({selectedText:n.substring(o.start,o.end),lineNumber:eo(T.current),offset:o.start})}function en(){B.current||(o?B.current=e.current?.parentElement?.parentElement:B.current=e.current?.parentElement)}function er(e){B.current&&(ei(),function(e){let t=K(),n=R.current;"end"===t&&(n=E.current),B.current&&B.current.scrollBy&&(n*M+P+50>=B.current.scrollLeft+B.current.clientWidth?B.current.scrollBy(n*M+P-B.current.scrollLeft-B.current.clientWidth+e,0):n*M+P<=B.current.scrollLeft&&B.current.scrollBy(n*M+P-B.current.scrollLeft-B.current.clientWidth,0))}(e)),Y()}function ei(){let e=K(),t=R.current,n=I.current;"end"===e&&(t=E.current,n=Z.current);let r=Math.min(eo(n+5),i.length);if(!(0,u.nB)(r)){let e=(0,u.Hm)(r);null===e&&window.scrollTo(0,n*H),e&&e.getBoundingClientRect().y<0||e&&e.getBoundingClientRect().y>window.innerHeight?(e.scrollIntoView({block:"center"}),window.scrollBy(-300,0)):e&&window.scrollBy(0,100);let i=window.innerWidth;i<t*M+P&&window.scrollTo(0,0)}let o=Math.max(eo(n-5),1);if((0,u.nB)(o))n<=7&&window.scrollTo(0,0);else{let e=(0,u.Hm)(o);null===e&&window.scrollTo(0,n*H),e&&e.getBoundingClientRect().y<0||e&&e.getBoundingClientRect().y>window.innerHeight?(e.scrollIntoView({block:"center"}),window.scrollBy(-300,0)):e&&window.scrollBy(0,-200)}}function eo(e){return i[e]?i[e].lineNumber:e}return(0,l.useEffect)(()=>{let e=window.innerHeight-200>300?window.innerHeight-200:300;F.current=Math.min(Math.max(Math.round(e/H),1),100)},[H]),(0,l.useEffect)(()=>{k&&""!==k&&(k.includes("PageUp")?X():k.includes("PageDown")&&Q())},[k]),(0,l.useEffect)(()=>{if(!S||S.start<0&&S.end<0||D.current===S.start&&O.current===S.end)return;let e=q(S.start),t=q(S.end);(D.current!==S.start||O.current===S.end)&&(S.keyboard||S.displayStart)?(W((0,u.VC)(e.offset,i[e.line]?.rawText??"",C),e.offset),_.current=e.offset,z(e.line*H,e.line),V(e.line,t.line,e.offset,t.offset,!0)):(W((0,u.VC)(t.offset,i[t.line]?.rawText??"",C),t.offset),_.current=t.offset,z(t.line*H,t.line),V(e.line,t.line,e.offset,t.offset,!1)),O.current=S.end,D.current=S.start,S.end!==S.start||S.keyboard||(v.value=!1,et()),T.current<=5&&S.keyboard&&ei()},[i,C,S]),(0,l.useEffect)(()=>{let e=(0,d.n6)(G);if(!e.blobRange?.start?.line||e.blobRange.start.line>i.length)return;let t=e.blobRange.start.line-1;I.current=t,g.value=t,b.value=0,j.value=t,w.value=0,R.current=0,Z.current=t,E.current=0,_.current=0,W(R.current,R.current),z(I.current*H,I.current),er(70)},[G,i,n,r]),{onEnter:function(){v.value=!0,(0,f.gZ)(),et()},updateUrlForLineNumber:U,onPageDown:Q,onPageUp:X,currentStartLine:I,currentEndLine:Z,determineAndSetTextAreaCursorPosition:Y,getCorrectLineNumberWithCollapsedSections:eo}}},57941:(e,t,n)=>{n.d(t,{Sl:()=>o,eS:()=>s,gZ:()=>a});var r=n(67294);let i="react_blob_view_focus_symbol_pane";function o(e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]),(0,r.useEffect)(()=>{let t=t=>{e(t.detail?.focusSymbolSearch||!1)};return window.addEventListener(i,t),()=>{window.removeEventListener(i,t)}},[e])}function a(){window.dispatchEvent(new CustomEvent(i))}function s(){window.dispatchEvent(new CustomEvent(i,{detail:{focusSymbolSearch:!0}}))}},90606:(e,t,n)=>{n.d(t,{$w:()=>l,XT:()=>s,_X:()=>a,kl:()=>c,kq:()=>d});var r=n(15345),i=n(67294),o=n(13816);let a="highlighted-line-menu-positioner",s=25;function l({lineData:e,onLineStickOrUnstick:t,onMenuClose:n,onCollapseToggle:a,setOpen:s}){function l(e,t=!1){s(e),n&&!e&&n(e,t)}let c=(0,i.useCallback)(()=>{if(!e)return;let{lineNumber:n,ownedSection:r}=e;r&&(r.collapsed=!1),a?.(),(0,o.yw)(n),t?.(e,!0)},[e,a,t]),d=(0,i.useCallback)(()=>{if(!e)return;let{lineNumber:t,ownedSection:n}=e;n&&(n.collapsed=!0),a?.(),(0,o.rH)(t)},[e,a]);return{setShouldBeOpen:l,expandOrCollapseSection:function(){if(!e)return;let{ownedSection:t}=e;t&&(t.collapsed?(c(),(0,o.dM)("Code section expanded")):(d(),(0,o.dM)("Code section collapsed"))),l(!1,!0)},openUpRefSelector:function(){let e=document.getElementsByClassName("ref-selector-class");e&&1===e.length?(e[0]?.click(),(0,r.x)("ref selector opened")):e&&2===e.length&&(e[1]?.click(),(0,r.x)("ref selector opened")),l(!1)}}}function c(e,t={x:0,y:0}){let n=document.getElementById(a);if(!e||!n)return{display:"none"};let{top:r,left:i,height:o}=e.getBoundingClientRect(),{top:l,left:c}=n.getBoundingClientRect();return{top:`${r-l-(s-o)/2+t.y}px`,left:`${Math.max(i-c+t.x,0)-13}px`}}function d(e){let t=e?.textContent??"";if(""!==t)return t;if(e){let n=e.childNodes;for(let e=0;e<n.length;e++){let r=n[e];if(r){let e=r.getAttribute("data-code-text");null===e&&(e=d(r)),t+=e}}}return t}},5262:(e,t,n)=>{n.d(t,{a:()=>d});var r=n(32769),i=n(78212),o=n(2048),a=n(53664),s=n(89445),l=n(67294),c=n(90874);function d(){let{sendAnalyticsEvent:e}=(0,a.z)(),t=u(),n=(0,o.y)("code_nav_ui_events");return{sendRepoClickEvent:(0,l.useCallback)((r,i={})=>{e("repository.click",r,i),n&&t(r,"click",i)},[e,t,n]),sendRepoKeyDownEvent:(0,l.useCallback)((r,i={})=>{e("repository.keydown",r,i),n&&t(r,"keydown",i)},[e,t,n]),sendStats:(0,l.useCallback)((r,i={})=>{e(r,"",i),n&&t(r,"stats",i)},[e,t,n]),sendMarketplaceActionEvent:(0,l.useCallback)((t,n={})=>{e("marketplace.action.click",t,n)},[e])}}function u(){let e=m(),t=(0,r.H)(),n=(0,i.Mf)(i.V6,{owner:t.ownerLogin,repo:t.name});return(0,l.useCallback)((t,r,i)=>{let o={target:t,interaction:r,context:i,...e,...h()};(0,s.v)(n,{method:"POST",body:o})},[e,n])}function h(){return{url:window.location.href,user_agent:window.navigator.userAgent,browser_width:window.innerWidth,browser_languages:window.navigator.languages.join(",")}}function m(){let e=(0,r.H)(),t=(0,c.x)();return(0,l.useMemo)(()=>({react_app:"code-view",repository_id:e.id,repository_nwo:`${e.ownerLogin}/${e.name}`,repository_public:e.public,repository_is_fork:e.isFork,actor_id:t?.id,actor_login:t?.login}),[e,t])}},8903:(e,t,n)=>{n.d(t,{v:()=>a,z:()=>o});var r=n(67294);let i="react_blob_view_scroll_line_into_view";function o(e){let t=(0,r.useRef)(e);(0,r.useEffect)(()=>{t.current=e},[e]),(0,r.useEffect)(()=>{let e=e=>t.current(e.detail);return window.addEventListener(i,e),()=>{window.removeEventListener(i,e)}},[])}function a(e){window.dispatchEvent(new CustomEvent(i,{detail:e}))}},20286:(e,t,n)=>{n.d(t,{TZ:()=>i,ik:()=>c,nx:()=>d});var r=n(67294);let i="repos-sticky-header",o="code_view_update_sticky_header_height_event",a=null,s=null,l=null;function c(e){e&&s!==e&&(s=e,null===a?a=new ResizeObserver(e=>{for(let t of e)t.contentRect.height!==l&&(l=t.contentRect.height,window.dispatchEvent(new CustomEvent(o,{detail:t.contentRect.height})))}):a.disconnect(),a.observe(e))}function d(){let[e,t]=(0,r.useState)(void 0);return((0,r.useEffect)(()=>{function e(e){t(e.detail)}return window.addEventListener(o,e),s&&c(s),()=>{window.removeEventListener(o,e)}},[]),void 0!==e)?e:102}},63372:(e,t,n)=>{n.d(t,{V:()=>o,X:()=>i});var r=n(67294);function i(e){let[t,n]=(0,r.useState)(!1),i=(0,r.useCallback)(e=>{let r=e[e.length-1],i=r.intersectionRatio<1;i!==t&&n(i)},[t,n]);return(0,r.useEffect)(()=>{let t=e.current,n=new IntersectionObserver(i,{threshold:[1],rootMargin:"-1px 0px 0px 0px"});return e.current&&n.observe(e.current),()=>{t&&n.unobserve(t)}},[e,i]),t}function o(){let e=r.useMemo(()=>({top:"0px",zIndex:1,background:"var(--color-canvas-default)",position:"sticky"}),[!0]);return e}},35499:(e,t,n)=>{n.d(t,{B:()=>l});var r=n(32769),i=n(78212),o=n(62073),a=n(67294),s=n(80624);function l(){let e=(0,r.H)(),{path:t,action:n,refInfo:l}=(0,s.Br)(),[c]=(0,o.D)(()=>!1,!0,[]),d=a.useCallback(t=>(0,i.Qi)({repo:e,commitish:l.name,action:"directory"===t.contentType?"tree":"blob",path:t.path}),[e.ownerLogin,e.name,l.name]);function u(e){return e?`?${e}`:""}function h(e){return c?"":void 0===e?window.location.hash:e?`#${e}`:""}return{getItemUrl:d,getUrl(r={}){let o=(0,i.Qi)({repo:e,commitish:r.commitish||l.name,action:r.action||n,path:r.path||t})+function({params:e,hash:t}){return u(e)+h(t)}(r);return r.absolute?new URL(o,window.location.origin).href:o},createPermalink(r={}){let o=(0,i.Qi)({repo:e,commitish:l.currentOid,action:r.action||n,path:r.path||t})+function({params:e,hash:t}){return u(e)+h(t)}(r);return r.absolute?new URL(o,window.location.origin).href:o},isCurrentPagePermalink:()=>!c&&l.name===l.currentOid&&window.location.pathname.includes(l.currentOid)}}},13816:(e,t,n)=>{n.d(t,{BS:()=>Z,Bx:()=>E,DD:()=>y,H1:()=>m,Hm:()=>I,KG:()=>c,PO:()=>f,TX:()=>p,VC:()=>C,Yo:()=>j,bP:()=>k,d5:()=>u,dM:()=>L,jP:()=>N,nB:()=>S,rH:()=>b,yk:()=>v,yw:()=>g});var r=n(77417),i=n(51252),o=n(56334),a=n(13570),s=n(86480);let l="collapse-show-rows-styles",c="read-only-cursor-text-area";function d(e,t){return document.querySelector(`#${e}LC${t}`)}function u(e,t){return document.querySelector(`main #${(0,a.o)(e,t)}`)}let h=new r.n7;function m(e){return(0,i.iu)(h.has(e))}function f(e){return(0,i.mU)(h,e)}function p(e,t,n,r){if(!r)return"";let i="";for(let t=0;t<e.length;t++)i+=`${x(e[t].startLine)} `;if(n&&r.has(t)){let e=r.get(t);if(e)for(let t=0;t<e.length;t++)i+=`${x(e[t].startLine)} `}return i}function x(e){return`child-of-line-${e}`}function y(e,t,n,r){if(!n)return;let i=n.get(t);if(i)for(let n of i){let i=e.get(n.endLine);if(i)for(let e of i)t>e.lineNumber&&r(e,!1)}}function g(e){w(e,!1),h.delete(e)}function b(e){w(e,!0),h.add(e)}function j(){let e=document.getElementById(l);e&&(e.textContent=""),h.clear()}function w(e,t){let n=`.${x(e)} { display: none; } `;if(document.getElementById(l)){let e=document.getElementById(l);if(t)e.textContent+=n;else{let t=e?.textContent||"";t=t.replace(n,""),e.textContent=t}}else{let e=document.createElement("style");e.id=l,e.textContent=n,document.head.appendChild(e)}}function v(e,t){if(e.length<3)return!1;let n=t.split(" "),r=n.includes("pl-ent")?/\n|\s|[();&.=,]/:/\n|\s|[();&.=",]/;return!(e.match(r)||n.includes("pl-c")||n.includes("pl-k"))}function N(e,t){let n=null,r=null,i=null,o=t;if(e.parentElement?.classList.contains("react-file-line"))n=e.parentElement.getAttribute("data-line-number"),r=e.parentElement,i=e;else if(e.parentElement?.parentElement?.classList.contains("react-file-line"))n=e.parentElement.parentElement.getAttribute("data-line-number"),r=e.parentElement.parentElement,i=e.parentNode;else{if(!e.parentElement?.firstElementChild?.classList.contains("react-file-line")||!(n=e.parentElement.firstElementChild.getAttribute("data-line-number"))||!parseInt(n,10))return;return{line:parseInt(n,10)-1,column:null}}if(n&&parseInt(n,10)){for(let e of r.childNodes){if(e===i)break;o+=e.textContent?.length||0}return{line:parseInt(n,10),column:0!==o?o+1:null}}}function C(e,t,n){let r=document.createElement("div");r.style.position="absolute",r.style.visibility="hidden",r.style.fontFamily="ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace",r.style.fontSize="12px",r.style.lineHeight="20px",r.style.whiteSpace="pre",r.style.tabSize=n.toString(),r.textContent=t.slice(0,e),document.body.appendChild(r);let i=r.clientWidth;return(document.body.removeChild(r),0===i&&0!==e)?e*s.Sg:i}function k(e,t,n,r,i){if(e?.start.line===n&&null!==e?.start.column){let a=(0,o.M9)({start:e.start,end:{line:e.start.line,column:e.end.line===n?e.end.column:null}},()=>t);if(a&&a.startContainer.parentElement){let r=a?.getBoundingClientRect().x-t.getBoundingClientRect().x;return{offset:r+10,width:e.end.line===n?a.getBoundingClientRect().width:void 0}}if(i){let t=C(e.start.column-1,i,r),o=e.end.line===n?e.end.column:null;return{offset:t+20,width:e.end.line===n?C(o?o-1:i.length-1,i,r)-t:void 0}}}else if(e?.end.line===n&&null!==e?.end.column){let n=(0,o.M9)({start:{line:e.end.line,column:0},end:e.end},()=>t);return n?{width:n.getBoundingClientRect().width+10}:{width:C(e.end.column-1,i,r)+10}}}function S(e){let t=d("",e);return R(t)}function I(e){return d("",e)}function R(e){if(!e)return!1;let t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)}function Z(e,t,n){return Math.floor((e-t)/n)+1}function E(e,t){let n=0,r=t.length-1;for(;n<=r;){let i=Math.floor((n+r)/2),o=t[i];if(!o)break;if(o.lineNumber===e)return i;o.lineNumber<e?n=i+1:r=i-1}return -1}function T(){let e=document.createElement("div");e.classList.add("sr-only"),e.id="screenReaderAnnouncementDiv",e.setAttribute("role","alert"),e.setAttribute("aria-live","assertive"),document.body.appendChild(e)}function L(e,t=0){let n=document.getElementById("screenReaderAnnouncementDiv");if(n||T(),!(n=document.getElementById("screenReaderAnnouncementDiv")))return;let r=n.textContent===e?`${e}\u00A0`:e;setTimeout(()=>{n&&(n.textContent=r)},t)}},21913:(e,t,n)=>{n.d(t,{A:()=>r});let r={"&:hover:not([disabled])":{textDecoration:"none"},"&:focus:not([disabled])":{textDecoration:"none"},"&:active:not([disabled])":{textDecoration:"none"}}},43811:(e,t,n)=>{n.d(t,{V:()=>WebWorker});let WebWorker=class WebWorker{set onmessage(e){this.worker.onmessage=e}postMessage(e){this.worker.postMessage(e)}terminate(){this.worker.terminate()}constructor(e,t){try{this.worker=new Worker(e)}catch(e){console.warn("Web workers are not available. Please enable web workers to benefit from the improved performance."),this.worker=new MainThreadWorker(t)}}};let MainThreadWorker=class MainThreadWorker{async postMessage(e){if(this.terminated)return;let t={data:this.job({data:e})};this.onmessage?.(t)}terminate(){this.terminated=!0}constructor(e){this.job=e,this.terminated=!1}}},22125:(e,t,n)=>{n.d(t,{A:()=>a});var r=n(67294),i=n(78720);let o=e=>e;function a(e){let t=r.useRef({}),n=r.useRef({}),a=(e,n)=>{t.current[e](n)},s=r.useRef(a);s.current=a;let l=r.useRef(new ResizeObserver(e=>{for(let t of e){let e=t.target,n="data-key",r=e.getAttribute(n);if(null===r)throw Error(`Value not found, for '${n}' attribute`);s.current(r,e)}}));r.useEffect(()=>{let e=l.current;return()=>{e.disconnect()}},[]);let{size:c,keyExtractor:d=o}=e,u=r.useMemo(()=>{let e=e=>t=>{n.current[e]&&l.current.unobserve(n.current[e]),t&&(s.current(e,t),l.current.observe(t)),n.current[e]=t},t={};for(let n=0;n<c;n++){let r=d(n);t[r]=e(r)}return t},[c,d]),h=(0,i.o)(e),m=h.virtualItems.map(e=>(t.current[e.key]=e.measureRef,{...e,measureRef:u[e.key]}));return{...h,virtualItems:m}}},15387:(e,t,n)=>{n.d(t,{F:()=>o});var r=n(67294),i=n(78720);function o({scrollToFn:e,horizontal:t,parentRef:n,useVirtualImpl:o=i.o,...s}){let l=(0,r.useRef)(window),c=(0,r.useCallback)(()=>{let e=n.current?.getBoundingClientRect(),r=e?.top??0,i=e?.left??0;return t?-1*i:-1*r},[t,n]),d=(0,r.useCallback)(e=>{let r=(n.current?.getBoundingClientRect().top??0)+window.scrollY,i=e+r;l.current?.scroll({top:t?0:i,left:t?i:0})},[t,n]);return o({...s,horizontal:t,parentRef:n,scrollToFn:e||d,onScrollElement:l,scrollOffsetFn:c,useObserver:()=>a(l)})}function a(e){let[t,n]=(0,r.useState)({height:0,width:0}),i=e.current;return(0,r.useLayoutEffect)(()=>{if(!i)return;let e=()=>{let e={height:i.innerHeight,width:i.innerWidth};n(t=>t.height!==e.height||t.width!==e.width?e:t)};return e(),i.addEventListener("resize",e),()=>{i.removeEventListener("resize",e)}},[i]),t}},64937:(e,t,n)=>{n.d(t,{IQ:()=>r,Ju:()=>i,iF:()=>o});let r=e=>{if(e){if(e.startsWith("refs/tags/"))return"tag";if(e.startsWith("refs/heads/"))return"branch"}},i=(e,t)=>"branch"===t?`refs/heads/${e}`:"tag"===t?`refs/tags/${e}`:e,o=e=>{if(!e)return;let t=r(e);if(!t)return e;let[,,...n]=e.split("/");return n.join("/")}},69202:(e,t,n)=>{let r;n.d(t,{G:()=>k});var i=n(21461);let AliveSession=class AliveSession extends i.a2{getUrlFromRefreshUrl(){return o(this.refreshUrl)}constructor(e,t,n,r){super(e,()=>this.getUrlFromRefreshUrl(),n,r),this.refreshUrl=t}};async function o(e){let t=await a(e);return t&&t.url&&t.token?s(t.url,t.token):null}async function a(e){let t=await fetch(e,{headers:{Accept:"application/json"}});if(t.ok)return t.json();if(404===t.status)return null;throw Error("fetch error")}async function s(e,t){let n=await fetch(e,{method:"POST",mode:"same-origin",headers:{"Scoped-CSRF-Token":t}});if(n.ok)return n.text();throw Error("fetch error")}var l=n(46263),c=n(4412),d=n(44544),u=n(22490),h=n(71643),m=n(7180);let f="alive";let InvalidSourceRelError=class InvalidSourceRelError extends m.d{};let p=u.ZO.createPolicy(f,{createScriptURL:e=>m.O.apply({policy:()=>{if(!(0,h.B)())return e;if(!e.startsWith("/"))throw new InvalidSourceRelError("Alive worker src URL must start with a slash");return e},policyName:f,fallback:e,fallbackOnError:!0})});function x(){return"SharedWorker"in window&&"true"!==(0,d.Z)("localStorage").getItem("bypassSharedWorker")}function y(){let e=document.head.querySelector("link[rel=shared-web-socket-src]")?.getAttribute("href")??"";try{return p.createScriptURL(e)}catch(e){if(e instanceof InvalidSourceRelError)return null;throw e}}function g(){return document.head.querySelector("link[rel=shared-web-socket]")?.href??null}function b(){return document.head.querySelector("link[rel=shared-web-socket]")?.getAttribute("data-refresh-url")??null}function j(){return document.head.querySelector("link[rel=shared-web-socket]")?.getAttribute("data-session-id")??null}function w(e,{channel:t,type:n,data:r}){for(let i of e)i.dispatchEvent(new CustomEvent(`socket:${n}`,{bubbles:!1,cancelable:!1,detail:{name:t,data:r}}))}let v=class AliveSessionProxy{subscribe(e){let t=this.subscriptions.add(...e);t.length&&this.worker.port.postMessage({subscribe:t});let n=new Set(t.map(e=>e.name)),r=e.reduce((e,t)=>{let r=t.topic.name;return(0,i.A)(r)&&!n.has(r)&&e.add(r),e},new Set);r.size&&this.worker.port.postMessage({requestPresence:Array.from(r)})}unsubscribeAll(...e){let t=this.subscriptions.drain(...e);t.length&&this.worker.port.postMessage({unsubscribe:t});let n=this.presenceMetadata.removeSubscribers(e);this.sendPresenceMetadataUpdate(n)}updatePresenceMetadata(e){let t=new Set;for(let n of e)this.presenceMetadata.setMetadata(n),t.add(n.channelName);this.sendPresenceMetadataUpdate(t)}sendPresenceMetadataUpdate(e){if(!e.size)return;let t=[];for(let n of e)t.push({channelName:n,metadata:this.presenceMetadata.getChannelMetadata(n)});this.worker.port.postMessage({updatePresenceMetadata:t})}online(){this.worker.port.postMessage({online:!0})}offline(){this.worker.port.postMessage({online:!1})}hangup(){this.worker.port.postMessage({hangup:!0})}receive(e){let{channel:t}=e;if("presence"===e.type){let n=this.notifyPresenceDebouncedByChannel.get(t);n||(n=(0,l.D)((e,n)=>{this.notify(e,n),this.notifyPresenceDebouncedByChannel.delete(t)},100),this.notifyPresenceDebouncedByChannel.set(t,n)),n(this.subscriptions.subscribers(t),e);return}this.notify(this.subscriptions.subscribers(t),e)}constructor(e,t,n,r,o){this.subscriptions=new i.SubscriptionSet,this.presenceMetadata=new i.ah,this.notifyPresenceDebouncedByChannel=new Map,this.notify=o,this.worker=new SharedWorker(e,`github-socket-worker-v2-${r}`),this.worker.port.onmessage=({data:e})=>this.receive(e),this.worker.port.postMessage({connect:{url:t,refreshUrl:n}})}};async function N(){let e=y();if(!e)return;let t=g();if(!t)return;let n=b();if(!n)return;let r=j();if(!r)return;let i=(()=>{if(x())try{return new v(e,t,n,r,w)}catch(e){}return new AliveSession(t,n,!1,w)})();return window.addEventListener("online",()=>i.online()),window.addEventListener("offline",()=>i.offline()),window.addEventListener("pagehide",()=>{"hangup"in i&&i.hangup()}),i}async function C(){return await c.x,N()}function k(){return r||(r=C())}},83991:(e,t,n)=>{n.d(t,{s:()=>i});var r=n(21461);function i(e,t,n){if(!e)throw Error("Not connected to alive");if(!t)throw Error("No channel name");let i=r.Topic.parse(t);if(!i)throw Error("Invalid channel name");let o={subscriber:{dispatchEvent:e=>{if(e instanceof CustomEvent){let t=e.detail;n(t.data)}}},topic:i};return e.subscribe([o]),{unsubscribe:()=>e.unsubscribeAll(o.subscriber)}}},99550:(e,t,n)=>{n.d(t,{l:()=>o,p:()=>i});let r=!0;function i(e){r=e}function o(){return r}},57446:(e,t,n)=>{var r,i;function o(e){return"blob"in e}function a(e){return o(e)&&"blame"in e}function s(e){return"deleteInfo"in e&&"webCommitInfo"in e}function l(e){return"editInfo"in e&&"webCommitInfo"in e}function c(e){return"overview"in e}function d(e){return"tree"in e}n.d(t,{yY:()=>r,kl:()=>i,K$:()=>a,Kg:()=>o,XU:()=>s,OH:()=>l,uF:()=>c,g6:()=>d}),function(e){e.FALSE_POSITIVE="false_positive",e.USED_IN_TESTS="used_in_tests",e.WILL_FIX_LATER="will_fix_later"}(r||(r={})),function(e){e.README="readme",e.CODE_OF_CONDUCT="code_of_conduct",e.LICENSE="license",e.SECURITY="security"}(i||(i={}))},14744:(e,t,n)=>{n.d(t,{D:()=>N,C:()=>O});var r,i,o,a,s,l,c,d,u,h,m,f=n(85893),p=n(57294),x=n(78212),y=n(42483),g=n(73290),b=n(97011);function j(e){return e.path?.startsWith("/apps/")??!1}var w=n(38490);function v({renderTooltip:e,author:t,children:n}){return!1===e?(0,f.jsx)(f.Fragment,{children:n}):(0,f.jsx)(w.Z,{"aria-label":`commits by ${t.login}`,direction:"se",children:n})}try{(r=v).displayName||(r.displayName="AuthorTooltip")}catch{}function N({author:e,repo:t,avatarSize:n,sx:r={},includeTooltip:i=!0}){if(!e)return null;let o=(0,f.jsx)(p.O,{"aria-label":`${e.login||"author"}`,src:e.avatarUrl,alt:`${e.login||"author"}`,sx:{mr:2},size:n,square:j(e)});return(0,f.jsxs)(y.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",...r},"data-testid":"author-avatar",children:[e.path?(0,f.jsx)(g.Z,{href:e.path,"data-testid":"avatar-icon-link","data-hovercard-url":e.login?(0,x.zP)({owner:e.login}):void 0,children:o}):o,e.login?(0,f.jsx)(v,{author:e,renderTooltip:i,children:(0,f.jsx)(g.Z,{muted:!0,href:(0,x.OI)({repo:t,author:e.login}),"aria-label":`commits by ${e.login}`,sx:{fontWeight:600,whiteSpace:"nowrap",color:"fg.default","&:hover":{color:"fg.default",textDecoration:"underline"}},children:e.login})}):(0,f.jsx)(b.Z,{sx:{fontWeight:600,whiteSpace:"nowrap",color:"fg.default"},children:e.displayName})]})}try{(i=N).displayName||(i.displayName="AuthorAvatar")}catch{}var C=n(67294),k=n(52516),S=n(79902),I=n(66280);function R({authors:e,repo:t}){let n=e.length,[r,i]=(0,C.useState)(!1),o=(0,C.useRef)(null);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsxs)(g.Z,{as:"button","aria-label":`Show ${n} authors`,"data-testid":"authors-dialog-anchor",onClick:()=>{i(!0)},sx:{mx:1},ref:o,muted:!0,children:[n," ","people"]}),r&&(0,f.jsx)(I.V,{title:`${n} authors`,onClose:()=>{i(!1),setTimeout(()=>o.current?.focus(),25)},width:"medium",height:n>=12?"small":"auto",renderBody:()=>(0,f.jsx)(k.S,{sx:{overflowY:"auto",py:2},"data-testid":"contributor-dialog-list",children:e.map((e,n)=>(0,f.jsx)(Z,{author:e,repo:t},`${e.login}_${n}`))})})]})}function Z({author:e,repo:t}){return(0,f.jsxs)(k.S.LinkItem,{sx:{display:"flex",flexDirection:"row",fontSize:1,py:2,color:"fg.default","&:hover":{backgroundColor:"canvas.subtle"}},"data-testid":"contributor-dialog-row",href:(0,x.OI)({repo:t,author:e.login??""}),children:[(0,f.jsx)(p.O,{src:e.avatarUrl,alt:e.login??e.displayName,sx:{mr:2},"aria-hidden":"true",square:j(e)}),(0,f.jsx)(S.Z,{inline:!0,title:e.login??e.displayName??"",children:e.login??e.displayName})]})}try{(o=R).displayName||(o.displayName="AuthorsDialog")}catch{}try{(a=Z).displayName||(a.displayName="AuthorRow")}catch{}var E=n(90836);function T({authors:e,avatarSize:t}){return(0,f.jsx)(E.Z,{children:e.slice(0,5).map((e,n)=>(0,f.jsx)(p.O,{"data-testid":"commit-stack-avatar",src:e.avatarUrl,alt:e.login??e.displayName,"data-hovercard-url":(0,x.zP)({owner:e.login??""}),square:j(e),size:t},`${e.login}_${n}`))})}try{(s=T).displayName||(s.displayName="CommitAuthorStack")}catch{}function L({author:e,repo:t,sx:n={},includeTooltip:r=!0}){return e?(0,f.jsx)(y.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",...n},"data-testid":"author-link",children:e.login?(0,f.jsx)(v,{author:e,renderTooltip:r,children:(0,f.jsx)(g.Z,{muted:!0,href:(0,x.OI)({repo:t,author:e.login}),"aria-label":`commits by ${e.login}`,sx:{fontWeight:600,whiteSpace:"nowrap",color:"fg.default","&:hover":{color:"fg.default",textDecoration:"underline"}},children:e.login})}):(0,f.jsx)(b.Z,{sx:{fontWeight:600,whiteSpace:"nowrap",color:"fg.default"},children:e.displayName})}):null}try{(l=L).displayName||(l.displayName="AuthorLink")}catch{}function B({author:e,repo:t,avatarSize:n,includeTooltip:r}){return(0,f.jsx)(N,{author:e,repo:t,sx:{px:1},avatarSize:n,includeTooltip:r})}function _({author:e,committer:t,repo:n,avatarSize:r,includeTooltip:i}){return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(T,{authors:[e,t],avatarSize:r}),(0,f.jsx)(L,{author:e,repo:n,sx:{px:1},includeTooltip:i})]})}function D({authors:e,repo:t,avatarSize:n,includeTooltip:r}){return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(T,{authors:e,avatarSize:n}),e.map((n,i)=>(0,f.jsxs)(C.Fragment,{children:[(0,f.jsx)(L,{author:n,repo:t,sx:{px:1},includeTooltip:r}),i!==e.length-1&&" and "]},`${n.login}_${i}`))]})}function F({authors:e,repo:t,avatarSize:n}){return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(T,{authors:e,avatarSize:n}),(0,f.jsx)(R,{authors:e,repo:t})]})}function O({authors:e,committer:t,committerAttribution:n,repo:r,avatarSize:i,includeVerbs:o=!0,includeAuthorTooltip:a=!0}){let s=1===e.length&&!n,l=1===e.length&&n,c=2===e.length&&!n;return(0,f.jsxs)(y.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center"},children:[s&&(0,f.jsx)(B,{author:e[0],repo:r,avatarSize:i,includeTooltip:a}),l&&(0,f.jsx)(_,{author:e[0],committer:t,repo:r,avatarSize:i,includeTooltip:a}),c&&(0,f.jsx)(D,{authors:e,repo:r,avatarSize:i,includeTooltip:a}),!s&&!l&&!c&&(0,f.jsx)(F,{authors:e,repo:r,avatarSize:i}),n?(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("span",{children:o?"authored and":"and"}),(0,f.jsx)(L,{author:t,repo:r,sx:{px:1},includeTooltip:a}),(0,f.jsx)("span",{children:o&&"committed"})]}):(0,f.jsx)("span",{children:o&&"committed"})]})}try{(c=B).displayName||(c.displayName="SingleAuthor")}catch{}try{(d=_).displayName||(d.displayName="AuthorAndCommitter")}catch{}try{(u=D).displayName||(u.displayName="TwoAuthors")}catch{}try{(h=F).displayName||(h.displayName="MultipleAuthors")}catch{}try{(m=O).displayName||(m.displayName="CommitAttribution")}catch{}},4751:(e,t,n)=>{n.d(t,{AF:()=>S,vC:()=>R,fQ:()=>T});var r,i,o,a,s,l=n(85893),c=n(85529),d=n(98833),u=n(97011),h=n(42483),m=n(50919),f=n(67294),p=n(52516),x=n(74121),y=n(66280),g=n(57294),b=n(38490),j=n(73290);function w({checkRun:e}){let{icon:t,iconColor:n}=v(e.icon),r="in_progress"===e.state;return(0,l.jsxs)(h.Z,{"data-testid":"check-run-item",sx:{display:"flex",borderBottomWidth:"1px",borderBottomStyle:"solid",borderBottomColor:"border.default",backgroundColor:"canvas.subtle",height:"38px",py:2,pr:3,pl:"12px",alignItems:"baseline"},children:[r?N():(0,l.jsx)(d.Z,{icon:t,sx:{color:n,margin:"0px 7px",alignSelf:"center"}}),(0,l.jsx)(b.Z,{"aria-label":e.avatarDescription,direction:"e",children:(0,l.jsx)(j.Z,{href:e.avatarUrl,sx:{mr:2},children:(0,l.jsx)(g.O,{square:!0,src:e.avatarLogo,sx:{backgroundColor:e.avatarBackgroundColor}})})}),(0,l.jsxs)(u.Z,{sx:{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",fontSize:"13px",color:"fg.muted"},children:[(0,l.jsxs)(u.Z,{sx:{fontWeight:"bold",color:"fg.default",mr:"2px"},children:[e.name," "]}),e.pending?(0,l.jsx)(u.Z,{sx:{fontStyle:"italic"},children:e.additionalContext}):e.additionalContext,e.description&&(0,l.jsxs)(u.Z,{children:[" ","- ",e.pending?(0,l.jsx)(u.Z,{sx:{fontStyle:"italic"},children:e.description}):e.description]})]}),(0,l.jsx)(j.Z,{href:e.targetUrl,sx:{pl:"12px",fontSize:"13px",marginLeft:"auto"},children:"Details"})]})}function v(e){switch(e){case"check":return{icon:c.CheckIcon,iconColor:"success.fg"};case"dot-fill":return{icon:c.DotFillIcon,iconColor:"attention.fg"};case"stop":return{icon:c.StopIcon,iconColor:"muted.fg"};case"issue-reopened":return{icon:c.IssueReopenedIcon,iconColor:"muted.fg"};case"clock":return{icon:c.ClockIcon,iconColor:"attention.fg"};case"square-fill":return{icon:c.SquareFillIcon,iconColor:"fg.default"};case"skip":return{icon:c.SkipIcon,iconColor:"muted.fg"};case"alert":return{icon:c.AlertIcon,iconColor:"danger.fg"};default:return{icon:c.XIcon,iconColor:"danger.fg"}}}function N(){return(0,l.jsx)(h.Z,{sx:{height:"16px",width:"16px",minWidth:"16px",alignSelf:"center",mx:"7px"},children:(0,l.jsxs)("svg",{fill:"none",viewBox:"0 0 16 16",className:"anim-rotate","aria-hidden":"true",role:"img",children:[(0,l.jsx)("path",{opacity:".5",d:"M8 15A7 7 0 108 1a7 7 0 000 14v0z",stroke:"#dbab0a",strokeWidth:"2"}),(0,l.jsx)("path",{d:"M15 8a7 7 0 01-7 7",stroke:"#dbab0a",strokeWidth:"2"}),(0,l.jsx)("path",{d:"M8 12a4 4 0 100-8 4 4 0 000 8z",fill:"#dbab0a"})]})})}try{(r=w).displayName||(r.displayName="CheckRunItem")}catch{}function C({checkRuns:e}){return(0,l.jsx)(h.Z,{sx:{display:"flex",flexDirection:"column",maxHeight:"230px",overflow:"auto"},children:e.map((e,t)=>(0,l.jsx)(w,{checkRun:e},t))})}try{(i=C).displayName||(i.displayName="ChecksStatusBadgeFooter")}catch{}function k({checksHeaderState:e}){switch(e){case"SUCCEEDED":return(0,l.jsx)(u.Z,{sx:{fontWeight:"bold",fontSize:2},children:"All checks have passed"});case"FAILED":return(0,l.jsx)(u.Z,{sx:{color:"checks.donutError",fontWeight:"bold",fontSize:2},children:"All checks have failed"});case"PENDING":return(0,l.jsx)(u.Z,{sx:{color:"checks.donutPending",fontWeight:"bold",fontSize:2},children:"Some checks haven\u2019t completed yet"});default:return(0,l.jsx)(u.Z,{sx:{color:"checks.donutError",fontWeight:"bold",fontSize:2},children:"Some checks were not successful"})}}try{(o=k).displayName||(o.displayName="HeaderState")}catch{}function S(e){let{combinedStatus:t,isOpen:n,rounded:r=!1,onDismiss:i}=e,o=t?(0,l.jsx)(k,{checksHeaderState:t.checksHeaderState}):"Loading...";return n?(0,l.jsx)(y.V,{onClose:i,sx:{overflowY:"auto",backgroundColor:"canvas.default",boxShadow:"none",...r?{border:"1px solid",borderColor:"border.default",borderBottom:0}:{borderRadius:0}},title:o,subtitle:t?t.checksStatusSummary:void 0,width:"xlarge",renderBody:()=>(0,l.jsx)(y.V.Body,{sx:{padding:0},children:(0,l.jsx)(p.S,{sx:{padding:0},children:t?(0,l.jsx)(C,{checkRuns:t.checkRuns}):(0,l.jsx)(h.Z,{sx:{display:"flex",justifyContent:"center",p:2},children:(0,l.jsx)(x.Z,{size:"medium"})})})})}):null}try{(a=S).displayName||(a.displayName="CheckStatusDialog")}catch{}let I={success:{circled:c.CheckCircleIcon,filled:c.CheckCircleFillIcon,default:c.CheckIcon,color:"checks.donutSuccess"},pending:{circled:c.CircleIcon,filled:c.DotFillIcon,default:c.DotFillIcon,color:"checks.donutPending"},error:{circled:c.XCircleIcon,filled:c.XCircleFillIcon,default:c.XIcon,color:"checks.donutError"}};function R(e){let{statusRollup:t,combinedStatus:n,variant:r="default",disablePopover:i,size:o="medium",descriptionText:a="",rounded:s=!1}=e,[c,p]=(0,f.useState)(!1),x=(0,f.useRef)(null),y=I[t],{icon:g,iconColor:b}={icon:y?.[r]||I.error[r],iconColor:y?.color||I.error.color};return i?(0,l.jsxs)("span",{"data-testid":"checks-status-badge-icon-only",children:[(0,l.jsx)(d.Z,{icon:g,"aria-label":"See all checks",sx:{color:b}}),a&&(0,l.jsxs)(u.Z,{children:[" ",a]})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(h.Z,{onClick:()=>{p(!0),e.onWillOpenPopup},onMouseEnter:e.onWillOpenPopup,children:(0,l.jsx)(m.h,{"data-testid":"checks-status-badge-icon",icon:g,variant:"invisible",size:o,"aria-label":n?.checksStatusSummary??t,sx:{py:0,px:0,mr:2,svg:{color:b},":hover:not([disabled])":{bg:"pageHeaderBg"}},ref:x})}),a&&(0,l.jsxs)(u.Z,{children:[" ",a]}),c&&(0,l.jsx)(S,{combinedStatus:n,isOpen:c,onDismiss:()=>{p(!1),x.current?.focus()},rounded:s})]})}try{(s=R).displayName||(s.displayName="ChecksStatusBadge")}catch{}var Z=n(78212),E=n(89445);function T(e,t){let[n,r]=(0,f.useState)(),[i,o]=(0,f.useState)(),a=(0,f.useCallback)(async()=>{if(i!==e&&(o(e),r(void 0),e)){let n=(0,Z.S$)(t,e),i=await (0,E.v)(n);r(await i.json())}},[e,i,t]);return[n,a]}},1640:(e,t,n)=>{n.d(t,{L4:()=>r,YZ:()=>o,cw:()=>i,d0:()=>a});let OpenCopilotChatEvent=class OpenCopilotChatEvent extends Event{constructor(e){super("open-copilot-chat",{bubbles:!1,cancelable:!0}),this.payload=e}};let AddCopilotChatReferenceEvent=class AddCopilotChatReferenceEvent extends Event{constructor(e){super("add-copilot-chat-reference",{bubbles:!1,cancelable:!0}),this.reference=e}};function r(e){window.dispatchEvent(new OpenCopilotChatEvent(e))}function i(e){window.dispatchEvent(new AddCopilotChatReferenceEvent(e))}function o(e){return window.addEventListener("open-copilot-chat",e),()=>{window.removeEventListener("open-copilot-chat",e)}}function a(e){return window.addEventListener("add-copilot-chat-reference",e),()=>{window.removeEventListener("add-copilot-chat-reference",e)}}},68912:(e,t,n)=>{n.d(t,{m:()=>x,z:()=>f});var r,i,o=n(85893),a=n(37169),s=n(85529),l=n(38490),c=n(42483),d=n(50919),u=n(67294);function h(e){let t=document.createElement("pre");return t.style.width="1px",t.style.height="1px",t.style.position="fixed",t.style.top="5px",t.textContent=e,t}function m(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e.textContent||"");let t=getSelection();if(null==t)return Promise.reject(Error());t.removeAllRanges();let n=document.createRange();return n.selectNodeContents(e),t.addRange(n),document.execCommand("copy"),t.removeAllRanges(),Promise.resolve()}function f(e){if("clipboard"in navigator)return navigator.clipboard.writeText(e);let t=document.body;if(!t)return Promise.reject(Error());let n=h(e);return t.appendChild(n),m(n),t.removeChild(n),Promise.resolve()}function p({sx:e,tooltipProps:t}){return(0,o.jsx)(l.Z,{"aria-label":"Copied!",sx:e,...t,children:(0,o.jsx)(c.Z,{as:"span",sx:{display:"inline-block",color:"success.fg",p:1,mr:1},children:(0,o.jsx)(s.CheckIcon,{})})})}function x({icon:e=s.CopyIcon,size:t="medium",onCopy:n,sx:r,textToCopy:i,tooltipProps:c,confirmationComponent:h=(0,o.jsx)(p,{sx:r,tooltipProps:c}),ariaLabel:m,accessibleButton:x}){let[y,g]=u.useState(!1),b=(0,a.Z)(),j=()=>{g(!0),f(i),n?.(),setTimeout(()=>b()&&g(!1),2e3)},w=m??`Copy ${i} to clipboard`;return y?(0,o.jsx)(o.Fragment,{children:h}):(0,o.jsx)(l.Z,{"aria-label":w,...c,children:(0,o.jsx)(d.h,{"aria-label":w,icon:e,variant:"invisible",size:t,tabIndex:!1===x?-1:0,sx:{...r},onClick:j})})}try{(r=p).displayName||(r.displayName="CopyConfirmationCheck")}catch{}try{(i=x).displayName||(i.displayName="CopyToClipboardButton")}catch{}},17920:(e,t,n)=>{n.d(t,{eE:()=>a});var r=n(86283);let i={Android:"Android",iOS:"iOS",macOS:"macOS",Windows:"Windows",Linux:"Linux",Unknown:"Unknown"};function o(){let e=i.Unknown,t=!1;if(r.iG){let n=r.iG.navigator,o=n.userAgent,a=n?.userAgentData?.platform||n.platform;-1!==["Macintosh","MacIntel","MacPPC","Mac68K","macOS"].indexOf(a)?e=i.macOS:-1!==["iPhone","iPad","iPod"].indexOf(a)?e=i.iOS:-1!==["Win32","Win64","Windows","WinCE"].indexOf(a)?e=i.Windows:/Android/.test(o)?e=i.Android:/Linux/.test(a)&&(e=i.Linux),t=n?.userAgentData?.mobile??(e===i.Android||e===i.iOS)}return{os:e,isAndroid:e===i.Android,isIOS:e===i.iOS,isMacOS:e===i.macOS,isWindows:e===i.Windows,isLinux:e===i.Linux,isDesktop:e===i.macOS||e===i.Windows||e===i.Linux,isMobile:t}}function a(){return o().isMacOS}},95628:(e,t,n)=>{n.d(t,{M:()=>o});let r=e=>{let t=getComputedStyle(e,null);return["overflow","overflow-y","overflow-x"].some(e=>{let n=t.getPropertyValue(e);return"auto"===n||"scroll"===n})},i=(e,t)=>e&&null!==e.parentNode?i(e.parentNode,t.concat([e])):t;function o(e){if(!(e instanceof HTMLElement||e instanceof SVGElement))return;let t=i(e.parentNode,[]);for(let e of t)if((e instanceof HTMLElement||e instanceof SVGElement)&&r(e))return e;return document.scrollingElement||document.documentElement}},78806:(e,t,n)=>{n.d(t,{Z:()=>i});let r=(e,t)=>{let n=new URL(e,window.location.origin),r=new URL(t,window.location.origin),i=r.href.includes("#");return i&&n.host===r.host&&n.pathname===r.pathname&&n.search===r.search},i=r},77417:(e,t,n)=>{function r(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function i(e,t){return t.get?t.get.call(e):t.value}function o(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}}function a(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function s(e,t){var n=a(e,t,"get");return i(e,n)}function l(e,t,n){r(e,t),t.set(e,n)}function c(e,t,n){var r=a(e,t,"set");return o(e,r,n),n}n.d(t,{fA:()=>ObservableValue,n7:()=>ObservableSet,vP:()=>ObservableMap});var d=new WeakMap;let u=class ObservableBase{subscribe(e){return s(this,d).add(e),()=>{s(this,d).delete(e)}}notify(e){for(let t of s(this,d))t(e)}constructor(){l(this,d,{writable:!0,value:new Set})}};var h=new WeakMap;let ObservableValue=class ObservableValue extends u{get value(){return s(this,h)}set value(e){s(this,h)!==e&&(c(this,h,e),this.notify(e))}constructor(e){super(),l(this,h,{writable:!0,value:void 0}),c(this,h,e)}};var m=new WeakMap,f=new WeakMap;let ObservableSet=class ObservableSet extends u{get value(){return s(this,m)}has(e){if(!s(this,f).has(e)){let t=new ObservableValue(s(this,m).has(e));s(this,f).set(e,t)}return s(this,f).get(e)}add(e){s(this,m).has(e)||(s(this,m).add(e),s(this,f).has(e)&&(s(this,f).get(e).value=!0),this.notify(s(this,m)))}delete(e){s(this,m).has(e)&&(s(this,m).delete(e),s(this,f).has(e)&&(s(this,f).get(e).value=!1),this.notify(s(this,m)))}clear(){if(0!==s(this,m).size){for(let e of(s(this,m).clear(),s(this,f).values()))e.value=!1;this.notify(s(this,m))}}constructor(...e){super(),l(this,m,{writable:!0,value:void 0}),l(this,f,{writable:!0,value:new Map}),c(this,m,new Set(...e))}};var p=new WeakMap,x=new WeakMap,y=new WeakMap;let ObservableMap=class ObservableMap extends u{get value(){return s(this,p)}has(e){if(!s(this,x).has(e)){let t=new ObservableValue(s(this,p).has(e));s(this,x).set(e,t)}return s(this,x).get(e)}get(e){if(!s(this,y).has(e)){let t=new ObservableValue(s(this,p).get(e));s(this,y).set(e,t)}return s(this,y).get(e)}set(e,t){s(this,p).get(e)!==t&&(s(this,p).set(e,t),s(this,x).has(e)&&(s(this,x).get(e).value=!0),s(this,y).has(e)&&(s(this,y).get(e).value=t),this.notify(s(this,p)))}delete(e){s(this,p).has(e)&&(s(this,p).delete(e),s(this,x).has(e)&&(s(this,x).get(e).value=!1),s(this,y).has(e)&&(s(this,y).get(e).value=void 0),this.notify(s(this,p)))}clear(){if(0!==s(this,p).size){for(let e of(s(this,p).clear(),s(this,x).values()))e.value=!1;for(let e of s(this,y).values())e.value=void 0;this.notify(s(this,p))}}constructor(...e){super(),l(this,p,{writable:!0,value:void 0}),l(this,x,{writable:!0,value:new Map}),l(this,y,{writable:!0,value:new Map}),c(this,p,new Map(...e))}}},2048:(e,t,n)=>{n.d(t,{g:()=>i,y:()=>o});var r=n(17891);let i=()=>r.M()?.enabled_features??{},o=e=>!!i()[e]},51252:(e,t,n)=>{n.d(t,{D_:()=>c,Kq:()=>a,iu:()=>l,mU:()=>s,yL:()=>o});var r=n(67294),i=n(77417);function o(e){let t=(0,r.useRef)(new i.fA(e));return t.current}function a(...e){let t=(0,r.useRef)(new i.vP(...e));return t.current}function s(e,t){let n=(0,r.useRef)(t);n.current=t,(0,r.useEffect)(()=>e.subscribe(e=>n.current(e)),[e])}function l(e){let[t,n]=(0,r.useState)(e.value);return s(e,e=>n(e)),t}function c(e,t){let n=o(t(e.value));return s(e,e=>{n.value=t(e)}),n}},75809:(e,t,n)=>{n.d(t,{f:()=>r});let r=e=>({"data-testid":e})},20684:(e,t,n)=>{n.d(t,{o:()=>x});var r,i,o=n(67294),a=n(37169),s=n(69202),l=n(83991),c=n(85893);let d=(0,o.createContext)(null),u=null;function h({children:e,initialMessages:t}){return(0,o.useEffect)(()=>{if(t)for(let[e,n]of t)setTimeout(()=>{m(e,n)},0);return()=>{u=null}}),(0,c.jsx)(d.Provider,{value:p,children:e})}function m(e,t){if(null===u)throw Error('Test helper `dispatchAliveTestMessage` called outside `AliveTestProvider`. Please wrap your component under test in `AliveTestProvider` from "@github-ui/use-alive/test-utils".');let n=Array.from(u.subscribers(e));for(let e of n)e(t)}function f(){return(0,o.useContext)(d)}async function p(e,t){let{SubscriptionSet:r,Topic:i}=await Promise.resolve().then(n.bind(n,21461)),o=i.parse(e);if(!o)throw Error('Invalid channel name. Did you forget to sign it with `signChannel("channel-name")`?');return u||(u=new r),u.add({topic:o,subscriber:t}),{unsubscribe:()=>{u?.delete({topic:o,subscriber:t})}}}try{(r=d).displayName||(r.displayName="AliveTestContext")}catch{}try{(i=h).displayName||(i.displayName="AliveTestProvider")}catch{}function x(e,t){let n=(0,a.Z)(),r=f();(0,o.useEffect)(()=>{let i=()=>{},o=!1;return async function(){if("function"==typeof r){let n=await r(e,t);n&&(i=n.unsubscribe);return}try{let r=await (0,s.G)();if(o)return;let a=(0,l.s)(r,e,t);a?.unsubscribe&&(n()?i=a.unsubscribe:a.unsubscribe())}catch(e){console.error(e)}}(),()=>{o=!0,i()}},[e,t,n,r])}},53664:(e,t,n)=>{n.d(t,{w:()=>s,z:()=>a});var r=n(67294),i=n(95253),o=n(87487);function a(){let e=(0,r.useContext)(o.f);if(!e)throw Error("useAnalytics must be used within an AnalyticsContext");let{appName:t,category:n,metadata:a}=e;return{sendAnalyticsEvent:(0,r.useCallback)((e,r,o={})=>{let s={react:!0,app_name:t,category:n,...a};(0,i.qP)(e,{...s,...o,target:r})},[t,n,a])}}function s(){let{sendAnalyticsEvent:e}=a();return{sendClickAnalyticsEvent:(0,r.useCallback)((t={})=>{e("analytics.click",void 0,t)},[e])}}},6582:(e,t,n)=>{n.d(t,{o:()=>a});var r=n(78212),i=n(89445),o=n(67294);function a(e,t,n,a){let[s,l]=(0,o.useState)({loading:!0}),c=t&&e&&n&&a?(0,r.Qi)({repo:{name:t,ownerLogin:e},commitish:n,action:"file-contributors",path:a}):null;return(0,o.useEffect)(()=>{if(!c)return;let e=!1,t=async()=>{l({loading:!0});let t=await (0,i.v)(c);if(!e)try{t.ok?l({contributors:await t.json()}):l({error:!0})}catch(e){l({error:!0})}};return t(),function(){e=!0}},[c]),s}},88455:(e,t,n)=>{n.d(t,{F:()=>i});var r=n(73968);function i(e,t){let{csrf_tokens:n}=(0,r.T)();return n?.[e]?.[t]}},37169:(e,t,n)=>{n.d(t,{Z:()=>o});var r=n(78249),i=n(67294);function o(){let e=(0,i.useRef)(!1),t=(0,i.useCallback)(()=>e.current,[]);return(0,r.g)(()=>(e.current=!0,()=>{e.current=!1}),[]),t}},16685:(e,t,n)=>{n.d(t,{f:()=>i});var r=n(62073);function i(e){let t=e.join(","),[n]=(0,r.D)(()=>{let e=o();return!!e&&t.includes(e)},!1,[t]);return n}function o(){return/Windows/.test(navigator.userAgent)?"windows":/Macintosh/.test(navigator.userAgent)?"mac":null}},64077:(e,t,n)=>{n.d(t,{F:()=>u,f:()=>h});var r=n(15205),i=n(78212),o=n(68202),a=n(89445),s=n(67294);async function l(e){let t=await (0,a.v)(e);return t.ok?await t.json():void 0}let c=new Map,d=(0,r.Z)(l,{cache:c});function u(){c.clear()}function h(e,t,n,r){let[a,l]=(0,s.useState)(),[c,h]=(0,s.useState)(!1),[m,f]=(0,s.useState)(!0),p=t&&e&&n&&r?(0,i.Qi)({repo:{name:t,ownerLogin:e},commitish:n,action:"latest-commit",path:r}):null;return(0,s.useEffect)(()=>(document.addEventListener(o.QE.START,u),()=>{document.removeEventListener(o.QE.START,u)})),(0,s.useEffect)(()=>{let e=!1,t=async()=>{if(!p)return;h(!1),f(!0),l(void 0);let t=await d(p);if(!e){try{t?l(t):h(!0)}catch(e){h(!0)}f(!1)}};return t(),function(){e=!0}},[p,n]),[a,m,c]}},68203:(e,t,n)=>{n.d(t,{s:()=>c});var r=n(67294),i=n(89250),o=n(12599),a=n(78806),s=n(45055),l=n(68202);let c=()=>{let{routes:e,history:t}=r.useContext(s.I),c=(0,i.s0)();return r.useCallback((r,i)=>{let s=(0,o.i3)(r).pathname,d=!(0,o.fp)(e,s);if(d){let e=t.createHref(r);(async()=>{let{softNavigate:t}=await Promise.all([n.e("vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js"),n.e("ui_packages_soft-navigate_soft-navigate_ts")]).then(n.bind(n,75198));t(e)})()}else{(0,a.Z)(location.href,r.toString())||(0,l.LD)("react"),c(r,i);let{turbo:e,...t}=window.history.state;window.history.replaceState({...t,skipTurbo:!0},"",location.href)}},[t,c,e])}},22554:(e,t,n)=>{n.d(t,{I:()=>s});var r,i=n(85893),o=n(67294),a=n(53467);function s({children:e}){let t=(0,a.n)();return t?(0,i.jsx)(i.Fragment,{children:e}):null}try{(r=s).displayName||(r.displayName="AllShortcutsEnabled")}catch{}},85844:(e,t,n)=>{n.d(t,{y:()=>N});var r,i,o=n(85893),a=n(32769),s=n(49713),l=n(75809),c=n(53664),d=n(89445),u=n(85529),h=n(89042),m=n(42483),f=n(2708),p=n(78912),x=n(62719),y=n(75308),g=n(97011),b=n(73290),j=n(67294),w=n(90874);let v={ORG_ADMIN:`For an organization, developers writing less boilerplate code means more productivity, while learning
  new technologies means delivering better customers solutions. Try it in Codespaces or your file editor.`,ORG_MEMBER:`We noticed that you're personally paying for GitHub Copilot. Instead, ask your organization admin
  to purchase the business version of GitHub Copilot.`,STANDARD:`Spend less time creating boilerplate and repetitive code patterns, and more time building great software.
  Try it in Codespaces or your favorite file editor.`},N=({view:e,copilotInfo:t,className:n})=>{let{documentationUrl:r,notices:i,userAccess:N}=t??{},{business:C,orgHasCFBAccess:k,userHasCFIAccess:S,userHasOrgs:I,userIsOrgAdmin:R,userIsOrgMember:Z,featureRequestInfo:E}=N??{},{codeViewPopover:T}=i??{},{sendClickAnalyticsEvent:L}=(0,c.w)(),B=(0,w.x)(),{isOrgOwned:_,ownerLogin:D}=(0,a.H)(),{inProgress:F,requested:O,toggleFeatureRequest:P}=(0,s.mG)(E),[A,M]=(0,j.useState)(!1),[H,$]=(0,j.useState)(!1),W=(0,j.useCallback)(()=>M(!0),[M]),z=(0,j.useCallback)(()=>M(!1),[M]),U=(0,j.useCallback)(()=>N&&_&&Z&&!R&&(!k||S)?"Your organization can pay for GitHub Copilot":"Code 55% faster with GitHub Copilot",[k,_,N,S,R,Z]),G=()=>{if(N&&_){if(R)return v.ORG_ADMIN;if(Z&&S)return v.ORG_MEMBER}return v.STANDARD},q=()=>E?.showFeatureRequest?O?(0,o.jsx)(s.VH,{inProgress:F,toggleFeatureRequest:P}):(0,o.jsx)(s.Bb,{inProgress:F,toggleFeatureRequest:P,featureName:E?.featureName}):null,V=()=>B&&D===B.login?"owner":R?"admin":Z?"member":"personal",K=()=>{B&&L({category:"copilot_popover_code_view",action:`click_to_open_popover_${e}`,label:`ref_cta:open_copilot_popover;owner:${D};relationship:${V()}`})},Y=(t,n)=>{L({category:"copilot_popover_code_view",action:t,label:`ref_cta:${n};ref_loc:code_view_${e}`})},Q=()=>{let t=`${_?"org_":""}code_view_${e}${R?"_org_admin":""}`;L({category:"copilot_popover_code_view",action:"click_to_dismiss_copilot_popover_forever",label:`ref_cta:dont_show_again;ref_loc:${t}`})},X=()=>{let e=N?.userHasOrgs??!1;L({category:"copilot_popover_code_view",action:`click_to_go_to_copilot_for_${e?"business":"individuals"}_info`,label:"ref_cta:learn_more;ref_loc:code_view"})},J=()=>{let e=!!C,t=e||S;return t||_&&(!_||Z)?Z&&!k&&R?(0,o.jsx)(h.Q,{type:"button",href:`/github-copilot/business_signup/organization/payment?org=${D}`,onClick:()=>Y("click_to_buy_copilot_for_business","get_github_copilot"),children:"Get GitHub Copilot"}):E?(0,o.jsx)(q,{}):null:I?(0,o.jsx)(h.Q,{type:"button",href:"/settings/copilot",onClick:()=>Y("click_to_go_to_copilot_settings","get_github_copilot"),children:"Get GitHub Copilot"}):(0,o.jsx)(h.Q,{type:"button",href:"/github-copilot/signup",onClick:()=>Y("click_to_go_to_copilot_trial_signup","start_a_free_trial"),children:"Start a free trial"})},ee=()=>{T&&((0,d.Q)(T.dismissPath,{method:Z?"DELETE":"POST"}),Q(),$(!0))};return H||!t?null:(0,o.jsx)(m.Z,{className:n,children:(0,o.jsx)(f.w,{onOpen:W,onClose:z,open:A,overlayProps:{role:"dialog",sx:{overflow:"inherit"}},focusZoneSettings:{disabled:!0},renderAnchor:e=>(0,o.jsx)(p.z,{...e,...(0,l.f)("copilot-popover-button"),leadingVisual:u.CopilotIcon,onClick:()=>{M(!A),K()},size:"small",sx:{color:"fg.default",display:["none","none","block","block"]},variant:"invisible",children:U()}),children:(0,o.jsxs)(x.Z,{...(0,l.f)("copilot-popover-content"),caret:"top",sx:{display:"flex",flexDirection:"column",fontSize:1,justifyContent:"space-between",padding:4,width:"350px"},children:[(0,o.jsx)(y.Z,{as:"h2",sx:{fontSize:1,fontWeight:"bold",pb:3},children:"Code 55% faster with GitHub Copilot"}),(0,o.jsxs)(m.Z,{sx:{fontSize:1,fontWeight:"normal",pb:3},children:[(0,o.jsx)(g.Z,{...(0,l.f)("copilot-popover-body-text"),children:G()}),(0,o.jsx)(b.Z,{...(0,l.f)("copilot-popover-content-learn-more"),"aria-label":"Click this link to learn more about copilot. This action opens in a new tab.",target:"_blank",href:r,onClick:()=>X(),sx:{marginLeft:"8px"},children:"Learn more"})]}),(0,o.jsxs)(m.Z,{sx:{alignItems:"center",display:"flex",flexDirection:"row"},children:[J(),(0,o.jsx)(p.z,{...(0,l.f)("copilot-popover-dismiss-button"),variant:"invisible",onClick:ee,sx:{cursor:"pointer",fontSize:1,fontWeight:"bold",textDecorationLine:"none",marginLeft:"8px"},children:"Don't show again"})]})]})})})};try{(r=N).displayName||(r.displayName="CopilotPopover")}catch{}try{(i=FeatureRequest).displayName||(i.displayName="FeatureRequest")}catch{}},90342:(e,t,n)=>{n.d(t,{P:()=>o});var r,i=n(85893);function o({buttonFocusId:e,buttonHotkey:t,onButtonClick:n,buttonTestLabel:r,onlyAddHotkeyScopeButton:o}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("button",{hidden:!0,"data-testid":r||"","data-hotkey":t,onClick:n,"data-hotkey-scope":e}),!o&&(0,i.jsx)("button",{hidden:!0,"data-hotkey":t,onClick:n})]})}try{(r=o).displayName||(r.displayName="DuplicateOnKeydownButton")}catch{}},4220:(e,t,n)=>{n.d(t,{s:()=>s});var r,i=n(85893),o=n(42483),a=n(67294);let s=({children:e,sx:t,...n})=>(0,i.jsx)(o.Z,{sx:{backgroundColor:"canvas.default",border:"1px solid",borderColor:"border.default",borderRadius:"6px",contain:"paint",display:"flex",flexDirection:"column",height:"100%",minHeight:0,maxHeight:"100vh",overflowY:"auto",right:0,...t},...n,children:e});try{(r=s).displayName||(r.displayName="Panel")}catch{}},92562:(e,t,n)=>{n.d(t,{m:()=>s});var r,i=n(85893),o=n(42483),a=n(74121);let s=()=>(0,i.jsx)(o.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",py:3},"data-testid":"suspense-spinner",children:(0,i.jsx)(a.Z,{"aria-label":"Loading"})});try{(r=s).displayName||(r.displayName="LoadingFallback")}catch{}},40856:(e,t,n)=>{n.d(t,{P:()=>m});var r,i=n(85893),o=n(86283),a=n(88455),s=n(85529),l=n(42483),c=n(98833),d=n(97011),u=n(78912),h=n(73290);function m({helpUrl:e,webCommitInfo:t}){let{shouldFork:n,lockedOnMigration:r,shouldUpdate:m}=t,f=o.ssrSafeLocation.pathname;f.endsWith("/")&&(f=f.slice(0,-1));let p=f+o.ssrSafeLocation.search,x=(0,a.F)(p,"post"),y=r?{message:"This repository is currently being migrated.",description:"Sorry, you\u2019re not able to edit this repository while the migration is in progress.",icon:s.LockIcon}:n?{message:"You need to fork this repository to propose changes.",description:"Sorry, you\u2019re not able to edit this repository directly\u2014you need to fork it and propose your changes from there instead.",icon:s.GitBranchIcon}:{message:"Sorry, it looks like your fork is outdated!",description:"You\u2019ll have to bring it up to date before you can propose changes.",icon:s.AlertIcon};return(0,i.jsxs)(l.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",m:4},children:[(0,i.jsx)(c.Z,{icon:y.icon,size:"medium",sx:{color:"fg.muted",mb:2}}),(0,i.jsx)(d.Z,{as:"h3",sx:{mb:1},children:y.message}),(0,i.jsx)(d.Z,{sx:{mb:2},children:y.description}),(n||m)&&(0,i.jsxs)("form",{"data-turbo":"false",method:"post",action:p,"data-testid":"edit-issues-form",children:[(0,i.jsx)("input",{hidden:!0,name:"authenticity_token",value:x,readOnly:!0}),(0,i.jsx)(u.z,{type:"submit",variant:"primary",children:n?"Fork this repository":"Update your fork"})]}),n&&(0,i.jsx)(h.Z,{href:`${e}/articles/fork-a-repo`,children:"Learn more about forks"})]})}try{(r=m).displayName||(r.displayName="EditIssues")}catch{}},57338:(e,t,n)=>{n.d(t,{r:()=>a});var r,i=n(85893),o=n(51461);function a({forkName:e,forkOwner:t}){return(0,i.jsxs)(o.Z,{sx:{mb:3},children:["You\u2019re making changes in a project you don\u2019t have write access to. Submitting a change will write it to a new branch in your fork ",(0,i.jsx)("b",{children:`${t}/${e}`}),", so you can send a pull request."]})}try{(r=a).displayName||(r.displayName="EditingForkBanner")}catch{}},81543:(e,t,n)=>{n.d(t,{Ew:()=>S,V1:()=>Z,d2:()=>E,fN:()=>R,fq:()=>L,h7:()=>v,hI:()=>N,ud:()=>T,v0:()=>B,yB:()=>I});var r,i,o,a=n(85893),s=n(86283),l=n(62073),c=n(85529),d=n(51461),u=n(98833),h=n(42483),m=n(73290),f=n(78912),p=n(38490),x=n(67294);let y=/[\u202A-\u202E]|[\u2066-\u2069]/,g=/[\u202A-\u202E]|[\u2066-\u2069]/g,b=/([\u202A-\u202E]|[\u2066-\u2069])/g,j={"\u202A":"U+202A","\u202B":"U+202B","\u202C":"U+202C","\u202D":"U+202D","\u202E":"U+202E","\u2066":"U+2066","\u2067":"U+2067","\u2068":"U+2068","\u2069":"U+2069"},w=new Map(Object.entries(j));function v(){let[e]=(0,l.D)(()=>s.iG,s.iG,[]);if(!e)return null;let t=new URL(e.location.href,e.location.origin),n="1"===t.searchParams.get("h");return n?t.searchParams.delete("h"):t.searchParams.set("h","1"),(0,a.jsxs)(d.Z,{full:!0,variant:"warning",sx:{alignItems:"center",display:"flex"},children:[(0,a.jsx)(u.Z,{icon:c.AlertIcon}),(0,a.jsxs)(h.Z,{as:"span",children:["This file contains bidirectional Unicode text that may be interpreted or compiled differently than what appears below. To review, open the file in an editor that reveals hidden Unicode characters."," ",(0,a.jsx)(m.Z,{href:"https://github.co/hiddenchars",target:"_blank",rel:"noreferrer",children:"Learn more about bidirectional Unicode characters"})]}),(0,a.jsx)(f.z,{as:"a",onClick:()=>{window.location.href=t.href},size:"small",sx:{float:"right",ml:"24px",backgroundClip:"padding-box"},children:n?"Hide revealed characters":"Show hidden characters"})]})}function N(){return(0,a.jsx)(p.Z,{direction:"e",text:"This line has hidden Unicode characters",children:(0,a.jsx)(u.Z,{icon:c.AlertIcon,sx:{mr:"12px"}})})}function C({char:e}){return(0,a.jsx)(h.Z,{as:"span",className:"bidi-replacement padded",children:e})}function k(e){return`<span class="bidi-replacement">${e}</span>`}function S(e){return`<span class="bidi-replacement" data-code-text="${e}"></span>`}function I(e){return e.split(b)}function R(e){if(!T(e))return null;let t=I(e),n=t.map(e=>{let t=w.get(e);return t?k(t):e});return n.join("")}function Z(e){return T(e)?e.replaceAll(g,e=>w.get(e)??""):e}function E(e){if(!T(e))return null;let t=I(e);return t.map((e,t)=>{let n=w.get(e);return n?(0,a.jsx)(C,{char:n},t):e})}function T(e){return y.test(e)}function L(){if(!s.iG)return!1;let e=new URL(s.iG.location.href,s.iG.location.origin);return"1"===e.searchParams.get("h")}function B(e){return w.get(e)}try{(r=v).displayName||(r.displayName="BidiAlert")}catch{}try{(i=N).displayName||(i.displayName="BidiTooltip")}catch{}try{(o=C).displayName||(o.displayName="BidiCharacter")}catch{}},50299:(e,t,n)=>{n.d(t,{Xj:()=>v,a:()=>w,ew:()=>b,hL:()=>a,iC:()=>y});var r,i,o,a,s=n(85893),l=n(78212),c=n(89445),d=n(85529),u=n(51461),h=n(98833),m=n(42483),f=n(78912),p=n(12470),x=n(67294);function y({errors:e,state:t}){let[n,r]=(0,x.useState)(!1);return t===a.ERROR?(0,s.jsx)(u.Z,{variant:"warning",sx:{mt:3},children:"Failed to validate this CODEOWNERS file"}):t===a.LOADING?(0,s.jsx)(u.Z,{variant:"default",sx:{mt:3},children:"Validating CODEOWNERS rules..."}):0===e.length?(0,s.jsx)(u.Z,{variant:"success",sx:{mt:3},children:"This CODEOWNERS file is valid."}):(0,s.jsxs)(u.Z,{variant:"warning",sx:{display:"flex",flexDirection:"row",mt:3},children:[(0,s.jsx)(h.Z,{icon:d.BugIcon}),(0,s.jsxs)(m.Z,{onClick:()=>r(!n),sx:{cursor:"pointer",display:"flex",p:0},children:["This CODEOWNERS file contains errors",(0,s.jsx)(f.z,{as:"a",sx:{alignSelf:"center",borderRadius:"1px",height:"12px",lineHeight:"6px",ml:1,px:"5px",py:0},children:"..."})]}),(0,s.jsxs)(p.Z,{isOpen:n,onDismiss:()=>r(!1),sx:{display:"flex",flexDirection:"column",width:"640px"},children:[(0,s.jsx)(p.Z.Header,{children:"CODEOWNERS errors"}),(0,s.jsx)(m.Z,{sx:{overflowX:"hidden",overflowY:"auto",p:3},children:e.map((e,t)=>(0,s.jsx)(g,{error:e},t))})]})]})}function g({error:e}){return(0,s.jsxs)(m.Z,{sx:{padding:"16px",listStyleType:"none",borderTop:"1px solid var(--borderColor-muted, var(--color-border-muted))"},children:[`${e.kind} on line ${e.line}${e.suggestion?`: ${e.suggestion}`:""}`,(0,s.jsx)(m.Z,{as:"pre",sx:{mt:3},children:(0,s.jsxs)("code",{children:[e.linePrefix,(0,s.jsx)(m.Z,{as:"b",sx:{cursor:"help",fontStyle:"italic",color:"var(--fgColor-danger, var(--color-danger-fg))",position:"relative","&::before":{content:'""',position:"absolute",top:"101%",left:0,width:"100%",height:"0.25em",background:"linear-gradient(135deg, transparent, transparent 45%, var(--fgColor-danger, var(--color-danger-fg)), transparent 55%, transparent 100%),linear-gradient(45deg, transparent, transparent 45%, var(--fgColor-danger, var(--color-danger-fg)), transparent 55%, transparent 100%)",backgroundRepeat:"repeat-x,repeat-x",backgroundSize:"0.5em 0.5em"}},children:e.lineError}),e.lineSuffix]})})]})}function b(e){let t=e.source.trim(),n=e.column-1,r=j(n,e.end_column,t);if(n>30){let e=n-30;t="\u2026"+t.slice(e),n-=e-1,r-=e-1}return{...e,linePrefix:t.substring(0,n),lineError:t.substring(n,r),lineSuffix:t.substring(r)}}function j(e,t,n){return t||(n.substring(e).indexOf(" ")>0?n.indexOf(" ",e):n.length)}function w(){return(0,s.jsx)(h.Z,{icon:d.DotFillIcon,sx:{color:"var(--fgColor-danger, var(--color-danger-fg))"},"aria-label":"This line contains CODEOWNERS errors"})}function v(e,t,n){return(0,c.v)((0,l.Cv)({owner:e.ownerLogin,repo:e.name,commitish:t.name,filePath:n}),{method:"GET"})}!function(e){e[e.ERROR=0]="ERROR",e[e.LOADING=1]="LOADING",e[e.VALIDATED=2]="VALIDATED"}(a||(a={}));try{(r=y).displayName||(r.displayName="CodeownerFileBanner")}catch{}try{(i=g).displayName||(i.displayName="CodeownersErrorDetails")}catch{}try{(o=w).displayName||(o.displayName="CodeownersErrorLineIndicator")}catch{}},98917:(e,t,n)=>{n.d(t,{E:()=>B});var r,i,o,a,s=n(85893),l=n(11117),c=n(85529),d=n(42483),u=n(50919),h=n(86010),m=n(67294),f=n(17391),p=n(86283),x=n(84312);function y(e,t,n,r,i){let o=(0,m.useRef)(null),a=(0,x.$L)(),s=!(void 0!==p.n4),l=(0,x.Ub)(),c=(0,m.useMemo)(()=>{let{isEndLine:o,isStartLine:a,lineNumber:c}=e,d=0,u=1,h=o&&!s?new IntersectionObserver(e=>{for(let{target:t,isIntersecting:n,intersectionRatio:i}of e)if(t){let{currentY:e,currentRatio:o}=g(d,u,t,i,c,l,n,r);d=e,u=o}},{root:null,rootMargin:`-${n}px 0px 0px 0px`,threshold:0}):void 0,m=a&&!s?new IntersectionObserver(t=>{for(let{target:n,isIntersecting:i,intersectionRatio:o}of t)if(n){let{currentY:t,currentRatio:a}=b(e,d,u,n,o,i,r);d=t,u=a}},{root:null,rootMargin:`-${n+(i?20*i:0)}px 0px 0px 0px`,threshold:[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1]}):void 0,f=t?a?m:o?h:void 0:void 0;return f&&(f.hasBeenDisconnected=!1,f.hasBeenObserved=!1),f},[e,t,n,l,r,i,s]),d=(0,m.useCallback)(e=>{o.current=e},[]);return(0,m.useEffect)(()=>{let{isStartLine:t,lineNumber:n,ownedSection:r}=e;return t&&o&&r&&c&&!c.hasBeenObserved&&!c.hasBeenDisconnected&&a(r.endLine,{lineNumber:n}),c&&o.current&&!c.hasBeenObserved&&(c.observe(o.current),c.hasBeenObserved=!0),()=>{c&&(c.disconnect(),c.hasBeenDisconnected=!0)}},[c,e]),d}function g(e,t,n,r,i,o,a,s){let l=n.getBoundingClientRect().y,c=i?o(i):void 0,d=window.innerHeight-n.getBoundingClientRect().bottom>0,u=n.getBoundingClientRect().bottom>0,h=n.getBoundingClientRect().top<150,m=d&&u,f=window.innerHeight-n.getBoundingClientRect().bottom>150&&window.innerHeight>300;for(let n of c||[])n&&m&&(l<e&&m?r>t&&a||s(n,!0):l>e&&a&&(r<t||f&&h&&s(n,!1)));return{currentY:l,currentRatio:r}}function b(e,t,n,r,i,o,a){let s=r.getBoundingClientRect().y,l=window.innerHeight-r.getBoundingClientRect().bottom>0,c=r.getBoundingClientRect().bottom>0,d=r.getBoundingClientRect().top<150&&r.getBoundingClientRect().top>-300,u=0===r.getBoundingClientRect().bottom&&0===r.getBoundingClientRect().top&&0===r.getBoundingClientRect().height&&0===r.getBoundingClientRect().width&&0===r.getBoundingClientRect().x&&0===r.getBoundingClientRect().y;return!e.ownedSection||e.ownedSection?.collapsed||(s<=t&&(l&&c||d)&&!u?i>n&&o||d&&a(e,!1):s>t&&o&&(i<n||a(e,!0))),{currentY:s,currentRatio:i}}var j=n(77262);function w(){return(0,m.useSyncExternalStore)(C,v,N)}function v(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function N(){return!1}function C(){return()=>{}}var k=n(20286),S=n(13816),I=n(60193),R=n(13570),Z=n(90984),E=n(90176),T=n(92110);let L=()=>{},B=m.memo(_);function _({codeLineData:e,codeLineClassName:t,id:n,onClick:r,setIsCollapsed:i,onLineStickOrUnstick:o,virtualOffset:a,codeLineToSectionMap:c,virtualKey:d,forceVisible:u,measureRef:p}){let{lineNumber:x,stylingDirectivesLine:g,rawText:b}=e,w=(0,S.H1)(x),{tabSize:v}=(0,f.G)(),N=(0,Z.C)(x),C=(0,m.useRef)(null),B=N&&C.current?(0,S.bP)(N,C.current,x,v,b??""):void 0,_=c?.get(x)?.length??0,O=(0,k.nx)(),P=y(e,!0,O,o??L,_),A=(0,l.O)().codeWrappingOption,M=(0,E.si)(x),H=(0,E.KC)(x),$=(0,j.G)("react-code-lines"),W=N?.start.line===x,z=(0,m.useRef)(null),U=(0,m.useRef)(null);return(0,m.useLayoutEffect)(()=>{W&&U.current?.setAnchor(z.current)},[W]),(0,s.jsx)("div",{ref:e=>{p?.(e),P(e)},"data-key":d,className:(0,h.W)(t,"react-code-text react-code-line-contents",a&&"virtual",A.enabled&&p&&"react-code-text-cell"),style:{transform:a?`translateY(${a}px)`:void 0,minHeight:A.enabled?$:"auto"},onClick:r,children:(0,s.jsxs)("div",{ref:z,children:[N&&(0,s.jsx)(D,{lineNumber:x,highlightPosition:B}),w&&!N&&(0,s.jsx)(D,{subtle:!0,lineNumber:x,highlightPosition:B}),M&&M.length>0&&(0,s.jsx)(R.R,{symbols:M,focusedSymbol:H,sx:{paddingLeft:"10px",width:"auto"},lineNumber:x}),(0,s.jsx)(T.$c,{id:n,lineNumber:x,stylingDirectivesLine:g,current:!!N,rawText:b,forceVisible:u,ref:C}),(0,s.jsx)(F,{codeLineData:e,setIsCollapsed:i,onLineStickOrUnstick:o}),W&&(0,s.jsx)(I.lv,{ref:U,rowBeginNumber:N.start.line,rowEndNumber:N.end.line})]})})}function D({lineNumber:e,highlightPosition:t,subtle:n}){let r=w(),i=t?.offset!==void 0,o=t?.width!==void 0,a=t?.offset??-72,l=t?.width??0;return(0,s.jsx)(d.Z,{sx:{position:"absolute",backgroundColor:n?"neutral.subtle":"var(--bgColor-attention-muted, var(--color-attention-subtle))",height:"100%",opacity:".6",boxShadow:n?"inset 2px 0 0 var(--fgColor-muted,  var(--color-fg-subtle))":"inset 2px 0 0 var(--fgColor-attention, var(--color-attention-fg))",top:r?"-3px":0,left:`${a}px`,width:o?`${i&&o?l:l+82}px`:"calc(100% + 72px)",pointerEvents:"none"}},`highlighted-line-${e}`)}function F({codeLineData:e,setIsCollapsed:t,onLineStickOrUnstick:n}){let{lineNumber:r,ownedSection:i}=e,o=(0,S.H1)(r);return o?(0,s.jsx)(u.h,{icon:c.EllipsisIcon,sx:{px:2,py:0,ml:1,pointerEvents:"auto",maxHeight:"20px",backgroundColor:"transparent",border:"none",color:"var(--fgColor-muted,  var(--color-fg-subtle))","&:hover":{color:"var(--fgColor-accent, var(--color-accent-fg))"}},"aria-label":"Expand row",onMouseDown:o=>{(0,S.yw)(r),t?.(!1),i&&(i.collapsed=!1,n?.(e,!0)),o.preventDefault()}}):null}try{(r=B).displayName||(r.displayName="CodeLine")}catch{}try{(i=_).displayName||(i.displayName="CodeLineUnmemoized")}catch{}try{(o=D).displayName||(o.displayName="HighlighterElement")}catch{}try{(a=F).displayName||(a.displayName="ExpandRowEllipsis")}catch{}},90984:(e,t,n)=>{n.d(t,{C:()=>u,k:()=>d});var r,i,o=n(85893),a=n(77417),s=n(51252),l=n(67294);let c=l.createContext(new a.fA(void 0));function d({highlightedLines:e,children:t}){let n=(0,s.yL)(e);return(0,l.useEffect)(()=>{n.value=e},[n,e]),(0,o.jsx)(c.Provider,{value:n,children:t})}function u(e){let t=l.useContext(c),n=(0,s.D_)(t,t=>t&&e>=t.start.line&&e<=t.end.line?t:void 0);return(0,s.iu)(n)}try{(r=c).displayName||(r.displayName="HighlightedLineContext")}catch{}try{(i=d).displayName||(i.displayName="HighlightedLinesProvider")}catch{}},16275:(e,t,n)=>{n.d(t,{_:()=>N});var r,i,o,a,s,l=n(85893),c=n(11117),d=n(85529),u=n(42483),h=n(98833),m=n(86010),f=n(67294),p=n(56334),x=n(86480),y=n(5262),g=n(13816),b=n(81543),j=n(50299),w=n(90984);let v=(0,f.lazy)(()=>Promise.resolve().then(n.bind(n,85503))),N=f.memo(C);function C({codeLineData:e,onClick:t,ownedCodeSections:n,onCollapseToggle:r,preventClick:i,onLineStickOrUnstick:o,virtualOffset:a}){let{lineNumber:s,ownedSection:d,codeLineClassName:u,isStartLine:h,codeownersLineError:N,bidi:C}=e,{sendRepoClickEvent:I}=(0,y.a)(),R=(0,x.gk)(),Z=(0,f.useCallback)(e=>{let n,r;if(e.defaultPrevented)return;let i=parseInt(e.currentTarget.getAttribute("data-line-number"),10),o=(0,p.n6)(`L${i}`),a=R.current;if(a)n=a.start,r=a.end;else{let e=window.getSelection()?.rangeCount?window.getSelection()?.getRangeAt(0):null;e&&(n=(0,g.jP)(e.startContainer,e.startOffset),r=(0,g.jP)(e.endContainer,e.endOffset))}let s=!1;n&&r&&n.line<=i&&r.line>=i&&(s=!0,o={anchorPrefix:"",blobRange:{start:n,end:r}});let{blobRange:l}=o,c=(0,p.G5)(window.location.hash);c&&e.shiftKey&&!s?(I("BLOB.MULTILINE"),o.blobRange={start:c.start,end:l.end}):I("BLOB.LINE");let d=(0,p.Dw)(o);history.replaceState(history.state,"",d),t?.(e)},[t,R,I]),E=(0,w.C)(s),T=E?.start.line===s,L=E&&E.start.line<s&&E.end.line>=s,B=L||T&&null===E?.start.column,_=(0,f.useRef)(null),D=(0,f.useRef)(null);(0,f.useLayoutEffect)(()=>{T&&D.current?.setAnchor(_.current)},[T]);let{codeFoldingOption:F}=(0,c.O)(),O=F.enabled;return(0,f.useEffect)(()=>{!F.enabled&&((0,g.Yo)(),r?.(!1),d&&(d.collapsed=!1))},[F.enabled,d,r]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{ref:_,"data-line-number":s,className:(0,m.W)(u,"react-line-number react-code-text",a&&"virtual",i&&"prevent-click",B&&"highlighted-line"),style:a?{paddingRight:"16px",transform:`translateY(${a}px)`}:{paddingRight:"16px"},onMouseDown:i?void 0:Z,children:[s,N&&(0,l.jsx)(k,{children:(0,l.jsx)(j.a,{})}),C&&(0,l.jsx)(k,{children:(0,l.jsx)(b.hI,{})}),O&&h&&d&&n&&(0,l.jsx)(k,{displayRight:!0,children:(0,l.jsx)(S,{codeLineData:e,onCollapseToggle:r,onLineStickOrUnstick:o})})]}),T&&(0,l.jsx)(v,{codeLineClassName:u,ref:D,rowBeginId:`LG${E.start.line}`,rowBeginNumber:E.start.line,rowEndNumber:E.end.line,rowEndId:`LG${E.end.line}`})]})}function k({children:e,sx:t,displayRight:n}){return(0,l.jsx)(u.Z,{as:"span",sx:{...n?void 0:{left:"-4px"},margin:`1px ${n?"8px":"1px"}`,position:"absolute",zIndex:"1",...t},children:e})}function S({codeLineData:e,onCollapseToggle:t,onLineStickOrUnstick:n}){let r=(0,g.H1)(e.lineNumber),i=(0,f.useCallback)(r=>{let{lineNumber:i,ownedSection:o}=e;o&&(o.collapsed=!1),t?.(!1),(0,g.yw)(i),n?.(e,!0),r.preventDefault()},[e,t,n]),o=(0,f.useCallback)(n=>{let{lineNumber:r,ownedSection:i}=e;i&&(i.collapsed=!0),t?.(!0),(0,g.rH)(r),n.preventDefault()},[e,t]);return r?(0,l.jsx)(u.Z,{"aria-label":"Expand code section",onMouseDown:i,role:"button",sx:{position:"absolute"},children:(0,l.jsx)(h.Z,{icon:d.ChevronRightIcon})}):(0,l.jsx)(u.Z,{"aria-label":"Collapse code section",onMouseDown:o,role:"button",sx:{position:"absolute"},children:(0,l.jsx)(h.Z,{icon:d.ChevronDownIcon})})}try{(r=v).displayName||(r.displayName="HighlightedLineMenu")}catch{}try{(i=N).displayName||(i.displayName="LineNumber")}catch{}try{(o=C).displayName||(o.displayName="LineNumberUnmemoized")}catch{}try{(a=k).displayName||(a.displayName="CodeAlert")}catch{}try{(s=S).displayName||(s.displayName="CodeFoldingChevron")}catch{}},90176:(e,t,n)=>{n.d(t,{KC:()=>u,Sh:()=>c,si:()=>d});var r,i=n(85893),o=n(77417),a=n(51252),s=n(67294);let l=(0,s.createContext)({resultsByLineNumber:new o.vP,focusedResult:new o.fA(void 0)});function c({searchResults:e,focusedSearchResult:t,children:n}){let r=(0,a.Kq)(),o=(0,a.yL)(void 0!==t?e[t]:void 0);(0,s.useEffect)(()=>{let t=new Map;for(let n of e){let e=n.lineNumber;t.has(e)?t.get(e).push(n):t.set(e,[n])}for(let[e,n]of(r.clear(),t))r.set(e,n)},[r,e]),(0,s.useEffect)(()=>{o.value=void 0!==t?e[t]:void 0},[e,o,t]);let c=(0,s.useMemo)(()=>({resultsByLineNumber:r,focusedResult:o}),[r,o]);return(0,i.jsx)(l.Provider,{value:c,children:n})}function d(e){let{resultsByLineNumber:t}=(0,s.useContext)(l);return(0,a.iu)(t.get(e))}function u(e){let{focusedResult:t}=(0,s.useContext)(l),n=(0,a.D_)(t,t=>t?.lineNumber===e?t:void 0);return(0,a.iu)(n)}try{(r=c).displayName||(r.displayName="SearchResultsProvider")}catch{}},92110:(e,t,n)=>{n.d(t,{$c:()=>u,RC:()=>f});var r,i,o=n(85893),a=n(93062),s=n(67294),l=n(17391),c=n(86480),d=n(81543);let u=s.memo(s.forwardRef(h));function h({id:e,stylingDirectivesLine:t,rawText:n,lineNumber:r,current:i,forceVisible:s},l){let c=m(),d=f(void 0,t,n,s?"plain":c);return(0,o.jsx)(a.sF,{id:e,className:"react-file-line html-div","data-testid":"code-cell","data-line-number":r,html:d,ref:l,style:{position:"relative"},"aria-current":i?"location":void 0})}function m(){let e=(0,c.nj)(),t=S();return e?t?"separated-characters":"data-attribute":"plain"}function f(e,t,n,r="plain"){let{tabSize:i}=(0,l.G)(),o=(0,d.fq)();return(0,s.useMemo)(()=>e??p(n,t,r,i,o),[n,e,t,r,i,o])}function p(e,t,n,r,i){e||(e="\n");let o=g(e,t,n,r),a=[];return x(o,n,i,a),a.join("")}function x(e,t,n,r){for(let i of(e.cssClass&&r.push(`<span class="${C(e.cssClass)}">`),e.nodes))j(i)?x(i,t,n,r):r.push(y(i,t,n));e.cssClass&&r.push("</span>")}function y(e,t,n){switch(t){case"data-attribute":{let r=C(e.text);if(n&&(0,d.ud)(r)){let n=(0,d.yB)(r),i=n.map(n=>{let r=(0,d.v0)(n);return r?(0,d.Ew)(r):y({...e,text:n,cssClass:""},t,!1)});return e.cssClass?`<span class="${C(e.cssClass)}">${i.join("")}</span>`:i.join("")}return e.cssClass?`<span class="${C(e.cssClass)}" data-code-text="${r}"></span>`:`<span data-code-text="${r}"></span>`}case"separated-characters":{let t=[...e.text].map(e=>{let t=n?(0,d.v0)(e):void 0;return t?(0,d.Ew)(t):`<span data-code-text="${k(e)}"></span>`}).join("");return e.cssClass?`<span class="${C(e.cssClass)}">${t}</span>`:t}default:{let t=C(e.text),r=n?(0,d.fN)(t)??t:t;return e.cssClass?`<span class="${C(e.cssClass)}">${r}</span>`:r}}}function g(e,t,n,r){let i={value:0},o={nodes:[],start:0,end:e.length,cssClass:""},a=t?.filter(e=>e.end>e.start);if(!a||0===a.length)return o.nodes.push(b("",e,0,e.length,i,r,n)),o;let s=[o];for(let t=0;t<a.length;t++){let l=a[t],c=a[t+1],d=s[s.length-1]??o,u=d.nodes[d.nodes.length-1];if(0===d.nodes.length&&l.start>d.start){let t=b("",e,d.start,l.start,i,r,n);d.nodes.push(t)}else if(u&&l.start>u.end){let t=b("",e,u.end,l.start,i,r,n);d.nodes.push(t)}let h=c&&c.start<l.end;if(h){let e={...l,nodes:[]};d.nodes.push(e),s.push(e)}else{let t=b(l.cssClass,e,l.start,l.end,i,r,n);d.nodes.push(t)}if(c&&c.start>=d.end){let t=l.end;if(d.end>t){let o=b("",e,t,d.end,i,r,n);d.nodes.push(o),t=d.end}for(;s.length>1&&c.start>=d.end;)if(s.pop(),d=s[s.length-1]??o,s.length>1&&c.start>=d.end&&d.end>t){let o=b("",e,t,d.end,i,r,n);t=d.end,d.nodes.push(o)}}}for(;s.length>0;){let t=s.pop(),o=t.nodes[t.nodes.length-1];if(o&&o.end<t.end){let a=b("",e,o.end,t.end,i,r,n);t.nodes.push(a)}}return o}function b(e,t,n,r,i,o,a){let s=t.substring(n,r),l="plain"!==a?w(s,o,i):s;return{cssClass:e,start:n,end:r,text:l}}function j(e){return"nodes"in e}function w(e,t,n){let r=[];for(let i of e)if("	"===i){let e=t-n.value%t;r.push(v(e)),n.value+=e}else r.push(i),n.value+=N(i);return r.join("")}function v(e){return Array(e).fill(" ").join("")}function N(e){return Array.from(e).length}function C(e){return e.replace(/[&<>"']/g,k)}function k(e){switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"'":return"&#039;";default:return e}}function S(){return(0,s.useSyncExternalStore)(()=>()=>{},()=>navigator.userAgent.toLowerCase().indexOf("firefox")>-1,()=>!1)}try{(r=u).displayName||(r.displayName="SyntaxHighlightedLine")}catch{}try{(i=h).displayName||(i.displayName="SyntaxHighlightedLineWithRef")}catch{}},87054:(e,t,n)=>{n.d(t,{V:()=>a,d:()=>s});var r,i=n(85893),o=n(42483);let a="find-result-marks-container";function s(){return(0,i.jsx)(o.Z,{sx:{position:"fixed",top:0,right:0,height:"100%",width:"15px",transition:"transform 0.3s","&:hover":{transform:"scaleX(1.5)"},zIndex:1},id:a})}try{(r=s).displayName||(r.displayName="ScrollMarksContainer")}catch{}},60193:(e,t,n)=>{n.d(t,{Id:()=>g,lv:()=>b});var r,i,o,a=n(85893),s=n(56302),l=n(32769),c=n(42483),d=n(67294),u=n(73935),h=n(64937),m=n(17391),f=n(80624),p=n(90606);let x="copilot-button-positioner",y="copilot-button-container";function g({children:e}){return(0,a.jsxs)(c.Z,{id:x,sx:{position:"relative"},children:[e,(0,a.jsx)("div",{id:y})]})}let b=d.memo(d.forwardRef(j));function j({rowBeginNumber:e,rowEndNumber:t},n){let[r,i]=(0,d.useState)(null);(0,d.useImperativeHandle)(n,()=>({setAnchor:i}));let[o,c]=(0,d.useState)({top:0,left:0});(0,d.useLayoutEffect)(()=>{let e=()=>requestAnimationFrame(()=>c(w(r)));return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[r]);let{refInfo:p,path:x}=(0,f.Br)(),g=(0,l.H)(),{language:b,languageID:j}=(0,m.G)(),v=(0,d.useMemo)(()=>({type:"snippet",languageID:j,languageName:b,path:x,range:{start:e,end:t},ref:(0,h.Ju)(p.name,p.refType),commitOID:p.currentOid,repoID:g.id,repoName:g.name,repoOwner:g.ownerLogin,url:window.location.href}),[j,b,x,e,t,p,g]),N=(0,a.jsx)("div",{style:{alignSelf:"center",position:"absolute",lineHeight:"16px",height:"24px",width:"24px",...o},children:(0,a.jsx)(s.Z,{messageReference:v})}),C=document.getElementById(y);return C?(0,u.createPortal)(N,C):null}function w(e,t={x:0,y:0}){let n=document.getElementById(x);if(!e||!n)return{display:"none"};let{top:r,height:i}=e.getBoundingClientRect(),{top:o}=n.getBoundingClientRect(),a=(p.XT-i)/2;return{top:`${r-o-a+t.y+1}px`,right:"37px"}}try{(r=g).displayName||(r.displayName="CopilotButtonContainer")}catch{}try{(i=b).displayName||(i.displayName="CopilotButton")}catch{}try{(o=j).displayName||(o.displayName="CopilotButtonWithRef")}catch{}},85503:(e,t,n)=>{n.r(t),n.d(t,{HighlightedLineMenuContainer:()=>k,default:()=>I,firstOptionId:()=>C});var r,i,o,a=n(85893),s=n(85529),l=n(42483),c=n(6324),d=n(50919),u=n(52516),h=n(67294),m=n(73935),f=n(79655),p=n(98950),x=n(51188),y=n(80624),g=n(31174),b=n(90606),j=n(35499),w=n(13816),v=n(22554);let N="highlighted-line-menu-container",C="highlighted-line-menu-first-option";function k({children:e}){return(0,a.jsxs)(l.Z,{id:b._X,sx:{position:"relative"},children:[e,(0,a.jsx)("div",{id:N})]})}let S=h.memo(h.forwardRef(R)),I=S;function R({codeLineClassName:e,offset:t,lineData:n,onLineStickOrUnstick:r,onMenuClose:i,onCollapseToggle:o,openOnLoad:l=!1,cursorRef:k,rowBeginId:S,rowBeginNumber:I,rowEndId:R,rowEndNumber:Z},E){let[T,L]=(0,h.useState)(null);(0,h.useImperativeHandle)(E,()=>({setAnchor:L}));let B=h.useRef(null),{githubDevUrl:_}=(0,y.Ou)(),[D,F]=(0,h.useState)({top:0,left:0});(0,h.useLayoutEffect)(()=>{let e=()=>requestAnimationFrame(()=>F((0,b.kl)(T,t)));return e(),window.addEventListener("resize",e),()=>{window.removeEventListener("resize",e)}},[T,t]),(0,h.useLayoutEffect)(()=>{l&&(k&&L(k.current),setTimeout(()=>{H(!0)},50))},[]);let{newDiscussionPath:O,newIssuePath:P}=(0,x.nO)(),{refSelectorShortcut:A}=(0,g.bx)(),[M,H]=h.useState(!1),{createPermalink:$,getUrl:W}=(0,j.B)(),{setShouldBeOpen:z,expandOrCollapseSection:U,openUpRefSelector:G}=(0,b.$w)({lineData:n,onLineStickOrUnstick:r,onMenuClose:i,onCollapseToggle:o,setOpen:H}),[q]=(0,f.lr)(),V="1"===q.get("plain"),K=$({absolute:!0,params:V?"plain=1":void 0}),Y=encodeURIComponent(K),Q=`L${I}${S!==R?`-L${Z}`:""}`,X=(0,a.jsxs)(c.P,{open:M,onOpenChange:z,children:[(0,a.jsx)(c.P.Anchor,{children:(0,a.jsx)(d.h,{className:e,size:"small",icon:s.KebabHorizontalIcon,"aria-label":`Code line ${I} options`,"data-testid":"highlighted-line-menu-button",sx:{alignSelf:"center",position:"absolute",lineHeight:"16px",height:"24px",width:"24px",visibility:l?"hidden":"visible",...D}})}),(0,a.jsx)(c.P.Overlay,{width:"small",children:(0,a.jsxs)(u.S,{children:[I===Z&&(0,a.jsx)(u.S.Item,{onClick:()=>{(0,w.dM)(`Copied line ${I}.`);let e=(0,b.kq)(document.getElementById(`LC${I}`));e&&(0,p.z)(e),z(!1)},onSelect:()=>{(0,w.dM)(`Copied line ${I}.`);let e=(0,b.kq)(document.getElementById(`LC${I}`));e&&(0,p.z)(e),z(!1)},ref:B,className:C,children:"Copy line"}),I!==Z&&(0,a.jsx)(u.S.Item,{onClick:()=>{(0,w.dM)(`Copied lines ${I}-${Z}.`);let e="";for(let t=I;t<=Z;t++)e+=`${(0,b.kq)(document.getElementById(`LC${t}`))}${t!==Z?"\n":""}`;e&&(0,p.z)(e),z(!1)},onSelect:()=>{(0,w.dM)(`Copied lines ${I}-${Z}.`);let e="";for(let t=I;t<=Z;t++)e+=`${(0,b.kq)(document.getElementById(`LC${t}`))}${t!==Z?"\n":""}`;e&&(0,p.z)(e),z(!1)},className:C,children:"Copy lines"}),K&&(0,a.jsx)(u.S.Item,{onClick:()=>{(0,w.dM)("Copied permalink."),(0,p.z)(K),z(!1)},onSelect:()=>{(0,w.dM)("Copied permalink."),(0,p.z)(K),z(!1)},children:"Copy permalink"}),(0,a.jsx)(u.S.LinkItem,{href:W({action:"blame",hash:Q}),children:"View git blame"}),P&&K&&(0,a.jsx)(u.S.LinkItem,{href:`${P}?permalink=${Y}`,children:"Reference in new issue"}),O&&K&&(0,a.jsx)(u.S.LinkItem,{href:`${O}?permalink=${Y}`,children:"Reference in new discussion"}),_&&(0,a.jsx)(u.S.LinkItem,{href:_+window.location.pathname.substring(1),children:"View file in GitHub.dev"}),I===Z&&n&&(0,a.jsxs)(u.S.Item,{onClick:U,onSelect:U,children:[n.ownedSection&&n.ownedSection.collapsed?"Expand":"Collapse"," current section"]}),(0,a.jsxs)(u.S.Item,{onClick:G,onSelect:G,children:["View file in different branch/tag",(0,a.jsx)(u.S.TrailingVisual,{children:(0,a.jsx)(v.I,{children:A.text})})]})]})})]}),J=document.getElementById(N);return J?(0,m.createPortal)(X,J):null}try{(r=k).displayName||(r.displayName="HighlightedLineMenuContainer")}catch{}try{(i=S).displayName||(i.displayName="HighlightedLineMenu")}catch{}try{(o=R).displayName||(o.displayName="HighlightedLineMenuWithRef")}catch{}},13570:(e,t,n)=>{n.d(t,{R:()=>s,o:()=>l});var r,i=n(85893),o=n(11117),a=n(97011);function s({symbols:e,focusedSymbol:t,lineNumber:n,sx:r,isNotUsingWhitespace:s}){let c=0,d=`overlay-${n}-${r?"blob":"panel"}`,u=e.length>0?e[0].bodyText:"",h=(0,o.O)().codeWrappingOption,m=e.length>0&&!s?e[0].leadingWhitespace??0:0;return(0,i.jsxs)(a.Z,{sx:{mb:"-20px",color:"transparent",position:"absolute",overflowWrap:h.enabled?"anywhere":"unset",maxWidth:h.enabled?"100%":"unset",maxHeight:"6rem",overflow:"hidden",width:"100%",display:"inline-block",userSelect:"none",...r},children:[e.map(e=>{let n=(0,i.jsxs)("span",{children:[(0,i.jsx)(a.Z,{sx:{userSelect:"none",visibility:"hidden"},children:u.substring(c,e.ident.start.column+m)}),(0,i.jsx)(a.Z,{sx:{bg:e===t?"#ff9632":"attention.muted",zIndex:e===t?10:void 0,color:e===t?"black":void 0,position:e===t?"relative":void 0,userSelect:"none",pointerEvents:"none"},children:(0,i.jsx)(a.Z,{sx:{visibility:e!==t?"hidden":void 0},id:l(e.lineNumber,e.ident.start.column+m),children:u.substring(e.ident.start.column+m,e.ident.end.column+m)})})]},`symbol-${e.ident.start.line}-${e.ident.start.column+m}`);return c=e.ident.end.column+m,n}),(0,i.jsx)(a.Z,{sx:{visibility:"hidden",userSelect:"none"},children:u.substring(c)})]},d)}function l(e,t){return`match-${e}-${t}`}try{(r=s).displayName||(r.displayName="HighlightedOverlay")}catch{}},18643:(e,t,n)=>{n.d(t,{I:()=>s});var r,i=n(85893),o=n(42483),a=n(73290);function s(){return(0,i.jsx)(i.Fragment,{children:(0,i.jsxs)(o.Z,{sx:{mt:2,mx:3,mb:"12px",fontSize:0,alignItems:"center"},children:[(0,i.jsx)(a.Z,{href:"https://docs.github.com/repositories/working-with-files/using-files/navigating-code-on-github",target:"_blank",children:"Documentation"}),"\xa0\u2022\xa0",(0,i.jsx)(a.Z,{href:"https://github.com/orgs/community/discussions/54546",target:"_blank",children:"Share feedback"})]})})}try{(r=s).displayName||(r.displayName="CodeViewFooter")}catch{}},36218:(e,t,n)=>{n.r(t),n.d(t,{FileResultRow:()=>K,default:()=>q});var r,i,o,a,s,l,c,d=n(85893),u=n(32769),h=n(95628),m=n(78212),f=n(60348),p=n(69942),x=n(68203),y=n(22114),g=n(13275),b=n(85529),j=n(17840),w=n(42483),v=n(51461),N=n(52516),C=n(73290),k=n(2708),S=n(74121),I=n(98833),R=n(97011),Z=n(47142),E=n(67294),T=n(43811),L=n(66523),B=n(80624),_=n(5262),D=n(35499),F=n(15205);let O=(0,F.Z)(Z.Gs);function P({data:e}){let{query:t,baseList:n,startTime:r}=e,i=t.replaceAll("\\",""),o=n.filter(e=>A(e,i)).sort((e,t)=>O(i,t)-O(i,e));return{query:t,list:o,baseCount:n.length,startTime:r}}function A(e,t){return""===t||(0,Z.CD)(t,e)&&O(t,e)>0}var M=n(18643),H=n(17206),$=n(89445);async function W(e){let t=await (0,$.v)(e);return t.ok?await t.json():void 0}let z=new Map,U=(0,F.Z)(W,{cache:z});function G(e){let t=(0,u.H)(),{refInfo:n}=(0,B.Br)(),[r,i]=(0,E.useState)({list:[],directories:[],loading:!0}),o=(0,m.a_)({repo:t,commitOid:n.currentOid,includeDirectories:!0});return(0,E.useEffect)(()=>{let t=!1,n=async()=>{i({list:[],directories:[],loading:!0});let e=await U(o);if(t)return;let n=e?.paths||[],r=e?.directories||[],a=n.concat(r).sort();i({list:a,directories:r,error:!e})};return e&&n(),function(){t=!0}},[o,e]),r}function q({onRenderRow:e,onItemSelected:t,onFeedbackLinkClick:n,searchBoxRef:r,isOverview:i,onFindFilesShortcut:o,sx:a={},...s}){let{query:l,setQuery:c}=(0,L.aM)(),f=(0,u.H)(),b=E.useRef(null),S=r??b,[I,R]=E.useState(l.length>0),[Z,T]=E.useState(!!l),{list:F,directories:O,loading:P,error:A}=G(I),{path:$}=(0,B.Br)(),{findFileWorkerPath:W}=(0,B.Ou)(),{getUrl:z}=(0,D.B)(),{queryText:U,queryLine:q}=en(l),{matches:K,clearMatches:Y}=et(F,U,W,I),{sendRepoClickEvent:Q}=(0,_.a)(),J=(0,x.s)(),[ee,er]=E.useState(0),[ei,eo]=E.useState((0,H.$)()),ea=E.useRef(null),es=E.useRef(null),el="file-results-list",{sendRepoKeyDownEvent:ec}=(0,_.a)(),{screenSize:ed}=(0,p.eI)(),eu=s.useOverlay||ed>=p._G.large,eh=E.useCallback(()=>{Q("FILE_TREE.SEARCH_RESULT_CLICK"),t?.(),T(!1)},[Q,t]),{containerRef:em}=(0,j.v)({bindKeys:y.Qw.ArrowVertical|y.Qw.HomeAndEnd,focusInStrategy:"previous"},[P,A]);E.useEffect(()=>{l||T(!1)},[l]),E.useEffect(()=>{document.activeElement!==S.current&&eu&&T(!1)},[$,S,eu]);let ef=K?.slice(0,40)||[],ep=K&&K.length>ef.length,ex=e=>{let{key:t,shiftKey:n,metaKey:r,altKey:i,ctrlKey:o}=e;if(!n&&!r&&!i){if("Escape"===t)l?(ec("FILE_TREE.CANCEL_SEARCH"),c(""),Y()):document.activeElement&&document.activeElement.blur();else if(!l)return;else if("Enter"===t)ep&&ee===ef.length?J((0,m.mY)({owner:f.ownerLogin,repo:f.name,searchTerm:`path:${U}`})):ef[ee]&&(J(z({path:ef[ee],action:"blob",hash:q?`L${q}`:""})),T(!1));else if("ArrowDown"===t||o&&"n"===t){if(ep&&ee>=ef.length-1){if(er(ef.length),ea.current&&em.current){let e=(0,h.M)(em.current);(0,g.z)(ea.current,e,{behavior:"instant"})}}else er(Math.min(ee+1,ef.length-1));e.preventDefault();return}else if("ArrowUp"===t||o&&"p"===t){er(Math.max(ee-1,0)),e.preventDefault();return}}},ey=(0,d.jsx)(w.Z,{sx:{maxHeight:eu?"60vh":"100% !important",overflowY:"auto",scrollbarGutter:"stable",maxWidth:"100vw","@media (max-width: 768px)":{ml:3,mr:2}},children:A?(0,d.jsx)(v.Z,{variant:"danger",sx:{m:3},children:"Failed to search"}):(0,d.jsxs)(N.S,{ref:em,sx:{overflow:"auto",p:eu?2:3,pl:eu?2:i?0:3,width:"100%",pr:eu?3:0,pt:eu?3:"2px !important"},role:"listbox",children:[!P&&ef.map((t,n)=>{let r=O.includes(t);return(0,d.jsx)(X,{active:t===$,index:n,focused:ei&&ee===n,match:t,onRender:e,query:U,onClick:eh,isDirectory:r,to:z({path:t,action:r?"tree":"blob",hash:q?`L${q}`:""}),useOverlay:eu,listRef:em},t)}),(0,d.jsxs)(w.Z,{sx:{m:3,textAlign:"center"},children:[(0,d.jsx)(V,{loading:P||!K,visibleResultCount:ef.length,truncated:!!ep}),ep&&(0,d.jsxs)(d.Fragment,{children:["\xa0",(0,d.jsx)(C.Z,{id:"see-all-results-link",className:"focus-visible",ref:ea,href:(0,m.mY)({owner:f.ownerLogin,repo:f.name,searchTerm:`path:${U}`}),sx:ei&&ee===ef.length?{outline:"2px solid var(--focus-outlineColor, var(--color-accent-fg))",outlineOffset:"-2px",boxShadow:"none"}:{},children:"See all results"})]})]})]})});return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(w.Z,{ref:es,sx:{mx:2,ml:i?0:3,...a},children:(0,d.jsx)(H.f,{ariaActiveDescendant:(!eu&&l||eu&&Z)&&ei&&ee>-1?ep&&ee===ef.length?"see-all-results-link":`file-result-${ee}`:void 0,ariaExpanded:eu?Z:void 0,ariaHasPopup:eu,ariaControls:eu?el:void 0,ref:S,query:l,onKeyDown:ex,onPreload:()=>R(!0),onSearch:e=>{c(e),e?T(!0):(Y(),T(!1)),er(0)},onBlur:e=>{em.current?.contains(e.relatedTarget)||(T(!1),eo(!1))},onFocus:()=>{l&&T(!0),eo(!0)},onFindFilesShortcut:o,sx:{minWidth:"200px"}})}),eu&&(0,d.jsx)(k.w,{anchorRef:es,open:eu&&Z,renderAnchor:null,onClose:()=>{T(!1)},focusZoneSettings:{disabled:!0},focusTrapSettings:{disabled:!0},width:"xlarge",align:"end",overlayProps:{id:el,role:"dialog"},children:ey}),!eu&&l&&(0,d.jsxs)(d.Fragment,{children:[ey,n&&(0,d.jsx)(w.Z,{sx:{"@media (min-height: 600px) and (min-width: 768px)":{display:"none"}},children:(0,d.jsx)(M.I,{})})]})]})}function V({visibleResultCount:e,truncated:t,loading:n}){return(0,d.jsx)("span",{role:"status",className:n||t||0===e?void 0:"sr-only","aria-label":n?"Loading":void 0,children:n?(0,d.jsx)(S.Z,{size:"large"}):0===e?"No matches found":t?`First ${e} files shown.`:`Showing ${e} files.`},"results-count-status")}let K=({active:e,focused:t,index:n,match:r,query:i,to:o,isDirectory:a,onClick:s,onRender:l,useOverlay:c,listRef:u})=>{let m=(0,Z.m7)(i,r);l?.();let p=E.useRef(null),x=a?Y:Q;E.useEffect(()=>{if(t&&p.current&&u?.current){let e=(0,h.M)(u.current);(0,g.z)(p.current,e,{behavior:"instant"})}},[t,u]);let y={};return t&&(y={outline:"none",border:"2 solid",boxShadow:"0 0 0 2px #0969da"}),(0,d.jsx)(N.S.Item,{id:`file-result-${n}`,ref:p,as:f.r,onClick:s,to:o,active:e,sx:{fontWeight:"normal",":hover":{textDecoration:"none"},mx:"2px",width:"calc(100% - 4px)",...y},role:"option","data-focus-visible-added":t||void 0,tabIndex:c?-1:0,children:(0,d.jsxs)(w.Z,{sx:{display:"flex"},children:[(0,d.jsx)(w.Z,{sx:{display:"flex",flexDirection:"column",overflow:"hidden",flexGrow:1},children:(0,d.jsx)(J,{text:r,positionsList:m,sx:{color:"fg.muted"},LeadingIcon:x})}),t&&(0,d.jsx)(w.Z,{sx:{pl:1,whiteSpace:"nowrap",color:"fg.muted"},children:`Go to ${a?"folder":"file"}`})]})},r)},Y=()=>(0,d.jsx)(I.Z,{"aria-label":"Directory",icon:b.FileDirectoryFillIcon,sx:{color:"var(--color-icon-directory)",mr:2},size:"small"}),Q=()=>(0,d.jsx)(I.Z,{"aria-label":"File",icon:b.FileIcon,sx:{color:"fg.muted",mr:2},size:"small"}),X=E.memo(K);function J({text:e,positionsList:t,sx:n,LeadingIcon:r}){let i=[],o=0;for(let n of t){if(Number(n)!==n||n<o||n>e.length)continue;let t=e.slice(o,n);t&&i.push(ee(t)),o=n+1,i.push((0,d.jsx)(R.Z,{as:"mark",sx:{fontWeight:"bold",background:"none",color:"fg.default"},children:e[n]},n))}return i.push(ee(e.slice(o))),(0,d.jsx)(w.Z,{sx:n,children:(0,d.jsxs)(d.Fragment,{children:[r&&(0,d.jsx)(r,{}),i]})})}function ee(e){return e.replaceAll("/","/\u200B")}function et(e,t,n,r){let[i,o]=E.useState(),a=E.useRef(""),s=E.useRef(),{sendStats:l}=(0,_.a)(),c=E.useRef(!1),d=E.useCallback(()=>{let e=new T.V(n,P);e.onmessage=({data:e})=>{c.current=!1,o(e.list),a.current=e.query,e.startTime&&l("repository.find-file",{"find-file-base-count":e.baseCount,"find-file-results-count":e.list.length,"find-file-duration-ms":performance.now()-e.startTime})},s.current=e},[l,n]);return E.useEffect(()=>{if(r)return d(),function(){s.current?.terminate()}},[d,r]),E.useEffect(()=>{if(e.length&&t){c.current&&(s.current?.terminate(),d());let n=a.current&&t.startsWith(a.current);c.current=!0,s.current?.postMessage({baseList:n&&i||e,query:t,startTime:performance.now()})}},[e,t,d]),{matches:i,clearMatches:()=>o(void 0)}}function en(e){e=e.replaceAll(" ","");let t=e.indexOf(":");return t>=0?{queryText:e.substring(0,t),queryLine:parseInt(e.substring(t+1),10)}:{queryText:e,queryLine:void 0}}try{(r=q).displayName||(r.displayName="FileResultsList")}catch{}try{(i=V).displayName||(i.displayName="FileResultsStatus")}catch{}try{(o=K).displayName||(o.displayName="FileResultRow")}catch{}try{(a=Y).displayName||(a.displayName="DirectoryIcon")}catch{}try{(s=Q).displayName||(s.displayName="FileResultIcon")}catch{}try{(l=X).displayName||(l.displayName="MemoizedFileResultRow")}catch{}try{(c=J).displayName||(c.displayName="HighlightMatch")}catch{}},17206:(e,t,n)=>{n.d(t,{$:()=>g,f:()=>x});var r,i=n(85893),o=n(46263),a=n(86283),s=n(85529),l=n(51526),c=n(42483),d=n(67294),u=n(22554),h=n(31174),m=n(5262),f=n(13816),p=n(90342);let x=d.forwardRef(({ariaActiveDescendant:e,ariaControls:t,ariaExpanded:n,ariaHasPopup:r,onBlur:a,onFocus:h,onKeyDown:f,onPreload:p,onSearch:x,query:b,onFindFilesShortcut:j,sx:w={}},v)=>{let{sendRepoClickEvent:N}=(0,m.a)(),[C,k]=d.useState(b),S=d.useRef((0,o.D)(e=>x(e),250));d.useEffect(()=>{k(b)},[b]);let I=b?(0,i.jsx)(l.Z.Action,{onClick:()=>{N("FILE_TREE.CANCEL_SEARCH"),x("")},icon:s.XCircleFillIcon,"aria-label":"Clear",sx:{color:"fg.subtle"}}):void 0;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(u.I,{children:(0,i.jsx)(y,{inputRef:v,onFindFilesShortcut:j})}),(0,i.jsx)(l.Z,{autoFocus:g(),ref:v,value:C,onKeyDown:f,onChange:e=>{k(e.target.value),p(),S.current(e.target.value)},sx:{display:"flex",...w},"aria-label":"Go to file","aria-activedescendant":e,role:r?"combobox":void 0,"aria-controls":t,"aria-expanded":n,"aria-haspopup":r?"dialog":void 0,autoCorrect:"off",spellCheck:"false",placeholder:"Go to file",leadingVisual:s.SearchIcon,trailingAction:I,trailingVisual:I?void 0:()=>(0,i.jsx)(u.I,{children:(0,i.jsx)(c.Z,{sx:{mr:"-6px"},children:(0,i.jsx)("kbd",{children:"t"})})}),onFocus:e=>{p(),e.target.select(),h?.(e)},onBlur:a,onClick:()=>N("FILE_TREE.SEARCH_BOX")})]})});function y({inputRef:e,onFindFilesShortcut:t}){let{sendRepoKeyDownEvent:n}=(0,m.a)(),{findFilesShortcut:r}=(0,h.bx)();return(0,i.jsx)(p.P,{buttonFocusId:f.KG,buttonHotkey:r.hotkey,onButtonClick:()=>{t?.(),e.current?.focus(),n("GO_TO_FILE")}})}function g(){let e=new URLSearchParams(a.ssrSafeLocation.search);return"1"===e.get("search")}x.displayName="FilesSearchBox";try{(r=y).displayName||(r.displayName="FindFilesShortcut")}catch{}},64071:(e,t,n)=>{n.d(t,{Is:()=>h,KG:()=>c,Lr:()=>d,QV:()=>u});var r,i,o=n(85893),a=n(67294),s=n(89250);let l=(0,a.createContext)({banners:[],addBanner:()=>void 0,addQueuedBanner:()=>void 0});function c(){return(0,a.useContext)(l).banners}function d(){return(0,a.useContext)(l).addBanner}function u(){return(0,a.useContext)(l).addQueuedBanner}function h({children:e}){let t=(0,s.TH)(),[n,r]=(0,a.useState)([]),[i,c]=(0,a.useState)([]),d=(0,a.useCallback)(e=>r(t=>[...t,e]),[]),u=(0,a.useCallback)(e=>c(t=>[...t,e]),[]),h=(0,a.useMemo)(()=>({banners:n,addBanner:d,addQueuedBanner:u}),[d,u,n]);return(0,a.useEffect)(()=>{r(i),c([])},[t.key]),(0,o.jsx)(l.Provider,{value:h,children:e})}try{(r=l).displayName||(r.displayName="CodeViewBannersContext")}catch{}try{(i=h).displayName||(i.displayName="CodeViewBannersProvider")}catch{}},74176:(e,t,n)=>{n.d(t,{Y:()=>d,o:()=>u});var r,i,o=n(85893),a=n(46263),s=n(69942),l=n(67294);let c=(0,l.createContext)(s._G.small);function d({children:e,initialValue:t,contentRef:n}){let[r,i]=(0,l.useState)(t||s._G.large);return(0,l.useEffect)(()=>{if(!n)return;i((0,s.ZV)(n.offsetWidth));let e=new ResizeObserver((0,a.D)(()=>{i((0,s.ZV)(n.offsetWidth))}));return e.observe(n),()=>e.disconnect()},[i,n]),(0,o.jsx)(c.Provider,{value:r,children:e})}function u(){return(0,l.useContext)(c)}try{(r=c).displayName||(r.displayName="ContentSizeContext")}catch{}try{(i=d).displayName||(i.displayName="ContentSizeProvider")}catch{}},51188:(e,t,n)=>{n.d(t,{Ez:()=>f,Uc:()=>u,nO:()=>h});var r,i,o=n(85893),a=n(78212),s=n(89445),l=n(67294);let c={showLicenseMeta:!1,license:null,codeownerInfo:{codeownerPath:null,ownedByCurrentUser:null,ownersForFile:null,ruleForPathLine:null},newDiscussionPath:null,newIssuePath:null},d=l.createContext(c);function u({children:e,...t}){return(0,o.jsx)(d.Provider,{value:t,children:e})}function h(){return l.useContext(d)}function m(e){return"boolean"==typeof e.showLicenseMeta&&"object"==typeof e.codeownerInfo}function f(e,t,n,r){let[i,o]=(0,l.useState)(c),d=t&&!r?(0,a.zh)({repo:e,commitish:t.name,path:n}):null;return(0,l.useEffect)(()=>{if(!d)return;let e=!1,t=async()=>{o(c);let t=await (0,s.v)(d);if(!e)try{if(t.ok){let e=await t.json();e&&m(e)&&o(e)}else o(c)}catch(e){o(c)}};return t(),function(){e=!0}},[d]),i}try{(r=d).displayName||(r.displayName="DeferredMetadataContext")}catch{}try{(i=u).displayName||(i.displayName="DeferredMetadataProvider")}catch{}},66523:(e,t,n)=>{n.d(t,{aM:()=>l,ve:()=>c});var r,i,o=n(85893),a=n(67294);let s=(0,a.createContext)({query:"",setQuery:()=>void 0});function l(){return(0,a.useContext)(s)}function c({children:e}){let[t,n]=(0,a.useState)(""),r=(0,a.useMemo)(()=>({query:t,setQuery:n}),[n,t]);return(0,o.jsx)(s.Provider,{value:r,children:e})}try{(r=s).displayName||(r.displayName="FileQueryContext")}catch{}try{(i=c).displayName||(i.displayName="FileQueryProvider")}catch{}},53467:(e,t,n)=>{n.d(t,{K:()=>l,n:()=>c});var r,i,o=n(85893),a=n(67294);let s=a.createContext(!0);function l({allShortcutsEnabled:e,children:t}){return(0,o.jsxs)(s.Provider,{value:e,children:[" ",t," "]})}function c(){return a.useContext(s)}try{(r=s).displayName||(r.displayName="AllShortcutsEnabledContext")}catch{}try{(i=l).displayName||(i.displayName="AllShortcutsEnabledProvider")}catch{}},73690:(e,t,n)=>{n.d(t,{M:()=>l,Q:()=>c});var r,i,o=n(85893),a=n(67294);let s=a.createContext(void 0);function l({blame:e,children:t}){return(0,o.jsxs)(s.Provider,{value:e,children:[" ",t," "]})}function c(){return a.useContext(s)}try{(r=s).displayName||(r.displayName="CurrentBlameContext")}catch{}try{(i=l).displayName||(i.displayName="CurrentBlameProvider")}catch{}},17391:(e,t,n)=>{n.d(t,{G:()=>c,d:()=>l});var r,i,o=n(85893),a=n(67294);let s=a.createContext({});function l({blob:e,children:t}){return(0,o.jsxs)(s.Provider,{value:e,children:[" ",t," "]})}function c(){return a.useContext(s)}try{(r=s).displayName||(r.displayName="CurrentBlobContext")}catch{}try{(i=l).displayName||(i.displayName="CurrentBlobProvider")}catch{}},84312:(e,t,n)=>{n.d(t,{$L:()=>m,S6:()=>d,Ub:()=>h,k:()=>u});var r,i,o=n(85893),a=n(67294),s=n(73690),l=n(17391);let c=a.createContext(null);function d({children:e}){let t=(0,l.G)(),n=(0,s.Q)(),r=a.useMemo(()=>n?null:new Map,[t,n]);return(0,o.jsxs)(c.Provider,{value:r,children:[" ",e," "]})}function u(){return a.useContext(c)}function h(){let e=a.useContext(c);return(0,a.useCallback)(t=>e?.get(t),[e])}function m(){let e=a.useContext(c);return(0,a.useCallback)((t,n)=>{e&&(e.has(t)?e.get(t)?.push(n):e.set(t,[n]))},[e])}try{(r=c).displayName||(r.displayName="CurrentLineRefMapContext")}catch{}try{(i=d).displayName||(i.displayName="CurrentLineRefMapProvider")}catch{}},80624:(e,t,n)=>{n.d(t,{Br:()=>d,Ou:()=>u,Tv:()=>c});var r,i,o=n(85893),a=n(17891),s=n(67294);let l=s.createContext({});function c({children:e,...t}){return(0,o.jsx)(l.Provider,{value:t,children:e})}function d(){return s.useContext(l)}function u(){return(0,a.M)()}try{(r=l).displayName||(r.displayName="FilesPageInfoContext")}catch{}try{(i=c).displayName||(i.displayName="FilesPageInfoProvider")}catch{}},31360:(e,t,n)=>{n.d(t,{T:()=>c,c:()=>l});var r,i,o=n(85893),a=n(67294);let s=a.createContext({});function l({refreshTree:e,children:t}){return(0,o.jsxs)(s.Provider,{value:e,children:[" ",t," "]})}function c(){return a.useContext(s)}try{(r=s).displayName||(r.displayName="RefreshTreeContext")}catch{}try{(i=l).displayName||(i.displayName="RefreshTreeProvider")}catch{}},54094:(e,t,n)=>{n.d(t,{Z:()=>di});var r,i,o,a,s,l,c,d,u,h,m,f,p,x,y,g,b,j,w,v,N,C,k,S,I,R,Z,E,T,L,B,_,D,F,O,P,A,M,H,$,W,z,U,G,q,V,K,Y,Q,X,J,ee,et,en,er,ei,eo,ea,es,el,ec,ed,eu,eh,em,ef,ep,ex,ey,eg,eb,ej,ew,ev,eN,eC,ek,eS,eI,eR,eZ,eE,eT,eL,eB,e_,eD,eF,eO,eP,eA,eM,eH,e$,eW,ez,eU,eG,eq,eV,eK,eY,eQ,eX,eJ,e0,e1,e2,e3,e4,e8,e6,e5,e9,e7,te,tt,tn,tr,ti,to,ta,ts,tl,tc,td,tu,th,tm,tf,tp,tx,ty,tg,tb,tj,tw,tv,tN,tC,tk,tS,tI,tR,tZ,tE,tT,tL,tB,t_,tD,tF,tO,tP,tA,tM,tH,t$,tW,tz,tU,tG,tq,tV,tK,tY,tQ,tX,tJ,t0,t1,t2,t3,t4,t8,t6,t5,t9,t7,ne,nt,nn,nr,ni,no,na,ns,nl,nc,nd,nu,nh,nm,nf,np,nx,ny,ng,nb,nj,nw,nv,nN,nC,nk,nS,nI,nR,nZ,nE,nT,nL,nB,n_,nD,nF,nO,nP,nA,nM,nH,n$,nW,nz,nU,nG,nq,nV,nK,nY=n(85893),nQ=n(57446),nX=n(32769),nJ=n(78212),n0=n(67294),n1=n(88479);function n2(){return(0,n0.useContext)(n1.h)}var n3=n(73968),n4=n(69942),n8=n(86283),n6=n(11117),n5=n(99550);function n9(){(0,n5.p)(!1),(0,n0.useEffect)(()=>()=>(0,n5.p)(!0),[])}var n7=n(42483),re=n(75308),rt=n(81248),rn=n(67404);function rr(e){let t=(0,n0.useRef)([]);for(let n of t.current)if(e===n||ri(n,e))return n;return t.current.unshift(e),t.current.length>5&&t.current.pop(),e}function ri(e,t){if(e===t)return!0;if("object"!=typeof e||typeof e!=typeof t||!e||!t)return!1;if(Array.isArray(e)){if(!Array.isArray(t)||e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(!ri(e[n],t[n]))return!1;return!0}let n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(let r of n)if(!ri(e[r],t[r]))return!1;return!0}var ro=n(90874),ra=n(46263),rs=n(85529),rl=n(50919),rc=n(51526),rd=n(98833),ru=n(97011),rh=n(41982),rm=n(87738),rf=n(22554),rp=n(57941),rx=n(5262),ry=n(79902),rg=n(81455);function rb({symbolKind:e,showFullSymbol:t}){return e?(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",position:"relative",...t?{}:{mr:2}},children:[(0,nY.jsx)(n7.Z,{sx:{backgroundColor:e.plColor,opacity:.1,position:"absolute",borderRadius:5,alignItems:"stretch",display:"flex",width:"100%",height:"100%"}}),(0,nY.jsx)(n7.Z,{sx:{color:e.plColor,borderRadius:5,fontWeight:600,...t?{fontSize:"small",px:2,py:"1px",mt:"2px"}:{fontSize:"smaller",px:1,py:"1px"}},children:t?e.fullName:e.shortName})]}):null}try{(r=rb).displayName||(r.displayName="SymbolIndicator")}catch{}let rj=n0.memo(function({treeSymbols:e,onTreeSymbolSelect:t}){let n=!e.some(e=>e.isParent||e.children.length>0);return(0,nY.jsx)(n7.Z,{sx:{mb:-2,overflowY:"auto",maxHeight:"calc(100vh - 237px)",pl:3,pb:2,pt:1},children:(0,nY.jsx)(rg.L,{"aria-label":"Code Navigation",flat:n,children:e.map((e,n)=>(0,nY.jsx)(rv,{id:`${n}${e.symbol.name}`,symbol:e,depth:e.isParent?1:2,onSelect:t},`${n}${e.symbol.name}`))})})});function rw({symbol:e}){return(0,nY.jsxs)(n7.Z,{sx:{display:"flex"},children:[(0,nY.jsx)(rb,{symbolKind:e.symbol.kind}),"  ",(0,nY.jsx)(ry.Z,{title:e.symbol.name,sx:{maxWidth:180,display:"block"},children:(0,nY.jsx)(ru.Z,{children:e.symbol.name})})]})}function rv({symbol:e,depth:t,onSelect:n,id:r}){let[i,o]=(0,n0.useState)(t<=7);return(0,nY.jsxs)(rg.L.Item,{onSelect:()=>n(e.symbol),expanded:i,onExpandedChange:()=>o(!i),id:r,children:[(0,nY.jsx)(rw,{symbol:e}),e.isParent&&e.children.length>0&&(0,nY.jsx)(rg.L.SubTree,{children:e.children.map((e,r)=>(0,nY.jsx)(rv,{symbol:e,depth:e.isParent?t+1:t,onSelect:n,id:`${r}${e.symbol.name}`},`${r}${e.symbol.name}`))})]})}try{(i=rj).displayName||(i.displayName="CodeNavSymbolTree")}catch{}try{(o=rw).displayName||(o.displayName="CodeNavTreeContent")}catch{}try{(a=rv).displayName||(a.displayName="CodeNavTreeItem")}catch{}var rN=n(15345),rC=n(22114),rk=n(17840),rS=n(52516),rI=n(79515),rR=n(73290);let rZ=n0.memo(function({symbol:e,filterText:t,onSelect:n,focused:r,index:i}){return(0,nY.jsx)(rS.S.Item,{role:"option",id:`jump-to-item-${i}`,"aria-selected":r,sx:{minWidth:0,...r?{backgroundColor:"var(--color-canvas-subtle) !important"}:{}},onSelect:()=>n(e),children:(0,nY.jsx)(rR.Z,{href:e.href(),sx:{":hover":{textDecoration:"none"}},children:(0,nY.jsxs)(n7.Z,{style:{display:"flex"},children:[(0,nY.jsx)(rb,{symbolKind:e.kind}),"  ",(0,nY.jsx)(n7.Z,{style:{display:"flex",minWidth:0,alignItems:"flex-end"},children:(0,nY.jsx)(rI.h,{search:t,text:e.name,overflowWidth:175,hideOverflow:!0},e.fullyQualifiedName)})]})})})});try{(s=rZ).displayName||(s.displayName="JumpToItem")}catch{}function rE(e){return(0,nY.jsx)(rT,{...e})}function rT({codeSymbols:e,filterText:t,onSelect:n,focusedIndex:r}){let i=(0,n0.useRef)(e.length),o=(0,n0.useRef)(""),{containerRef:a}=(0,rk.v)({bindKeys:rC.Qw.ArrowVertical|rC.Qw.HomeAndEnd});return(0,n0.useEffect)(()=>{e.length===i.current&&(o.current+="\u200B");let t=1===e.length?"symbol":"symbols";(0,rN.x)(`${e.length} ${t} found${o.current}`),i.current=e.length},[e]),(0,nY.jsx)(rS.S,{ref:a,role:"listbox",id:"filter-results","aria-orientation":"vertical",sx:{maxHeight:"68vh",overflowY:"auto"},children:e.map((e,i)=>{let{name:o,lineNumber:a}=e;return(0,nY.jsx)(rZ,{symbol:e,filterText:t,onSelect:n,focused:i===r,index:i},`${o}_${a}`)})})}try{(l=rE).displayName||(l.displayName="JumpToItemList")}catch{}try{(c=rT).displayName||(c.displayName="FullJumpToItemList")}catch{}function rL({filterText:e,isFindInFile:t}){let[n,r]=(0,n0.useState)(t?"No matches found":"No symbols found"),i=(0,nX.H)(),o=(0,n0.useRef)(!0);return(0,n0.useEffect)(()=>{if(o.current){o.current=!1;return}r(`${n}\u200B`)},[e]),(0,nY.jsxs)(n7.Z,{sx:{justifyContent:"center",alignItems:"center",display:"flex",flexDirection:"column",pb:2},children:[""===e&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(n7.Z,{sx:{bg:"canvas.subtle",borderRadius:6,p:"16px"},children:(0,nY.jsxs)(n7.Z,{sx:{textAlign:"center"},children:[(0,nY.jsx)(re.Z,{as:"h3",sx:{fontSize:0,marginBottom:"4px"},children:"Symbol outline not available for this file"}),(0,nY.jsx)(n7.Z,{sx:{justifyContent:"center",alignItems:"center",display:"flex",fontSize:"12px",color:"fg.muted"},children:"To inspect a symbol, try clicking on the symbol directly in the code view."})]})}),(0,nY.jsxs)(n7.Z,{sx:{mt:"8px",fontSize:0,textAlign:"center",color:"fg.muted"},children:[" ","Code navigation supports a limited number of languages."," ",(0,nY.jsx)(rR.Z,{href:"https://docs.github.com/repositories/working-with-files/using-files/navigating-code-on-github",children:"See which languages are supported."})]})]}),e&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rd.Z,{icon:rs.SearchIcon,size:24}),(0,nY.jsx)(ru.Z,{as:"h3",sx:{textAlign:"center",fontWeight:600,fontSize:3,py:2},role:"alert","aria-relevant":"all",children:n})]}),e&&(0,nY.jsxs)(ru.Z,{sx:{textAlign:"center",px:3,mt:2,fontSize:0,color:"fg.subtle"},children:["No lines in this file contain that string.",(0,nY.jsx)("br",{}),"Search in"," ",(0,nY.jsxs)(rR.Z,{href:(0,nJ.mY)({owner:i.ownerLogin,repo:i.name,searchTerm:e}),children:[i.ownerLogin,"/",i.name]})," ","or ",(0,nY.jsx)(rR.Z,{href:(0,nJ.GX)({searchTerm:e}),children:"all of GitHub"})]})]})}try{(d=rL).displayName||(d.displayName="SymbolZeroState")}catch{}let rB="symbols-pane-header";function r_({codeSymbols:e,onSymbolSelect:t,treeSymbols:n,autoFocusSearch:r,onClose:i}){let[o,a]=(0,n0.useState)(""),[s,l]=(0,n0.useState)(e);return(0,n0.useEffect)(()=>{if(""===o){l(e);return}let t=rF(o,e);l(t)},[o,e]),(0,nY.jsx)(rD,{treeSymbols:n,codeSymbols:s,filterText:o,setFilterText:a,onSymbolSelect:t,autoFocusSearch:r,onClose:i})}function rD({codeSymbols:e,setFilterText:t,filterText:n,onSymbolSelect:r,treeSymbols:i,autoFocusSearch:o,onClose:a}){let s=e?.length>0,l=i.length>0,[c,d]=(0,n0.useState)(-1),{sendRepoKeyDownEvent:u}=(0,rx.a)(),h=(0,n0.useRef)(null);(0,n0.useEffect)(()=>{o&&h.current?.focus()},[o]),(0,rp.Sl)(()=>{h.current?.focus()});let m=(0,n0.useMemo)(()=>(0,ra.D)(()=>{u("BLOB_SYMBOLS_MENU.FILTER_SYMBOLS")},400),[u]);return(0,nY.jsxs)(n7.Z,{sx:{py:2,px:3},"aria-labelledby":rB,children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",justifyContent:"space-between"},children:[(0,nY.jsx)(n7.Z,{as:"h2",sx:{fontSize:1,order:1,display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center",fontWeight:600},id:rB,tabIndex:-1,children:"Symbols"}),(0,nY.jsx)(rl.h,{"aria-label":"Close symbols","data-hotkey":"Escape",icon:rs.XIcon,sx:{order:3,color:"fg.default",mr:-2},onClick:a,variant:"invisible"})]}),(s||l)&&(0,nY.jsx)(n7.Z,{sx:{fontSize:0,color:"fg.muted",pt:2},children:"Find definitions and references for functions and other symbols in this file by clicking a symbol below or in the code."}),(s||""!==n)&&(0,nY.jsx)(rc.Z,{block:!0,leadingVisual:()=>(0,nY.jsx)(rd.Z,{"aria-hidden":"true",icon:rs.FilterIcon}),ref:h,trailingAction:n?(0,nY.jsx)(rc.Z.Action,{onClick:()=>{t(""),d(-1)},icon:rs.XCircleFillIcon,"aria-label":"Clear input","data-testid":"clear-search",sx:{color:"fg.subtle"}}):(0,nY.jsx)(nY.Fragment,{}),trailingVisual:n?void 0:()=>(0,nY.jsx)(rf.I,{children:(0,nY.jsx)(n7.Z,{sx:{mr:"6px"},children:(0,nY.jsx)("kbd",{children:"r"})})}),sx:{mt:2,borderRadius:2},placeholder:"Filter symbols",value:n,name:"Filter symbols","aria-label":"Filter symbols","aria-controls":"filter-results","aria-expanded":"true","aria-autocomplete":"list","aria-activedescendant":-1===c?void 0:`jump-to-item-${c}`,onKeyDown:t=>{if("ArrowDown"===t.key||("N"===t.key||"n"===t.key)&&t.ctrlKey){let t=Math.min(c+1,e.length-1);d(t)}else"ArrowUp"===t.key||("P"===t.key||"p"===t.key)&&t.ctrlKey?d(Math.max(c-1,0)):"Enter"===t.key&&e[c]?r(e[c]):"Escape"===t.key&&a()},role:"combobox",onChange:e=>{let n=e.target;t(n.value),m(),d(-1)}}),l&&""===n&&(0,nY.jsx)(n7.Z,{sx:{ml:-3,mb:-2},children:(0,nY.jsx)(rj,{treeSymbols:i,onTreeSymbolSelect:r})}),s&&(!l||""!==n)&&(0,nY.jsx)(rE,{codeSymbols:e,filterText:n,onSelect:r,focusedIndex:c}),!s&&(!l||""!==n)&&(0,nY.jsx)(ru.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",mt:2},children:(0,nY.jsx)(rL,{filterText:n})})]})}function rF(e,t){let n=e.replace(/\s/g,""),r=e=>{let t=(0,rm.EW)(e.name,n);return t>0?{score:t,text:e.name}:null};return(0,rh.W)(t,r,rm.qu)}try{(u=r_).displayName||(u.displayName="CodeNavSymbolNavigation")}catch{}try{(h=rD).displayName||(h.displayName="JumpToActionList")}catch{}var rO=n(87054),rP=n(95253),rA=n(90342),rM=n(44544);let rH=(0,rM.Z)("localStorage"),r$=(0,n0.createContext)({openPanel:void 0,setOpenPanel:()=>void 0});function rW({children:e,payload:t,openPanelRef:n}){let r="blame"in t,i=(0,nQ.g6)(t),o=(0,ro.x)(),a=(0,n0.useRef)(),[s,l]=(0,n0.useState)(()=>{let e=rH.getItem("codeNavOpen");if(!o&&""!==e&&null!==e||o&&t.symbolsExpanded)return"codeNav"}),c=(r||i)&&"codeNav"===s?void 0:s;n0.useEffect(()=>{n.current=c},[c,n]);let d=(0,n0.useCallback)((e,t)=>{l(n=>(n&&a.current&&a.current.focus(),a.current=t,r||i&&"codeNav"===e?void 0:e))},[r,i]);rz(d);let u=(0,n0.useMemo)(()=>({openPanel:c,setOpenPanel:d}),[c,d]);return(0,nY.jsx)(r$.Provider,{value:u,children:e})}function rz(e){let{screenSize:t}=(0,n4.eI)(),n=(0,n0.useRef)(t);(0,n0.useEffect)(()=>{let r=n.current>=n4._G.large,i=n.current===t;t<n4._G.large&&(r||i)&&e(void 0),n.current=t},[t,e])}function rU(){return(0,n0.useContext)(r$)}try{(m=r$).displayName||(m.displayName="OpenPanelContext")}catch{}try{(f=rW).displayName||(f.displayName="OpenPanelProvider")}catch{}var rG=n(73690),rq=n(17391),rV=n(84312),rK=n(80624),rY=n(31174),rQ=n(92059),rX=n(68203),rJ=n(79655),r0=n(56334),r1=n(13816),r2=n(8903);function r3(e,t,n,r,i){let o=(0,rX.s)(),a=(0,nX.H)(),s=(0,ro.x)(),{refInfo:l,path:c}=(0,rK.Br)(),[d,u]=(0,n0.useState)(!1),[h]=(0,rJ.lr)(),m="1"===h.get("plain"),f=(0,n0.useMemo)(()=>{n(!0);try{return new rQ.bm(a,l,c,!!s,e.rawLines||[],e.symbols?.symbols??[],e.stylingDirectives,e.language,m,u)}catch(e){n(!1)}},[a,l,c,e,n,m,s]),[p,x]=(0,n0.useState)(()=>{let t=(0,r0.n6)(r);if(!t.blobRange?.start?.line)return{selectedText:"",lineNumber:-1,offset:0};let n=!i&&t.blobRange.start.line===t.blobRange.end.line&&null!==t.blobRange.start.column&&null!==t.blobRange.end.column&&t.blobRange.end.column-t.blobRange.start.column>2&&e.stylingDirectives&&e.stylingDirectives[t.blobRange.start.line-1]?.length&&f?.blobLines[t.blobRange.start.line-1];if(n){let n=f.blobLines[t.blobRange.start.line-1]?.substring(t.blobRange.start.column-1,t.blobRange.end.column-1),r=e.stylingDirectives[t.blobRange.start.line-1]?.find(e=>e.start===t.blobRange.start.column-1&&e.end===t.blobRange.end.column-1);return n&&r&&(0,r1.yk)(n,r.cssClass)?{selectedText:n,lineNumber:t.blobRange.start.line,offset:t.blobRange.start.column}:{selectedText:"",lineNumber:-1,offset:0}}if(!r||!f||i)return{selectedText:"",lineNumber:-1,offset:0};{let e=f.getSymbolOnLine(Number(r.substring(2)));return e?{selectedText:e.name,lineNumber:e.lineNumber,offset:e.ident.start.column}:{selectedText:"",lineNumber:-1,offset:0}}}),y=(0,n0.useCallback)(e=>{x({selectedText:e.name,lineNumber:e.lineNumber,offset:e.ident.start.column}),t(),o(e.href()),(0,r2.v)({line:e.lineNumber})},[t,o]),g=(0,n0.useCallback)(e=>{x(e),t()},[t]);return{isCodeNavLoading:d,codeNavInfo:f,showCodeNavWithSymbol:y,showCodeNavForToken:g,setSearchingText:x,searchingText:p}}let DebouncedWorkerManager=class DebouncedWorkerManager{post(e){if(this.debounceOverrideCondition&&this.debounceOverrideCondition(e))return this.delayId&&clearTimeout(this.delayId),this.postNow(e);this.idle()?(this.delayId&&clearTimeout(this.delayId),this.delayId=setTimeout(()=>{this.postNow(e)},this.delayMs)):this.nextRequest=e}postNow(e){this.currentRequest=e,this.worker.postMessage(e)}idle(){return!this.currentRequest}terminate(){this.worker.terminate()}constructor(e,t=200,n){this.worker=e,this.delayMs=t,this.debounceOverrideCondition=n,this.currentRequest=void 0,this.nextRequest=void 0,this.worker.onmessage=({data:e})=>{this.onResponse(e),this.nextRequest?(this.postNow(this.nextRequest),this.nextRequest=void 0):this.currentRequest=void 0}}};var r4=n(43811);function r8({data:e}){let{query:t,lines:n,currentCodeReferences:r}=e;return{ranges:r?(0,rQ.e7)(r,n,(0,rQ.Ny)(t)):(0,rQ.v)(n,(0,rQ.Ny)(t)),query:t}}function r6(e,t){let{findInFileWorkerPath:n}=(0,rK.Ou)(),[r,i]=(0,n0.useState)([]),[o,a]=(0,n0.useState)(void 0),[s,l]=(0,n0.useState)("done"),c=n0.useRef(),{refInfo:d,path:u}=(0,rK.Br)();!c.current&&t&&(c.current=new DebouncedWorkerManager(new r4.V(n,r8),200,e=>1!==e.query.length));let h=(0,n0.useRef)(t);h.current=t;let m=(0,n0.useRef)("");(0,n0.useEffect)(()=>function(){c.current?.terminate()},[]);let f=(0,n0.useRef)();return c.current&&e!==f.current&&(c.current.onResponse=t=>{t.query===h.current&&(a(0),i(e?.createReferences(t.ranges)||[]),l("done"),m.current=h.current)},f.current=e),(0,n0.useEffect)(()=>{if(!e||!c.current||!m.current||""===m.current){i([]),a(0),l("done");return}i([]),a(0),l("pending"),c.current.post({query:m.current,lines:e.blobLines,currentCodeReferences:void 0})},[d.name,u]),(0,n0.useEffect)(()=>{if(e&&c.current){if(""===t)i([]),a(0),l("done"),m.current="";else{if(m.current===t||!r5(t))return;l("pending");let n=m.current.length>0&&t.startsWith(m.current);c.current.post({query:t,lines:e.blobLines,currentCodeReferences:n?r:void 0})}}},[t]),{focusedSearchResult:o,setFocusedSearchResult:a,searchResults:r,setSearchResults:i,searchStatus:s}}function r5(e){return e.length>0&&e.length<=1e3}var r9=n(20286);function r7(){let[e,t]=(0,n0.useState)(new Map),n=(0,n0.useCallback)((n,r)=>{let i=!1;if(null===n&&0===e.size)return;if(null===n&&r){e.clear(),t(new Map(e));return}if(!n)return;let o=n.lineNumber;if(r&&e.has(o)?(e.delete(o),i=!0):r||e.has(o)||(e.set(o,n),i=!0),e.has(o))for(let[t]of e){let n=e.get(t);(!n.ownedSection||n.ownedSection.endLine<o||o<n.lineNumber)&&(e.delete(t),i=!0)}i&&t(new Map(e))},[]);return{currentStickyLines:e,setStickyLines:n}}var ie=n(63372),it=n(89445);function ir(){return async function(e,t){let n=new FormData;n.set("tree_view_expanded_preference",null===e?"":e?"true":"false"),n.set("symbols_view_expanded_preference",null===t?"":t?"true":"false"),(0,it.Q)("/repos/preferences",{method:"PUT",body:n,headers:{Accept:"application/json"}})}}var ii=n(86480);let io=(0,n0.createContext)({findInFileOpen:!1,setFindInFileOpen:()=>void 0});function ia({children:e,searchTerm:t,setSearchTerm:n,isBlame:r}){let i=(0,ii.nj)(r),o=(0,n0.useRef)(""),[a,s]=(0,n0.useState)(!1),l=(0,n0.useCallback)(e=>{e&&""===t&&""!==o.current?n(o.current):e||""===t||(o.current=t,n("")),s(e)},[t,n]),c=(0,n0.useMemo)(()=>({findInFileOpen:!i&&a,setFindInFileOpen:l}),[a,l,i]);return(0,nY.jsx)(io.Provider,{value:c,children:e})}function is(){return(0,n0.useContext)(io)}try{(p=io).displayName||(p.displayName="FindInFileOpenContext")}catch{}try{(x=ia).displayName||(x.displayName="FindInFileOpenProvider")}catch{}function il(){let e=(0,rq.G)(),t=(0,rG.Q)();return!e.renderedFileInfo||e.shortPath||t?e.renderImageOrRaw?e.image?y.Image:y.TooLargeError:e.csv&&!t?y.CSV:e.richText&&!t?y.Markdown:(e.issueTemplate?.structured&&e.issueTemplate.valid||e.discussionTemplate&&e.discussionTemplate.valid)&&!t&&!e.isPlain?y.IssueTemplate:y.Code:y.FileRenderer}function ic(e){throw Error(`Unexpected object: ${e}`)}!function(e){e.FileRenderer="FileRenderer",e.Image="Image",e.TooLargeError="TooLargeError",e.CSV="CSV",e.Markdown="Markdown",e.IssueTemplate="IssueTemplate",e.Code="Code"}(y||(y={}));var id=n(78806),iu=n(93062),ih=n(89250),im=n(87098),ip=n(98950),ix=n(74121);function iy(){let e="clipboard"in navigator,t="undefined"!=typeof ClipboardItem;return e&&t}async function ig(e){let t=await fetch(e,{method:"get"});if(!t.ok)throw Error(`Failed to fetch ${e}: ${t.status} ${t.statusText}`);let n=(await t.text()).replace(/\r?\n$/,"");return new Blob([n],{type:"text/plain"})}function ib(e){let t,n;switch(e){case g.Success:t="Copied!",n=(0,nY.jsx)(rd.Z,{icon:rs.CheckIcon});break;case g.Fetching:t="Copying",n=(0,nY.jsx)(ix.Z,{size:"small"});break;case g.Error:t="Something went wrong. Try again.",n=(0,nY.jsx)(rd.Z,{icon:rs.AlertIcon});break;default:t="Copy",n=(0,nY.jsx)(rd.Z,{icon:rs.CopyIcon})}return{ariaLabel:t,content:n}}function ij(){let{sendRepoClickEvent:e}=(0,rx.a)(),{rawBlobUrl:t}=(0,rq.G)(),n=(0,n0.useRef)(!1);return(0,n0.useEffect)(()=>{n.current=navigator.userAgent.toLowerCase().indexOf("firefox")>-1},[]),(0,n0.useCallback)(async()=>{e("BLOB_RAW_DROPDOWN.COPY");try{let e=ig(t);if(iy()&&navigator&&navigator.clipboard&&"write"in navigator.clipboard&&!n.current)await navigator.clipboard.write([new ClipboardItem({"text/plain":e})]);else{let t=await e;if(!t)return g.Error;await (0,ip.z)(await t.text())}}catch(e){return g.Error}return g.Success},[n,e,t])}function iw({containerRef:e,shouldNotOverrideCopy:t}){let{selectAllShortcut:n}=(0,rY.bx)(),r=(0,n0.useRef)(!1),i=ij(),o=(0,n0.useCallback)(()=>{r.current=!1,document.removeEventListener("selectionchange",o)},[]);(0,n0.useEffect)(()=>()=>document.removeEventListener("selectionchange",o),[o]);let a=(0,n0.useCallback)(t=>{e&&e.current&&(iv(e.current),t.preventDefault(),r.current=!0,setTimeout(()=>document.addEventListener("selectionchange",o),0))},[e,o]),s=(0,n0.useCallback)(e=>{r.current&&(e.preventDefault(),i())},[i]);return(0,n0.useEffect)(()=>{if(!t)return window.addEventListener("copy",s),()=>window.removeEventListener("copy",s)},[s,t]),(0,nY.jsx)("button",{hidden:!0,"data-hotkey":n.hotkey,onClick:a})}function iv(e){let t=document.createRange();t.selectNode(e);let n=window.getSelection();n?.removeAllRanges(),n?.addRange(t)}!function(e){e[e.Idle=0]="Idle",e[e.Fetching=1]="Fetching",e[e.Success=2]="Success",e[e.Error=3]="Error"}(g||(g={}));try{(b=iw).displayName||(b.displayName="SelectAllShortcutButton")}catch{}function iN({onAnchorClick:e,richText:t,stickyHeaderHeight:n,sx:r}){let{hash:i}=(0,ih.TH)(),o=(0,nX.H)(),a=(0,rX.s)(),s=(0,n0.useRef)(null);return(0,n0.useEffect)(()=>{let e=()=>{iC(window.location.hash,n)};return window.addEventListener("load",e),window.addEventListener("hashchange",e),()=>{window.removeEventListener("load",e),window.removeEventListener("hashchange",e)}},[]),(0,n0.useLayoutEffect)(()=>{s.current&&iC(window.location.hash,n)},[i]),(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(iu.wB,{ref:s,className:"js-snippet-clipboard-copy-unpositioned",html:t,sx:r,"data-hpc":!0,onClick:t=>{let r=t.metaKey||t.ctrlKey,i=t.target.closest("a");if(i&&i.href){if(!r){let e=i.href,r=new URL(e,window.location.origin);(0,id.Z)(window.location.href,e)?iC(r.hash,n):e.startsWith(`${window.location.origin}/${o.ownerLogin}/${o.name}/`)&&(a(r.pathname+r.search+r.hash),t.preventDefault())}e?.(t)}}}),(0,nY.jsx)(iw,{containerRef:s})]})}function iC(e,t=125){if(!e)return;let n=(0,im.$z)(e).toLowerCase(),r=n.startsWith("user-content-")?n:`user-content-${n}`,i=(0,im.Q)(document,r);i&&document&&document.defaultView&&window.requestAnimationFrame(()=>{let e=i.getBoundingClientRect().top-document.body.getBoundingClientRect().top-t;window.scrollTo({top:e});let n=i.closest("h1,h2,h3,h4,h5,h6,li,span");n&&(n.focus(),n.setAttribute("data-react-autofocus","true"))})}try{(j=iN).displayName||(j.displayName="MarkdownContent")}catch{}var ik=n(92562),iS=n(62073);let iI=(0,n0.createContext)([]);function iR(){return(0,n0.useContext)(iI)}try{(w=iI).displayName||(w.displayName="SplitCodeownersErrorsContext")}catch{}function iZ(){let[e,t]=(0,n0.useState)({});return(0,n0.useCallback)(()=>t({}),[])}var iE=n(24601);function iT(e,t){let n=!!(0,rG.Q)(),r=(0,ii.nj)();(0,n0.useEffect)(()=>{let i;if(n||!t||r)return;let o=(0,ra.D)(t=>{clearTimeout(i),i=setTimeout(()=>{iL(t,e)},15)},5);return window.addEventListener("mousemove",o),()=>{window.removeEventListener("mousemove",o)}},[e,n,t,r])}function iL(e,t){let n=i_(/\w+[!?]?/g,e.clientX,e.clientY);if(!n)return null;let r=n.commonAncestorContainer.parentElement;if(r)for(let e of r.classList){if(["pl-token","pl-c","pl-s","pl-k"].includes(e))return null;let r=n.toString();if(!r||r.match(/\n|\s|[();&.=",]/))return null;let{lineNumber:i,offset:o,node:a}=iB(n);if(0===i&&0===o||!a)return null;return t({lineNumber:i,offset:o,node:a})}}function iB(e){let t=e.startContainer,n=e.startOffset;for(;;){let e=t.previousSibling;for(;e;)n+=(e.textContent||"").length,e=e.previousSibling;let r=t.parentElement;if(!r)return{lineNumber:0,offset:0,node:null};if(r.classList.contains("react-file-line")){let e=parseInt(r.getAttribute("data-line-number")||"1",10);return{lineNumber:e,offset:n,node:t}}t=r}}function i_(e,t,n){let r,i;if(document.caretPositionFromPoint){let e=document.caretPositionFromPoint(t,n);e&&(r=e.offsetNode,i=e.offset)}else if(document.caretRangeFromPoint){let e=document.caretRangeFromPoint(t,n);e&&(r=e.startContainer,i=e.startOffset)}if(!r||"number"!=typeof i||r.nodeType!==Node.TEXT_NODE||!r.textContent)return null;let o=iD(r.textContent,e,i);if(!o)return null;let a=document.createRange();return a.setStart(r,o[1]),a.setEnd(r,o[2]),a}function iD(e,t,n){let r;let i=null;for(;r=t.exec(e);){if(t.lastIndex===i){(0,iE.eK)(Error("regexp did not advance in findNearestMatch()"));break}i=t.lastIndex;let e=r.index+r[0].length;if(r.index<=n&&n<=e)return[r[0],r.index,e]}return null}var iF=n(81543),iO=n(60193),iP=n(85503),iA=n(57294),iM=n(60348),iH=n(47001),i$=n(38490),iW=n(15387),iz=n(8386);let iU=[.007,.014,.03,.049,.084,.14,.23,.38,.62,Number.MAX_VALUE],iG={"scale.orange.0":"#ffdfb6","scale.orange.1":"#ffc680","scale.orange.2":"#f0883e","scale.orange.3":"#f0883e","scale.orange.4":"#db6d28","scale.orange.5":"#bd561d","scale.orange.6":"#9b4215","scale.orange.7":"#762d0a","scale.orange.8":"#5a1e02","scale.orange.9":"#3d1300"};function iq(){let{resolvedColorScheme:e}=(0,iz.Fg)(),t=e?.startsWith("dark");return t?Array(10).fill(null).map((e,t)=>iG[`scale.orange.${9-t}`]):Array(10).fill(null).map((e,t)=>iG[`scale.orange.${t}`])}function iV(e,t){let{resolvedColorScheme:n}=(0,iz.Fg)(),r=n?.startsWith("dark"),i=r?iG["scale.orange.9"]:iG["scale.orange.0"];if(e<t)return i;let o=Date.now(),a=Math.min(o-t.getTime(),63072e6),s=o-e.getTime(),l=s/a,c=0;for(let e of iU){if(l<e)return r?iG[`scale.orange.${c}`]:iG[`scale.orange.${9-c}`];++c}return i}var iK=n(98917),iY=n(16275);let iQ=n0.forwardRef(iJ);function iX({linesData:e,tabSize:t,onLineNumberClick:n}){let r=(0,n0.useRef)(null),i=(0,rG.Q)(),o=i1(i,e);return(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexDirection:"column",tabSize:t},ref:r,"data-hpc":!0,children:o.map((e,t)=>(0,nY.jsx)(i0,{...e,index:t,onLineNumberClick:n},`blame-for-segment-${e.range?.start??e.linesData[0].lineNumber}`))})}function iJ({linesData:e,tabSize:t,materializeAllLines:n,onLineNumberClick:r},i){let o=(0,n0.useRef)(null),a=(0,rG.Q)();(0,n0.useImperativeHandle)(i,()=>({scrollToTop:()=>{(0,r1.nB)(0)||l.scrollToIndex(0,{align:"start"})},scrollToLine:e=>{(0,r1.nB)(e)||l.scrollToIndex(e,{align:"center"})}}));let s=(0,n0.useCallback)(t=>{let n=e[t],r=a?.ranges[n.lineNumber];return r&&r.end===r.start?window.innerWidth>n4._G.medium?31:41:20},[e,a]),l=(0,iW.F)({parentRef:o,size:e.length,overscan:n?Number.MAX_SAFE_INTEGER:100,estimateSize:s}),c=l.virtualItems.map(t=>({...e[t.index],virtualOffset:t.start})),d=i1(a,c);return(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexDirection:"column",height:[`${l.totalSize+41*d.length}px`,`${l.totalSize+41*d.length}px`,`${l.totalSize}px`],tabSize:t},ref:o,"data-hpc":!0,children:d.map((e,t)=>(0,nY.jsx)(i0,{...e,index:t,onLineNumberClick:r},`blame-for-segment-${e.range?.start??e.linesData[0].lineNumber}`))})}function i0({range:e,commit:t,linesData:n,index:r,onLineNumberClick:i}){let o=n[0].virtualOffset;function a(r){return(0,nY.jsxs)(nY.Fragment,{children:[e&&t?(0,nY.jsx)(i2,{range:e,commit:t}):(0,nY.jsx)(n7.Z,{sx:{height:"100%"}}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row"},children:[(0,nY.jsx)(n7.Z,{className:"react-line-numbers",sx:n.length>1?void 0:{py:["10px","10px","5px"]},children:n.map(e=>(0,nY.jsx)(iY._,{codeLineData:e,onClick:i},`line-number-${e.lineNumber}`))}),(0,nY.jsx)(n7.Z,{className:"react-code-lines",sx:n.length>1?void 0:{py:["10px","10px","5px"]},children:n.map(e=>(0,nY.jsx)(iK.E,{id:`LC${e.lineNumber}-${r?"narrow":"wide"}`,codeLineData:e},`code-line=${e.lineNumber}`))})]})]})}return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(n7.Z,{sx:{display:["none","none","flex"],borderBottomColor:"border.muted",borderBottomWidth:"1px",borderBottomStyle:"solid",width:"100%",flexDirection:"row"},style:void 0!==o?{transform:`translateY(${o}px)`,position:"absolute",top:0}:void 0,children:a(!1)}),(0,nY.jsx)(n7.Z,{sx:{display:["flex","flex","none"],borderBottomColor:"border.muted",borderBottomWidth:"1px",borderBottomStyle:"solid",width:"100%",flexDirection:"column"},style:void 0!==o?{transform:`translateY(${o+(r??0)*41}px)`,position:"absolute",top:0}:void 0,children:a(!0)})]})}function i1(e,t){if(!e)return[{linesData:t}];let n=[],r=null,i=!0;for(let o of t){r||(r={linesData:[]});let t=i?Object.values(e?.ranges??{}).find(e=>e.start<=o.lineNumber&&e.end>=o.lineNumber):e?.ranges[o.lineNumber];t&&(r.range=t,r.commit=e.commits[t.commitOid]),r.linesData.push(o),r.range?.end===o.lineNumber&&(n.push(r),r=null),i=!1}return r&&(n.push(r),r=null),n}let i2=n0.memo(i3);function i3({range:e,commit:t}){let n=(0,nX.H)(),r=new Date(t.committedDate),i=new Date(n.createdAt),o=(0,nY.jsx)(n7.Z,{sx:{verticalAlign:"top",pl:"10px",width:100},children:(0,nY.jsx)(iH.Z,{date:r,tense:"past",sx:{color:"fg.muted",whiteSpace:"nowrap",fontSize:"smaller"}})});return(0,nY.jsxs)(n7.Z,{className:"react-blame-for-range",sx:{display:"flex",minWidth:["auto","auto",350],maxWidth:["auto","auto",350]},children:[(0,nY.jsx)(n7.Z,{"aria-hidden":!0,sx:{padding:"2px",width:4},children:(0,nY.jsx)(i8,{commitDate:r,repoCreationDate:i})}),(0,nY.jsx)(n7.Z,{sx:{display:["none","none","inherit"],pt:"4px"},children:o}),(0,nY.jsx)(n7.Z,{sx:{pl:1,pt:["6px","6px","3px"],verticalAlign:"top",width:25},children:t.authorAvatarUrl&&(0,nY.jsx)(iA.O,{src:t.authorAvatarUrl,size:18})}),(0,nY.jsx)(n7.Z,{sx:{verticalAlign:"top",pt:[2,2,"6px"],pb:[2,2,0],minWidth:[0,0,170],flexGrow:[1,1,1]},children:(0,nY.jsx)(n7.Z,{sx:{display:"flex"},children:(0,nY.jsx)(iu.WZ,{html:t.shortMessageHtmlLink,sx:{whiteSpace:"nowrap",ml:2,overflowX:"hidden",textOverflow:"ellipsis",maxWidth:"100%",fontSize:[1,1,0]},"data-hovercard-url":(0,nJ.QY)({owner:n.ownerLogin,repo:n.name,commitish:t.oid})})})}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignContent:"flex-start",justifySelf:"flex-end",verticalAlign:"top",pl:2,pt:["2px","2px","1px"],pb:[1,1,0],width:[150,150,34],pr:[2,2,0]},children:[(0,nY.jsx)(n7.Z,{sx:{display:["flex","flex","none"],pt:"1px",pr:3},children:o}),(0,nY.jsx)(i4,{range:e,commit:t})]})]})}function i4({range:e,commit:t}){let n=(0,nX.H)();if(!e.reblamePath)return null;let r=(0,nJ.t4)({owner:n.ownerLogin,repo:n.name,commitish:t.firstParentOid,filePath:e.reblamePath}),i=new Intl.DateTimeFormat(void 0,{year:"numeric",month:"short",day:"numeric"}),o=t.oid.slice(0,7),a=i.format(new Date(t.committedDate)),s=`Blame prior to change ${o}, made on ${a}`;return(0,nY.jsx)(i$.Z,{"aria-label":s,children:(0,nY.jsx)(rl.h,{"aria-label":s,as:iM.r,sx:{color:"fg.muted"},to:r,icon:rs.VersionsIcon,variant:"invisible",size:"small"})})}function i8({commitDate:e,repoCreationDate:t}){let n=iV(e,t);return(0,nY.jsx)(n7.Z,{sx:{backgroundColor:n,width:"4px",borderRadius:"2px",height:"100%"}})}try{(v=iQ).displayName||(v.displayName="BlameLines")}catch{}try{(N=iX).displayName||(N.displayName="BlameLinesSSR")}catch{}try{(C=iJ).displayName||(C.displayName="BlameLinesWithRef")}catch{}try{(k=i0).displayName||(k.displayName="BlameSegment")}catch{}try{(S=BlameSegmentContent).displayName||(S.displayName="BlameSegmentContent")}catch{}try{(I=i2).displayName||(I.displayName="BlameForRange")}catch{}try{(R=i3).displayName||(R.displayName="BlameForRangeUnmemoized")}catch{}try{(Z=i4).displayName||(Z.displayName="ReblameButton")}catch{}try{(E=i8).displayName||(E.displayName="BlameAgeIndicator")}catch{}var i6=n(78720),i5=n(22125),i9=n(77262);function i7({parentRef:e,lineCount:t,materializeAllLines:n}){let r=(0,i9.G)("react-code-lines"),i=(0,n0.useMemo)(()=>()=>r,[r]),o=(0,n6.O)().codeWrappingOption.enabled,a=(0,n0.useMemo)(()=>ot(t),[t]);return(0,iW.F)({parentRef:e,size:t,overscan:n?Number.MAX_SAFE_INTEGER:100,scrollToFn:oe,estimateSize:i,rangeExtractor:a,measureSize:o?void 0:i,useVirtualImpl:i5.A})}function oe(e){window.scroll({top:e,left:0})}function ot(e){return function(t){if(e<150)return on(0,e);let n=(0,i6.M)(t);if(0===n.length)return n;let r=n[0],i=n[n.length-1],o=on(0,Math.min(75,r)),a=on(e+1-Math.min(75,e-i),e);return o.concat(n,a)}}function on(e,t){return Array(t-e).fill(null).map((t,n)=>n+e)}var or=n(68588);function oi({linesData:e,onCodeNavTokenSelected:t,onLineNumberClick:n,isBlame:r,isCursorVisible:i,isVirtualized:o,textAreaRef:a,shouldRenderOverlay:s,tabSize:l,optionalTestLeftOffsetFunction:c,textSelection:d,onCollapseToggle:u,onLineStickOrUnstick:h,optionalTestTopOffsetFunction:m,additionalTextAreaInstructions:f}){let[p,x]=(0,n0.useState)(0),[y,g]=(0,n0.useState)(0),[b,j]=(0,n0.useState)(!1),w=(0,n0.useRef)(""),v=(0,n0.useRef)(null),{cursorNavigationHighlightLine:N,expandAndFocusLineContextMenu:C,cursorNavigationEnter:k,searchShortcut:S,cursorNavigationPageDown:I,cursorNavigationPageUp:R}=(0,rY.bx)(),Z=(0,ih.TH)();(0,n0.useEffect)(()=>{x(0),g(0)},[Z.key]),(0,n0.useEffect)(()=>{function e(e){w.current=e.key}return window.oncontextmenu=function(e){if("ContextMenu"===w.current&&-1===e.button&&-1!==document.activeElement?.className.indexOf(iP.firstOptionId))return w.current="",e?.preventDefault(),e?.stopPropagation(),!1},window.addEventListener("keydown",e),()=>{window.removeEventListener("keydown",e),window.oncontextmenu=null}},[]);let E=r?ii.O$:ii.jn,T=(0,n0.useRef)(null),{onEnter:L,updateUrlForLineNumber:B,onPageUp:_,onPageDown:D,currentStartLine:F,currentEndLine:O,determineAndSetTextAreaCursorPosition:P,getCorrectLineNumberWithCollapsedSections:A}=(0,ii.RP)(T,t,c??x,m??g,e,o,r,n,a,l,f,d),M=(0,n0.useRef)(!1);(0,n0.useEffect)(()=>{M.current=/iPhone|iPad|iPod|Android/i.test(navigator.userAgent)},[]);let H=(0,i9.G)("react-code-lines"),$=i?{height:`${H}px`,width:"1.5px",backgroundColor:"fg.default",position:"absolute",visibility:M.current?"hidden":"visible"}:{};return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(n7.Z,{"aria-hidden":!0,style:{top:y,left:E+p},sx:$,ref:T,"data-testid":"navigation-cursor",className:"code-navigation-cursor",children:" "}),s&&(0,nY.jsx)(n7.Z,{style:{top:y+H,left:E+p},sx:{position:"absolute",backgroundColor:"canvas.subtle",borderColor:"border.default",borderStyle:"solid",borderWidth:"1px",borderRadius:2,px:3,py:2},children:(0,nY.jsx)(ru.Z,{children:"Code view is read-only."})}),b&&(0,nY.jsx)(iP.default,{ref:v,rowBeginId:`LG${A(F.current)}`,rowBeginNumber:A(F.current),rowEndNumber:A(O.current),rowEndId:`LG${A(O.current)}`,openOnLoad:!0,cursorRef:T,onCollapseToggle:u,onLineStickOrUnstick:h,lineData:function(){if(F.current!==O.current)return null;let t=e[F.current];if(!t)return null;if(t.isStartLine)return t;if(""===t.codeLineClassName)return null;let n=t.codeLineClassName?.split("child-of-line-");if(!n||-1===t.codeLineClassName?.indexOf("child-of-line-"))return null;let r=parseInt(n[n.length-1]?.trim()??"undefined");return r&&!Number.isNaN(r)?e[r-1]&&e[r-1]?.lineNumber===r?e[r-1]:e[(0,r1.Bx)(r,e)]:null}(),onMenuClose:function(e,t){j(e),setTimeout(()=>{t&&P(),a?.current?.focus()},300)}}),(0,nY.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorEnter","data-hotkey":k.hotkey,onClick:L,"data-hotkey-scope":r1.KG}),(0,nY.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorSetHighlightedLine","data-hotkey":N.hotkey,onClick:B,"data-hotkey-scope":r1.KG}),(0,nY.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorSetHighlightAndExpandMenu","data-hotkey":C.hotkey,onClick:e=>(e.preventDefault(),e.stopPropagation(),j(!0),setTimeout(()=>{v.current?.setAnchor(T.current)},0),!1),"data-hotkey-scope":r1.KG}),(0,nY.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorPageDown","data-hotkey":I.hotkey,onClick:D,"data-hotkey-scope":r1.KG}),(0,nY.jsx)("button",{hidden:!0,"data-testid":"NavigationCursorPageUp","data-hotkey":R.hotkey,onClick:_,"data-hotkey-scope":r1.KG}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:S.hotkey,onButtonClick:()=>{(0,or.n)({retainScrollPosition:!0,returnTarget:a?.current??void 0})},onlyAddHotkeyScopeButton:!0})]})}try{(T=oi).displayName||(T.displayName="NavigationCursor")}catch{}let oo=n0.memo(n0.forwardRef(oa));function oa({linesData:e,onLineNumberClick:t,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:i,tabSize:o,contentWidth:a,onCollapseToggle:s,onCodeNavTokenSelected:l,textAreaRef:c,isTextAreaFocused:d,textOverlayShouldBeVisible:u,materializeAllLines:h,textSelection:m,additionalTextAreaInstructions:f},p){let x=(0,n0.useRef)(null),y=(0,n0.useRef)(null),g=(0,n0.useRef)(null),b=(0,ii.nj)(),j=(0,n0.useRef)(!1),w=(0,n0.useRef)(!1);(0,n0.useEffect)(()=>{if(c&&c.current){c.current.onscroll=()=>{y&&y.current&&c.current&&c.current.scrollLeft!==y.current.scrollLeft&&(y.current.scrollLeft=c.current.scrollLeft)};let e=c.current;return()=>{e&&(e.onscroll=null)}}},[c,x,b]);let v=(0,n6.O)().codeWrappingOption.enabled,N=i7({parentRef:x,lineCount:e.length,materializeAllLines:!!h});return(0,n0.useImperativeHandle)(p,()=>({scrollToTop:()=>{(0,r1.nB)(0)||N.scrollToIndex(0,{align:"start"})},scrollToLine:(e,t)=>{N.scrollToIndex(e,{align:"start"});let n=x.current;n&&n.scroll({left:ol(n,e,t)})}})),(0,nY.jsxs)(n7.Z,{ref:x,sx:{position:"relative",pointerEvents:b?"none":"auto"},onScroll:e=>os(e,c),children:[(0,nY.jsx)(n7.Z,{ref:y,sx:b?{overflowX:"auto",scrollbarWidth:"none","&::-webkit-scrollbar":{display:"none"}}:{overflowX:"auto"},tabIndex:0,onScroll:e=>{b&&g.current&&g.current.scrollLeft!==e.currentTarget.scrollLeft&&(j.current?j.current=!1:(c?.current&&(c.current.scrollLeft=e.currentTarget.scrollLeft),w.current=!0,g.current.scrollLeft=e.currentTarget.scrollLeft))},children:(0,nY.jsxs)(n7.Z,{className:"react-code-file-contents",role:"presentation","aria-hidden":!0,"data-tab-size":o,"data-paste-markdown-skip":!0,sx:{tabSize:o,isolation:"isolate",position:"relative",width:a,maxWidth:v?"100%":"unset"},style:{height:N.totalSize},"data-hpc":!0,children:[(0,nY.jsx)("div",{className:"react-line-numbers",style:{pointerEvents:"auto",height:N.totalSize},children:N.virtualItems.map(r=>{let o=e[r.index];return(0,nY.jsx)(iY._,{codeLineData:o,onClick:t,ownedCodeSections:n,onLineStickOrUnstick:i,onCollapseToggle:s,virtualOffset:r.start},`line-number-${o.lineNumber}`)})}),(0,nY.jsx)("div",{className:"react-code-lines",style:{height:N.totalSize},children:N.virtualItems.map(t=>{let n=e[t.index];return(0,nY.jsx)(iK.E,{codeLineData:n,codeLineClassName:n.codeLineClassName,id:`LC${n.lineNumber}`,onLineStickOrUnstick:i,setIsCollapsed:s,codeLineToSectionMap:r,virtualOffset:t.start,virtualKey:t.key,measureRef:t.measureRef},`line-number-${n.lineNumber}-content:${n.rawText}`)})}),(0,nY.jsx)(iw,{shouldNotOverrideCopy:b,containerRef:b?c:x}),b&&(0,nY.jsx)(oi,{linesData:e,isBlame:!1,onCodeNavTokenSelected:l,onLineNumberClick:t,isCursorVisible:!!d,isVirtualized:!0,textAreaRef:c,onCollapseToggle:s,onLineStickOrUnstick:i,tabSize:o,textSelection:m,shouldRenderOverlay:!!u,additionalTextAreaInstructions:f??""})]})}),b&&a&&y.current&&y.current.clientWidth<a&&(0,nY.jsx)(n7.Z,{sx:{width:"100%",pointerEvents:"auto",overflowX:"auto",overflowY:"visible",height:"17px",position:"sticky",bottom:0},onScroll:e=>{y.current&&y.current.scrollLeft!==e.currentTarget.scrollLeft&&(w.current?w.current=!1:(c?.current&&(c.current.scrollLeft=e.currentTarget.scrollLeft),j.current=!0,y.current.scrollLeft=e.currentTarget.scrollLeft))},ref:g,onClick:e=>e.preventDefault(),onMouseDown:e=>e.preventDefault(),onMouseUp:e=>e.preventDefault(),children:(0,nY.jsx)(n7.Z,{sx:{width:a,height:"1px"}})})]})}function os(e,t){let n=e.target;t?.current?.scrollTo(n.scrollLeft,n.scrollTop)}function ol(e,t,n){if(!n)return 0;let r=(0,r1.d5)(t,n);if(!r)return 0;let i=e.getBoundingClientRect(),o=r.getBoundingClientRect(),a=i.left+i.width-e.scrollLeft-(o.left+o.width)>0;return a?0:r.offsetLeft}try{(L=oo).displayName||(L.displayName="CodeLines")}catch{}try{(B=oa).displayName||(B.displayName="CodeLinesUnmemoized")}catch{}let oc=n0.memo(od);function od({linesData:e,onLineNumberClick:t,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:i,tabSize:o,contentWidth:a,onCollapseToggle:s}){let l=(0,n6.O)().codeWrappingOption.enabled,{rawBlobUrl:c}=(0,rq.G)();return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(n7.Z,{className:"react-code-file-contents",role:"presentation","aria-hidden":!0,"data-tab-size":o,"data-paste-markdown-skip":!0,sx:{tabSize:o,isolation:"isolate",position:"relative",width:a,overflow:"auto",maxWidth:l?"100%":"unset"},"data-hpc":!0,children:[(0,nY.jsx)("div",{className:"react-line-numbers",style:{pointerEvents:"auto"},children:e.map(e=>(0,nY.jsx)(iY._,{codeLineData:e,onClick:t,ownedCodeSections:n,onLineStickOrUnstick:i,onCollapseToggle:s},`line-number-${e.lineNumber}`))}),(0,nY.jsx)("div",{className:"react-code-lines",children:e.map(e=>(0,nY.jsx)(iK.E,{codeLineData:e,codeLineClassName:e.codeLineClassName,id:`LC${e.lineNumber}`,onLineStickOrUnstick:i,setIsCollapsed:s,codeLineToSectionMap:r,measureRef:void 0},`line-number-${e.lineNumber}-content:${e.rawText}`))})]}),1e3===e.length&&(0,nY.jsx)(n7.Z,{sx:{justifyContent:"center",display:"flex"},children:(0,nY.jsx)(rR.Z,{href:c,children:"View remainder of file in raw view"})})]})}try{(_=oc).displayName||(_.displayName="CodeLinesSSR")}catch{}try{(D=od).displayName||(D.displayName="CodeLinesSSRUnmemoized")}catch{}var ou=n(90984);function oh(e,t,n,r,i){let o=om(e,t,n,r,i),a=(0,iF.fq)(),s=(0,n0.useRef)("");(0,r1.PO)(e=>s.current=op(e));let l=(0,n0.useMemo)(()=>of(o,n),[o,n,s.current]),c=l.map(e=>a&&e.rawText?(0,iF.V1)(e.rawText):e.rawText).join("\n");return{lines:l,plainTextLinesAsString:c}}function om(e,t,n,r,i){let[o]=(0,iS.D)(()=>e.length,Math.min(e.length,1e3),e);return(0,n0.useMemo)(()=>{let a=Array(o).fill(null).map((e,t)=>t+1);return a.map(o=>{let a;let s=t?.[o-1],l=!1,c=!1;for(let e of n?.get(o)??[])e.startLine===o&&(l=!0,a=e),e.endLine===o&&(c=!0);let d=e[o-1]?.replace(/[\n\r]/g,"")??"",u=i&&i.get(o)||[],h=(0,r1.TX)(u,o,c,n);return{stylingDirectivesLine:s,lineNumber:o,codeLineClassName:h,isStartLine:l,isEndLine:c,ownedSection:a,rawText:d,bidi:(0,iF.ud)(d),codeownersLineError:r?.find(e=>e.line===o)}})},[o,t,e,i,n,r])}function of(e,t){let n=new Set;for(let r=0;r<e.length;r++){if(n.has(r))continue;let e=t?.get(r)??[];for(let t=0;t<e.length;t++)if(e[t].collapsed){for(let r=e[t].startLine+1;r<=e[t].endLine;r++)n.add(r);e[t].startLine===r&&(r=e[t].endLine);break}}return e.filter(e=>!n.has(e.lineNumber))}function op(e){let t=[...e];return t.sort(),t.join(",")}var ox=n(90176),oy=n(66280),og=n(73935);function ob({onDismiss:e}){let{expandAndFocusLineContextMenu:t,cursorNavigationEnter:n,cursorNavigationHighlightLine:r}=(0,rY.bx)();return(0,og.createPortal)((0,nY.jsx)(oy.V,{width:"large","aria-label":"Code Blob Focused Hotkeys",onClose:e,title:"Code Blob Focused Hotkeys",children:(0,nY.jsx)(n7.Z,{children:(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsxs)(n7.Z,{sx:{p:1},children:["Select the line the cursor is on ",(0,nY.jsx)("kbd",{children:r.text})]}),(0,nY.jsxs)(n7.Z,{sx:{p:1},children:["Select the symbol under the cursor ",(0,nY.jsx)("kbd",{children:n.text})]}),(0,nY.jsxs)(n7.Z,{sx:{p:1},children:["Move focus to the highlighted line menu ",(0,nY.jsx)("kbd",{children:t.text})]})]})})}),document.body)}try{(F=ob).displayName||(F.displayName="TextAreaHelpDialog")}catch{}function oj({textAreaRef:e,setTextOverlayShouldBeVisible:t,setTextSelection:n,setAdditionalTextAreaInstructions:r,cursorClickStartRef:i,parentRef:o,tabSize:a,plainTextLinesAsString:s,numLines:l,setIsTextAreaFocused:c}){let d=(0,ii.nj)(),u=(0,n6.O)().codeWrappingOption,h=(0,rY.tW)(),m=(0,rY.ln)(),f=(0,i9.G)("react-code-lines"),[p,x]=(0,n0.useState)(!1),{cursorNavigationOpenHelpDialog:y}=(0,rY.bx)();return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)("textarea",{id:r1.KG,ref:e,onMouseUp:r=>ow(r,e,t,n,i,d,o,f),onMouseDown:e=>ov(e,o,d,i,f),"aria-label":"file content","aria-readonly":!0,inputMode:"none",tabIndex:0,"aria-multiline":!0,"aria-haspopup":!1,"data-gramm":"false","data-gramm_editor":"false","data-enable-grammarly":"false",style:{resize:"none",marginTop:-2,paddingLeft:ii.jn,display:"hidden",width:"calc(100% - 70px)",backgroundColor:"unset",color:"var(--color-canvas-default)",position:"absolute",border:"none",tabSize:a,outline:"none",overflowX:"auto",height:f*(l+1),fontSize:"12px",lineHeight:"20px",overflowY:"hidden",overflowWrap:u.enabled?"anywhere":"normal",whiteSpace:u.enabled?"pre-wrap":"pre"},value:s,onKeyDown:function(i){h.includes(i.key)||m.includes(i.key)&&(i.getModifierState("Control")||i.getModifierState("Alt")||i.getModifierState("Shift")||i.getModifierState("Meta"))?(" "===i.key&&(i.preventDefault(),i.shiftKey?r(`PageUp${Date.now()}`):r(`PageDown${Date.now()}`)),i.altKey&&i.ctrlKey&&"\u02D9"===i.key&&x(!0),t(!1),setTimeout(()=>{e.current&&n({start:e.current.selectionStart,end:e.current.selectionEnd,keyboard:!0,displayStart:!1})},5)):!i.ctrlKey&&!i.metaKey&&!i.altKey&&!i.shiftKey&&(function(e,t){let n=e.exec(t);return n&&n[0]===t}(/[a-zA-Z0-9-_ ]{1,1}/,i.key)||"Backspace"===i.key||"Enter"===i.key)&&((0,rN.x)("Code view is read only."),t(!0),i.preventDefault())},spellCheck:!1,autoCorrect:"off",autoCapitalize:"off",autoComplete:"off","data-ms-editor":"false",onDrop:e=>{let t=e.dataTransfer.getData("Text");try{let e=new URL(t);window.open(e,"_blank")?.focus()}catch(e){}return!1},onPaste:e=>(e.preventDefault(),!1),onChange:()=>{},className:"react-blob-print-hide",onFocus:()=>{c(!0)}}),p&&(0,nY.jsx)(ob,{onDismiss:()=>{x(!1)}}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:y.hotkey,onButtonClick:()=>{x(!0)},onlyAddHotkeyScopeButton:!0})]})}function ow(e,t,n,r,i,o,a,s){if(o&&!e.defaultPrevented&&i){if(2===e.button)e.preventDefault(),e.stopPropagation();else if(0===e.button){n(!1);let o=(0,n4.ZV)(window.innerWidth),l=o<n4._G.medium,c=a.current?.getBoundingClientRect().top?window.scrollY+a.current?.getBoundingClientRect().top:l?423:354;if(a.current&&e.pageY>c+a.current?.clientHeight){i.current={startX:-2,startY:-2};return}let d=(0,r1.BS)(e.pageY,c,s),u=a.current?.getBoundingClientRect().left||0,h=e.clientX-u-ii.jn,m=!1;(d<i.current.startY||d===i.current.startY&&h<i.current.startX)&&(m=!0),setTimeout(()=>{t&&t.current&&r({start:t.current.selectionStart,end:t.current.selectionEnd,keyboard:!1,displayStart:m})},0)}}}function ov(e,t,n,r,i){if(n&&!e.defaultPrevented&&r){if(2===e.button){e.preventDefault(),e.stopPropagation();return}if(0===e.button){if(e.ctrlKey){e.preventDefault(),e.stopPropagation();return}let n=(0,n4.ZV)(window.innerWidth),o=n<n4._G.medium,a=t.current?.getBoundingClientRect().top?window.scrollY+t.current?.getBoundingClientRect().top:o?423:354;if(t.current&&e.pageY>a+t.current?.clientHeight){r.current={startX:-2,startY:-2};return}let s=(0,r1.BS)(e.pageY,a,i),l=t.current?.getBoundingClientRect().left||0,c=e.clientX-l-ii.jn;r.current={startX:c,startY:s}}}}try{(O=oj).displayName||(O.displayName="TextArea")}catch{}let oN=(0,rM.Z)("localStorage");function oC({blobLinesHandle:e,onCodeNavTokenSelected:t,codeSections:n,codeLineToSectionMap:r,validCodeNav:i,onLineStickOrUnstick:o,searchResults:a,focusedSearchResult:s}){let{rawLines:l,stylingDirectives:c,tabSize:d}=(0,rq.G)(),u=!!(0,rG.Q)(),[h,m]=(0,n0.useState)(!1),f=(0,ii.nj)(),[p,x]=(0,n0.useState)(void 0),[y,g]=(0,n0.useState)({start:-1,end:-1,keyboard:!0,displayStart:!1}),[b,j]=(0,n0.useState)(""),[w,v]=(0,n0.useState)(!1),N=(0,n0.useRef)(null),[C,k]=(0,n0.useState)(!1),S=(0,n0.useRef)({startX:0,startY:0}),{hash:I}=(0,ih.TH)(),{refInfo:R,path:Z}=(0,rK.Br)(),E=(0,n0.useRef)(null),[T,L]=(0,n0.useState)(void 0),[B]=(0,iS.D)(()=>!1,!0,[]),_=iZ(),D=(0,rV.k)(),F=(0,n0.useCallback)(()=>{x(r0.n6(window.location.hash)?.blobRange)},[]),{findInFileOpen:O,setFindInFileOpen:P}=is(),A=iR(),{lines:M,plainTextLinesAsString:H}=oh(l??[],c??null,n??null,A,r),$=(0,n0.useRef)(M);$.current=M,(0,n0.useEffect)(()=>{(0,r1.Yo)()},[R.currentOid,Z]),(0,n0.useEffect)(()=>{window.onbeforeprint=()=>m(!0),window.onafterprint=()=>m(!1)},[]),(0,n0.useEffect)(()=>{"true"!==oN.getItem("heardHelpAnnouncement")&&((0,r1.dM)("While the code is focused, press Alt+F1 for a menu of operations.",2e3),oN.setItem("heardHelpAnnouncement","true"))},[]),iT((0,n0.useCallback)(e=>{if(e&&e?.node&&!u){if(!e.node.textContent||e.node.textContent.length<3)return;let n=e.node;!n||!n.hasAttribute||n.hasAttribute("clickadded")||(n.classList.add("pl-token"),n.setAttribute("clickadded","true"),n.addEventListener("click",function(n){let r=n.target.textContent?n.target.textContent:"";t&&e&&(t({selectedText:r,lineNumber:e.lineNumber,offset:e.offset}),O&&P(!1))}))}},[O,u,P,t]),i);let W=({line:t,column:n})=>{if(t<10)e.current?.scrollToTop();else if(e.current){let i=(0,r1.Bx)(t,M);if(-1===i){let e=r?.get(t);for(let t of e??[])t&&t.collapsed&&(t.collapsed=!1,(0,r1.yw)(t?.startLine));_()}setTimeout(()=>{let r=(0,r1.Bx)(t,$.current);e.current?.scrollToLine(r,n)},0),D&&!(0,r1.nB)(t)&&(o(null,!0),(0,r1.DD)(D,t,r,o))}};return(0,n0.useEffect)(()=>{let e=(0,r0.n6)(I);if(!e.blobRange?.start?.line){x(void 0);return}x(e.blobRange)},[Z,I,M.length]),(0,n0.useEffect)(()=>{let e=(0,r0.n6)(I);e.blobRange?.start?.line&&setTimeout(()=>W({line:e.blobRange.start.line}),0)},[Z,u]),(0,n0.useEffect)(()=>{let e=E.current;if(!e||!f){L(void 0);return}L(e.scrollWidth>e.clientWidth?e.scrollWidth+70:void 0);let t=new ResizeObserver(e=>{for(let{target:t}of e)L(t.scrollWidth>t.clientWidth?t.scrollWidth+70:void 0)});return t.observe(e),()=>{t.disconnect()}},[f,Z]),(0,r2.z)(W),(0,nY.jsx)(ox.Sh,{searchResults:a,focusedSearchResult:s,children:(0,nY.jsxs)(ou.k,{highlightedLines:p,children:[M.some(e=>e.bidi)&&(0,nY.jsx)(iF.h7,{}),(0,nY.jsx)(n7.Z,{sx:{display:"flex",flex:1,py:u?0:2,flexDirection:"column",justifyContent:"space-between",minWidth:0,position:"relative"},children:(0,nY.jsx)(iP.HighlightedLineMenuContainer,{children:(0,nY.jsx)(iO.Id,{children:(0,nY.jsxs)(n7.Z,{sx:{flex:1,position:"relative",minWidth:0,overflowX:u?"auto":void 0,overflowY:u?"hidden":void 0},ref:N,onBlur:e=>{e.currentTarget.contains(e.relatedTarget)||v(!1)},children:[f&&(0,nY.jsx)(oj,{textAreaRef:E,setTextOverlayShouldBeVisible:k,setTextSelection:g,setAdditionalTextAreaInstructions:j,cursorClickStartRef:S,parentRef:N,tabSize:d,plainTextLinesAsString:H,numLines:M.length,setIsTextAreaFocused:v}),u?B?(0,nY.jsx)(iX,{linesData:M,tabSize:d,onLineNumberClick:F}):(0,nY.jsx)(iQ,{ref:e,linesData:M,tabSize:d,onLineNumberClick:F}):B?(0,nY.jsx)(oc,{linesData:M,onLineNumberClick:F,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:o,tabSize:d,contentWidth:T,onCollapseToggle:_}):(0,nY.jsx)(oo,{ref:e,linesData:M,onLineNumberClick:F,codeSections:n,codeLineToSectionMap:r,onLineStickOrUnstick:o,tabSize:d,contentWidth:T,onCollapseToggle:_,textAreaRef:E,isTextAreaFocused:w,onCodeNavTokenSelected:t,textOverlayShouldBeVisible:C,materializeAllLines:h,textSelection:y,additionalTextAreaInstructions:b})]})})})})]})})}try{(P=oC).displayName||(P.displayName="CodeBlob")}catch{}function ok({displayName:e,displayUrl:t}){return(0,nY.jsx)(n7.Z,{sx:{display:"flex",justifyContent:"center",width:"100%"},children:(0,nY.jsx)(n7.Z,{as:"img",alt:e,src:t,"data-hpc":!0,sx:{maxWidth:"100%"}})})}try{(A=ok).displayName||(A.displayName="ImageBlob")}catch{}var oS=n(6324),oI=n(17791),oR=n(8760),oZ=n(9770),oE=n(99782);function oT({issueTemplate:e,type:t}){return(0,nY.jsxs)(n7.Z,{sx:{borderBottomLeftRadius:"6px",borderBottomRightRadius:"6px",p:5},children:[(0,nY.jsxs)(n7.Z,{as:"table",sx:{mb:3},children:[(0,nY.jsx)("thead",{children:(0,nY.jsxs)("tr",{children:[t===K.Issue?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(oL,{header:!0,children:"Name"}),(0,nY.jsx)(oL,{header:!0,children:"About"})]}):(0,nY.jsx)(oL,{header:!0,children:"Title"}),(0,nY.jsx)(oL,{header:!0,children:"Labels"}),e.projects&&(0,nY.jsx)(oL,{header:!0,children:"Projects"}),t===K.Issue&&(0,nY.jsx)(oL,{header:!0,children:"Assignees"})]})}),(0,nY.jsx)("tbody",{children:(0,nY.jsxs)("tr",{children:[t===K.Issue?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(oL,{children:e.name}),(0,nY.jsx)(oL,{children:e.about})]}):(0,nY.jsx)(oL,{children:e.title}),(0,nY.jsx)(oL,{children:e.labels}),e.projects&&(0,nY.jsx)(oL,{children:e.projects}),t===K.Issue&&(0,nY.jsx)(oL,{children:e.assignees})]})})]}),e.inputs.map((e,t)=>(0,nY.jsx)(oB,{input:e},t))]})}function oL({children:e,header:t}){return(0,nY.jsx)(n7.Z,{as:t?"th":"td",sx:{p:"6px 13px",border:"1px solid var(--borderColor-default, var(--color-border-default))"},children:e})}function oB({input:e}){switch(e.type){case"markdown":return(0,nY.jsx)(o_,{input:e});case"dropdown":return(0,nY.jsx)(oD,{input:e});case"input":return(0,nY.jsx)(oF,{input:e});case"textarea":return(0,nY.jsx)(oO,{input:e});case"checkboxes":return(0,nY.jsx)(oP,{input:e});default:return null}}function o_({input:e}){return e.value?(0,nY.jsx)(iu.wB,{html:e.value}):null}function oD({input:e}){let t=e.options?.slice();e.required||t?.unshift("None");let n=e.multiple?"Selections: ":"Selection: ";return e.value&&(n+=e.value),(0,nY.jsx)(oA,{input:e,sx:{alignItems:"start"},children:(0,nY.jsxs)(oS.P,{children:[(0,nY.jsx)(oS.P.Button,{children:n}),(0,nY.jsx)(oS.P.Overlay,{width:"medium",children:(0,nY.jsx)(rS.S,{selectionVariant:e.multiple?"multiple":"single",children:t?.map((t,n)=>(0,nY.jsx)(rS.S.Item,{selected:t===e.value,disabled:!0,children:t},n))})})]})})}function oF({input:e}){return(0,nY.jsx)(oA,{input:e,children:(0,nY.jsx)(rc.Z,{placeholder:e.placeholder,value:e.value??""})})}function oO({input:e}){return(0,nY.jsx)(oA,{input:e,children:(0,nY.jsx)(oI.ZP,{placeholder:e.placeholder,value:e.value??"",sx:e.render?{fontFamily:"ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace !important"}:{}})})}function oP({input:e}){return e.checkboxes?(0,nY.jsxs)(oR.Z,{disabled:!0,sx:{color:"var(--fgColor-default, var(--color-fg-default)) !important",my:"15px"},children:[(0,nY.jsx)(oR.Z.Label,{sx:{color:"var(--fgColor-default, var(--color-fg-default))",fontSize:["18px","18x","20px"],fontWeight:600},children:e.label}),e.description&&(0,nY.jsx)(oR.Z.Caption,{sx:{color:"var(--fgColor-muted, var(--color-fg-subtle))",fontSize:"12px"},children:(0,nY.jsx)(iu.wB,{html:e.description})}),e.checkboxes.map((e,t)=>(0,nY.jsxs)(oZ.Z,{disabled:!0,required:e.required,children:[(0,nY.jsx)(oE.Z,{}),(0,nY.jsx)(oZ.Z.Label,{children:e.label})]},t))]}):null}function oA({children:e,input:t,sx:n}){return(0,nY.jsxs)(oZ.Z,{disabled:!0,required:t.required,sx:{my:"15px",...n},children:[(0,nY.jsx)(oZ.Z.Label,{sx:{color:"var(--fgColor-default, var(--color-fg-default))",fontSize:["18px","18x","20px"],"> span > span:last-of-type":{color:"var(--fgColor-danger, var(--color-danger-fg))"}},children:t.label}),t.description&&(0,nY.jsx)(oZ.Z.Caption,{children:(0,nY.jsx)(iu.wB,{html:t.description})}),e]})}!function(e){e.Issue="issue",e.Discussion="discussion"}(K||(K={}));try{(M=oT).displayName||(M.displayName="YamlTemplateContent")}catch{}try{(H=oL).displayName||(H.displayName="MarkdownTableCell")}catch{}try{($=oB).displayName||($.displayName="TemplateInput")}catch{}try{(W=o_).displayName||(W.displayName="MarkdownInput")}catch{}try{(z=oD).displayName||(z.displayName="DropdownInput")}catch{}try{(U=oF).displayName||(U.displayName="InputInput")}catch{}try{(G=oO).displayName||(G.displayName="TextareaInput")}catch{}try{(q=oP).displayName||(q.displayName="CheckboxesInput")}catch{}try{(V=oA).displayName||(V.displayName="InputWrapper")}catch{}let oM=(0,n0.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_CSV_CSVBlob_tsx").then(n.bind(n,31021))),oH=(0,n0.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_Renderable_FileRendererBlob_tsx").then(n.bind(n,49186)));function o$({setOpenPanel:e,codeNavInfo:t,validCodeNav:n,onCodeNavTokenSelected:r,onLineStickOrUnstick:i,searchResults:o,setSearchTerm:a,blobLinesHandle:s,focusedSearchResult:l}){let c=!!(0,rG.Q)(),{rawLines:d}=(0,rq.G)(),{sendRepoKeyDownEvent:u}=(0,rx.a)(),{findInFileShortcut:h}=(0,rY.bx)(),m=il(),{setFindInFileOpen:f}=is(),p=m===y.Code,x=p&&null!=d&&n?h.hotkey:"",g=(0,ii.nj)();(0,n0.useEffect)(()=>{p||e(void 0)},[p,e]);let b=p||m===y.CSV||m===y.Markdown?{}:{overflow:"auto"},j=m===y.Markdown?{justifyContent:"center"}:{};return(0,nY.jsxs)(n7.Z,{as:"section","aria-labelledby":"file-name-id-wide file-name-id-mobile",sx:{backgroundColor:"var(--bgColor-default, var(--color-canvas-default))",border:"0px",borderWidth:0,borderRadius:"0px 0px 6px 6px",p:0,minWidth:0,mt:c?"92px":"46px",...j,...b},children:[(0,nY.jsx)(oW,{blobLinesHandle:s,onCodeNavTokenSelected:r,codeSections:c?void 0:t?.codeSections,codeLineToSectionMap:t?t.lineToSectionMap:void 0,validCodeNav:n,onLineStickOrUnstick:i,searchResults:o,focusedSearchResult:l}),p&&!g&&(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:x,onButtonClick:function(){f(!0),u("BLOB_FIND_IN_FILE_MENU.OPEN");let e=window.getSelection()?.toString();e&&a(e)},buttonTestLabel:"hotkey-button"})]})}let oW=n0.memo(function({onCodeNavTokenSelected:e,codeSections:t,codeLineToSectionMap:n,validCodeNav:r,onLineStickOrUnstick:i,searchResults:o,blobLinesHandle:a,focusedSearchResult:s}){let l=(0,rq.G)(),c=il();switch(c){case y.TooLargeError:return(0,nY.jsxs)(n7.Z,{sx:{textAlign:"center"},"data-hpc":!0,children:[(0,nY.jsx)(rR.Z,{href:l.rawBlobUrl,children:"View raw"}),l.large&&(0,nY.jsx)("p",{children:"(Sorry about that, but we can\u2019t show files that are this big right now.)"})]});case y.Code:return(0,nY.jsx)(oC,{blobLinesHandle:a,onCodeNavTokenSelected:e,codeSections:t,codeLineToSectionMap:n,validCodeNav:r,onLineStickOrUnstick:i,searchResults:o,focusedSearchResult:s});case y.Markdown:return(0,nY.jsx)(iN,{richText:l.richText,sx:{borderBottomLeftRadius:"6px",borderBottomRightRadius:"6px",p:5,minWidth:0}});case y.CSV:return(0,nY.jsx)(n0.Suspense,{fallback:(0,nY.jsx)(ik.m,{}),children:(0,nY.jsx)(oM,{csv:l.csv})});case y.FileRenderer:return(0,nY.jsx)(n0.Suspense,{fallback:(0,nY.jsx)(ik.m,{}),children:(0,nY.jsx)(oH,{identityUuid:l.renderedFileInfo.identityUUID,size:l.renderedFileInfo.size,type:l.renderedFileInfo.renderFileType,url:l.displayUrl})});case y.Image:return(0,nY.jsx)(ok,{displayName:l.displayName,displayUrl:l.displayUrl});case y.IssueTemplate:return(0,nY.jsx)(oT,{issueTemplate:l.issueTemplate?l.issueTemplate:l.discussionTemplate,type:l.issueTemplate?K.Issue:K.Discussion,"data-hpc":!0});default:ic(c)}});try{(Y=oM).displayName||(Y.displayName="CSVBlob")}catch{}try{(Q=oH).displayName||(Q.displayName="FileRendererBlob")}catch{}try{(X=o$).displayName||(X.displayName="BlobContent")}catch{}try{(J=oW).displayName||(J.displayName="Blob")}catch{}var oz=n(85844),oU=n(87634),oG=n(33831),oq=n(89042),oV=n(4855),oK=n(35880),oY=n(35499),oQ=n(21913);function oX({disabled:e,...t}){return(0,nY.jsx)(rl.h,{size:"small",...t,...e?{className:"btn","aria-disabled":!0,onClick:e=>e.preventDefault()}:{}})}try{(ee=oX).displayName||(ee.displayName="AccessibleIconButton")}catch{}var oJ=n(63309);let o0=(0,n0.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_CodeNav_ScrollMarks_tsx").then(n.bind(n,65710)));function o1({stickied:e,searchTerm:t,searchResults:n,setSearchTerm:r,focusedSearchResult:i,setFocusedSearchResult:o,onClose:a}){let s=(0,ii.nj)(),l=(0,n0.useRef)(null),{findInFileShortcut:c,findSelectionShortcut:d,findNextShortcut:u,findPrevShortcut:h}=(0,rY.bx)(),[m,f]=(0,n0.useState)(!0),p=()=>{r(""),o(0)},{sendRepoKeyDownEvent:x}=(0,rx.a)(),y=e=>{e.target.value?(f(!1),r(e.target.value),void 0===i&&o(0)):(f(!0),p())},g=e=>{if(void 0===i){o(0);return}1===e?o(i===n.length-1?0:i+1):o(0===i?n.length-1:i-1)};(0,n0.useEffect)(()=>{l.current?.focus(),l.current?.select()},[]);let b=()=>{let e=window.getSelection()?.toString();e?.length&&(r(e),o(0),x("BLOB_FIND_IN_FILE_MENU.FIND_IN_FILE_FROM_SELECTION")),l.current?.focus(),l.current?.select()};return((0,n0.useEffect)(()=>{n.length>0&&void 0!==i&&(0,r2.v)({line:n[i].lineNumber,column:n[i].ident.start.column})},[n,i]),s)?null:(0,nY.jsxs)(n7.Z,{className:`find-in-file-popover ${e?"find-in-file-popover-stickied":"find-in-file-popover-not-stickied"}`,children:[(0,nY.jsxs)(n7.Z,{sx:{fontSize:0,py:2,pl:3,pr:2,borderBottom:"1px solid var(--borderColor-default, var(--color-border-default))",display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"baseline"},children:[(0,nY.jsx)(ru.Z,{as:"h5",sx:{color:"fg.default",pr:2,fontWeight:"bold"},children:"Find"}),(0,nY.jsxs)(ru.Z,{className:"find-text-help-tooltip",sx:{color:"fg.subtle",visibility:m?"visible":"hidden"},children:["Press ",(0,nY.jsx)(oJ.Z,{children:c.text})," again to open the browser's find menu"]})]}),(0,nY.jsx)(n7.Z,{sx:{flex:1}}),(0,nY.jsx)(rl.h,{variant:"invisible",size:"small",onClick:a,icon:rs.XIcon,sx:{color:"fg.subtle"},"aria-label":"Close find in file"})]}),(0,nY.jsxs)(n7.Z,{sx:{pl:3,pr:2,py:"6px"},children:[(0,nY.jsx)(rc.Z,{ref:l,sx:{pl:0,border:"none",boxShadow:"none"},validationStatus:n.length>1e3?"error":void 0,type:"text",leadingVisual:()=>(0,nY.jsx)(rd.Z,{icon:rs.SearchIcon,"aria-hidden":"true"}),"aria-labelledby":"find-in-file-label","aria-expanded":"true",autoComplete:"off",name:"Find in file input",placeholder:"Search this file",value:t,block:!0,onChange:y,trailingAction:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[(0,nY.jsxs)(ru.Z,{className:"text-small",sx:{textAlign:"center",color:"fg.subtle",m:2},children:[0===n.length||void 0===i?0:i+1,"/",n.length]}),(0,nY.jsx)(rl.h,{size:"small",variant:"invisible",onClick:()=>{g(-1)},icon:rs.ChevronUpIcon,"aria-label":"Up","data-testid":"up-search",sx:{color:"fg.subtle"}}),(0,nY.jsx)(rl.h,{size:"small",variant:"invisible",onClick:()=>{g(1)},icon:rs.ChevronDownIcon,"aria-label":"Down","data-testid":"down-search",sx:{color:"fg.subtle"}})]}),onKeyDown:e=>{"Enter"===e.code||"NumpadEnter"===e.code?e.shiftKey?g(-1):g(1):(e.metaKey||e.ctrlKey)&&("g"===e.key||"G"===e.key)?(e.preventDefault(),e.shiftKey?g(-1):g(1)):(e.metaKey||e.ctrlKey)&&("f"===e.key||"F"===e.key)?m?(x("BLOB_FIND_IN_FILE_MENU.FALLBACK_TO_BROWSER_SEARCH"),a()):(f(!0),e.preventDefault(),l.current?.focus(),l.current?.select()):"Escape"===e.key&&a()}}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:c.hotkey,onButtonClick:b,buttonTestLabel:"hotkey-button"}),(0,nY.jsx)("button",{hidden:!0,"data-hotkey":d.hotkey,onClick:b,"data-testid":"selection-hotkey"}),(0,nY.jsx)("button",{hidden:!0,"data-hotkey":u.hotkey,onClick:()=>g(1),"data-testid":"find-next-button"}),(0,nY.jsx)("button",{hidden:!0,"data-hotkey":h.hotkey,onClick:()=>g(-1),"data-testid":"find-prev-button"}),(0,nY.jsx)(n0.Suspense,{fallback:null,children:(0,nY.jsx)(o0,{definitionsOrReferences:n})})]})]})}try{(et=o0).displayName||(et.displayName="ScrollMarks")}catch{}try{(en=o1).displayName||(en.displayName="FindInFilePopover")}catch{}var o2=n(6582),o3=n(90836),o4=n(54901);function o8({width:e,...t}){return(0,nY.jsx)("div",{style:{width:e},className:"Skeleton Skeleton--text",...t,children:"\xa0"})}try{(er=o8).displayName||(er.displayName="SkeletonText")}catch{}function o6({showTitle:e=!0}){let{sendRepoClickEvent:t}=(0,rx.a)(),[n,r]=(0,n0.useState)(!1),i=(0,nX.H)(),{refInfo:o,path:a}=(0,rK.Br)(),{contributors:s,loading:l,error:c}=(0,o2.o)(i.ownerLogin,i.name,o.name,a);if(c)return(0,nY.jsx)(o7,{});if(l)return(0,nY.jsx)(o8,{width:100,"data-testid":"contributors-skeleton"});if(!s||!s?.users.length)return null;let{users:d,totalCount:u}=s,h=o9(u," contributor","contributors");return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center"},children:[(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexDirection:"row"},children:(0,nY.jsx)(o3.Z,{children:d.slice(0,10).map((e,t)=>(0,nY.jsx)(iA.O,{className:t>5?"AvatarShowLarge":"AvatarShowMedium",src:e.primaryAvatarUrl,alt:e.login,"data-testid":"contributor-avatar","data-hovercard-url":(0,nJ.zP)({owner:e.login})},e.login))})}),(0,nY.jsxs)(rR.Z,{as:"button","aria-label":`Show ${h}"`,onClick:()=>{r(!0),t("CONTRIBUTORS.LIST.OPEN")},"data-testid":"contributors-count-button",sx:{ml:2,color:"fg.default"},children:[(0,nY.jsx)(rd.Z,{icon:rs.PeopleIcon}),e&&(0,nY.jsx)(ru.Z,{className:"react-contributors-title",sx:{mx:1,fontSize:0},children:"Contributors"}),(0,nY.jsx)(o4.Z,{sx:{mx:1,px:2,py:1},children:u})]}),n&&(0,nY.jsx)(oy.V,{title:h,onClose:()=>r(!1),width:"medium",height:s.totalCount>=12?"small":"auto",renderBody:()=>(0,nY.jsx)(rS.S,{sx:{overflowY:"auto",py:2},"data-testid":"contributor-dialog-list",children:d.map(e=>(0,nY.jsx)(o5,{user:e},e.login))})})]})}function o5({user:e}){let{sendRepoClickEvent:t}=(0,rx.a)(),{path:n,refInfo:r}=(0,rK.Br)(),i=(0,nX.H)();return(0,nY.jsxs)(rS.S.Item,{sx:{display:"flex",flexDirection:"row",fontSize:1,py:2,color:"fg.default","&:hover":{backgroundColor:"canvas.subtle"}},"data-testid":"contributor-dialog-row",onClick:()=>t("CONTRIBUTORS.LIST.USER"),children:[(0,nY.jsxs)(rR.Z,{as:iM.r,sx:{flex:1},muted:!0,to:e.profileLink,onClick:()=>t("CONTRIBUTORS.LIST.USER"),children:[(0,nY.jsx)(iA.O,{src:e.primaryAvatarUrl,alt:e.login,sx:{mr:2},"aria-hidden":"true"}),(0,nY.jsx)(ry.Z,{inline:!0,title:e.login,children:e.login})]}),(0,nY.jsx)(rS.S.TrailingVisual,{children:(0,nY.jsx)(rR.Z,{as:iM.r,muted:!0,to:(0,nJ.SV)({repo:i,branch:r.name,path:n,author:e.login}),onClick:()=>t("CONTRIBUTORS.LIST.COMMITS"),"aria-label":`${o9(e.commitsCount,"commit","commits")} by ${e.login}`,"data-testid":"commit-link",children:o9(e.commitsCount,"commit","commits")})})]})}function o9(e,t,n){return`${e} ${1===e?t:n}`}function o7(){return(0,nY.jsxs)(ru.Z,{sx:{color:"danger.fg"},children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertFillIcon}),"\xa0Cannot retrieve contributors info at this time."]})}try{(ei=o6).displayName||(ei.displayName="ContributorAvatars")}catch{}try{(eo=o5).displayName||(eo.displayName="ContributorRow")}catch{}try{(ea=o7).displayName||(ea.displayName="ContributorsError")}catch{}var ae=n(78912),at=n(39004);function an({fileNameId:e="file-name-id",id:t,fontSize:n,showCopyPathButton:r}){let i=(0,nX.H)(),{refInfo:o,path:a,action:s}=(0,rK.Br)();return(0,nY.jsx)(nY.Fragment,{children:(0,nY.jsx)(at.a,{path:a,repo:i,commitish:o.name,isFolder:"tree"===s,fileNameId:e,id:t,fontSize:n,showCopyPathButton:r&&""!==a})})}try{(es=an).displayName||(es.displayName="ReposHeaderBreadcrumb")}catch{}var ar=n(80490);function ai(){let{addToast:e}=(0,oU.V6)();return(0,n0.useCallback)(t=>e({type:"error",message:t}),[e])}function ao({size:e,buttonClassName:t,allowResizing:n,idEnding:r}){let i=(0,nX.H)(),{refInfo:o,path:a,action:s}=(0,rK.Br)(),l=ai(),{sendRepoClickEvent:c}=(0,rx.a)(),{refSelectorShortcut:d}=(0,rY.bx)(),u=o.name;return u===o.currentOid&&(u=o.name.slice(0,7)),(0,nY.jsx)(ar.cq,{currentCommitish:u,defaultBranch:i.defaultBranch,owner:i.ownerLogin,repo:i.name,canCreate:i.currentUserCanPush,cacheKey:o.listCacheKey,selectedRefType:"tree"===o.refType?"branch":o.refType,getHref:e=>`${(0,nJ.Qi)({repo:i,commitish:e,action:s,path:a})}${window.location.search}`,hotKey:d.hotkey,onBeforeCreate:e=>c("REF_SELECTOR_MENU.CREATE_BRANCH",{ref_name:e}),onCreateError:l,onOpenChange:e=>e&&c("REF_SELECTOR_MENU"),size:e,buttonClassName:t,allowResizing:n,idEnding:r||"repos-header-ref-selector",useFocusZone:!0})}try{(el=ao).displayName||(el.displayName="ReposHeaderRefSelector")}catch{}function aa({isStickied:e,showTree:t,treeToggleElement:n}){let r=()=>(0,nY.jsx)(an,{id:"sticky-breadcrumb",fileNameId:"sticky-file-name-id",fontSize:1}),i=({sx:e})=>(0,nY.jsx)(ae.z,{leadingVisual:rs.ArrowUpIcon,variant:"invisible",size:"small",sx:{color:"fg.default",...e},onClick:e=>{e.preventDefault(),window.scrollTo({top:0,behavior:"smooth"})},children:"Top"});return(0,nY.jsx)(n7.Z,{sx:{display:e?"flex":"none",minWidth:0,py:2,...e?{backgroundColor:"canvas.subtle",borderLeft:"1px solid var(--color-border-default)",borderRight:"1px solid var(--color-border-default)"}:{}},children:t?(0,nY.jsxs)(n7.Z,{sx:{mr:2,ml:3,textOverflow:"ellipsis",overflow:"hidden",display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",width:"100%"},children:[(0,nY.jsx)(r,{}),(0,nY.jsx)(i,{sx:{ml:2}})]}):(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center",overflow:"hidden",mx:2,flexDirection:"row",justifyContent:"space-between",width:"100%"},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center"},children:[e&&n,(0,nY.jsx)(n7.Z,{sx:{ml:1,mr:2},children:(0,nY.jsx)(ao,{buttonClassName:"ref-selector-class"})}),(0,nY.jsx)(n7.Z,{sx:{textOverflow:"ellipsis",overflow:"hidden",display:"flex"},children:(0,nY.jsx)(r,{})})]}),(0,nY.jsx)(i,{sx:{ml:2}})]})})}try{(ec=aa).displayName||(ec.displayName="FileNameStickyHeader")}catch{}try{(ed=StickyReposHeaderBreadcrumb).displayName||(ed.displayName="StickyReposHeaderBreadcrumb")}catch{}try{(eu=GoToTopButton).displayName||(eu.displayName="GoToTopButton")}catch{}let as=n0.memo(al);function al(){let e=iq();return(0,nY.jsxs)(n7.Z,{"aria-hidden":!0,sx:{display:"flex",color:"fg.muted",alignItems:"center",gap:"2px",fontSize:0},children:[(0,nY.jsx)(ru.Z,{sx:{mr:2},children:"Older"}),e.map((e,t)=>(0,nY.jsx)(n7.Z,{sx:{height:"0.5rem",width:"0.5rem",backgroundColor:e}},`blame-recency-color-${t}`)),(0,nY.jsx)(ru.Z,{sx:{ml:2},children:"Newer"})]})}try{(eh=al).displayName||(eh.displayName="BlameAgeLegend")}catch{}var ac=n(72278);function ad(){let{headerInfo:{isCSV:e,isRichtext:t,shortPath:n},renderedFileInfo:r,image:i,issueTemplate:o,discussionTemplate:a,viewable:s}=(0,rq.G)(),l=(0,rG.Q)(),[c]=(0,rJ.lr)(),d="1"===c.get("plain")||!!c.get("short_path")?.length,u=r&&!s||i,h=t||o||a||e||r,m=r?`short_path=${n}`:"plain=1",{getUrl:f}=(0,oY.B)(),p=!h||d||l?l?2:1:0,[x,y]=(0,n0.useState)(p);(0,n0.useLayoutEffect)(()=>{y(p)},[p]);let g=(0,rX.s)(),{viewCodeShortcut:b,viewPreviewShortcut:j,viewBlameShortcut:w}=(0,rY.bx)(),v=e=>{if(h||(e+=1),y(e),x!==e)switch(e){case 0:g(f({action:"blob",params:"",hash:""}));break;case 1:{let e=location.hash?.substring(1)??void 0;g(f({action:"blob",params:h?m:"",hash:e}));break}case 2:{let e=location.hash?.substring(1)??void 0;g(f({action:"blame",params:"",hash:e}))}}};if(u)return null;let N=[(0,nY.jsx)(ac.s.Button,{selected:1===x,"data-hotkey":b.hotkey,children:"Code"},"raw"),(0,nY.jsx)(ac.s.Button,{selected:2===x,"data-hotkey":w.hotkey,children:"Blame"},"blame")],C=(0,nY.jsx)(ac.s.Button,{selected:0===x,"data-hotkey":j.hotkey,children:"Preview"},"preview'"),k=(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:b.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>v(h?1:0)}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:w.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>v(h?2:1)}),h&&(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:j.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>v(0)})]}),S=h?u?[C]:[C,...N]:[...N];return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(ac.s,{"aria-label":"File view",size:"small",onChange:v,sx:{fontSize:1},children:S}),k]})}try{(em=ad).displayName||(em.displayName="BlobTabButtons")}catch{}var au=n(51188);function ah({className:e}){let{codeownerInfo:t}=(0,au.nO)(),n=(0,rq.G)(),{refInfo:r}=(0,rK.Br)(),i=(0,nX.H)(),o=(0,rG.Q)(),{headerInfo:{blobSize:a,gitLfsPath:s,lineInfo:{truncatedLoc:l,truncatedSloc:c},mode:d},viewable:u,rawLines:h}=n,m="symbolic link"===d?ap({rawLines:h,blame:o,repo:i,refInfo:r}):void 0;return(0,nY.jsxs)(n7.Z,{className:e,sx:{alignItems:"center"},children:[t&&(0,nY.jsx)(am,{codeownerInfo:t}),(0,nY.jsxs)(n7.Z,{sx:{fontSize:0,flex:"auto",pr:3,color:"fg.muted",minWidth:0},className:"text-mono",children:["file"!==d&&!m&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(ry.Z,{title:d,inline:!0,sx:{ml:1,mr:2,textTransform:"capitalize"},children:(0,nY.jsx)(ru.Z,{children:d})}),u&&(0,nY.jsx)(ru.Z,{sx:{color:"fg.muted",mr:1},children:"\xb7"})]}),u?(0,nY.jsxs)(nY.Fragment,{children:[m&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rR.Z,{as:iM.r,muted:!0,to:m,sx:{ml:1,mr:2},children:"Symbolic Link"}),(0,nY.jsx)(ru.Z,{sx:{color:"fg.muted",mr:1},children:"\xb7"})]}),(0,nY.jsx)(ry.Z,{title:a,inline:!0,sx:{maxWidth:"100%"},"data-testid":"blob-size",children:(0,nY.jsx)(ru.Z,{children:`${l} lines (${c} loc) \xb7 ${a}`})})]}):(0,nY.jsx)(ru.Z,{children:a}),s&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(ru.Z,{className:"file-info-divider"}),(0,nY.jsx)(rR.Z,{muted:!0,href:s,"aria-label":"Learn more about Git LFS",sx:{ml:2},children:(0,nY.jsx)(rd.Z,{icon:rs.QuestionIcon})}),(0,nY.jsx)(ru.Z,{children:" Stored with Git LFS"})]})]})]})}function am({codeownerInfo:{codeownerPath:e,ownedByCurrentUser:t,ownersForFile:n,ruleForPathLine:r}}){if(!(t||n))return null;let i=af(t,n,r),o=t?{color:"var(--color-accent-fg)"}:{};return(0,nY.jsx)(i$.Z,{id:"codeowners-tooltip","aria-label":i,children:e?(0,nY.jsx)(rR.Z,{"aria-labelledby":"codeowners-tooltip",href:e,muted:!t,sx:{...o},children:(0,nY.jsx)(rd.Z,{icon:rs.ShieldLockIcon,sx:{mr:2,mt:1}})}):(0,nY.jsx)(rd.Z,{icon:rs.ShieldLockIcon,sx:{mr:2,mt:1,...o}})})}function af(e,t,n){let r="Owned by ";return e&&(r+="you",t&&(r+=" along with ")),r+=t,n&&(r+=` (from CODEOWNERS line ${n})`),r}function ap({rawLines:e,blame:t,repo:n,refInfo:r}){if(!e||!e[0])return null;let i={owner:n.ownerLogin,repo:n.name,commitish:r.name,filePath:e[0].replaceAll("../","")};return t?(0,nJ.t4)(i):(0,nJ.C9)(i)}try{(ef=ah).displayName||(ef.displayName="CodeSizeDetails")}catch{}try{(ep=am).displayName||(ep.displayName="CodeOwnersBadge")}catch{}var ax=n(16685);function ay({editEnabled:e,githubDevUrl:t,ghDesktopPath:n,onBranch:r}){let{sendRepoClickEvent:i}=(0,rx.a)(),o=(0,ax.f)(["windows","mac"]),{openWithGitHubDevShortcut:a}=(0,rY.bx)();return(0,nY.jsxs)(rS.S.Group,{title:"Open with...",children:[t?(0,nY.jsxs)(rS.S.LinkItem,{onClick:()=>i("BLOB_EDIT_DROPDOWN.DEV_LINK",{edit_enabled:e}),className:"js-blob-dropdown-click js-github-dev-shortcut",href:t,"data-hotkey":a.hotkey,children:["github.dev",(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(rf.I,{children:(0,nY.jsx)("kbd",{children:"."})})})]}):null,r&&o&&n?(0,nY.jsx)(rS.S.LinkItem,{onClick:()=>i("BLOB_EDIT_DROPDOWN.DESKTOP"),href:n,children:"GitHub Desktop"}):null]})}function ag(e,t,n){let r=(0,ax.f)(["windows","mac"]);return!!(e||t&&!r&&n)}try{(ex=ay).displayName||(ex.displayName="OpenWithActionItems")}catch{}function ab({editAllowed:e,hasOpenWithItem:t}){let n=(0,rq.G)(),{refInfo:{canEdit:r}}=(0,rK.Br)(),{githubDevUrl:i}=(0,rK.Ou)(),{sendRepoClickEvent:o}=(0,rx.a)(),{getUrl:a}=(0,oY.B)(),{headerInfo:{ghDesktopPath:s,onBranch:l}}=n;return(0,nY.jsxs)(nY.Fragment,{children:[e&&(0,nY.jsx)(rS.S.Group,{title:"Edit file",children:(0,nY.jsxs)(rS.S.Item,{as:iM.r,onClick:()=>o("BLOB_EDIT_DROPDOWN.IN_PLACE"),to:a({action:"edit"}),"aria-keyshortcuts":"e",children:[(0,nY.jsx)(n7.Z,{sx:{display:"flex"},children:"Edit in place"}),(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(rf.I,{children:(0,nY.jsx)("kbd",{children:"e"})})})]})}),e&&t&&(0,nY.jsx)(rS.S.Divider,{}),t&&(0,nY.jsx)(ay,{editEnabled:r,githubDevUrl:i,ghDesktopPath:s,onBranch:l})]})}try{(ey=ab).displayName||(ey.displayName="EditMenuActionItems")}catch{}function aj({shortcut:e}){return(0,nY.jsx)(nY.Fragment,{children:e.text?.split(" ").map(e=>(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)("kbd",{children:e},e)," "]}))})}try{(eg=aj).displayName||(eg.displayName="KeyboardVisual")}catch{}function aw({viewable:e,onCopy:t,name:n,updateTooltipMessage:r,all:i}){let{sendRepoClickEvent:o}=(0,rx.a)(),{rawBlobUrl:a}=(0,rq.G)(),{downloadRawContentShortcut:s}=(0,rY.bx)(),l=(0,n0.useCallback)(async()=>await av(a,n),[n,a]);return(0,nY.jsxs)(rS.S.Group,{title:"Raw file content",children:[i&&(0,nY.jsx)(aN,{viewable:e,onCopy:t,updateTooltipMessage:r}),i&&(0,nY.jsx)(aC,{onClick:()=>o("BLOB_RAW_DROPDOWN.VIEW"),rawHref:a}),(0,nY.jsxs)(rS.S.LinkItem,{onClick:l,children:["Download",s.text&&(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(aj,{shortcut:s})})]})]})}async function av(e,t){let n=await fetch(e,{method:"get"}),r=await n.blob(),i=document.createElement("a");i.setAttribute("download",t);let o=URL.createObjectURL(r);i.href=o,i.setAttribute("target","_blank"),i.click(),URL.revokeObjectURL(o)}function aN({viewable:e,onCopy:t,updateTooltipMessage:n}){let{copyRawContentShortcut:r}=(0,rY.bx)();return e?(0,nY.jsxs)(rS.S.Item,{onClick:async()=>{let e=await t(),{ariaLabel:r}=ib(e);n(r)},children:["Copy",r.text&&(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(aj,{shortcut:r})})]}):null}function aC({onClick:e,rawHref:t}){let{viewRawContentShortcut:n}=(0,rY.bx)();return(0,nY.jsxs)(rS.S.LinkItem,{onClick:e,href:t,children:["View",n.text&&(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(aj,{shortcut:n})})]})}try{(eb=aw).displayName||(eb.displayName="RawMenuActionItems")}catch{}try{(ej=aN).displayName||(ej.displayName="CopyActionItem")}catch{}try{(ew=aC).displayName||(ew.displayName="RawActionItem")}catch{}function ak({toc:e,openPanel:t,setOpenPanel:n,isDirectoryReadme:r}){let i=n0.useRef(null);return aS(e)?(0,nY.jsx)(rl.h,{ref:i,sx:{color:"var(--fgColor-muted, var(--color-fg-muted))",mr:r?0:2},icon:rs.ListUnorderedIcon,variant:"invisible","aria-label":"Outline","aria-pressed":"toc"===t,onClick:()=>{n&&n("toc"===t?void 0:"toc",i.current)},size:"small"}):null}function aS(e){return e&&e.length>=2}try{(ev=ak).displayName||(ev.displayName="TableOfContents")}catch{}function aI({currentStickyLines:e}){let t=Array.from(e.values());return(0,nY.jsxs)(n7.Z,{sx:{overflow:"hidden",display:"flex"},children:[(0,nY.jsx)(n7.Z,{className:"react-line-numbers",sx:{marginLeft:"2px"},children:t.map(e=>(0,nY.jsx)(iY._,{codeLineData:e},`sticky-header-line-number-${e.lineNumber}`))}),(0,nY.jsx)("div",{className:"react-code-lines",children:t.map(e=>(0,nY.jsx)(iK.E,{codeLineData:e,codeLineToSectionMap:void 0,onClick:()=>(0,r2.v)({line:e.lineNumber})},`sticky-header-line-${e.lineNumber}`))})]})}try{(eN=aI).displayName||(eN.displayName="StickyLinesHeader")}catch{}function aR({openPanel:e,setOpenPanel:t,showTree:n,validCodeNav:r,treeToggleElement:i,searchTerm:o,setSearchTerm:a,currentStickyLines:s,focusedSearchResult:l,setFocusedSearchResult:c,searchResults:d,searchingText:u,stickyHeaderRef:h,copilotInfo:m}){let f=(0,rq.G)(),p=(0,ie.X)(h),{refInfo:x,path:g}=(0,rK.Br)(),b=il(),{sendRepoClickEvent:j}=(0,rx.a)(),w=(0,r9.nx)(),{copyFilePathShortcut:v}=(0,rY.bx)(),{copyPermalinkShortcut:N}=(0,rY.bx)(),{githubDevUrl:C}=(0,rK.Ou)(),{headerInfo:{toc:k,onBranch:S,ghDesktopPath:I},viewable:R}=f,Z=ag(C,S,I),E=!!f.headerInfo.gitLfsPath,T=(0,rG.Q)(),L=ij(),{createPermalink:B}=(0,oY.B)(),{addToast:_}=(0,oU.V6)(),{findInFileOpen:D,setFindInFileOpen:F}=is(),O=(0,n0.useRef)(null),[P,A,M]=(0,oV.a)("raw-actions-message-tooltip",O,{direction:"nw"}),H=ir();return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(n7.Z,{ref:r9.ik,sx:{display:"flex",flexDirection:"column",width:"100%",position:"absolute"},children:[(0,nY.jsx)(n7.Z,{className:"react-blob-sticky-header",children:(0,nY.jsx)(aa,{isStickied:p,showTree:n,treeToggleElement:i})}),(0,nY.jsxs)(n7.Z,{sx:{pl:2,py:2,display:"flex",flex:1,alignItems:"center",justifyContent:"space-between",backgroundColor:"canvas.subtle",border:"1px solid var(--borderColor-default, var(--color-border-default))",borderRadius:p?"0px":"6px 6px 0px 0px"},children:[(0,nY.jsx)(oK.N,{as:"h2",text:"File metadata and controls"}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center",gap:2,minWidth:0},children:[(0,nY.jsx)(ad,{}),(0,nY.jsx)(ah,{className:"react-code-size-details-in-header"}),(0,nY.jsx)(oz.y,{copilotInfo:m,className:"react-code-size-details-in-header",view:T?"blame":"preview"})]}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center",gap:2,mr:2},children:[v.hotkey&&(0,nY.jsx)(aL,{hotkey:v.hotkey,onActivate:()=>{(0,ip.z)(g),_({type:"success",message:"Path copied!"})}}),N.hotkey&&(0,nY.jsx)(aL,{hotkey:N.hotkey,onActivate:()=>{(0,ip.z)(B({absolute:!0})),_({type:"success",message:"Permalink copied!"})}}),(0,nY.jsxs)(n7.Z,{className:"react-blob-header-edit-and-raw-actions",sx:{gap:2},children:[(0,nY.jsx)(aE,{onCopy:L,fileName:f.displayName,isLfs:E}),(0,nY.jsx)(aZ,{})]}),b===y.Code&&!T&&r&&(0,nY.jsx)(aT,{isCodeNavOpen:"codeNav"===e,setCodeNavOpen:e=>{e&&j("BLOB_SYMBOLS_MENU.OPEN"),localStorage.setItem("codeNavOpen",e?"codeNav":""),H(null,e),t(e?"codeNav":void 0)},size:"small",searchingText:u.selectedText}),!T&&(0,nY.jsx)(ak,{toc:k,openPanel:e,setOpenPanel:t}),(0,nY.jsxs)(n7.Z,{className:"react-blob-header-edit-and-raw-actions-combined",children:[M,(0,nY.jsxs)(oS.P,{anchorRef:O,children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsx)(rl.h,{icon:rs.KebabHorizontalIcon,"aria-label":"Edit and raw actions",className:"js-blob-dropdown-click",size:"small",sx:{color:"fg.muted"},title:"More file actions",variant:"invisible","data-testid":"more-file-actions-button",onBlur:A})}),(0,nY.jsx)(oS.P.Overlay,{className:"react-blob-header-edit-and-raw-actions-combined",width:"small",sx:{maxHeight:"55vh",overflowY:"auto"},children:(0,nY.jsxs)(rS.S,{children:[(x.canEdit&&R||Z)&&(0,nY.jsxs)("div",{className:"react-navigation-menu-edit-and-raw-actions",children:[(0,nY.jsx)(ab,{editAllowed:x.canEdit&&R,hasOpenWithItem:Z}),(0,nY.jsx)(rS.S.Divider,{})]}),(0,nY.jsx)(nY.Fragment,{children:(0,nY.jsx)(aw,{viewable:R,onCopy:L,name:f.displayName,updateTooltipMessage:P,all:!0})})]})})]})]})]})]}),T&&(0,nY.jsxs)(n7.Z,{sx:{px:"12px",py:2,height:"44px",display:"flex",flexShrink:0,alignItems:"center",border:"1px solid",borderColor:"border.default",borderTop:0,justifyContent:"space-between",backgroundColor:"canvas.default"},children:[(0,nY.jsx)(as,{}),(0,nY.jsx)(o6,{})]})]}),D&&(0,nY.jsx)(o1,{stickied:p,searchTerm:o,focusedSearchResult:l,setFocusedSearchResult:c,setSearchTerm:a,searchResults:d,onClose:()=>{F(!1),"codeNav"===e&&a(u.selectedText)}}),(0,nY.jsx)(n7.Z,{children:!T&&s.size>0&&(0,nY.jsx)(n7.Z,{sx:{zIndex:1,background:"var(--bgColor-default, var(--color-canvas-default))",top:w,position:"absolute",width:"100%",border:"1px solid var(--borderColor-default, var(--color-border-default))",borderBottom:"none",borderTop:"none",boxShadow:"0 1px 0 var(--borderColor-default, var(--color-border-default))",tableLayout:"fixed"},children:(0,nY.jsx)(aI,{currentStickyLines:s})})})]})}function aZ(){let e=(0,rq.G)(),{getUrl:t}=(0,oY.B)(),{refInfo:{canEdit:n}}=(0,rK.Br)(),{sendRepoClickEvent:r}=(0,rx.a)(),{githubDevUrl:i}=(0,rK.Ou)(),o=(0,rX.s)(),{editFileShortcut:a,openWithGitHubDevShortcut:s,openWithGitHubDevInNewWindowShortcut:l}=(0,rY.bx)(),{headerInfo:{editInfo:{editTooltip:c},ghDesktopPath:d,onBranch:u},viewable:h}=e,m=ag(i,u,d);return(n||m)&&h?(0,nY.jsxs)(nY.Fragment,{children:[i&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rR.Z,{className:"js-github-dev-shortcut d-none","data-hotkey":s.hotkey,href:i}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:s.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{o(i+window.location.pathname.substring(1))}}),(0,nY.jsx)(rR.Z,{className:"js-github-dev-new-tab-shortcut d-none","data-hotkey":l.hotkey,href:i,target:"_blank"}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:l.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{window.open(i,"_blank")}})]}),(0,nY.jsxs)(oG.Z,{children:[(0,nY.jsx)(i$.Z,{direction:"nw",text:c,children:n?(0,nY.jsx)(rl.h,{as:iM.r,"aria-label":"Edit file","data-hotkey":a.hotkey,icon:rs.PencilIcon,to:t({action:"edit"}),size:"small",sx:{...oQ.A,borderTopRightRadius:0,borderBottomRightRadius:0,borderRightWidth:0},"data-testid":"edit-button"}):(0,nY.jsx)(oX,{icon:rs.PencilIcon,sx:{borderTopRightRadius:0,borderBottomRightRadius:0,borderRightWidth:0},"aria-label":"Edit file",disabled:!0})}),(0,nY.jsxs)(oS.P,{onOpenChange:e=>e&&r("BLOB_EDIT_DROPDOWN"),children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsx)(rl.h,{icon:rs.TriangleDownIcon,size:"small","aria-label":"More edit options","data-testid":"more-edit-button"})}),(0,nY.jsx)(oS.P.Overlay,{align:"end",children:(0,nY.jsx)(rS.S,{children:(0,nY.jsx)(ab,{editAllowed:n,hasOpenWithItem:m})})})]})]}),n&&(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:"e,E",onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{o(t({action:"edit"}))}})]}):null}function aE({onCopy:e,fileName:t,isLfs:n}){let{viewRawContentShortcut:r,copyRawContentShortcut:i,downloadRawContentShortcut:o}=(0,rY.bx)(),{renderImageOrRaw:a,renderedFileInfo:s,viewable:l,image:c,rawBlobUrl:d}=(0,rq.G)(),u=(0,rX.s)(),{addToast:h}=(0,oU.V6)(),m=(0,n0.useRef)(null),[f,p,x]=(0,oV.a)("raw-copy-message-tooltip",m),y=new URL(d,n8.ssrSafeLocation.origin);y.searchParams.set("download","");let g={"aria-label":"Download raw content",icon:rs.DownloadIcon,size:"small",onClick:async()=>{n||await av(d,t)},"data-testid":"download-raw-button","data-hotkey":o.hotkey,sx:{borderTopLeftRadius:0,borderBottomLeftRadius:0}};return!n&&(s&&!l||c)?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(i$.Z,{"aria-label":"Download raw file",children:(0,nY.jsx)(rl.h,{"aria-label":"Download raw content",icon:rs.DownloadIcon,size:"small",onClick:async()=>{await av(d,t)},"data-testid":"download-raw-button","data-hotkey":o.hotkey})}),(0,nY.jsx)(rA.P,{buttonTestLabel:"download-raw-button-shortcut",buttonFocusId:r1.KG,buttonHotkey:o.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:async()=>{await av(d,t)}})]}):(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(oG.Z,{children:[(0,nY.jsx)(oq.Q,{href:d,download:a?"true":void 0,size:"small",sx:{linkButtonSx:oQ.A,px:2},"data-testid":"raw-button","data-hotkey":r.hotkey,children:"Raw"}),!n&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rl.h,{ref:m,"aria-label":"Copy raw content",icon:rs.CopyIcon,size:"small",onFocus:()=>f("Copy raw file"),onMouseEnter:()=>f("Copy raw file"),onMouseLeave:p,onClick:async()=>{let t=await e(),{ariaLabel:n}=ib(t);f(n)},"data-testid":"copy-raw-button","data-hotkey":i.hotkey,onBlur:p}),x]}),(0,nY.jsx)(i$.Z,{"aria-label":"Download raw file",children:n?(0,nY.jsx)(rl.h,{as:"a","data-turbo":"false",href:y.toString(),...g}):(0,nY.jsx)(rl.h,{...g})})]}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:r.hotkey,buttonTestLabel:"raw-button-shortcut",onlyAddHotkeyScopeButton:!0,onButtonClick:()=>u(d)}),!n&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rA.P,{buttonTestLabel:"copy-raw-button-shortcut",buttonFocusId:r1.KG,buttonHotkey:i.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:async()=>{let t=await e(),{ariaLabel:n,content:r}=ib(t);h({message:n,icon:r})}}),(0,nY.jsx)(rA.P,{buttonTestLabel:"download-raw-button-shortcut",buttonFocusId:r1.KG,buttonHotkey:o.hotkey,onlyAddHotkeyScopeButton:!0,onButtonClick:async()=>{await av(d,t)}})]})]})}function aT({isCodeNavOpen:e,setCodeNavOpen:t,size:n,searchingText:r}){let{toggleSymbolsShortcut:i}=(0,rY.bx)(),o=!(0,n6.O)().openSymbolsOption.enabled&&!e;return(0,nY.jsx)(i$.Z,{direction:"nw",text:e?"Close symbols panel":"Open symbols panel",children:(0,nY.jsx)(rl.h,{"aria-label":"Symbols","aria-pressed":e,"aria-expanded":e,"aria-controls":"symbols-pane",icon:rs.CodeSquareIcon,className:o&&r?"react-button-with-indicator":"","data-hotkey":i.hotkey,onClick:()=>{(0,ii.NC)(!0),t(!e)},variant:"invisible",sx:{color:"fg.muted",position:"relative"},"data-testid":"symbols-button",id:"symbols-button",size:n})})}function aL({hotkey:e,onActivate:t}){return(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:e,onButtonClick:t})}try{(eC=aR).displayName||(eC.displayName="BlobViewHeader")}catch{}try{(ek=aZ).displayName||(ek.displayName="EditMenu")}catch{}try{(eS=aE).displayName||(eS.displayName="RawGroup")}catch{}try{(eI=aT).displayName||(eI.displayName="SymbolsButton")}catch{}try{(eR=aL).displayName||(eR.displayName="KeyboardShortcut")}catch{}var aB=n(14744),a_=n(64077),aD=n(4751);function aF({status:e,oid:t}){let n=(0,nX.H)(),[r,i]=(0,aD.fQ)(t,n);return e?(0,nY.jsx)(aD.vC,{statusRollup:e,combinedStatus:r,onWillOpenPopup:i,size:"small"}):null}try{(eZ=aF).displayName||(eZ.displayName="ReposChecksStatusBadge")}catch{}function aO({commitCount:e}){let t=(0,nX.H)(),{refInfo:n,path:r}=(0,rK.Br)(),[i,o,a]=(0,a_.f)(t.ownerLogin,t.name,n.name,r),[s,l]=(0,n0.useState)(!1);return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",border:"1px solid",borderColor:"border.default",borderRadius:6,mb:3},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center",gap:2,minWidth:"273px",pr:2,pl:3,py:2},children:[(0,nY.jsx)(oK.N,{as:"h2",text:"Latest commit"}),a?(0,nY.jsx)(aP,{}):o?(0,nY.jsx)(o8,{width:120,"data-testid":"loading"}):i?(0,nY.jsx)(aA,{commit:i,detailsOpen:s,setDetailsOpen:l}):null,(0,nY.jsx)(aM,{commit:i,commitCount:e,detailsOpen:s,setDetailsOpen:l})]}),s&&i&&(0,nY.jsx)(n7.Z,{sx:{display:i.bodyMessageHtml?"inherit":["inherit","none","none"]},children:(0,nY.jsx)(aW,{commit:i})})]})}function aP(){return(0,nY.jsxs)(ru.Z,{sx:{color:"attention.fg"},"data-testid":"latest-commit-error-message",children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertFillIcon}),"\xa0Cannot retrieve latest commit at this time."]})}function aA({commit:e,detailsOpen:t,setDetailsOpen:n}){let r=(0,nX.H)(),i=`data-hovercard-url=${(0,nJ.QY)({owner:r.ownerLogin,repo:r.name,commitish:e.oid})} `,o=az(e.shortMessageHtmlLink,i);return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",minWidth:0,fontSize:1,alignItems:"center",width:"max-content",gap:2,flexBasis:0,flexGrow:1},"data-testid":"latest-commit",children:[(0,nY.jsx)(aB.D,{author:e.author,repo:r}),(0,nY.jsxs)(n7.Z,{className:"react-last-commit-message",sx:{alignItems:"center",minWidth:0,gap:2},children:[(0,nY.jsx)(n7.Z,{className:"Truncate",sx:{fontSize:1,alignItems:"center"},children:e.shortMessageHtmlLink&&(0,nY.jsx)(iu.WZ,{className:"Truncate-text","data-testid":"latest-commit-html",unverifiedHTML:o})}),e.bodyMessageHtml&&(0,nY.jsx)(a$,{detailsOpen:t,setDetailsOpen:n}),(0,nY.jsx)(aF,{oid:e.oid,status:e.status})]}),(0,nY.jsx)(ru.Z,{className:"react-last-commit-summary-timestamp",sx:{color:"fg.muted",fontSize:0},children:(0,nY.jsx)(iH.Z,{datetime:e.date,tense:"past"})})]})}function aM({commit:e,commitCount:t,detailsOpen:n,setDetailsOpen:r}){let i=e?.oid.slice(0,7);return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",gap:2},children:[(0,nY.jsx)(n7.Z,{sx:{display:"flex",alignItems:"center"},"data-testid":"latest-commit-details",children:e&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(ru.Z,{className:"react-last-commit-oid-timestamp",sx:{color:"fg.muted",fontSize:0},children:[(0,nY.jsx)(rR.Z,{as:iM.r,to:e.url,className:"Link--secondary","aria-label":`Commit ${i}`,children:i}),"\xa0\xb7\xa0",(0,nY.jsx)(iH.Z,{datetime:e.date,tense:"past"})]}),(0,nY.jsx)(ru.Z,{className:"react-last-commit-timestamp",sx:{color:"fg.muted",fontSize:0},children:(0,nY.jsx)(iH.Z,{datetime:e.date,tense:"past"})})]})}),(0,nY.jsx)(oK.N,{as:"h2",text:"History"}),(0,nY.jsx)(aH,{className:"react-last-commit-history-group",size:"small",leadingIcon:rs.HistoryIcon,children:(0,nY.jsx)(ru.Z,{sx:{color:"fg.default"},children:t?`${t} Commits`:"History"})}),(0,nY.jsx)(n7.Z,{sx:{display:["inherit","none","none"]},children:(e?.shortMessageHtmlLink||e?.bodyMessageHtml)&&(0,nY.jsx)(a$,{detailsOpen:n,setDetailsOpen:r,useMediumButton:!0})}),(0,nY.jsx)(i$.Z,{"aria-label":"Commit history",children:(0,nY.jsx)(aH,{className:"react-last-commit-history-icon",leadingIcon:rs.HistoryIcon})})]})}function aH({children:e,className:t,leadingIcon:n,size:r}){let{sendRepoClickEvent:i}=(0,rx.a)(),{refInfo:o,path:a}=(0,rK.Br)(),s=(0,nX.H)();return(0,nY.jsx)(oq.Q,{"aria-label":"Commit history",className:t,sx:{alignItems:"center",color:"fg.default",...oQ.A},onClick:()=>i("HISTORY_BUTTON"),href:(0,nJ.w2)({owner:s.ownerLogin,repo:s.name,ref:o.name,path:a}),variant:"invisible",size:r,leadingVisual:n,children:e})}function a$({detailsOpen:e,setDetailsOpen:t,useMediumButton:n}){return(0,nY.jsx)(rl.h,{"aria-label":"Open commit details",icon:rs.EllipsisIcon,sx:{color:"fg.muted",minWidth:"28px"},onClick:()=>t(!e),variant:"invisible","aria-pressed":e,"aria-expanded":e,"data-testid":"latest-commit-details-toggle",size:n?"medium":"small"})}function aW({commit:e}){let t=e?.oid.slice(0,7);return(0,nY.jsxs)(n7.Z,{sx:{backgroundColor:"canvas.subtle",borderTop:"1px solid",borderColor:"border.default",borderRadius:"0px 0px 6px 6px",px:3,py:2,flexGrow:1},children:[(0,nY.jsxs)(n7.Z,{sx:{display:["flex","none","none"],flexDirection:"column"},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",minWidth:0,gap:2,alignItems:"center"},children:[e.shortMessageHtmlLink&&(0,nY.jsx)(iu.WZ,{className:"Truncate-text","data-testid":"latest-commit-html",html:e.shortMessageHtmlLink,sx:{"> a":{color:"var(--fgColor-default, var(--color-fg-default)) !important"}}}),(0,nY.jsx)(aF,{oid:e.oid,status:e.status})]}),(0,nY.jsx)(rR.Z,{as:iM.r,to:e.url,className:"Link--secondary","aria-label":`Commit ${t}`,children:t}),e.bodyMessageHtml&&(0,nY.jsx)("br",{})]}),e.bodyMessageHtml&&(0,nY.jsx)(n7.Z,{sx:{mt:[2,0,0],color:"fg.muted"},children:(0,nY.jsx)(iu.WZ,{className:"Truncate-text","data-testid":"latest-commit-html",html:e.bodyMessageHtml,sx:{whiteSpace:"pre-wrap"}})})]})}function az(e,t){let n="";if(e){let r=e.split("<a ");for(let e of r)if(""!==e){if(e.includes("data-hovercard-url")){n=n.concat("<a ",e);continue}n=n.concat(...["<a ",t,e])}}return n}try{(eE=aO).displayName||(eE.displayName="LatestCommitSingleLine")}catch{}try{(eT=aP).displayName||(eT.displayName="CommitErrorMessage")}catch{}try{(eL=aA).displayName||(eL.displayName="CommitSummary")}catch{}try{(eB=aM).displayName||(eB.displayName="HistoryLink")}catch{}try{(e_=aH).displayName||(e_.displayName="HistoryLinkButton")}catch{}try{(eD=a$).displayName||(eD.displayName="CommitDetailsButton")}catch{}try{(eF=aW).displayName||(eF.displayName="CommitDetails")}catch{}var aU=n(56302),aG=n(64937);let aq={definitions:void 0,localReferences:void 0,crossReferences:void 0,error:!1};function aV(e,t,n,r){let[i,o]=(0,n0.useState)(aq);return(0,n0.useEffect)(()=>{(async()=>{if(n<0||r<0)return;let{definitions:i,localReferences:a,crossReferences:s,setLoading:l}=e.getDefinitionsAndReferences(t,n,r);o(aq);try{let[e,t,n]=await Promise.all([i,a,s]);o({definitions:e,localReferences:t,crossReferences:n,error:!1})}catch(e){o({...aq,error:!0})}finally{l(!1)}})()},[e,t,n,r]),i}var aK=n(92110),aY=n(13570);function aQ({reference:e,isHighlighted:t,href:n,onClick:r,role:i,ariaLevel:o,symbol:a,index:s,focusElement:l}){let[c,d]=(0,n0.useState)(null);(0,n0.useEffect)(()=>{if(l){let e=document.getElementById(`find-in-file-item-${s}`);e&&e.focus()}},[l,s]);let u=(0,aK.RC)(e.highlightedText,e.stylingDirectives,e.bodyText);return(0,nY.jsx)(rR.Z,{as:iM.r,className:"blob-code blob-code-inner",to:n,role:i,sx:{display:"block",p:0,fontWeight:400,fontSize:"12px",":hover:not([disabled])":{bg:"canvas.default"},":hover":{textDecoration:"none"},'[data-component="text"]':{gridArea:"auto"},gridTemplateAreas:"text",whiteSpace:"break-spaces"},onClick:e=>{e.ctrlKey||e.metaKey||r?.()},onSelect:r,id:`find-in-file-item-${s}`,"aria-current":t?"location":void 0,"aria-level":o,onKeyDown:e=>{"ArrowDown"===e.key?(aJ("nextElementSibling"),e.preventDefault()):"ArrowUp"===e.key&&(aJ("previousElementSibling"),e.preventDefault())},children:(0,nY.jsx)(n7.Z,{sx:{p:1,py:"5px",...t?{background:"var(--color-attention-subtle)",boxShadow:"inset 2px 0 0 var(--color-attention-fg)"}:{}},children:(0,nY.jsxs)("div",{className:"d-flex",children:[(0,nY.jsx)(n7.Z,{className:"text-small blob-num color-fg-muted",sx:{width:"auto",minWidth:"auto"},children:e.lineNumber}),(0,nY.jsxs)(n7.Z,{sx:{overflow:"hidden",whiteSpace:"pre",position:"relative"},children:[null!==c&&(0,nY.jsxs)("div",{id:`offset-${e.href(!1)}`,style:{marginLeft:-c},children:[a.length>0&&(0,nY.jsx)(aY.R,{symbols:[e],lineNumber:e.lineNumber,sx:{overflow:"initial"},isNotUsingWhitespace:!0}),(0,nY.jsx)(iu.WZ,{sx:{position:"relative",width:"100%",overflow:"hidden"},html:u,"aria-current":t?"location":void 0})]}),(0,nY.jsx)("span",{ref:e=>d(e?.offsetWidth??null),style:{visibility:"hidden",position:"absolute",whiteSpace:"pre"},children:aX(a,e)})]})]})})})}function aX(e,t){let n=34-e.length,r=t.bodyText.slice(0,t.ident.start.column);if(n<=0)return r;let i=t.bodyText.slice(t.ident.start.column+e.length).trimEnd();n=Math.max(n/2,n-i.length);let o=r.split(" "),a=[];for(let e=o.length-1;e>=0;e--){let t=o[e];if(a.unshift(t),a.join(" ").length<=n)o.pop();else break}let s=o.join(" "),l=(r.slice(s.length).match(/^[ \t]*/)||[])[0]??"";return`${o.join(" ")}${l}`}function aJ(e){let{activeElement:t}=document,n=t?.[e];if(n){if("treeitem"!==n.role&&"nextElementSibling"===e){let e=n.querySelector('[role="treeitem"]');e?.focus()}else n.focus()}}try{(eO=aQ).displayName||(eO.displayName="CodeNavCell")}catch{}function a0({results:e,repo:t,filePath:n,highlightedIndex:r,isDefinition:i,onClick:o,offset:a,initiallyExpanded:s,enableExpandCollapse:l,symbol:c,setFocusOnFile:d}){let u=!!(0,rG.Q)(),[h,m]=(0,n0.useState)(s),f=(0,nX.H)(),{path:p}=(0,rK.Br)(),x=n0.useRef(null),[y,g]=(0,n0.useState)(!1),b=(0,ii.nJ)(),j=e.slice(0,10),w=e.length>10?e.slice(10):[],{sendRepoClickEvent:v}=(0,rx.a)(),N=f.ownerLogin===t.ownerLogin&&f.name===t.name,C=(0,n0.useCallback)(e=>{"Enter"===e.key||" "===e.key?(m(!h),e.preventDefault()):"ArrowLeft"===e.key?m(!1):"ArrowRight"===e.key?(m(!0),h&&x.current?.focus()):"ArrowDown"===e.key?(aJ("nextElementSibling"),e.preventDefault()):"ArrowUp"===e.key&&(aJ("previousElementSibling"),e.preventDefault())},[h]);(0,n0.useEffect)(()=>{r&&r>=10+a&&g(!0)},[r,a]),(0,n0.useEffect)(()=>{d&&b&&x.current?.focus()},[d,b]),(0,n0.useEffect)(()=>{b||document.getElementById(r1.KG)?.focus()},[b]);let k=`${n}-${i?"definition":"reference"}-group`;return(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsxs)(n7.Z,{sx:{fontSize:0,px:3,py:2,display:"flex",justifyContent:"space-between",borderTop:"1px solid",borderColor:"border.muted",cursor:l?"pointer":"auto"},onClick:l?()=>m(!h):void 0,onKeyDown:C,ref:x,children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex"},children:[i&&f.id!==t.id&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(iA.O,{square:!0,src:t.ownerAvatar,sx:{mr:2,backgroundColor:"#FFFFFF"},size:16}),(0,nY.jsx)(n7.Z,{sx:{fontWeight:"600",mr:1},children:t.name})]}),(0,nY.jsxs)(n7.Z,{as:"button","aria-expanded":h,"aria-controls":k,sx:{fontWeight:"400",color:"fg.muted",display:"flex",flexDirection:"row",backgroundColor:"canvas.default",border:"none",padding:0},children:[l&&(0,nY.jsx)(rd.Z,{"aria-hidden":"true",icon:h?rs.ChevronDownIcon:rs.ChevronRightIcon}),(0,nY.jsx)(n7.Z,{sx:{display:"table",width:"100%",tableLayout:"fixed"},children:(0,nY.jsxs)(ry.Z,{"aria-label":`${i?"Definitions":"References"} in ${n!==p?n:"this file"}`,title:n,sx:{direction:"rtl",maxWidth:"100%",pl:2,display:"table-cell",textAlign:"left"},children:["\u200E",N&&n===p?"In this file":n,"\u200E"]})})]})]}),e&&!h&&(0,nY.jsx)(o4.Z,{sx:{ml:2},children:e.length})]}),h&&(0,nY.jsxs)(n7.Z,{"aria-label":`Results in ${n!==p?n:"this file"}`,id:k,sx:{overflowX:"hidden"},role:"group",className:"code-nav-file-information",children:[j.map((e,t)=>(0,nY.jsx)(aQ,{reference:e,isHighlighted:r===t+a,href:e.href(u),onClick:()=>{o&&o(t+a),v("BLOB_SYMBOLS_MENU.SYMBOL_DEFINITION_CLICK")},symbol:c,index:t+a},`codeNavigation${t+a}`)),y&&w.map((e,t)=>(0,nY.jsx)(aQ,{reference:e,isHighlighted:r===t+a+10,href:e.href(u),onClick:()=>{o&&o(t+a+10),v("BLOB_SYMBOLS_MENU.SYMBOL_DEFINITION_CLICK")},symbol:c,index:t+a+10,focusElement:0===t},`codeNavigation${t+a+10}`)),w.length>0&&(0,nY.jsx)(n7.Z,{sx:{px:3,pt:1,pb:2,fontSize:0,color:"fg.muted",borderColor:"border.muted"},children:(0,nY.jsx)(ae.z,{leadingVisual:y?rs.FoldIcon:rs.UnfoldIcon,onClick:()=>g(!y),sx:{color:"fg.default"},variant:"invisible",size:"small","aria-selected":!1,children:y?"Show less":"Show more"})})]})]},n)}try{(eP=a0).displayName||(eP.displayName="CodeNavFileInformation")}catch{}function a1({definitions:e,references:t,highlightedIndex:n,initiallyExpanded:r,enableExpandCollapse:i,onClick:o,symbol:a,setFocusOnFile:s}){let l=(0,n0.useMemo)(()=>{let n={};if(e)for(let t of e){let e=t.pathKey();n[e]||(n[e]=[]),n[e].push(t)}else if(t)for(let e of t){let t=e.pathKey();n[t]||(n[t]=[]),n[t].push(e)}return n},[e,t]),c=0;return(0,nY.jsx)(n7.Z,{children:Object.keys(l).map((t,d)=>{let u=l[t],h=(0,nY.jsx)(a0,{repo:u[0].repo,filePath:u[0].path,results:u,highlightedIndex:n,isDefinition:void 0!==e&&e.length>0,onClick:o,offset:c,initiallyExpanded:r,enableExpandCollapse:i,symbol:a,setFocusOnFile:0===d&&s},t);return c+=u.length,h})})}try{(eA=a1).displayName||(eA.displayName="CodeNavInfoPanelData")}catch{}let a2=(0,n0.lazy)(()=>n.e("app_assets_modules_react-code-view_components_blob_BlobContent_CodeNav_ScrollMarks_tsx").then(n.bind(n,65710)));function a3({codeNavInfo:e,selectedText:t,lineNumber:n,offset:r,onClose:i,onBackToSymbol:o,onSymbolSelect:a,isLoading:s,setSearchResults:l,setFocusedSearchResult:c}){let{findNextShortcut:d,findPrevShortcut:u}=(0,rY.bx)(),h=!!(0,rG.Q)(),{definitions:m,localReferences:f,crossReferences:p,error:x}=aV(e,t,n,r),[y,g]=(0,n0.useState)(-1),[b,j]=(0,n0.useState)(!1),w=(0,nX.H)(),v=m?.definitions||[],N=(0,n0.useMemo)(()=>f?.references||[],[f]),C=p?.references||[],k=v.length,S=N.length+C.length,I=(0,rX.s)(),R=v.length>0?v[0]:void 0,Z=C.map(e=>e.path).filter((e,t,n)=>n.indexOf(e)===t),E=b?S:N.length,T=(0,n0.useRef)(null),{language:L,languageID:B}=(0,rq.G)(),_={type:"symbol",kind:"codeNavSymbol",name:t,languageID:B,languageName:L,codeNavDefinitions:v.map(e=>({ident:e.ident,extent:e.extent,kind:e.kind.fullName,fullyQualifiedName:e.fullyQualifiedName,ref:a6(e.refInfo),commitOID:e.refInfo.currentOid,repoID:e.repo.id,repoName:e.repo.name,repoOwner:e.repo.ownerLogin,path:e.path})),codeNavReferences:[...N.map(e=>({ident:e.ident,path:e.path,ref:a6(e.refInfo),commitOID:e.refInfo.currentOid,repoID:e.repo.id,repoName:e.repo.name,repoOwner:e.repo.ownerLogin})),...C.map(e=>({ident:e.ident,path:e.path,ref:a6(e.refInfo),commitOID:e.refInfo.currentOid,repoID:e.repo.id,repoName:e.repo.name,repoOwner:e.repo.ownerLogin}))]};(0,n0.useEffect)(()=>{R&&R.repo.name===e.repo.name&&R.path===e.path?l([R,...N]):l(N),c(void 0)},[e.path,e.repo.name,R,N,t,l,c]),(0,rp.Sl)(e=>{e||T.current?.focus()}),(0,n0.useEffect)(()=>{g(-1)},[t]),(0,n0.useEffect)(()=>{j(Z.length>0&&Z.length<=5)},[Z.length]);let D=({index:e,direction:t,navigate:n})=>{if(void 0!==e){let t=e>=k?N[e-k]:v[e];g(e),n&&I(t.href(h)),(0,r2.v)({line:t.lineNumber,column:t.ident.start.column})}if(void 0!==t){let e=Math.max(k,y+t),r=N[e-k];e<N.length+v.length&&r&&(g(e),n&&I(r.href(h)),(0,r2.v)({line:r.lineNumber,column:r.ident.start.column}))}};return(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsxs)(n7.Z,{as:"h2",sx:{fontSize:"12px",py:2,px:3,display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center"},role:"group","aria-roledescription":"Symbol Navigation Details",children:[(0,nY.jsx)(ae.z,{onClick:o,onSelect:o,id:"back-to-all-symbols","aria-label":"Back to All Symbols",ref:T,variant:"invisible",sx:{order:1,pr:3,pl:0,px:0,":hover:not([disabled])":{bg:"canvas.default"}},children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",justifyContent:"center",alignItems:"center"},children:[(0,nY.jsx)(rd.Z,{icon:rs.ArrowLeftIcon,sx:{pr:1,fontWeight:600,color:"fg.muted"},size:20}),(0,nY.jsx)(n7.Z,{sx:{fontSize:0,color:"fg.subtle",fontWeight:400},children:"All Symbols"})]})}),(0,nY.jsx)(rl.h,{"aria-label":"Close symbols","data-hotkey":"Escape",icon:rs.XIcon,sx:{order:3,mr:-2,color:"fg.default"},onClick:i,variant:"invisible"})]}),(0,nY.jsxs)(n7.Z,{sx:{alignItems:"center",display:"flex",justifyContent:"space-between",pb:3},children:[(0,nY.jsx)(a8,{currentSymbol:R,selectedText:t,codeNavInfo:e,onSymbolSelect:a}),1===v.length&&(0,nY.jsx)(aU.Z,{messageReference:_})]}),s&&(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",p:3,justifyContent:"center"},children:(0,nY.jsx)(ix.Z,{size:"small"})}),!x&&!s&&v&&v.length>0?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(a4,{headerText:v.length>1?"Definitions":"Definition",searchType:m?.backend==="precise"?"Precise":"Search"}),(0,nY.jsx)(n7.Z,{children:v&&(0,nY.jsx)(a1,{definitions:v,onClick:e=>{D({index:e})},highlightedIndex:y,initiallyExpanded:!0,enableExpandCollapse:v.length>1,symbol:t,setFocusOnFile:!0},"definitions")})]}):null,!s&&(N.length>0||C.length>0)&&(0,nY.jsx)(a4,{headerText:`${E} ${E>1?"References":"Reference"}`,searchType:"Search",sx:{justifyContent:"space-between"},children:(0,nY.jsxs)(n7.Z,{sx:{display:"float",float:"right",mr:"-6px"},children:[(0,nY.jsx)(rl.h,{"aria-label":"Previous reference","data-hotkey":u.hotkey,onClick:()=>D({direction:-1,navigate:!0}),sx:{mr:2,cursor:"pointer",color:"fg.muted"},disabled:y<=v.length,icon:rs.ChevronUpIcon,variant:"invisible",size:"small"}),(0,nY.jsx)(rl.h,{"aria-label":"Next reference","data-hotkey":d.hotkey,onClick:()=>D({direction:1,navigate:!0}),sx:{cursor:"pointer",color:"fg.muted"},disabled:y>=N.length+v.length-1,icon:rs.ChevronDownIcon,variant:"invisible",size:"small"}),(0,nY.jsx)("button",{hidden:!0,"data-hotkey":d.hotkey,onClick:()=>D({direction:1,navigate:!0}),"data-testid":"find-next-button"}),(0,nY.jsx)("button",{hidden:!0,"data-hotkey":u.hotkey,onClick:()=>D({direction:-1,navigate:!0}),"data-testid":"find-prev-button"})]})}),x&&(0,nY.jsx)(n7.Z,{sx:{p:3,fontWeight:"400",color:"fg.muted"},children:"No references found"}),!s&&N.length>0&&(0,nY.jsx)(a1,{initiallyExpanded:!0,enableExpandCollapse:!0,references:N,highlightedIndex:y-k,onClick:e=>{D({index:k+e})},symbol:t,setFocusOnFile:!(v&&v.length>0)},"referencesInfoBox"),!s&&b&&(0,nY.jsx)(a1,{initiallyExpanded:!1,enableExpandCollapse:!0,references:C,symbol:t},"crossReferencesInfoBox"),0===N.length&&0===v.length&&!x&&!s&&(0,nY.jsx)(n7.Z,{sx:{p:3,fontWeight:"400",color:"fg.muted"},children:"No definitions or references found"}),(0,nY.jsxs)(n7.Z,{sx:{px:2,py:2,fontSize:0,color:"fg.muted",borderTop:"1px solid",borderColor:"border.muted"},children:[Z.length>5&&(0,nY.jsx)(ae.z,{leadingVisual:b?rs.FoldIcon:rs.UnfoldIcon,sx:{color:"fg.default",mb:2},variant:"invisible",size:"small",onClick:()=>j(!b),children:b?"Show less":"Show more"}),(0,nY.jsx)(ae.z,{as:rR.Z,leadingVisual:rs.SearchIcon,sx:{color:"fg.default"},variant:"invisible",size:"small",href:(0,nJ.mY)({owner:w.ownerLogin,repo:w.name,searchTerm:t}),children:"Search for this symbol"})]}),(0,nY.jsx)(n0.Suspense,{fallback:null,children:(0,nY.jsx)(a2,{definitionsOrReferences:[...v,...N]})})]})}function a4({headerText:e,searchType:t,sx:n,children:r}){return(0,nY.jsxs)(n7.Z,{sx:{fontSize:"14px",px:3,py:2,fontWeight:"600",backgroundColor:"canvas.subtle",borderTop:"1px solid",borderColor:"border.muted",height:"36px",display:"flex",flexDirection:"row",alignItems:"center",...n},children:[(0,nY.jsxs)(re.Z,{as:"h3",sx:{fontSize:"12px",fontWeight:"semibold",color:"fg.muted"},children:[e,(0,nY.jsx)(ru.Z,{sx:{ml:2,fontWeight:"light"},children:t})]}),r]})}function a8({currentSymbol:e,selectedText:t,codeNavInfo:n,onSymbolSelect:r}){let i=e?.fullyQualifiedName??t,o=i.split(/(\W+)/),a=o.map(e=>{let t=/^\W+$/.test(e),r=t?[]:n.getLocalDefinitions(e,!0),i=1===r.length?r[0]:void 0,o=i?.kind.plColor;return{text:e,symbol:i,symbolColor:o,isSeparator:t}});return(0,nY.jsxs)(n7.Z,{as:"h3",sx:{display:"flex",flexDirection:"row",alignContent:"center",fontWeight:400,fontSize:1,fontFamily:"mono",flexWrap:"wrap",minWidth:0,verticalAlign:"center",gap:2,px:3},"aria-label":`${e?.kind.fullName||""} ${i}`.trimStart(),children:[e&&(0,nY.jsx)(rb,{symbolKind:e.kind,showFullSymbol:!0}),(0,nY.jsxs)(ry.Z,{title:i,sx:{maxWidth:290,mt:"3px",direction:"rtl"},inline:!0,children:["\u200E",a.map((e,t)=>{let n=e.symbol?{all:"unset",cursor:"pointer","&:hover":{backgroundColor:"attention.muted"}}:{};return(0,nY.jsx)(n7.Z,{as:"span",role:"button",tabIndex:e.isSeparator?-1:0,sx:{...n,color:e.symbolColor,direction:"ltr"},onClick:()=>e.symbol?r(e.symbol):void 0,onKeyDown:t=>{e.symbol&&["Enter","Space"].includes(t.code)&&r(e.symbol)},children:e.text},`${e.text}-${t}`)}),"\u200E"]})]})}function a6(e){return e.name===e.currentOid?e.currentOid:"tree"===e.refType?`refs/heads/${e.name}`:(0,aG.Ju)(e.name,e.refType??"branch")}try{(eM=a2).displayName||(eM.displayName="ScrollMarks")}catch{}try{(eH=a3).displayName||(eH.displayName="CodeNavSymbolDetails")}catch{}try{(e$=a4).displayName||(e$.displayName="CodeNavSymbolSectionHeader")}catch{}try{(eW=a8).displayName||(eW.displayName="CodeNavSymbolDefinitionHeader")}catch{}function a5({selectedText:e,showCodeNavWithSymbol:t,lineNumber:n,offset:r,onClose:i,onClear:o,codeNavInfo:a,isLoading:s,setSearchResults:l,setFocusedSearchResult:c,autoFocusSearch:d}){function u(e){t(e),m(!1)}let[h,m]=(0,n0.useState)(!e);return(0,rp.Sl)(e=>{e&&(o(),m(!0),l([]))}),(0,n0.useEffect)(()=>{e?m(!1):h||m(!0)},[e,a]),(0,nY.jsx)(n7.Z,{id:"symbols-pane",children:h?a?(0,nY.jsx)(r_,{treeSymbols:a.symbolTree,onSymbolSelect:u,codeSymbols:a.symbols,onClose:i,autoFocusSearch:d}):(0,nY.jsx)(n7.Z,{children:"Click on a symbol to see code navigation data"}):(0,nY.jsx)(a3,{codeNavInfo:a,selectedText:e,lineNumber:n,offset:r,onBackToSymbol:()=>{o(),m(!0),l([])},onClose:()=>{i(),o(),l([])},onSymbolSelect:u,isLoading:s,setSearchResults:l,setFocusedSearchResult:c})})}try{(ez=a5).displayName||(ez.displayName="CodeNavInfoPanel")}catch{}var a9=n(14783);function a7({onClose:e,toc:t}){let[n,r]=n0.useState(""),i=n0.useRef(null);return(n0.useEffect(()=>{i.current?.focus()},[]),t)?(0,nY.jsxs)(n7.Z,{sx:{px:2,pt:2,maxWidth:"100vw"},as:"section","aria-labelledby":"outline-id",children:[e?(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},children:[(0,nY.jsx)(n7.Z,{as:"h3",id:"outline-id",ref:i,sx:{display:"flex",alignItems:"center",justifyContent:"center",fontSize:1,fontWeight:600,px:2},tabIndex:-1,children:"Outline"}),(0,nY.jsx)(rl.h,{"aria-label":"Close outline",icon:rs.XIcon,onClick:e,variant:"invisible",sx:{color:"fg.muted"}})]}):null,t.length>=8?(0,nY.jsx)(n7.Z,{sx:{pt:3,px:2},children:(0,nY.jsx)(rc.Z,{leadingVisual:rs.FilterIcon,placeholder:"Filter headings","aria-label":"Filter headings",sx:{width:"100%"},onChange:e=>{r(e.target.value)}})}):null,(0,nY.jsx)(a9.$,{sx:{overflowY:"auto","> li":{borderRadius:2,width:"100%"}},children:t.map(({level:e,htmlText:t,anchor:r},i)=>{let o;if(!t||n&&!t.toLowerCase().includes(n.toLowerCase()))return null;o=1===e?{fontWeight:"bold"}:{paddingLeft:`${(e-1)*16}px`};let a=`#${r}`;return(0,nY.jsx)(se,{to:a,onClick:e=>{1===e.button||e.metaKey||e.ctrlKey||(location.href=a,iC(a),e.preventDefault())},children:(0,nY.jsx)(iu.wB,{sx:{...o},html:t})},`outline-${r}-${i}`)})})]}):null}function se({to:e,onClick:t,children:n}){let r=(0,ih.WU)(e),[i,o]=n0.useState(!1),a=(0,ih.TH)();return(0,n0.useEffect)(()=>{a.hash===r.hash?o(!0):o(!1)},[r.hash,a]),(0,nY.jsx)(a9.$.Item,{href:e,"aria-current":i?"page":void 0,onClick:t,children:n})}try{(eU=a7).displayName||(eU.displayName="TableOfContentsPanel")}catch{}try{(eG=se).displayName||(eG.displayName="NavItem")}catch{}var st=n(4220);let sn=n0.memo(sr);function sr(e){let{...t}=e,[n,r]=n0.useState(!1),{screenSize:i}=(0,n4.eI)();return n0.useEffect(()=>{r(i<n4._G.large)},[i]),(0,nY.jsxs)(nY.Fragment,{children:[!n&&(0,nY.jsx)(si,{className:"inner-panel-content-not-narrow",...e}),n&&(0,nY.jsx)(oy.V,{onClose:()=>t.setOpenPanel(void 0),renderHeader:()=>null,renderBody:()=>si({...t})})]})}function si({stickySx:e,stickyHeaderRef:t,openPanel:n,isCodeNavLoading:r,codeNavInfo:i,setOpenPanel:o,showCodeNavWithSymbol:a,searchingText:s,setSearchingText:l,setSearchTerm:c,setSearchResults:d,setFocusedSearchResult:u,autoFocusSearch:h,className:m}){let{headerInfo:{toc:f}}=(0,rq.G)(),p=(0,ie.X)(t),x=ir();return(0,nY.jsx)(st.s,{sx:{...e,...p?{borderRadius:"0px 0px 6px 6px",borderTop:0}:{}},className:`panel-content-narrow-styles ${m||""}`,children:"toc"===n?(0,nY.jsx)(a7,{toc:f,onClose:()=>{o(void 0)}}):"codeNav"===n&&(0,nY.jsx)(a5,{codeNavInfo:i,showCodeNavWithSymbol:a,selectedText:s.selectedText,lineNumber:s.lineNumber-1,offset:s.offset,onClose:()=>{o(void 0),localStorage.setItem("codeNavOpen",""),x(null,!1),document.getElementById("symbols-button")?.focus()},isLoading:r,onClear:()=>l({selectedText:"",lineNumber:0,offset:-1}),setSearchTerm:c,setSearchResults:d,setFocusedSearchResult:u,autoFocusSearch:h})})}try{(eq=sn).displayName||(eq.displayName="PanelContent")}catch{}try{(eV=sr).displayName||(eV.displayName="PanelContentUnmemoized")}catch{}try{(eK=si).displayName||(eK.displayName="InnerPanelContent")}catch{}function so({blame:e,blob:t,searchTerm:n,setSearchTerm:r,setValidCodeNav:i,showTree:o,treeToggleElement:a,validCodeNav:s,copilotInfo:l}){let{path:c}=(0,rK.Br)(),d=(0,n0.useRef)(null),u=(0,ie.V)(),[h,m]=(0,n0.useState)(!1),{openPanel:f,setOpenPanel:p}=rU(),x=(0,n0.useRef)(null),y=(0,n6.O)().openSymbolsOption.enabled;(0,rp.Sl)(e=>{e&&m(!0)});let{sendRepoClickEvent:g}=(0,rx.a)(),b=(0,n0.useRef)(!0);function j(){window.scrollY<300&&C(null,!0)}(0,n0.useEffect)(()=>{b.current?(b.current=!1,E.selectedText&&!f&&y&&k()):(C(null,!0),r(""),m(!1),T({selectedText:"",lineNumber:-1,offset:0}))},[c]),(0,n0.useEffect)(()=>(window.addEventListener("scroll",j),()=>{window.removeEventListener("scroll",j)}),[]);let w=!!(0,rG.Q)(),v=ir(),{currentStickyLines:N,setStickyLines:C}=r7(),k=(0,n0.useCallback)(()=>{!w&&y&&(p("codeNav"),g("BLOB_SYMBOLS_MENU.OPEN_WITH_SYMBOL"),localStorage.setItem("codeNavOpen","codeNav"),v(null,!0))},[w,y,p,g,v]),{isCodeNavLoading:S,codeNavInfo:I,showCodeNavWithSymbol:R,showCodeNavForToken:Z,searchingText:E,setSearchingText:T}=r3(t,k,i,n8.ssrSafeLocation.hash,w),{searchStatus:L,searchResults:B,setSearchResults:_,focusedSearchResult:D,setFocusedSearchResult:F}=r6(I,n),{headerInfo:{toc:O}}=t,P=(0,n0.useMemo)(()=>({value:!1}),[I]);P.value=!f||P.value;let A=void 0!==f&&I&&s&&!(0===I.symbols.length&&"codeNav"===f&&!P.value)&&!(!O&&"toc"===f),M=A?f:void 0;return(0,n0.useEffect)(()=>{f&&!A&&p(void 0)},[f,p,A]),(0,n0.useEffect)(()=>{try{(0,rP.qP)("blob-size",{lines:t.stylingDirectives?.length,truncatedSloc:t.headerInfo?.lineInfo.truncatedSloc,truncatedLoc:t.headerInfo?.lineInfo.truncatedLoc,length:t.rawLines?.reduce((e,t)=>e+t.length,0)??0,humanLength:t.headerInfo?.blobSize})}catch(e){}},[t]),(0,nY.jsx)(rq.d,{blob:t,children:(0,nY.jsx)(rG.M,{blame:e,children:(0,nY.jsxs)(rV.S6,{children:[I&&(0,nY.jsx)(sa,{codeNavInfo:I}),(0,nY.jsx)(aO,{}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row"},children:[(0,nY.jsxs)(n7.Z,{className:"container",sx:{width:"100%",height:"fit-content",minWidth:0,mr:M&&I?3:0},children:[(0,nY.jsxs)(n7.Z,{sx:{pl:1,pb:3},className:"react-code-size-details-banner",children:[(0,nY.jsx)(ah,{className:"react-code-size-details-banner"}),(0,nY.jsx)(oz.y,{copilotInfo:l,className:"react-code-size-details-banner",view:e?"blame":"preview"})]}),(0,nY.jsx)(n7.Z,{className:"react-blob-view-header-sticky",sx:u,id:r9.TZ,ref:d,children:(0,nY.jsx)(aR,{currentStickyLines:N,focusedSearchResult:D,openPanel:M,searchingText:E,searchResults:B,searchTerm:n,setFocusedSearchResult:F,setOpenPanel:p,setSearchTerm:r,showTree:o,stickyHeaderRef:d,treeToggleElement:a,validCodeNav:s,copilotInfo:l})}),(0,nY.jsx)(n7.Z,{sx:{border:"1px solid",borderTop:"none",borderColor:"border.default",borderRadius:"0px 0px 6px 6px",minWidth:"273px"},children:(0,nY.jsx)(o$,{blobLinesHandle:x,setOpenPanel:p,validCodeNav:s,codeNavInfo:I,onCodeNavTokenSelected:Z,onLineStickOrUnstick:C,searchResults:B,setSearchTerm:r,focusedSearchResult:D})})]}),M&&I?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(n7.Z,{sx:{pb:"33px"}}),(0,nY.jsx)(sn,{stickySx:u,stickyHeaderRef:d,openPanel:M,isCodeNavLoading:S,codeNavInfo:I,setOpenPanel:p,showCodeNavWithSymbol:R,searchingText:E,setSearchingText:T,searchTerm:n,searchResults:B,searchStatus:L,setSearchResults:_,setSearchTerm:r,setFocusedSearchResult:F,autoFocusSearch:h})]}):null]})]})})})}function sa({codeNavInfo:e}){let{sendRepoKeyDownEvent:t}=(0,rx.a)(),{findSymbolShortcut:n}=(0,rY.bx)(),{setOpenPanel:r}=rU();return 0===e.symbols.length?null:(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:n.hotkey,onButtonClick:()=>{r("codeNav"),(0,rp.eS)(),t("FIND_SYMBOL")}})}try{(eY=so).displayName||(eY.displayName="BlobViewContent")}catch{}try{(eQ=sa).displayName||(eQ.displayName="FindSymbolShortcut")}catch{}var ss=n(51461),sl=n(64071);let sc=n0.createContext({});function sd(){return n0.useContext(sc)}function su({payload:e,children:t}){return(0,nY.jsx)(sc.Provider,{value:e,children:t})}try{(eX=sc).displayName||(eX.displayName="TreeContext")}catch{}try{(eJ=su).displayName||(eJ.displayName="CurrentTreeProvider")}catch{}function sh(){let e=(0,n3.T)(),t="blame"in e?e.blame:void 0;return(0,nY.jsxs)(rG.M,{blame:t,children:[(0,nY.jsx)(sm,{}),(0,nY.jsx)(sp,{})]})}function sm(){let e=(0,nX.H)(),t=(0,rK.Br)().refInfo.name,n=(0,rG.Q)(),[r,i]=n0.useState(!0);if(!n)return null;let o=n?.ignoreRevs,a=n?.errorType;if(!a)return null;let s=()=>(0,nY.jsx)(iM.r,{to:(0,nJ.C9)({repo:e.name,owner:e.ownerLogin,commitish:t,filePath:o.path}),children:o.path});return(0,nY.jsx)(nY.Fragment,{children:r&&(0,nY.jsxs)(ss.Z,{variant:"warning",sx:{mt:3},children:[(0,nY.jsx)(rd.Z,{icon:rs.InfoIcon}),(0,nY.jsx)(sf,{blameErrorType:a,renderIgnoreRefsLink:s}),(0,nY.jsx)(n7.Z,{sx:{float:"right",cursor:"pointer"},onClick:()=>i(!1),children:(0,nY.jsx)(rd.Z,{icon:rs.XIcon})})]})})}function sf({blameErrorType:e,renderIgnoreRefsLink:t}){switch(e){case"invalid_ignore_revs":return(0,nY.jsxs)(ru.Z,{children:["Your ",t()," file is invalid."]});case"ignore_revs_too_big":return(0,nY.jsxs)(ru.Z,{children:["Your ",t()," file is too large."]});case"symlink_disallowed":return(0,nY.jsx)(ru.Z,{children:"Symlinks are not supported."});case"blame_timeout":return(0,nY.jsx)(ru.Z,{children:"Your blame took too long to compute."});default:ic(e)}}function sp(){let e=(0,nX.H)(),t=(0,rK.Br)().refInfo.name,n=rG.Q()?.ignoreRevs,[r,i]=n0.useState(!0);if(!n?.present)return null;let o=(0,nY.jsx)(iM.r,{to:(0,nJ.C9)({repo:e.name,owner:e.ownerLogin,commitish:t,filePath:n.path}),children:n.path});return(0,nY.jsx)(nY.Fragment,{children:r&&(0,nY.jsxs)(ss.Z,{sx:{mt:3},children:[(0,nY.jsx)(rd.Z,{icon:rs.InfoIcon}),n.timedOut?(0,nY.jsxs)(ru.Z,{children:["Failed to ignore revisions in ",o,"."]}):(0,nY.jsxs)(ru.Z,{children:["Ignoring revisions in ",o,"."]}),(0,nY.jsx)(n7.Z,{sx:{float:"right",cursor:"pointer"},onClick:()=>i(!1),children:(0,nY.jsx)(rd.Z,{icon:rs.XIcon})})]})})}try{(e0=sh).displayName||(e0.displayName="BlameBanners")}catch{}try{(e1=sm).displayName||(e1.displayName="BlameErrorBanner")}catch{}try{(e2=sf).displayName||(e2.displayName="BlameErrorText")}catch{}try{(e3=sp).displayName||(e3.displayName="IgnoreRevsBanner")}catch{}function sx({showPublishActionBanner:e,showPublishStackBanner:t,releasePath:n,dismissActionNoticePath:r,dismissStackNoticePath:i,sx:o}){let[a,s]=(0,n0.useState)(!1),l=()=>{(0,it.Q)(r,{method:"POST"}),s(!0)},c=()=>{(0,it.Q)(i,{method:"POST"}),s(!0)};return e||t?(0,nY.jsxs)(ss.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",mt:3,...o},hidden:a,children:[e?(0,nY.jsx)(n7.Z,{sx:{flexGrow:1},children:"You can publish this Action to the GitHub Marketplace"}):(0,nY.jsxs)(n7.Z,{sx:{flexGrow:1},children:[(0,nY.jsxs)(n7.Z,{as:"h5",children:[(0,nY.jsx)(rd.Z,{icon:rs.StackIcon}),"Publish this stack as a release"]}),(0,nY.jsx)(n7.Z,{sx:{fontSize:0},children:"Make your stack discoverable in releases and the GitHub Marketplace. People will use it to create new repositories."})]}),(0,nY.jsx)(oq.Q,{href:n,sx:{fontSize:0,mr:2,...oQ.A},children:"Draft a release"}),(0,nY.jsx)(rl.h,{icon:rs.XIcon,"aria-label":"Dismiss",onClick:e?l:c,sx:{backgroundColor:"transparent",border:0,pr:0},title:"Dismiss"})]}):null}try{(e4=sx).displayName||(e4.displayName="PublishBanners")}catch{}var sy=n(50299);function sg({errors:e}){if(!e||0===e.length)return null;let t=[];return 1===e.length?t.push("Learn more about this error."):e.map((e,n)=>{t.push(`Learn more about error ${n+1}.`)}),(0,nY.jsxs)(ss.Z,{variant:"danger",sx:{mt:3},children:[(0,nY.jsxs)("p",{children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertIcon}),(0,nY.jsxs)("strong",{children:["There ",1===e.length?"is a problem":"are some problems"," with this template"]})]}),e.map((e,n)=>(0,nY.jsxs)("p",{children:[(0,nY.jsx)(iu.WZ,{html:e.message}),". ",(0,nY.jsx)(rR.Z,{href:e.link,target:"_blank",children:t[n]})]},`error-${n}`))]})}try{(e8=sg).displayName||(e8.displayName="DiscussionTemplateBanner")}catch{}function sb(){let e;let t=(0,rq.G)(),n=t.issueTemplate;if(!t.loggedIn||!n&&!t.isValidLegacyIssueTemplate&&!t.showIssueFormWarning)return null;let r=null,i=[];return n?!1===n.valid?(e="danger",n.errors&&(1===n.errors.length?i.push("Learn more about this error."):n.errors.map((e,t)=>{i.push(`Learn more about error ${t+1}.`)})),r=(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)("p",{children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertIcon}),(0,nY.jsxs)("strong",{children:["There ",n.errors?.length===1?"is a problem":"are some problems"," with this template"]})]}),n.errors?.map((e,t)=>(0,nY.jsxs)("p",{children:[(0,nY.jsx)(iu.WZ,{html:e.message}),". ",(0,nY.jsx)(rR.Z,{href:e.link,target:"_blank",children:i[t]})]},`error-${t}`))]})):r=n.structured?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(oJ.Z,{sx:{mr:2},variant:"success",children:"Beta"}),"This file is used as an Issue Form template."," ",(0,nY.jsx)("a",{href:"https://github.com/orgs/community/discussions/categories/projects-and-issues",children:"Give Feedback."})]}):"This file is used as a markdown issue template.":t.showIssueFormWarning&&(r="Issue form templates are not supported on private repositories.",e="warning"),(0,nY.jsxs)(nY.Fragment,{children:[(n||t.showIssueFormWarning)&&(0,nY.jsx)(ss.Z,{variant:e,sx:{mt:3},children:r}),t.isValidLegacyIssueTemplate&&(0,nY.jsx)(sj,{helpUrl:t.issueTemplateHelpUrl})]})}function sj({helpUrl:e}){return(0,nY.jsxs)(ss.Z,{variant:"warning",sx:{mt:3},children:["You are using an old version of issue templates. Please update to the new issue template workflow."," ",(0,nY.jsx)(rR.Z,{href:e,target:"_blank",children:"Learn more about issue templates."})]})}try{(e6=sb).displayName||(e6.displayName="IssueTemplateBanner")}catch{}try{(e5=sj).displayName||(e5.displayName="LegacyIssueTemplateBanner")}catch{}function sw(){let e=(0,nX.H)(),{refInfo:t,path:n}=(0,rK.Br)(),{csvError:r,isCodeownersFile:i,publishBannersInfo:{showPublishActionBanner:o,showPublishStackBanner:a,releasePath:s,dismissActionNoticePath:l,dismissStackNoticePath:c},discussionTemplate:d}=(0,rq.G)(),[u,h]=(0,n0.useState)([]),[m,f]=(0,n0.useState)(sy.hL.LOADING),p=(0,n0.useRef)(0);return(0,n0.useEffect)(()=>{p.current++;let r=async()=>{try{let r=p.current,i=await (0,sy.Xj)(e,t,n);if(r<p.current)return;if(i.ok){let e=await i.json();h(e.map(e=>(0,sy.ew)(e))),f(sy.hL.VALIDATED)}else f(sy.hL.ERROR)}catch(e){f(sy.hL.ERROR)}};i&&r()},[i,e,t,n]),(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(sx,{showPublishActionBanner:o,showPublishStackBanner:a,releasePath:s,dismissActionNoticePath:l,dismissStackNoticePath:c}),(0,nY.jsx)(sb,{}),d?.errors&&d.errors.length>0&&(0,nY.jsx)(sg,{...d}),(0,nY.jsx)(sv,{}),i&&(0,nY.jsx)(iI.Provider,{value:u,children:(0,nY.jsx)(sy.iC,{errors:u,state:m})}),r&&(0,nY.jsx)(sN,{csvError:r})]})}function sv(){let{truncated:e,large:t,image:n,renderedFileInfo:r,rawBlobUrl:i}=(0,rq.G)();return!e||t||n||r?null:(0,nY.jsxs)(ss.Z,{sx:{mt:3},children:["This file has been truncated, but you can ",(0,nY.jsx)(rR.Z,{href:i,children:"view the full file"}),"."]})}function sN({csvError:e}){return(0,nY.jsx)(ss.Z,{sx:{mt:3},variant:"warning",children:(0,nY.jsx)(iu.wB,{html:e})})}try{(e9=sw).displayName||(e9.displayName="BlobLowerBanners")}catch{}try{(e7=sv).displayName||(e7.displayName="TruncatedBanner")}catch{}try{(te=sN).displayName||(te.displayName="CSVErrorBanner")}catch{}function sC(){let e=(0,nX.H)(),{refInfo:t,path:n}=(0,rK.Br)(),[r]=(0,a_.f)(e.ownerLogin,e.name,t.name,n);return r?.isSpoofed?(0,nY.jsxs)(ss.Z,{variant:"warning",sx:{mt:3},children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertIcon}),(0,nY.jsx)(ru.Z,{children:"This commit does not belong to any branch on this repository, and may belong to a fork outside of the repository."})]}):null}try{(tt=sC).displayName||(tt.displayName="SpoofedCommitWarning")}catch{}function sk(){let{topBannersInfo:{repoName:e,repoOwner:t}}=(0,rq.G)(),{license:n}=(0,au.nO)(),r={permissions:{icon:rs.CheckIcon,color:"success.fg"},limitations:{icon:rs.XIcon,color:"danger.fg"},conditions:{icon:rs.InfoIcon,color:"accent.fg"}};return n?(0,nY.jsxs)(n7.Z,{sx:{borderColor:"border.default",borderStyle:"solid",borderWidth:1,borderRadius:"6px",mt:3},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"grid",px:3,py:1,gridTemplateColumns:"repeat(auto-fit, minmax(350px, 1fr))",gap:2},className:"blob-license-banner-outer",children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flex:"1",flexDirection:"column",pr:3},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,nY.jsx)(rd.Z,{icon:rs.LawIcon,size:32}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",ml:2},children:[(0,nY.jsxs)(n7.Z,{sx:{fontSize:0,color:"fg.muted"},children:[`${t}/${e} is licensed under`," ",n.name.toLowerCase().startsWith("the ")?"":" the"]}),(0,nY.jsx)(n7.Z,{as:"h3",children:n.name})]})]}),(0,nY.jsx)(iu.wB,{html:n.description,sx:{fontSize:0,color:"fg.muted",mt:2,flexWrap:"wrap"}})]}),(0,nY.jsx)(n7.Z,{sx:{display:"flex",flex:"1"},children:Object.keys(n.rules).map((e,t)=>(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",flex:"1",pb:3},children:[(0,nY.jsx)(n7.Z,{sx:{display:"flex",mb:2},as:"h5",children:e.charAt(0).toUpperCase()+e.substring(1)}),n.rules[e].map(t=>(0,nY.jsxs)(n7.Z,{sx:{fontSize:0},children:[(0,nY.jsx)(rd.Z,{icon:r[e].icon,size:13,sx:{color:r[e].color,mr:1}}),t.label]},t.tag))]},t))})]}),(0,nY.jsxs)(n7.Z,{sx:{borderTop:"1px solid",borderColor:"border.default",fontSize:0,px:3,py:2},children:["This is not legal advice.\xa0",(0,nY.jsx)(rR.Z,{href:n.helpUrl,children:"Learn more about repository licenses"})]})]}):null}try{(tn=sk).displayName||(tn.displayName="BlobLicenseBanner")}catch{}function sS({citationHelpUrl:e}){return(0,nY.jsxs)(ss.Z,{variant:"warning",sx:{mt:3},children:["Your ",(0,nY.jsx)("strong",{children:"CITATION.cff"})," file cannot be parsed. Make sure the formatting is correct."," ",(0,nY.jsx)(rR.Z,{href:e,children:"Learn more about CITATION files."})]})}try{(tr=sS).displayName||(tr.displayName="InvalidCitationWarning")}catch{}function sI({globalPreferredFundingPath:e}){return(0,nY.jsxs)(ss.Z,{sx:{mt:3},children:["This file is overriding the organization-wide ",(0,nY.jsx)("code",{children:"FUNDING.yml"})," file. Removing ",(0,nY.jsx)("code",{children:"FUNDING.yml"})," in this repository will use the organization default.",(0,nY.jsx)(rR.Z,{href:e??void 0,children:" View organization funding file."})]})}try{(ti=sI).displayName||(ti.displayName="OverridingGlobalFundingFileWarning")}catch{}function sR(){let{topBannersInfo:{overridingGlobalFundingFile:e,globalPreferredFundingPath:t,showInvalidCitationWarning:n,citationHelpUrl:r}}=(0,rq.G)(),{showLicenseMeta:i}=(0,au.nO)();return(0,nY.jsxs)(n7.Z,{children:[i&&(0,nY.jsx)(sk,{}),n&&(0,nY.jsx)(sS,{citationHelpUrl:r}),(0,nY.jsx)(sC,{}),e&&(0,nY.jsx)(sI,{globalPreferredFundingPath:t})]})}try{(to=sR).displayName||(to.displayName="BlobMidBanners")}catch{}var sZ=n(62719);function sE(){let{defaultBranch:e}=(0,nX.H)(),{refInfo:{name:t}}=(0,rK.Br)(),{dependabotInfo:{showConfigurationBanner:n}}=(0,rq.G)();return n?e===t?(0,nY.jsx)(sT,{}):(0,nY.jsx)(sL,{}):null}function sT(){let{dependabotInfo:{configFilePath:e,networkDependabotPath:t,dismissConfigurationNoticePath:n,configurationNoticeDismissed:r}}=(0,rq.G)(),i=(0,n0.useCallback)(()=>(0,it.Q)(n,{method:"POST"}),[n]);return r?null:(0,nY.jsx)(n7.Z,{sx:{position:"absolute",zIndex:2,left:"50%",transform:"translate(-50%, 0)"},children:(0,nY.jsxs)(sZ.Z,{caret:"top",sx:{backgroundColor:"canvas.overlay",width:300,p:3},children:[(0,nY.jsx)(ru.Z,{as:"h5",sx:{mb:1},children:"Dependabot"}),(0,nY.jsx)(ru.Z,{as:"p",sx:{mb:3},children:"Dependabot creates pull requests to keep your dependencies secure and up-to-date."}),(0,nY.jsxs)(ru.Z,{as:"p",sx:{mb:3},children:["You can opt out at any time by removing the ",(0,nY.jsx)("code",{children:e})," config file."]}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex"},children:[(0,nY.jsx)(ae.z,{as:"a",href:t,children:"View update status"}),(0,nY.jsx)(ae.z,{variant:"invisible",sx:{color:"fg.muted",ml:2},onClick:i,children:"Dismiss"})]})]})})}function sL(){let{defaultBranch:e,name:t,ownerLogin:n}=(0,nX.H)(),{path:r}=(0,rK.Br)(),i=(0,nJ.C9)({owner:n,repo:t,commitish:e,filePath:r});return(0,nY.jsxs)(ss.Z,{variant:"warning",children:[(0,nY.jsxs)(ru.Z,{as:"h5",children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertIcon}),"Cannot configure Dependabot"]}),(0,nY.jsxs)(ru.Z,{as:"p",children:["To configure Dependabot, you must use"," ",(0,nY.jsx)(rR.Z,{href:i,children:"this repository's default branch"})]})]})}try{(ta=sE).displayName||(ta.displayName="DependabotConfigurationBanner")}catch{}try{(ts=sT).displayName||(ts.displayName="DefaultBranchDependabotConfigurationBanner")}catch{}try{(tl=sL).displayName||(tl.displayName="DirectionsForNonDefaultBranch")}catch{}var sB=n(98641);function s_({children:e,mediaUrl:t,mediaPreviewSrc:n,iconSvg:r,taskTitle:i,taskPath:o,org:a}){return(0,nY.jsxs)(n7.Z,{as:"section",sx:{position:"relative",display:"flex",borderColor:"border.muted",borderStyle:"solid",borderWidth:1,borderRadius:"6px",backgroundColor:"canvas.subtle",p:3,mt:3},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex"},children:[(0,nY.jsx)(sD,{iconSvg:r}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",ml:3},children:[(0,nY.jsxs)(sB.Z,{children:[(0,nY.jsx)(sB.Z.Item,{href:(0,nJ.lr)({owner:a}),children:"Tasks"}),(0,nY.jsx)(sB.Z.Item,{href:o,sx:{color:"fg.default"},children:i})]}),e]})]}),(0,nY.jsx)(sO,{mediaPreviewSrc:n,mediaUrl:t})]})}function sD({iconSvg:e}){return(0,nY.jsxs)(n7.Z,{sx:{position:"relative",maxHeight:48},children:[(0,nY.jsx)(iu.wB,{html:e,sx:{width:48,height:48,background:"radial-gradient(circle, rgba(141, 123, 255, 1) 0%, rgba(123, 133, 255, 1) 48%, rgba(141, 123, 255, 1) 85%, rgba(141, 123, 255, 1) 98%)",borderRadius:2,p:1,"& path":{fill:"#fff"}}}),(0,nY.jsx)(sF,{size:6,color:"#6c84e9",bottom:-7,left:-7}),(0,nY.jsx)(sF,{size:4,color:"#9e7bff",top:-4,right:4}),(0,nY.jsx)(sF,{size:6,color:"#6c84e9",top:-7,right:-8})]})}function sF({size:e,color:t,left:n,right:r,top:i,bottom:o}){return(0,nY.jsx)(n7.Z,{sx:{position:"absolute",width:e,height:e,borderRadius:"50%",left:`${n}px`,right:`${r}px`,top:`${i}px`,bottom:`${o}px`,backgroundColor:t}})}function sO({mediaUrl:e,mediaPreviewSrc:t}){return!((n8.iG?.innerWidth??0)<n4._G.xlarge)&&e&&t?(0,nY.jsx)(n7.Z,{sx:{display:"flex",alignItems:"center",background:"center / contain no-repeat url(/images/modules/dashboard/onboarding/glow-1.png)",minWidth:500},className:"org-onboarding-tip-media",children:(0,nY.jsx)(rR.Z,{href:e,sx:{display:"flex",justifyContent:"center"},children:(0,nY.jsx)("img",{src:t,alt:"Guidance",loading:"lazy",style:{width:"50%",height:"50%"}})})}):null}function sP({repo:e,owner:t}){return(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsx)(ru.Z,{as:"h3",sx:{mb:1},children:"Auto-assign new issue with GitHub Actions"}),(0,nY.jsxs)(n7.Z,{sx:{color:"fg.muted"},children:[(0,nY.jsxs)("p",{children:["The ",(0,nY.jsx)(ru.Z,{sx:{fontWeight:600,fontFamily:"monospace"},children:"auto-assign.yml"})," file below lives inside your"," ",(0,nY.jsx)(ru.Z,{sx:{fontWeight:600,fontFamily:"monospace"},children:"demo-repository"})," and defines when and how it\u2019s automatically triggered. This"," ",(0,nY.jsx)("a",{href:"https://github.com/marketplace/actions/auto-assign-issues-prs",target:"_blank",rel:"noreferrer",children:"\u201CAuto Assign\u201D workflow"})," ","happens to add reviewers and assignees to issues and pull requests when they\u2019re opened in this repository."]}),(0,nY.jsxs)("p",{children:["You can see the results of this workflow in any"," ",(0,nY.jsx)("a",{href:(0,nJ.FL)({owner:t,repo:e,action:"issues"}),children:"issue"})," or"," ",(0,nY.jsx)("a",{href:(0,nJ.FL)({owner:t,repo:e,action:"pulls"}),children:"pull request"})," that you create in this repository, as it\u2019ll assign them to the specified members. And you can see a log of any workflows you run in"," ",(0,nY.jsx)("a",{href:(0,nJ.FL)({owner:t,repo:e,action:"actions"}),children:"your repository\u2019s \u201CActions\u201D tab."})]}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center",mt:3},children:[(0,nY.jsx)(oq.Q,{variant:"primary",href:(0,nJ.FL)({owner:t,repo:e,action:"issues/new"}),sx:oQ.A,children:"Create new issue to see results"}),(0,nY.jsxs)(rR.Z,{sx:{ml:4},href:"https://docs.github.com/en/actions",target:"_blank",children:[(0,nY.jsx)(rd.Z,{icon:rs.FileIcon,sx:{mr:1}}),"Learn how automation works on GitHub"]})]})]})]})}try{(tc=s_).displayName||(tc.displayName="OrgOnboardingTip")}catch{}try{(td=sD).displayName||(td.displayName="SuggestIcon")}catch{}try{(tu=sF).displayName||(tu.displayName="Bubble")}catch{}try{(th=sO).displayName||(th.displayName="Media")}catch{}try{(tm=sP).displayName||(tm.displayName="ActionsOnboardingPrompt")}catch{}function sA({feature:e,featureName:t,repoIsFork:n,repoOwnedByCurrentUser:r,requestFullPath:i,showFreeOrgGatedFeatureMessage:o,showPlanSupportBanner:a,upgradeDataAttributes:s,upgradePath:l}){let c={};if(s)for(let e in s)c[`data-${e}`]=s[e];return a?(0,nY.jsx)(ss.Z,{variant:"warning",sx:{mt:3},children:r?n?`This repository is a fork, and inherits the features of the parent repository. Contact the owner of the root repository to enable ${t||"this feature"}`:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,nY.jsx)(ru.Z,{sx:{flexGrow:1},children:`Upgrade to GitHub Pro or make this repository public to enable ${t||"this feature"}.`}),(0,nY.jsx)(sM,{dataAttributes:c,individual:!0,requestFullPath:i,feature:e,upgradePath:l})]}):o?(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center"},children:[(0,nY.jsx)(ru.Z,{sx:{flexGrow:1},children:`Upgrade to GitHub Team or make this repository public to enable ${t||"this feature"}.`}),(0,nY.jsx)(sM,{dataAttributes:c,individual:!1,requestFullPath:i,feature:e,upgradePath:l})]}):`Contact the owner of the repository to enable ${t||"this feature"}.`}):null}function sM({dataAttributes:e,individual:t,requestFullPath:n,feature:r,upgradePath:i}){return(0,nY.jsx)(ae.z,{...e,"data-ga-click":`Change ${t?"individual":"organization"}, click to upgrade, ref_page:${n};ref_cta:Upgrade now;ref_loc:${r};location:${r};text:Upgrade now`,onClick:()=>{location.href=i},children:"Upgrade now"})}try{(tf=sA).displayName||(tf.displayName="PlanSupportBanner")}catch{}try{(tp=sM).displayName||(tp.displayName="UpgradeButton")}catch{}function sH(){let{planSupportInfo:e,topBannersInfo:t}=(0,rq.G)(),{actionsOnboardingTip:n}=t,r=(0,nX.H)();return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column"},children:[(0,nY.jsx)(sA,{...e,feature:"codeowners",featureName:"CODEOWNERS"}),(0,nY.jsx)(sE,{}),n&&(0,nY.jsx)(s_,{iconSvg:n.iconSvg,mediaPreviewSrc:n.mediaPreviewSrc,mediaUrl:n.mediaUrl,taskTitle:n.taskTitle,taskPath:n.taskPath,org:n.orgName,children:(0,nY.jsx)(sP,{owner:r.ownerLogin,repo:r.name})})]})}try{(tx=sH).displayName||(tx.displayName="BlobTopBanners")}catch{}function s$({interactionLimitBanner:e}){let t;let n=(0,nX.H)();return t=e.usersHaveAccess?"Users that have recently created their account will be unable to interact with the repository.":e.contributorsHaveAccess?(0,nY.jsxs)(nY.Fragment,{children:["Users that have not previously ",(0,nY.jsx)(rR.Z,{href:(0,nJ.BK)(n),children:"committed"})," to the"," ",n.defaultBranch," branch of this repository will be unable to interact with the repository."]}):(0,nY.jsxs)(nY.Fragment,{children:["Users that are not ",(0,nY.jsx)(rR.Z,{href:(0,nJ.IU)(n),children:"collaborators"})," will not be able to interact with the repository."]}),(0,nY.jsxs)(ss.Z,{children:[(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsx)(rd.Z,{icon:rs.ClockIcon,sx:{mr:1}}),(0,nY.jsx)(ru.Z,{children:e.limitTitle}),(0,nY.jsx)(oJ.Z,{variant:"success",sx:{ml:2},children:`${e.currentExpiry} remaining`})]}),(0,nY.jsx)(n7.Z,{sx:{mt:2,fontSize:0},children:(0,nY.jsx)(ru.Z,{children:t})}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",my:2,flexWrap:"wrap",fontSize:0},children:[(0,nY.jsx)(sW,{userRole:"New users",access:!1}),(0,nY.jsx)(sW,{userRole:"Users",access:e.usersHaveAccess}),(0,nY.jsx)(sW,{userRole:"Contributors",access:e.contributorsHaveAccess}),(0,nY.jsx)(sW,{userRole:"Collaborators",access:!0}),e.inOrganization&&(0,nY.jsx)(sW,{userRole:"Organization members",access:!0})]}),e.adminText&&e.adminLink&&e.disablePath&&(0,nY.jsxs)(n7.Z,{sx:{mt:2,display:"flex",alignItems:"center",flexWrap:"wrap",fontSize:0},children:[(0,nY.jsx)(ae.z,{onClick:async()=>{let t=new FormData;t.append("interaction_setting","NO_LIMIT"),t.append("_method","put"),await (0,it.Q)(e.disablePath,{body:t,method:"POST",redirect:"manual"}),window.location.reload()},children:"Disable"}),(0,nY.jsxs)(n7.Z,{sx:{whiteSpace:"pre"},children:["\xa0or view\xa0",(0,nY.jsx)(rR.Z,{href:e.adminLink,children:e.adminText})]})]})]})}function sW({userRole:e,access:t}){return(0,nY.jsxs)(n7.Z,{sx:{mr:3,whiteSpace:"pre"},children:[(0,nY.jsx)(rd.Z,{icon:t?rs.CheckIcon:rs.XIcon,sx:{path:{color:t?"success.fg":"danger.fg"}}}),e]})}try{(ty=s$).displayName||(ty.displayName="InterractionLimitsBanner")}catch{}try{(tg=sW).displayName||(tg.displayName="RoleInteractionIndicator")}catch{}function sz({inviterName:e}){let{ownerLogin:t,name:n}=(0,nX.H)();return(0,nY.jsx)(ss.Z,{children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"center",gap:2},children:[(0,nY.jsxs)(n7.Z,{sx:{flexGrow:1,alignItems:"center"},children:[(0,nY.jsxs)(rR.Z,{href:(0,nJ.lr)({owner:e}),"data-hovercard-url":(0,nJ.zP)({owner:e}),sx:{color:"fg.default",fontWeight:"bold"},children:["@",e]}),"\xa0has invited you to collaborate on this repository"]}),(0,nY.jsx)(nY.Fragment,{children:(0,nY.jsx)(oq.Q,{href:(0,nJ.wy)({owner:t,repo:n}),sx:oQ.A,children:"View invitation"})})]})})}try{(tb=sz).displayName||(tb.displayName="PendingInvitationBanner")}catch{}function sU({helpUrl:e}){let{refInfo:t}=(0,rK.Br)(),n=(0,nX.H)(),r=`${e}/repositories/configuring-branches-and-merges-in-your-repository/defining-the-mergeability-of-pull-requests/about-protected-branches`,i=(0,ro.x)(),o=()=>{if(!i)return;let e=(0,nJ.xv)({login:i.login}),t=new FormData;t.append("_method","delete"),t.append("repository_id",n.id.toString()),t.append("notice_name","sculk_protect_this_branch"),(0,it.Q)(e,{method:"POST",body:t}),s(!0)},[a,s]=n0.useState(!1),[l]=(0,iS.D)(()=>!1,!0,[]),c=sG("click to learn more about branch protection rules","ref_cta:Learn more about protected branches",n,l),d=sG("click to add a rule","ref_cta:Protect this branch",n,l),u=sG("click to dismiss","ref_cta:Dismiss",n,l);return(0,nY.jsx)(n7.Z,{sx:{borderColor:"border.default",borderStyle:"solid",borderWidth:1,borderRadius:"6px"},hidden:a,children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:["start","center","start","center"],px:3,py:3},children:[(0,nY.jsx)(rd.Z,{icon:rs.GitBranchIcon,size:"medium",sx:{mr:2,mt:1}}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:["column","row","column","row"],width:"100%"},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",mb:2,flexGrow:1,flexBasis:0},children:[(0,nY.jsxs)(ru.Z,{sx:{ml:2,fontWeight:600,fontSize:2,mb:1},children:["Your ",t.name," branch isn't protected"]}),(0,nY.jsxs)(ru.Z,{sx:{ml:2,color:"fg.muted"},children:["Protect this branch from force pushing or deletion, or require status checks before merging.\xa0",(0,nY.jsx)(rR.Z,{href:r,"data-analytics-event":c,children:"View documentation."})]})]}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",gap:2,pl:2},children:[(0,nY.jsx)(oq.Q,{href:(0,nJ.CY)({owner:n.ownerLogin,repo:n.name,branchName:t.name}),sx:oQ.A,"data-analytics-event":d,children:"Protect this branch"}),(0,nY.jsx)(ae.z,{onClick:o,"data-analytics-event":u,sx:{display:["inherit","none","inherit","none"]},children:"Dismiss"}),(0,nY.jsx)(rl.h,{"aria-label":"Dismiss banner",icon:rs.XIcon,variant:"invisible",onClick:o,"data-analytics-event":u,sx:{display:["none","inherit","none","inherit"],color:"fg.muted"}})]})]})]})})}function sG(e,t,n,r){return JSON.stringify({category:"Suggestions",action:e,label:`ref_page:${r?`https://github.com${(0,nJ.IY)(n)}`:window.location};${t};ref_loc:repo files listing;`})}try{(tj=sU).displayName||(tj.displayName="ProtectBranchBanner")}catch{}function sq({actionSlug:e,actionId:t}){let{sendMarketplaceActionEvent:n}=(0,rx.a)();return(0,nY.jsx)(ss.Z,{children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",gap:2},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",flexGrow:1},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center"},children:[(0,nY.jsx)(rd.Z,{icon:rs.PlayIcon,size:"small"}),(0,nY.jsx)(ru.Z,{sx:{fontWeight:600},children:"Use this GitHub action with your project"})]}),(0,nY.jsx)(ru.Z,{sx:{fontSize:0},children:"Add this Action to an existing workflow or create a new one"})]}),(0,nY.jsx)(oq.Q,{href:(0,nJ.J9)({slug:e}),sx:oQ.A,onClick:()=>n("MARKETPLACE.ACTION.CLICK",{repository_action_id:t,source_url:`${window.location}`,location:"files#overview"}),children:"View on Marketplace"})]})})}try{(tw=sq).displayName||(tw.displayName="UseActionBanner")}catch{}function sV({payload:e}){return(0,nQ.uF)(e)?(0,nY.jsx)(sK,{overview:e.overview}):(0,nQ.g6)(e)?(0,nY.jsx)(sY,{tree:e.tree}):(0,nQ.Kg)(e)?(0,nY.jsx)(sQ,{blob:e.blob}):null}function sK({overview:e}){let{showUseActionBanner:t,showProtectBranchBanner:n,actionId:r,actionSlug:i,publishBannersInfo:{dismissActionNoticePath:o,dismissStackNoticePath:a,releasePath:s,showPublishActionBanner:l,showPublishStackBanner:c},interactionLimitBanner:d,showInvitationBanner:u,inviterName:h}=e.banners,{helpUrl:m}=e;return(0,nY.jsxs)(n7.Z,{sx:{mb:d||u&&h||l||c||t&&i&&r||n?3:0,display:"flex",flexDirection:"column",rowGap:3},children:[d&&(0,nY.jsx)(s$,{interactionLimitBanner:d}),u&&h&&(0,nY.jsx)(sz,{inviterName:h}),(0,nY.jsx)(sx,{showPublishActionBanner:l,showPublishStackBanner:c,releasePath:s,dismissActionNoticePath:o,dismissStackNoticePath:a,sx:{mt:0}}),t&&i&&r&&(0,nY.jsx)(sq,{actionSlug:i,actionId:r}),n&&(0,nY.jsx)(sU,{helpUrl:m})]})}function sY({tree:e}){return(0,nY.jsxs)(su,{payload:e,children:[(0,nY.jsx)(sC,{}),(0,nY.jsx)(sX,{}),(0,nY.jsx)(sJ,{})]})}function sQ({blob:e}){return(0,nY.jsxs)(rq.d,{blob:e,children:[(0,nY.jsx)(sH,{}),(0,nY.jsx)(sR,{}),(0,nY.jsx)(sw,{}),(0,nY.jsx)(sh,{}),(0,nY.jsx)(sJ,{})]})}function sX(){let{items:e,totalCount:t}=sd(),n=t-e.length;return n>0?(0,nY.jsxs)(ss.Z,{variant:"warning","data-testid":"repo-truncation-warning",sx:{mt:3},children:["Sorry, we had to truncate this directory to ",e.length.toLocaleString()," files. ",n.toLocaleString()," ",1===n?"entry was":"entries were"," omitted from the list. Latest commit info may be omitted."]}):null}function sJ(){let e=(0,sl.KG)();return(0,nY.jsx)(nY.Fragment,{children:e.map((e,t)=>(0,nY.jsx)(ss.Z,{variant:e.variant,sx:{mt:3},children:e.message},t))})}try{(tv=sV).displayName||(tv.displayName="CodeViewBanners")}catch{}try{(tN=sK).displayName||(tN.displayName="OverviewBanners")}catch{}try{(tC=sY).displayName||(tC.displayName="TreeBanners")}catch{}try{(tk=sQ).displayName||(tk.displayName="BlobBanners")}catch{}try{(tS=sX).displayName||(tS.displayName="TruncatedTreeBanner")}catch{}try{(tI=sJ).displayName||(tI.displayName="CodeViewContextBanners")}catch{}var s0=n(48170);function s1(e){let t=(0,nX.H)(),{refInfo:n}=(0,rK.Br)();return(0,nY.jsx)(n7.Z,{sx:{minHeight:"100vh",margin:16},children:(0,nY.jsxs)(n7.Z,{sx:{border:"1px solid var(--borderColor-default, var(--color-border-default))",borderRadius:"6px",padding:50,display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column"},children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertIcon,sx:{color:"fg.muted",mb:2},size:20}),(0,nY.jsx)(s2,{...e}),(0,nY.jsx)(oq.Q,{type:"button",sx:{mt:4,...oQ.A},variant:"primary","aria-label":n.currentOid?"go to Overview":"go to default branch",href:n.currentOid?(0,nJ.IY)(t):(0,nJ.sA)(t),children:n.currentOid?"Return to the repository overview":"Go to default branch"})]})})}function s2({httpStatus:e,type:t}){return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:1,textAlign:"center"},children:[(0,nY.jsx)(n7.Z,{sx:{fontSize:4,color:"fg.default",fontWeight:"bold"},children:404===e?"404 - page not found":"Error loading page"}),404===e?(0,nY.jsx)(s3,{}):(0,nY.jsx)(s4,{httpStatus:e,type:t})]})}function s3(){let e=(0,nX.H)(),{path:t,refInfo:n}=(0,rK.Br)();return n.currentOid?(0,nY.jsxs)(n7.Z,{sx:{color:"fg.muted",display:"flex",flexWrap:"wrap",justifyContent:"center"},"data-testid":"eror-404-description",children:["The\xa0",(0,nY.jsx)(s0.Z,{as:"p",sx:{mb:0},children:n.name}),"\xa0branch of\xa0",(0,nY.jsx)(ru.Z,{as:"p",sx:{fontWeight:"bold",mb:0},children:e.name}),"\xa0does not contain the path\xa0",(0,nY.jsxs)(ru.Z,{as:"p",sx:{fontWeight:"bold",mb:0},children:[t,"."]})]}):(0,nY.jsxs)(n7.Z,{sx:{color:"fg.muted",display:"flex",flexWrap:"wrap",justifyContent:"center"},"data-testid":"error-404-description",children:["Cannot find a valid ref in\xa0",(0,nY.jsx)(s0.Z,{as:"p",sx:{mb:0},children:n.name})]})}function s4({httpStatus:e,type:t}){let n=e?` ${e} error`:"error";return"fetchError"===t?(0,nY.jsx)(n7.Z,{sx:{fontSize:1,color:"fg.muted"},"data-testid":"fetch-error-description",children:"It looks like your internet connection is down. Please check it."}):(0,nY.jsxs)(n7.Z,{sx:{fontSize:1,color:"fg.muted"},"data-testid":"default-error-description",children:["An unexpected ",n," occured. Try",(0,nY.jsx)(rR.Z,{onClick:()=>window.location.reload(),children:"\xa0reloading the page."},"reload-page")]})}try{(tR=s1).displayName||(tR.displayName="CodeViewError")}catch{}try{(tZ=s2).displayName||(tZ.displayName="ErrorText")}catch{}try{(tE=s3).displayName||(tE.displayName="DescriptionText404")}catch{}try{(tT=s4).displayName||(tT.displayName="DefaultDescriptionText")}catch{}var s8=n(98234),s6=n(57338),s5=n(40856),s9=n(68912);let s7={addition:"diffstat.additionBg",deletion:"danger.emphasis",neutral:"neutral.bg"},le={addition:"addition",deletion:"deletion",neutral:"neutral"};function lt({linesAdded:e,linesChanged:t,linesDeleted:n}){return`${t} ${1===t?"change":"changes"}: ${e} ${1===e?"addition":"additions"} & ${n} ${1===n?"deletion":"deletions"}`}function ln({linesAdded:e,linesDeleted:t,linesChanged:n}){let r=[...[,,,,,].fill(le.neutral)];if(n>0){let i=Math.floor(e/n*5);e>0&&(i=Math.max(1,i));let o=Math.floor(t/n*5);t>0&&(o=Math.max(1,o));let a=5-i-o;r=[...Array(i).fill(le.addition),...Array(o).fill(le.deletion),...Array(a).fill(le.neutral)]}return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(ru.Z,{sx:{fontWeight:"bold",fontSize:0,color:"success.fg",pl:2},children:["+",e]}),(0,nY.jsxs)(ru.Z,{sx:{fontWeight:"bold",fontSize:0,color:"danger.fg",px:1},children:["-",t]}),(0,nY.jsx)(i$.Z,{"aria-label":lt({linesAdded:e,linesChanged:n,linesDeleted:t}),direction:"e",children:(0,nY.jsx)(lr,{squares:r})})]})}function lr({squares:e}){return(0,nY.jsx)(n7.Z,{sx:{display:"flex"},children:e.map((e,t)=>(0,nY.jsx)(n7.Z,{"data-testid":`${e} diffstat`,sx:{backgroundColor:s7[e],width:"8px",height:"8px",marginLeft:"1px",outlineOffset:"-1px",borderStyle:"solid",borderColor:"border.subtle",borderWidth:"1px"}},t))})}try{(tL=ln).displayName||(tL.displayName="DiffStats")}catch{}try{(tB=lr).displayName||(tB.displayName="DiffSquares")}catch{}function li({diff:e,index:t}){let[n,r]=(0,n0.useState)(!1),[i,o]=(0,n0.useState)(!1),[a,s]=(0,n0.useState)(!1),l=[];for(let t=0;t<5;t++)e.deletions>t?l.push("deletion"):l.push("neutral");return e.diffHTML?(0,nY.jsx)("div",{id:"readme",className:"readme prose-diff html-blob blob",children:(0,nY.jsx)(iu.wB,{html:e.diffHTML,className:"markdown-body container-lg"})}):(0,nY.jsxs)(n7.Z,{sx:{border:"1px solid",borderColor:"border.default",borderRadius:"6px",mt:3},id:`diff-entry-${t}`,children:[(0,nY.jsxs)(n7.Z,{sx:{backgroundColor:"canvas.subtle",borderBottom:"1px solid",borderColor:"border.default",display:"flex",py:1,px:2,alignItems:"center",gap:2},children:[(0,nY.jsx)(rl.h,{"aria-label":"Search",icon:n?rs.ChevronRightIcon:rs.ChevronDownIcon,size:"small",variant:"invisible",onClick:()=>r(!n)}),(0,nY.jsx)(ru.Z,{sx:{color:"fg.muted"},children:e.deletions}),(0,nY.jsx)(lr,{squares:l}),(0,nY.jsx)(rR.Z,{sx:{color:"fg.default",cursor:"pointer"},underline:!1,href:`#diff-entry-${t}`,children:e.path}),(0,nY.jsx)(s9.m,{textToCopy:e.path,ariaLabel:"Copy path to clipboard"})]}),n?null:(0,nY.jsx)(n7.Z,{sx:{px:i?0:3,py:i?0:4,position:"relative"},tabIndex:-1,children:i?(0,nY.jsx)("include-fragment",{"data-testid":"delete-diff-fragment",src:e.loadDiffPath,onLoad:()=>s(!0),children:!a&&(0,nY.jsx)(n7.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"center",height:"137px"},children:(0,nY.jsx)(ix.Z,{})})}):(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(lo,{}),(0,nY.jsxs)(n7.Z,{sx:{position:"absolute",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",top:0,left:0,height:"100%",width:"100%"},children:[(0,nY.jsx)(rR.Z,{onClick:()=>o(!0),sx:{cursor:"pointer"},children:"Load diff"}),"This file was deleted."]})]})})]})}function lo(){return(0,nY.jsx)(n7.Z,{as:"svg","aria-hidden":"true",className:"width-full",viewBox:"0 0 340 84",xmlns:"http://www.w3.org/2000/svg",sx:{height:"84",maxWidth:"340px"},children:(0,nY.jsx)(n7.Z,{as:"path",className:"js-diff-placeholder",clipPath:"url(#diff-placeholder)",d:"M0 0h340v84H0z",fillRule:"evenodd",sx:{fill:"canvas.subtle"}})})}try{(t_=li).displayName||(t_.displayName="DiffEntry")}catch{}try{(tD=lo).displayName||(tD.displayName="DiffPlaceholderSvg")}catch{}function la({deleteInfo:e,webCommitInfo:t}){let{path:n}=(0,rK.Br)(),{helpUrl:r}=(0,rK.Ou)(),i=(0,nX.H)();return t.shouldFork||t.shouldUpdate||t.lockedOnMigration?(0,nY.jsx)(s5.P,{helpUrl:r,webCommitInfo:t}):(0,nY.jsxs)(n7.Z,{sx:{maxWidth:"1280px",mx:"auto"},children:[(0,nY.jsx)(re.Z,{as:"h1",className:"sr-only",children:`Deleting ${e.isBlob?"":"directory "}${i.name}/${n}`}),(0,nY.jsx)(s8.Z,{}),t.forkedRepo&&(0,nY.jsx)(s6.r,{forkName:t.forkedRepo.name,forkOwner:t.forkedRepo.owner}),e.truncated&&(0,nY.jsx)(ss.Z,{variant:"warning",className:"mb-2",children:"The diff you're trying to view is too large. We only load the first 1000 changed files."}),e.diffs.map((e,t)=>(0,nY.jsx)(li,{diff:e,index:t},t))]})}try{(tF=la).displayName||(tF.displayName="DeleteViewContent")}catch{}var ls=n(17338);let ll=n0.forwardRef(({expanded:e,onToggleExpanded:t,className:n,ariaControls:r},i)=>{let{toggleTreeShortcut:o}=(0,rY.bx)(),[a]=(0,iS.D)(()=>!1,!0,[]);return(0,nY.jsxs)(nY.Fragment,{children:[(!e||a)&&(0,nY.jsx)(ae.z,{"aria-label":"Expand side panel",leadingVisual:rs.ArrowLeftIcon,"data-hotkey":o.hotkey,"data-testid":"expand-file-tree-button-mobile",ref:i,onClick:t,variant:"invisible",sx:{color:"fg.muted",px:2,display:"none","@media screen and (max-width: 768px)":{display:"block"}},children:"Files"}),(0,nY.jsx)(ls.V,{dataHotkey:o.hotkey,className:n,expanded:e,alignment:"left",ariaLabel:"Side panel",testid:"file-tree-button",ariaControls:r,ref:i,onToggleExpanded:t,sx:{height:"32px",position:"relative","@media screen and (max-width: 768px)":{display:!e||a?"none":"flex"}}}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:o.hotkey,onButtonClick:t,onlyAddHotkeyScopeButton:!0})]})});ll.displayName="ExpandFileTreeButton";var lc=n(17206),ld=n(13275),lu=n(8677),lh=n(12464);function lm({sx:e,onClick:t}){let{searchShortcut:n}=(0,rY.bx)();return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rl.h,{"aria-label":"Search this repository",icon:rs.SearchIcon,"data-hotkey":n.hotkey,sx:{color:"fg.subtle",fontSize:14,fontWeight:"normal",flexShrink:0,...e},size:"medium",onClick:e=>{t?.(),(0,or.n)({retainScrollPosition:!0,returnTarget:e.target.closest("button")})}}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:n.hotkey,onButtonClick:()=>{let e=document.getElementById(r1.KG);t?.(),(0,or.n)({retainScrollPosition:!0,returnTarget:e??void 0})},onlyAddHotkeyScopeButton:!0})]})}try{(tO=lm).displayName||(tO.displayName="SearchButton")}catch{}var lf=n(66523);let lp=(0,n0.createContext)({knownFolders:new Map,dispatchKnownFolders:()=>{}});function lx(){return(0,n0.useContext)(lp)}try{(tP=lp).displayName||(tP.displayName="FileTreeContext")}catch{}var ly=n(18643),lg=n(36218),lb=n(52793),lj=n(31360),lw=n(70697);function lv(e,t,n){let[r,i]=n0.useState(e.items),[o,a]=n0.useState(e.data.totalCount||0),[s,l]=n0.useState(!1),[c,d]=n0.useState(!1),{safeSetTimeout:u}=(0,lw.Z)();n0.useEffect(()=>{i(e.items)},[e.items]),n0.useEffect(()=>{void 0!==e.data.totalCount&&a(e.data.totalCount)},[e.data.totalCount]);let h=n0.useCallback(()=>{d(!1)},[]),m=n0.useCallback((e,t)=>{let n=e||[...r];i(n.slice(0,100)),u(()=>{i(n),void 0!==t&&a(t)},1)},[r,u]),f=n0.useCallback(async()=>{let r=n(e.data),o=new Map;d(!1),l(!0);let s=Date.now(),c=await (0,it.v)(`${r}?noancestors=1`);try{if(c.ok){let n=await c.json(),r=n.payload.tree.items.map(e=>{let t={items:[],data:{...e},autoExpand:"directory"===e.contentType&&1===n.payload.tree.items.length};if(o.set(e.path,t),e.hasSimplifiedPath){let n=lN(t,e,o);return n}return t});t({type:"add",folders:o,processingTime:Date.now()-s}),e.items=r,e.data.totalCount=n.payload.tree.totalCount,r.length>100?m(r,n.payload.tree.totalCount):(i(r),a(n.payload.tree.totalCount))}else d(!0)}catch{d(!0)}l(!1)},[n,e,t,m]);return[f,m,r,s,c,h,o]}function lN(e,t,n){e.data.name=e.data.name.slice(e.data.name.lastIndexOf("/")+1,e.data.name.length);let r=t.name.slice(0,t.name.lastIndexOf("/")),i=r.indexOf("/")>-1,o={path:t.path.slice(0,t.path.lastIndexOf("/")),contentType:t.contentType,name:r,hasSimplifiedPath:i},a={items:[e],data:o};return(n.set(o.path,a),i)?lN(a,{...o},n):a}function lC(e,t,n){if(!e)return{newRootItems:n,rootItemsUpdated:!1};let r=lk("",t,n,e[""].items);for(let n of Object.keys(e).sort())if(n){let r=t.get(n);r&&(lk(n,t,r.items,e[n].items),r.data.totalCount=e[n].totalCount)}return{newRootItems:n,rootItemsUpdated:r}}function lk(e,t,n,r){let i=!1;for(let o of r){let r=e?`${e}/${o.name}`:o.name;if(!t.get(r)){let e={items:[],data:{...o}};if(t.set(r,e),o.hasSimplifiedPath){let r=lN(e,o,t),a=n.findIndex(e=>e.data.path===r.data.path);-1!==a?r.items.length>n[a].items.length&&(n[a]=r,i=!0):n.push(r)}else n.push(e)}}return i}function lS(e,t){let[n,r]=n0.useState(!1);return n0.useEffect(()=>{if(e.current&&t.current){let n=()=>{let t=e.current?.querySelector(".PRIVATE_TreeView-item-content-text");t?.scrollWidth!==t?.offsetWidth&&r(!0)};e.current.onfocus=()=>{n()},e.current.onblur=()=>{r(!1)},t.current.onmouseenter=()=>{n()},t.current.onmouseleave=()=>{r(!1)}}},[e,t]),n}function lI({isActive:e,file:t,onItemSelected:n,getItemUrl:r,selectedItemRef:i,navigate:o,onRenderRow:a}){let{sendRepoClickEvent:s}=(0,rx.a)(),l=n0.useRef(null),c=lS(l,l),d="submodule"===t.data.contentType,u=n0.useCallback(i=>{d?(i.preventDefault(),t.data.submoduleUrl&&(window.location.href=t.data.submoduleUrl)):i.metaKey||i.ctrlKey||1===i.button?(window.open(r(t.data),"_blank"),i.preventDefault()):e?i.preventDefault():(n?.(),s("FILES_TREE.ITEM",{item_path:t.data.path}),o(r(t.data)),i.stopPropagation())},[t.data,r,e,d,o,n,s]);return a?.(),(0,nY.jsxs)(rg.L.Item,{ref:l,onSelect:u,current:e,id:`${t.data.path}-item`,containIntrinsicSize:e?void 0:"auto 2rem",children:[(0,nY.jsx)(rg.L.LeadingVisual,{children:d?(0,nY.jsx)(rs.FileSubmoduleIcon,{}):(0,nY.jsx)(rs.FileIcon,{})}),(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)("span",{ref:i,style:{color:d?"var(--color-accent-fg)":void 0},children:t.data.name}),c&&(0,nY.jsx)(lb.u,{"data-testid":`${t.data.name}-item-tooltip`,id:`${t.data.name}-item-tooltip`,contentRef:l,"aria-label":t.data.name,open:!0,direction:"ne"})]})]})}let lR=n0.memo(lI);function lZ({directory:e,isActive:t,isAncestorOfActive:n,leadingPath:r="",onItemSelected:i,dispatchKnownFolders:o,getItemUrl:a,selectedItemRef:s,navigate:l,onRenderRow:c}){let[d,u]=(0,n0.useState)(n),{sendRepoClickEvent:h}=(0,rx.a)(),m=n0.useRef(null),f=n0.useRef(null),p=lS(f,m),x=n0.useCallback(n=>{n.metaKey||n.ctrlKey||1===n.button?(window.open(a(e.data),"_blank"),n.preventDefault()):t?n.preventDefault():(i?.(),h("FILES_TREE.ITEM",{item_path:e.data.path}),l(a(e.data)),n.stopPropagation())},[e.data,a,t,l,i,h]),[y,g,b,j,w,v,N]=lv(e,o,a),C=N-b.length,k=r?`${r}/`:"",S=n0.useCallback(r=>{!r||d||j||w||(e.items.length>100?g():0!==e.items.length||t||n||y()),r!==d&&u(r)},[d,j,w,e.items.length,t,n,g,y]);n0.useEffect(()=>{n&&!d&&S?.(!0)},[n]),n0.useEffect(()=>{0===e.items.length&&d?S?.(!1):!d&&e.autoExpand&&S?.(!0)},[e.items.length]);let I=n0.useCallback(e=>{s&&t&&s(e),m.current=e},[s,t]);return 1===e.items.length&&"directory"===e.items[0].data.contentType?(0,nY.jsx)(lL,{directoryItems:e.items,leadingPath:k+e.data.name,inheritsActive:t,dispatchKnownFolders:o,onItemSelected:i,selectedItemRef:s}):(c?.(),(0,nY.jsxs)(rg.L.Item,{ref:f,expanded:d,onExpandedChange:S,current:t,onSelect:x,id:`${e.data.path}-item`,containIntrinsicSize:t?void 0:"auto 2rem",children:[(0,nY.jsx)(rg.L.LeadingVisual,{children:(0,nY.jsx)(rg.L.DirectoryIcon,{})}),(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)("span",{ref:I,children:[k,e.data.name]}),p&&(0,nY.jsx)(lb.u,{"data-testid":`${e.data.name}-directory-item-tooltip`,id:`${e.data.name}-directory-item-tooltip`,contentRef:f,"aria-label":`${k}${e.data.name}`,open:!0,direction:"ne"})]}),(0,nY.jsx)(rg.L.SubTree,{state:j?"loading":w?"error":"done",children:w?(0,nY.jsx)(rg.L.ErrorDialog,{onRetry:y,onDismiss:v,children:"There was an error loading the folder contents."}):(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(lL,{directoryItems:b,dispatchKnownFolders:o,onItemSelected:i,selectedItemRef:s}),C>0&&(0,nY.jsx)(l_,{message:`${C} entries not shown`})]})})]}))}let lE=n0.memo(lZ);function lT({directoryItems:e,leadingPath:t,inheritsActive:n,onItemSelected:r,dispatchKnownFolders:i,selectedItemRef:o,onRenderRow:a}){let{path:s}=(0,rK.Br)(),{getItemUrl:l}=(0,oY.B)(),c=(0,rX.s)(),d=n0.useRef(c);return(0,nY.jsx)(nY.Fragment,{children:e.map(e=>{let c=s===e.data.path,u=c||s.startsWith(`${e.data.path}/`);return"directory"===e.data.contentType?(0,nY.jsx)(lE,{isActive:n||c,isAncestorOfActive:u,onItemSelected:r,leadingPath:t,directory:e,dispatchKnownFolders:i,getItemUrl:l,selectedItemRef:u?o:void 0,navigate:d.current,onRenderRow:a},e.data.name):(0,nY.jsx)(lR,{onItemSelected:r,file:e,isActive:c,getItemUrl:l,selectedItemRef:c?o:void 0,navigate:d.current,onRenderRow:a},e.data.name)})})}let lL=n0.memo(lT);function lB(e){let{data:t,rootItems:n,setRootItems:r,fetchError:i,processingTime:o,loading:a,onRenderRow:s}=e,{knownFolders:l,dispatchKnownFolders:c}=lx(),d=(0,lj.T)();n0.useEffect(()=>{if(a)return;let e=new Map,i=[];d.current||(e=new Map(l),i=n.slice());let{newRootItems:s,rootItemsUpdated:u}=lC(t,e,i);(s.length>n.length||u||d.current)&&r(s),(e.size>l.size||d.current)&&c({type:"set",folders:e,processingTime:o}),d.current=!1},[t,a]);let u=n0.useCallback(e=>{1===e.button&&e.preventDefault()},[]);return(0,nY.jsx)(n7.Z,{onMouseDown:u,sx:{px:3,pb:2},"data-testid":"repos-file-tree-container",children:a?(0,nY.jsx)(n7.Z,{sx:{display:"flex",justifyContent:"center",p:2},children:(0,nY.jsx)(ix.Z,{"aria-label":"Loading file tree"})}):(0,nY.jsx)("nav",{"aria-label":"File Tree Navigation",children:(0,nY.jsxs)(rg.L,{"aria-label":"Files",children:[i&&(0,nY.jsx)(l_,{message:"Some files could not be loaded."}),(0,nY.jsx)(lL,{directoryItems:n,onItemSelected:e.onItemSelected,dispatchKnownFolders:c,selectedItemRef:e.selectedItemRef,onRenderRow:s})]})})})}function l_({message:e}){return(0,nY.jsxs)(rg.L.Item,{id:"error-tree-row",children:[(0,nY.jsx)(rg.L.LeadingVisual,{children:(0,nY.jsx)(rd.Z,{icon:rs.AlertFillIcon,sx:{color:"attention.fg"}})}),(0,nY.jsx)(n7.Z,{sx:{color:"fg.muted"},children:e||"Couldn't load."})]})}try{(tA=lI).displayName||(tA.displayName="WrappedFileTreeRow")}catch{}try{(tM=lR).displayName||(tM.displayName="FileTreeRow")}catch{}try{(tH=lZ).displayName||(tH.displayName="WrappedDirectoryTreeRow")}catch{}try{(t$=lE).displayName||(t$.displayName="DirectoryTreeRow")}catch{}try{(tW=lT).displayName||(tW.displayName="WrappedDirectoryContents")}catch{}try{(tz=lL).displayName||(tz.displayName="DirectoryContents")}catch{}try{(tU=lB).displayName||(tU.displayName="ReposFileTreeView")}catch{}try{(tG=l_).displayName||(tG.displayName="ErrorTreeRow")}catch{}let lD=n4._G.xxxlarge;function lF({collapseTree:e,showTree:t,fileTree:n,treeToggleElement:r,treeToggleRef:i,onItemSelected:o,processingTime:a,searchBoxRef:s,repo:l,path:c,refInfo:d,isFilePath:u,foldersToFetch:h,isOverview:m,id:f,onFindFilesShortcut:p}){let{openPanel:x}=rU(),[y,g]=n0.useState(h.length>0),[b,j]=n0.useState(!1),w=n0.useRef([]),v=n0.useRef(null),N=n0.useRef(!1),C=n0.useRef(!1),k=n0.useRef(null),{getItemUrl:S}=(0,oY.B)(),{query:I}=(0,lf.aM)(),{codeCenterOption:R}=(0,n6.O)(),Z=n0.useRef(x),[E]=(0,iS.D)(()=>!1,!0,[]),T=[],L=new Map,B=n0.useRef(y);if(!B.current&&n){let e=lC(n,L,[]);T=e.newRootItems}B.current=!0;let[_,D]=n0.useReducer(lO,L),[F,O]=n0.useState(T);n0.useEffect(()=>{t&&(!I||window.innerWidth>=n4._G.large)||(k.current=null)},[t,I]),n0.useEffect(()=>{x&&Z.current!==x&&window.innerWidth<lD&&e({setCookie:!1}),Z.current=x},[e,x]);let P=n0.useCallback(async e=>{let t=S({contentType:"directory",path:e,name:e});try{let r=await (0,it.v)(`${t}?noancestors=1`);if(r.ok){let t=await r.json(),i={items:t.payload.tree.items,totalCount:t.payload.tree.totalCount};n[e]=i}else j(!0)}catch{j(!0)}w.current.push(e),w.current.length===h.length&&g(!1)},[n,h.length,S]);n0.useEffect(()=>{if(h&&!N.current)for(let e of h)P(e);N.current=!0},[P,h,_.size]);let A=n0.useCallback(e=>{t&&(!I||window.innerWidth>=n4._G.large)&&v.current&&e&&(0,ld.z)(e,v.current,{endMargin:window.innerHeight/2,startMargin:window.innerHeight/2,behavior:"auto"})},[I,t]),M=n0.useCallback(e=>{e&&C.current?C.current=!1:k.current!==e&&A(e),k.current=e},[A]),H=n0.useCallback(e=>{v.current=e;let t=window.innerWidth;t>=lD&&A(k.current)},[A]),$=n0.useCallback(e=>{e&&A(k.current)},[A]),{screenSize:W}=(0,n4.eI)(),z=!E&&((m||x)&&W<lD||W<n4._G.xlarge)&&W>=n4._G.large,U=n0.useCallback(()=>{z||o(),C.current=!0},[o,z]),G=t?{}:{display:"none"},q=u?c.substring(0,c.lastIndexOf("/")):c,V=(0,n0.useMemo)(()=>({knownFolders:_,dispatchKnownFolders:D}),[_]),K=n0.useMemo(()=>E?null:(0,lh.oD)(),[E]),Y=n0.useCallback(()=>{window.innerWidth>n4._G.large&&window.innerWidth<n4._G.xxxxlarge&&e({setCookie:!1})},[e]),Q=(0,nY.jsxs)(n7.Z,{id:f,sx:{maxHeight:"100%",height:"100%",display:"flex",flexDirection:"column","@media screen and (max-width: 768px)":E?{display:"none"}:void 0,"@media screen and (min-width: 768px)":{maxHeight:"100vh",height:"100vh"}},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",px:3,pb:2,pt:3},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",width:"100%",mb:3,alignItems:"center"},children:[t&&r,(0,nY.jsx)(re.Z,{as:"h2",sx:{fontSize:2,ml:2},children:"Files"})]}),(0,nY.jsxs)(n7.Z,{sx:{mx:4,display:"flex",width:"100%"},children:[(0,nY.jsx)(n7.Z,{sx:{flexGrow:1},children:(0,nY.jsx)(ao,{buttonClassName:"react-repos-tree-pane-ref-selector width-full ref-selector-class",allowResizing:!0})}),(0,nY.jsxs)(n7.Z,{sx:{ml:2,whiteSpace:"nowrap","&:hover button:not(:hover)":{borderLeftColor:"var(--button-default-borderColor-hover, var(--color-btn-hover-border))"}},children:[d.canEdit&&(0,nY.jsx)(i$.Z,{"aria-label":"Add file",direction:"s",children:(0,nY.jsx)(rl.h,{"aria-label":"Add file",as:iM.r,sx:{color:"fg.subtle",borderTopRightRadius:0,borderBottomRightRadius:0,borderRight:0},icon:rs.PlusIcon,to:(0,nJ.Qi)({repo:l,path:q,commitish:d.name,action:"new"}),onClick:U})}),(0,nY.jsx)(lm,{sx:d.canEdit?{borderTopLeftRadius:0,borderBottomLeftRadius:0}:void 0,onClick:Y})]})]})]}),d.currentOid&&(0,nY.jsx)(lg.default,{onItemSelected:o,searchBoxRef:s,isOverview:m,onFindFilesShortcut:p,sx:{ml:3,mr:3,mb:"12px","@media screen and (max-width: 768px)":E?{display:"none"}:void 0}}),(0,nY.jsx)(lP,{scrollingRef:v}),(0,nY.jsxs)(n7.Z,{ref:H,sx:{flexGrow:1,maxHeight:"100% !important",overflowY:"auto","@media screen and (max-width: 768px)":E?{display:"none"}:void 0,scrollbarGutter:"stable"},children:[E?d.currentOid&&(0,nY.jsx)("div",{className:I?"react-tree-show-tree-items-on-large-screen":"react-tree-show-tree-items",children:(0,nY.jsx)(lB,{data:n,rootItems:F,selectedItemRef:M,setRootItems:O,onItemSelected:U,processingTime:a,loading:y,fetchError:b})}):K&&(0,nY.jsx)(lh.t0,{node:K}),!d.currentOid&&!l.isEmpty&&(0,nY.jsxs)(n7.Z,{sx:{mt:2,mx:4,mb:"12px",fontSize:1,alignItems:"center",color:"danger.fg"},children:[(0,nY.jsx)(rd.Z,{icon:rs.AlertFillIcon}),"\xa0Ref is invalid"]}),(0,nY.jsx)(n7.Z,{sx:{"@media (min-height: 600px) and (min-width: 768px)":{display:"none"}},children:(0,nY.jsx)(ly.I,{})})]}),(0,nY.jsx)(n7.Z,{sx:{"@media (max-height: 599px), (max-width: 767px)":{display:"none"}},children:(0,nY.jsx)(ly.I,{})})]});return(0,nY.jsxs)(lp.Provider,{value:V,children:[K&&(0,nY.jsx)(lh.Nq,{node:K,children:d.currentOid&&(0,nY.jsx)("div",{className:I?"react-tree-show-tree-items-on-large-screen":"react-tree-show-tree-items",children:(0,nY.jsx)(lB,{data:n,rootItems:F,selectedItemRef:M,setRootItems:O,onItemSelected:U,processingTime:a,loading:y,fetchError:b})})}),!t&&R.enabled&&!m&&(0,nY.jsx)(n7.Z,{sx:{position:"absolute",p:m?void 0:3,pl:m?3:void 0,mt:m?3:void 0,display:"none",...m?{"@media screen and (min-width: 1476px)":{display:"block"}}:{"@media screen and (min-width: 1360px)":{display:"block"}}},children:r}),(!m||W<n4._G.large)&&(0,nY.jsx)(rt.jw.Pane,{position:"start",sticky:!0,sx:{minWidth:0,...G,flexDirection:["column","column","inherit"],"@media screen and (min-width: 769px)":{height:"100vh",maxHeight:"100vh !important"},...m||z||x?{"@media print, screen and (max-width: 1349px) and (min-width: 768px)":{display:"none"}}:{"@media print, screen and (max-width: 1011px) and (min-width: 768px)":{display:"none"}}},padding:"none",width:"large",resizable:!0,widthStorageKey:"codeView.tree-pane-width",divider:{regular:"line",narrow:"none"},children:t&&!z&&(0,nY.jsx)(n7.Z,{className:E?x?"react-tree-pane-contents-3-panel":"react-tree-pane-contents":void 0,children:(0,nY.jsx)(nY.Fragment,{children:Q})})}),t&&!m&&z&&Z.current===x&&(0,nY.jsx)(lu.Z,{className:E?m||x?"react-tree-pane-overlay-3-panel":"react-tree-pane-overlay":void 0,ref:$,returnFocusRef:i,onClickOutside:Y,onEscape:Y,top:0,position:"fixed",sx:{...G,width:"440px",height:"100vh",maxHeight:"100vh",borderTopLeftRadius:0,borderBottomLeftRadius:0},children:(0,nY.jsx)(nY.Fragment,{children:Q})})]})}function lO(e,t){switch(t.type){case"set":{let n=e?.size>0;return(0,rP.qP)("file-tree",{"fetch-count":n?t.folders.size-e.size:t.folders.size,"file-count":t.folders.size,"nav-type":n?"soft":"hard","processing-time":t.processingTime}),t.folders}case"add":{let n=new Map([...e,...t.folders]);return(0,rP.qP)("file-tree",{"fetch-count":t.folders.size,"file-count":n.size,"nav-type":"fetch","processing-time":t.processingTime}),n}default:throw Error(`Unknown action type: ${t.type}`)}}function lP({scrollingRef:e}){let[t,n]=n0.useState(e.current&&e.current.scrollTop>0);return n0.useEffect(()=>{if(e.current){let t=e.current,r=()=>{t&&t.scrollTop>0?n(!0):n(!1)};return t.addEventListener("scroll",r),()=>{t.removeEventListener("scroll",r)}}},[e]),t?(0,nY.jsx)(n7.Z,{sx:{borderBottom:"1px solid",borderColor:"border.default",boxShadow:"0 3px 8px rgba(0, 0, 0, 0.3)"}}):null}try{(tq=lF).displayName||(tq.displayName="ReposFileTreePane")}catch{}try{(tV=lP).displayName||(tV.displayName="TreeBorder")}catch{}var lA=n(72982);function lM(){let[e,t]=(0,n0.useState)(),[n,r]=(0,n0.useState)(),{refInfo:i,path:o}=(0,rK.Br)(),a=(0,nX.H)(),s=(0,nJ.Qi)({repo:a,action:"branch-infobar",commitish:i.name,path:o});return(0,n0.useEffect)(()=>{let e=async()=>{t(void 0);let e=await (0,it.v)(s);try{e.ok?t(await e.json()):r(422===e.status?"timeout":e.statusText)}catch(e){r(e?.message||e?.toString())}};e()},[s]),[e,n]}var lH=n(84915);function l$({children:e}){return(0,nY.jsx)(n7.Z,{className:"popover-container-width",sx:{borderRadius:6,minWidth:250},children:e})}function lW({icon:e,header:t,content:n}){return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",p:3},children:[(0,nY.jsx)(n7.Z,{sx:{mr:2},children:e}),(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsx)(re.Z,{as:"h2",sx:{fontSize:1,mb:1},children:t}),(0,nY.jsx)(ru.Z,{sx:{color:"fg.muted",fontSize:0},children:n})]})]})}function lz({icon:e,bg:t}){return(0,nY.jsx)(lH.Z,{sx:{bg:t,color:"fg.onEmphasis"},size:30,icon:()=>(0,nY.jsx)(e,{size:16})})}function lU({children:e}){return(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexWrap:"wrap",p:3,justifyContent:"space-between",borderTop:"solid 1px",borderColor:"border.muted",gap:3},children:e})}try{(tK=l$).displayName||(tK.displayName="PopoverContainer")}catch{}try{(tY=lW).displayName||(tY.displayName="PopoverContent")}catch{}try{(tQ=lz).displayName||(tQ.displayName="PopoverIcon")}catch{}try{(tX=lU).displayName||(tX.displayName="PopoverActions")}catch{}function lG(e){return`${e} ${1===e?"commit":"commits"}`}function lq({comparison:e,repo:t,linkify:n=!1}){let{sendRepoClickEvent:r}=(0,rx.a)(),{ahead:i,behind:o,baseBranch:a,baseBranchRange:s,currentRef:l}=e,c=(0,nJ.j6)({repo:t,base:s,head:l}),d=(0,nJ.j6)({repo:t,base:l,head:s}),u=()=>r("AHEAD_BEHIND_LINK",{category:"Branch Infobar",action:"Ahead Compare",label:`ref_loc:bar;is_fork:${t.isFork}`}),h=()=>r("AHEAD_BEHIND_LINK",{category:"Branch Infobar",action:"Behind Compare",label:`ref_loc:bar;is_fork:${t.isFork}`});return 0===i&&0===o?(0,nY.jsxs)("span",{children:["This branch is up to date with ",(0,nY.jsx)(s0.Z,{as:"span",children:a}),"."]}):i>0&&o>0?(0,nY.jsxs)("span",{children:["This branch is"," ",(0,nY.jsxs)(lV,{linkify:n,href:c,onClick:u,children:[lG(i)," ahead of"]}),","," ",(0,nY.jsxs)(lV,{linkify:n,href:d,onClick:h,children:[lG(o)," behind"]})," ",(0,nY.jsx)(s0.Z,{as:"span",children:a}),"."]}):i>0?(0,nY.jsxs)("span",{children:["This branch is"," ",(0,nY.jsxs)(lV,{linkify:n,href:c,onClick:u,children:[lG(i)," ahead of"]})," ",(0,nY.jsx)(s0.Z,{as:"span",children:a}),"."]}):(0,nY.jsxs)("span",{children:["This branch is"," ",(0,nY.jsxs)(lV,{linkify:n,href:d,onClick:h,children:[lG(o)," behind"]})," ",(0,nY.jsx)(s0.Z,{as:"span",children:a}),"."]})}function lV({sx:e,href:t,linkify:n,children:r,...i}){return n?(0,nY.jsx)(rR.Z,{sx:e,href:t,...i,children:r}):(0,nY.jsx)(ru.Z,{children:r})}try{(tJ=lq).displayName||(tJ.displayName="RefComparisonText")}catch{}try{(t0=lV).displayName||(t0.displayName="LinkOrText")}catch{}function lK({comparison:e}){let t=(0,nX.H)(),n=e.ahead>0,r=(0,nJ.j6)({repo:t,base:e.baseBranchRange,head:e.currentRef}),i=(0,nJ.wu)({repo:t,refName:e.currentRef});return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(lW,{icon:(0,nY.jsx)(lz,{bg:"neutral.emphasis",icon:rs.GitPullRequestIcon}),header:n?(0,nY.jsx)(lq,{repo:t,comparison:{...e,behind:0}}):(0,nY.jsxs)(ru.Z,{children:["This branch is not ahead of the upstream ",(0,nY.jsx)(s0.Z,{as:"span",children:e.baseBranch}),"."]}),content:(0,nY.jsx)(ru.Z,{as:"p",children:n?"Open a pull request to contribute your changes upstream.":"No new commits yet. Enjoy your day!"})}),n&&(0,nY.jsxs)(lU,{children:[!t.isFork&&(0,nY.jsx)(ae.z,{as:rR.Z,sx:{flex:1,...oQ.A},href:r,"data-testid":"compare-button",children:"Compare"}),(0,nY.jsx)(ae.z,{as:rR.Z,sx:{flex:1,...oQ.A},href:i,variant:"primary","data-testid":"open-pr-button",children:"Open pull request"})]})]})}try{(t1=lK).displayName||(t1.displayName="ContributePopoverContent")}catch{}function lY({comparison:e}){let{sendRepoClickEvent:t}=(0,rx.a)();return(0,nY.jsxs)(oS.P,{onOpenChange:e=>e&&t("CONTRIBUTE_BUTTON",{category:"Branch Infobar",action:"Open Contribute dropdown",label:"ref_loc:contribute_dropdown"}),children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsxs)(ae.z,{leadingVisual:rs.GitPullRequestIcon,children:["Contribute",(0,nY.jsx)(rd.Z,{icon:rs.TriangleDownIcon,sx:{marginLeft:1}})]})}),(0,nY.jsx)(oS.P.Overlay,{align:"end",sx:{marginTop:2},children:(0,nY.jsx)(l$,{children:(0,nY.jsx)(lK,{comparison:e})})})]})}try{(t2=lY).displayName||(t2.displayName="ContributeButton")}catch{}function lQ(){let e=(0,nX.H)(),{refInfo:{name:t}}=(0,rK.Br)(),n=(0,nJ.db)({repo:e,refName:t,discard:!0}),r=(0,nJ.db)({repo:e,refName:t,discard:!1}),i=(0,n0.useCallback)(()=>(0,it.Q)(r,{method:"POST"}),[r]),o=(0,n0.useCallback)(()=>(0,it.Q)(n,{method:"POST"}),[n]);return{updateBranch:i,discardChanges:o}}function lX({head:e,base:t}){let[n,r]=(0,n0.useState)(),[i,o]=(0,n0.useState)(!0),[a,s]=(0,n0.useState)(),l=(0,nX.H)(),c=(0,nJ.$_)({repo:l,head:e,base:t});return(0,n0.useEffect)(()=>{let e=async()=>{o(!0),r(void 0);let e=await (0,it.v)(c);try{e.ok?r((await e.json()).state):s(e.statusText)}catch(e){s(e?.message||e?.toString())}o(!1)};e()},[c]),[n,i,a]}function lJ(e,t,n){let[r,i]=(0,n0.useState)(!1);return{disabled:r,label:r?t:e,action:async()=>{i(!0),await n(),i(!1)}}}let l0={category:"Branch Infobar",label:"ref_loc:fetch_upstream_dropdown"};function l1({comparison:e,discard:t,update:n}){let r=(0,nX.H)(),{helpUrl:i}=(0,rK.Ou)(),{sendRepoClickEvent:o}=(0,rx.a)(),a=`${i}/github/collaborating-with-issues-and-pull-requests/syncing-a-fork`,s=(0,nJ.j6)({repo:r,base:e.baseBranchRange,head:e.currentRef}),l=l2(e),c=e.behind>0,d=lJ(`Discard ${lG(e.ahead)}`,"Discarding changes...",t),u=lJ("Update branch","Updating...",n),h={compare:"behind"===l,discard:"behind-and-ahead"===l&&e.isTrackingBranch,update:["behind","behind-and-ahead"].includes(l)},m=Object.values(h).some(Boolean);return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(lW,{icon:(0,nY.jsx)(lz,{icon:c?rs.GitMergeIcon:rs.CheckIcon,bg:c?"neutral.emphasis":"success.emphasis"}),header:(0,nY.jsx)(l3,{comparison:e}),content:(0,nY.jsx)(l4,{comparison:e,helpUrl:a})}),m&&(0,nY.jsxs)(lU,{children:[h.compare&&(0,nY.jsx)(ae.z,{as:rR.Z,sx:{flex:1,...oQ.A},href:s,onClick:()=>o("SYNC_FORK.COMPARE",{...l0,action:"Compare"}),"data-testid":"compare-button",children:"Compare"}),h.discard&&(0,nY.jsx)(ae.z,{onClick:d.action,sx:{flex:1},"data-testid":"discard-button",variant:"danger",disabled:d.disabled,children:d.label}),h.update&&(0,nY.jsx)(ae.z,{onClick:u.action,disabled:u.disabled,sx:{flex:1},variant:"primary","data-testid":"update-branch-button",children:u.label})]})]})}function l2({behind:e,ahead:t}){return 0===e&&0===t?"sync":e>0&&t>0?"behind-and-ahead":e>0?"behind":"ahead"}function l3({comparison:e}){let t=l2(e);switch(t){case"behind":case"behind-and-ahead":return(0,nY.jsx)(ru.Z,{children:"This branch is out-of-date"});default:return(0,nY.jsxs)(ru.Z,{children:["This branch is not behind the upstream ",(0,nY.jsx)(s0.Z,{as:"span",children:e.baseBranch}),"."]})}}function l4({comparison:e,helpUrl:t}){let n=l2(e);switch(n){case"sync":case"ahead":return(0,nY.jsx)(ru.Z,{as:"p",children:"No new commits to fetch. Enjoy your day!"});case"behind":return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(ru.Z,{as:"p",children:["Update branch to keep this branch up-to-date by syncing ",lG(e.behind)," from the upstream repository."]}),(0,nY.jsx)(ru.Z,{as:"p",children:(0,nY.jsx)(rR.Z,{href:t,target:"_blank",rel:"noopener noreferrer",children:"Learn more about syncing a fork"})})]});case"behind-and-ahead":return e.isTrackingBranch?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(ru.Z,{as:"p",children:"Update branch to merge the latest changes from the upstream repository into this branch."}),(0,nY.jsxs)(ru.Z,{as:"p",children:["Discard ",lG(e.ahead)," to make this branch match the upstream repository."," ",lG(e.ahead)," will be removed from this branch."]}),(0,nY.jsx)(ru.Z,{as:"p",children:(0,nY.jsx)(rR.Z,{href:t,target:"_blank",rel:"noopener noreferrer",children:"Learn more about syncing a fork"})})]}):(0,nY.jsx)(ru.Z,{as:"p",children:"Update branch to merge the latest changes from the upstream repository into this branch."})}}try{(t3=l1).displayName||(t3.displayName="FetchUpstreamPopoverContent")}catch{}try{(t4=l3).displayName||(t4.displayName="HeaderText")}catch{}try{(t8=l4).displayName||(t8.displayName="ContentText")}catch{}function l8({comparison:e,discard:t}){let n=(0,nX.H)(),{sendRepoClickEvent:r}=(0,rx.a)(),i=(0,nJ.wu)({repo:n,refName:e.currentRef}),o=lG(e.ahead),a=lJ(`Discard ${o}`,"Discarding changes...",t);return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(lW,{icon:(0,nY.jsx)(lz,{icon:rs.AlertIcon,bg:"neutral.emphasis"}),header:"This branch has conflicts that must be resolved",content:(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(ru.Z,{as:"p",children:["Discard ",o," to make this branch match the upstream repository. ",o," will be removed from this branch."]}),(0,nY.jsx)(ru.Z,{as:"p",children:"You can resolve merge conflicts using the command line and a text editor."})]})}),(0,nY.jsxs)(lU,{children:[(0,nY.jsx)(ae.z,{sx:{flex:1},onClick:a.action,disabled:a.disabled,"data-testid":"discard-button",variant:"danger",children:a.label}),(0,nY.jsx)(ae.z,{as:rR.Z,sx:{flex:1,...oQ.A},href:i,variant:"primary","data-testid":"open-pr-button",onClick:()=>r("SYNC_FORK.OPEN_PR",{...l0,action:"Open pull request"}),children:"Open pull request"})]})]})}try{(t6=l8).displayName||(t6.displayName="FetchUpstreamWithConflictsPopoverContent")}catch{}function l6({comparison:e}){let[t,n,r]=lX({base:e.currentRef,head:e.baseBranchRange}),{sendRepoClickEvent:i}=(0,rx.a)(),{discardChanges:o,updateBranch:a}=lQ(),s=async()=>{i("SYNC_FORK.DISCARD",{...l0,action:"Discard Conflicts"});let e=await o();e.ok&&e.url&&(window.location.href=e.url)},l=async()=>{i("SYNC_FORK.UPDATE",{...l0,action:"Fetch and merge"});let e=await a();e.ok&&e.url&&(window.location.href=e.url)};return 0===e.behind?(0,nY.jsx)(l1,{update:l,discard:s,comparison:e}):n||r?(0,nY.jsx)(n7.Z,{sx:{p:4,display:"flex",justifyContent:"center"},children:(0,nY.jsx)(ix.Z,{})}):"clean"===t?(0,nY.jsx)(l1,{update:l,discard:s,comparison:e}):(0,nY.jsx)(l8,{discard:s,comparison:e})}try{(t5=l6).displayName||(t5.displayName="FetchPopoverContainer")}catch{}function l5({comparison:e}){let{sendRepoClickEvent:t}=(0,rx.a)();return(0,nY.jsxs)(oS.P,{onOpenChange:n=>n&&t("SYNC_FORK_BUTTON",{category:"Branch Infobar",action:"Open Fetch upstream dropdown",label:"ref_loc:fetch_upstream_dropdown",ahead:e.ahead,behind:e.behind}),children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsxs)(ae.z,{leadingVisual:rs.SyncIcon,children:["Sync fork",(0,nY.jsx)(rd.Z,{icon:rs.TriangleDownIcon,sx:{marginLeft:1}})]})}),(0,nY.jsx)(oS.P.Overlay,{align:"end",sx:{marginTop:2},children:(0,nY.jsx)(l$,{children:(0,nY.jsx)(l6,{comparison:e})})})]})}try{(t9=l5).displayName||(t9.displayName="FetchUpstreamButton")}catch{}function l9({repo:e,pullRequestNumber:t}){return(0,nY.jsxs)(rR.Z,{href:(0,nJ.xR)({repo:e,number:t}),sx:{display:"flex",gap:1,alignItems:"center",color:"fg.muted","&:hover":{color:"accent.fg"}},children:[(0,nY.jsx)(rs.GitPullRequestIcon,{size:16}),"#",t]})}try{(t7=l9).displayName||(t7.displayName="PullRequestLink")}catch{}function l7({sx:e}){let t;let[n,r]=lM(),i=(0,nX.H)();return t="timeout"===r?(0,nY.jsx)(nY.Fragment,{children:"Sorry, getting ahead/behind information for this branch is taking too long."}):n?n.refComparison?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(lq,{linkify:!0,repo:i,comparison:n.refComparison}),(0,nY.jsx)(n7.Z,{sx:{display:"flex",gap:2},children:n.pullRequestNumber?(0,nY.jsx)(l9,{repo:i,pullRequestNumber:n.pullRequestNumber}):(0,nY.jsxs)(nY.Fragment,{children:[i.currentUserCanPush&&(0,nY.jsx)(lY,{comparison:n.refComparison}),i.isFork&&i.currentUserCanPush&&(0,nY.jsx)(l5,{comparison:n.refComparison})]})})]}):(0,nY.jsx)(nY.Fragment,{children:"Cannot retrieve ahead/behind information for this branch."}):(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(o8,{width:"40%"}),(0,nY.jsx)(o8,{width:"30%"})]}),(0,nY.jsx)(ce,{sx:{flexDirection:["column","row"],alignItems:["start","center"],justifyContent:"space-between",border:"solid 1px",borderColor:"border.default",borderRadius:"6px",pl:3,pr:2,py:2,mb:3,...e},children:t})}function ce({children:e,sx:t}){return(0,nY.jsx)(n7.Z,{"data-testid":"branch-info-bar","aria-live":"polite",sx:{display:"flex",gap:2,bg:"canvas.subtle",fontSize:1,...t},children:e})}try{(ne=l7).displayName||(ne.displayName="BranchInfoBar")}catch{}try{(nt=ce).displayName||(nt.displayName="BranchInfoBarContainer")}catch{}var ct=n(34493);function cn(){let{refInfo:e,path:t}=(0,rK.Br)(),n=(0,nX.H)(),[r,i]=(0,n0.useState)({loading:!0}),o=(0,sl.Lr)(),a=(0,nJ.Qi)({repo:n,action:"tree-commit-info",commitish:e.name,path:t});return(0,n0.useEffect)(()=>{let e=!1,t=async()=>{i({loading:!0});let t=await (0,it.v)(a);if(!e)try{t.ok?i({commitInfo:await t.json()}):(o({variant:"warning",message:"Failed to load latest commit information."}),i({error:!0}))}catch(e){i({error:!0})}};return t(),function(){e=!0}},[o,a]),r}function cr(e,t){let n=e.length>t,[r,i]=(0,n0.useState)(n);ci(()=>{r&&i(!1)},[r]);let o=r?e.slice(0,t):e;return{truncated:r,items:o}}function ci(e,t){let n=(0,n0.useCallback)(e,t);(0,n0.useEffect)(()=>{let e=null,t=null;return t=requestAnimationFrame(()=>{e=setTimeout(()=>{n(),e=null},0),t=null}),()=>{e&&clearTimeout(e),t&&cancelAnimationFrame(t)}},[n])}function co({useIcon:e}){let{refInfo:t,path:n}=(0,rK.Br)(),r=(0,nX.H)(),{sendRepoClickEvent:i}=(0,rx.a)();return t.canEdit?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(oK.N,{as:"h2",text:"Add file"}),(0,nY.jsxs)(oS.P,{children:[e?(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsx)(rl.h,{icon:rs.PlusIcon,"aria-label":"Add file"})}):(0,nY.jsx)(oS.P.Button,{children:"Add file"}),(0,nY.jsx)(oS.P.Overlay,{sx:{maxHeight:"55vh",overflowY:"auto"},children:(0,nY.jsxs)(rS.S,{children:[(0,nY.jsxs)(rS.S.LinkItem,{as:iM.r,onClick:()=>i("NEW_FILE_BUTTON"),to:(0,nJ.Qi)({repo:r,path:n,commitish:t.name,action:"new"}),children:[(0,nY.jsx)(rS.S.LeadingVisual,{children:(0,nY.jsx)(rs.PlusIcon,{})}),"Create new file"]}),(0,nY.jsxs)(rS.S.LinkItem,{onClick:()=>i("UPLOAD_FILES_BUTTON"),href:(0,nJ.Qi)({repo:r,path:n,commitish:t.name,action:"upload"}),children:[(0,nY.jsx)(rS.S.LeadingVisual,{children:(0,nY.jsx)(rs.UploadIcon,{})}),"Upload files"]})]})})]})]}):null}try{(nn=co).displayName||(nn.displayName="AddFileDropdownButton")}catch{}var ca=n(96604);function cs({rename:e}){let[t,n]=(0,n0.useState)(!1),r=(0,n0.useRef)(null),i=(0,ro.x)(),o=(0,nX.H)(),a=()=>{if(!i)return;let e=(0,nJ.xv)({login:i.login}),t=new FormData;t.append("_method","delete"),t.append("repository_id",o.id.toString()),t.append("notice_name","repo_default_branch_rename"),(0,it.Q)(e,{method:"POST",body:t}),n(!0)};return(0,nY.jsx)(ca.Z,{open:!t,caret:"top-left",sx:{width:"480px",mt:"6px",ml:1},children:(0,nY.jsxs)(ca.Z.Content,{sx:{width:"480px",color:"fg.default",fontSize:1},children:[(0,nY.jsx)(re.Z,{as:"h4",sx:{fontSize:2,pb:2},children:"The default branch has been renamed!"}),(0,nY.jsxs)(ru.Z,{as:"p",children:[(0,nY.jsx)(s0.Z,{children:e.oldName})," is now named"," ",(0,nY.jsx)(s0.Z,{sx:{backgroundColor:"accent.emphasis",color:"fg.onEmphasis"},children:e.newName})]}),(0,nY.jsxs)(ru.Z,{as:"p",sx:{mb:0},children:["If you have a local clone, you can update it by running the following commands.",e.shellEscapingDocsURL?(0,nY.jsx)(rR.Z,{href:e.shellEscapingDocsURL,children:"Learn about dealing with special characters on the command line."}):null]}),(0,nY.jsx)(s9.m,{textToCopy:r.current?.textContent??"",tooltipProps:{sx:{position:"absolute",right:3,top:"140px"}}}),(0,nY.jsx)(n7.Z,{as:"pre",sx:{py:"20px"},ref:r,children:`git branch -m ${e.shellOldName} ${e.shellNewName}
git fetch origin
git branch -u ${e.shellNewName} ${e.shellNewName}
git remote set-head origin -a`}),(0,nY.jsx)(ae.z,{onClick:a,children:"OK, got it"})]})})}try{(nr=cs).displayName||(nr.displayName="BranchRenamePopover")}catch{}function cl({branchName:e,nameWithOwner:t}){let[n,r]=(0,n0.useState)(!1),i=(0,ro.x)(),o=(0,nX.H)(),a=()=>{if(!i)return;let e=(0,nJ.xv)({login:i.login}),t=new FormData;t.append("_method","delete"),t.append("repository_id",o.id.toString()),t.append("notice_name","repo_parent_default_branch_rename"),(0,it.Q)(e,{method:"POST",body:t}),r(!0)};return(0,nY.jsx)(ca.Z,{open:!n,caret:"top-left",sx:{width:"530px",mt:"6px",ml:1},children:(0,nY.jsxs)(ca.Z.Content,{sx:{width:"530px",color:"fg.default",fontSize:1},children:[(0,nY.jsx)(re.Z,{as:"h4",sx:{fontSize:2,pb:2},children:"The default branch on the parent repository has been renamed!"}),(0,nY.jsxs)(ru.Z,{as:"p",children:[(0,nY.jsx)(s0.Z,{children:t})," renamed its default branch"," ",(0,nY.jsx)(s0.Z,{sx:{backgroundColor:"accent.emphasis",color:"fg.onEmphasis"},children:e})]}),(0,nY.jsxs)(ru.Z,{as:"p",children:["You can rename this fork's default branch to match in"," ",(0,nY.jsx)(rR.Z,{href:(0,nJ.FL)({owner:o.ownerLogin,repo:o.name,action:"settings"}),children:"branch settings"})]}),(0,nY.jsx)(ae.z,{onClick:a,children:"OK, got it"})]})})}try{(ni=cl).displayName||(ni.displayName="ParentBranchRenamePopover")}catch{}let cc={directory:"Directory",submodule:"Submodule",symlink_directory:"Symlink to directory",symlink_file:"Symlink to file"};function cd({contentType:e}){return`(${cc[e]||"File"})`}function cu({item:e}){switch(e.contentType){case"directory":return(0,nY.jsx)(rs.FileDirectoryFillIcon,{className:"icon-directory"});case"submodule":case"symlink_directory":case"symlink_file":return(0,nY.jsx)(rs.FileSubmoduleIcon,{className:"icon-directory"});default:return(0,nY.jsx)(rs.FileIcon,{className:"color-fg-muted"})}}function ch({initialFocus:e,item:t,getItemUrl:n,onNavigate:r,typeIdSuffix:i}){let o=n(t),a=`item-type-${t.name}${i??""}`,s=t.hasSimplifiedPath?"This path skips through empty directories":t.name;return"submodule"===t.contentType&&(o=t.submoduleUrl??o,r=e=>{e.preventDefault(),t.submoduleUrl&&(window.location.href=t.submoduleUrl)}),(0,nY.jsxs)("div",{className:"overflow-hidden",children:[(0,nY.jsx)("h3",{children:(0,nY.jsx)("div",{className:"react-directory-truncate",title:s,"aria-label":t.name,children:(0,nY.jsx)(iM.r,{"aria-describedby":a,className:"symlink_directory"!==t.contentType&&"symlink_file"!==t.contentType?"Link--primary":void 0,"data-react-autofocus":!!e||null,onClick:r,to:o,children:(0,nY.jsx)(cm,{item:t})})})}),(0,nY.jsx)("div",{id:a,className:"sr-only",children:cd(t)})]})}function cm({item:e}){return e.hasSimplifiedPath?(0,nY.jsx)(nY.Fragment,{children:e.name.split("/").map((e,t,n)=>{let r=t===n.length-1;return(0,nY.jsx)("span",{className:r?"":"react-directory-default-color","data-testid":"path-name-segment",children:`${e}${r?"":"/"}`},t)})}):e.submoduleDisplayName?(0,nY.jsx)("span",{style:{color:"var(--color-accent-fg)"},children:e.submoduleDisplayName}):(0,nY.jsx)(nY.Fragment,{children:e.name})}function cf({commit:e}){return e?e.shortMessageHtmlLink?(0,nY.jsx)("div",{children:(0,nY.jsx)(iu.sF,{className:"react-directory-commit-message",html:e.shortMessageHtmlLink})}):(0,nY.jsx)(rR.Z,{className:"Link--secondary",href:e.url,children:"No commit message"}):(0,nY.jsx)(o8,{})}function cp({commit:e}){return e?.date?(0,nY.jsx)("div",{className:"react-directory-commit-age",children:(0,nY.jsx)(iH.Z,{datetime:e.date,tense:"past"})}):(0,nY.jsx)(o8,{})}try{(no=cu).displayName||(no.displayName="IconCell")}catch{}try{(na=ch).displayName||(na.displayName="NameCell")}catch{}try{(ns=cm).displayName||(ns.displayName="ItemPathName")}catch{}try{(nl=cf).displayName||(nl.displayName="CommitMessageCell")}catch{}try{(nc=cp).displayName||(nc.displayName="CommitAgeCell")}catch{}function cx({children:e,sx:t,...n}){return(0,nY.jsx)(n7.Z,{as:"table",sx:{width:"100%",borderCollapse:"separate",borderSpacing:0,border:"1px solid",borderColor:"border.default",borderRadius:"6px",tableLayout:"fixed",overflow:"hidden",...t},...n,children:e})}let cy=({children:e,sx:t})=>(0,nY.jsx)(n7.Z,{as:"thead",sx:{height:"40px",...t},children:(0,nY.jsx)(n7.Z,{as:"tr",sx:{p:3,color:"fg.muted",fontSize:0,textAlign:"left",height:"40px",th:{pl:3,backgroundColor:"canvas.subtle"}},children:e})}),cg=({children:e,onClick:t,index:n,id:r})=>(0,nY.jsx)(n7.Z,{as:"tr",sx:{fontSize:1,height:"40px",td:{pl:3,textAlign:"left",borderTopStyle:"solid",borderTopWidth:1,borderTopColor:"border.default"},"&:hover":{bg:"canvas.subtle"}},onClick:t,"data-index":n,id:r,children:e}),cb=({children:e})=>(0,nY.jsx)(n7.Z,{as:"tfoot",sx:{backgroundColor:"canvas.subtle",borderTopColor:"border.default",p:3},children:e});try{(nd=cx).displayName||(nd.displayName="Table")}catch{}try{(nu=cy).displayName||(nu.displayName="HeaderRow")}catch{}try{(nh=cg).displayName||(nh.displayName="Row")}catch{}try{(nm=cb).displayName||(nm.displayName="TableFooter")}catch{}function cj({initialFocus:e,item:t,commit:n,onNavigate:r,getItemUrl:i,navigate:o,index:a,className:s}){let l=n0.useCallback(e=>{window.innerWidth<n4._G.small&&!e.defaultPrevented&&("submodule"===t.contentType?t.submoduleUrl&&o(t.submoduleUrl):o(i(t)))},[t,i,o]),c=()=>(0,nY.jsxs)("tr",{className:`react-directory-row ${s}`,onClick:l,id:`folder-row-${a}`,children:[(0,nY.jsx)("td",{className:"react-directory-row-name-cell-small-screen",colSpan:2,children:(0,nY.jsxs)("div",{className:"react-directory-filename-column",children:[(0,nY.jsx)(cu,{item:t}),(0,nY.jsx)(ch,{initialFocus:e,item:t,getItemUrl:i,onNavigate:r,typeIdSuffix:"-small"})]})}),(0,nY.jsx)("td",{className:"react-directory-row-name-cell-large-screen",colSpan:1,children:(0,nY.jsxs)("div",{className:"react-directory-filename-column",children:[(0,nY.jsx)(cu,{item:t}),(0,nY.jsx)(ch,{initialFocus:e,item:t,getItemUrl:i,onNavigate:r})]})}),(0,nY.jsx)("td",{className:"react-directory-row-commit-cell",children:(0,nY.jsx)(cf,{commit:n})}),(0,nY.jsx)("td",{children:(0,nY.jsx)(cp,{commit:n})})]});return(0,nY.jsx)(c,{})}let cw=n0.memo(cj);function cv({initialFocus:e,linkTo:t,linkRef:n,navigate:r}){let{setFocusHint:i}=(0,ct.x)(),{path:o}=(0,rK.Br)(),a=n0.useCallback(()=>{let e=window.innerWidth<n4._G.medium;e&&r(t)},[t,r]);return(0,nY.jsx)(cg,{onClick:a,id:"folder-row-0",children:(0,nY.jsxs)(n7.Z,{as:"td",colSpan:3,sx:{fontSize:1,fontWeight:400,px:3},children:[(0,nY.jsx)(oK.N,{as:"h3",text:"parent directory"}),(0,nY.jsx)(rR.Z,{"aria-label":"Parent directory","data-react-autofocus":!!e||null,"data-testid":"up-tree",as:iM.r,muted:!0,onClick:()=>{i(o)},ref:n,rel:"nofollow",sx:{fontWeight:"bold",textDecoration:"none",cursor:"pointer","&:hover":{textDecoration:"none"},"&:focus:focus-visible div":{outline:"2px solid var(--focus-outlineColor, var(--color-accent-fg))",outlineOffset:"-2px"}},to:t,children:(0,nY.jsxs)(n7.Z,{className:"width-full",sx:{width:16,textAlign:"center",letterSpacing:"2px",display:"flex",alignItems:"center"},children:[(0,nY.jsx)(rd.Z,{icon:rs.FileDirectoryFillIcon,size:"small",sx:{color:"var(--color-icon-directory)",mr:"10px"}}),".."]})})]})})}try{(nf=cj).displayName||(nf.displayName="WrappedDirectoryRow")}catch{}try{(np=RowContent).displayName||(np.displayName="RowContent")}catch{}try{(nx=cw).displayName||(nx.displayName="DirectoryRow")}catch{}try{(ny=cv).displayName||(ny.displayName="GoDirectoryUpRow")}catch{}let cN=(0,n0.lazy)(()=>Promise.resolve().then(n.bind(n,36218)));function cC({showTree:e,expandTree:t,overview:n,treeToggleElement:r}){let i=(0,nX.H)(),{refInfo:o,path:a}=(0,rK.Br)(),{items:s,templateDirectorySuggestionUrl:l}=sd(),{items:c}=cr(s,100),[d,u]=n0.useState(!!n),{commitInfo:h}=cn(),m=a.length>1,f=(0,nJ.C2)(a),p=(0,nJ.Qi)({repo:i,action:"tree",commitish:o.name,path:f}),x=n0.useRef(null),{getItemUrl:y}=(0,oY.B)(),g=(0,rX.s)(),{focusHint:b}=(0,ct.x)(),[j,w]=n0.useState(-1),v=n0.useCallback(e=>{0===e.screenX&&0===e.screenY&&x.current?.focus()},[]),N=n0.useCallback(()=>{t?.({focus:"search"})},[t]),C=n0.useCallback(()=>{u(!1)},[]),k=n0.useCallback(e=>{let t;w(e);let n=document.getElementById(`folder-row-${e}`);(t=window.innerWidth<=n4._G.medium?n?.querySelector(".react-directory-row-name-cell-small-screen"):n?.querySelector(".react-directory-row-name-cell-large-screen"))||(t=n),t?.getElementsByTagName("a")[0]?.focus()},[]);return(0,nY.jsxs)(n7.Z,{"data-hpc":!0,children:[(0,nY.jsx)(rA.P,{buttonTestLabel:"focus-next-element-button",buttonFocusId:r1.KG,buttonHotkey:"j",onButtonClick:()=>{let e=Math.min(j+1,m?c.length:c.length-1);k(e)}}),(0,nY.jsx)(rA.P,{buttonTestLabel:"focus-previous-element-button",buttonFocusId:r1.KG,buttonHotkey:"k",onButtonClick:()=>{k(Math.max(j-1,0))}}),(0,nY.jsx)(oK.N,{as:"h2",text:"Folders and files",id:"folders-and-files"}),(0,nY.jsxs)(cx,{"aria-labelledby":"folders-and-files",sx:{overflow:"unset"},children:[(0,nY.jsxs)(cy,{sx:n?{height:"0px",lineHeight:"0px",tr:{height:"0px",fontSize:"0px"}}:void 0,children:[(0,nY.jsx)(n7.Z,{as:"th",sx:{width:"100%","@media screen and (min-width: 544px)":{display:"none"}},colSpan:2,children:(0,nY.jsx)(ru.Z,{sx:{fontWeight:600},children:"Name"})}),(0,nY.jsx)(n7.Z,{as:"th",sx:{width:"40%","@media screen and (max-width: 543px)":{display:"none"}},colSpan:1,children:(0,nY.jsx)(ru.Z,{sx:{fontWeight:600},children:"Name"})}),(0,nY.jsx)(n7.Z,{as:"th",sx:{"@media screen and (max-width: 543px)":{display:"none"}},children:(0,nY.jsx)(ry.Z,{inline:!0,title:"Last commit message",sx:{maxWidth:"100%"},children:(0,nY.jsx)(ru.Z,{sx:{fontWeight:600},children:"Last commit message"})})}),(0,nY.jsx)(n7.Z,{as:"th",sx:{textAlign:"right",pr:3,width:"136px"},colSpan:1,children:(0,nY.jsx)(ry.Z,{inline:!0,title:"Last commit date",sx:{maxWidth:"100%"},children:(0,nY.jsx)(ru.Z,{sx:{fontWeight:600},children:"Last commit date"})})})]}),(0,nY.jsxs)("tbody",{children:[!!n&&(0,nY.jsx)(n7.Z,{as:"tr",sx:{color:"fg.muted",fontSize:0,height:"40px"},children:(0,nY.jsx)(n7.Z,{as:"td",colSpan:3,sx:{backgroundColor:"canvas.subtle",p:2,borderTopLeftRadius:2,borderTopRightRadius:2},children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex"},children:[(0,nY.jsx)(n7.Z,{sx:{mr:1,...e||n?{"@media screen and (max-width: 768px), screen and (min-width: 1441px)":{display:"none"}}:{"@media screen and (max-width: 768px), screen and (min-width: 1476px)":{display:"none"}}},children:r}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",justifyContent:"space-between",flexGrow:1},children:[(0,nY.jsxs)(n7.Z,{sx:{position:"relative"},children:[(0,nY.jsx)(ao,{}),n.popovers.rename?(0,nY.jsx)(cs,{rename:n.popovers.rename}):n.popovers.renamedParentRepo?(0,nY.jsx)(cl,{branchName:n.popovers.renamedParentRepo.branchName,nameWithOwner:n.popovers.renamedParentRepo.nameWithOwner}):null]}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",pl:2},children:[(!e||n)&&(0,nY.jsx)(n7.Z,{sx:{display:"flex","@media screen and (max-width: 768px)":{display:"none"}},children:(0,nY.jsx)(n0.Suspense,{fallback:null,children:(0,nY.jsx)(cN,{})})}),(0,nY.jsx)(n7.Z,{sx:{display:"flex","@media screen and (min-width: 769px)":{display:"none"}},children:(0,nY.jsx)(ae.z,{onClick:N,sx:{mr:2},children:"Go to file"})}),(0,nY.jsx)("div",{className:"react-directory-add-file-icon",children:(0,nY.jsx)(co,{useIcon:!0})}),(0,nY.jsx)("div",{className:"react-directory-remove-file-icon",children:(0,nY.jsx)(co,{useIcon:!1})})]})]})]})})}),m&&(0,nY.jsx)(cv,{initialFocus:!b||!c.some(e=>e.path===b),linkTo:p,linkRef:x,navigate:g}),c.map((e,t)=>(0,nY.jsx)(cw,{initialFocus:e.path===b,item:e,commit:(h||{})[e.name],onNavigate:v,getItemUrl:y,navigate:g,className:d&&t>=10?"truncate-for-mobile":void 0,index:m?t+1:t},e.name)),(0,nY.jsx)(n7.Z,{as:"tr",className:d&&c.length>10?"show-for-mobile":"d-none",sx:{textAlign:"center",verticalAlign:"center",height:"40px",borderTop:"1px solid",borderColor:"border.default"},"data-testid":"view-all-files-row",children:(0,nY.jsx)(n7.Z,{as:"td",colSpan:3,onClick:C,sx:{borderTop:"1px solid var(--borderColor-default, var(--color-border-default))",cursor:"pointer"},children:(0,nY.jsx)("div",{children:(0,nY.jsx)(rR.Z,{as:"button",onClick:C,children:"View all files"})})})})]}),l&&(0,nY.jsx)(cb,{children:(0,nY.jsx)(cg,{children:(0,nY.jsxs)("td",{colSpan:3,children:["Customize the issue creation experience with a ",(0,nY.jsx)("code",{children:"config.yml"})," file."," ",(0,nY.jsx)(rR.Z,{href:l,children:"Learn more about configuring a template chooser."})]})})})]})]})}try{(ng=cN).displayName||(ng.displayName="FileResultsList")}catch{}try{(nb=cC).displayName||(nb.displayName="DirectoryContent")}catch{}function ck({disabled:e,editPath:t,editTooltip:n,loggedIn:r,siteNavLoginPath:i,hidden:o,customSx:a,buttonVariant:s}){let{editFileShortcut:l}=(0,rY.bx)(),c=(0,rX.s)();return(0,nY.jsx)(rl.h,{icon:rs.PencilIcon,sx:{...e?{color:"var(--color-primer-fg-disabled)"}:void 0,...o?{display:"none"}:void 0,...a},"aria-label":n,onClick:()=>{r?!e&&t&&c(t):c(i)},"data-hotkey":l.hotkey,disabled:r&&e,size:"small",title:n,variant:s})}try{(nj=ck).displayName||(nj.displayName="EditButton")}catch{}function cS({openPanel:e,readme:t,setOpenPanel:n,stickyHeaderHeight:r}){let{displayName:i,errorMessage:o,richText:a,headerInfo:s,timedOut:l}=t,{toc:c,siteNavLoginPath:d}=s||{},u=(0,nX.H)(),{refInfo:h,path:m}=(0,rK.Br)(),f=m&&"/"!==m?`${m}/${i}`:i;return(0,nY.jsxs)(n7.Z,{sx:{minWidth:0,display:"flex",flexDirection:"row",justifyContent:"space-between",gap:3},children:[(0,nY.jsxs)(n7.Z,{id:"readme",sx:{borderColor:"border.default",borderWidth:1,borderStyle:"solid",borderRadius:2,width:"toc"===e?"65%":"100%"},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"center",gap:2,pr:2,pl:3,py:2,borderBottom:"1px solid",borderColor:"border.default"},children:[(0,nY.jsx)(re.Z,{as:"h2",sx:{fontSize:1,flexGrow:1},children:(0,nY.jsx)(rR.Z,{sx:{color:"fg.default","&:hover":{color:"accent.fg"}},href:"#readme",children:i})}),h.canEdit&&(0,nY.jsx)(ck,{disabled:!1,editPath:(0,nJ.Qi)({repo:u,commitish:h.name,action:"edit",path:f}),editTooltip:"Edit README",loggedIn:!0,siteNavLoginPath:d,customSx:{color:"var(--color-fg-muted)"},buttonVariant:"invisible"}),(0,nY.jsx)(ak,{toc:c,openPanel:e,setOpenPanel:n,isDirectoryReadme:!0})]}),(0,nY.jsx)(cI,{richText:a,errorMessage:o,path:f,stickyHeaderHeight:r,timedOut:l})]}),"toc"===e&&(0,nY.jsx)(st.s,{sx:{height:"fit-content",width:"35%"},children:(0,nY.jsx)(a7,{onClose:()=>{n(void 0)},toc:c})})]})}function cI({errorMessage:e,onAnchorClick:t,path:n,richText:r,stickyHeaderHeight:i,timedOut:o}){let a=(0,nX.H)(),{refInfo:s}=(0,rK.Br)();return e?(0,nY.jsxs)(n7.Z,{sx:{py:6,px:3,textAlign:"center"},children:[o&&(0,nY.jsx)(rd.Z,{icon:rs.HourglassIcon,size:32}),(0,nY.jsx)(n7.Z,{"data-testid":"directory-richtext-error-message",children:e}),o&&(0,nY.jsxs)(n7.Z,{children:["But you can view the"," ",(0,nY.jsx)(rR.Z,{href:(0,nJ.Qi)({repo:a,commitish:s.name,action:"raw",path:n}),"data-testid":"directory-richtext-timeout-raw-link",children:"raw file"}),"."]})]}):r?(0,nY.jsx)(iN,{onAnchorClick:t,richText:r,stickyHeaderHeight:i,sx:{p:5,overflow:"auto"}}):null}try{(nw=cS).displayName||(nw.displayName="DirectoryReadmePreview")}catch{}try{(nv=cI).displayName||(nv.displayName="DirectoryRichtextContent")}catch{}function cR({uploadUrl:e}){return(0,nY.jsx)("div",{className:"repo-file-upload-tree-target js-document-dropzone js-upload-manifest-tree-view","data-testid":"dragzone","data-drop-url":e,children:(0,nY.jsx)("div",{className:"repo-file-upload-outline",children:(0,nY.jsxs)("div",{className:"repo-file-upload-slate",children:[(0,nY.jsx)(n7.Z,{sx:{color:"fg.muted"},children:(0,nY.jsx)(rs.FileIcon,{size:32})}),(0,nY.jsx)("h2",{"aria-hidden":"true",children:"Drop to upload your files"})]})})})}try{(nN=cR).displayName||(nN.displayName="Dropzone")}catch{}function cZ({showTree:e,treeToggleElement:t}){let n=(0,n0.useRef)(null),r=(0,ie.X)(n),i=(0,ie.V)();return(0,nY.jsx)(n7.Z,{className:"react-blob-view-header-sticky",sx:{...i,zIndex:r?1:0},ref:n,children:(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexDirection:"column",backgroundColor:"canvas.subtle",borderBottom:r?"1px solid var(--color-border-default)":"none",overflow:"hidden"},children:(0,nY.jsx)(aa,{isStickied:r,showTree:e,treeToggleElement:t})})})}try{(nC=cZ).displayName||(nC.displayName="FolderViewHeader")}catch{}var cE=n(21921);function cT({readme:e,coc:t,licenses:n,securityPolicy:r,shouldRecommendReadme:i,isPersonalRepo:o,processingTime:a}){let s,l,c="readme-ov-file";e||i||(t?c="coc-ov-file":n[0]?c=`${n[0].tabName}-1-ov-file`:r&&(c="security-ov-file"));let[d,u]=(0,n0.useState)(c),h=(0,n0.useRef)(null),m=(0,nX.H)(),{refInfo:f}=(0,rK.Br)();(0,n0.useEffect)(()=>{(e||t||n.length||r)&&(0,rP.qP)("overview-repo-files",{"file-count":[e,t,...n,r].filter(e=>void 0!==e).length,"timed-out":e?.timedOut||t?.timedOut||n.some(e=>e.timedOut)||r?.timedOut,"processing-time":a})},[t,n,a,e,r]);let p=(0,n0.useCallback)(()=>{let a=[];if((e||i)&&a.push("readme-ov-file"),(t||i&&!o)&&a.push("coc-ov-file"),n.length)for(let[e,t]of n.entries())a.push(`${t.tabName}-${e+1}-ov-file`);else i&&!o&&a.push("license-ov-file");return(r||i&&!o)&&a.push("security-ov-file"),a},[t,o,n,e,r,i]);(0,n0.useLayoutEffect)(()=>{if(!e&&!t&&!n.length&&!r)return;let i=()=>{let e=window.location.hash.replace("#",""),t=p();if(e&&t.includes(e)){let t=`${window.location.protocol}//${window.location.host}${window.location.pathname}?tab=${e}#readme`;history.replaceState(null,"",t),u(e)}else{let e=new URLSearchParams(window.location.search),n=e.get("tab");n&&t.includes(n)&&u(n)}window.requestAnimationFrame(()=>{h.current&&"#readme"===window.location.hash&&h.current.scrollIntoView()})};return i(),window.addEventListener("hashchange",i),()=>{window.removeEventListener("hashchange",i)}},[]);let x=(0,n0.useCallback)((e,t)=>{if(e.preventDefault(),d===t)return;u(t);let n=`${window.location.protocol}//${window.location.host}${window.location.pathname}?tab=${t}`;history.replaceState(null,"",n)},[d]);if(!e&&!t&&!n.length&&!r&&!i)return null;let y=n.find((e,t)=>`${e.tabName}-${t+1}-ov-file`===d);return"readme-ov-file"===d?e?l=e:i&&(s=(0,nY.jsx)(cL,{title:"Add a README",description:o?"Add a README with an overview of your project.":"Help people interested in this repository understand your project by adding a README.",icon:rs.BookIcon,buttonText:"Add a README",href:`${(0,nJ.Qi)({repo:m,path:void 0,commitish:f.name,action:"new"})}?filename=README.md`})):"coc-ov-file"===d?t?l=t:i&&!o&&(s=(0,nY.jsx)(cL,{title:"Add a code of conduct",description:"Define community standards, signal a welcoming and inclusive project, and outline procedures for handling abuse by adding a code of conduct.",icon:rs.CodeOfConductIcon,buttonText:"Add a code of conduct",href:(0,nJ.DG)(m.ownerLogin,m.name)})):y?l=y:"license-ov-file"===d&&i&&!o?s=(0,nY.jsx)(cL,{title:"Add a license",description:"Add a license to your repository to make it clear how others can use, change, and contribute to your project.",icon:rs.LawIcon,buttonText:"Add a license",href:(0,nJ.GO)(m.ownerLogin,m.name)}):r?l=r:i&&!o&&(s=(0,nY.jsx)(cL,{title:"Add a security policy",description:"Help your community understand how to securely report security vulnerabilities for your project.",icon:rs.LawIcon,buttonText:"Add a security policy",href:(0,nJ.vf)(m.ownerLogin,m.name)})),l&&l.path&&(s=(0,nY.jsx)(cI,{errorMessage:l.errorMessage,onAnchorClick:()=>{let e=`${window.location.protocol}//${window.location.host}${window.location.pathname}?tab=${d}${window.location.hash}`;history.replaceState(null,"",e)},path:l.path,richText:l.richText,stickyHeaderHeight:50,timedOut:l.timedOut})),(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexGrow:1,gap:3},children:(0,nY.jsxs)(n7.Z,{sx:{border:"1px solid",borderColor:"border.default",borderRadius:"6px",display:"flex",flexDirection:"column",flexGrow:1,"@media screen and (max-width: 543px)":{mx:"-16px",maxWidth:"calc(100% + 32px)"},"@media screen and (min-width: 544px)":{maxWidth:"100%"}},ref:h,children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",borderBottom:"1px solid",borderBottomColor:"border.default",alignItems:"center",pr:2,position:"sticky",top:0,backgroundColor:"canvas.default",zIndex:1,borderTopLeftRadius:"6px",borderTopRightRadius:"6px"},children:[(0,nY.jsxs)(cE.J,{"aria-label":"Repository files",sx:{flexGrow:1,borderBottom:"none",maxWidth:"100%",px:2},children:[(e||i)&&(0,nY.jsx)(cE.J.Item,{icon:rs.BookIcon,"aria-current":"readme-ov-file"===d?"page":void 0,onSelect:e=>x(e,"readme-ov-file"),children:e?.tabName||"README"}),(t||i&&!o)&&(0,nY.jsx)(cE.J.Item,{icon:rs.CodeOfConductIcon,"aria-current":"coc-ov-file"===d?"page":void 0,onSelect:e=>x(e,"coc-ov-file"),children:t?.tabName||"Code of conduct"}),n.length?n.map((e,t)=>{let n=`${e.tabName}-${t+1}-ov-file`;return(0,nY.jsx)(cE.J.Item,{icon:rs.LawIcon,"aria-current":d===n?"page":void 0,onSelect:e=>x(e,n),children:"license"!==e.tabName.toLowerCase()?`${e.tabName} license`:"License"},e.path)}):i&&!o?(0,nY.jsx)(cE.J.Item,{icon:rs.LawIcon,"aria-current":"license-ov-file"===d?"page":void 0,onSelect:e=>x(e,"license-ov-file"),children:"License"}):null,(r||i&&!o)&&(0,nY.jsx)(cE.J.Item,{icon:rs.LawIcon,"aria-current":"security-ov-file"===d?"page":void 0,onSelect:e=>x(e,"security-ov-file"),children:r?.tabName||"Security policy"})]}),f.canEdit&&l&&(0,nY.jsx)(ck,{disabled:!1,editPath:(0,nJ.ti)({owner:m.ownerLogin,repo:l.repoName,commitish:l.refName,filePath:l.path}),editTooltip:"Edit README",loggedIn:!0,siteNavLoginPath:"",customSx:{mr:2,color:"var(--color-fg-muted)",height:"28px"},buttonVariant:"invisible"}),l?.headerInfo?.toc&&l?.headerInfo?.toc?.length>=2&&(0,nY.jsxs)(oS.P,{children:[(0,nY.jsx)(oS.P.Button,{icon:rs.ListUnorderedIcon,variant:"invisible","aria-label":"Outline",sx:{color:"fg.subtle",px:2},children:"Outline"}),(0,nY.jsx)(oS.P.Overlay,{align:"end",sx:{minWidth:"256px"},children:(0,nY.jsx)(a7,{toc:l.headerInfo.toc})})]})]}),s]})})}function cL({title:e,description:t,icon:n,buttonText:r,href:i}){return(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",py:5,px:3},children:[(0,nY.jsx)(rd.Z,{icon:n,size:32,sx:{color:"fg.subtle",mb:3}}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",gap:1,mb:5},children:[(0,nY.jsx)(re.Z,{as:"h2",sx:{fontSize:4},children:e}),(0,nY.jsx)(ru.Z,{sx:{fontSize:0,color:"fg.subtle",textAlign:"center"},children:t})]}),(0,nY.jsx)(ae.z,{as:iM.r,to:i,variant:"primary",reloadDocument:!0,children:r})]})}try{(nk=cT).displayName||(nk.displayName="OverviewFiles")}catch{}try{(nS=cL).displayName||(nS.displayName="MissingContent")}catch{}function cB({tree:e,overview:t,showTree:n,treeToggleElement:r,expandTree:i}){let o=(0,nX.H)(),{refInfo:a,path:s}=(0,rK.Br)(),l=(0,nJ.Qi)({repo:o,commitish:a.name,path:s,action:"upload"}),{openPanel:c,setOpenPanel:d}=rU(),[u,h]=(0,n0.useState)(t?.hideRepoFiles),m=(0,n0.useCallback)(()=>{h(!1)},[]),f=t?t.overviewFiles.find(e=>e.preferredFileType===nQ.kl.README):e.readme,p=t?.overviewFiles.find(e=>e.preferredFileType===nQ.kl.CODE_OF_CONDUCT),x=t?.overviewFiles.filter(e=>e.preferredFileType===nQ.kl.LICENSE),y=t?.overviewFiles.find(e=>e.preferredFileType===nQ.kl.SECURITY);return(0,nY.jsxs)(su,{payload:e,children:[(0,nY.jsx)(aO,{commitCount:t?.commitCount}),e.showBranchInfobar&&(0,nY.jsx)(lA.S,{fallback:(0,nY.jsx)(c_,{}),children:(0,nY.jsx)(l7,{})}),!t&&(0,nY.jsx)(cZ,{showTree:n,treeToggleElement:r}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",gap:3},children:[u?(0,nY.jsx)(rR.Z,{onClick:m,children:"View stack template source"}):(0,nY.jsx)(n7.Z,{children:(0,nY.jsx)(cC,{overview:t,showTree:n,expandTree:i,treeToggleElement:r})}),t&&(0,nY.jsx)(cT,{readme:f,coc:p,licenses:x??[],securityPolicy:y,shouldRecommendReadme:t.banners.shouldRecommendReadme,isPersonalRepo:t.banners.isPersonalRepo,processingTime:t.overviewFilesProcessingTime}),f&&!t&&(0,nY.jsx)(cS,{openPanel:c,setOpenPanel:d,readme:f,stickyHeaderHeight:50}),t&&!f&&t.banners.shouldRecommendReadme&&(0,nY.jsxs)(ss.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between"},className:"file-tree-view-readme-flash-narrow",children:[(0,nY.jsx)(ru.Z,{children:t.banners.isPersonalRepo?"Add a README with an overview of your project.":"Help people interested in this repository understand your project by adding a README."}),(0,nY.jsx)(ae.z,{as:iM.r,to:`${(0,nJ.Qi)({repo:o,path:void 0,commitish:a.name,action:"new"})}?filename=README.md`,reloadDocument:!0,children:"Add a README"})]}),o.currentUserCanPush&&(0,nY.jsx)(cR,{uploadUrl:l})]})]})}function c_(){return(0,nY.jsx)(ss.Z,{variant:"warning",sx:{my:3},children:(0,nY.jsx)(ru.Z,{children:"Cannot retrieve comparison with upstream repository."})})}try{(nI=cB).displayName||(nI.displayName="FileTreeViewContent")}catch{}try{(nR=c_).displayName||(nR.displayName="BranchInfoBarErrorBanner")}catch{}var cD=n(12470);function cF(){let e=(0,rq.G)();return e.workflowRedirectUrl}function cO({onBlamePage:e,onDismiss:t,maxLineNumber:n}){let r=(0,rX.s)(),i=(0,ii.nj)(),o=(0,ii.Tw)(),a=(0,ii.i$)(),s=(0,n0.useRef)(i?o:1),l=(0,n0.useRef)(!0),[c,d]=(0,n0.useState)(!0),u=(0,n0.useRef)(o!==a&&i?a:null),{refInfo:h,path:m}=(0,rK.Br)(),f=(0,nX.H)(),p=(0,n0.useRef)(null),x=(0,n0.useRef)(s.current?`#L${s.current}${u.current?`-L${u.current}`:""}`:""),y=e?(0,nJ.t4)({repo:f.name,owner:f.ownerLogin,filePath:m,commitish:h.name}):(0,nJ.C9)({repo:f.name,owner:f.ownerLogin,filePath:m,commitish:h.name});(0,n0.useEffect)(()=>{p&&p.current&&(p.current.value=i?`${o}${a!==o?`-${a}`:""}`:"1",p.current.focus())},[]);let g=e=>{let n=(0,r0.n6)(e);if(!n.blobRange?.start?.line||!l.current){d(l.current),setTimeout(()=>{p.current?.focus()},25);return}(0,r2.v)({line:n.blobRange.start.line}),t()};return(0,og.createPortal)((0,nY.jsx)(cD.Z,{isOpen:!0,onDismiss:t,children:(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsx)(cD.Z.Header,{children:"Jump to line"}),(0,nY.jsxs)(n7.Z,{sx:{display:"flex",pl:3,pr:3,pt:3,pb:c?3:0},children:[(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexGrow:1,mr:2},children:(0,nY.jsx)(rc.Z,{ref:p,"aria-invalid":!c,"aria-describedby":c?"":"goToLineErrorValidation",sx:{flexGrow:1,pr:2},placeholder:"Jump to line...",onChange:e=>{let t=e.target.value;p&&p.current&&(p.current.value=t),function(e){let t=!0;if(""===e.trim()&&(s.current=1),e.startsWith("-")&&n){let r=parseInt(e,10);if(!Number.isNaN(r)&&r<0){let e=n+r+1;e<=0&&(e=1,t=!1),s.current=e}}else if(e.includes("-")){let[r,i]=e.split("-"),o=parseInt(r,10),a=parseInt(i,10);!Number.isNaN(o)&&o>0&&(s.current=n?Math.min(o,n):o,t=void 0!==n&&o<=n),!Number.isNaN(a)&&a>0&&(u.current=n?Math.min(a,n):a,t=t&&void 0!==n&&a<=n)}else{let r=parseInt(e,10);!Number.isNaN(r)&&r>0?(s.current=n?Math.min(r,n):r,t=void 0!==n&&r<=n):t=""===e}l.current=t,t&&!c&&d(!0),x.current=`#L${s.current}${u.current?`-L${u.current}`:""}`}(t)},onFocus:()=>{p&&p.current&&p.current.select()},onKeyDown:e=>{if("Enter"===e.key){if("Enter"===e.key&&!l.current){d(l.current),setTimeout(()=>{p.current?.focus()},25);return}r(y+x.current),g(x.current)}}})}),(0,nY.jsx)(oq.Q,{href:c?y+x.current:void 0,onClick:()=>g(x.current),sx:oQ.A,children:"Go"})]}),!c&&(0,nY.jsx)(n7.Z,{role:"alert",id:"goToLineErrorValidation",sx:{display:"flex",p:2,justifyContent:"center",color:"red"},children:"Invalid line number"})]})}),document.body)}try{(nZ=cO).displayName||(nZ.displayName="GoToLineDialog")}catch{}function cP(){let{refInfo:e}=(0,rK.Br)();return e.canEdit?(0,nY.jsxs)(oS.P,{children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsx)(rl.h,{icon:rs.KebabHorizontalIcon,"aria-label":"More folder actions",title:"More folder actions"})}),(0,nY.jsx)(oS.P.Overlay,{children:(0,nY.jsx)(rS.S,{children:(0,nY.jsx)(cA,{})})})]}):null}function cA(){let e=(0,nX.H)(),{path:t,refInfo:n}=(0,rK.Br)();return n.canEdit?(0,nY.jsx)(rS.S.LinkItem,{as:iM.r,to:(0,nJ.Qi)({repo:e,path:t,commitish:n.name,action:"tree/delete"}),children:(0,nY.jsx)(ru.Z,{sx:{color:"danger.fg"},children:"Delete directory"})}):null}try{(nE=cP).displayName||(nE.displayName="TreeOverflowMenu")}catch{}try{(nT=cA).displayName||(nT.displayName="DeleteDirectoryItem")}catch{}let cM=(0,n0.lazy)(()=>Promise.all([n.e("app_assets_modules_react-code-view_components_blob-edit_WebCommitDialog_tsx"),n.e("node_modules_github_text-expander-element_dist_index_js")]).then(n.bind(n,20925)));function cH({webCommitInfo:e,isBlob:t}){let[n,r]=(0,n0.useState)("closed"),i=(0,n0.useRef)(null),o=(0,nX.H)(),{refInfo:a,path:s}=(0,rK.Br)(),{helpUrl:l}=(0,rK.Ou)(),c=(0,nJ.Qi)({repo:o,commitish:a.name,action:t?"blob":"tree",path:s});return e.shouldFork||e.shouldUpdate||e.lockedOnMigration?null:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",gap:2},children:[(0,nY.jsx)(ae.z,{as:iM.r,to:c,children:"Cancel changes"}),(0,nY.jsx)(ae.z,{variant:"primary",ref:i,onClick:()=>{(0,n0.startTransition)(()=>{r("pending")})},children:"Commit changes..."}),("pending"===n||"saving"===n)&&(0,nY.jsx)(n0.Suspense,{fallback:null,children:(0,nY.jsx)(cM,{isNewFile:!1,isDelete:!0,helpUrl:l,ownerName:o.ownerLogin,dialogState:n,setDialogState:r,refName:a.name,placeholderMessage:`Delete ${s}${t?"":" directory"}`,webCommitInfo:e,returnFocusRef:i})})]})}try{(nL=cM).displayName||(nL.displayName="WebCommitDialog")}catch{}try{(nB=cH).displayName||(nB.displayName="DeleteHeaderButtons")}catch{}var c$=n(2048);function cW({fileReference:e}){return(0,nY.jsx)(nY.Fragment,{children:(0,nY.jsx)(rS.S.Item,{onClick:()=>(0,aU.o)(e,!0),children:"Ask about this file"})})}try{(n_=cW).displayName||(n_.displayName="CopilotMenuItems")}catch{}let cz=(0,rM.Z)("localStorage");function cU(){let{codeFoldingOption:e,codeWrappingOption:t,codeCenterOption:n,openSymbolsOption:r}=(0,n6.O)();return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(cG,{option:e}),(0,nY.jsx)(cG,{option:t}),(0,nY.jsx)(cG,{option:n}),(0,nY.jsx)(cG,{option:r})]})}function cG({option:e}){let t=(0,n0.useCallback)(()=>{e.setEnabled(!e.enabled),cz.setItem(e.name,String(!e.enabled))},[e]);return(0,nY.jsxs)(rS.S.Item,{onSelect:t,children:[(0,nY.jsx)(rS.S.LeadingVisual,{children:e.enabled&&(0,nY.jsx)(rs.CheckIcon,{})}),e.label]},e.name)}try{(nD=cU).displayName||(nD.displayName="SettingsMenuItems")}catch{}try{(nF=cG).displayName||(nF.displayName="OptionsElement")}catch{}function cq({onCopy:e,validCodeNav:t,narrow:n}){let r=(0,rq.G)(),{action:i,path:o,refInfo:{canEdit:a,currentOid:s,name:l,refType:c}}=(0,rK.Br)(),{githubDevUrl:d}=(0,rK.Ou)(),{sendRepoClickEvent:u}=(0,rx.a)(),[h,m]=(0,n0.useState)(!1),f=cF(),{search:p}=(0,ih.TH)(),x=new URLSearchParams(p),{setFindInFileOpen:g}=is(),b=(0,ii.nj)(),j=!(r.richText&&"1"!==x.get("plain")||r.renderImageOrRaw||r.renderedFileInfo&&!x.get("short_path")||r.issueTemplate?.structured&&r.issueTemplate.valid),{headerInfo:{deleteInfo:{deleteTooltip:w},onBranch:v,siteNavLoginPath:N,lineInfo:{truncatedLoc:C}},loggedIn:k,viewable:S}=r,{getUrl:I}=(0,oY.B)(),R=il(),Z=(0,n0.useRef)("");(0,n0.useEffect)(()=>{Z.current=I({action:"blame"})},[I]);let{goToLineShortcut:E,findInFileShortcut:T,alternativeGoToLineShortcut:L}=(0,rY.bx)(),B=(0,n0.useRef)(null),[_,D,F]=(0,oV.a)("raw-copy-message-tooltip",B,{direction:"nw"}),O=(0,c$.y)("copilot_conversational_ux"),P=(0,nX.H)(),A=I(),[M]=(0,iS.D)(()=>window.location.origin+A,A),H=(0,n0.useMemo)(()=>({type:"file",url:M,path:o,repoID:P.id,repoOwner:P.ownerLogin,repoName:P.name,ref:cY(l,c??"branch"),commitOID:s}),[M,o,P.id,P.ownerLogin,P.name,l,c,s]);return(0,nY.jsxs)(nY.Fragment,{children:[j&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:E.hotkey,onButtonClick:()=>m(!0)}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:L.hotkey,onButtonClick:()=>m(!0)})]}),(0,nY.jsx)(cV,{blameUrl:Z.current,viewable:S,hidden:!0}),F,(0,nY.jsxs)(oS.P,{onOpenChange:e=>e&&u("MORE_OPTIONS_DROPDOWN",{edit_enabled:a,github_dev_enabled:!!d}),anchorRef:B,children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsx)(rl.h,{icon:rs.KebabHorizontalIcon,"aria-label":"More file actions",className:"js-blob-dropdown-click",size:"medium",sx:{color:"fg.muted"},title:"More file actions",variant:"default","data-testid":"more-file-actions-button",onBlur:D})}),(0,nY.jsx)(oS.P.Overlay,{width:"small",sx:{maxHeight:"55vh",overflowY:"auto"},children:(0,nY.jsxs)(rS.S,{children:[n&&null!==f&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rS.S.LinkItem,{href:f,children:"View Runs"}),(0,nY.jsx)(rS.S.Divider,{})]}),(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(aw,{viewable:S,onCopy:e,name:r.displayName,updateTooltipMessage:_}),(0,nY.jsx)(rS.S.Divider,{})]}),(0,nY.jsxs)(rS.S.Group,{children:[j&&(0,nY.jsxs)(rS.S.Item,{onSelect:()=>{u("MORE_OPTIONS_DROPDOWN.GO_TO_LINE"),m(!0)},"aria-keyshortcuts":E.hotkey,children:["Jump to line",(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(rf.I,{children:(0,nY.jsx)("kbd",{children:E.text})})})]}),R===y.Code&&null!==r.rawLines&&t&&!b&&(0,nY.jsxs)(rS.S.Item,{onSelect:()=>{u("BLOB_FIND_IN_FILE_MENU.OPEN"),g(!0)},"aria-keyshortcuts":T.ariaKeyShortcuts,children:["Find in file",(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(aj,{shortcut:T})})]})]}),(j||R===y.Code&&null!==r.rawLines&&t)&&(0,nY.jsx)(rS.S.Divider,{}),(0,nY.jsx)(c4,{path:o,updateTooltipMessage:_}),(0,nY.jsx)(rS.S.Divider,{}),O&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rS.S.Group,{title:"Copilot",children:(0,nY.jsx)(cW,{fileReference:H})}),(0,nY.jsx)(rS.S.Divider,{})]}),(0,nY.jsx)(rS.S.Group,{title:"View options",children:(0,nY.jsx)(cU,{})}),(a&&v||!S&&v)&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(rS.S.Divider,{}),(0,nY.jsx)(cK,{deleteTooltip:w,loggedIn:k,siteNavLoginPath:N})]})]})})]}),h&&(0,nY.jsx)(cO,{onBlamePage:"blame"===i,onDismiss:()=>{m(!1),setTimeout(()=>{let e=document.getElementById(r1.KG);e?.focus()},0)},maxLineNumber:parseInt(C,10)??void 0})]})}function cV({blameUrl:e,viewable:t,hidden:n}){let{hash:r}=(0,ih.TH)(),i=(0,rJ.gs)(e+r),{viewBlameShortcut:o}=(0,rY.bx)();return t?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(ae.z,{"data-hotkey":o.hotkey,sx:{borderTopLeftRadius:0,borderBottomLeftRadius:0,...n?{display:"none"}:void 0},onClick:i,children:"Blame"}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:o.hotkey,onButtonClick:i,onlyAddHotkeyScopeButton:!0})]}):null}function cK({deleteTooltip:e,loggedIn:t,siteNavLoginPath:n}){let{getUrl:r}=(0,oY.B)();return(0,nY.jsx)(rS.S.LinkItem,{as:iM.r,sx:{padding:2,color:"danger.fg",":hover":{color:"danger.fg"}},"aria-label":e,to:t?r({action:"delete"}):n,children:"Delete file"})}let cY=(e,t)=>"branch"===t?`refs/heads/${e}`:"tag"===t?`refs/tags/${e}`:e;try{(nO=cq).displayName||(nO.displayName="NavigationMenu")}catch{}try{(nP=cV).displayName||(nP.displayName="BlameButton")}catch{}try{(nA=cK).displayName||(nA.displayName="DeleteActionItem")}catch{}var cQ=n(20684),cX=n(59050);function cJ({channel:e,repo:t}){let[n,r]=(0,n0.useState)([]),i=(0,n0.useCallback)(async()=>{let e=await (0,it.v)(`/${t.ownerLogin}/${t.name}/recently-touched-branches`);e.ok&&r(await e.json())},[t.name,t.ownerLogin]),o=(0,n0.useRef)(null),a=(0,n0.useCallback)(()=>{null!==o.current&&window.clearTimeout(o.current),o.current=window.setTimeout(()=>{i()},500)},[i]);return(0,cQ.o)(e,a),(0,n0.useEffect)(()=>{i()},[i]),(0,nY.jsx)(nY.Fragment,{children:n?.map((e,r)=>(0,nY.jsxs)(ss.Z,{variant:"warning",sx:{display:"flex",alignItems:"center",justifyContent:"space-between",mb:r===n.length-1?3:2},children:[(0,nY.jsxs)(n7.Z,{sx:{mr:1,overflowWrap:"anywhere","a:not(:hover)":{color:"inherit"}},children:[(0,nY.jsx)(rd.Z,{icon:rs.GitBranchIcon,sx:{mr:1}}),(0,nY.jsxs)(iM.r,{to:(0,nJ.v5)({owner:e.repoOwner,repo:e.repoName,branch:e.branchName}),reloadDocument:!0,style:{fontWeight:"bold"},children:[t.ownerLogin!==e.repoOwner?`${e.repoOwner}:`:"",e.branchName]}),` had recent pushes ${(0,cX.C)(new Date(e.date))}`]}),(0,nY.jsx)(oq.Q,{href:e.comparePath,variant:"primary",children:"Compare & pull request"})]},e.branchName+r))})}try{(nM=cJ).displayName||(nM.displayName="RecentlyTouchedBranches")}catch{}function c0({payload:e,showTree:t,treeToggleElement:n,validCodeNav:r}){let i=ij(),o=(0,nQ.uF)(e),{codeCenterOption:a}=(0,n6.O)(),{githubDevUrl:s}=(0,rK.Ou)(),{openWithGitHubDevShortcut:l,openWithGitHubDevInNewWindowShortcut:c}=(0,rY.bx)(),d=!t&&(0,nY.jsx)(lg.default,{useOverlay:!0,sx:{mr:1,ml:1}});return o?(0,nY.jsx)(c2,{showTree:t,payload:e}):(0,nY.jsxs)(n7.Z,{className:"container",sx:{width:"100%"},children:[(0,nY.jsx)(c1,{showTree:t,treeToggleElement:n,payload:e,validCodeNav:r}),(0,nY.jsx)(n7.Z,{sx:{p:3,pb:0,px:3},id:"StickyHeader",className:"react-code-view-header--wide",children:(0,nY.jsx)(n7.Z,{sx:{display:"flex",gap:2,flexDirection:"column",width:"100%"},children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"start",justifyContent:"space-between",gap:2},children:[t?(0,nY.jsx)(n7.Z,{sx:{alignSelf:"center",display:"flex",pr:2,minWidth:0},children:(0,nY.jsx)(an,{id:"repos-header-breadcrumb-wide",fileNameId:"file-name-id-wide",showCopyPathButton:!0})}):(0,nY.jsxs)(n7.Z,{sx:{display:"flex",alignItems:"start",minWidth:0},children:[(0,nY.jsx)(n7.Z,{sx:{display:"block","@media screen and (min-width: 1360px)":{display:a.enabled?"none":"block"},mr:2},children:n}),(0,nY.jsx)(n7.Z,{sx:{mr:2},children:(0,nY.jsx)(ao,{buttonClassName:"ref-selector-class",idEnding:"repos-header-ref-selector-wide"})}),(0,nY.jsx)(n7.Z,{sx:{alignSelf:"center",display:"flex",px:2,minWidth:0},children:(0,nY.jsx)(an,{id:"repos-header-breadcrumb-wide",fileNameId:"file-name-id-wide",showCopyPathButton:!0})})]}),(0,nY.jsx)(n7.Z,{sx:{minHeight:"32px",display:"flex",alignItems:"start"},children:(0,nY.jsxs)("div",{className:"d-flex gap-2",children:[(0,nQ.Kg)(e)&&(0,nY.jsxs)(rq.d,{blob:e.blob,children:[(0,nY.jsx)(c8,{}),d,(0,nY.jsx)(cq,{onCopy:i,validCodeNav:r})]}),(0,nQ.g6)(e)&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(oK.N,{as:"h2",text:"Directory actions"}),d,(0,nY.jsx)(co,{}),(0,nY.jsx)(c3,{}),(0,nY.jsx)(rR.Z,{className:"js-github-dev-shortcut d-none","data-hotkey":l.hotkey,href:s}),(0,nY.jsx)(rR.Z,{className:"js-github-dev-new-tab-shortcut d-none","data-hotkey":c.hotkey,href:s,target:"_blank"})]}),(0,nQ.XU)(e)&&(0,nY.jsx)(cH,{webCommitInfo:e.webCommitInfo,isBlob:e.deleteInfo.isBlob})]})})]})})})]})}function c1({showTree:e,treeToggleElement:t,payload:n,validCodeNav:r}){let i=ij(),[o]=(0,iS.D)(()=>!1,!0,[]);return(0,nY.jsx)(n7.Z,{sx:{p:3,pb:0},className:"react-code-view-header--narrow",children:(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"column",justifyContent:"space-between",gap:3,width:"100%"},children:[(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"start",justifyContent:"space-between"},children:[e&&!o?(0,nY.jsx)(n7.Z,{sx:{alignSelf:"center",display:"flex",minWidth:0},children:(0,nY.jsx)(an,{id:"repos-header-breadcrumb-mobile",fileNameId:"file-name-id-mobile",showCopyPathButton:!0})}):t,(0,nY.jsxs)(n7.Z,{sx:{display:"flex",flexDirection:"row",alignItems:"start",justifyContent:"space-between",justifySelf:"flex-end"},children:[(!e||o)&&(0,nY.jsx)(n7.Z,{sx:{mx:2},children:(0,nY.jsx)(ao,{buttonClassName:"ref-selector-class",idEnding:"repos-header-ref-selector-narrow"})}),(0,nQ.Kg)(n)?(0,nY.jsx)(rq.d,{blob:n.blob,children:(0,nY.jsx)(cq,{onCopy:i,validCodeNav:r,narrow:!0})}):null,(0,nQ.g6)(n)?(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(oK.N,{as:"h2",text:"Directory actions"}),(0,nY.jsx)(c3,{narrow:!0})]}):(0,nQ.XU)(n)?(0,nY.jsx)(cH,{webCommitInfo:n.webCommitInfo,isBlob:n.deleteInfo.isBlob}):null]})]}),(!e||o)&&(0,nY.jsx)(n7.Z,{sx:{justifySelf:"end",maxWidth:"100%"},children:(0,nY.jsx)(an,{id:"repos-header-breadcrumb-mobile",fileNameId:"file-name-id-mobile",showCopyPathButton:!0})})]})})}function c2({showTree:e,payload:t}){let n=(0,nX.H)(),{banners:r}=t.overview;return null!==r.recentlyTouchedDataChannel?(0,nY.jsx)(n7.Z,{sx:{display:"flex",flexDirection:"column",justifyContent:"space-between",pl:e?3:0},children:(0,nY.jsx)(cJ,{channel:r.recentlyTouchedDataChannel,repo:n})}):null}function c3({narrow:e}){let{refInfo:t,path:n}=(0,rK.Br)(),r=(0,nX.H)(),{sendRepoClickEvent:i}=(0,rx.a)(),{addToast:o}=(0,oU.V6)(),{createPermalink:a}=(0,oY.B)(),{copyFilePathShortcut:s,copyPermalinkShortcut:l}=(0,rY.bx)(),{codeCenterOption:c}=(0,n6.O)(),d=(0,n0.useRef)(null),[u,h,m]=(0,oV.a)("raw-copy-message-tooltip",d,{direction:"nw"});return(0,nY.jsxs)(nY.Fragment,{children:[s.hotkey&&(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:s.hotkey,onButtonClick:()=>{(0,ip.z)(n),o({type:"success",message:"Path copied!"})}}),l.hotkey&&(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:l.hotkey,onButtonClick:()=>{(0,ip.z)(a({absolute:!0})),o({type:"success",message:"Permalink copied!"})}}),(0,nY.jsx)(oK.N,{as:"h2",text:"More options"}),m,(0,nY.jsxs)(oS.P,{onOpenChange:e=>e&&i("MORE_OPTIONS_DROPDOWN"),anchorRef:d,children:[(0,nY.jsx)(oS.P.Anchor,{children:(0,nY.jsx)(rl.h,{icon:rs.KebabHorizontalIcon,"aria-label":"More options",size:"medium",sx:{color:"fg.muted"},title:"More options","data-testid":"tree-overflow-menu-anchor",onBlur:h})}),(0,nY.jsx)(oS.P.Overlay,{width:"small",children:(0,nY.jsxs)(rS.S,{children:[e&&t.canEdit&&(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(rS.S.LinkItem,{as:iM.r,onClick:()=>i("NEW_FILE_BUTTON"),to:(0,nJ.Qi)({repo:r,path:n,commitish:t.name,action:"new"}),children:[(0,nY.jsx)(rS.S.LeadingVisual,{children:(0,nY.jsx)(rs.PlusIcon,{})}),"Create new file"]}),(0,nY.jsxs)(rS.S.LinkItem,{onClick:()=>i("UPLOAD_FILES_BUTTON"),href:(0,nJ.Qi)({repo:r,path:n,commitish:t.name,action:"upload"}),children:[(0,nY.jsx)(rS.S.LeadingVisual,{children:(0,nY.jsx)(rs.UploadIcon,{})}),"Upload files"]}),(0,nY.jsx)(rS.S.Divider,{})]}),(0,nY.jsx)(c4,{path:n,updateTooltipMessage:u}),t.canEdit&&(0,nY.jsx)(rS.S.Divider,{}),(0,nY.jsx)(cA,{}),(0,nY.jsx)(rS.S.Divider,{}),(0,nY.jsx)(rS.S.Group,{title:"View options",children:(0,nY.jsx)(cG,{option:c})})]})})]})]})}function c4({path:e,updateTooltipMessage:t}){let{copyFilePathShortcut:n}=(0,rY.bx)(),{copyPermalinkShortcut:r}=(0,rY.bx)(),{sendRepoClickEvent:i}=(0,rx.a)(),{createPermalink:o}=(0,oY.B)();return(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsxs)(rS.S.Item,{onSelect:()=>{i("MORE_OPTIONS_DROPDOWN.COPY_PATH"),(0,ip.z)(e),t("Path copied!")},children:["Copy path",n.hotkey&&(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(aj,{shortcut:n})})]}),(0,nY.jsxs)(rS.S.Item,{onSelect:()=>{i("MORE_OPTIONS_DROPDOWN.COPY_PERMALINK"),(0,ip.z)(o({absolute:!0})),t("Permalink copied!")},children:["Copy permalink",r.hotkey&&(0,nY.jsx)(rS.S.TrailingVisual,{"aria-hidden":"true",children:(0,nY.jsx)(aj,{shortcut:r})})]})]})}function c8(){let e=cF();return e?(0,nY.jsx)(ae.z,{as:iM.r,to:e,children:"View Runs"}):null}try{(nH=c0).displayName||(nH.displayName="CodeViewHeader")}catch{}try{(n$=c1).displayName||(n$.displayName="MobileCodeHeader")}catch{}try{(nW=c2).displayName||(nW.displayName="OverviewHeader")}catch{}try{(nz=c3).displayName||(nz.displayName="TreeOverflowMenu")}catch{}try{(nU=c4).displayName||(nU.displayName="CopyPathsActionItems")}catch{}try{(nG=c8).displayName||(nG.displayName="ViewRunsButton")}catch{}var c6=n(74176),c5=n(53467);function c9(e){(0,n0.useEffect)(()=>{if(!e)return;let t=document.querySelector(".footer");if(t)return t.hidden=!0,()=>{t.hidden=!1}},[e])}function c7(e){return(0,nQ.g6)(e)?{...e.fileTree,[e.path]:{items:e.tree.items,totalCount:e.tree.totalCount}}:e.fileTree}function de(e,t,n){return{path:n,repo:e.repo,refInfo:e.refInfo,currentUser:e.currentUser,fileTree:c7(e),fileTreeProcessingTime:e.fileTreeProcessingTime,foldersToFetch:e.foldersToFetch,allShortcutsEnabled:e.allShortcutsEnabled,treeExpanded:e.treeExpanded,symbolsExpanded:e.symbolsExpanded,reducedMotionEnabled:e.reducedMotionEnabled,error:t||void 0}}let dt=Symbol("isProxied");function dn(e){if(!e||"object"!=typeof e||e[dt])return e;if(Array.isArray(e))e.forEach((t,n)=>e[n]=dn(t));else for(let[t,n]of Object.entries(e)){let r=t.replace(/_([a-z])/g,e=>e[1].toUpperCase());n&&"object"==typeof n&&dn(n),r!==t&&(e[r]=n)}return e[dt]=!0,e}let dr=(0,n0.lazy)(()=>Promise.all([n.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),n.e("vendors-node_modules_lit-html_lit-html_js"),n.e("vendors-node_modules_codemirror_lib_codemirror_js"),n.e("vendors-node_modules_js-yaml_index_js-node_modules_leven_index_js"),n.e("vendors-node_modules_tanstack_query-core_build_lib_queryClient_mjs-node_modules_tanstack_reac-0a6e0d"),n.e("vendors-node_modules_cronstrue_dist_cronstrue_js"),n.e("vendors-node_modules_codemirror_autocomplete_dist_index_js-node_modules_codemirror_search_dis-aafe81"),n.e("vendors-node_modules_jsonc-parser_lib_esm_main_js"),n.e("vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_react_l-30d52d"),n.e("app_assets_modules_github_editor_codemirror-linter-util_ts-app_assets_modules_github_editor_y-89a4a6"),n.e("app_assets_modules_github_editor_yaml-editors_workflow_workflow-rules_ts"),n.e("app_assets_modules_github_editor_yaml-editors_stack-template_stack-template-rules_ts"),n.e("app_assets_modules_react-code-view_components_blob-edit_WebCommitDialog_tsx"),n.e("app_assets_modules_github_editor_yaml-editors_dependabot_dependabot-rules_ts"),n.e("app_assets_modules_react-code-view_components_blob-edit_BlobEditor_tsx")]).then(n.bind(n,7469)));function di({initialPayload:e}){let t;let n=ds(e),r=(0,nX.H)(),{path:i}=n,o=rr(n.refInfo),a=(0,nQ.OH)(n),s=(0,nQ.Kg)(n),l=(0,nQ.K$)(n),c=(0,nQ.XU)(n),d="overview"in n,[u,h]=(0,n0.useState)(null),m=(0,au.Ez)(r,o,i,n.error?.httpStatus===404),f=n0.useRef(!1),p=n0.useRef(null),x=n0.useRef(!1),y=n0.useRef(!1),g=n0.useRef(null),b=n0.useRef(null),j="repos-file-tree",w=n0.useRef(),[v,N]=(0,n0.useState)(""),{toggleFocusedPaneShortcut:C}=(0,rY.bx)();n9(),c9(!d);let k=n0.useMemo(()=>c7(n),[n.path,n.refInfo.currentOid]),{isTreeExpanded:S,collapseTree:I,expandTree:R,treeToggleElement:Z,treeToggleRef:E,searchBoxRef:T}=dl(d,j,w,n.treeExpanded),L=(0,n0.useCallback)(()=>{window.innerWidth<n4._G.large&&I({focus:null})},[I]),[B,_]=(0,n0.useState)(!0);(0,n0.useEffect)(()=>{if(!window.location.hash&&window.scrollY>0){let e=document.querySelector("#StickyHeader");e&&(e.style.position="relative",e.scrollIntoView(),e.style.position="sticky")}},[n.path]);let{codeCenterOption:D}=(0,n6.O)(),F=n0.useCallback(()=>{window.innerWidth<n4._G.large&&R({focus:"search"})},[R]);return t=a?n.editInfo.isNewFile?"new":"edit":l?"blame":s?"blob":"tree",(0,nY.jsx)(au.Uc,{...m,children:(0,nY.jsx)(rK.Tv,{refInfo:o,path:i,action:t,children:(0,nY.jsx)(c5.K,{allShortcutsEnabled:n.allShortcutsEnabled,children:(0,nY.jsxs)(lj.c,{refreshTree:f,children:[(0,nY.jsx)(da,{}),(0,nY.jsxs)(n7.Z,{children:[(0,nY.jsx)(lf.ve,{children:(0,nY.jsx)(rW,{payload:n,openPanelRef:w,children:(0,nY.jsxs)(rt.jw,{children:[(0,nY.jsx)(n7.Z,{ref:p,tabIndex:0,sx:{width:["100%","100%","auto"]},children:!d&&(0,nY.jsx)(lF,{id:j,repo:r,path:i,isFilePath:s||a,refInfo:o,collapseTree:I,showTree:S,fileTree:k,onItemSelected:L,processingTime:n.fileTreeProcessingTime,treeToggleElement:Z,treeToggleRef:E,searchBoxRef:T,foldersToFetch:n.foldersToFetch,isOverview:d,onFindFilesShortcut:F})}),(0,nY.jsx)(rt.jw.Content,{padding:"none",width:D.enabled||d?"xlarge":"full",hidden:{narrow:S},sx:{marginRight:d?0:"auto","@media print":{display:"flex !important"},...d?{"@media screen and (min-width: 1440px)":{"> div":{mr:4}}}:{}},children:(0,nY.jsx)(n7.Z,{sx:{marginLeft:"auto",marginRight:d?0:"auto",flexDirection:"column",pb:d?3:6,maxWidth:d&&!S?1012:"100%",mt:d?3:0},ref:h,"data-selector":"repos-split-pane-content",tabIndex:0,children:(0,nY.jsx)(c6.Y,{contentRef:u,children:(0,nY.jsx)(ia,{searchTerm:v,setSearchTerm:N,isBlame:l,children:(0,nY.jsxs)(sl.Is,{children:[(0,nY.jsx)(n7.Z,{sx:{display:a?"none":"inherit"},children:(0,nY.jsx)(c0,{payload:n,showTree:S,treeToggleElement:Z,validCodeNav:B})}),n.error?(0,nY.jsx)(s1,{...n.error}):(0,nY.jsxs)(nY.Fragment,{children:[(0,nY.jsx)(n7.Z,{className:d?"":"react-code-view-bottom-padding",sx:{mx:d?0:3,"@media screen and (min-width: 1440px)":{ml:d&&!S?0:3}},children:(0,nY.jsx)(sV,{payload:n})}),(0,nY.jsx)(n7.Z,{sx:{mx:d?0:3,"@media screen and (min-width: 1440px)":{ml:d&&!S?0:3}},children:(0,nQ.g6)(n)?(0,nY.jsx)(cB,{overview:(0,nQ.uF)(n)?n.overview:void 0,tree:n.tree,showTree:S,treeToggleElement:d?null:Z,expandTree:R}):(0,nQ.OH)(n)?(0,nY.jsx)(n0.Suspense,{fallback:(0,nY.jsx)(ik.m,{}),children:(0,nY.jsx)(dr,{collapseTree:I,editInfo:n.editInfo,repo:n.repo,showTree:S,treeToggleElement:Z,webCommitInfo:n.webCommitInfo,copilotInfo:n.copilotInfo})}):s?(0,nY.jsx)(so,{blame:n.blame,blob:n.blob,searchTerm:v,setSearchTerm:N,setValidCodeNav:_,showTree:S,treeToggleElement:Z,validCodeNav:B,copilotInfo:n.copilotInfo}):c?(0,nY.jsx)(la,{deleteInfo:n.deleteInfo,webCommitInfo:n.webCommitInfo}):null})]})]})})})})})]})})}),(0,nY.jsx)(rO.d,{}),(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:C.hotkey,onButtonClick:()=>(function(){let e=document.getElementById(rB),t=document.getElementById(r1.KG);if(document.activeElement?.id===r1.KG&&(y.current=!0),u?.contains(document.activeElement)&&!y.current?x.current=!0:p.current?.contains(document.activeElement)&&(x.current=!1),x.current||y.current){if(y.current){let t=g.current||e||u;x.current=!0,y.current=!1,t?.focus()}else{let e=b.current||p.current;g.current=u?.contains(document.activeElement)?document.activeElement:null,x.current=!1,y.current=!1,e?.focus()}}else b.current=p.current?.contains(document.activeElement)?document.activeElement:null,x.current=!1,(t||u)?.focus()})()})]})]})})})})}function da(){let e=(0,oY.B)(),{permalinkShortcut:t}=(0,rY.bx)();return e.isCurrentPagePermalink()?(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:t.hotkey,buttonTestLabel:"header-permalink-button",onlyAddHotkeyScopeButton:!0,onButtonClick:()=>{}}):(0,nY.jsx)(rA.P,{buttonFocusId:r1.KG,buttonHotkey:t.hotkey,buttonTestLabel:"header-permalink-button",onButtonClick:()=>{let t=e.createPermalink();0>window.location.href.indexOf(t)&&window.history.pushState(null,document.title,t)}})}function ds(e){let t=(0,n3.T)(),n=e||t,r=(0,n0.useRef)(n),i=n2();if(n)r.current=n;else{let e=(0,nJ.Vr)(location.pathname,r.current.refInfo.name,r.current.path);n=de(r.current,i,e)}return(0,nQ.Kg)(n)&&(n.blob.symbols=dn(n.blob.symbols)),n}function dl(e,t,n,r){let{sendRepoClickEvent:i}=(0,rx.a)(),o=ir(),a=(0,ro.x)(),s=(0,rn.ej)("fileTreeExpanded"),l=!(void 0!==n8.n4),c=(!a&&s&&"false"!==s.value||a&&r)&&!e;void 0===c&&(c=!1),e&&c&&!l&&document.querySelector(".react-repos-overview-margin")?.classList.add("tree-open");let d=(0,n0.useRef)(null),u=(0,n0.useRef)(null),{screenSize:h}=(0,n4.eI)(),[m,f]=(0,n0.useState)(c),p=(0,n0.useRef)(c),x=(0,n0.useRef)(!1),y=(0,n0.useCallback)(()=>!((e||n.current)&&window.innerWidth>=lD||!n.current&&window.innerWidth>=n4._G.xlarge),[e,n]);(0,n0.useLayoutEffect)(()=>{let t=y();t||(x.current=!1),f((!t||x.current)&&(a&&m||!a&&s?.value!=="false")&&!e)},[x,s?.value,h,n,e,y,a]),(0,n0.useLayoutEffect)(()=>{let t=!(e||n.current)&&window.innerWidth<n4._G.xlarge,r=!(e||n.current)&&window.innerWidth>=n4._G.xlarge;t&&p.current&&!(0,lc.$)()&&m&&f(!1),r&&p.current&&!m&&f(!0)},[e,n,h]),(0,n0.useLayoutEffect)(()=>{let t=(e||n.current)&&window.innerWidth<lD,r=(e||n.current)&&window.innerWidth>=lD;t&&p.current&&!(0,lc.$)()&&m&&f(!1),r&&p.current&&!m&&f(!0)},[e,n]);let g=(0,n0.useCallback)(t=>{if(f(!0),y()&&(x.current=!0),t?.setCookie){o(!0,null),p.current=!0;let e=new Date(new Date().getTime()+2592e6).toUTCString();(0,rn.d8)("fileTreeExpanded","true",e)}t?.focus==="toggleButton"?requestAnimationFrame(()=>d.current?.focus()):t?.focus==="search"&&requestAnimationFrame(()=>u.current?.focus()),e&&document.querySelector(".react-repos-overview-margin")?.classList.add("tree-open")},[2592e6,e,y,o]),b=(0,n0.useCallback)(t=>{if(f(!1),x.current=!1,t?.setCookie){o(!1,null),p.current=!1;let e=new Date(new Date().getTime()+2592e6).toUTCString();(0,rn.d8)("fileTreeExpanded","false",e)}t?.focus==="toggleButton"&&requestAnimationFrame(()=>d.current?.focus()),e&&document.querySelector(".react-repos-overview-margin")?.classList.remove("tree-open")},[2592e6,e,o]),j=(0,n0.useCallback)(t=>(e||t)&&window.innerWidth>=lD||!n.current&&!e&&window.innerWidth>=n4._G.xlarge,[e,n]),w=(0,n0.useMemo)(()=>(0,nY.jsx)(re.Z,{as:"h2",sx:{fontSize:1},children:(0,nY.jsx)(ll,{expanded:m,ariaControls:t,onToggleExpanded:t=>{i(m?"FILES_TREE.HIDE":"FILES_TREE.SHOW"),m?b({focus:e&&0!==t.detail?void 0:"toggleButton",setCookie:j(n.current)}):g({focus:"toggleButton",setCookie:j(n.current)})},className:void 0!==s||m||l?void 0:"react-tree-toggle-button-with-indicator",ref:d})}),[m,t,s,i,b,e,j,n,g,l]);return{isTreeExpanded:m,expandTree:g,collapseTree:b,treeToggleElement:w,treeToggleRef:d,searchBoxRef:u}}try{(nq=dr).displayName||(nq.displayName="BlobEditor")}catch{}try{(nV=di).displayName||(nV.displayName="CodeView")}catch{}try{(nK=da).displayName||(nK.displayName="PermalinkShortcut")}catch{}},59050:(e,t,n)=>{n.d(t,{C:()=>x,E:()=>y});var r,i=n(85893),o=n(79902),a=n(97011),s=n(73290);let l=6e4,c=36e5,d=24*c,u=30*d,h=[{unit:"month",ms:u},{unit:"day",ms:d},{unit:"hour",ms:c},{unit:"minute",ms:6e4},{unit:"second",ms:1e3}],m=new Intl.DateTimeFormat(void 0,{year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",second:void 0,timeZoneName:"short"}),f=new Intl.DateTimeFormat(void 0,{year:"numeric",month:"short",day:"numeric"}),p=new Intl.DateTimeFormat(void 0,{month:"short",day:"numeric"});function x(e,t=!0){let n="",r=new Date,i=r.getTime()-e.getTime(),o=h.find(e=>e.ms<i);if(o&&"month"!==o.unit){let e=Math.floor(i/o.ms);n="day"===o.unit&&1===e?"yesterday":`${e} ${o.unit}${e>1?"s":""} ago`}else{let i=e.getFullYear()===r.getFullYear()?p:f;n=`${t?"on ":""}${i.format(e)}`}return n}function y({timestamp:e,usePreposition:t=!0,linkUrl:n,sx:r}){let l=x(e,t),c=m.format(e);return n?(0,i.jsx)(s.Z,{sx:{color:"fg.muted",...r},href:n,target:"_blank",children:(0,i.jsx)(o.Z,{inline:!0,title:c,children:(0,i.jsx)(a.Z,{title:c,sx:{"&:hover, &:focus":{color:"accent.fg",textDecoration:"underline"}},children:l})})}):(0,i.jsx)(o.Z,{inline:!0,title:c,children:(0,i.jsx)(a.Z,{title:c,sx:r,children:l})})}try{(r=y).displayName||(r.displayName="Ago")}catch{}},39004:(e,t,n)=>{n.d(t,{Z:()=>C,a:()=>w});var r,i,o,a,s,l=n(85893),c=n(78212),d=n(60348),u=n(85529),h=n(42483),m=n(50919),f=n(73290),p=n(97011),x=n(75308),y=n(67294),g=n(98950),b=n(4855),j=n(35880);function w({id:e="breadcrumb",fileNameId:t,path:n,repo:r,commitish:i,isFolder:o,fontSize:a,showCopyPathButton:s}){let{fileName:c,segments:d}=(0,y.useMemo)(()=>S(n),[n]),f=(0,y.useRef)(null),[p,x,w]=(0,b.a)("copy-path-tooltip",f,{direction:"nw"});return(0,l.jsxs)(h.Z,{sx:{display:"flex",flexDirection:"row",fontSize:a??2,minWidth:0,flexShrink:1,flexWrap:"wrap",maxWidth:"100%",alignItems:"center"},children:[(0,l.jsxs)(h.Z,{as:"nav","data-testid":"breadcrumbs","aria-labelledby":`${e}-heading`,id:e,sx:{maxWidth:"100%"},children:[(0,l.jsx)(j.N,{id:`${e}-heading`,as:"h2",text:"Breadcrumbs"}),(0,l.jsxs)(h.Z,{as:"ol",sx:{maxWidth:"100%",listStyle:"none",display:"inline-block"},children:[(0,l.jsx)(h.Z,{as:"li",sx:{display:"inline-block",maxWidth:"100%"},children:(0,l.jsx)(v,{repo:r,commitish:i})}),d.map(({directoryName:e,directoryPath:t})=>(0,l.jsxs)(h.Z,{as:"li",sx:{display:"inline-block",maxWidth:"100%"},children:[(0,l.jsx)(C,{fontSize:a}),(0,l.jsx)(N,{path:t,directoryName:e,repo:r,commitish:i})]},t))]})]}),c&&(0,l.jsxs)(h.Z,{"data-testid":"breadcrumbs-filename",sx:{display:"inline-block",maxWidth:"100%"},children:[(0,l.jsx)(C,{fontSize:a}),(0,l.jsx)(k,{value:c,id:t,fontSize:a}),!!n&&o&&(0,l.jsx)(C,{})]},c),s&&w,s&&(0,l.jsx)(m.h,{icon:u.CopyIcon,ref:f,variant:"invisible",size:"small","aria-label":"Copy path",onClick:()=>{(0,g.z)(n),p("Path copied!"),setTimeout(()=>{x()},5e3)},sx:{ml:2},"data-testid":"breadcrumb-copy-path-button"})]})}function v({repo:e,commitish:t}){return(0,l.jsx)(f.Z,{as:d.r,sx:{fontWeight:"bold"},to:(0,c.Qi)({repo:e,commitish:t,action:"tree"}),"data-testid":"breadcrumbs-repo-link",reloadDocument:!0,children:e.name})}function N({directoryName:e,path:t,repo:n,commitish:r}){return(0,l.jsx)(f.Z,{as:d.r,to:(0,c.Qi)({repo:n,commitish:r,path:t,action:"tree"}),sx:{fontWeight:400},children:e})}function C({fontSize:e}){return(0,l.jsx)(p.Z,{sx:{px:1,fontWeight:400,color:"fg.muted",fontSize:e??2},"aria-hidden":"true",children:"/"})}function k({value:e,id:t,fontSize:n}){return(0,l.jsx)(x.Z,{as:"h1",tabIndex:-1,sx:{fontWeight:600,display:"inline-block",maxWidth:"100%",fontSize:n??2},id:t,children:e})}function S(e){let t=e.split("/"),n=t.pop();return{fileName:n,segments:t.map((e,n)=>({directoryName:e,directoryPath:t.slice(0,n+1).join("/")}))}}try{(r=w).displayName||(r.displayName="Breadcrumb")}catch{}try{(i=v).displayName||(i.displayName="RepoLink")}catch{}try{(o=N).displayName||(o.displayName="DirectoryLink")}catch{}try{(a=C).displayName||(a.displayName="Separator")}catch{}try{(s=k).displayName||(s.displayName="FileName")}catch{}},17338:(e,t,n)=>{n.d(t,{V:()=>s});var r=n(85893),i=n(85529),o=n(50919),a=n(67294);let s=a.forwardRef(({expanded:e,testid:t,ariaLabel:n,ariaControls:a,onToggleExpanded:s,sx:l,alignment:c,dataHotkey:d,className:u},h)=>(0,r.jsx)(o.h,{ref:h,"data-testid":e?`collapse-${t}`:`expand-${t}`,"aria-label":n,"aria-expanded":e,"aria-controls":a,icon:e?"left"===c?i.SidebarExpandIcon:i.SidebarCollapseIcon:"left"===c?i.SidebarCollapseIcon:i.SidebarExpandIcon,sx:{color:"fg.muted",...l},"data-hotkey":d,onClick:e=>{s(e)},variant:"invisible",className:u}));s.displayName="ExpandButton"},98234:(e,t,n)=>{n.d(t,{Z:()=>s});var r,i=n(85893),o=n(42483);function a(){return(0,i.jsx)(o.Z,{"aria-hidden":"true",as:"svg",version:"1.1",viewBox:"0 0 340 84",xmlns:"http://www.w3.org/2000/svg",sx:{bottom:"0 !important",clip:"rect(1px, 1px, 1px, 1px)",clipPath:"inset(50%)",height:"84px",position:"absolute",width:"320px"},children:(0,i.jsxs)("defs",{children:[(0,i.jsxs)("clipPath",{id:"diff-placeholder",children:[(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"67.0175439",x:"0",y:"0"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"100.701754",x:"18.9473684",y:"47.7194983"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"37.8947368",x:"0",y:"71.930126"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"53.3333333",x:"127.017544",y:"48.0703769"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"72.9824561",x:"187.719298",y:"48.0703769"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"140.350877",x:"76.8421053",y:"0"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"140.350877",x:"17.8947368",y:"23.8597491"}),(0,i.jsx)("rect",{height:"11.9298746",rx:"2",width:"173.684211",x:"166.315789",y:"23.8597491"})]}),(0,i.jsxs)("linearGradient",{id:"animated-diff-gradient",spreadMethod:"reflect",x1:"0",x2:"0",y1:"0",y2:"1",children:[(0,i.jsx)("stop",{offset:"0",stopColor:"#eee"}),(0,i.jsx)("stop",{offset:"0.2",stopColor:"#eee"}),(0,i.jsx)("stop",{offset:"0.5",stopColor:"#ddd"}),(0,i.jsx)("stop",{offset:"0.8",stopColor:"#eee"}),(0,i.jsx)("stop",{offset:"1",stopColor:"#eee"}),(0,i.jsx)("animateTransform",{attributeName:"y1",dur:"1s",repeatCount:"3",values:"0%; 100%; 0"}),(0,i.jsx)("animateTransform",{attributeName:"y2",dur:"1s",repeatCount:"3",values:"100%; 200%; 0"})]})]})})}let s=a;try{(r=a).displayName||(r.displayName="DiffPlaceholder")}catch{}},90874:(e,t,n)=>{n.d(t,{M:()=>l,x:()=>c});var r,i,o=n(85893),a=n(67294);let s=a.createContext(void 0);function l({user:e,children:t}){return(0,o.jsxs)(s.Provider,{value:e,children:[" ",t," "]})}function c(){return a.useContext(s)}try{(r=s).displayName||(r.displayName="CurrentUserContext")}catch{}try{(i=l).displayName||(i.displayName="CurrentUserProvider")}catch{}},35880:(e,t,n)=>{n.d(t,{N:()=>a});var r,i=n(85893),o=n(75308);function a({as:e,text:t,...n}){return(0,i.jsx)(o.Z,{as:e,className:"sr-only","data-testid":"screen-reader-heading",...n,children:t})}try{(r=a).displayName||(r.displayName="ScreenReaderHeading")}catch{}},34493:(e,t,n)=>{n.d(t,{o:()=>c,x:()=>d});var r,i,o=n(85893),a=n(86283),s=n(67294);let l=s.createContext({focusHint:null,setFocusHint:()=>void 0});function c({children:e}){let t={key:a.ssrSafeLocation.pathname+a.ssrSafeLocation.search},n=(0,s.useRef)(t.key),r=(0,s.useRef)(t.key),i=(0,s.useRef)({hint:null,location:null}),c=(0,s.useCallback)((e,n)=>{i.current={hint:e,context:n,location:t.key}},[t.key]);r.current!==t.key&&(n.current=r.current,r.current=t.key);let d=i.current.location===n.current,u=d?i.current.hint:null,h=d?i.current.context:null,m=(0,s.useMemo)(()=>({focusHint:u,context:h,setFocusHint:c}),[u,h,c]);return(0,o.jsx)(l.Provider,{value:m,children:e})}function d(){return(0,s.useContext)(l)}try{(r=l).displayName||(r.displayName="FocusHintContext")}catch{}try{(i=c).displayName||(i.displayName="FocusHintContextProvider")}catch{}},4855:(e,t,n)=>{n.d(t,{a:()=>s});var r,i=n(85893),o=n(52793),a=n(67294);function s(e,t,n){let[r,o]=(0,a.useState)(""),s=(0,a.useCallback)(()=>{o("")},[]),c=(0,a.useCallback)(e=>{o(e),t.current!==document.activeElement&&setTimeout(s,3e3)},[s,t]);return[c,s,(0,i.jsx)(l,{message:r,id:e,contentRef:t,clearMessage:s,portalTooltipProps:n},e)]}function l({message:e,id:t,contentRef:n,clearMessage:r,portalTooltipProps:a}){return e?(0,i.jsx)(o.u,{id:t,contentRef:n,"aria-label":e,open:!!e,onMouseLeave:r,"aria-live":"assertive",...a}):null}try{(r=l).displayName||(r.displayName="AlertTooltip")}catch{}},56302:(e,t,n)=>{n.d(t,{Z:()=>p,o:()=>x});var r,i=n(85893),o=n(57070),a=n(2048),s=n(87634),l=n(85529),c=n(33831),d=n(6324),u=n(52516),h=n(67294),m=n(1640);let f={height:"24px","span[role=tooltip]":{height:"16px"}};function p({messageReference:e,style:t}){let[n,r]=(0,h.useState)(!1),p=(0,a.y)("copilot_conversational_ux"),y=(0,a.y)("copilot_smell_icebreaker_ux"),{addToast:g}=(0,s.V6)(),b=(0,h.useCallback)(()=>{(0,m.L4)({content:"Explain",intent:"explain",references:[e]}),r(!1)},[e]),j=(0,h.useCallback)(()=>{(0,m.L4)({content:"Suggest improvements to this code.",intent:"suggest",references:[e]}),r(!1)},[e]),w=(0,h.useCallback)(()=>{(0,m.L4)({intent:"conversation",references:[e]}),r(!1)},[e]),v=(0,h.useCallback)(()=>{x(e,!1,g),r(!1)},[g,e]);return p?(0,i.jsxs)(c.Z,{sx:t||{pr:3},children:[(0,i.jsx)(o.E,{icon:l.CopilotIcon,size:"small",label:`Ask Copilot about this ${e.type}`,onClick:w,sx:f,tooltipDirection:"sw","data-testid":"copilot-ask-menu"}),(0,i.jsxs)(d.P,{open:n,onOpenChange:r,children:[(0,i.jsx)(d.P.Anchor,{children:(0,i.jsx)(o.E,{icon:l.TriangleDownIcon,label:"Copilot menu",onSelect:()=>r(!0),size:"small",sx:f,tooltipDirection:"sw","data-testid":"more-copilot-button"})}),(0,i.jsx)(d.P.Overlay,{align:"end",children:(0,i.jsxs)(u.S,{children:[(0,i.jsx)(u.S.Item,{onClick:b,children:"Explain"}),y?(0,i.jsx)(u.S.Item,{onClick:j,children:"Suggest Improvements"}):null,(0,i.jsx)(u.S.Divider,{}),(0,i.jsx)(u.S.Item,{onClick:v,children:"Attach to current thread"})]})})]})]}):null}let x=(e,t,n)=>{t?(0,m.L4)({intent:"conversation",references:[e]}):n&&((0,m.cw)(e),n({message:"Reference added to thread",type:"success"}))};try{(r=p).displayName||(r.displayName="CopilotChatButton")}catch{}},32769:(e,t,n)=>{n.d(t,{H:()=>c,d:()=>l});var r,i,o=n(85893),a=n(67294);let s=a.createContext({});function l({repository:e,children:t}){return(0,o.jsxs)(s.Provider,{value:e,children:[" ",t," "]})}function c(){return a.useContext(s)}try{(r=s).displayName||(r.displayName="CurrentRepositoryContext")}catch{}try{(i=l).displayName||(i.displayName="CurrentRepositoryProvider")}catch{}},49713:(e,t,n)=>{n.d(t,{VH:()=>R,g6:()=>S,Bb:()=>I,mG:()=>k});var r,i,o,a,s,l,c,d,u=n(85893),h=n(78912),m=n(73290),f=n(98833),p=n(85529),x=n(75809),y=n(53664),g=n(67294),b=n(87634),j=n(89445);let w=async(e,t,n)=>{try{let r=await (0,j.Q)(e,{method:t,body:n});return r.ok}catch(e){return!1}},v=(e,t)=>w(e,"DELETE",C(t)),N=(e,t)=>w(e,"POST",C(t)),C=e=>{let t=new FormData;return t.append("feature",e),t};function k(e){let{alreadyRequested:t=!1,featureName:n="",requestPath:r=""}=e??{},[i,o]=(0,g.useState)(!1),[a,s]=(0,g.useState)(t),{addToast:l}=(0,b.V6)(),c=async()=>{o(!0);let e=await (a?v:N)(r,n);e?s(!a):l({type:"error",message:"Something went wrong. Please try again later."}),o(!1)};return{inProgress:i,requested:a,toggleFeatureRequest:c}}function S({featureRequestInfo:e,learnMorePath:t,requestMessage:n,requestedMessage:r}){let{inProgress:i,requested:o,toggleFeatureRequest:a}=k(e);return e.showFeatureRequest?o?(0,u.jsx)(R,{inProgress:i,toggleFeatureRequest:a,requestedMessage:r}):(0,u.jsx)(I,{inProgress:i,toggleFeatureRequest:a,featureName:e.featureName,learnMorePath:t,requestMessage:n}):null}let I=({inProgress:e,toggleFeatureRequest:t,featureName:n,learnMorePath:r,requestMessage:i})=>{let{sendClickAnalyticsEvent:o}=(0,y.w)(),a=()=>{t(),o({category:"member_feature_request",action:`action.${n}`,label:`ref_cta:ask_admin_for_access;ref_loc:${n};`})},s=()=>{o({category:"suggestion",action:"click_to_read_docs",label:`ref_cta:learn_more;ref_loc:${n};`})};return(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(Z,{onClick:a,inProgress:e}),i&&(0,u.jsx)(T,{message:i}),r&&(0,u.jsx)(E,{onClick:s,path:r})]})},R=({inProgress:e,toggleFeatureRequest:t,requestedMessage:n})=>(0,u.jsxs)(u.Fragment,{children:[n&&(0,u.jsx)(L,{message:n}),(0,u.jsx)(B,{onClick:t,inProgress:e})]}),Z=({onClick:e,inProgress:t})=>(0,u.jsx)(h.z,{onClick:e,disabled:t,...(0,x.f)("feature-request-request-button"),children:t?"Requesting...":"Ask admin for access"}),E=({onClick:e,path:t})=>(0,u.jsx)(m.Z,{href:t,onClick:e,...(0,x.f)("feature-request-learn-more-link"),children:"Learn more"}),T=({message:e})=>(0,u.jsx)("span",{children:e}),L=({message:e})=>(0,u.jsxs)("span",{className:"d-inline-block color-fg-subtle mr-1",children:[(0,u.jsx)(f.Z,{icon:p.CheckIcon}),e]}),B=({onClick:e,inProgress:t})=>(0,u.jsx)(m.Z,{className:"color-fg-danger text-semibold",as:"button",onClick:e,disabled:t,...(0,x.f)("feature-request-cancel-link"),children:t?"Cancelling...":"Remove request"});try{(r=S).displayName||(r.displayName="FeatureRequest")}catch{}try{(i=I).displayName||(i.displayName="RequestFeature")}catch{}try{(o=R).displayName||(o.displayName="CancelFeatureRequest")}catch{}try{(a=Z).displayName||(a.displayName="RequestCTA")}catch{}try{(s=E).displayName||(s.displayName="LearnMore")}catch{}try{(l=T).displayName||(l.displayName="RequestMessage")}catch{}try{(c=L).displayName||(c.displayName="RequestedMessage")}catch{}try{(d=B).displayName||(d.displayName="RemoveRequestCTA")}catch{}},57294:(e,t,n)=>{n.d(t,{O:()=>l});var r,i=n(85893),o=n(67294),a=n(26012),s=n(86283);let l=(0,o.forwardRef)(function({src:e,size:t=20,...n},r){let l=(0,o.useMemo)(()=>{let n=new URL(e,s.ssrSafeLocation.origin);return n.searchParams.has("size")||n.searchParams.has("s")||n.searchParams.set("size",String(2*Number(t))),n.toString()},[e,t]);return(0,i.jsx)(a.Z,{ref:r,src:l,size:t,"data-testid":"github-avatar",...n})});try{(r=l).displayName||(r.displayName="GitHubAvatar")}catch{}},57070:(e,t,n)=>{n.d(t,{E:()=>u});var r,i=n(85893),o=n(17920),a=n(38490),s=n(50919),l=n(67294);let c=e=>` ${e.split(" ").map(e=>`<${e.replaceAll("CommandOrControl",(0,o.eE)()?"Cmd":"Ctrl").replaceAll("Command","Cmd").replaceAll("Control","Ctrl").toLowerCase()}>`).join(" / ")}`,d=e=>e.replaceAll("{CMD_CTRL}",(0,o.eE)()?"Command":"Control"),u=(0,l.forwardRef)(({label:e,icon:t,shortcut:n,sx:r,tooltipDirection:o,...u},h)=>{let m=(0,l.useCallback)(()=>(0,i.jsx)(a.Z,{"aria-label":`${e}${n?c(n):""}`,className:"icon-button-with-tooltip__tooltip",direction:o,children:(0,i.jsx)(t,{})}),[e,n,t,o]);return(0,i.jsx)(s.h,{ref:h,icon:m,"aria-keyshortcuts":n?d(n):void 0,sx:{...r,"&:focus-visible, &:hover":{"& .icon-button-with-tooltip__tooltip":{"&::after, &::before":{display:"inline-block",textDecoration:"none",animationName:"tooltip-appear",animationDuration:"0.1s",animationFillMode:"forwards",animationTimingFunction:"ease-in"}}},"&:hover .icon-button-with-tooltip__tooltip":{"&::after, &::before":{animationDelay:"0.4s"}}},...u,"aria-label":e})});u.displayName="IconButtonWithTooltip";try{(r=TooltippedIcon).displayName||(r.displayName="TooltippedIcon")}catch{}},45222:(e,t,n)=>{n.d(t,{h:()=>h});var r,i=n(85893),o=n(42379),a=n(15173),s=n(41905),l=n(86010),c=n(67294),d=n(15388);let u=d.ZP.span`
  &::before {
    position: absolute;
    z-index: 1000001;
    display: none;
    width: 0px;
    height: 0px;
    color: ${(0,o.U2)("colors.neutral.emphasisPlus")};
    pointer-events: none;
    content: '';
    border: 6px solid transparent;
    opacity: 0;
  }
  &::after {
    position: absolute;
    z-index: 1000000;
    display: none;
    padding: 0.5em 0.75em;
    font: normal normal 11px/1.5 ${(0,o.U2)("fonts.normal")};
    -webkit-font-smoothing: subpixel-antialiased;
    color: ${(0,o.U2)("colors.fg.onEmphasis")};
    text-align: center;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-wrap: break-word;
    white-space: pre;
    pointer-events: none;
    content: attr(aria-label);
    background: ${(0,o.U2)("colors.neutral.emphasisPlus")};
    border-radius: ${(0,o.U2)("radii.1")};
    opacity: 0;
  }
  /* delay animation for tooltip */
  @keyframes tooltip-appear {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  &.tooltipped-open,
  &:hover,
  &:active,
  &:focus {
    &::before,
    &::after {
      display: inline-block;
      text-decoration: none;
      animation-name: tooltip-appear;
      animation-duration: 0.1s;
      animation-fill-mode: forwards;
      animation-timing-function: ease-in;
      animation-delay: 0.4s;
    }
  }

  &.tooltipped-no-delay.tooltipped-open,
  &.tooltipped-no-delay:hover,
  &.tooltipped-no-delay:active,
  &.tooltipped-no-delay:focus {
    &::before,
    &::after {
      animation-delay: 0s;
    }
  }

  /* Tooltipped south */
  &.tooltipped-s,
  &.tooltipped-se,
  &.tooltipped-sw {
    &::after {
      top: 100%;
      right: 50%;
      margin-top: 6px;
    }
    &::before {
      top: auto;
      right: 50%;
      bottom: -7px;
      margin-right: -6px;
      border-bottom-color: ${(0,o.U2)("colors.neutral.emphasisPlus")};
    }
  }
  &.tooltipped-se {
    &::after {
      right: auto;
      left: 50%;
      margin-left: -${(0,o.U2)("space.3")};
    }
  }
  &.tooltipped-sw::after {
    margin-right: -${(0,o.U2)("space.3")};
  }
  /* Tooltips above the object */
  &.tooltipped-n,
  &.tooltipped-ne,
  &.tooltipped-nw {
    &::after {
      right: 50%;
      bottom: 100%;
      margin-bottom: 6px;
    }
    &::before {
      top: -7px;
      right: 50%;
      bottom: auto;
      margin-right: -6px;
      border-top-color: ${(0,o.U2)("colors.neutral.emphasisPlus")};
    }
  }
  &.tooltipped-ne {
    &::after {
      right: auto;
      left: 50%;
      margin-left: -${(0,o.U2)("space.3")};
    }
  }
  &.tooltipped-nw::after {
    margin-right: -${(0,o.U2)("space.3")};
  }
  /* Move the tooltip body to the center of the object. */
  &.tooltipped-s::after,
  &.tooltipped-n::after {
    transform: translateX(50%);
  }
  /* Tooltipped to the left */
  &.tooltipped-w {
    &::after {
      right: 100%;
      bottom: 50%;
      margin-right: 6px;
      transform: translateY(50%);
    }
    &::before {
      top: 50%;
      bottom: 50%;
      left: -7px;
      margin-top: -6px;
      border-left-color: ${(0,o.U2)("colors.neutral.emphasisPlus")};
    }
  }
  /* tooltipped to the right */
  &.tooltipped-e {
    &::after {
      bottom: 50%;
      left: 100%;
      margin-left: 6px;
      transform: translateY(50%);
    }
    &::before {
      top: 50%;
      right: -7px;
      bottom: 50%;
      margin-top: -6px;
      border-right-color: ${(0,o.U2)("colors.neutral.emphasisPlus")};
    }
  }
  &.tooltipped-align-right-2::after {
    right: 0;
    margin-right: 0;
  }
  &.tooltipped-align-right-2::before {
    right: 15px;
  }
  &.tooltipped-align-left-2::after {
    left: 0;
    margin-left: 0;
  }
  &.tooltipped-align-left-2::before {
    left: 10px;
  }
  ${a.Z};
`,h=(0,c.forwardRef)(function({direction:e="n",className:t,text:n,noDelay:r,align:o,wrap:a,open:c=!1,portalProps:d={},...h},m){let f=(0,l.W)(t,`tooltipped-${e}`,o&&`tooltipped-align-${o}-2`,r&&"tooltipped-no-delay",a&&"tooltipped-multiline",c&&"tooltipped-open");return(0,i.jsx)(s.h,{...d,children:(0,i.jsx)(u,{ref:m,role:"tooltip","aria-label":n,...h,sx:{position:"fixed",zIndex:1,...h.sx},className:f})})});try{(r=h).displayName||(r.displayName="ControlledTooltip")}catch{}},52793:(e,t,n)=>{n.d(t,{u:()=>c});var r,i=n(85893),o=n(48030),a=n(67294),s=n(45222),l=n(95628);let c=(0,a.forwardRef)(function({contentRef:e,open:t,anchoredPositionAlignment:n,anchorSide:r,anchorOffset:c,alignmentOffset:d,allowOutOfBounds:u,...h},m){let f=(0,a.useRef)(null);(0,a.useImperativeHandle)(m,()=>f.current);let p=(0,a.useRef)({left:0,top:0}),x=(0,a.useSyncExternalStore)((0,a.useCallback)(n=>{if(!f.current||!e.current||!t)return()=>void 0;let r=(0,l.M)(e.current);return r?.addEventListener("scroll",n),()=>{r?.removeEventListener("scroll",n)}},[e,t]),(0,a.useCallback)(()=>{if(!f.current||!e.current)return p.current;let t=(0,o.N)(f.current,e.current,{align:n??"center",side:r??"outside-top",alignmentOffset:d??0,anchorOffset:c??0,allowOutOfBounds:u});return(t.left!==p.current.left||t.top!==p.current.top)&&(p.current=t),p.current},[e,d,c,n,r,u]));return(0,i.jsx)(s.h,{...h,ref:f,open:t,style:{position:"absolute",...x,...h.style}})});try{(r=c).displayName||(r.displayName="PortalTooltip")}catch{}},60348:(e,t,n)=>{n.d(t,{r:()=>c});var r=n(85893),i=n(67294),o=n(12599),a=n(79655),s=n(45055),l=n(86283);let c=i.forwardRef(({to:e,reloadDocument:t,...n},c)=>{let{routes:d}=i.useContext(s.I),u=(0,o.i3)(e,l.ssrSafeLocation.pathname).pathname;return t=t??!(0,o.fp)(d,u),(0,r.jsx)(a.rU,{to:e,...n,reloadDocument:t,ref:c})});c.displayName="Link"},93062:(e,t,n)=>{n.d(t,{WZ:()=>h,sF:()=>f,wB:()=>u});var r,i=n(85893),o=n(42483),a=n(97011),s=n(27856),l=n.n(s),c=n(67294);function d(e){if("html"in e&&void 0!==e.html){let{html:t,...n}=e;return{safeHTML:t,props:n}}let{unverifiedHTML:t,...n}=e;return{safeHTML:l().sanitize(t),props:n}}let u=m(o.Z);u.displayName="SafeHTMLBox";let h=m(a.Z);function m(e){let t=(0,c.forwardRef)((t,n)=>{let{safeHTML:r,props:o}=d(t);return(0,i.jsx)(e,{ref:n,...o,dangerouslySetInnerHTML:r?{__html:r}:void 0})});return t}h.displayName="SafeHTMLText";let f=(0,c.forwardRef)((e,t)=>{let{safeHTML:n,props:r}=d(e);return(0,i.jsx)("div",{ref:t,...r,dangerouslySetInnerHTML:n?{__html:n}:void 0})});f.displayName="SafeHTMLDiv";try{(r=SafeHTMLComponent).displayName||(r.displayName="SafeHTMLComponent")}catch{}},69942:(e,t,n)=>{n.d(t,{ZV:()=>m,_G:()=>o,eI:()=>u,xp:()=>h});var r,i,o,a=n(85893),s=n(46263),l=n(67294);!function(e){e[e.small=1]="small",e[e.medium=544]="medium",e[e.large=768]="large",e[e.xlarge=1012]="xlarge",e[e.xxlarge=1280]="xxlarge",e[e.xxxlarge=1350]="xxxlarge",e[e.xxxxlarge=1440]="xxxxlarge"}(o||(o={}));let c=[1440,1350,1280,1012,768,544,1],d=l.createContext({screenSize:1});function u(){return l.useContext(d)}function h({children:e,initialValue:t}){let n=(0,l.useSyncExternalStore)(()=>()=>{},()=>t??m(window.innerWidth),()=>t??1),r=(0,l.useRef)(n),[i,o]=(0,l.useState)(n),c=(0,l.useCallback)(()=>{let e=m(window.innerWidth);r.current!==e&&(r.current=e,o(e))},[]);(0,l.useEffect)(()=>{let e=new ResizeObserver((0,s.D)(c));return e.observe(document.documentElement),()=>e.disconnect()},[c]);let u=(0,l.useMemo)(()=>({screenSize:i}),[i]);return(0,a.jsx)(d.Provider,{value:u,children:e})}function m(e){for(let t of c)if(e>=t)return t;return 1}try{(r=d).displayName||(r.displayName="ScreenContext")}catch{}try{(i=h).displayName||(i.displayName="ScreenSizeProvider")}catch{}},11117:(e,t,n)=>{n.d(t,{K:()=>p,O:()=>y});var r,i,o=n(85893),a=n(44544),s=n(67294);let l=(0,a.Z)("localStorage"),c="codeView.codeFolding",d="codeView.codeWrapping",u="codeView.centerView",h="codeView.openSymbolsOnClick",m=new Map([[c,"Show code folding buttons"],[d,"Wrap lines"],[u,"Center content"],[h,"Open symbols on click"]]),f=(0,s.createContext)({codeFoldingOption:{},codeWrappingOption:{},codeCenterOption:{},openSymbolsOption:{}}),p=({children:e})=>{let t=x(c,!0),n=x(d,!1),r=x(u,!1),i=x(h,!0),a=(0,s.useMemo)(()=>({codeFoldingOption:t,codeWrappingOption:n,codeCenterOption:r,openSymbolsOption:i}),[t,n,r,i]);return(0,o.jsx)(f.Provider,{value:a,children:e})};function x(e,t){let n=l.getItem(e),[r,i]=(0,s.useState)(()=>n?"true"===n:t),o=m.get(e)||"";return{name:e,enabled:r,setEnabled:i,label:o}}function y(){return(0,s.useContext)(f)}try{(r=f).displayName||(r.displayName="CodeViewOptionsContext")}catch{}try{(i=p).displayName||(i.displayName="CodeViewOptionsProvider")}catch{}}}]);
//# sourceMappingURL=app_assets_modules_react-code-view_pages_CodeView_tsx-20f9863cf398.js.map