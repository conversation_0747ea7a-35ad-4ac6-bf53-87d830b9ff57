(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["behaviors"],{70354:(e,t,n)=>{"use strict";let r,i,a,o,s,l,c,u,d;var f,m,h,p,g=n(54679),b=n(59753);(0,b.on)("deprecatedAjaxSend","[data-remote]",function(e){e.currentTarget!==e.target||e.defaultPrevented||e.currentTarget.classList.add("loading")}),(0,b.on)("deprecatedAjaxComplete","[data-remote]",function(e){e.currentTarget===e.target&&e.currentTarget.classList.remove("loading")});var y=n(65935);(0,y.AC)("form.js-ajax-pagination, .js-ajax-pagination form",async function(e,t){let n;let r=e.closest(".js-ajax-pagination");try{n=await t.html()}catch(e){if(e.response&&404===e.response.status){r.remove();return}throw e}r.replaceWith(n.html),(0,b.f)(e,"page:loaded")});var v=n(95253),w=n(86283),S=n(44544);let{getItem:E}=(0,S.Z)("localStorage"),L="analytics.click";function j(){return!!w.n4?.head?.querySelector('meta[name="is_logged_out_page"]')?.content}(0,b.on)("click","[data-analytics-event]",e=>{if(j())return;let t=e.currentTarget,n=t.getAttribute("data-analytics-event");if(!n)return;let r=JSON.parse(n);(0,v.qP)("analytics.click",r)});var A=n(36071),T=n(71643);let q=["system","disabled"].map(e=>`html[data-a11y-animated-images="${e}"] img[data-animated-image]`).join(", ");(0,A.N7)(q,e=>{if(!(e instanceof HTMLImageElement)||e.closest("a")&&!(e.parentElement instanceof HTMLAnchorElement))return;let t=e.parentElement,n=null;if(t instanceof HTMLAnchorElement){if(t.childElementCount>1)return;(n=t).setAttribute("data-target","animated-image.originalLink"),t=n.parentElement}e.removeAttribute("data-animated-image"),e.setAttribute("data-target","animated-image.originalImage");let r=n?n.cloneNode(!0):e.cloneNode(!0),i=document.createElement("animated-image");i.appendChild(r),t?.replaceChild(i,n||e),(0,T.b)({incrementKey:"ANIMATED_IMAGE_PLAYER_WRAPPED",requestUrl:window.location.href})});var k=n(15345),C=n(81775);let M=new WeakMap;function x(e,t){t.classList.remove("is-loading","successed","errored","warn"),e.classList.remove("is-autocheck-loading","is-autocheck-successful","is-autocheck-errored");let n=t.querySelector("p.note");if(n){let e=M.get(n);e&&(n.innerHTML=e)}"DL"===t.tagName?(t.querySelector("dd.error")?.remove(),t.querySelector("dd.warning")?.remove(),t.querySelector("dd.success")?.remove()):(t.querySelector("div.error")?.remove(),t.querySelector("div.warning")?.remove(),t.querySelector("div.success")?.remove())}(0,A.N7)("auto-check",function(e){let t;if(e.classList.contains("js-prevent-default-behavior"))return;let n=e.querySelector("input");if(!n)return;let r=n.closest(".form-group")||e,i=n.form;function a(){return t||(t=`input-check-${(1e4*Math.random()).toFixed(0)}`),t}let o=n.getAttribute("aria-describedby");n.addEventListener("focusout:delay",()=>{n.setAttribute("aria-describedby",[t,o].join(" "))});let s=r.querySelector("p.note");s&&(s.id||(s.id=a()),M.set(s,s.innerHTML)),e.addEventListener("loadstart",()=>{x(n,r),r.classList.add("is-loading"),n.classList.add("is-autocheck-loading"),(0,C.G)(i)}),e.addEventListener("loadend",()=>{r.classList.remove("is-loading"),n.classList.remove("is-autocheck-loading")}),n.addEventListener("auto-check-success",async e=>{n.classList.add("is-autocheck-successful"),r.classList.add("successed"),(0,C.G)(i);let{response:t}=e.detail;if(!t)return;let o=await t.text();if(o){if(s instanceof HTMLElement)s.innerHTML=o,(0,k.N)(s);else{let e=200===t.status,i="DL"===r.tagName?"dd":"div",s=document.createElement(i);s.id=a(),s.classList.add(e?"success":"warning"),s.innerHTML=o,r.append(s),r.classList.add(e?"successed":"warn"),(0,k.N)(s),e&&(s.hidden=document.activeElement!==n)}(0,b.f)(n,"auto-check-message-updated")}}),n.addEventListener("auto-check-error",async e=>{n.classList.add("is-autocheck-errored"),r.classList.add("errored"),(0,C.G)(i);let{response:t}=e.detail;if(!t)return;let o=await t.text();if(s instanceof HTMLElement)s.innerHTML=o||"Something went wrong",(0,k.N)(s);else{let e="DL"===r.tagName?"dd":"div",t=document.createElement(e);t.id=a(),t.classList.add("error"),t.innerHTML=o||"Something went wrong",r.append(t),(0,k.N)(t)}}),n.addEventListener("input",()=>{n.removeAttribute("aria-describedby"),n.value||x(n,r)}),n.addEventListener("blur",()=>{let e=r.querySelector(".success");e&&(e.hidden=!0)}),n.addEventListener("focus",()=>{let e=r.querySelector(".success");e&&(e.hidden=!1)}),i.addEventListener("reset",()=>{x(n,r)})});var _=n(46481);function R(e){let t=e.closest("form");if(!t)return;let n=t.querySelector(".js-auto-complete-button");n instanceof HTMLButtonElement&&(n.disabled=!e.value)}(0,A.N7)("auto-complete",function(e){e.addEventListener("loadstart",()=>e.classList.add("is-auto-complete-loading")),e.addEventListener("loadend",()=>e.classList.remove("is-auto-complete-loading"))}),(0,A.N7)("auto-complete",{constructor:_.Z,initialize:R}),(0,b.on)("auto-complete-change","auto-complete",function(e){R(e.currentTarget)});var H=n(58700),N=n(67525),P=n(9302);let $=null;(0,b.on)("submit","[data-autosearch-results-container]",async function(e){let t=e.currentTarget;if(!(t instanceof HTMLFormElement))return;e.preventDefault(),$?.abort(),t.classList.add("is-sending");let n=new URL(t.action,window.location.origin),i=t.method,a=new FormData(t),o=(0,H.KL)(n,a),s=null;"get"===i?n.search=o:s=a;let{signal:l}=$=new AbortController,c=new Request(n.toString(),{method:i,body:s,signal:l,headers:{Accept:"text/html","X-Requested-With":"XMLHttpRequest"}}),u=null;try{u=await fetch(c)}catch{}if(t.classList.remove("is-sending"),!u||!u.ok||l.aborted)return;let d=t.getAttribute("data-autosearch-results-container"),f=d?document.getElementById(d):null;if(f){let e=f.style.height;f.style.height=getComputedStyle(f).height,f.textContent="",void 0!==r&&clearTimeout(r);let t=f.hasAttribute("data-delay-results"),n=await u.text(),i=(0,N.r)(document,n).querySelector("[data-autosearch-results]")||(0,N.r)(document,n).firstElementChild;r=setTimeout(()=>{f.appendChild((0,N.r)(document,n)),(0,k.N)(i),requestAnimationFrame(()=>{f.style.height=e})},t?500:0)}(0,P.lO)(null,"",`?${o}`)});var I=n(14873),O=n(254);(0,O.ZG)("input[data-autoselect], textarea[data-autoselect]",async function(e){await (0,I.gJ)(),e.select()});var D=n(46263),B=n(56959);function W(e){let t=e.target;if(!(t instanceof HTMLInputElement)&&!(t instanceof HTMLSelectElement))return;let n=t.form;(0,H.Bt)(n)}(0,b.on)("change","form[data-autosubmit]",function(e){let t=e.currentTarget;(0,H.Bt)(t)}),(0,b.on)("change","input[data-autosubmit], select[data-autosubmit]",W);let F=(0,D.D)(W,300);async function U(e){let t=e.getAttribute("data-url")||"",n=await z(t);if(n){let t=e.getAttribute("data-gravatar-text");null!=t&&(e.textContent=t)}}async function z(e){if(!e)return!1;try{let t=await fetch(e,{headers:{Accept:"application/json"}});if(!t.ok)return!1;let n=await t.json();return n.has_gravatar}catch{return!1}}(0,A.N7)("input[data-throttled-autosubmit]",{subscribe:e=>(0,B.RB)(e,"input",F)}),(0,A.N7)(".js-detect-gravatar",function(e){U(e)});var V=n(47442),G=n(68423),X=n(97629),Z=n(75198);let K=[".unstyled-additional-seats-price-obj",".unstyled-base-price-obj",".unstyled-final-price-obj"];function J(e){return"string"!=typeof e&&"number"!=typeof e&&"default_currency"in e&&"local_currency"in e}let Y=null;async function Q(e){let t=e.getAttribute("data-item-name")||"items",n=e.value,r=new URL(e.getAttribute("data-url"),window.location.origin),i=new URLSearchParams(r.search.slice(1)),a=parseInt(e.getAttribute("data-item-minimum"))||0,o=parseInt(e.getAttribute("data-item-maximum"))||1e6,s=parseInt(e.getAttribute("data-item-count"))||0,l=Math.max(a,parseInt(n)||0),c=l>o,u=document.querySelector(".js-downgrade-button"),d=document.getElementById("downgrade-disabled-message");u instanceof HTMLButtonElement&&(u.disabled=l===s),d instanceof HTMLElement&&u instanceof HTMLButtonElement&&(d.hidden=!u.disabled),i.append(t,l.toString());let f=document.querySelector(".js-transform-user");f&&i.append("transform_user","1"),r.search=i.toString(),Y?.abort();let{signal:m}=Y=new AbortController,h=null;try{let e=await fetch(r.toString(),{signal:m,headers:{Accept:"application/json"}});if(!e.ok)return;h=await e.json()}catch{}if(m.aborted||!h)return;let p=document.querySelector(".js-contact-us");p&&p.classList.toggle("d-none",!c);let g=document.querySelector(".js-cost-info");g&&(g.hidden=c);let b=document.querySelector(".js-payment-summary");b&&b.classList.toggle("d-none",c);let y=document.querySelector(".js-submit-billing");y instanceof HTMLElement&&(y.hidden=c);let v=document.querySelector(".js-billing-section");v&&v.classList.toggle("has-removed-contents",h.free||h.is_enterprise_cloud_trial);let w=document.querySelector(".js-upgrade-info");w&&w.classList.toggle("d-none",l<=0);let S=document.querySelector(".js-downgrade-info");S&&S.classList.toggle("d-none",l>=0);let E=document.querySelector(".js-extra-seats-line-item");E&&E.classList.toggle("d-none",h.no_additional_seats);let L=document.querySelector(".js-seat-field");L&&ei(n);let j=document.querySelector(".js-minimum-seats-disclaimer");j&&(j.classList.toggle("tooltipped",5===h.seats),j.classList.toggle("tooltipped-nw",5===h.seats));let A=h.selectors;for(let e in A)for(let t of document.querySelectorAll(e))ee(e)&&J(A[e])?(t.textContent="",t.appendChild(et("default-currency",A[e].default_currency)),t.appendChild(et("local-currency",A[e].local_currency))):t.textContent=A[e];(0,P.lO)(history.state,"",h.url)}function ee(e){return K.includes(e)}function et(e,t){let n=document.createElement("span");return n.classList.add(e),n.textContent=t,n}function en(){for(let e of document.querySelectorAll(".js-unit-price"))e.hidden=!e.hidden}function er(e){let t="year"===e?"month":"year";for(let t of document.querySelectorAll(".js-plan-duration-text"))t.textContent=e;for(let t of document.querySelectorAll(".unstyled-available-plan-duration-adjective"))t.textContent=`${e}ly`;for(let e of document.querySelectorAll(".js-org-signup-duration-change"))e.setAttribute("data-plan-duration",t);let n=document.getElementById("signup-plan-duration");n&&(n.value=e)}function ei(e){for(let t of document.querySelectorAll(".js-seat-field")){let n=t.getAttribute("data-item-maximum"),r=t?.parentNode?.querySelector(".Popover");n&&n.length&&(parseInt(e,10)>parseInt(n,10)?(t.classList.add("color-border-danger-emphasis"),r?.removeAttribute("hidden")):(t.classList.remove("color-border-danger-emphasis"),r?.setAttribute("hidden","true")))}}function ea(e){for(let t of document.querySelectorAll(".js-seat-field")){let n=new URL(t.getAttribute("data-url"),window.location.origin),r=new URLSearchParams(n.search.slice(1));r.delete("plan_duration"),r.append("plan_duration",e),n.search=r.toString(),t.setAttribute("data-url",n.toString())}}function eo(e){let t=document.querySelector(".js-addon-purchase-field"),n=e.target.querySelector("input:checked");if(t instanceof HTMLInputElement&&n instanceof HTMLInputElement){let e=n.getAttribute("data-upgrade-url");e&&(t.setAttribute("data-url",e),t.value="0",Q(t))}}function es(e,t=!1){for(let[n,r]of Object.entries({"tooltipped-nw":"tooltipped-sw","tooltipped-n":"tooltipped-s","tooltipped-ne":"tooltipped-se"})){let i=t?r:n,a=t?n:r;for(let t of e.querySelectorAll(`.${i}`))t.classList.replace(i,a)}}function el(e){let t=e.target,n=t?.closest(".js-branch-protection-integration-select"),r=n?.querySelector(".js-branch-protection-integration-select-current"),i=t?.closest(".js-branch-protection-integration-select-item"),a=i?.querySelector(".js-branch-protection-integration-select-label");r&&a&&n&&(r.innerHTML=a.innerHTML,n.open=!1)}function ec(e){let t=new URL(e.getAttribute("data-bulk-actions-url"),window.location.origin),n=new URLSearchParams(t.search.slice(1)),r=e.getAttribute("data-bulk-actions-parameter"),i=Array.from(e.querySelectorAll(".js-bulk-actions-toggle:checked"));if(r){let e=i.map(e=>e.closest(".js-bulk-actions-item").getAttribute("data-bulk-actions-id")).sort();for(let t of e)n.append(`${r}[]`,t)}else for(let e of i.sort((e,t)=>e.value>t.value?1:-1))n.append(e.name,e.value);return t.search=n.toString(),t.toString()}(0,b.on)("click",".js-org-signup-duration-change",e=>{e.preventDefault();let t=e.currentTarget,n=t.getAttribute("data-plan-duration");for(let e of(er(n),ea(n),document.querySelectorAll(".js-seat-field")))Q(e);en()}),(0,b.on)("change",".js-org-signup-duration-toggle",function({currentTarget:e}){let t=new URL(e.getAttribute("data-url"),window.location.origin);(0,Z.softNavigate)(t.toString())}),(0,A.N7)(".js-addon-purchase-field",{constructor:HTMLInputElement,add(e){(0,X.Z)(e)&&Q(e),(0,G.oq)(e,function(){Q(e)})}}),(0,A.N7)(".js-addon-downgrade-field",{constructor:HTMLSelectElement,add(e){(0,X.Z)(e)&&Q(e),e.addEventListener("change",function(){Q(e)})}}),(0,b.on)("details-menu-selected",".js-organization-container",eo,{capture:!0}),(0,O.q6)(".js-csv-filter-field",function(e){let t=e.target.value.toLowerCase();for(let e of document.querySelectorAll(".js-csv-data tbody tr"))e instanceof HTMLElement&&e.textContent&&(e.hidden=!!t&&!e.textContent.toLowerCase().includes(t))}),(0,A.N7)(".js-blob-header.is-stuck",{add(e){es(e)},remove(e){es(e,!0)}}),(0,b.on)("click",".js-blob-dropdown-click",e=>{let t=e.currentTarget.getAttribute("data-dropdown-tracking");if(!t)return;let n=JSON.parse(t);(0,v.qP)(n.type,n.context)}),(0,b.on)("change",".js-branch-protection-integration-select-input",el);let eu=null;async function ed(e){let t=e.target;if(!(t instanceof HTMLElement))return;let n=t.querySelector(".js-bulk-actions"),r=!!t.querySelector(".js-bulk-actions-toggle:checked");eu?.abort();let{signal:i}=eu=new AbortController,a="";try{let e=await fetch(ec(t),{signal:i,headers:{"X-Requested-With":"XMLHttpRequest"}});if(!e.ok)return;a=await e.text()}catch{}!i.aborted&&a&&(r?(ef(t),n.innerHTML=a):(n.innerHTML=a,ef(t)),(0,b.f)(t,"bulk-actions:updated"))}function ef(e){let t=document.querySelector(".js-membership-tabs");if(t){let n=e.querySelectorAll(".js-bulk-actions-toggle:checked");t.classList.toggle("d-none",n.length>0)}}(0,b.on)("change",".js-bulk-actions-toggle",function(e){let t=e.currentTarget,n=t.closest(".js-bulk-actions-container");(0,b.f)(n,"bulk-actions:update")}),(0,b.on)("bulk-actions:update",".js-bulk-actions-container",(0,D.D)(ed,100));var em=n(4412);function eh(e){try{let t=window.localStorage.getItem(e);return{kind:"ok",value:t?JSON.parse(t):null}}catch(e){return{kind:"err",value:e}}}function ep(e,t){try{return window.localStorage.setItem(e,JSON.stringify(t)),{kind:"ok",value:null}}catch(e){return{kind:"err",value:e}}}function eg(){let e={};for(let t of document.getElementsByTagName("script")){let n=t.src.match(/\/([\w-]+)-[0-9a-f]{8,}\.js$/);n&&(e[`${n[1]}.js`]=t.src)}for(let t of document.getElementsByTagName("link")){let n=t.href.match(/\/([\w-]+)-[0-9a-f]{8,}\.css$/);n&&(e[`${n[1]}.css`]=t.href)}return e}function eb(){let e=eg(),t=eh("bundle-urls");if("err"===t.kind){ep("bundle-urls",e);return}let n=t.value||{},r=Object.keys(e).filter(t=>n[t]!==e[t]);if(r.length){let t=ep("bundle-urls",{...n,...e});"ok"===t.kind&&(0,T.b)({downloadedBundles:r})}}(async()=>{await em.C,window.requestIdleCallback(eb)})();var ey=n(90596);function ev(e){e.preventDefault(),e.stopPropagation()}(0,A.N7)("a.btn.disabled",{subscribe:e=>(0,B.RB)(e,"click",ev)});var ew=n(78332),eS=n(58797);(0,A.N7)(".js-check-all-container",{constructor:HTMLElement,subscribe:eS.Z});var eE=n(67404);let eL="logout-was-successful";function ej(){for(let e of[sessionStorage,localStorage])try{e.clear()}catch{}}function eA(e){eq.delete(e),eT(e)}function eT(e){let t=e.querySelector(".js-clipboard-copy-icon"),n=e.querySelector(".js-clipboard-check-icon");e.classList.toggle("ClipboardButton--success"),t&&t.classList.toggle("d-none"),n&&(n.classList.contains("d-sm-none")?n.classList.toggle("d-sm-none"):n.classList.toggle("d-none"))}(0,eE.$1)(eL).length>0&&(ej(),(0,eE.kT)(eL)),(0,b.on)("clipboard-copy","[data-copy-feedback]",e=>{let t=e.currentTarget,n=t.getAttribute("data-copy-feedback"),r=t.getAttribute("aria-label"),i=t.getAttribute("data-tooltip-direction")||"s";t.setAttribute("aria-label",n),t.classList.add("tooltipped",`tooltipped-${i}`),t instanceof HTMLElement&&((0,k.N)(t),setTimeout(()=>{r?t.setAttribute("aria-label",r):t.removeAttribute("aria-label"),t.classList.remove("tooltipped",`tooltipped-${i}`)},2e3))});let eq=new WeakMap;(0,b.on)("clipboard-copy",".js-clipboard-copy:not([data-view-component])",function({currentTarget:e}){if(!(e instanceof HTMLElement))return;let t=eq.get(e);t?clearTimeout(t):eT(e),eq.set(e,window.setTimeout(eA,2e3,e))});var ek=n(24601);async function eC(e,t,n,r,i){let a=e.getAttribute("data-tagsearch-url");if(!a)return"";let o=e.getAttribute("data-tagsearch-ref");if(!o)return"";let s=e.getAttribute("data-tagsearch-code-nav-context");s||(s="UNKNOWN_VIEW");let l=new URL(a,window.location.origin),c=new URLSearchParams;c.set("q",t),c.set("blob_path",i),c.set("ref",o),c.set("language",n),c.set("row",r[0].toString()),c.set("col",r[1].toString()),c.set("code_nav_context",s),l.search=c.toString();try{let e=await fetch(l.toString(),{headers:{"X-Requested-With":"XMLHttpRequest"}});if(!e.ok)return"";let t=await e.text();if(/js-tagsearch-no-definitions/.test(t))return"";return t}catch{return""}}function eM(e,t,n){let r,i;if(document.caretPositionFromPoint){let e=document.caretPositionFromPoint(t,n);e&&(r=e.offsetNode,i=e.offset)}else if(document.caretRangeFromPoint){let e=document.caretRangeFromPoint(t,n);e&&(r=e.startContainer,i=e.startOffset)}if(!r||"number"!=typeof i||r.nodeType!==Node.TEXT_NODE)return;let a=r.textContent,o=""===a?.replaceAll("\n","").trim();if(!a||o||r.nodeType!==Node.TEXT_NODE)return null;let s=ex(a,e,i);if(!s)return null;let l=document.createRange();return l.setStart(r,s[1]),l.setEnd(r,s[2]),l}function ex(e,t,n){let r;let i=null;for(;r=t.exec(e);){if(t.lastIndex===i){(0,ek.eK)(Error("regexp did not advance in findNearestMatch()"));break}i=t.lastIndex;let e=r.index+r[0].length;if(r.index<=n&&n<=e)return[r[0],r.index,e]}return null}function e_(e){let t=e.closest(".highlight");if(t)for(let e of t.classList)switch(e){case"highlight-source-go":return"Go";case"highlight-source-js":return"JavaScript";case"highlight-source-python":return"Python";case"highlight-source-ruby":return"Ruby";case"highlight-source-ts":return"TypeScript"}return null}function eR(e){let t=e.startContainer,n=e.startOffset,r=!1;for(;;){let e=t.previousSibling;for(;!r&&e;)["#comment","BUTTON"].includes(e.nodeName)||(n+=(e.textContent||"").length),e=e.previousSibling;let i=t.parentElement;if(!i)return[0,0];if(i.classList.contains("js-code-nav-pass"))r=!0;else if(i.classList.contains("js-file-line")){let e=i.previousElementSibling;if(!e.classList.contains("js-code-nav-line-number"))throw Error("invariant");let t=parseInt(e.getAttribute("data-line-number")||"1",10);return[t-1,n]}t=i}}(0,b.on)("click",".js-code-nav-retry",async function(e){let t;if(e.altKey||e.ctrlKey||e.metaKey||e.shiftKey)return;let n=document.querySelector(".js-tagsearch-popover");if(!n)return;let r=n.querySelector(".js-tagsearch-popover-content");if(!r)return;let i=e.currentTarget,a=i.getAttribute("data-code-nav-kind");if(!(t="definitions"===a?n.querySelector(".js-tagsearch-popover-content"):n.querySelector(".js-code-nav-references")))return;let o=i.getAttribute("data-code-nav-url");if(!o)return;let s=new URL(o,window.location.origin);try{let e=await fetch(s.toString(),{headers:{"X-Requested-With":"XMLHttpRequest"}});if(!e.ok)return;let n=await e.text();if(!n)return;t.innerHTML=n}catch{return}r.scrollTop=0}),(0,A.N7)(".js-code-nav-container",{constructor:HTMLElement,subscribe(e){let t;let n=document.querySelector(".js-tagsearch-popover");if(!(n instanceof HTMLElement))return{unsubscribe(){}};let r=n.querySelector(".js-tagsearch-popover-content"),i=new WeakMap,a=new WeakMap;async function o(o){let m=eM(/\w+[!?]?/g,o.clientX,o.clientY);if(!m)return;let h=m.commonAncestorContainer.parentElement;if(!h)return;for(let e of h.classList)if(["pl-token","pl-c","pl-s","pl-k"].includes(e))return;if(h.closest(".js-skip-tagsearch"))return;let p=m.toString();if(!p||p.match(/\n|\s|[();&.=",]/))return;let g=a.get(h);if(g||(g=new Set,a.set(h,g)),g.has(p))return;g.add(p);let b=h.closest(".js-tagsearch-file");if(!b)return;let y=b.getAttribute("data-tagsearch-path")||"",v=b.getAttribute("data-tagsearch-lang")||"";if("HTML+ERB"===v){if(!h.closest(".pl-sre"))return;v="Ruby"}if(e.classList.contains("js-code-block-container")&&!(v=e_(h)||""))return;let w=eR(m),S=document.createElement("span");m.surroundContents(S),S.classList.add("pl-token"),S.addEventListener("click",async function(e){if(!e.altKey&&!e.ctrlKey&&!e.metaKey&&!e.shiftKey){if(S===t)c();else{let e;if(!(e=await eC(n,p,v,w,y)))return;(function(e,t){let n=document.createElement("span");n.innerHTML=t;let r=n.firstElementChild;if(!r)return;let i=r.getAttribute("data-hydro-click"),a=r.getAttribute("data-hydro-click-hmac");a&&i&&(e.setAttribute("data-hydro-click",i),e.setAttribute("data-hydro-click-hmac",a))})(S,e),i.set(S,e),t&&t.classList.remove("active"),(t=S).classList.add("active"),r.innerHTML=i.get(S)||"",l(S),function(){if(!n.hidden){s();return}n.hidden=!1,s(),document.addEventListener("click",d),document.addEventListener("keyup",f),window.addEventListener("resize",u)}()}e.preventDefault()}})}function s(){r.scrollTop=0}function l(t){let r=e.getClientRects()[0],i=t.getClientRects();if(0===i.length)return;let a=i[0];n.style.position="absolute",n.style.zIndex="3",e.classList.contains("position-relative")?(n.style.top=`${a.bottom-r.top+7}px`,n.style.left=`${a.left-r.left-10}px`):(n.style.top=`${window.scrollY+a.bottom}px`,n.style.left=`${window.scrollX+a.left}px`)}function c(){n.hidden||(n.hidden=!0,t&&t.classList.remove("active"),t=void 0,document.removeEventListener("click",d),document.removeEventListener("keyup",f),window.removeEventListener("resize",u))}function u(){t instanceof HTMLElement&&l(t)}function d(e){let{target:r}=e;r instanceof Node&&!n.contains(r)&&!t.contains(r)&&c()}function f(e){"Escape"===e.key&&c()}return function(){for(let e of(c(),document.getElementsByClassName("pl-token")))e.classList.remove("pl-token","active")}(),e.addEventListener("mousemove",o),{unsubscribe(){e.removeEventListener("mousemove",o)}}}});var eH=n(22971);function eN(e){let t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}function eP(e){let t=e.querySelector(".js-comment-form-error");t instanceof HTMLElement&&(t.hidden=!0)}function e$(e,t){let n="You can't comment at this time";if(t.response&&422===t.response.status){let e=t.response.json;e.errors&&(Array.isArray(e.errors)?n+=` \u{2014} your comment ${e.errors.join(", ")}`:n=e.errors)}n+=". ";let r=e.querySelector(".js-comment-form-error");if(r instanceof HTMLElement){r.textContent=n,r.hidden=!1;let e=r.closest("div.form-group.js-remove-error-state-on-click");e&&e.classList.add("errored")}}(0,b.on)("click",".errored.js-remove-error-state-on-click",function({currentTarget:e}){e.classList.remove("errored")}),(0,y.AC)(".js-new-comment-form",async function(e,t){let n;eP(e);try{n=await t.json()}catch(t){e$(e,t)}if(!n)return;for(let t of(e.reset(),e.querySelectorAll(".js-resettable-field")))(0,H.Se)(t,t.getAttribute("data-reset-value")||"");let r=e.querySelector(".js-write-tab");if(r instanceof HTMLElement){let e=eN(r);e&&r.click()}let i=n.json.updateContent;for(let e in i){let t=i[e],n=document.querySelector(e);n instanceof HTMLElement?(0,eH.Of)(n,t):console.warn(`couldn't find ${e} for immediate update`)}(0,b.f)(e,"comment:success")});let eI=(e,t)=>{let n=e.querySelector(".js-form-action-text"),r=n||e;r.textContent=t?e.getAttribute("data-comment-text"):r.getAttribute("data-default-action-text")},eO=e=>{let t;return n=>{let r=n.currentTarget,i=r.value.trim();i!==t&&(t=i,eI(e,Boolean(i)))}};(0,A.N7)(".js-comment-and-button",{constructor:HTMLButtonElement,initialize(e){let t=e.form.querySelector(".js-comment-field"),n=eO(e);return{add(){t.addEventListener("input",n),t.addEventListener("change",n)},remove(){t.removeEventListener("input",n),t.removeEventListener("change",n)}}}});var eD=n(66602);function eB(e,t){let n=e.closest(".js-write-bucket");n&&n.classList.toggle("focused",t)}function eW(e){let t=e.currentTarget;t instanceof Element&&eB(t,!1)}(0,O.ZG)(".js-comment-field",function(e){eB(e,!0),e.addEventListener("blur",eW,{once:!0})});var eF=n(57619),eU=n(52769),ez=n(54697);let eV=class PNGScanner{static fromFile(e){return new Promise(function(t,n){let r=new FileReader;r.onload=function(){t(new PNGScanner(r.result))},r.onerror=function(){n(r.error)},r.readAsArrayBuffer(e)})}advance(e){this.pos+=e}readInt(e){let t=this,n=function(){switch(e){case 1:return t.dataview.getUint8(t.pos);case 2:return t.dataview.getUint16(t.pos);case 4:return t.dataview.getUint32(t.pos);default:throw Error("bytes parameter must be 1, 2 or 4")}}();return this.advance(e),n}readChar(){return this.readInt(1)}readShort(){return this.readInt(2)}readLong(){return this.readInt(4)}readString(e){let t=[];for(let n=0;n<e;n++)t.push(String.fromCharCode(this.readChar()));return t.join("")}scan(e){if(2303741511!==this.readLong())throw Error("invalid PNG");for(this.advance(4);;){let t=this.readLong(),n=this.readString(4),r=this.pos+t+4;if(!1===e.call(this,n,t)||"IEND"===n)break;this.pos=r}}constructor(e){this.dataview=new DataView(e),this.pos=0}};async function eG(e){if("image/png"!==e.type)return null;let t=e.slice(0,10240,e.type),n=await eV.fromFile(t),r={width:0,height:0,ppi:1};return n.scan(function(e){switch(e){case"IHDR":r.width=this.readLong(),r.height=this.readLong();break;case"pHYs":{let e;let t=this.readLong(),n=this.readLong(),i=this.readChar();return 1===i&&(e=.0254),e&&(r.ppi=Math.round((t+n)/2*e)),!1}case"IDAT":return!1}return!0}),r}var eX=n(89900);let eZ=new WeakMap,eK=class CaretPosition{get top(){return this.coords.top}get left(){return this.coords.left}get height(){return this.coords.height}currentChar(e=1){return this.textArea.value.substring(this.index-e,this.index)}checkLine(e){return e<this.coords.top?-1:e>this.coords.top+this.coords.height?1:0}xDistance(e){return Math.abs(this.left-e)}constructor(e,t,n){this.index=e,this.coords=t,this.textArea=n}};function eJ(e,t){let n;if(eZ.has(e)?n=eZ.get(e):(n=new Map,eZ.set(e,n)),n.has(t))return n.get(t);{let r=new eK(t,(0,eX.Z)(e,t),e);return n.set(t,r),r}}let eY=(e,t,n,r,i,a)=>{if(n===t)return n;let o=e=>{let t=e.filter(e=>0===e.checkLine(i)).sort((e,t)=>e.xDistance(r)>t.xDistance(r)?1:-1);return 0===t.length?n:t[0].index};if(n-t==1){let r=eJ(e,t),i=eJ(e,n);return o([r,i])}if(n-t==2){let r=eJ(e,t),i=eJ(e,n-1),a=eJ(e,n);return o([r,i,a])}let s=Math.floor((n+t)/2);if(s===t||s===n)return s;let l=eJ(e,s);return i>l.top+l.height?eY(e,s+1,n,r,i,a+1):i<l.top?eY(e,t,s-1,r,i,a+1):3>l.xDistance(r)?s:l.left<r?0!==eJ(e,s+1).checkLine(i)?s:eY(e,s+1,n,r,i,a+1):l.left>r?0!==eJ(e,s-1).checkLine(i)?s:eY(e,t,s-1,r,i,a+1):s},eQ=(e,t,n)=>{let r=e.value.length;return eY(e,0,r,t,n,0)};function e0(e,t,n){let r=eQ(e,t,n);e.setSelectionRange(r,r)}function e1(e,t){let n=e.getBoundingClientRect();"dragenter"===t.type&&eZ.delete(e);let r=t.clientX-n.left,i=t.clientY-n.top+e.scrollTop;e0(e,r,i)}var e4=n(49421);let e7=new Map;(0,A.N7)(".js-paste-markdown",{initialize(e){let t;let n=e.hasAttribute("data-paste-url-links-as-plain-text");return{add(){t=(0,eU.Ld)(e,{defaultPlainTextPaste:{urlLinks:n}}).unsubscribe},remove(){t()}}}});let e2=new WeakMap;function e5(e){return e2.get(e)||ts(e)}function e3(e){return["video/mp4","video/quicktime"].includes(e.file.type)}function e9(e){return e.replace(/[[\]\\"<>&]/g,".").replace(/\.{2,}/g,".").replace(/^\.|\.$/gi,"")}function e6(e){if(e3(e))return`
Uploading ${e.file.name}\u{2026}
`;let t=e.isImage()?"!":"";return`${t}[Uploading ${e.file.name}\u{2026}]()`}function e8(e){return e9(e).replace(/\.[^.]+$/,"").replace(/\./g," ")}function te(e){let t=e.target.closest("form");if(t){let e=t.querySelector(".btn-primary");e&&(e.disabled=!0)}}function tt(e){let t=e.target.closest("form");if(t){let e=t.querySelector(".btn-primary");e&&(e.disabled=!1)}}async function tn(e){let{attachment:t}=e.detail,n=e.currentTarget;tc("",t.isImage()?await ta(t):e3(t)?ti(t):tr(t),e,n),e7.size>0&&tf()}function tr(e){return`[${e.file.name}](${e.href})`}function ti(e){return`
${e.href}
`}async function ta(e){let t=await to(e.file),n=e8(e.file.name),r=e.href;if(144===t.ppi){let e=Math.round(t.width/2);return`<img width="${e}" alt="${n}" src="${r}">`}return`![${n}](${r})`}async function to(e){let t={width:0,height:0,ppi:0};try{return await eG(e)??t}catch{return t}}function ts(e){let t=e6(e);return e3(e)?`
${t}
`:`${t}
`}function tl(e){let t=e.currentTarget.querySelector(".js-comment-field"),n=e5(e.detail.attachment);if(t)t.setCustomValidity(""),(0,eF.lp)(t,n,"");else{let t=tu(e.currentTarget);if(!t){(0,e4.a)("upload:editor:change",e.currentTarget,{state:"failed",placeholder:n,replacementText:""});return}let r=t.getSearchCursor(n);r.findNext(),r.replace("")}}function tc(e,t,n,r){let i=(r||n.currentTarget).querySelector(".js-comment-field"),a=(r||n.currentTarget).querySelector(".js-file-upload-loading-text"),o=e6(n.detail.attachment),{batch:s}=n.detail;if(i){let r=i.value.substring(i.selectionStart,i.selectionEnd);if("uploading"===e){let e;e=r.length?(0,eF.t4)(i,r,o):(0,eF.Om)(i,o,{appendNewline:!0}),e2.set(n.detail.attachment,e)}else i.value.includes(o)||e7.set(o,t),(0,eF.lp)(i,o,t,document.activeElement===i);s.isFinished()?tt(n):te(n)}else{let i=tu(r||n.currentTarget);if(i){if("uploading"===e){if(i.getSelection().length)i.replaceSelection(o);else{let e=i.getCursor(),t=ts(n.detail.attachment);i.replaceRange(t,e)}}else{let e=i.getSearchCursor(o);e.findNext(),e.replace(t)}}else(0,e4.a)("upload:editor:change",r||n.currentTarget,{state:""===e?"uploaded":"uploading",placeholder:o,replacementText:""===e?t:ts(n.detail.attachment)});s.isFinished()?tt(n):te(n)}if(a){let e=a.getAttribute("data-file-upload-message");a.textContent=`${e} (${s.uploaded()+1}/${s.size})`}}function tu(e){let t=e.querySelector(".js-code-editor");if(!t)return;let n=(0,ez.P)(t);if(n)return n.editor}function td(e){e.stopPropagation();let t=e.currentTarget;if(!t)return;let n=t.querySelector(".js-comment-field");if(n)e1(n,e);else{let n=tu(t);if((0,e4.a)("upload:editor:cursor",t,{left:e.clientX,top:e.clientY}),n){let t=n.coordsChar({left:e.pageX,top:e.pageY});n.setCursor(t),n.focus()}}}function tf(){let e=document.querySelectorAll(".issue-form-textarea");for(let t of e)for(let[e,n]of e7)t.value.includes(e)&&((0,eF.lp)(t,e,n,document.activeElement===t),e7.delete(e))}(0,b.on)("upload:setup",".js-upload-markdown-image",function(e){tc("uploading","",e)}),(0,b.on)("upload:complete",".js-upload-markdown-image",tn),(0,b.on)("upload:error",".js-upload-markdown-image",function(e){tl(e);let{batch:t}=e.detail;t.isFinished()?tt(e):te(e)}),(0,b.on)("dragenter","file-attachment",td),(0,b.on)("dragover","file-attachment",td),(0,b.on)("upload:invalid",".js-upload-markdown-image",function(e){tl(e);let{batch:t}=e.detail;t.isFinished()?tt(e):te(e)});var tm=n(29501),th=n(15205);function tp(e){let t=e.querySelector(".js-data-preview-url-csrf"),n=e.closest("form").elements.namedItem("authenticity_token");if(t instanceof HTMLInputElement)return t.value;if(n instanceof HTMLInputElement)return n.value;throw Error("Comment preview authenticity token not found")}function tg(e){let t=e.closest(".js-previewable-comment-form"),n=e.classList.contains("js-preview-tab");if(n){let e=t.querySelector(".js-write-bucket"),n=t.querySelector(".js-preview-body");e.clientHeight>0&&(n.style.minHeight=`${e.clientHeight}px`)}t.classList.toggle("preview-selected",n),t.classList.toggle("write-selected",!n);let r=t.querySelector('.tabnav-tab.selected, .tabnav-tab[aria-selected="true"]');r.setAttribute("aria-selected","false"),r.classList.remove("selected"),e.classList.add("selected"),e.setAttribute("aria-selected","true");let i=t.querySelector(".js-write-tab");return n?i.setAttribute("data-hotkey","Control+P,Meta+Shift+p"):i.removeAttribute("data-hotkey"),t}function tb(e){let t=e.querySelector(".js-comment-field").value,n=e.querySelector(".js-path")?.value,r=e.querySelector(".js-line-number")?.value,i=e.querySelector(".js-start-line-number")?.value,a=e.querySelector(".js-side")?.value,o=e.querySelector(".js-start-side")?.value,s=e.querySelector(".js-start-commit-oid")?.value,l=e.querySelector(".js-end-commit-oid")?.value,c=e.querySelector(".js-base-commit-oid")?.value,u=e.querySelector(".js-comment-id")?.value,d=new FormData;return d.append("text",t),d.append("authenticity_token",tp(e)),n&&d.append("path",n),r&&d.append("line_number",r),i&&d.append("start_line_number",i),a&&d.append("side",a),o&&d.append("start_side",o),s&&d.append("start_commit_oid",s),l&&d.append("end_commit_oid",l),c&&d.append("base_commit_oid",c),u&&d.append("comment_id",u),d}function ty(e){let t=e.getAttribute("data-preview-url"),n=tb(e);return(0,b.f)(e,"preview:setup",{data:n}),tv(t,n)}(0,b.on)("click",".js-write-tab",function(e){let t=e.currentTarget,n=t.closest(".js-previewable-comment-form");if(n instanceof tm.Z){setTimeout(()=>{n.querySelector(".js-comment-field").focus()});return}let r=tg(t);(0,b.f)(n,"preview:toggle:off");let i=n.querySelector(".js-discussion-poll-form-component");i&&(0,b.f)(i,"poll-preview:toggle:off"),setTimeout(()=>{r.querySelector(".js-comment-field").focus()});let a=n.querySelector("markdown-toolbar");a instanceof HTMLElement&&(a.hidden=!1)}),(0,b.on)("click",".js-preview-tab",function(e){let t=e.currentTarget,n=t.closest(".js-previewable-comment-form");if(n instanceof tm.Z)return;let r=tg(t);(0,b.f)(n,"preview:toggle:on"),setTimeout(()=>{tL(r)});let i=n.querySelector("markdown-toolbar");i instanceof HTMLElement&&(i.hidden=!0),e.stopPropagation(),e.preventDefault()}),(0,b.on)("tab-container-change",".js-previewable-comment-form",function(e){let t=e.detail.relatedTarget,n=t&&t.classList.contains("js-preview-panel"),r=e.currentTarget,i=r.querySelector(".js-write-tab");if(n){let e=r.querySelector(".js-write-bucket"),t=r.querySelector(".js-preview-body"),n=t.hasAttribute("data-skip-sizing");!n&&e.clientHeight>0&&(t.style.minHeight=`${e.clientHeight}px`),i.setAttribute("data-hotkey","Control+P,Meta+Shift+p"),tL(r);let a=r.querySelector("markdown-toolbar");a instanceof HTMLElement&&(a.hidden=!0)}else{i.removeAttribute("data-hotkey");let e=r.querySelector("markdown-toolbar");e instanceof HTMLElement&&(e.hidden=!1);let t=document.querySelector(".js-discussion-poll-form-component");t&&(0,b.f)(t,"poll-preview:toggle:off")}r.classList.toggle("preview-selected",n),r.classList.toggle("write-selected",!n)}),(0,b.on)("preview:render",".js-previewable-comment-form",function(e){let t=e.target.querySelector(".js-preview-tab"),n=tg(t);setTimeout(()=>{tL(n);let e=n.querySelector("markdown-toolbar");e instanceof HTMLElement&&(e.hidden=!0)})});let tv=(0,th.Z)(tS,{hash:tE}),tw=null;async function tS(e,t){tw?.abort();let{signal:n}=tw=new AbortController,r=await fetch(e,{method:"post",body:t,signal:n});if(!r.ok)throw Error("something went wrong");return r.text()}function tE(e,t){let n=[...t.entries()].toString();return`${e}:${n}`}async function tL(e){let t=e.querySelector(".comment-body");t.innerHTML="<p>Loading preview&hellip;</p>";try{let n=await ty(e);t.innerHTML=n||"<p>Nothing to preview</p>",(0,b.f)(e,"preview:rendered")}catch(e){"AbortError"!==e.name&&(t.innerHTML="<p>Error rendering preview</p>")}}(0,A.N7)(".js-preview-tab",function(e){e.addEventListener("mouseenter",async()=>{let t=e.closest(".js-previewable-comment-form");try{await ty(t)}catch(e){}})}),(0,O.w4)("keydown",".js-comment-field",function(e){let t=e.target;if((e.ctrlKey||e.metaKey)&&e.shiftKey&&"P"===e.key.toUpperCase()){let n=t.closest(".js-previewable-comment-form");n.classList.contains("write-selected")&&(n instanceof tm.Z?n.querySelector(".js-preview-tab").click():(t.blur(),n.dispatchEvent(new CustomEvent("preview:render",{bubbles:!0,cancelable:!1}))),e.preventDefault(),e.stopImmediatePropagation())}});let tj=/^(\+1|-1|:\+1?|:-1?)$/,tA=e=>{let t=!1;for(let n of e.split("\n")){let e=n.trim();if(!(!e||e.startsWith(">"))){if(t&&!1===tj.test(e))return!1;!t&&tj.test(e)&&(t=!0)}}return t};function tT(e){let t=e.target,n=t.value,r=t.closest(".js-reaction-suggestion");if(r){if(tA(n)){r.classList.remove("hide-reaction-suggestion"),r.classList.add("reaction-suggestion");let e=r.getAttribute("data-reaction-markup");r.setAttribute("data-reaction-suggestion-message",e)}else tq(r)}}function tq(e){e.classList.remove("reaction-suggestion"),e.classList.add("hide-reaction-suggestion"),e.removeAttribute("data-reaction-suggestion-message")}(0,b.on)("focusout","#new_comment_field",function(e){let t=e.currentTarget,n=t.closest(".js-reaction-suggestion");n&&tq(n)}),(0,b.on)("focusin","#new_comment_field",function(e){tT(e)}),(0,O.w4)("keyup","#new_comment_field",function(e){tT(e)});var tk=n(76134);(0,b.on)("navigation:keydown",".js-commits-list-item",function(e){(0,tk.Zf)(e.detail.originalEvent)&&e.target instanceof Element&&"c"===e.detail.hotkey&&e.target.querySelector(".js-navigation-open").click()});var tC=n(26361);(0,O.q6)(".js-company-name-input",function(e){let t=e.target,n=t.form,r=n.querySelector(".js-corp-tos-link"),i=n.querySelector(".js-tos-link");i&&(i.classList.add("d-none"),i.setAttribute("aria-hidden","true"),r&&(r.classList.remove("d-none"),r.setAttribute("aria-hidden","false")));let a=n.querySelectorAll(".js-company-name-text");if(0!==a.length)for(let e of a)if(t.value){let n=e.hasAttribute("data-wording");if(n){let n=e.getAttribute("data-wording");e.textContent=` ${n} ${t.value}`}else e.textContent=t.value}else e.textContent=""}),(0,A.N7)(".js-company-owned:not(:checked)",{constructor:HTMLInputElement,add(e){let t=e.form,n=t.querySelector(".js-company-name-input"),r=document.querySelector(".js-company-name-text"),i=document.querySelector(".js-corp-tos-link"),a=document.querySelector(".js-tos-link");n&&(e.getAttribute("data-optional")&&n.removeAttribute("required"),(0,H.Se)(n,"")),a.classList.remove("d-none"),a.setAttribute("aria-hidden","false"),i.classList.add("d-none"),i.setAttribute("aria-hidden","true"),r&&(r.textContent="")}}),(0,A.N7)(".js-company-owned:checked",{constructor:HTMLInputElement,add(e){let t=e.form,n=t.querySelector(".js-company-name-input");n&&(n.setAttribute("required",""),(0,b.f)(n,"focus"),(0,b.f)(n,"input"))}}),(0,A.N7)(".js-company-owned-autoselect",{constructor:HTMLInputElement,add(e){function t(){if(e.checked&&e.form){let t=e.form.querySelector(".js-company-owned");(0,H.Se)(t,!0)}}e.addEventListener("change",t),t()}});var tM=n(70763),tx=n(92792),t_=n(88309);function tR(e){let t=e.querySelectorAll("details-menu [role=menuitemradio] input[type=radio]:checked");for(let e of t)(0,b.f)(e,"change")}async function tH({currentTarget:e}){let t=e.hasAttribute("open");if(t){let t=e.querySelector(".js-filterable-field");t instanceof HTMLInputElement&&t.focus()}(0,b.f)(e,t?"menu:activate":"menu:deactivate"),await (0,I.gJ)(),(0,b.f)(e,t?"menu:activated":"menu:deactivated")}(0,A.N7)("details.select-menu details-menu include-fragment",function(e){let t=e.closest("details");t&&(e.addEventListener("loadstart",function(){t.classList.add("is-loading"),t.classList.remove("has-error")}),e.addEventListener("error",function(){t.classList.add("has-error")}),e.addEventListener("loadend",function(){t.classList.remove("is-loading");let e=t.querySelector(".js-filterable-field");e&&(0,b.f)(e,"filterable:change")}))}),(0,A.N7)("details details-menu .js-filterable-field",{constructor:HTMLInputElement,add(e){let t=e.closest("details");t.addEventListener("toggle",function(){t.hasAttribute("open")||(e.value="",(0,b.f)(e,"filterable:change"))})}}),(0,A.N7)("details-menu[role=menu] [role=menu]",e=>{let t=e.closest("details-menu[role]");t&&t!==e&&t.removeAttribute("role")}),(0,A.N7)("details details-menu remote-input input",{constructor:HTMLInputElement,add(e){let t=e.closest("details");t.addEventListener("toggle",function(){t.hasAttribute("open")||(e.value="")})}}),(0,A.N7)("form details-menu",e=>{let t=e.closest("form");t.addEventListener("reset",()=>{setTimeout(()=>tR(t),0)})}),(0,O.w4)("keypress","details-menu .js-filterable-field, details-menu filter-input input",e=>{if("Enter"===e.key){let t=e.currentTarget,n=t.closest("details-menu"),r=n.querySelector('[role^="menuitem"]:not([hidden])');r instanceof HTMLElement&&!r.classList.contains("select-menu-clear-item")&&r.click(),e.preventDefault()}}),(0,b.on)("details-menu-selected","details-menu",e=>{let t=e.currentTarget,n=t.querySelector(".js-filterable-field");n instanceof HTMLInputElement&&n.value&&n.focus()},{capture:!0}),(0,b.on)("details-menu-selected","[data-menu-input]",e=>{if(!(e.target instanceof Element))return;let t=e.target.getAttribute("data-menu-input"),n=document.getElementById(t);(n instanceof HTMLInputElement||n instanceof HTMLTextAreaElement)&&(n.value=e.detail.relatedTarget.value)},{capture:!0}),(0,A.N7)("details-menu remote-input",{constructor:t_.Z,initialize(e){let t=document.getElementById(e.getAttribute("aria-owns")||"");if(!t)return;let n=null;e.addEventListener("load",()=>{n=document.activeElement&&t.contains(document.activeElement)&&document.activeElement.id?document.activeElement.id:null}),e.addEventListener("loadend",()=>{if(n){let r=t.querySelector(`#${n}`)||t.querySelector('[role^="menu"]');r instanceof HTMLElement?r.focus():e.input&&e.input.focus()}})}}),(0,b.on)("details-menu-selected","details-menu[data-menu-max-options]",e=>{let t=+e.currentTarget.getAttribute("data-menu-max-options"),n=e.currentTarget.querySelectorAll('[role="menuitemcheckbox"][aria-checked="true"]'),r=t===n.length;for(let t of(e.currentTarget.querySelector("[data-menu-max-options-warning]").hidden=!r,e.currentTarget.querySelectorAll('[role="menuitemcheckbox"] input')))t.disabled=r&&!t.checked},{capture:!0}),(0,A.N7)("details > details-menu",{subscribe(e){let t=e.closest("details");return(0,B.RB)(t,"toggle",tH)}}),(0,A.N7)("details > details-menu[preload]:not([src])",{subscribe:e=>(0,B.RB)(e.parentElement,"mouseover",function(e){let t=e.currentTarget,n=t.querySelector("include-fragment[src]");n?.load()})}),(0,A.N7)("button[data-show-dialog-id]",e=>{e?.addEventListener("mouseenter",()=>{let t=e.getAttribute("data-show-dialog-id"),n=e.ownerDocument.getElementById(t);n?.querySelector("include-fragment[loading=lazy]")?.setAttribute("loading","eager")})}),(0,A.N7)("summary[data-show-dialog-id]",e=>{e?.addEventListener("click",()=>{let t=e.getAttribute("data-show-dialog-id");if(!t)return;let n=e.ownerDocument.getElementById(t);n?.show()})});let tN=new WeakMap;function tP(e){return e instanceof HTMLInputElement?e.value||"Submit":e.innerHTML||""}function t$(e){return[Array.from(e.querySelectorAll("input[type=submit][data-disable-with], button[data-disable-with]")),Array.from(document.querySelectorAll(`button[data-disable-with][form="${e.id}"]`))].flat()}function tI(e,t){e instanceof HTMLInputElement?e.value=t:e.innerHTML=t}function tO(e){for(let t of t$(e)){let n=tN.get(t);null!=n&&(tI(t,n),(!t.hasAttribute("data-disable-invalid")||e.checkValidity())&&(t.disabled=!1),tN.delete(t))}}(0,b.on)("submit","form",function(e){let t=e.currentTarget;for(let e of t$(t)){tN.set(e,tP(e));let t=e.getAttribute("data-disable-with");t&&tI(e,t),e.disabled=!0}},{capture:!0}),(0,b.on)("deprecatedAjaxComplete","form",function({currentTarget:e,target:t}){e===t&&tO(e)}),(0,y.uT)(tO);var tD=n(74721),tB=n(14992),tW=n(68202);function tF(e,t){let n=document.querySelector('.js-site-favicon[type="image/svg+xml"]'),r=document.querySelector('.js-site-favicon[type="image/png"]');t||(t="light");let a="light"===t?"":"-dark";if(n&&r){if(null==i&&(i=n.href),e){e=`${e=e.substr(0,e.lastIndexOf("."))}${a}.svg`,n.href=e;let t=n.href.substr(0,n.href.lastIndexOf("."));r.href=`${t}.png`}else{let e=n.href.indexOf("-dark.svg"),t=n.href.substr(0,-1!==e?e:n.href.lastIndexOf("."));n.href=`${t}${a}.svg`,r.href=`${t}${a}.png`}}}function tU(){return window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches}function tz(){null!=i&&tF(i,tU()?"dark":"light")}function tV(){tU()&&tF(void 0,"dark")}async function tG(e){let t=e.getAttribute("data-feature-preview-indicator-src"),n=await tX(t),r=e.querySelectorAll(".js-feature-preview-indicator");for(let e of r)e.hidden=!n}async function tX(e){try{let t=await fetch(e,{headers:{Accept:"application/json"}});if(!t.ok)return!1;let n=await t.json();return n.show_indicator}catch{return!1}}(0,A.N7)("[data-favicon-override]",{add(e){let t=e.getAttribute("data-favicon-override");setTimeout(()=>tF(t,tU()?"dark":"light"))},remove(){tz()}}),tV(),document.addEventListener(tW.QE.SUCCESS,tV),window.matchMedia("(prefers-color-scheme: dark)").addListener(()=>{tF(void 0,tU()?"dark":"light")}),(0,A.N7)(".js-feature-preview-indicator-container",e=>{tG(e)});var tZ=n(19146),tK=n(34892);(0,b.on)("click","[data-feature-preview-trigger-url]",async e=>{let t=e.currentTarget,n=t.getAttribute("data-feature-preview-trigger-url"),r=await (0,tZ.W)({content:(0,tK.a_)(document,n),dialogClass:"feature-preview-dialog"}),i=t.getAttribute("data-feature-preview-close-details"),a=t.getAttribute("data-feature-preview-close-hmac");r.addEventListener("dialog:remove",()=>{(0,T.b)({hydroEventPayload:i,hydroEventHmac:a},!0)});let o=document.querySelectorAll(".js-feature-preview-indicator");for(let e of o)e.hidden=!0}),(0,y.AC)(".js-feature-preview-unenroll",async(e,t)=>{await t.text();let n=e.querySelector(".js-feature-preview-slug").value;(0,b.f)(e,`feature-preview-unenroll:${n}`)}),(0,y.AC)(".js-feature-preview-enroll",async(e,t)=>{await t.text();let n=e.querySelector(".js-feature-preview-slug").value;(0,b.f)(e,`feature-preview-enroll:${n}`)});let tJ=class AttachmentUpload{async process(e){let t=window.performance.now(),n=new Headers(this.policy.header||{}),r=new XMLHttpRequest;for(let[e,t]of(r.open("POST",this.policy.upload_url,!0),n))r.setRequestHeader(e,t);r.onloadstart=()=>{e.attachmentUploadDidStart(this.attachment,this.policy)},r.upload.onprogress=t=>{if(t.lengthComputable){let n=Math.round(t.loaded/t.total*100);e.attachmentUploadDidProgress(this.attachment,n)}},await tY(r,tQ(this.attachment,this.policy)),204===r.status?(t0(this.policy),e.attachmentUploadDidComplete(this.attachment,this.policy,{})):201===r.status?(t0(this.policy),e.attachmentUploadDidComplete(this.attachment,this.policy,JSON.parse(r.responseText))):e.attachmentUploadDidError(this.attachment,{status:r.status,body:r.responseText});let i=window.performance.now(),a={duration:i-t,size:this.attachment?.file?.size,fileType:this.attachment?.file?.type,success:204===r.status||201===r.status};(0,T.b)({uploadTiming:a},!0)}constructor(e,t){this.attachment=e,this.policy=t}};function tY(e,t){return new Promise((n,r)=>{e.onload=()=>n(e),e.onerror=r,e.send(t)})}function tQ(e,t){let n=new FormData;for(let e in t.same_origin&&n.append("authenticity_token",t.upload_authenticity_token),t.form)n.append(e,t.form[e]);return n.append("file",e.file),n}function t0(e){let t="string"==typeof e.asset_upload_url?e.asset_upload_url:null,n="string"==typeof e.asset_upload_authenticity_token?e.asset_upload_authenticity_token:null;if(!(t&&n))return;let r=new FormData;r.append("authenticity_token",n),fetch(t,{method:"PUT",body:r,credentials:"same-origin",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}})}async function t1(e,t){let n=t5(e,t);for(let r of e.attachments){let i=await t4(e,r,t);if(!i)return;try{let e=new tJ(r,i);await e.process(n)}catch{e.setAttachmentAsFailed(r),(0,b.f)(t,"upload:error",{batch:e,attachment:r}),ne(t,"is-failed");return}}}async function t4(e,t,n){let r=t7(t,n),i=[];(0,b.f)(n,"upload:setup",{batch:e,attachment:t,form:r,preprocess:i});try{await Promise.all(i);let a=await fetch(t2(r,n));if(a.ok)return await a.json();e.setAttachmentAsFailed(t),(0,b.f)(n,"upload:invalid",{batch:e,attachment:t});let o=await a.text(),s=a.status,{state:l,messaging:c}=t9({status:s,body:o},t.file);ne(n,l,c)}catch{e.setAttachmentAsFailed(t),(0,b.f)(n,"upload:invalid",{batch:e,attachment:t}),ne(n,"is-failed")}return null}function t7(e,t){let n=t.querySelector(".js-data-upload-policy-url-csrf").value,r=t.getAttribute("data-upload-repository-id"),i=t.getAttribute("data-subject-type"),a=t.getAttribute("data-subject-param"),o=t.getAttribute("data-upload-container-type"),s=t.getAttribute("data-upload-container-id"),l=e.file,c=new FormData;return c.append("name",l.name),c.append("size",String(l.size)),c.append("content_type",l.type),c.append("authenticity_token",n),i&&c.append("subject_type",i),a&&c.append("subject",a),r&&c.append("repository_id",r),e.directory&&c.append("directory",e.directory),o&&s&&(c.append("upload_container_type",o),c.append("upload_container_id",s)),c}function t2(e,t){return new Request(t.getAttribute("data-upload-policy-url"),{method:"POST",body:e,credentials:"same-origin",headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}})}function t5(e,t){return{attachmentUploadDidStart(n,r){n.saving(0),ne(t,"is-uploading"),(0,b.f)(t,"upload:start",{batch:e,attachment:n,policy:r})},attachmentUploadDidProgress(n,r){n.saving(r),(0,b.f)(t,"upload:progress",{batch:e,attachment:n})},attachmentUploadDidComplete(n,r,i){n.saved(t3(i,r)),(0,b.f)(t,"upload:complete",{batch:e,attachment:n}),e.isFinished()&&ne(t,"is-default")},attachmentUploadDidError(n,r){e.setAttachmentAsFailed(n),(0,b.f)(t,"upload:error",{batch:e,attachment:n});let{state:i}=t9(r);ne(t,i)}}}function t3(e,t){let n=(null==e.id?null:String(e.id))||(null==t.asset.id?null:String(t.asset.id)),r=("string"==typeof e.href?e.href:null)||("string"==typeof t.asset.href?t.asset.href:null);return{id:n,href:r,name:t.asset.name}}function t9(e,t){if(400===e.status)return{state:"is-bad-file"};if(422!==e.status)return{state:"is-failed"};let n=JSON.parse(e.body);if(!n||!n.errors)return{state:"is-failed"};for(let e of n.errors)switch(e.field){case"size":{let n=t?t.size:null;if(null!=n&&0===n)return{state:"is-empty"};return{state:"is-too-big",messaging:{message:t6(e.message),target:".js-upload-too-big"}}}case"file_count":return{state:"is-too-many"};case"width":case"height":return{state:"is-bad-dimensions"};case"name":if("already_exists"===e.code)return{state:"is-duplicate-filename"};return{state:"is-bad-file"};case"content_type":return{state:"is-bad-file"};case"uploader_id":return{state:"is-bad-permissions"};case"repository_id":return{state:"is-repository-required"};case"format":return{state:"is-bad-format"}}return{state:"is-failed"}}let t6=e=>e.startsWith("size")?e.substring(5):e,t8=["is-default","is-uploading","is-bad-file","is-duplicate-filename","is-too-big","is-too-many","is-hidden-file","is-failed","is-bad-dimensions","is-empty","is-bad-permissions","is-repository-required","is-bad-format"];function ne(e,t,n){if(n){let{message:t,target:r}=n,i=e.querySelector(r);i&&(i.innerHTML=t)}e.classList.remove(...t8),e.classList.add(t)}let nt=class Batch{percent(){let e=nn(this.attachments,e=>e.file.size*e.percent/100);return Math.round(e/this.total*100)}uploaded(){let e=e=>e.isSaved()?1:0;return nn(this.attachments,e)}isFinished(){return this.attachments.every(e=>this.failedAttachments.includes(e)||e.isSaved())}setAttachmentAsFailed(e){this.attachments.includes(e)&&!this.failedAttachments.includes(e)&&this.failedAttachments.push(e)}constructor(e){this.attachments=e,this.failedAttachments=[],this.size=this.attachments.length,this.total=nn(this.attachments,e=>e.file.size)}};function nn(e,t){return e.reduce((e,n)=>e+t(n),0)}(0,A.N7)("file-attachment[hover]",{add(e){e.classList.add("dragover")},remove(e){e.classList.remove("dragover")}}),(0,b.on)("file-attachment-accept","file-attachment",function(e){let{attachments:t}=e.detail;0===t.length&&(ne(e.currentTarget,"is-hidden-file"),e.preventDefault())}),(0,b.on)("file-attachment-accepted","file-attachment",function(e){let t=e.currentTarget.querySelector(".drag-and-drop");if(t&&t.hidden)return;let{attachments:n}=e.detail;t1(new nt(n),e.currentTarget)}),(0,b.on)("click","button[data-file-attachment-for]",function(e){let t=e.currentTarget,n=t.getAttribute("data-file-attachment-for"),r=document.querySelector(`input[type=file]#${n}`);r.click()});let nr=0;function ni(e){return Array.from(e.types).indexOf("Files")>=0}function na(e){let t=e.dataTransfer;t&&ni(t)&&e.preventDefault()}function no(e){let t=e.dataTransfer;t&&ni(t)&&e.preventDefault()}function ns({currentTarget:e}){let t=e.querySelector("file-attachment");ne(t,"is-default")}(0,A.N7)("file-attachment",{add(e){0==nr++&&(document.addEventListener("drop",na),document.addEventListener("dragover",no));let t=e.closest("form");t&&t.addEventListener("reset",ns)},remove(e){0==--nr&&(document.removeEventListener("drop",na),document.removeEventListener("dragover",no));let t=e.closest("form");t&&t.removeEventListener("reset",ns)}});var nl=n(13002);function nc(e){let t=e.querySelector("filter-input");t&&!e.hasAttribute("open")&&t.reset()}function nu(e,t,n,r={}){let i=r.limit??1/0,a=0;for(let r of e.children){let e=n(r,t);null==e||(e&&a<i?(a++,nd(r,!0)):nd(r,!1))}return a}function nd(e,t){e.style.display=t?"":"none",e.hidden=!t}(0,b.on)("filter-input-updated","filter-input",e=>{let t=e.currentTarget.input;if(!(document.activeElement&&document.activeElement===t))return;let{count:n,total:r}=e.detail;(0,k.x)(`Found ${n} out of ${r} ${1===r?"item":"items"}`)}),(0,b.on)("toggle","details",e=>{setTimeout(()=>nc(e.target),0)},{capture:!0}),(0,b.on)("tab-container-changed","tab-container",e=>{if(!(e.target instanceof HTMLElement))return;let{relatedTarget:t}=e.detail,n=e.target.querySelector("filter-input");n instanceof nl.Z&&n.setAttribute("aria-owns",t.id)},{capture:!0});var nf=n(87738),nm=n(41982);let nh=new WeakMap;function np(e,t,n){let r=t.toLowerCase(),i=n.limit,a=nh.get(e),o=e.querySelector('input[type="radio"]:checked'),s=Array.from(e.children);if(a){if(e.classList.contains("filter-sort-list-refresh")){e.classList.remove("filter-sort-list-refresh");let t=Array.from(e.children);for(let e of t)a.includes(e)||a.push(e)}}else a=Array.from(e.children),nh.set(e,a);for(let t of s)e.removeChild(t),t instanceof HTMLElement&&(t.style.display="");let l=r?(0,nm.W)(a,n.sortKey,nf.qu):a,c=null==i?l:l.slice(0,i),u=c.length,d=document.createDocumentFragment();for(let e of c)d.appendChild(e);let f=!1;if(o instanceof HTMLInputElement)for(let e of d.querySelectorAll('input[type="radio"]:checked'))e instanceof HTMLInputElement&&e.value!==o.value&&(e.checked=!1,f=!0);e.appendChild(d),o&&f&&o.dispatchEvent(new Event("change",{bubbles:!0}));let m=e.querySelectorAll(".js-divider");for(let e of m)e.classList.toggle("d-none",Boolean(r&&r.trim().length>0));return u}var ng=n(40458);let nb=new AbortController,ny=new WeakMap,nv=new WeakMap,nw=new WeakMap;async function nS(e,t,n){n&&!ny.has(e)&&nj(e);let r=await nE(e,t,n),i=e.hasAttribute("data-filterable-data-pre-rendered");return i&&(r.suggestions=nL(e,n)),r}async function nE(e,t,n){let r=new URL(e.getAttribute("data-filterable-src")||"",window.location.origin);if("/"===r.pathname)throw Error("could not get data-filterable-src");if(n){let n=ny.get(e),i=t.trim();if(n.lastSearchText===i)return n.lastSearchResult;let a=void 0===n.lastSearchText;n.lastSearchText=i;let o=e.getAttribute("data-filterable-for")||"",s=document.getElementById(o);if(nb.abort(),""===i)n.lastSearchResult={suggestions:[],users:[]};else{nb=new AbortController;let e={headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"},signal:nb.signal},i=r.searchParams||new URLSearchParams;i.set("q",t),i.set("typeAhead","true"),r.search=i.toString(),a||s?.classList.add("is-loading");let o=await fetch(r.toString(),e);n.lastSearchResult=await o.json()}return s?.classList.remove("is-loading"),n.lastSearchResult}{let e=await fetch(r.toString(),{headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});return await e.json()}}function nL(e,t){let n=[],r=e.querySelectorAll(".js-filterable-suggested-user");if(r.length>0)for(let t of e.querySelectorAll(".js-filterable-suggested-user"))t.classList.remove("js-filterable-suggested-user"),n.push({name:t.querySelector(".js-description").textContent,login:t.querySelector(".js-username").textContent,selected:"true"===t.getAttribute("aria-checked"),element:t,suggestion:!0});if(t){let t=ny.get(e);return r.length>0&&(t.cachedSuggestions=n,t.userResultCache.clear()),t.cachedSuggestions}return n}function nj(e){ny.set(e,{lastSearchResult:{suggestions:[],users:[]},cachedSuggestions:[],userResultCache:new Map})}async function nA(e,t,n){nw.set(e,t),await (0,ng.Z)();let r=e.hasAttribute("data-filterable-show-suggestion-header"),i=e.hasAttribute("data-filterable-type-ahead"),a=nv.get(e);if(!a)try{a=await nS(e,t,i),i||nv.set(e,a)}catch(e){if("AbortError"===e.name)return -1;throw e}if(!i&&nw.get(e)!==t)return -1;let o=n.limit,s=e.querySelector("template"),l={};for(let t of e.querySelectorAll("input[type=hidden]"))l[`${t.name}${t.value}`]=t;let c=s.nextElementSibling;for(;c;){let e=c;c=e.nextElementSibling,e instanceof HTMLElement&&(i||"true"===e.getAttribute("aria-checked")||e.classList.contains("select-menu-divider"))?e.hidden=!0:e.remove()}let u=0,d=""===t.trim(),f=document.createDocumentFragment(),m=e.querySelector(".js-divider-suggestions"),h=e.querySelector(".js-divider-rest"),p=ny.get(e);function g(e){let n=`${e.login} ${e.name}`.toLowerCase().trim().includes(t),r=!(null!=o&&u>=o)&&n,i=r||e.selected||e.suggestion;if(i){let t=nT(e,s,l,p);t.hidden=!r,r&&u++,f.appendChild(t)}}let b=!1;if(m&&(a.suggestions?.length>0||r&&a.users.length>0)){let e=a.suggestions??[],t=e.filter(e=>e.selected),n=e.filter(e=>!e.selected);for(let e of t)g(e);f.appendChild(m);let o=u;for(let e of n)g(e);b=u>o,m.hidden=!b||i&&!d,r&&a.users.length>0&&(m.hidden=!d)}h&&f.appendChild(h);let y=u;for(let e of a.users)g(e);return h&&(h.hidden=y===u||!b),e.append(f),u}function nT(e,t,n,r){if(null!=e.element)return e.element;if(r?.userResultCache.has(e.id))return r.userResultCache.get(e.id);let i=t.content.cloneNode(!0),a=i.querySelector("input[type=checkbox], input[type=radio]");e.type&&(a.name=`reviewer_${e.type}_ids[]`),a.value=e.id;let o=`${a.name}${e.id}`,s=e.selected;n[o]&&(s=!0,n[o].remove(),delete n[o]);let l=i.querySelector("[role^=menuitem]");s&&(l.setAttribute("aria-checked","true"),a.checked=!0),e.disabled&&l.setAttribute("aria-disabled","true");let c=i.querySelector(".js-username");c&&(c.textContent=e.login);let u=i.querySelector(".js-description");u&&(u.textContent=e.name);let d=i.querySelector(".js-extended-description");d&&(e.description?d.textContent=e.description:d.remove());let f=i.querySelector(".js-avatar");return f.className=`${f.className} ${e.class}`,f.src=e.avatar,e.element=l,r?.userResultCache.set(e.id,l),e.element}let nq=new AbortController,nk=new WeakMap,nC=new WeakMap,nM=new WeakMap;async function nx(e,t,n){await (0,ng.Z)(),nM.set(e,t);let r=nC.get(e);if(!r)try{r=await nR(e,t)}catch(e){if("AbortError"===e.name)return -1;throw e}let i={};for(let t of e.querySelectorAll("label[aria-checked=true] > div > input[hidden]"))i[`${t.name}${t.value}`]=t;let a=e.querySelector("template"),o=a.nextElementSibling;for(;o;){let e=o;o=e.nextElementSibling,e instanceof HTMLElement&&("true"===e.getAttribute("aria-checked")||e.classList.contains("select-menu-divider"))?e.hidden=!0:e.remove()}let s=document.createDocumentFragment(),l=nk.get(e),c=n.limit,u=0;for(let e of r.labels)!function(e){let n=`${e.name}`.toLowerCase().trim().includes(t.toLocaleLowerCase()),r=!(null!=c&&u>=c)&&n,o=r||e.selected;if(o){let t=nP(e,a,i,l);t.hidden=!r,r&&u++,s.appendChild(t)}}(e);return e.append(s),u}function n_(e){nk.set(e,{lastSearchResult:{labels:[]},cachedSuggestions:[],labelResultCache:new Map})}async function nR(e,t){nk.has(e)||n_(e);let n=e.hasAttribute("data-filterable-data-pre-rendered");return n?nH(e):await nN(e,t)}function nH(e){let t=[],n=e.querySelectorAll(".js-filterable-label");if(e.removeAttribute("data-filterable-data-pre-rendered"),n.length>0)for(let n of e.querySelectorAll(".js-filterable-label"))n.classList.remove("js-filterable-label"),t.push({id:n.querySelector("input[hidden]").getAttribute("value")||"",name:n.querySelector("input[hidden]").getAttribute("data-label-name")||"",htmlName:n.querySelector(".js-label-name-html").textContent,description:n.querySelector(".js-label-description")?.textContent||"",color:n.querySelector(".js-label-color").getAttribute("label-color")||"",selected:"true"===n.getAttribute("aria-checked"),element:n});let r=nk.get(e);return t.length>0&&(r.cachedSuggestions=t,r.lastSearchText="",r.lastSearchResult={labels:t}),r.lastSearchResult}async function nN(e,t){let n=new URL(e.getAttribute("data-filterable-src")||"",window.location.origin);if("/"===n.pathname)throw Error("could not get data-filterable-src");let r=nk.get(e),i=t.trim();if(r.lastSearchText===i)return r.lastSearchResult;r.lastSearchText=i;let a=e.getAttribute("data-filterable-for")||"",o=document.getElementById(a);nq.abort(),nq=new AbortController;let s={headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"},signal:nq.signal},l=n.searchParams||new URLSearchParams;l.set("q",t),l.set("typeAhead","true"),n.search=l.toString(),o?.classList.add("is-loading");let c=await fetch(n.toString(),s);return r.lastSearchResult=await c.json(),o?.classList.remove("is-loading"),r.lastSearchResult}function nP(e,t,n,r){if(null!=e.element)return e.element;let i=r?.labelResultCache.get(e.id);if(i)return i;let a=t.content.cloneNode(!0),o=a.querySelector("input[type=checkbox]");o.value=e.id,o.setAttribute("data-label-name",e.name);let s=`${o.name}${e.id}`,l=e.selected;n[s]&&(l=!0,n[s].remove(),delete n[s]);let c=a.querySelector("[role^=menuitem]");l&&(c.setAttribute("aria-checked","true"),o.checked=!0);let u=a.querySelector(".js-label-id");u&&u.setAttribute("data-name",e.id);let d=a.querySelector(".js-label-color");if(d){let t=d.getAttribute("style")?.replace("background-color:",`background-color:#${e.color};`);d.setAttribute("style",t)}let f=a.querySelector(".js-label-name-html");f&&(f.innerHTML=e.htmlName);let m=a.querySelector(".js-label-description");return m&&(e.description?m.textContent=e.description:m.remove()),e.element=c,r?.labelResultCache.set(e.id,c),e.element}function n$(e){return e.hasAttribute("data-filter-value")?e.getAttribute("data-filter-value").toLowerCase().trim():e.textContent.toLowerCase().trim()}async function nI(e,t){let n=parseInt(e.getAttribute("data-filterable-limit"),10)||null,r=0;switch(e.getAttribute("data-filterable-type")){case"fuzzy-prio":{let i=t.toLowerCase(),a=e=>{let t=e.getAttribute("data-prio-filter-value").toLowerCase().trim(),n=e.textContent.toLowerCase().trim(),r=2*(0,nf.EW)(t,i,.01),a=(0,nf.EW)(n,i,.01);return r>a&&(a=r),a>0?{score:a,text:n}:null};r=np(e,t,{limit:n,sortKey:a});break}case"fuzzy":{let i=t.toLowerCase(),a=e=>{let t=n$(e),n=(0,nf.EW)(t,i);return n>0?{score:n,text:t}:null};r=np(e,t,{limit:n,sortKey:a});break}case"substring":r=nu(e,t.toLowerCase(),nD,{limit:n});break;case"substring-memory":r=await nA(e,t,{limit:n});break;case"labels-typeahead":r=await nx(e,t,{limit:n});break;default:r=nu(e,t.toLowerCase(),nO,{limit:n})}return e.classList.toggle("filterable-active",t.length>0),e.classList.toggle("filterable-empty",0===r),r}function nO(e,t){return e.textContent.toLowerCase().trim().startsWith(t)}function nD(e,t){if(e.hasAttribute("data-skip-substring-filter")||e.classList.contains("select-menu-no-results"))return null;let n=e.querySelector("[data-filterable-item-text]")||e;return n.textContent.toLowerCase().trim().includes(t)}function nB(e,t,n){let r=n.length>0&&!nW(e,n);if(e.classList.toggle("is-showing-new-item-form",r),!r)return;t.querySelector(".js-new-item-name").textContent=n;let i=t.querySelector(".js-new-item-value");(i instanceof HTMLInputElement||i instanceof HTMLButtonElement)&&(i.value=n)}function nW(e,t){for(let n of e.querySelectorAll("[data-menu-button-text]")){let e=n.textContent.toLowerCase().trim();if(e===t.toLowerCase())return!0}return!1}(0,A.N7)(".js-filterable-field",{constructor:HTMLInputElement,initialize(e){e.autocomplete||(e.autocomplete="off");let t=e.hasAttribute("type-ahead")?200:null,n=e.value;async function r(e){n!==e.value&&(n=e.value,await (0,I.gJ)(),(0,b.f)(e,"filterable:change"))}async function i(){n=e.value,await (0,I.gJ)(),(0,b.f)(e,"filterable:change")}return{add(e){e.addEventListener("focus",i),(0,G.oq)(e,r,{wait:t}),document.activeElement===e&&i()},remove(e){e.removeEventListener("focus",i),(0,G.iU)(e,r)}}}}),(0,b.on)("filterable:change",".js-filterable-field",async function(e){let t=e.currentTarget,n=t.value.trim().toLowerCase(),r=document.querySelectorAll(`[data-filterable-for="${t.id}"]`);for(let e of r){let r=await nI(e,n);if(-1===r)return;document.activeElement&&t===document.activeElement&&(0,k.x)(`${r} results found.`),e.dispatchEvent(new CustomEvent("filterable:change",{bubbles:!0,cancelable:!1,detail:{inputField:t}}))}}),(0,b.on)("filterable:change","details-menu .select-menu-list",function(e){let t=e.currentTarget,n=t.querySelector(".js-new-item-form");n&&nB(t,n,e.detail.inputField.value)}),(0,A.N7)("tab-container .select-menu-list .filterable-empty, details-menu .select-menu-list .filterable-empty",{add(e){let t=e.closest(".select-menu-list");t.classList.add("filterable-empty")},remove(e){let t=e.closest(".select-menu-list");t.classList.remove("filterable-empty")}});var nF=n(3126),nU=n(81574);function nz(){let e=document.firstElementChild;!e.classList.contains("js-skip-scroll-target-into-view")&&(0,nF.lA)(document)&&(0,nF.kc)(document)}function nV(e){let t=document.createTextNode("\xa0"),n=document.createElement("span");n.classList.add("sr-only"),n.appendChild(t),e.appendChild(n)}(0,nU.Z)(nz),(0,b.on)("click",'a[href^="#"]',function(e){let{currentTarget:t}=e;t instanceof HTMLAnchorElement&&setTimeout(nz,0)}),(0,b.on)("click",".js-flash-close",function(e){let t=e.currentTarget.closest(".flash-messages"),n=e.currentTarget.closest(".flash");n.remove(),t&&!t.querySelector(".flash")&&t.remove()}),async function(){await em.C;let e=document.querySelector('.js-flash-alert[role="alert"]');e&&setTimeout(()=>{nV(e)},200)}();var nG=n(69567);let nX=["flash-notice","flash-error","flash-message","flash-warn"];function nZ(e){for(let{key:t,value:n}of nX.flatMap(eE.$1)){let r;(0,eE.kT)(t);try{r=atob(decodeURIComponent(n))}catch{continue}e.after(new nG.R(e,{className:t,message:r}))}}(0,A.N7)("template.js-flash-template",{constructor:HTMLTemplateElement,add(e){nZ(e)}});let nK=new WeakMap;document.addEventListener("focus",function(e){let t=e.target;t instanceof Element&&!nK.get(t)&&((0,b.f)(t,"focusin:delay"),nK.set(t,!0))},{capture:!0}),document.addEventListener("blur",function(e){setTimeout(function(){let t=e.target;t instanceof Element&&t!==document.activeElement&&((0,b.f)(t,"focusout:delay"),nK.delete(t))},200)},{capture:!0}),(0,y.AC)(".js-form-toggle-target",async function(e,t){try{await t.text()}catch{return}let n=e.closest(".js-form-toggle-container");n.querySelector(".js-form-toggle-target[hidden]").hidden=!1,e.hidden=!0});var nJ=n(97895);function nY(e){e instanceof CustomEvent&&(0,k.x)(`${e.detail} results found.`)}(0,A.N7)("fuzzy-list",{constructor:nJ.Z,subscribe:e=>(0,B.RB)(e,"fuzzy-list-sorted",nY)}),(0,b.on)("click",".email-hidden-toggle",function(e){let t=e.currentTarget.nextElementSibling;t instanceof HTMLElement&&(t.style.display="",t.classList.toggle("expanded"),e.preventDefault())});var nQ=n(20332);function n0(e){let t=document.querySelectorAll(".js-hook-event-checkbox");for(let n of t)n.checked=n.matches(e)}(0,A.N7)(".js-hook-url-field",{constructor:HTMLInputElement,add(e){function t(){let t;let n=e.form;if(!n)return;try{t=new URL(e.value)}catch(e){}let r=n.querySelector(".js-invalid-url-notice");r instanceof HTMLElement&&(r.hidden=!!(""===e.value||t&&/^https?:/.test(t.protocol)));let i=n.querySelector(".js-insecure-url-notice");i instanceof HTMLElement&&t&&e.value&&(i.hidden=/^https:$/.test(t.protocol));let a=n.querySelector(".js-ssl-hook-fields");a instanceof HTMLElement&&(a.hidden=!(t&&"https:"===t.protocol))}(0,G.oq)(e,t),t()}}),(0,b.on)("change",".js-hook-event-choice",function(e){let t=e.currentTarget,n=t.checked&&"custom"===t.value,r=t.closest(".js-hook-events-field");if(r&&r.classList.toggle("is-custom",n),t.checked){if(n){let e=document.querySelector(".js-hook-wildcard-event");e.checked=!1}else"push"===t.value?n0('[value="push"]'):"all"===t.value&&n0(".js-hook-wildcard-event")}}),(0,b.on)("click",".js-hook-deliveries-pagination-button",async function(e){let t=e.currentTarget;t.disabled=!0;let n=t.parentElement,r=t.getAttribute("data-url");n.before(await (0,tK.a_)(document,r)),n.remove()}),(0,y.AC)(".js-redeliver-hook-form",async function(e,t){let n;try{n=await t.html()}catch(t){e.classList.add("failed");return}let r=document.querySelector(".js-hook-deliveries-container");r.replaceWith(n.html)}),function(){let e=document.getElementById("insecure_ssl_verification"),t=document.getElementById("insecure_ssl_0"),n=document.getElementById("insecure_ssl_1");e&&t&&n&&(n.addEventListener("change",n=>{n.stopPropagation(),t.checked=!0,e.show()}),e.addEventListener("close",()=>{n.checked=!0}))}();var n1=n(67044);function n4(e){let t=e.split(",");return t.filter(e=>(0,tk.YE)(e)).join(",")}(0,A.N7)("[data-hotkey]",{constructor:HTMLElement,add(e){if((0,tk.Ty)())(0,n1.N9)(e);else{let t=e.getAttribute("data-hotkey");if(t){let n=n4(t);n.length>0&&(e.setAttribute("data-hotkey",n),(0,n1.N9)(e))}}},remove(e){(0,n1.Tz)(e)}});var n7=n(46426),n2=n(29764);let n5=document.querySelector(".js-hovercard-content");(0,A.N7)(".js-hovercard-content",e=>{n5=e});let n3=(0,th.Z)(tK.a_),n9=null,n6=!1,n8=!1,re=0;function rt(e){return"Popover-message--"+e}function rn(e){setTimeout(()=>{if(document.body&&document.body.contains(e)){let t=e.querySelector("[data-hydro-view]");t instanceof HTMLElement&&(0,n2.Fk)(t)}},500)}function rr(){n5 instanceof HTMLElement&&(n5.style.display="none",n5.children[0].textContent="",n5.removeAttribute("data-hovercard-target-url"),n9=null,a=null)}function ri(e){let t=e.getClientRects(),n=t[0]||e.getBoundingClientRect()||{top:0,left:0,height:0,width:0};if(t.length>0){for(let e of t)if(e.left<re&&e.right>re){n=e;break}}return n}function ra(e){let{width:t,height:n}=n5.getBoundingClientRect(),{left:r,top:i,height:a,width:o}=ri(e),s=i>n,l=e.classList.contains("js-hovercard-left");if(l){let e=i+a/2;return{containerTop:s?e-n+17+8:e-17-8,containerLeft:r-t-12,contentClassSuffix:s?"right-bottom":"right-top"}}{let e=window.innerWidth-r>t,l=r+o/2;return{containerTop:s?i-n-12:i+a+12,containerLeft:e?l-24:l-t+24,contentClassSuffix:s?e?"bottom-left":"bottom-right":e?"top-left":"top-right"}}}function ro(e,t){if(!(n5 instanceof HTMLElement))return;n5.style.visibility="hidden",n5.style.display="block",t.classList.remove(rt("bottom-left"),rt("bottom-right"),rt("right-top"),rt("right-bottom"),rt("top-left"),rt("top-right"));let{containerTop:n,containerLeft:r,contentClassSuffix:i}=ra(e);t.classList.add(rt(i)),n5.style.top=`${n+window.pageYOffset}px`,n5.style.left=`${r+window.pageXOffset}px`,rL(e,n5),n5.style.visibility=""}function rs(e,t){if(!(n5 instanceof HTMLElement))return;let n=n5.children[0];n.textContent="";let r=document.createElement("div");for(let t of e.children)r.appendChild(t.cloneNode(!0));n.appendChild(r),ro(t,n),rn(r),(0,k.N)(r),n5.style.display="block",n5.setAttribute("data-hovercard-target-url",t.getAttribute("data-hovercard-url")||"")}function rl(e){let t=e.closest("[data-hovercard-subject-tag]");if(t)return t.getAttribute("data-hovercard-subject-tag");let n=document.head&&document.head.querySelector('meta[name="hovercard-subject-tag"]');return n?n.getAttribute("content"):null}function rc(e){let t=e.getAttribute("data-hovercard-url");if(t){let n=rl(e);if(n){let e=new URL(t,window.location.origin),r=new URLSearchParams(e.search.slice(1));return r.append("subject",n),r.append("current_path",window.location.pathname+window.location.search),e.search=r.toString(),e.toString()}return t}return""}function ru(e){let t=e.getAttribute("data-hovercard-type");return"pull_request"===t||"issue"===t?!!e.closest("[data-issue-and-pr-hovercards-enabled]"):"team"===t?!!e.closest("[data-team-hovercards-enabled]"):"repository"===t?!!e.closest("[data-repository-hovercards-enabled]"):"commit"===t?!!e.closest("[data-commit-hovercards-enabled]"):"project"===t?!!e.closest("[data-project-hovercards-enabled]"):"discussion"===t?!!e.closest("[data-discussion-hovercards-enabled]"):"acv_badge"===t?!!e.closest("[data-acv-badge-hovercards-enabled]"):"sponsors_listing"!==t||!!e.closest("[data-sponsors-listing-hovercards-enabled]")}async function rd(e,t){let n;let r="ontouchstart"in document;if(r)return;let i=e.currentTarget;if(e instanceof MouseEvent&&(re=e.clientX),!(i instanceof Element)||a===i||i.closest(".js-hovercard-content")||!ru(i))return;rr(),a=i,n9=document.activeElement;let o=rc(i);try{let r=new Promise(e=>window.setTimeout(e,t,0));await r,(0,n7.c)("HOVERCARD_SHOW_ON_FOCUS")&&!a&&"focusin"===e.type&&(a=e.target),i===a&&(n=await n3(document,o))}catch(t){let e=t.response;if(e&&404===e.status)i.setAttribute("aria-label","Hovercard is unavailable"),i.classList.add("tooltipped","tooltipped-ne");else if(e&&410===e.status){let t=await e.clone().json();i.setAttribute("aria-label",t.message),i.classList.add("tooltipped","tooltipped-ne")}return}i===a&&n&&(rs(n,i),e instanceof KeyboardEvent&&n5 instanceof HTMLElement&&n5.focus())}function rf(e){rd(e,250)}function rm(e){if(a){if(e instanceof MouseEvent&&e.relatedTarget instanceof HTMLElement){let t=e.relatedTarget;if(t.closest(".js-hovercard-content")||t.closest("[data-hovercard-url]"))return}else e instanceof KeyboardEvent&&n9 instanceof HTMLElement&&n9.focus();rr()}}function rh(e){let t=a;o=window.setTimeout(()=>{a===t&&rm(e)},100)}function rp(e){e instanceof KeyboardEvent&&"Escape"===e.key&&rm(e)}function rg(){o&&clearTimeout(o)}function rb(e){n6=!0,rd(e,0)}function ry(e){n6=!1,n8||rm(e)}function rv(e){n8=!1,n6||rh(e)}function rw(e){n8=!0,rf(e)}function rS(){n8=!0,rg()}function rE(){n6=!0,rg()}function rL(e,t){let n=e.getAttribute("data-hovercard-z-index-override");n?t.style.zIndex=n:t.style.zIndex="100"}n5&&((0,A.N7)("[data-hovercard-url]",{subscribe:e=>(0,B.qC)((0,B.RB)(e,"mouseover",rw),(0,B.RB)(e,"focusin",rb),(0,B.RB)(e,"mouseleave",rv),(0,B.RB)(e,"focusout",ry),(0,B.RB)(e,"keyup",rp))}),(0,A.N7)("[data-hovercard-url]",{remove(e){a===e&&rr()}}),(0,A.N7)(".js-hovercard-content",{subscribe:e=>(0,B.qC)((0,B.RB)(e,"mouseover",rS),(0,B.RB)(e,"focusin",rE),(0,B.RB)(e,"mouseleave",rv),(0,B.RB)(e,"focusout",ry),(0,B.RB)(e,"keyup",rp))}),(0,b.on)("menu:activated","details",rr),window.addEventListener("turbo:load",rr),window.addEventListener("statechange",rr),window.addEventListener("focusin",ry));var rj=n(38085);let rA=["fcp","lcp","fid","inp","cls","hpc","ttfb"];function rT(e){let t;if(document.querySelector('[data-hydrostats="publish"]')){for(let n of rA)if(void 0!==e[n]&&e[n]<6e4){if(!t){let n=document.querySelector("react-app");(t=rq()).react=!!n,t.reactApp=n?.getAttribute("app-name"),t.reactPartials=[...new Set(Array.from(document.querySelectorAll("react-partial")).map(e=>e.getAttribute("partial-name")||""))],t.ssr=e.ssr}t[n]=e[n].toPrecision(6),"hpc"===n&&(t.hpcMechanism=e.mechanism,t.hpcSoft=e.soft);break}}}function rq(){return s||(s={},rk()),s}async function rk(){await em.C,window.requestIdleCallback(rC)}function rC(){(0,v.qP)("web-vital",s),s=void 0}let rM=(0,w.cF)(),rx=rR(),r_=rH();function rR(){return Boolean(document.querySelector('react-app[data-lazy="true"]'))}function rH(){return Boolean(document.querySelector('react-app[data-alternate="true"]'))}function rN(){return Boolean(document.querySelector("header.AppHeader"))}function rP(){return performance.getEntriesByType("resource").some(e=>"fetch"===e.initiatorType&&e.name.includes("_graphql?"))}function r$(){return performance.getEntriesByType("resource").some(e=>"script"===e.initiatorType)}function rI(e){let{name:t,value:n}=e,r={name:window.location.href,app:(0,tW.Nb)()||"rails"};r[t.toLowerCase()]=n,(0,n7.c)("SAMPLE_NETWORK_CONN_TYPE")&&(r.networkConnType=rB()),"HPC"===t?(r.soft=e.soft,r.ssr=e.ssr,r.mechanism=tW.CF[e.mechanism],r.lazy=e.lazy,r.alternate=e.alternate,r.hpcFound=e.found,r.hpcGqlFetched=e.gqlFetched,r.hpcJsFetched=e.jsFetched,r.headerRedesign=rN()):(r.ssr=rM,r.lazy=rx,r.alternate=r_);let i=document.querySelector('meta[name="synthetic-test"]');i&&(r.synthetic=!0),(0,T.b)({webVitalTimings:[r]}),rT(r),rO(t,n)}function rO(e,t){let n=document.querySelector("#staff-bar-web-vitals"),r=n?.querySelector(`[data-metric=${e.toLowerCase()}]`);r&&(r.textContent=t.toPrecision(6))}function rD(){return!!(window.performance&&window.performance.timing&&window.performance.getEntriesByType)}function rB(){return"connection"in navigator?navigator.connection.effectiveType:"N/A"}async function rW(){rD()&&(await em.C,await new Promise(e=>setTimeout(e)),rF(),rU())}let rF=()=>{let e=window.performance.getEntriesByType("resource").map(e=>({name:e.name,entryType:e.entryType,startTime:e.startTime,duration:e.duration,initiatorType:e.initiatorType,nextHopProtocol:e.nextHopProtocol,workerStart:e.workerStart,workerTiming:(e.workerTiming||[]).map(e=>({duration:e.duration,startTime:e.startTime,name:e.name,entryType:e.entryType})),serverTiming:(e.serverTiming||[]).map(e=>({duration:e.duration,description:e.description,name:e.name})),redirectStart:e.redirectStart,redirectEnd:e.redirectEnd,fetchStart:e.fetchStart,domainLookupStart:e.domainLookupStart,domainLookupEnd:e.domainLookupEnd,connectStart:e.connectStart,connectEnd:e.connectEnd,secureConnectionStart:e.secureConnectionStart,requestStart:e.requestStart,responseStart:e.responseStart,responseEnd:e.responseEnd,transferSize:e.transferSize,encodedBodySize:e.encodedBodySize,decodedBodySize:e.decodedBodySize}));e.length&&(0,T.b)({resourceTimings:e})},rU=()=>{let e=window.performance.getEntriesByType("navigation").map(e=>({activationStart:e.activationStart,name:e.name,entryType:e.entryType,startTime:e.startTime,duration:e.duration,initiatorType:e.initiatorType,nextHopProtocol:e.nextHopProtocol,workerStart:e.workerStart,workerTiming:(e.workerTiming||[]).map(e=>({duration:e.duration,startTime:e.startTime,name:e.name,entryType:e.entryType})),serverTiming:(e.serverTiming||[]).map(e=>({duration:e.duration,description:e.description,name:e.name})),redirectStart:e.redirectStart,redirectEnd:e.redirectEnd,fetchStart:e.fetchStart,domainLookupStart:e.domainLookupStart,domainLookupEnd:e.domainLookupEnd,connectStart:e.connectStart,connectEnd:e.connectEnd,secureConnectionStart:e.secureConnectionStart,requestStart:e.requestStart,responseStart:e.responseStart,responseEnd:e.responseEnd,transferSize:e.transferSize,encodedBodySize:e.encodedBodySize,decodedBodySize:e.decodedBodySize,unloadEventStart:e.unloadEventStart,unloadEventEnd:e.unloadEventEnd,domInteractive:e.domInteractive,domContentLoadedEventStart:e.domContentLoadedEventStart,domContentLoadedEventEnd:e.domContentLoadedEventEnd,domComplete:e.domComplete,loadEventStart:e.loadEventStart,loadEventEnd:e.loadEventEnd,type:e.type,redirectCount:e.redirectCount}));e.length&&(0,T.b)({navigationTimings:e})};rW(),(0,rj.mw)(rI),(0,rj.a4)(rI),(0,rj.Fu)(rI),(0,rj.NO)(rI),(0,rj.mr)(rI),(0,rj.Yn)(rI);let HPCTimingEvent=class HPCTimingEvent extends Event{constructor(e,t,n,r,i,a,o,s,l){super("hpc:timing"),this.soft=e,this.ssr=t,this.lazy=n,this.alternate=r,this.mechanism=i,this.found=a,this.gqlFetched=o,this.jsFetched=s,this.name="HPC",this.value=performance.now()-l}};let rz=new AbortController;function rV(e){return"function"==typeof e.checkVisibility?e.checkVisibility():Boolean(e.offsetParent||e.offsetWidth||e.offsetHeight)}function rG(e,t){(0,rj.NO)(({value:n})=>{window.performance.measure("HPC",{start:"navigationStart",end:n}),rI({name:"HPC",value:n,soft:e,found:t,gqlFetched:rP(),jsFetched:r$(),ssr:(0,w.cF)(),lazy:rR(),alternate:rH(),mechanism:"hard"})})}function rX({soft:e=!1,mechanism:t="hard"}){let n,r,i;rz.abort(),rz=new AbortController;let a=e?performance.now():0,o=new EventTarget,s=!1,l=new MutationObserver(i=>{let s=!1,c=!1;if(!i.every(e=>0===e.addedNodes.length)){for(let e of i)if("childList"===e.type){for(let t of e.addedNodes)if(t instanceof Element){if(t.hasAttribute("data-hpc")||t.querySelector("[data-hpc]")){cancelAnimationFrame(n),s=!0;break}rV(t)&&(cancelAnimationFrame(n),c=!0)}if(s)break}s?(window.performance.measure("HPC","navigationStart"),l.disconnect(),r=requestAnimationFrame(()=>{o.dispatchEvent(new HPCTimingEvent(e,(0,w.cF)(),rR(),rH(),t,!0,rP(),r$(),a))})):c&&(n=requestAnimationFrame(()=>{o.dispatchEvent(new Event("hpc:dom-insertion"))}))}});l.observe(document,{childList:!0,subtree:!0});let c={capture:!0,passive:!0,once:!0,signal:rz.signal},u=()=>rz.abort();document.addEventListener("touchstart",u,c),document.addEventListener("mousedown",u,c),document.addEventListener("keydown",u,c),document.addEventListener("pointerdown",u,c);let d=!1;o.addEventListener("hpc:dom-insertion",()=>{d=!0,clearTimeout(i);let n=new HPCTimingEvent(e,(0,w.cF)(),rR(),rH(),t,!1,rP(),r$(),a);i=setTimeout(()=>o.dispatchEvent(n),1e4)},{signal:rz.signal}),o.addEventListener("hpc:timing",e=>{!s&&e.value<6e4&&rI(e),rz.abort()},{signal:rz.signal}),rz.signal.addEventListener("abort",()=>{cancelAnimationFrame(r),cancelAnimationFrame(n),clearTimeout(i),l.disconnect()}),document.addEventListener("visibilitychange",()=>{s=!0,rz.abort()},{signal:rz.signal}),e||setTimeout(()=>{d||rG(e,!1)},1e4),!e&&document.querySelector("[data-hpc]")&&(rG(e,!0),rz.abort())}async function rZ(){if(!1===(0,n7.c)("IMAGE_METRIC_TRACKING"))return;let e=Array.from(document.querySelectorAll("img.js-img-time")).slice(0,5),t=Date.now(),n=[];await Promise.all(e.map(e=>rK(e,t,n))),n.length>0&&(0,T.b)({transparentRedirectTimings:n})}async function rK(e,t,n){let r=/\/assets\/storage\/user\/([0-9]+)\/files\/([{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?)/,i=e.getAttribute("src");if(!i)return;let a=new URL(i,window.location.origin),o=r.test(a.pathname)?r:/assets\/([0-9]+)\/([{]?[0-9a-fA-F]{8}-([0-9a-fA-F]{4}-){3}[0-9a-fA-F]{12}[}]?)/,[,s,l]=a.pathname.match(o)||[];if(!s||!l)return;await fetch(`/assets/measure/${s}/${l}`);let c=Date.now()-t;n.push({duration:c,fileGUID:l,userID:s})}document.addEventListener(tW.QE.START,({mechanism:e})=>{rX({soft:!0,mechanism:e})}),rX({soft:!1}),async function(){document.addEventListener(tW.QE.FRAME_UPDATE,()=>(0,v.YT)({turbo:"true"})),document.addEventListener(tW.QE.SUCCESS,()=>{"turbo.frame"!==(0,tW.Gj)()&&(0,v.YT)({turbo:"true"})}),await em.C,(0,v.YT)()}(),(0,b.on)("click","[data-octo-click]",function(e){let t=e.currentTarget;if(!(t instanceof HTMLElement))return;let n=t.getAttribute("data-octo-click")||"",r={};if(t.hasAttribute("data-ga-click")){let e=t.getAttribute("data-ga-click"),n=e.split(",");r.category=n[0].trim(),r.action=n[1].trim()}if(t.hasAttribute("data-octo-dimensions")){let e=t.getAttribute("data-octo-dimensions").split(",");for(let t of e){let[e,n]=t.split(/:(.+)/);e&&(r[e]=n||"")}}(0,v.qP)(n,r)}),(0,b.on)("click","[data-hydro-click]",function(e){let t=e.currentTarget,n=t.getAttribute("data-hydro-click")||"",r=t.getAttribute("data-hydro-click-hmac")||"",i=t.getAttribute("data-hydro-client-context")||"";(0,n2.$S)(n,r,i)}),(0,y.AC)(".js-immediate-updates",async function(e,t){let n;try{let e=await t.json();n=e.json.updateContent}catch(e){e.response.json&&(n=e.response.json.updateContent)}if(n)for(let e in n){let t=n[e],r=document.querySelector(e);r instanceof HTMLElement&&(0,eH.Of)(r,t)}}),document.addEventListener("DOMContentLoaded",rZ);var rJ=n(52191);(0,A.N7)("[data-indeterminate]",{constructor:HTMLInputElement,initialize(e){e.indeterminate=!0}});var rY=n(3626);function rQ(){n.e("app_assets_modules_github_jump-to_ts").then(n.bind(n,65674))}(0,A.N7)(".js-jump-to-field",{constructor:HTMLInputElement,add(e){e.addEventListener("focusin",rQ,{once:!0}),(0,rY.Nc)(window.location.pathname)}});let r0=!1;async function r1(){if(r0)return;r0=!0;let e=document.querySelector("meta[name=github-keyboard-shortcuts]"),t={contexts:e.content},n=`/site/keyboard_shortcuts?${new URLSearchParams(t).toString()}`,r=await (0,tZ.W)({content:(0,tK.a_)(document,n),labelledBy:"keyboard-shortcuts-heading"});r.style.width="800px",r.addEventListener("dialog:remove",function(){r0=!1},{once:!0})}function r4(e){let t=e.currentTarget;if(!(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement))return;let n=parseInt(t.getAttribute("data-input-max-length")||"",10),r=parseInt(t.getAttribute("data-warning-length")||"",10)||5,i=t.value,a=i.replace(/(\r\n|\n|\r)/g,"\r\n"),o=n-a.length;if(o<=0){let e=a.substr(0,n);e.endsWith("\r")?(e=e.substr(0,n-1),o=1):o=0,t.value=e}let s=t.getAttribute("data-warning-text"),l=t.closest(".js-length-limited-input-container"),c=l.querySelector(".js-length-limited-input-warning");o<=r?(c.textContent=s.replace(/{{remaining}}/g,`${o}`),c.classList.remove("d-none")):(c.textContent="",c.classList.add("d-none"))}function r7(e){try{let t=e.getBoundingClientRect();if(0===t.height&&0===t.width||"0"===e.style.opacity||"hidden"===e.style.visibility)return!1}catch{}return!0}(0,b.on)("click",".js-keyboard-shortcuts",r1),document.addEventListener("keydown",e=>{!(e instanceof KeyboardEvent)||!(0,tk.Zf)(e)||e.target instanceof Node&&(0,H.sw)(e.target)||"Shift+?"!==(0,n1.EL)(e)||r1()}),(0,A.N7)(".js-modifier-key",{constructor:HTMLElement,add(e){if(/Macintosh/.test(navigator.userAgent)){let t=e.textContent;t&&(t=(t=t.replace(/ctrl/,"\u2318")).replace(/alt/,"\u2325"),e.textContent=t)}}}),(0,A.N7)(".js-modifier-label-key",{add(e){let t=e.textContent?.replace(/ctrl/i,"Ctrl");t&&(/Macintosh/.test(navigator.userAgent)&&(t=(t=t.replace(/ctrl/i,"Cmd")).replace(/alt/i,"Option")),e.textContent=t)}}),(0,A.N7)(".js-length-limited-input",{add(e){e.addEventListener("input",r4),e.addEventListener("change",r4)},remove(e){e.removeEventListener("input",r4),e.removeEventListener("change",r4)}}),(0,b.on)("click",".js-member-search-filter",function(e){e.preventDefault();let t=e.currentTarget.getAttribute("data-filter"),n=e.currentTarget.closest("[data-filter-on]"),r=n.getAttribute("data-filter-on"),i=document.querySelector(".js-member-filter-field"),a=i.value,o=RegExp(`${r}:(?:[a-z]|_|((').*(')))+`),s=a.toString().trim().replace(o,"");i.value=`${s} ${t}`.replace(/\s\s/," ").trim(),i.focus(),(0,b.f)(i,"input")}),(0,y.AC)(".js-notice-dismiss",async function(e,t){await t.text();let n=e.closest(".js-notice");n.remove()}),(0,b.on)("submit",".js-notice-dismiss-remote",async function(e){let t;let n=e.currentTarget;e.preventDefault();try{t=await fetch(n.action,{method:n.method,body:new FormData(n),headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}})}catch{(0,g.v)();return}if(t&&!t.ok)(0,g.v)();else{let e=n.closest(".js-notice");e.remove()}}),(0,b.on)("click",".js-github-dev-shortcut",function(e){for(let t of(e.preventDefault(),document.querySelectorAll("textarea.js-comment-field")))if(t.value&&r7(t)&&!confirm("Are you sure you want to open github.dev?"))return;let t=e.currentTarget;t.pathname=window.location.pathname,t.hash=window.location.hash,window.location.href=t.href}),(0,b.on)("click",".js-github-dev-new-tab-shortcut",function(e){let t=e.currentTarget;t.pathname=window.location.pathname,t.hash=window.location.hash}),(0,b.on)("click",".js-permalink-shortcut",function(e){let t=e.currentTarget;try{(0,P.lO)(null,"",t.href+window.location.hash)}catch(e){window.location.href=t.href+window.location.hash}for(let e of document.querySelectorAll(".js-permalink-replaceable-link"))e instanceof HTMLAnchorElement&&(e.href=e.getAttribute("data-permalink-href"));e.preventDefault()}),(0,y.AC)(".js-permission-menu-form",async function(e,t){let n;let r=e.querySelector(".js-permission-success"),i=e.querySelector(".js-permission-error");r.hidden=!0,i.hidden=!0,e.classList.add("is-loading");try{n=await t.json()}catch(t){e.classList.remove("is-loading"),i.hidden=!1;return}e.classList.remove("is-loading"),r.hidden=!1;let a=e.closest(".js-org-repo");if(a){let e=n.json;a.classList.toggle("with-higher-access",e.members_with_higher_access)}});let r2=null,r5="last_turbo_request",r3="turbo_start",r9="turbo_end";function r6(e){e instanceof CustomEvent&&e.detail?.url&&!e.defaultPrevented&&(window.performance.mark(r3),r2=e.detail.url)}async function r8(){if(await (0,I.gJ)(),!window.performance.getEntriesByName(r3).length)return;window.performance.mark(r9),window.performance.measure(r5,r3,r9);let e=window.performance.getEntriesByName(r5),t=e.pop(),n=t?t.duration:null;n&&(r2&&(0,T.b)({requestUrl:r2,turboDuration:Math.round(n)}),ie())}function ie(){window.performance.clearMarks(r3),window.performance.clearMarks(r9),window.performance.clearMeasures(r5)}function it(e){let t=e.target;if((e.ctrlKey||e.metaKey)&&"Enter"===e.key){let n=t.form,r=n.querySelector("input[type=submit], button[type=submit]");if(e.shiftKey){let e=n.querySelector(".js-quick-submit-alternative");(e instanceof HTMLInputElement||e instanceof HTMLButtonElement)&&!e.disabled&&(0,H.Bt)(n,e)}else(r instanceof HTMLInputElement||r instanceof HTMLButtonElement)&&r.disabled||(0,H.Bt)(n);e.preventDefault()}}"getEntriesByName"in window.performance&&(document.addEventListener("turbo:before-fetch-request",r6),document.addEventListener("turbo:render",r8)),(0,A.N7)("body.js-print-popup",()=>{window.print(),setTimeout(window.close,1e3)}),(0,A.N7)("poll-include-fragment[data-redirect-url]",function(e){let t=e.getAttribute("data-redirect-url");e.addEventListener("load",function(){window.location.href=t})}),(0,A.N7)("poll-include-fragment[data-reload]",function(e){e.addEventListener("load",function(){window.location.reload()})}),(0,O.w4)("keydown",".js-quick-submit",function(e){it(e)});var ir=n(55498);function ii(e){return"DIV"===e.nodeName&&e.classList.contains("highlight")}function ia(e){return"IMG"===e.nodeName||null!=e.firstChild}(0,A.N7)(".js-comment-quote-reply",function(e){e.hidden=null==e.closest(".js-quote-selection-container")?.querySelector(".js-inline-comment-form-container textarea, .js-new-comment-form textarea")});let io={PRE(e){let t=e.parentElement;if(t&&ii(t)){let n=t.className.match(/highlight-source-(\S+)/),r=n?n[1]:"",i=(e.textContent||"").replace(/\n+$/,"");e.textContent=`\`\`\`${r}
${i}
\`\`\``,e.append("\n\n")}return e},A(e){let t=e.textContent||"";return e.classList.contains("user-mention")||e.classList.contains("team-mention")?t:e.classList.contains("issue-link")&&/^#\d+$/.test(t)?t:e},IMG(e){let t=e.getAttribute("alt");return t&&e.classList.contains("emoji")?t:e},DIV(e){if(e.classList.contains("js-suggested-changes-blob"))e.remove();else if(e.classList.contains("blob-wrapper-embedded")){let t=e.parentElement,n=t.querySelector("a[href]"),r=document.createElement("p");r.textContent=n.href,t.replaceWith(r)}else if(e.classList.contains("js-render-enrichment-target")){let t=e.closest(".js-render-needs-enrichment"),n=t.getAttribute("data-type"),r=e.getAttribute("data-plain"),i=document.createElement("pre");return i.textContent=`\`\`\`${n}
${r}\`\`\``,i}return e}};function is(e){let t=document.createNodeIterator(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.nodeName in io&&ia(e)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),n=[],r=t.nextNode();for(;r;)r instanceof HTMLElement&&n.push(r),r=t.nextNode();for(let e of(n.reverse(),n))e.replaceWith(io[e.nodeName](e))}(0,b.on)("click",".js-comment-quote-reply",function({isTrusted:e,currentTarget:t}){let n=t,r=new ir.p;if(!e){if(r.range.collapsed||null===r.range.startContainer.parentElement)return;n=r.range.startContainer.parentElement}let i=n.closest(".js-comment"),a=i.querySelector(".js-comment-body"),o=i.querySelector(".js-comment-body").cloneNode(!0),s=i.closest(".js-quote-selection-container"),c=a.querySelectorAll("button.js-convert-to-issue-button, span.js-clear");for(let e of c)e.remove();if(s.hasAttribute("data-quote-markdown")&&(r=new ir.I(s.getAttribute("data-quote-markdown")||"",e=>{let t=r.range.startContainer.parentElement,n=t&&t.closest("pre");if(n instanceof HTMLElement){let t=n.parentElement;if(t&&ii(t)){let n=document.createElement("div");n.className=t.className,n.appendChild(e),e.appendChild(n)}}is(e)})),l&&a.contains(l.anchorNode)&&!l.range.collapsed&&""!==l.range.toString().trim()?r.range=l.range:(r.range.collapsed||""===r.range.toString().trim())&&r.select(a),r.closest(".js-quote-selection-container")!==s)return;let u=r.range;for(let e of(s.dispatchEvent(new CustomEvent("quote-selection",{bubbles:!0,detail:r})),r.range=u,Array.from(s.querySelectorAll("textarea")).reverse()))if((0,X.Z)(e)&&!e.closest("tracking-block")){r.insert(e);break}i.querySelector(".js-comment-body").replaceWith(o)}),document.addEventListener("selectionchange",(0,D.D)(function(){let e;let t=window.getSelection();try{e=t.getRangeAt(0)}catch{c=null;return}c={anchorNode:t.anchorNode,range:e}},100)),document.addEventListener("toggle",()=>{l=c},{capture:!0});let il=new ResizeObserver(e=>{for(let t of e)t.contentRect.height>40&&ic(t.target)});function ic(e){let t=.7*e.offsetWidth,n=e.querySelectorAll(".js-reaction-group-button"),r=e.querySelector(".js-all-reactions-popover"),i=0;for(let e of n)i+=e.clientWidth;if(t<(i+=r?.clientWidth||0)){let e=t;for(let t of(r&&(r.removeAttribute("hidden"),e-=r.offsetWidth),n)){let n=t.offsetWidth;n>e?t.setAttribute("hidden","hidden"):t.removeAttribute("hidden"),e-=n}}}(0,A.N7)(".js-reactions-container",function(e){il.observe(e)});let iu=(0,D.D)(async e=>{let t;let n=e.target;try{t=await fetch(n.action,{method:n.method,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:new FormData(n)})}catch{(0,g.v)()}if(t&&!t.ok&&(0,g.v)(),t&&200===t.status){let e=await t.json(),r=n.closest(".js-comment"),i=r?.querySelector(".js-reactions-container"),a=r?.querySelector(".js-comment-header-reaction-button");if(e&&i&&a){let t=(0,N.r)(document,e.reactions_container.trim()),n=(0,N.r)(document,e.comment_header_reaction_button.trim());i.replaceWith(t),a.replaceWith(n)}let o=r?.querySelector(".js-reactions-focus");o&&o.focus()}},1e3);function id(e){let t=e.target,n=t.getAttribute("data-reaction-label"),r=t.closest(".js-add-reaction-popover"),i=r.querySelector(".js-reaction-description");i.hasAttribute("data-default-text")||i.setAttribute("data-default-text",i.textContent||""),i.textContent=n}function im(e){let t=e.target.closest(".js-add-reaction-popover"),n=t.querySelector(".js-reaction-description"),r=n.getAttribute("data-default-text");r&&(n.textContent=r)}(0,b.on)("submit",".js-pick-reaction",e=>{e.preventDefault(),iu(e)}),(0,b.on)("toggle",".js-reaction-popover-container",function(e){let t=e.currentTarget.hasAttribute("open");for(let n of e.target.querySelectorAll(".js-reaction-option-item"))t?(n.addEventListener("mouseenter",id),n.addEventListener("mouseleave",im)):(n.removeEventListener("mouseenter",id),n.removeEventListener("mouseleave",im))},{capture:!0});var ih=n(5582);function ip(e,t,n){let r=e.getAttribute("data-type");"json"===r&&n.headers.set("Accept","application/json"),(0,b.f)(e,"deprecatedAjaxSend",{request:n}),t.text().catch(e=>{if(e.response)return e.response;throw e}).then(t=>{t.status<300?(0,b.f)(e,"deprecatedAjaxSuccess"):(0,b.f)(e,"deprecatedAjaxError",{error:t.statusText,status:t.status,text:t.text})},t=>{(0,b.f)(e,"deprecatedAjaxError",{error:t.message,status:0,text:null})}).then(()=>{(0,b.f)(e,"deprecatedAjaxComplete")})}(0,b.on)("click","form button:not([type]), form button[type=submit], form input[type=submit]",function(e){let t=e.currentTarget,n=t.form;n&&!e.defaultPrevented&&(0,ih.j)(t)}),(0,y.AC)("form[data-remote]",ip),(0,b.on)("deprecatedAjaxComplete","form",function({currentTarget:e}){let t=(0,ih.u)(e);t&&t.remove()}),(0,y.uT)(e=>{let t=(0,ih.u)(e);t&&t.remove()}),(0,y.rK)(ng.Z),(0,b.on)("click",".js-remote-submit-button",async function(e){let t;let n=e.currentTarget,r=n.form;e.preventDefault();try{t=await fetch(r.action,{method:r.method,body:new FormData(r),headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}})}catch{}t&&!t.ok&&(0,g.v)()}),(0,A.N7)(".has-removed-contents",function(){let e;return{add(t){for(let n of e=Array.from(t.childNodes))t.removeChild(n);let n=t.closest("form");n&&(0,b.f)(n,"change")},remove(t){for(let n of e)t.appendChild(n);let n=t.closest("form");n&&(0,b.f)(n,"change")}}});var ig=n(75297),ib=n(93491);if((0,y.AC)("form[data-replace-remote-form]",async function(e,t){e.classList.remove("is-error"),e.classList.add("is-loading");try{let n=e,r=await t.html(),i=e.closest("[data-replace-remote-form-target]");if(i){let e=i.getAttribute("data-replace-remote-form-target");n=e?document.getElementById(e):i}n.replaceWith(r.html)}catch(t){e.classList.remove("is-loading"),e.classList.add("is-error")}}),PerformanceObserver&&(PerformanceObserver.supportedEntryTypes||[]).includes("longtask")){let e=new PerformanceObserver(function(e){let t=e.getEntries().map(({name:e,duration:t})=>({name:e,duration:t,url:window.location.href}));(0,T.b)({longTasks:t})});e.observe({entryTypes:["longtask"]})}let iy=new WeakMap;function iv(e){return e.closest("markdown-toolbar").field}(0,b.on)("click",".js-markdown-link-button",async function({currentTarget:e}){let t=document.querySelector(".js-markdown-link-dialog"),n=t.content.cloneNode(!0);if(!(n instanceof DocumentFragment))return;let r=await (0,tZ.W)({content:n,labelledBy:"box-title"});e instanceof HTMLElement&&iy.set(r,iv(e).selectionEnd)}),(0,b.on)("click",".js-markdown-link-insert",({currentTarget:e})=>{let t=e.closest("details-dialog"),n=document.querySelector(`#${e.getAttribute("data-for-textarea")}`),r=iy.get(t)||0,i=t.querySelector("#js-dialog-link-href").value,a=t.querySelector("#js-dialog-link-text").value,o=`[${a}](${i}) `,s=n.value.slice(0,r),l=n.value.slice(r);n.value=s+o+l,n.focus(),n.selectionStart=n.selectionEnd=r+o.length});var iw=n(55240),iS=n(28585);(0,b.on)("click",".js-saved-reply-menu.ActionListWrap",function(e){if(!(e.target instanceof Element))return;let t=e.target.closest('button[role="menuitem"]')?.querySelector(".js-saved-reply-body");if(!t)return;let n=(t.textContent||"").trim(),r=e.target.closest(".js-previewable-comment-form"),i=r.querySelector("textarea.js-comment-field");(0,eF.Om)(i,n),e.target.closest("modal-dialog")?.close(),setTimeout(()=>i.focus(),0)},{capture:!0}),(0,b.on)("details-menu-select",".js-saved-reply-menu",function(e){if(!(e.target instanceof Element))return;let t=e.detail.relatedTarget.querySelector(".js-saved-reply-body");if(!t)return;let n=(t.textContent||"").trim(),r=e.target.closest(".js-previewable-comment-form"),i=r.querySelector("textarea.js-comment-field");(0,eF.Om)(i,n),setTimeout(()=>i.focus(),0)},{capture:!0}),(0,O.w4)("keydown",".js-saved-reply-shortcut-comment-field",function(e){if("Control+."===(0,n1.EL)(e)){let t=e.target.closest(".js-previewable-comment-form"),n=t.querySelector(".js-saved-reply-container");n instanceof iS.F?n.show():n.setAttribute("open",""),e.preventDefault()}}),(0,O.w4)("keydown",".js-saved-reply-filter-input",function(e){if(/^Control\+[1-9]$/.test((0,n1.EL)(e))){let t=e.target.closest(".js-saved-reply-container"),n=t.querySelectorAll('[role="menuitem"]'),r=Number(e.key),i=n[r-1];i instanceof HTMLElement&&(i.click(),e.preventDefault())}else if("Enter"===e.key){let t=e.target.closest(".js-saved-reply-container"),n=t.querySelectorAll('[role="menuitem"]');n.length>0&&n[0]instanceof HTMLButtonElement&&n[0].click(),e.preventDefault()}});var iE=n(56334),iL=n(11445);function ij(e,t){return e.querySelector(`#LC${t}`)}function iA(e,t,n,r){let i=(0,iE.M9)(e,e=>ij(t,e));if(!i)return;if(n){let e=(0,eF.yb)(i.startContainer.textContent,i.startOffset);if(-1===e)return;i.setStart(i.startContainer,e)}if(r){let e=(0,eF.yb)(i.endContainer.textContent,i.endOffset);if(-1===e)return;i.setEnd(i.endContainer,e)}let a=document.createElement("span");a.classList.add(...["text-bold","hx_keyword-hl","rounded-2","d-inline-block"]),(0,iL.v)(i,a)}function iT(e,t){if(e.start.line!==e.end.line){let n={start:{line:e.start.line,column:e.start.column},end:{line:e.start.line,column:null}};iA(n,t,!0,!1);for(let n=e.start.line+1;n<e.end.line;n+=1){let e={start:{line:n,column:0},end:{line:n,column:null}};iA(e,t,!1,!1)}let r={start:{line:e.end.line,column:0},end:{line:e.end.line,column:e.end.column}};iA(r,t,!1,!0)}else iA(e,t,!0,!0)}function iq(e){let t=parseInt(e.getAttribute("data-start-line")),n=parseInt(e.getAttribute("data-end-line")),r=parseInt(e.getAttribute("data-start-column")),i=parseInt(e.getAttribute("data-end-column"));return t===n&&r===i?null:{start:{line:t,column:r},end:{line:n,column:0!==i?i:null}}}(0,A.N7)(".js-highlight-code-snippet-columns",function(e){let t=iq(e);null!==t&&iT(t,e)});var ik=n(79422),iC=n(82565),iM=n(34090),ix=n(27034),i_=n(95005);function iR(){let e=new URLSearchParams(window.location.search),t=(0,iM.n)(e);if(t){let e=new URL(window.location.href,window.location.origin);return e.search=t.toString(),e.toString()}}function iH(e){if(!(e instanceof ix.Z))return;let t=(0,iM.I)();if(!t)return;let n=e.getAttribute("data-base-src");if(!n)return;let r=new URL(n,window.location.origin),i=new URLSearchParams(r.search);for(let[e,n]of Object.entries(t))"string"==typeof n&&i.set(e,n);r.search=i.toString(),e.setAttribute("src",r.toString())}async function iN(e){try{await e.text()}catch(e){}}function iP(){let e=iR();e&&(0,P.lO)(null,"",e)}async function i$(){await em.C;let e=document.querySelector(".js-mark-notification-form");e instanceof HTMLFormElement&&(0,H.Bt)(e)}function iI(e){return!!e.closest(".js-jump-to-field")}function iO(e,t){let n,r;if(iI(e))return;let i=document.querySelector(".js-site-search-form");document.querySelector(".js-site-search").classList.toggle("scoped-search",t),t?(n=i.getAttribute("data-scoped-search-url"),r=e.getAttribute("data-scoped-placeholder")):(n=i.getAttribute("data-unscoped-search-url"),r=e.getAttribute("data-unscoped-placeholder")),i.setAttribute("action",n),e.setAttribute("placeholder",r)}function iD(e,t){window.innerWidth<768?t?(0,n1.Tz)(e):(0,n1.N9)(e):window.innerWidth>=768&&(t?(0,n1.N9)(e):(0,n1.Tz)(e))}(async function(){(0,y.AC)(".js-notification-shelf .js-notification-action form",async function(e,t){let n=e.hasAttribute("data-redirect-to-inbox-on-submit");if(n){await iN(t);let e=document.querySelector(".js-notifications-back-to-inbox");e&&e.click();return}(0,i_.a)(e,e),await iN(t)})})(),iP(),document.addEventListener(tW.QE.SUCCESS,iP),document.addEventListener("turbo:before-fetch-request",function(e){let t=(0,iM.I)(e.detail.url.pathname);if(t){let n=new URLSearchParams(e.detail.url.search);for(let[e,r]of Object.entries(t))r&&n.set(e,r);e.detail.url.search=n.toString()}}),(0,A.N7)(".js-notification-shelf-include-fragment",iH),(0,b.on)("submit",".js-mark-notification-form",async function(e){let t=e.currentTarget;e.preventDefault();try{await fetch(t.action,{method:t.method,body:new FormData(t),headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}})}catch{}}),document.addEventListener(tW.QE.SUCCESS,i$),i$(),(0,O.w4)("keyup",".js-site-search-field",function(e){let t=e.target,n=0===t.value.length;n&&"Backspace"===e.key&&t.classList.contains("is-clearable")&&iO(t,!1),n&&"Escape"===e.key&&iO(t,!0),t.classList.toggle("is-clearable",n)}),(0,O.ZG)(".js-site-search-focus",function(e){let t=e.closest(".js-chromeless-input-container");t.classList.add("focus"),e.addEventListener("blur",function n(){t.classList.remove("focus"),0===e.value.length&&e.classList.contains("js-site-search-field")&&iO(e,!0),e.removeEventListener("blur",n)})}),(0,b.on)("submit",".js-site-search-form",function(e){if(!(e.target instanceof Element))return;let t=e.target.querySelector(".js-site-search-type-field");t.value=new URLSearchParams(window.location.search).get("type")||""});let iB=new ResizeObserver(e=>{for(let{target:t}of e){let e=t.classList.contains("regular-search-input");(t.classList.contains("sm-search-input")||e)&&iD(t,e)}});(0,A.N7)(".regular-search-input",{constructor:HTMLElement,add(e){iB.observe(e)},remove(e){(0,n1.Tz)(e),iB.unobserve(e)}}),(0,A.N7)(".sm-search-input",{constructor:HTMLElement,add(e){iB.observe(e)},remove(e){(0,n1.Tz)(e),iB.unobserve(e)}}),(0,b.on)("click",".js-toggle-appheader-search",function(){let e=document.querySelector(".js-global-bar-second-row");if(e&&(e.toggleAttribute("hidden"),!e.getAttribute("hidden"))){let t=e.querySelector(".js-site-search-focus");t&&t.focus()}});var iW=n(54430);(0,A.N7)("textarea.js-size-to-fit",{constructor:HTMLTextAreaElement,subscribe:iW.Z});var iF=n(87098);(0,b.on)("click",".js-smoothscroll-anchor",function(e){let t=e.currentTarget;if(!(t instanceof HTMLAnchorElement))return;let n=(0,iF.Kt)(document,t.hash);if(!n&&"#top"===t.hash){let t=document.querySelector("html");if(t){let n=t.style.scrollBehavior;t.style.scrollBehavior="smooth",window.location.hash="",t.scrollIntoView({behavior:"smooth"}),t.style.scrollBehavior=n,e.preventDefault();return}}if(!n)return;n.focus();let r=window.matchMedia("(prefers-reduced-motion: reduce)");r&&r.matches?n.scrollIntoView():n.scrollIntoView({behavior:"smooth"}),e.preventDefault()});let iU=new WeakMap,iz=document.querySelector("#snippet-clipboard-copy-button"),iV=document.querySelector("#snippet-clipboard-copy-button-unpositioned");async function iG(e,t){let n=e.getAttribute("data-snippet-clipboard-copy-content");if(null===n)return;e.removeAttribute("data-snippet-clipboard-copy-content");let r=!!e.closest(".js-snippet-clipboard-copy-unpositioned"),i=r?iV:iz;if(!(i instanceof HTMLTemplateElement))return;let a=i.content.cloneNode(!0),o=a.children[0];if(!(o instanceof HTMLElement))return;let s=o.children[0];if(s instanceof HTMLElement){if(s.setAttribute("value",n),!r){document.addEventListener("selectionchange",()=>{let t=document.getSelection();if(t&&e.contains(t.anchorNode)){let e=t?.toString();s.style.display=""===e.trim()?"inherit":"none"}},{signal:t});let n=e.querySelector("pre");if(null!==n){let e;n.addEventListener("scroll",()=>{e&&clearTimeout(e),s.style.display="none",e=setTimeout(()=>{s.style.display="inherit"},1e3)},{signal:t})}}e.appendChild(o)}}function iX(e,t,n){iK(e,t),n&&e.classList.toggle("on");let r=Array.from(e.querySelectorAll(".js-social-updatable"),eH.x0);return Promise.all(r)}function iZ(e){let t;let n=e.querySelectorAll(":scope > *");for(let e of n)e.checkVisibility()&&(t=e.querySelector('button[type="submit"]'));t?.focus()}function iK(e,t){for(let n of e.querySelectorAll(".js-social-count")){n.textContent=t,n.setAttribute("title",t);let e=n.getAttribute("data-singular-suffix"),r=n.getAttribute("data-plural-suffix"),i="1"===t?e:r;i&&n.setAttribute("aria-label",`${t} ${i}`)}}(0,A.N7)("[data-snippet-clipboard-copy-content]",{constructor:HTMLElement,add(e){let t=new AbortController;iU.set(e,t),iG(e,t.signal)}}),(0,A.N7)(".snippet-clipboard-content clipboard-copy",{constructor:HTMLElement,remove(e){let t=iU.get(e);t&&t.abort()}}),(0,y.AC)(".js-social-form",async function(e,t){let n;let r=e.closest(".js-social-container"),i=e.classList.contains("js-deferred-toggler-target");try{n=await t.json(),r&&(await iX(r,n.json.count,i),(0,n7.c)("STAR_BUTTON_FOCUS")&&iZ(r),r.dispatchEvent(new CustomEvent("social:success",{detail:n,bubbles:!0})))}catch(t){if(t.response?.status===409&&t.response.json.confirmationDialog){let n=t.response.json.confirmationDialog,a=document.querySelector(n.templateSelector),o=e.querySelector(".js-confirm-csrf-token")?.value;if(a instanceof HTMLTemplateElement&&o){let t=new nG.R(a,{confirmUrl:e.action,confirmCsrfToken:o,...n.inputs||{}}),s=await (0,tZ.W)({content:t});s.addEventListener("social-confirmation-form:success",async e=>{e instanceof CustomEvent&&r&&await iX(r,e.detail.count,i)}),s.addEventListener("social-confirmation-form:error",()=>{(0,g.v)()})}}else r&&!i&&r.classList.toggle("on"),(0,g.v)()}}),(0,y.AC)(".js-social-confirmation-form",async function(e,t){try{let n=await t.json();(0,b.f)(e,"social-confirmation-form:success",n.json)}catch{(0,b.f)(e,"social-confirmation-form:error")}});var iJ=n(69202),iY=n(21461);let iQ=[],i0=w.n4?.hidden||!1;function i1(e){return e(i0),iQ.push(e),new B.w0(()=>{let t=iQ.indexOf(e);-1!==t&&iQ.splice(t,1)})}function i4(e){return null!=e}function i7(e){return i2(e).map(t=>({subscriber:e,topic:t}))}function i2(e){let t=(e.getAttribute("data-channel")||"").trim().split(/\s+/);return t.map(iY.Topic.parse).filter(i4)}function i5(e){let t=document.querySelector(".js-stale-session-flash"),n=t.querySelector(".js-stale-session-flash-signed-in"),r=t.querySelector(".js-stale-session-flash-signed-out"),i=t.querySelector(".js-stale-session-flash-switched");if(t.hidden=!1,n.hidden="SIGNED_IN"!==e,r.hidden="SIGNED_OUT"!==e,i.hidden=!e?.startsWith("SWITCHED"),e?.startsWith("SWITCHED:")){let n=e.split(":");if(3===n.length){let e=n[1],r=n[2],a=i.getAttribute("data-original-user-id");a&&a===r?(t.hidden=!0,i.hidden=!0,i.removeAttribute("data-original-user-id")):a||i.setAttribute("data-original-user-id",e||"")}}window.addEventListener("popstate",function(e){e.state&&null!=e.state.container&&location.reload()}),document.addEventListener("submit",function(e){e.preventDefault()})}w.n4?.addEventListener("visibilitychange",()=>{let e=w.n4?.hidden||!1;void 0!==u&&clearTimeout(u);let t=e?3e4:0;u=setTimeout(()=>{if(e!==i0)for(let t of(i0=e,u=void 0,iQ))t(i0)},t)}),async function(){let e=await (0,iJ.G)();if(!e)return;let t=(0,I.g)(t=>e.subscribe(t.flat())),n=(0,I.g)(t=>e.unsubscribeAll(...t)),r=(0,I.g)(t=>e.updatePresenceMetadata(t));(0,A.N7)(".js-socket-channel[data-channel]",{subscribe:e=>{let n=i7(e),i=n.map(e=>e.topic.name).filter(e=>(0,iY.A)(e)),a={unsubscribe(){}};if(i.length){let t,n;let o=()=>{let a=[];for(let o of(n&&a.push(n),void 0!==t&&a.push({[iY.ZE]:t?1:0}),i))r({subscriber:e,channelName:o,metadata:a})};a=(0,B.qC)((0,B.RB)(e,"socket:set-presence-metadata",e=>{let{detail:t}=e;n=t,o()}),i1(e=>{t=e,o()}))}return t(n),a},remove:e=>n(e)})}(),(0,A.N7)("form.js-auto-replay-enforced-sso-request",{constructor:HTMLFormElement,initialize(e){(0,H.Bt)(e)}});let i3=null;if("function"==typeof BroadcastChannel)try{(i3=new BroadcastChannel("stale-session")).onmessage=e=>{"string"==typeof e.data&&i5(e.data)}}catch{}if(!i3){let e=!1;i3={postMessage(t){e=!0;try{window.localStorage.setItem("logged-in",t)}finally{e=!1}},onmessage:null},window.addEventListener("storage",function(t){if(!e&&t.storageArea===window.localStorage&&"logged-in"===t.key)try{("SIGNED_IN"===t.newValue||"SIGNED_OUT"===t.newValue||t.newValue?.startsWith("SWITCHED"))&&i5(t.newValue)}finally{window.localStorage.removeItem(t.key)}})}let i9=document.querySelector(".js-stale-session-flash[data-signedin]");if(i9){let e=i9.getAttribute("data-signedin")||"";i3?.postMessage(e)}let i6=()=>{i3?.postMessage("false")};function i8(e,t,n){let r=e.getBoundingClientRect().height,i=t.getBoundingClientRect(),a=n.getBoundingClientRect(),o=a.top;o+i.height+10>=r&&(o=Math.max(r-i.height-10,0));let s=a.right;null!=n.closest(".js-build-status-to-the-left")&&(s=Math.max(a.left-i.width-10,0)),t.style.top=`${o}px`,t.style.left=`${s}px`,t.style.right="auto"}async function ae(e){let t;let n=e.querySelector(".js-dropdown-details"),r=e.querySelector(".js-status-dropdown-menu")||e.closest(".js-status-dropdown-menu");if(!(r instanceof HTMLElement))return;let i=r.querySelector(".js-status-loader");if(!i)return;let a=r.querySelector(".js-status-loading"),o=r.querySelector(".js-status-error"),s=i.getAttribute("data-contents-url");a.classList.remove("d-none"),o.classList.add("d-none");try{await (0,ng.Z)(),t=await (0,tK.a_)(document,s)}catch(e){a.classList.add("d-none"),o.classList.remove("d-none")}t&&(i.replaceWith(t),r.querySelector(".js-details-container").classList.add("open"),n&&r.classList.contains("js-append-menu-to-body")&&i8(document.body,r,n))}function at(e){let t=e.currentTarget;ae(t)}(0,A.N7)(".js-loggout-form",function(e){e.addEventListener("submit",i6)}),(0,b.on)("toggle",".js-build-status .js-dropdown-details",function(e){let t=e.currentTarget,n=t.querySelector(".js-status-dropdown-menu");function r(){t.hasAttribute("open")||a()}function i(e){n.contains(e.target)||a()}function a(){t.removeAttribute("open"),n.classList.add("d-none"),t.appendChild(n),t.removeEventListener("toggle",r),window.removeEventListener("scroll",i)}n&&(t.addEventListener("toggle",r),n.classList.contains("js-close-menu-on-scroll")&&window.addEventListener("scroll",i,{capture:!0}),n.classList.remove("d-none"),n.querySelector(".js-details-container").classList.add("open"),n.classList.contains("js-append-menu-to-body")&&(document.body.appendChild(n),i8(document.body,n,t)))},{capture:!0}),(0,b.on)("click",".js-status-retry",({currentTarget:e})=>{ae(e)}),(0,A.N7)(".js-build-status",{add(e){e.addEventListener("mouseenter",at,{once:!0})},remove(e){e.removeEventListener("mouseenter",at)}});var an=n(80860),ar=n(94056);async function ai(e){let t=e.currentTarget;if(!(t instanceof HTMLElement))return;let n=t.getAttribute("data-sudo-required");if("false"===n)return;e.stopPropagation(),e.preventDefault();let r=await (0,ar.Z)(t);if(r)t.removeAttribute("data-sudo-required"),t instanceof HTMLFormElement?(0,H.Bt)(t):t.click();else{let e=t.closest("form");e&&(0,b.f)(e,"deprecatedAjaxComplete")}}function aa(e){if(e.hasAttribute("data-use-colon-emoji"))return e.getAttribute("data-value");let t=e.firstElementChild;return t&&"G-EMOJI"===t.tagName&&!t.firstElementChild?t.textContent:e.getAttribute("data-value")}function ao(e,t){let n=` ${t.toLowerCase().replace(/_/g," ")}`,r=e=>{let t=e.getAttribute("data-emoji-name"),r=al(as(e),n);return r>0?{score:r,text:t}:null};return(0,nm.W)(e,r,nf.qu)}function as(e){let t=e.getAttribute("data-text").trim().toLowerCase().replace(/_/g," ");return` ${t}`}function al(e,t){let n=e.indexOf(t);return n>-1?1e3-n:0}function ac(e){let t=e.detail;":"===t.key&&(t.value=aa(t.item))}function au(e){let{key:t,provide:n,text:r}=e.detail;if(":"!==t)return;let i=e.target,a=i.getAttribute("data-emoji-url");n(ad(a,r))}async function ad(e,t){let[n,r]=await am(e),i=ao(r,t).slice(0,5);for(let e of(n.textContent="",i))n.append(e);return{fragment:n,matched:i.length>0}}async function af(e){let t=await (0,tK.a_)(document,e),n=t.firstElementChild;return[n,[...n.children]]}(0,b.on)("click","button[data-sudo-required], summary[data-sudo-required]",ai),(0,A.N7)("form[data-sudo-required]",{constructor:HTMLFormElement,subscribe:e=>(0,B.RB)(e,"submit",ai)}),(0,A.N7)("text-expander[data-emoji-url]",{subscribe:e=>(0,B.qC)((0,B.RB)(e,"text-expander-change",au),(0,B.RB)(e,"text-expander-value",ac))});let am=(0,th.Z)(af);var ah=n(45974);function ap(e){return`${e.number} ${e.title.trim().toLowerCase()}`}function ag(e,t){if(!t)return e;let n=RegExp(`\\b${ab(t)}`),r=/^\d+$/.test(t)?e=>ay(e,n):e=>(0,nf.EW)(e,t),i=e=>{let t=ap(e),n=r(t);return n>0?{score:n,text:t}:null};return(0,nm.W)(e,i,nf.qu)}function ab(e){return e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function ay(e,t){let n=e.search(t);return n>-1?1e3-n:0}function av(e,t){let n=ag(e.suggestions,t).slice(0,5);return{matches:n,icons:e.icons}}function aw(e,t,n,r=""){let i=e=>{let t=e.type in n?(0,N.r)(document,n[e.type]):"";return(0,ah.dy)`
      <li class="markdown-title" role="option" id="suggester-issue-${e.id}" data-value="${e.number}">
        <span class="d-inline-block mr-1">${t}</span>
        <small>#${e.number}</small> ${(0,ah.Au)(e.title)}
      </li>
    `};(0,ah.sY)((0,ah.dy)`
    <ul
      role="listbox"
      class="suggester-container suggester suggestions list-style-none position-absolute"
      data-query="${r}"
    >
      ${e.map(i)}
    </ul>
  `,t)}function aS(e){let t=e.detail;if("#"!==t.key)return;let n=t.item.getAttribute("data-value");t.value=`#${n}`}function aE(e){let{key:t,provide:n,text:r}=e.detail;if("#"!==t)return;if("#"===r){aL(e.target);return}let i=e.target,a=i.getAttribute("data-issue-url");n(aT(a,r,i))}function aL(e){if(!e)return;let t=e.closest("text-expander");t&&t.dismiss()}function aj(e){aL(e.target)}function aA(e){let{key:t}=e;0>["ArrowRight","ArrowLeft"].indexOf(t)||aL(e.target)}async function aT(e,t,n){let r=await aC(e,t,n),i=document.createElement("div");aw(r.matches,i,r.icons,t);let a=i.firstElementChild;return{fragment:a,matched:r.matches.length>0}}(0,A.N7)("text-expander[data-issue-url]",{subscribe:e=>{let t=[(0,B.RB)(e,"text-expander-change",aE),(0,B.RB)(e,"text-expander-value",aS),(0,B.RB)(e,"keydown",aA),(0,B.RB)(e,"click",aj)];return(0,B.qC)(...t)}});let aq=new Set,ak=new Map;async function aC(e,t,n){let r=await a_(e),i=av(r,t);if(t.length<3||r.suggestions.length<1e3)return i;let a=t.slice(0,3);(0,n7.c)("REPOSITORY_SUGGESTER_ELASTIC_SEARCH")&&Number.isFinite(Number(t))&&(a=t);let o=ak.get(a);if(o)return av(o,t);if(!aq.has(a)){aq.add(a);let r=aM(e,a,n);if(0===i.matches.length){let e=await r;return av(e,t)}}return av(r,t)}async function aM(e,t,n){let r=new URL(e,window.location.origin);r.searchParams.set("q",t);let i=await ax(r.toString());if(ak.set(t,i),aq.delete(t),ak.size>5){let e=ak.size-5,t=Array.from(ak.keys()).slice(0,e);for(let e of t)ak.delete(e)}let a=n?.querySelector("ul.suggestions"),o=a?.getAttribute("data-query");if(a&&o?.startsWith(t)){let e=n?.querySelector("[aria-activedescendant]")?.getAttribute("aria-activedescendant"),t=document.createElement("div"),r=av(i,o);if(aw(r.matches,t,r.icons),e)for(let n of t.querySelectorAll(`#${e}`))n.setAttribute("aria-selected","true");let s=t.firstElementChild;a.replaceChildren(...s.children)}return i}async function ax(e){let t=await self.fetch(e,{headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}});if(!t.ok){let e=Error(),n=t.statusText?` ${t.statusText}`:"";throw e.message=`HTTP ${t.status}${n}`,e}return t.json()}let a_=(0,th.Z)(ax);function aR(e){return e.description?`${e.name} ${e.description}`.trim().toLowerCase():`${e.login} ${e.name}`.trim().toLowerCase()}function aH(e,t){if(!t)return e;let n=aP(t),r=e=>{let t=aR(e),r=n(t,e.participant);return r>0?{score:r,text:t}:null};return(0,nm.W)(e,r,nf.qu)}function aN(e,t){let n=e=>{let t="user"===e.type?e.login:e.name,n="user"===e.type?e.name:e.description;return(0,ah.dy)`
      <li role="option" id="suggester-${e.id}-${e.type}-${t}" data-value="${t}">
        <span>${t}</span>
        <small>${n}</small>
      </li>
    `};(0,ah.sY)((0,ah.dy)`
    <ul role="listbox" class="suggester-container suggester suggestions list-style-none position-absolute">
      ${e.map(n)}
    </ul>
  `,t)}function aP(e){if(!e)return()=>2;let t=e.toLowerCase().split("");return(n,r)=>{if(!n)return 0;let i=a$(n,t);if(!i)return 0;let a=e.length/i[1],o=a/(i[0]/2+1);return r?o+1:o}}function a$(e,t){let n,r,i,a;let o=aI(e,t[0]);if(0===o.length)return null;if(1===t.length)return[o[0],1,[]];for(r=0,a=null,i=o.length;r<i;r++){let i=o[r];if(!(n=aO(e,t,i+1)))continue;let s=n[n.length-1]-i;(!a||s<a[1])&&(a=[i,s,n])}return a}function aI(e,t){let n=0,r=[];for(;(n=e.indexOf(t,n))>-1;)r.push(n++);return r}function aO(e,t,n){let r=n,i=[];for(let n=1;n<t.length;n+=1){if(-1===(r=e.indexOf(t[n],r)))return;i.push(r++)}return i}function aD(e){let t=e.detail;if("@"!==t.key)return;let n=t.item.getAttribute("data-value");t.value=`@${n}`}function aB(e){let{key:t,provide:n,text:r}=e.detail;if("@"!==t||r?.split(" ").length>1)return;let i=e.target,a=i.getAttribute("data-mention-url");n(aW(a,r))}async function aW(e,t){let n=await aF(e),r=document.createElement("div"),i=aH(n,t).slice(0,5);aN(i,r);let a=r.firstElementChild;return{fragment:a,matched:i.length>0}}(0,A.N7)("text-expander[data-mention-url]",{subscribe:e=>(0,B.qC)((0,B.RB)(e,"text-expander-change",aB),(0,B.RB)(e,"text-expander-value",aD))});let aF=(0,th.Z)(async function(e){let t=await self.fetch(e,{headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}});if(!t.ok){let e=Error(),n=t.statusText?` ${t.statusText}`:"";throw e.message=`HTTP ${t.status}${n}`,e}return t.json()});(0,b.on)("change","input.js-survey-contact-checkbox",function(e){let t=e.currentTarget,n=t.closest(".js-survey-question-form"),r=n.querySelector(".js-survey-contact-checkbox-hidden");t.checked?r.setAttribute("disabled","true"):r.removeAttribute("disabled")}),(0,b.on)("details-menu-selected",".js-sync-select-menu-text",function(e){let t=document.querySelector(".js-sync-select-menu-button"),n=e.detail.relatedTarget.querySelector("span[data-menu-button-text]").textContent;t.textContent=n,t.focus()},{capture:!0}),(0,b.on)("click",'tab-container [role="tab"]',function(e){let{currentTarget:t}=e,n=t.closest("tab-container"),r=n.querySelector(".js-filterable-field, [data-filter-placeholder-input]");if(r instanceof HTMLInputElement){let e=t.getAttribute("data-filter-placeholder");e&&r.setAttribute("placeholder",e),r.focus()}}),(0,b.on)("tab-container-changed","tab-container",function(e){let t=e.detail.relatedTarget,n=t.getAttribute("data-fragment-url"),r=t.querySelector("include-fragment");n&&r&&!r.hasAttribute("src")&&(r.src=n)});var aU=n(36543),az=n(96776);document.addEventListener("keydown",e=>{if("Escape"!==e.key||e.target!==document.body)return;let t=document.querySelector(".js-targetable-element:target");t&&(0,az.uQ)(t,()=>{window.location.hash="",(0,P.lO)(window.history.state,"",window.location.pathname+window.location.search)})}),document.addEventListener("click",e=>{let t=document.querySelector(".js-targetable-element:target");!(!t||e.target instanceof HTMLAnchorElement)&&e.target instanceof HTMLElement&&(t.contains(e.target)||(0,az.uQ)(t,()=>{window.location.hash="",(0,P.lO)(window.history.state,"",window.location.pathname+window.location.search)}))});var aV=n(48804);async function aG(e){let t=e.currentTarget;if(aK(t)){t.classList.remove("tooltipped");return}let n=t.getAttribute("data-url");if(!n)return;let r=await fetch(n,{headers:{Accept:"application/json"}});if(!r.ok)return;let i=await r.json(),a=t.getAttribute("data-id"),o=document.querySelectorAll(`.js-team-mention[data-id='${a}']`);for(let e of o)e.removeAttribute("data-url");try{0===i.total?i.members.push("This team has no members"):i.total>i.members.length&&i.members.push(`${i.total-i.members.length} more`),aX(o,aZ(i.members))}catch(r){let e=r.response?r.response.status:500,n=t.getAttribute(404===e?"data-permission-text":"data-error-text");aX(o,n)}}function aX(e,t){for(let n of e)n instanceof HTMLElement&&(n.setAttribute("aria-label",t),n.classList.add("tooltipped","tooltipped-s","tooltipped-multiline"))}function aZ(e){if("ListFormat"in Intl){let t=new Intl.ListFormat;return t.format(e)}if(0===e.length)return"";if(1===e.length)return e[0];if(2===e.length)return e.join(" and ");{let t=e[e.length-1];return e.slice(0,-1).concat(`and ${t}`).join(", ")}}function aK(e){return!!e.getAttribute("data-hovercard-url")&&!!e.closest("[data-team-hovercards-enabled]")}function aJ(){let e=document.querySelector(".js-timeline-marker");return null!=e?e.getAttribute("data-last-modified"):null}function aY(e){if(aQ(e))return;let t=aJ();t&&e.headers.set("X-Timeline-Last-Modified",t)}function aQ(e){let t;try{t=new URL(e.url)}catch(e){return!0}return t.host!==window.location.host}function a0(){let e=on();if(!e)return;let t=document.querySelector(".js-pull-discussion-timeline");if(t)return;let n=document.getElementById(e);n&&a8(n)}function a1(e=!0){let t=on();if(!t)return;let n=document.getElementById(t);if(n)a8(n);else{if(a4(t))return;let n=document.querySelector("#js-timeline-progressive-loader");n&&e&&oe(t,n)}}function a4(e){return a7(e)||a2(e,".js-thread-hidden-comment-ids")||a2(e,".js-review-hidden-comment-ids")}function a7(e){let t=a5(e,".js-comment-container");return!!t&&((0,rJ.$)(t),!0)}function a2(e,t){let n=a5(e,t);return!!n&&(n.addEventListener("page:loaded",function(){a1()}),n.querySelector("button[type=submit]").click(),!0)}function a5(e,t){let n=document.querySelectorAll(t);for(let t of n){let n=t.getAttribute("data-hidden-comment-ids");if(n){let r=n.split(","),i=e.match(/\d+/g)?.[0];if(i&&r.includes(i))return t}}return null}async function a3(){let e=document.querySelectorAll(".js-comment-body video"),t=Array.from(e).map(e=>new Promise(t=>{if(e.readyState>=e.HAVE_METADATA)t(e);else{let n=setTimeout(()=>t(e),5e3),r=()=>{clearTimeout(n),t(e)};e.addEventListener("loadeddata",()=>{e.readyState>=e.HAVE_METADATA&&r()}),e.addEventListener("error",()=>r())}}));return Promise.all(t)}async function a9(){let e=document.querySelectorAll(".js-comment-body img"),t=Array.from(e).map(e=>{new Promise(t=>{if(e.complete)t(e);else{let n=setTimeout(()=>t(e),5e3),r=()=>{clearTimeout(n),t(e)};e.addEventListener("load",()=>r()),e.addEventListener("error",()=>r())}})});return Promise.all(t)}async function a6(){return Promise.all([a3(),a9()])}async function a8(e){await a6(),ot(e);let t=e.querySelector(`[href='#${e.id}']`);if((0,nF.zT)(e),t){let e=t.getAttribute("data-turbo");t.setAttribute("data-turbo","false"),t.click(),null===e?t.removeAttribute("data-turbo"):t.setAttribute("data-turbo",e)}}async function oe(e,t){let n;if(!t)return;let r=t.getAttribute("data-timeline-item-src");if(!r)return;let i=new URL(r,window.location.origin),a=new URLSearchParams(i.search.slice(1));a.append("anchor",e),i.search=a.toString();try{n=await (0,tK.a_)(document,i.toString())}catch(e){return}let o=n.querySelector(".js-timeline-item");if(!o)return;let s=o.getAttribute("data-gid");if(!s)return;let l=document.querySelector(`.js-timeline-item[data-gid='${s}']`);if(l)l.replaceWith(o),a1(!1);else{let e=document.getElementById("js-progressive-timeline-item-container");e&&e.replaceWith(n),a1(!1)}}function ot(e){let t=e.closest("details, .js-details-container");t&&("DETAILS"===t.nodeName?t.setAttribute("open","open"):(0,tM.jo)(t)||(0,tM.Qp)(t))}function on(){return window.location.hash.slice(1)}function or(){let e=new WeakSet;function t(){e=new WeakSet(document.querySelectorAll(".js-timeline-item"))}t(),document.addEventListener("turbo:load",t),(0,A.N7)(".js-timeline-item",t=>{t instanceof HTMLElement&&(e.has(t)||(0,k.N)(t))})}async function oi(){let e=[];try{e=await navigator.serviceWorker.getRegistrations()}catch(e){if("SecurityError"===e.name)return}for(let t of e)t.unregister()}(0,A.N7)(".js-team-mention",function(e){e.addEventListener("mouseenter",aG)}),(0,y.AC)(".js-needs-timeline-marker-header",function(e,t,n){aY(n)}),(0,b.on)("deprecatedAjaxSend","[data-remote]",function(e){let{request:t}=e.detail;aY(t)}),(0,nU.Z)(function(){a1()}),(0,A.N7)(".js-timeline-progressive-focus-container",a0),window.addEventListener("sticky-header-rendered",()=>{a0()}),(0,A.N7)(".js-inline-comments-container",function(e){let t=on();if(!t)return;let n=document.getElementById(t);n&&e.contains(n)&&a8(n)}),(0,A.N7)("#js-discussions-timeline-anchor-loader",{constructor:HTMLElement,add:e=>{let t=document.querySelector("#js-timeline-progressive-loader");if(t)return;let n=on();if(!n)return;let r=document.getElementById(n);r||oe(n,e)}}),(0,A.N7)(".js-discussion",or),(0,b.on)("click",".js-toggler-container .js-toggler-target",function(e){if(0!==e.button)return;let t=e.currentTarget.closest(".js-toggler-container");t&&t.classList.toggle("on")}),(0,y.AC)(".js-toggler-container",async(e,t)=>{e.classList.remove("success","error"),e.classList.add("loading");try{await t.text(),e.classList.add("success")}catch(t){e.classList.add("error")}finally{e.classList.remove("loading")}}),async function(){if("serviceWorker"in navigator){await em.x;let e=document.querySelector('link[rel="service-worker-src"]')?.href;e?navigator.serviceWorker.register(e,{scope:"/"}):await oi()}}();var oa=n(67852),oo=n(23243),os=n(78923);(0,oa.E_)(os.w),(0,oa.OY)(0),oa.session.isVisitable=()=>!0;let ol=Object.getOwnPropertyDescriptor(oa.cr.prototype,"reloadReason").get;function*oc(e){for(let t of Object.values(e.detailsByOuterHTML))if(t.tracked)for(let e of t.elements)e instanceof HTMLMetaElement&&e.getAttribute("http-equiv")&&(yield[e.getAttribute("http-equiv")||"",e.getAttribute("content")||""])}Object.defineProperty(oa.cr.prototype,"reloadReason",{get(){let e=ol.call(this);if("tracked_element_mismatch"!==e.reason)return e;let t=Object.fromEntries(oc(this.currentHeadSnapshot)),n=[];for(let[e,r]of oc(this.newHeadSnapshot))t[e]!==r&&n.push((0,oo.T2)(e));return{reason:`tracked_element_mismatch-${n.join("-")}`}}});let ou=e=>{let t=history[e];history[e]=function(n,r,i){let a=n?.skipTurbo||n?.usr?.skipTurbo;oa.ry.history.update(function(r,i,o){let s=history.state?.turboCount||0,l="pushState"===e&&n?.turbo?s+1:s,c=a?{...n,skipTurbo:!0}:{...n,...r,turboCount:l};t.call(this,c,i,o)},new URL(i||location.href,location.href),n?.turbo?.restorationIdentifier)}};ou("replaceState"),ou("pushState");let od=oa.session.adapter,of=()=>{od.progressBar.setValue(0),od.progressBar.show()},om=()=>{od.progressBar.setValue(1),od.progressBar.hide()};var oh=n(78806);let op=new Map,og=new Map,ob=()=>op.get(document.location.href),oy=(e,t)=>op.set(e,t),ov=()=>og.set(document.location.href,(0,oo.Yg)()),ow=()=>og.get(document.location.href);(async()=>{await em.x,oy(document.location.href,(0,oo.ag)(document)),ov()})();let oS=!1,oE=null;w.iG&&(0,A.N7)("[data-turbo-frame]",{constructor:HTMLElement,add(e){if("A"!==e.tagName&&""!==e.getAttribute("data-turbo-frame"))for(let t of e.querySelectorAll("a:not([data-turbo-frame])"))t.setAttribute("data-turbo-frame",e.getAttribute("data-turbo-frame")||"")}}),w.n4?.addEventListener("turbo:click",function(e){if(e.target instanceof HTMLElement&&e instanceof CustomEvent){if((0,oh.Z)(location.href,e.detail.url)){e.preventDefault();return}e.defaultPrevented||(0,tW.LD)("turbo")}}),w.n4?.addEventListener("turbo:before-fetch-request",function(e){let t=window.onbeforeunload?.(e);if(t){let n=confirm(t);n?window.onbeforeunload=null:(e.preventDefault(),om())}}),w.n4?.addEventListener("turbo:before-fetch-request",e=>{if(e.defaultPrevented)return;let t=e.target;if((0,oo.HN)(t)&&of(),t?.tagName==="HTML"){let t=e;t.detail.fetchOptions.headers["Turbo-Visit"]="true"}});let oL=w.n4?.createElement("turbo-frame"),oj=Object.getPrototypeOf(oL.delegate),oA=oj.requestErrored;oj.requestErrored=function(e,t){return this.element.dispatchEvent(new CustomEvent("turbo:fetch-error",{bubbles:!0,detail:{request:e,error:t}})),oA.apply(this,e,t)},w.n4?.addEventListener("turbo:fetch-error",e=>{if(e.target instanceof HTMLFormElement)return;let t=e.detail.request;window.location=t.location,e.preventDefault()}),w.n4?.addEventListener("turbo:before-fetch-response",async e=>{let t=e.detail.fetchResponse;if(oS=t.statusCode>=500,404===t.statusCode&&((0,oo.uL)(t.statusCode),window.location=t.location,e.preventDefault()),history.replaceState({...history.state,skipTurbo:!1},"",location.href),oS){let e=await t.responseHTML,n=os.w.createHTML(e,t.response);oE=new DOMParser().parseFromString(n,"text/html")}}),w.n4?.addEventListener("turbo:frame-render",e=>{(0,oo.HN)(e.target)&&om()}),w.n4?.addEventListener("turbo:before-render",async e=>{e instanceof CustomEvent&&(e.preventDefault(),e.detail.render=oq,await (0,oo.q3)(),e.detail.resume(!0),(0,oo.Ap)(document.documentElement,e.detail.newBody.ownerDocument.documentElement),ov())});let oT=()=>new Promise(e=>{setTimeout(()=>e(),0)}),oq=async(e,t)=>{if(await oT(),oS&&oE){for(let e of(document.documentElement.replaceWith(oE.documentElement),document.querySelectorAll("script"))){let t=(0,oo.lL)(e);t&&e.replaceWith(t)}return}let n=e.querySelector("[data-turbo-body]"),r=t.querySelector("[data-turbo-body]");n&&r?((0,oo.Ap)(e,t),n.replaceWith(r)):((0,oo.uL)("missing_turbo_body"),window.location.reload())};w.iG?.addEventListener("popstate",()=>{let e=document.documentElement,t=ow();if(t){for(let n of e.attributes)t.find(e=>e.nodeName===n.nodeName)||e.removeAttribute(n.nodeName);for(let n of t)e.getAttribute(n.nodeName)!==n.nodeValue&&e.setAttribute(n.nodeName,n.nodeValue)}});var ok=n(16730);let oC=!1,oM=e=>{if(!(e.target instanceof HTMLElement))return;let t=e.target.closest("[data-turbo-frame]"),n=e.target.closest("#js-repo-pjax-container"),r=new URL(e.detail.url,window.location.origin),i=e.target.closest("#user-profile-frame");return n&&t&&!(0,oo.AU)(r.pathname,location.pathname)||i&&!(0,oo.ck)(r.pathname,location.pathname)};w.n4?.addEventListener("turbo:frame-click",function(e){if(e.target instanceof HTMLElement&&e instanceof CustomEvent){if((0,oh.Z)(location.href,e.detail.url)){e.preventDefault();return}oM(e)&&((0,oo.uL)("repo_mismatch"),e.target.removeAttribute("data-turbo-frame"),e.preventDefault()),e.defaultPrevented||(0,tW.LD)("turbo.frame")}}),w.n4?.addEventListener("turbo:before-fetch-response",e=>{d=e.detail.fetchResponse,(0,oo.HN)(e.target)&&oy(window.location.href,(0,oo.ag)(document))}),w.n4?.addEventListener("turbo:before-frame-render",async e=>{e.preventDefault();let{resume:t,newFrame:n}=e.detail;if(oC=!0,!d)return;let r=await d.responseHTML,i=d.location,a=os.w.createHTML(r,d.response),o=new DOMParser().parseFromString(a,"text/html");d=null;let s=e.target,l=o.querySelectorAll("turbo-frame"),c=[...l].find(e=>e.id===s?.id),u=(0,oo.po)(o);if(!c||u.length>0){(0,oo.uL)(`tracked_element_mismatch-${u.join("-")}`),window.location=i;return}oy(i.href,(0,oo.ag)(o)),(0,oo.DT)(o),(0,oo.xk)(o),(0,oo.wz)(o),oN(s,c),await (0,oo.q3)(),t(),oP(n)&&window.scrollTo(0,0),ox(o)}),w.iG?.addEventListener("popstate",()=>{document.addEventListener("turbo:load",()=>{let e=ob()?.replacedElements||[];(0,oo.wz)(document,e)},{once:!0})}),w.n4?.addEventListener(tW.QE.SUCCESS,()=>{o_(),oC&&(oC=!1,oR(),oH(),(0,tW.u5)())});let ox=e=>{let t=e.querySelector("meta[name=turbo-body-classes]")?.content;t&&(document.body.setAttribute("class",t),document.querySelector("[data-turbo-body]")?.setAttribute("class",t))},o_=()=>{let e=ob()?.bodyClasses;e&&(document.body.setAttribute("class",e),document.querySelector("[data-turbo-body]")?.setAttribute("class",e))},oR=()=>{let e=ob()?.title;e&&(0,ok.T)(e)},oH=()=>{let e=ob()?.transients;if(e){for(let e of document.querySelectorAll("head [data-turbo-transient]"))e.remove();for(let t of e)t.matches("title, script, link[rel=stylesheet]")||(t.setAttribute("data-turbo-transient",""),document.head.append(t))}},oN=(e,t)=>{e&&(e.className=t.className)},oP=e=>"true"!==e.getAttribute("data-turbo-skip-scroll")&&"advance"===e.getAttribute("data-turbo-action");function o$(){if("Intl"in window)try{let e=new window.Intl.DateTimeFormat;return e.resolvedOptions().timeZone}catch{}}w.n4?.addEventListener("turbo:frame-load",()=>{(0,tW.sj)()&&(0,tW.U6)("turbo.frame"),(0,tW.TL)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo.frame"]})}),w.n4?.addEventListener("turbo:load",e=>{(0,oo.zH)();let t=0===Object.keys(e.detail.timing).length;!(0,tW.sj)()||t||(0,tW.OE)()?t&&((0,tW.OE)()||(0,tW.sj)())?(0,tW.r_)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo","turbo.frame"]}):t&&(0,tW.Yl)():((0,tW.TL)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo"]}),(0,tW.BT)({skipIfGoingToReactApp:!0,allowedMechanisms:["turbo","turbo.frame"]}))}),w.n4?.addEventListener("beforeunload",()=>(0,tW.FP)()),w.n4?.addEventListener("turbo:reload",function(e){e instanceof CustomEvent&&(0,tW.Ak)(e.detail.reason)}),w.n4?.addEventListener(tW.QE.END,ov),w.n4?.addEventListener(tW.QE.PROGRESS_BAR.START,of),w.n4?.addEventListener(tW.QE.PROGRESS_BAR.END,om),window.requestIdleCallback(()=>{let e=o$();e&&(0,eE.d8)("tz",encodeURIComponent(e))});var oI=n(76006),oO=n(59840),oD=n(96056),oB=n(8433),oW=n(98576);function oF(e,t){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object")}function oU(e,t){return t.get?t.get.call(e):t.value}function oz(e,t,n){if(t.set)t.set.call(e,n);else{if(!t.writable)throw TypeError("attempted to set read only private field");t.value=n}}function oV(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function oG(e,t){var n=oV(e,t,"get");return oU(e,n)}function oX(e,t,n){oF(e,t),t.set(e,n)}function oZ(e,t,n){var r=oV(e,t,"set");return oz(e,r,n),n}var oK=function(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o};!function(e){e.WebAuthn="webauthn",e.Password="password",e.GitHubMobile="github_mobile",e.TotpApp="app",e.TotpSms="sms"}(h||(h={}));let oJ=(f=new WeakMap,(m=class SudoCredentialOptionsElement extends HTMLElement{connectedCallback(){let e=this.initialState;oZ(this,f,e),this.reRenderPrompt(!0)}reRenderPrompt(e=!1){this.resetPrompt();try{switch(oG(this,f)){case h.WebAuthn:this.renderWebauthnOption();break;case h.GitHubMobile:this.renderGitHubMobileOption(e);break;case h.TotpApp:this.renderTotpAppOption();break;case h.TotpSms:this.renderTotpSmsOption(e);break;case h.Password:default:this.renderPasswordOption()}this.reRenderNavContainer()}catch(e){this.handleUnexpectedPromptError(e)}}handleUnexpectedPromptError(e){let t="";switch(oG(this,f)){case h.GitHubMobile:t=this.githubMobileGenericErrorMessage;break;case h.TotpSms:t=this.smsGenericErrorMessage;break;default:t=this.genericErrorMessage}if(e&&oG(this,f)!==h.Password)throw this.renderPasswordOptionWithError(t),e}renderPasswordOptionWithError(e){this.showPassword(),this.showErrorMessage(e)}resetPrompt(){this.hideErrorMessage(),this.isWebAuthnAvailable()&&this.hideWebAuthn(),this.isGitHubMobileAvailable()&&this.hideGitHubMobile(),this.isTotpAppAvailable()&&this.hideTotpApp(),this.isTotpSmsAvailable()&&this.hideTotpSms(),this.hidePassword()}hideWebAuthn(){this.safeSetElementVisibility(this.webauthnContainer,!1),this.safeSetElementVisibility(this.webauthnNav,!1)}hideGitHubMobile(){this.safeSetElementVisibility(this.githubMobileContainer,!1),this.safeSetElementVisibility(this.githubMobileNav,!1),this.safeSetElementVisibility(this.githubMobileLoading,!1),this.safeSetElementVisibility(this.githubMobileLanding,!1)}hideTotpApp(){this.safeSetElementVisibility(this.totpAppContainer,!1),this.safeSetElementVisibility(this.totpAppNav,!1)}hideTotpSms(){this.safeSetElementVisibility(this.totpSmsContainer,!1),this.safeSetElementVisibility(this.totpSmsLanding,!1),this.safeSetElementVisibility(this.totpSmsNav,!1),this.safeSetElementVisibility(this.totpSmsResendNav,!1)}hidePassword(){this.safeSetElementVisibility(this.passwordContainer,!1),this.safeSetElementVisibility(this.passwordNav,!1)}reRenderNavContainer(){this.isWebAuthnAvailable()&&oG(this,f)!==h.WebAuthn&&this.safeSetElementVisibility(this.webauthnNav,!0),this.isGitHubMobileAvailable()&&oG(this,f)!==h.GitHubMobile&&this.safeSetElementVisibility(this.githubMobileNav,!0),this.isTotpAppAvailable()&&oG(this,f)!==h.TotpApp&&this.safeSetElementVisibility(this.totpAppNav,!0),this.isTotpSmsAvailable()&&oG(this,f)!==h.TotpSms&&this.safeSetElementVisibility(this.totpSmsNav,!0),oG(this,f)!==h.Password&&this.safeSetElementVisibility(this.passwordNav,!0)}renderWebauthnOption(){this.safeSetElementVisibility(this.webauthnContainer,!0),this.webauthnGet?.setState((0,oB.Zh)()?oO.State.Ready:oO.State.Unsupported)}renderGitHubMobileOption(e){try{(0,oD._8)()}catch{}e?(this.safeSetElementVisibility(this.githubMobileLoading,!1),this.safeSetElementVisibility(this.githubMobileLanding,!0),this.safeSetElementVisibility(this.githubMobileContainer,!1)):(this.safeSetElementVisibility(this.githubMobileLoading,!0),this.safeSetElementVisibility(this.githubMobileLanding,!1),this.safeSetElementVisibility(this.githubMobileContainer,!1),this.initiateGitHubMobileAuthRequest())}renderTotpSmsOption(e){e?(this.safeSetElementVisibility(this.totpSmsLanding,!0),this.safeSetElementVisibility(this.totpSmsContainer,!1)):(this.safeSetElementVisibility(this.totpSmsLanding,!1),this.safeSetElementVisibility(this.totpSmsContainer,!0),this.initiateTotpSmsRequest())}renderTotpAppOption(){this.safeSetElementVisibility(this.totpAppContainer,!0)}renderPasswordOption(){this.safeSetElementVisibility(this.passwordContainer,!0),this.loginField?this.loginField.focus():this.passwordField?.focus()}hasMultipleOptions(){return this.isWebAuthnAvailable()||this.isGitHubMobileAvailable()||this.isTotpAppAvailable()||this.isTotpSmsAvailable()}isWebAuthnAvailable(){return"true"===this.webauthnAvailable}isGitHubMobileAvailable(){return"true"===this.githubMobileAvailable}isTotpAppAvailable(){return"true"===this.totpAppAvailable}isTotpSmsAvailable(){return"true"===this.totpSmsAvailable}showWebauthn(){oZ(this,f,h.WebAuthn),this.reRenderPrompt()}showGitHubMobile(){oZ(this,f,h.GitHubMobile),this.reRenderPrompt()}showTotpApp(){oZ(this,f,h.TotpApp),this.reRenderPrompt()}showTotpSms(){oZ(this,f,h.TotpSms),this.reRenderPrompt()}showPassword(){oZ(this,f,h.Password),this.reRenderPrompt()}githubMobileRetry(e){e.preventDefault(),this.showGitHubMobile()}async initiateGitHubMobileAuthRequest(){let e=this.githubMobilePromptUrl,t=document.getElementById("sudo-credential-options-github-mobile-csrf").value,n=new FormData;n.append("authenticity_token",t);try{let t=await fetch(e,{method:"POST",headers:{"X-Requested-With":"XMLHttpRequest"},body:n});if(!t.ok&&oG(this,f)===h.GitHubMobile){this.mobileFailHandler(this.githubMobileGenericErrorMessage);return}let r=await t.json(),i=!!r.challenge;this.safeSetElementVisibility(this.githubMobileNoChallengeMessage,!i),this.safeSetElementVisibility(this.githubMobileChallengeMessage,i),this.safeSetElementVisibility(this.githubMobileChallengeValue,i),i&&(this.githubMobileChallengeValue.textContent=r.challenge);let a=document.getElementsByClassName("js-poll-github-mobile-sudo-authenticate")[0];(0,oD.Hu)(a,()=>this.mobileApprovedHandler(),e=>this.mobileFailHandler(e),()=>this.mobileCancelCheck())}catch(e){oG(this,f)===h.GitHubMobile&&this.mobileFailHandler(this.githubMobileGenericErrorMessage)}finally{oG(this,f)===h.GitHubMobile&&(this.safeSetElementVisibility(this.githubMobileLoading,!1),this.safeSetElementVisibility(this.githubMobileContainer,!0))}}async resendTotpSms(){this.hideErrorMessage(),(0,oW.C)();try{await this.initiateTotpSmsRequest(!0)}catch(e){}document.body.classList.remove("is-sending")}async initiateTotpSmsRequest(e=!1){let t=new URL(this.totpSmsTriggerUrl,window.location.origin);e&&t.searchParams.set("resend","true");let n=document.getElementById("sudo-credential-options-sms-csrf").value,r=new FormData;r.append("authenticity_token",n);try{let n=await fetch(t,{method:"POST",headers:{"X-Requested-With":"XMLHttpRequest"},body:r});if(n.ok||oG(this,f)!==h.TotpSms)oG(this,f)===h.TotpSms&&e&&(0,oW.v)();else{let e=await n.json();this.showErrorMessage(e.error)}}catch(e){oG(this,f)===h.TotpSms&&this.showErrorMessage(this.smsGenericErrorMessage)}oG(this,f)===h.TotpSms&&this.safeSetElementVisibility(this.totpSmsResendNav,!0)}mobileApprovedHandler(){if(oG(this,f)===h.GitHubMobile){let e=this.githubMobileContainer.getElementsByTagName("form")[0];(0,H.Bt)(e)}}mobileFailHandler(e){oG(this,f)===h.GitHubMobile&&(this.showErrorMessage(e),(0,oD.cj)())}mobileCancelCheck(){return oG(this,f)!==h.GitHubMobile}safeSetElementVisibility(e,t){return!!e&&(e.hidden=!t,!0)}showErrorMessage(e){this.flashErrorMessageText&&(this.flashErrorMessageText.textContent=e,this.safeSetElementVisibility(this.flashErrorMessageContainer,!0))}hideErrorMessage(){this.flashErrorMessageText&&(this.flashErrorMessageText.textContent=""),this.safeSetElementVisibility(this.flashErrorMessageContainer,!1)}constructor(...e){super(...e),oX(this,f,{writable:!0,value:void 0})}}).attrPrefix="",m);oK([oI.Lj],oJ.prototype,"initialState",void 0),oK([oI.Lj],oJ.prototype,"webauthnAvailable",void 0),oK([oI.Lj],oJ.prototype,"githubMobileAvailable",void 0),oK([oI.Lj],oJ.prototype,"totpAppAvailable",void 0),oK([oI.Lj],oJ.prototype,"totpSmsAvailable",void 0),oK([oI.Lj],oJ.prototype,"githubMobilePromptUrl",void 0),oK([oI.Lj],oJ.prototype,"githubMobileGenericErrorMessage",void 0),oK([oI.Lj],oJ.prototype,"smsGenericErrorMessage",void 0),oK([oI.Lj],oJ.prototype,"genericErrorMessage",void 0),oK([oI.Lj],oJ.prototype,"totpSmsTriggerUrl",void 0),oK([oI.fA],oJ.prototype,"flashErrorMessageContainer",void 0),oK([oI.fA],oJ.prototype,"flashErrorMessageText",void 0),oK([oI.fA],oJ.prototype,"webauthnContainer",void 0),oK([oI.fA],oJ.prototype,"githubMobileContainer",void 0),oK([oI.fA],oJ.prototype,"githubMobileLoading",void 0),oK([oI.fA],oJ.prototype,"githubMobileLanding",void 0),oK([oI.fA],oJ.prototype,"totpAppContainer",void 0),oK([oI.fA],oJ.prototype,"totpSmsContainer",void 0),oK([oI.fA],oJ.prototype,"totpSmsLanding",void 0),oK([oI.fA],oJ.prototype,"passwordContainer",void 0),oK([oI.fA],oJ.prototype,"githubMobileNoChallengeMessage",void 0),oK([oI.fA],oJ.prototype,"githubMobileChallengeMessage",void 0),oK([oI.fA],oJ.prototype,"githubMobileChallengeValue",void 0),oK([oI.fA],oJ.prototype,"webauthnNav",void 0),oK([oI.fA],oJ.prototype,"githubMobileNav",void 0),oK([oI.fA],oJ.prototype,"totpAppNav",void 0),oK([oI.fA],oJ.prototype,"totpSmsNav",void 0),oK([oI.fA],oJ.prototype,"totpSmsResendNav",void 0),oK([oI.fA],oJ.prototype,"passwordNav",void 0),oK([oI.fA],oJ.prototype,"webauthnGet",void 0),oK([oI.fA],oJ.prototype,"loginField",void 0),oK([oI.fA],oJ.prototype,"passwordField",void 0),oJ=oK([oI.Ih],oJ);let oY=0;function oQ(){if(!document.hasFocus())return;let e=document.querySelector(".js-timeline-marker-form");e&&e instanceof HTMLFormElement&&(0,H.Bt)(e)}let o0="IntersectionObserver"in window?new IntersectionObserver(function(e){for(let t of e)t.isIntersecting&&o1(t.target)},{root:null,rootMargin:"0px",threshold:1}):null;function o1(e){e.classList.remove("js-unread-item","unread-item")}(0,A.N7)(".js-unread-item",{constructor:HTMLElement,add(e){oY++,o0&&o0.observe(e)},remove(e){oY--,o0&&o0.unobserve(e),0===oY&&oQ()}}),(0,A.N7)(".js-discussion[data-channel-target]",{subscribe:e=>(0,B.RB)(e,"socket:message",function(e){let t=e.target,n=e.detail.data;if(t.getAttribute("data-channel-target")===n.gid)for(let e of document.querySelectorAll(".js-unread-item"))o1(e)})});let o4=0,o7=/^\(\d+\)\s+/;function o2(){let e=o4?`(${o4}) `:"";document.title.match(o7)?document.title=document.title.replace(o7,e):document.title=`${e}${document.title}`}function o5(e,t){if(e.getAttribute("data-gid")===t)return e;for(let n of e.querySelectorAll("[data-url][data-gid]"))if(n.getAttribute("data-gid")===t)return n;return null}async function o3(){if(history.state&&history.state.staleRecords){for(let e in await em.x,history.state.staleRecords)for(let t of document.querySelectorAll(`.js-updatable-content [data-url='${e}'], .js-updatable-content[data-url='${e}']`)){let n=history.state.staleRecords[e];t instanceof HTMLElement&&(0,eH.Of)(t,n,!0)}(0,P.lO)(null,"",location.href)}}(0,A.N7)(".js-unread-item",{add(){o4++,o2()},remove(){o4--,o2()}}),(0,A.N7)(".js-socket-channel.js-updatable-content",{subscribe:e=>(0,B.RB)(e,"socket:message",function(e){let{gid:t,wait:n}=e.detail.data,r=e.target,i=t?o5(r,t):r;i&&setTimeout(eH.x0,n||0,i)})}),window.addEventListener("pagehide",eH.z8);try{o3()}catch{}(0,b.on)("upload:setup",".js-upload-avatar-image",function(e){let{form:t}=e.detail,n=e.currentTarget.getAttribute("data-alambic-organization"),r=e.currentTarget.getAttribute("data-alambic-owner-type"),i=e.currentTarget.getAttribute("data-alambic-owner-id");n&&t.append("organization_id",n),r&&t.append("owner_type",r),i&&t.append("owner_id",i)}),(0,b.on)("upload:complete",".js-upload-avatar-image",function(e){let{attachment:t}=e.detail,n=`/settings/avatars/${t.id}`;(0,tZ.W)({content:(0,tK.a_)(document,n),detailsClass:"upload-avatar-details"})}),(0,b.on)("dialog:remove",".upload-avatar-details",async function(e){let t=e.currentTarget.querySelector("#avatar-crop-form"),n=t.getAttribute("data-alambic-avatar-id"),r=`/settings/avatars/${n}?op=destroy`,i=e.currentTarget.querySelector(".js-avatar-post-csrf").getAttribute("value"),a=new Request(r,{method:"POST",headers:{"Scoped-CSRF-Token":i,"X-Requested-With":"XMLHttpRequest"}});await self.fetch(a)});var o9=n(99550);function o6(){if(!(0,o9.l)()||document.querySelector(":target"))return;let e=(0,iF.$z)(location.hash),t=e.startsWith("user-content-")?e:`user-content-${e}`,n=(0,iF.Q)(document,t)??(0,iF.Q)(document,t.toLowerCase());n&&(0,nF.zT)(n)}window.addEventListener("hashchange",o6),document.addEventListener("turbo:load",o6),async function(){await em.x,o6()}(),(0,b.on)("click","a[href]",function(e){let{currentTarget:t}=e;t instanceof HTMLAnchorElement&&t.href===location.href&&location.hash.length>1&&setTimeout(function(){e.defaultPrevented||o6()})});var o8=n(19052);async function se(){await n.e("app_assets_modules_github_user-status-submit_ts").then(n.bind(n,47455))}(0,A.N7)(".js-user-status-container, .js-load-user-status-submit",{subscribe:e=>(0,B.RB)(e,"click",se,{once:!0})});var st=n(64611);function sn(e,t){let n=e.querySelector(".js-user-list-base");n&&(n.textContent=t||n.getAttribute("data-generic-message"),n.hidden=!1)}function sr(e,t){let n=(t||e).querySelectorAll(".js-user-list-error");for(let e of n)e.hidden=!0;let r=t?[t]:e.querySelectorAll(".errored.js-user-list-input-container");for(let e of r)e.classList.remove("errored");let i=e.querySelector(".js-user-list-base");i&&(i.hidden=!0)}function si(e){if(!(e.currentTarget instanceof HTMLElement))return;let t=e.currentTarget.closest(".js-user-list-form"),n=e.currentTarget.closest(".js-user-list-input-container");t&&n&&sr(t,n)}function sa(e){let t=new Map;for(let n of e){let e=n.querySelector(".js-user-lists-create-trigger")?.getAttribute("data-repository-id");if(e){let r=t.get(e);r?r.push(n):t.set(e,[n])}}return t}async function so(e,t,n){let r=new FormData;for(let e of(r.set("authenticity_token",t),n))r.append("repository_ids[]",e);let i=await fetch(e,{method:"POST",body:r,headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}}),a=new Map;if(i.ok){let e=await i.json();for(let t in e)a.set(t,(0,N.r)(document,e[t]))}return a}function ss(e,t){for(let[n,r]of e.entries()){let e=t.get(n)||[];for(let t of e)t.replaceWith(1===e.length?r:r.cloneNode(!0))}}async function sl(){let e=document.querySelectorAll(".js-user-list-menu-content-root");if(0===e.length)return;let t=e[0].getAttribute("data-batch-update-url");if(!t)return;let n=e[0].querySelector(".js-user-list-batch-update-csrf")?.value;if(!n)return;let r=sa(e),i=r.keys(),a=await so(t,n,i);a.size>0&&ss(a,r)}function sc(e){let t=new Promise((t,n)=>{e.addEventListener("user-list-menu-form:success",()=>t()),e.addEventListener("user-list-menu-form:error",e=>n(e))});return(0,H.Bt)(e),t}function su(e){let t=e.target;if(!(t instanceof HTMLDetailsElement)||t.hasAttribute("open"))return;let n=t.querySelector(".js-user-list-menu-form");n&&(0,st.T)(n)&&(0,H.Bt)(n);let r=t.querySelector(".js-user-list-create-trigger-text");r&&(r.textContent="")}function sd(e){let t=e.currentTarget;(0,st.T)(t)?sf(t):sm()}function sf(e){let t=e.getAttribute("data-warn-unsaved-changes")||"Changes you made may not be saved.";window.onbeforeunload=function(e){return e.returnValue=t,t}}function sm(){window.onbeforeunload=null}function sh({currentTarget:e}){e.hasAttribute("open")||sm()}function sp(e){let t=e.currentTarget,n=t.closest("details[open]");if(!n)return;let r=!0,i=t.querySelectorAll("form[data-warn-unsaved-changes]");for(let e of i)if((0,st.T)(e)){let t=e.getAttribute("data-warn-unsaved-changes");r=confirm(t);break}r||e.preventDefault()}function sg(e){let t=e.target;t.classList.remove("will-transition-once")}async function sb(e){let t=e.currentTarget,n=t.getAttribute("data-url");if(!n||sv(t))return;let r=t.getAttribute("data-id")||"",i=t.textContent,a=document.querySelectorAll(`.js-issue-link[data-id='${r}']`);for(let e of a)e.removeAttribute("data-url");try{let e=`${n}/title`,t=await fetch(e,{headers:{"X-Requested-With":"XMLHttpRequest",Accept:"application/json"}});if(!t.ok){let e=Error(),n=t.statusText?` ${t.statusText}`:"";throw e.message=`HTTP ${t.status}${n}`,e}let r=await t.json();sy(a,`${i}, ${r.title}`)}catch(r){let e=(null!=r.response?r.response.status:void 0)||500,n=404===e?t.getAttribute("data-permission-text"):t.getAttribute("data-error-text");sy(a,n||"")}}function sy(e,t){for(let n of e)n instanceof HTMLElement&&(n.classList.add("tooltipped","tooltipped-ne"),n.setAttribute("aria-label",t))}function sv(e){let t=e.getAttribute("data-hovercard-type");switch(t){case"issue":case"pull_request":return!!e.closest("[data-issue-and-pr-hovercards-enabled]");case"discussion":return!!e.closest("[data-discussion-hovercards-enabled]");default:return!1}}(0,y.AC)(".js-user-list-form",async function(e,t){sr(e);let n=e.querySelector("[data-submitting-message]"),r=n?.textContent;for(let t of(n&&(n.textContent=n.getAttribute("data-submitting-message"),n.disabled=!0),e.querySelectorAll(".js-user-list-input")))t.disabled=!0;try{let n=await t.html();(0,b.f)(e,"user-list-form:success",n.html)}catch(t){if(t.response?.status===422)e.replaceWith(t.response.html);else for(let t of(sn(e),n&&(r&&(n.textContent=r),n.disabled=!1),e.querySelectorAll(".js-user-list-input")))t.disabled=!1}}),(0,b.on)("user-list-form:success",".js-follow-list",e=>{let t=e.detail,n=t instanceof DocumentFragment?t.querySelector(".js-target-url"):null;n?.href?location.href=n.href:location.reload()}),(0,O.q6)(".js-user-list-form input",si),(0,O.q6)(".js-user-list-form textarea",si),(0,b.on)("auto-check-error",".js-user-list-form input",function(e){let t=e.currentTarget.closest(".js-user-list-input-container"),n=t?.querySelector(".js-user-list-error");n&&(n.hidden=!1)}),(0,b.on)("toggle",".js-user-list-menu",su,{capture:!0}),(0,O.q6)(".js-user-lists-menu-filter",e=>{let t=e.currentTarget,n=t.value.trim(),r=t.closest(".js-user-list-menu-content-root"),i=r?.querySelector(".js-user-list-create-trigger-text");i&&(i.textContent=n?`"${n}"`:"")}),(0,y.AC)(".js-user-list-menu-form",async function(e,t){let n;try{n=await t.json()}catch(t){(0,g.v)(),(0,b.f)(e,"user-list-menu-form:error",t);return}if(n.json.didStar){let t=e.closest(".js-toggler-container");t&&t.classList.add("on");let r=n.json.starCount;if(r){let t=e.closest(".js-social-container");t&&iK(t,r)}}let r=e.closest(".js-user-list-menu-content-root[data-update-after-submit]");if(r)for(let t of e.querySelectorAll(".js-user-list-menu-item"))t.checked=t.defaultChecked;n.json.didCreate?await sl():r&&await (0,eH.x0)(r),(0,b.f)(e,"user-list-menu-form:success")}),(0,b.on)("click",".js-user-list-delete-confirmation-trigger",e=>{let{currentTarget:t}=e,n=t.getAttribute("data-template-id");if(!n)return;let r=document.getElementById(n);if(!r||!(r instanceof HTMLTemplateElement))return;let i=t.closest(".js-edit-user-list-dialog");i&&(i.open=!1);let a=r.content.cloneNode(!0),o=r.getAttribute("data-labelledby");(0,tZ.W)({content:a,labelledBy:o})}),(0,b.on)("click",".js-user-lists-create-trigger",async function(e){let{currentTarget:t}=e,n=document.querySelector(".js-user-list-create-dialog-template"),r=e.currentTarget.getAttribute("data-repository-id"),i=t.closest(".js-user-list-menu-content-root"),a=i?.querySelector(".js-user-lists-menu-filter"),o=a?.value.trim();if(!n||!(n instanceof HTMLTemplateElement)||!r){t instanceof HTMLButtonElement&&(t.disabled=!0);return}let s=n.getAttribute("data-label");if(i&&(0,st.T)(i)){let e=i.querySelector(".js-user-list-menu-form");e&&await sc(e)}let l=new nG.R(n,{repositoryId:r,placeholderName:o}),c=await (0,tZ.W)({content:l,label:s});c.addEventListener("user-list-form:success",async e=>{let n=e.detail;if(!(n instanceof DocumentFragment))return;let r=n.querySelector(".js-target-url"),i="true"===r?.getAttribute("data-did-star");if(!i)return;let a=t.closest(".js-toggler-container");a&&a.classList.add("on");let o=r?.getAttribute("data-star-count");if(o){let e=t.closest(".js-social-container");e&&iK(e,o)}await sl();let s=c.closest("details");s&&(s.open=!1)})}),(0,A.N7)("[data-warn-unsaved-changes]",{add(e){e.addEventListener("input",sd),e.addEventListener("change",sd),e.addEventListener("submit",sm);let t=e.closest("details-dialog");t&&(t.closest("details").addEventListener("toggle",sh),t.addEventListener("details-dialog-close",sp))},remove(e){e.removeEventListener("input",sd),e.removeEventListener("change",sd),e.removeEventListener("submit",sm);let t=e.closest("details-dialog");t&&(t.closest("details").removeEventListener("toggle",sh),t.removeEventListener("details-dialog-close",sp),sm())}}),(0,A.N7)(".will-transition-once",{constructor:HTMLElement,subscribe:e=>(0,B.RB)(e,"transitionend",sg)}),(0,A.N7)(".js-issue-link",{subscribe:e=>(0,B.RB)(e,"mouseenter",sb)});var sw=n(12085),sS=n.n(sw);function sE(){return[Math.floor(255*Math.random()+0),Math.floor(255*Math.random()+0),Math.floor(255*Math.random()+0)]}function sL(e,t){let n=sS().rgb.hsl(t);e.style.setProperty("--label-r",t[0].toString()),e.style.setProperty("--label-g",t[1].toString()),e.style.setProperty("--label-b",t[2].toString()),e.style.setProperty("--label-h",n[0].toString()),e.style.setProperty("--label-s",n[1].toString()),e.style.setProperty("--label-l",n[2].toString())}function sj(e,t){e.blur();let n=e.closest("form"),r=n.querySelector(".js-new-label-color-input");(0,H.Se)(r,`#${sS().rgb.hex(t)}`);let i=n.querySelector(".js-new-label-color");sL(i,t)}function sA(e,t){let n=e.closest(".js-label-error-container");n.classList.add("errored"),e.textContent=t,e.hidden=!1}function sT(e){let t=e.closest(".js-label-error-container");t.classList.remove("errored"),e.hidden=!0}function sq(e,t,n){let r=t.querySelector(e);r&&(n?sA(r,n[0]):sT(r))}function sk(e,t){sq(".js-label-name-error",e,t.name),sq(".js-label-description-error",e,t.description),sq(".js-label-color-error",e,t.color)}function sC(e){sq(".js-label-name-error",e,null),sq(".js-label-description-error",e,null),sq(".js-label-color-error",e,null)}function sM(e,t,n,r,i){let a=new URL(`${e}${encodeURIComponent(t)}`,window.location.origin),o=new URLSearchParams(a.search.slice(1));return o.append("color",n),r&&o.append("description",r),i&&o.append("id",i),a.search=o.toString(),a.toString()}function sx(e){let t=null,n=e.querySelector(".js-new-label-description-input");return n instanceof HTMLInputElement&&n.value.trim().length>0&&(t=n.value.trim()),t}function s_(e){let t=e.querySelector(".js-new-label-color-input");return t.checkValidity()?t.value.trim().replace(/^#/,""):"ededed"}function sR(e,t){let n=e.querySelector(".js-new-label-name-input"),r=n.value.trim();return r.length<1&&(r=t.getAttribute("data-default-name")),r}async function sH(e){let t;let n=e.closest(".js-label-preview-container");if(!n)return;let r=e.closest(".js-label-form"),i=r.querySelector(".js-new-label-error"),a=r.getAttribute("data-label-id"),o=n.querySelector(".js-label-preview"),s=sR(r,o);if(!r.checkValidity()&&"Label preview"!==s)return;let l=s_(r),c=sx(r),u=o.getAttribute("data-url-template"),d=sM(u,s,l,c,a);if(n.hasAttribute("data-last-preview-url")){let e=n.getAttribute("data-last-preview-url");if(d===e)return}try{t=await (0,tK.a_)(document,d)}catch(t){let e=await t.response.json();sk(r,e),i&&(i.textContent=e.message,i.hidden=!1);return}i&&(i.textContent="",i.hidden=!0),sC(r),o.textContent="",o.appendChild(t),n.setAttribute("data-last-preview-url",d)}function sN(e){sH(e.target)}function sP(e,t){let n=e.closest(".js-details-container");n.classList.toggle("is-empty",t)}function s$(e){let t=document.querySelector(".js-labels-count"),n=Number(t.textContent),r=n+e;t.textContent=r.toString();let i=document.querySelector(".js-labels-label");return i.textContent=i.getAttribute(1===r?"data-singular-string":"data-plural-string"),r}async function sI(e){let t=e.querySelector(".js-new-label-name-input");if(!t)return;let n=e.querySelector(".js-new-label-color-input"),r=sE(),i=`#${sS().rgb.hex(r)}`;n.value=i;let a=e.querySelector(".js-new-label-color");sL(a,r);let o=document.querySelector(".js-new-label-name"),s=o.textContent;(0,H.Se)(t,s),(0,ew.OD)(t),sH(a)}(0,O.q6)(".js-label-filter-field",function(e){let t=e.target,n=t.closest("details-menu"),r=n.querySelector(".js-new-label-name");if(!r)return;let i=t.value.trim();r.textContent=i}),(0,b.on)("filterable:change",".js-filterable-issue-labels",function(e){let t=e.currentTarget.closest("details-menu"),n=t.querySelector(".js-add-label-button");if(!n)return;let r=e.detail.inputField,i=r.value.trim().toLowerCase(),a=!1;for(let e of t.querySelectorAll("input[data-label-name]")){let t=e.getAttribute("data-label-name")||"";if(t.toLowerCase()===i){a=!0;break}}n.hidden=0===i.length||a}),(0,O.ZG)(".js-new-label-color-input",function(e){let t=e.closest("form"),n=t.querySelector(".js-new-label-swatches");n.hidden=!1,e.addEventListener("blur",function(){n.hidden=!0},{once:!0})}),(0,O.q6)(".js-new-label-color-input",function(e){let t=e.target,n=t.value.trim();if(!(n.length<1)){if(0!==n.indexOf("#")&&(n=`#${n}`,t.value=n),t.checkValidity()){t.classList.remove("color-fg-danger");let e=t.closest("form"),r=e.querySelector(".js-new-label-color");sL(r,sS().hex.rgb(n))}else t.classList.add("color-fg-danger")}}),(0,O.w4)("keyup",".js-new-label-color-input",function(e){let t=e.target,n=t.value.trim();if(0!==n.indexOf("#")&&(n=`#${n}`,t.value=n),t.checkValidity()){let e=t.closest("form"),r=e.querySelector(".js-new-label-color");sL(r,sS().hex.rgb(n))}(0,b.f)(t,"change",!1);let r=t.closest("form");sC(r)}),(0,O.w4)("keyup",".js-new-label-description-input",function(e){let t=e.target,n=t.form;sC(n)}),(0,O.w4)("keyup",".js-new-label-color-input",function(e){let t=e.target,n=t.form;sC(n)}),(0,b.on)("click",".js-new-label-color",async function(e){let t=e.currentTarget,n=sE();sj(t,n),sH(t)}),(0,b.on)("mousedown",".js-new-label-color-swatch",function(e){let t=e.currentTarget,n=t.getAttribute("data-color");sj(t,sS().hex.rgb(n)),sH(t);let r=t.closest(".js-new-label-swatches");r.hidden=!0}),(0,b.on)("toggle",".js-new-label-modal",function(e){e.target.hasAttribute("open")&&sI(e.target)},{capture:!0}),(0,y.AC)(".js-new-label-modal-form",async function(e,t){let n;let r=e.querySelector(".js-new-label-error");try{n=await t.html()}catch(t){let e=t.response.json;r.textContent=e.message,r.hidden=!1}if(!n)return;r.hidden=!0,document.querySelector(".js-new-label-modal").removeAttribute("open");let i=document.querySelector(".js-issue-labels-menu-content"),a=i.querySelector(".js-filterable-issue-labels"),o=n.html.querySelector("input");a.prepend(n.html),a.classList.add("filter-sort-list-refresh"),o&&o.dispatchEvent(new Event("change",{bubbles:!0}));let s=i.querySelector(".js-label-filter-field");s.value=s.defaultValue,s.focus()}),(0,b.on)("click",".js-edit-label-cancel",function(e){let t=e.target.closest("form");sC(t),t.reset();let n=t.querySelector(".js-new-label-color-input"),r=n.value,i=t.querySelector(".js-new-label-color");sL(i,sS().hex.rgb(r)),(0,ew.Qc)(t),sH(n);let a=e.currentTarget.closest(".js-labels-list-item");if(a){let e=a.querySelector(".js-update-label");e.classList.add("d-none");let t=a.querySelector(".js-label-preview");if(t){t.classList.add("d-none");let e=a.querySelector(".js-label-link");e.classList.remove("d-none")}let n=a.querySelectorAll(".js-hide-on-label-edit");for(let e of n)e.hidden=!e.hidden}}),(0,y.AC)(".js-update-label",async function(e,t){let n;try{n=await t.html()}catch(n){let t=n.response.json;sk(e,t);return}sC(e);let r=e.closest(".js-labels-list-item");r.replaceWith(n.html)}),(0,y.AC)(".js-create-label",async function(e,t){let n;try{n=await t.html()}catch(n){let t=n.response.json;sk(e,t);return}e.reset(),sC(e),document.querySelector(".js-label-list").prepend(n.html),s$(1),sP(e,!1);let r=e.querySelector(".js-new-label-color"),i=sE();sj(r,i),sH(e.querySelector(".js-new-label-name-input")),(0,ew.Qc)(e);let a=e.closest(".js-details-container");a instanceof HTMLElement&&(0,tM.Qp)(a)}),(0,b.on)("click",".js-details-target-new-label",function(){let e=document.querySelector(".js-create-label"),t=e.querySelector(".js-new-label-name-input");t.focus()}),(0,b.on)("click",".js-edit-label",function(e){let t=e.currentTarget.closest(".js-labels-list-item"),n=t.querySelector(".js-update-label");n.classList.remove("d-none");let r=n.querySelector(".js-new-label-name-input");r.focus();let i=t.querySelector(".js-label-preview");if(i){i.classList.remove("d-none");let e=t.querySelector(".js-label-link");e.classList.add("d-none")}let a=t.querySelectorAll(".js-hide-on-label-edit");for(let e of a)e.hidden=!e.hidden}),(0,y.AC)(".js-delete-label",async function(e,t){let n=e.closest(".js-labels-list-item");n.querySelector(".js-label-delete-spinner").hidden=!1,await t.text();let r=s$(-1);sP(e,0===r),n.remove()});let sO=(0,D.D)(sN,500);function sD(){let e=document.querySelector(".js-reveal-custom-thread-settings").checked,t=!document.querySelector(".js-custom-thread-notification-option:checked"),n=document.querySelector(".js-custom-thread-settings"),r=document.querySelector("[data-custom-option-required-text]"),i=e&&t?r.getAttribute("data-custom-option-required-text"):"";r.setCustomValidity(i),n.hidden=!e}(0,b.on)("suggester:complete",".js-new-label-name-input",sO),(0,O.q6)(".js-new-label-name-input",sO),(0,O.q6)(".js-new-label-description-input",sO),(0,O.q6)(".js-new-label-color-input",sO),(0,O.w4)("keypress",".js-new-label-name-input",function(e){let t=e.target,n=parseInt(t.getAttribute("data-maxlength"));(0,eF.rq)(t.value)>=n&&e.preventDefault()}),(0,b.on)("click",".js-issues-label-select-menu-item",function(e){(e.altKey||e.shiftKey)&&(e.preventDefault(),e.stopPropagation(),e.altKey&&(window.location.href=e.currentTarget.getAttribute("data-excluded-url")),e.shiftKey&&(window.location.href=e.currentTarget.getAttribute("data-included-url")))}),(0,O.w4)("keydown",".js-issues-label-select-menu-item",function(e){if("Enter"!==e.key||!e.altKey&&!e.shiftKey)return;let t=e.currentTarget;e.preventDefault(),e.stopPropagation(),t instanceof HTMLAnchorElement&&(e.altKey&&(window.location.href=t.getAttribute("data-excluded-url")),e.shiftKey&&(window.location.href=t.getAttribute("data-included-url")))}),(0,b.on)("click",".js-open-label-creation-modal",async function(e){e.stopImmediatePropagation();let t=await (0,tZ.W)({content:document.querySelector(".js-label-creation-template").content.cloneNode(!0),detailsClass:"js-new-label-modal"});sI(t)},{capture:!0}),(0,b.on)("change",".js-thread-notification-setting",sD),(0,b.on)("change",".js-custom-thread-notification-option",sD),(0,b.on)("reset",".js-custom-thread-settings-form",sD);var sB=function(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o};let sW=class CollapsibleSidebarWidgetElement extends HTMLElement{get activeClass(){return this.getAttribute("active-class")||"collapsible-sidebar-widget-active"}get loadingClass(){return this.getAttribute("loading-class")||"collapsible-sidebar-widget-loading"}get url(){return this.getAttribute("url")||""}get isOpen(){return this.hasAttribute("open")}set isOpen(e){e?this.setAttribute("open",""):this.removeAttribute("open")}onKeyDown(e){if("Enter"===e.code||"Space"===e.code)return e.preventDefault(),this.load()}onMouseDown(e){return e.preventDefault(),this.load()}load(){return this.pendingRequest?this.pendingRequest.abort():this.collapsible.hasAttribute("loaded")?this.isOpen?this.setClose():this.setOpen():(this.setLoading(),this.updateCollapsible())}setLoading(){this.classList.add(this.loadingClass),this.classList.remove(this.activeClass)}setOpen(){this.classList.add(this.activeClass),this.classList.remove(this.loadingClass),this.isOpen=!0}setClose(){this.classList.remove(this.activeClass),this.classList.remove(this.loadingClass),this.isOpen=!1}handleAbort(){this.pendingRequest=null,this.setClose()}async updateCollapsible(){try{this.pendingRequest=new AbortController,this.pendingRequest.signal.addEventListener("abort",()=>this.handleAbort());let e=await fetch(this.url,{signal:this.pendingRequest?.signal,headers:{Accept:"text/html","X-Requested-With":"XMLHttpRequest"}});if(this.pendingRequest=null,!e.ok)return this.setClose();let t=await e.text();this.collapsible.innerHTML=t,this.collapsible.setAttribute("loaded",""),this.setOpen()}catch{return this.pendingRequest=null,this.setClose()}}};sB([oI.fA],sW.prototype,"collapsible",void 0),sW=sB([oI.Ih],sW);var sF=function(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o};let sU=((p=class SidebarMemexInputElement extends HTMLElement{get isDisabled(){return this.read?.hasAttribute("disabled")}set hasErrored(e){e?this.setAttribute("errored",""):this.removeAttribute("errored")}set disabled(e){e?this.setAttribute("disabled",""):this.removeAttribute("disabled")}get hasExpanded(){return"true"===this.read.getAttribute("aria-expanded")}get detailsElement(){return this.querySelector("details")??null}connectedCallback(){this.disabled=this.read?.disabled??!0,this.detailsElement?.addEventListener("toggle",()=>this.handleSelectMenuToggle())}disconnectedCallback(){this.detailsElement?.removeEventListener("toggle",()=>this.handleSelectMenuToggle())}handleSelectMenuToggle(){this.detailsElement&&!this.detailsElement?.open?this.disabled=!0:this.detailsElement&&this.detailsElement?.open&&(this.disabled=!1)}handleDetailsSelect(e){let t=e.target,n=e.detail?.relatedTarget,r=t.closest("details"),i=r?.querySelector("[data-menu-button]"),a=r?.querySelector("summary");if("true"===n.getAttribute("aria-checked")){for(let t of(n.setAttribute("aria-checked","false"),e.preventDefault(),this.inputs))if(n.contains(t)){this.updateCell(t.name,""),i?.innerHTML&&(i.innerHTML=t.placeholder);break}r?.removeAttribute("open"),a?.focus()}}handleDetailsSelected(e){let t=e.detail?.relatedTarget;for(let e of this.inputs)if(t.contains(e)){this.updateCell(e.name,e.value);break}}mouseDownFocus(e){this.isDisabled&&this.onFocus(e)}keyDownFocus(e){("Enter"===e.code||"Space"===e.code)&&(this.detailsElement&&this.onSelectMenuOpen(),this.read!==document.activeElement&&this.onFocus(e))}mouseDownFocusHeader(){this.detailsElement&&this.onSelectMenuOpen()}onChange(e){let t=e.target;"date"!==t.getAttribute("type")&&this.updateCell(this.read?.name,this.read?.value)}onFocus(e){e.preventDefault(),this.disabled=!1,this.read.disabled=!1,this.read.focus()}onSelectMenuOpen(){this.detailsElement&&(this.detailsElement.open=!0)}onBlur(e){if(this.hasExpanded){e.preventDefault();return}let t=e.target;"date"===t.getAttribute("type")&&this.updateCell(this.read?.name,this.read?.value),this.read.disabled=!0,this.disabled=!0}onKeyDown(e){"Enter"!==e.code&&"Tab"!==e.code||(e.preventDefault(),e.stopPropagation(),this.hasExpanded||this.read.blur())}async updateCell(e="",t=""){let n=new FormData;for(let r of(n.set(e,t),n.set("ui",this.instrumentType),this.parameters))n.set(r.name,r.value);try{if(this.write){let e=this.read.value,t="date"===this.read.type&&e?this.format.format(Date.parse(e)):e;this.write.textContent=e?t:this.read.placeholder}let e=await fetch(this.updateUrl,{method:"PUT",body:n,headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest","Scoped-CSRF-Token":`${this.csrfToken}`}});if(!e.ok)throw Error("connection error");if(!this.write)return;let r=await e.json(),i=r.memexProjectItem.memexProjectColumnValues.find(e=>e.memexProjectColumnId===Number(this.columnId)),a=i.value,o=this.parseAndFormatUpdate(a);this.write.innerHTML=t?o:this.read.placeholder}catch(e){this.hasErrored=!0}}parseAndFormatUpdate(e){switch(this.read.type){case"date":{let t=e.value?Date.parse(e.value):void 0;return t?this.format.format(t):""}case"number":return null==e.value?"":e.value;default:return e.html??""}}constructor(...e){super(...e),this.format=Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",year:"numeric",timeZone:"UTC"}),this.updateUrl="",this.csrfToken="",this.instrumentType="",this.columnId=1}}).attrPrefix="",p);sF([oI.Lj],sU.prototype,"updateUrl",void 0),sF([oI.Lj],sU.prototype,"csrfToken",void 0),sF([oI.Lj],sU.prototype,"instrumentType",void 0),sF([oI.Lj],sU.prototype,"columnId",void 0),sF([oI.GO],sU.prototype,"inputs",void 0),sF([oI.fA],sU.prototype,"read",void 0),sF([oI.fA],sU.prototype,"write",void 0),sF([oI.GO],sU.prototype,"parameters",void 0),sU=sF([oI.Ih],sU);var sz=n(98973);function sV(e,t=!1){(t||!s5(e))&&(e instanceof HTMLFormElement?(0,H.Bt)(e):s7(e))}function sG(e){let t=e.currentTarget,n=t.closest(".js-issue-sidebar-form")||t.querySelector(".js-issue-sidebar-form");sV(n)}function sX(e,t){e.replaceWith((0,N.r)(document,t))}function sZ(e,t){let n=e.querySelector(`[data-menu-trigger="${t}"]`);n?.focus()}function sK(e,t){let n=e.getAttribute("data-cache-name");return`${t}:sidebar:${n}`}function sJ(e,t,n){let r=e.getAttribute("data-cache-name");if(!r)return;let i=[];for(let[e,n]of t.entries())-1!==e.indexOf(r)&&i.push([e,n]);let a=i.filter(e=>""!==e[1]);a.length>0?sessionStorage.setItem(n,JSON.stringify(a)):sessionStorage.removeItem(n)}(0,b.on)("details-menu-selected",".js-discussion-sidebar-menu",function(e){let t=e.detail.relatedTarget,n=e.currentTarget,r=t.closest(".js-issue-sidebar-form"),i=n.hasAttribute("data-multiple");if(t.hasAttribute("data-clear-assignees")){let e=n.querySelectorAll('input[name="issue[user_assignee_ids][]"]:checked');for(let t of e)t.disabled=!1,t.checked=!1;sV(r)}else i?n.closest("details").addEventListener("toggle",sG,{once:!0}):sV(r)},{capture:!0}),(0,y.AC)(".js-issue-sidebar-form",async function(e,t){let n;try{let n=await t.html(),r=e.closest(".js-discussion-sidebar-item"),i=r?.querySelector(".select-menu")?.getAttribute("id"),a=r?.parentElement;r.replaceWith(n.html),a&&i&&sZ(a,i)}catch(e){if(e instanceof Error)throw e}finally{e.dispatchEvent(new CustomEvent("submit:complete",{bubbles:!0,detail:{error:n}}))}}),(0,b.on)("click","div.js-issue-sidebar-form .js-suggested-reviewer",function(e){let t=e.currentTarget,n=t.closest(".js-issue-sidebar-form");s7(n,"post",{name:t.name,value:t.value}),e.preventDefault()}),(0,b.on)("click","div.js-issue-sidebar-form .js-issue-assign-self",function(e){let t=e.currentTarget,n=t.closest(".js-issue-sidebar-form");s7(n,"post",{name:t.name,value:t.value}),t.remove(),document.querySelector("form#new_issue .js-submit-button-value")?.remove(),e.preventDefault()}),(0,b.on)("click",".js-issue-unassign-self",function(e){let t=e.currentTarget.closest(".js-issue-sidebar-form");s7(t,"delete"),e.preventDefault()});let sY=new Set;function sQ(){sY.clear()}async function s0(e,t){let n=e.getAttribute("data-cache-name"),r=sessionStorage.getItem(t);if(!n||!r||sY.has(n))return;sY.add(n);let i=JSON.parse(r),a=[];for(let[t,n]of i){if("[object String]"!==Object.prototype.toString.call(n))continue;let r=document.createElement("input");r.type="hidden",r.value=n,r.name=t,e.appendChild(r),a.push(r)}try{for(let t of(await s2(e),a))t.remove()}catch(e){sY.delete(n)}}let s1=!1;function s4(e,t){if(s1)return;let n=s8(e);sJ(e,n,t),sQ()}async function s7(e,t="post",n){await s2(e,t,n);let r=e.closest(".js-discussion-sidebar-item"),i=r?.querySelector(".select-menu")?.getAttribute("id"),a=r?.parentElement;a&&i&&sZ(a,i)}async function s2(e,t="post",n){let r=s8(e);n&&r.append(n.name,n.value);let i=e.getAttribute("data-url");if(!i)return;let a=e.querySelector(".js-data-url-csrf"),o=await fetch(i,{method:t,body:"delete"===t?"":r,mode:"same-origin",headers:{"Scoped-CSRF-Token":a.value,"X-Requested-With":"XMLHttpRequest"}});if(!o.ok)return;let s=await o.text();sX(e.closest(".js-discussion-sidebar-item"),s)}function s5(e){let t=e.getAttribute("data-reviewers-team-size-check-url");if(!t)return!1;let n=[...document.querySelectorAll(".js-reviewer-team")].map(e=>e.getAttribute("data-id")),r=e instanceof HTMLFormElement?new FormData(e):s8(e),i=new URLSearchParams(r).getAll("reviewer_team_ids[]"),a=i.filter(e=>!n.includes(e));if(0===a.length)return!1;let o=new URLSearchParams(a.map(e=>["reviewer_team_ids[]",e]));return s3(e,`${t}?${o}`),!0}async function s3(e,t){let n=await fetch(t);if(!n.ok)return;let r=await n.text();if(r.match(/[^\w-]js-large-team[^\w-]/))s9(e,r);else{sV(e,!0);return}}function s9(e,t){let n=e.querySelector(".js-large-teams-check-warning-container");for(;n.firstChild;)n.removeChild(n.firstChild);n.appendChild((0,N.r)(document,t));let r=n.querySelector("details");function i(t){if(t.target instanceof Element){if(r.open=!1,!t.target.classList.contains("js-large-teams-confirm-button")){let t=e.querySelectorAll("input[name='reviewer_team_ids[]']");for(let e of t)n.querySelector(`.js-large-team[data-id='${e.value}']`)&&(e.checked=!1)}sV(e,!0),t.preventDefault()}}n.querySelector(".js-large-teams-confirm-button").addEventListener("click",i,{once:!0}),n.querySelector(".js-large-teams-cancel-button").addEventListener("click",i,{once:!0}),r.addEventListener("details-dialog-close",i,{once:!0}),r.open=!0}function s6(e){let t=e.closest(".js-project-column-menu-dropdown"),n=t.querySelector(".js-project-column-menu-summary"),r=e.getAttribute("data-column-name");n.textContent=r}function s8(e){let t=e.closest("form");if(!t)return new FormData;let n=new FormData(t),r=n.entries(),i=new FormData;for(let[e,n]of r)t.contains(le(t,e,n.toString()))&&i.append(e,n);return i}function le(e,t,n){for(let r of e.elements)if((r instanceof HTMLInputElement||r instanceof HTMLTextAreaElement||r instanceof HTMLButtonElement)&&r.name===t&&r.value===n)return r;return null}(0,A.N7)("[data-cacher]",{add(e){let t=sK(e,(0,sz.e)());s0(e,t),window.addEventListener("pagehide",()=>s4(e,t)),window.addEventListener("turbo:before-visit",()=>s4(e,t)),window.addEventListener("submit",e=>{e.defaultPrevented||(s1=!0,setTimeout(()=>{for(let e of Object.keys(sessionStorage))-1!==e.indexOf(t)&&(sessionStorage.removeItem(e),sQ())},0))},{capture:!0})}}),(0,b.on)("click","div.js-project-column-menu-container .js-project-column-menu-item button",async function(e){let t=e.currentTarget;s6(t);let n=t.getAttribute("data-url"),r=t.parentElement.querySelector(".js-data-url-csrf"),i=t.getAttribute("data-card-id"),a=new FormData;a.append("card_id",i),a.append("use_automation_prioritization","true"),e.preventDefault();let o=await fetch(n,{method:"PUT",mode:"same-origin",body:a,headers:{"Scoped-CSRF-Token":r.value,"X-Requested-With":"XMLHttpRequest"}});if(!o.ok)return;let s=document.activeElement,l=t.closest(".js-project-column-menu-dropdown");if(s&&l.contains(s))try{s.blur()}catch(e){}}),(0,b.on)("click",".js-prompt-dismiss",function(e){e.currentTarget.closest(".js-prompt").remove()}),(0,b.on)("click",".js-convert-to-draft",function(e){let t=e.currentTarget.getAttribute("data-url"),n=e.currentTarget.parentElement.querySelector(".js-data-url-csrf");fetch(t,{method:"POST",mode:"same-origin",headers:{"Scoped-CSRF-Token":n.value,"X-Requested-With":"XMLHttpRequest"}})}),(0,b.on)("click","div.js-restore-item",async function(e){let t=e.currentTarget.getAttribute("data-url"),n=e.currentTarget.getAttribute("data-column"),r=e.currentTarget.querySelector(".js-data-url-csrf"),i=new FormData;i.set("memexProjectItemIds[]",n);let a=await fetch(t,{method:"PUT",mode:"same-origin",body:i,headers:{"Scoped-CSRF-Token":r.value,"X-Requested-With":"XMLHttpRequest"}});if(!a.ok)throw Error("connection error");sG(e)}),(0,A.N7)("#clear-project-search-button",e=>{e?.setAttribute("type","button"),e?.addEventListener("click",()=>{let e=document.getElementById("project-search-input");e&&(e.value="",e.focus())})}),(0,tW.cY)(),(0,n7.c)("NAVIGATION_INTERCEPTS")&&(0,tW.RN)()},47442:(e,t,n)=>{"use strict";n.d(t,{s:()=>BaseBatchDeferredContentElement});var r=n(76006),i=n(67525);let a=class AutoFlushingQueue{push(e){let t=`item-${this.index++}`;return this.timer&&(window.clearTimeout(this.timer),this.timer=null),this.elements.length>=this.limit&&this.flush(),this.timer=window.setTimeout(()=>{this.timer=null,this.flush()},this.timeout),this.elements.push([e,t]),t}onFlush(e){this.callbacks.push(e)}async flush(){let e=this.elements.splice(0,this.limit);0!==e.length&&await Promise.all(this.callbacks.map(t=>t(e)))}constructor(e=50,t=30){this.elements=[],this.timer=null,this.callbacks=[],this.timeout=e,this.limit=t,this.index=0}};let BatchLoader=class BatchLoader{loadInBatch(e){let t=this.autoFlushingQueue.push(e);return new Promise(e=>this.callbacks.set(t,e))}async load(e){let t=new Map;for(let[n,r]of e)t.set(r,n);let n=new FormData;for(let[e,r]of t.entries())for(let t of r.inputs)n.append(`items[${e}][${t.name}]`,t.value);if(0===Array.from(n.values()).length)return;n.set("_method","GET");let r=await fetch(this.url,{method:"POST",body:n,headers:{Accept:"application/json","X-Requested-With":"XMLHttpRequest"}});if(r.ok){let e=await r.json();if(!e||"object"!=typeof e||Array.isArray(e))throw Error("Malformed batch response");for(let t in e){let n=this.callbacks.get(t);if(n){let r=e[t];this.validate(r),n(r)}}}}constructor(e,t){this.url=e,this.callbacks=new Map,this.autoFlushingQueue=new a,this.autoFlushingQueue.onFlush(async e=>{this.load(e)}),this.validate=t}};var o=function(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o};let BaseBatchDeferredContentElement=class BaseBatchDeferredContentElement extends HTMLElement{async connectedCallback(){let e=await this.batchLoader.loadInBatch(this);this.update(e)}get batchLoader(){let e=this.getAttribute("data-url");if(!e)throw Error(`${this.tagName} element requires a data-url attribute`);let t=this.batchLoaders.get(e);return t||(t=new BatchLoader(e,e=>this.validate(e)),this.batchLoaders.set(e,t)),t}};let s=new Map,l=class BatchDeferredContentElement extends BaseBatchDeferredContentElement{validate(e){if("string"!=typeof e)throw Error("Batch deferred content was not a string")}update(e){let t=(0,i.r)(document,e);this.replaceWith(t)}constructor(...e){super(...e),this.batchLoaders=s}};o([r.GO],l.prototype,"inputs",void 0),l=o([r.Ih],l)},90596:()=>{let e;let t=!1;function n(){e=document.activeElement,document.body&&document.body.classList.toggle("intent-mouse",t)}document.addEventListener("mousedown",function(){t=!0,e===document.activeElement&&n()},{capture:!0}),document.addEventListener("keydown",function(){t=!1},{capture:!0}),document.addEventListener("focusin",n,{capture:!0})},78332:(e,t,n)=>{"use strict";n.d(t,{OD:()=>s,Qc:()=>l,nz:()=>o});var r=n(57619);function i(e,t,n){let i=n.closest(".js-characters-remaining-container");if(!i)return;let a=i.querySelector(".js-characters-remaining"),o=String(a.getAttribute("data-suffix")),s=(0,r.rq)(e),l=t-s;l<=20?(a.textContent=`${l} ${o}`,a.classList.toggle("color-fg-danger",l<=5),a.setAttribute("role","status"),a.hidden=!1):(a.setAttribute("role","none"),a.hidden=!0)}function a(e){return e.hasAttribute("data-maxlength")?parseInt(e.getAttribute("data-maxlength")||""):e.maxLength}function o(e){let t=a(e),n=(0,r.rq)(e.value);return t-n<0}function s(e){let t=a(e);i(e.value,t,e)}function l(e){let t=e.querySelectorAll(".js-characters-remaining-container");for(let e of t){let t=e.querySelector(".js-characters-remaining-field");s(t)}}(0,n(254).ZG)(".js-characters-remaining-field",function(e){function t(){(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&s(e)}t(),e.addEventListener("input",t),e.addEventListener("blur",()=>{e.removeEventListener("input",t)},{once:!0})})},26361:()=>{document.addEventListener("click",function(e){if(!(e.target instanceof Element))return;let t=e.target.closest("a[data-confirm], input[type=submit][data-confirm], input[type=checkbox][data-confirm], button[data-confirm]");if(!t)return;let n=t.getAttribute("data-confirm");if(n){if(t instanceof HTMLInputElement&&t.hasAttribute("data-confirm-checked")&&!t.checked)return;confirm(n)||(e.stopImmediatePropagation(),e.preventDefault())}},!0)},92792:(e,t,n)=>{"use strict";n.d(t,{r:()=>d});var r=n(56959),i=n(81574),a=n(52191),o=n(36071),s=n(59753);let l=null;function c({currentTarget:e}){if(e.hasAttribute("open")){let t=e.querySelector("[autofocus]");t&&t.focus()}else{let t=e.querySelector("summary");t&&t.focus()}}function u({currentTarget:e}){e.hasAttribute("open")?(l&&l!==e&&l.removeAttribute("open"),l=e):e===l&&(l=null)}function d(e){e.hasAttribute("open")?e.removeAttribute("open"):e.setAttribute("open","open")}document.addEventListener("keydown",function(e){!e.defaultPrevented&&"Escape"===e.key&&l&&l.removeAttribute("open")}),(0,o.N7)(".js-dropdown-details",{subscribe:e=>(0,r.qC)((0,r.RB)(e,"toggle",u),(0,r.RB)(e,"toggle",c))}),(0,o.N7)("[data-deferred-details-content-url]:not([data-details-no-preload-on-hover])",{subscribe:e=>{let t=e.querySelector("summary");return(0,r.RB)(t,"mouseenter",a.G)}}),(0,o.N7)("[data-deferred-details-content-url]",{subscribe:e=>(0,r.RB)(e,"toggle",a.G)}),(0,s.on)("click","[data-toggle-for]",function(e){let t=e.currentTarget.getAttribute("data-toggle-for")||"",n=document.getElementById(t);n&&d(n)}),(0,i.Z)(function({target:e}){if(!e||e.closest("summary"))return;let t=e.parentElement;for(;t;)(t=t.closest("details"))&&(t.hasAttribute("open")||t.setAttribute("open",""),t=t.parentElement)}),(0,s.on)("details-dialog-close","[data-disable-dialog-dismiss]",function(e){e.preventDefault()})},74721:(e,t,n)=>{"use strict";n.d(t,{D:()=>i});var r=n(59753);function i(e){return Array.from(e.types).indexOf("Files")>=0}(0,n(36071).N7)(".js-document-dropzone",{constructor:HTMLElement,add(e){document.body.addEventListener("dragstart",u),document.body.addEventListener("dragend",d),document.body.addEventListener("dragenter",o),document.body.addEventListener("dragover",o),document.body.addEventListener("dragleave",s),e.addEventListener("drop",l)},remove(e){document.body.removeEventListener("dragstart",u),document.body.removeEventListener("dragend",d),document.body.removeEventListener("dragenter",o),document.body.removeEventListener("dragover",o),document.body.removeEventListener("dragleave",s),e.removeEventListener("drop",l)}});let a=null;function o(e){if(c)return;let t=e.currentTarget;a&&window.clearTimeout(a),a=window.setTimeout(()=>t.classList.remove("dragover"),200);let n=e.dataTransfer;n&&i(n)&&(n.dropEffect="copy",t.classList.add("dragover"),e.stopPropagation(),e.preventDefault())}function s(e){if(e.target instanceof Element&&e.target.classList.contains("js-document-dropzone")){let t=e.currentTarget;t.classList.remove("dragover")}}function l(e){let t=e.currentTarget;t.classList.remove("dragover"),document.body.classList.remove("dragover");let n=e.dataTransfer;n&&i(n)&&((0,r.f)(t,"document:drop",{transfer:n}),e.stopPropagation(),e.preventDefault())}let c=!1;function u(){c=!0}function d(){c=!1}},14992:(e,t,n)=>{"use strict";n.d(t,{HI:()=>a,Jx:()=>o,cv:()=>s});var r=n(69567),i=n(59753);async function a(e,t){let r=new TextEncoder,i=r.encode(t),{seal:a}=await Promise.all([n.e("vendors-node_modules_blakejs_index_js-node_modules_tweetnacl_nacl-fast_js"),n.e("_empty-file_js-app_assets_modules_github_tweetsodium_ts")]).then(n.bind(n,20179));return a(i,e)}function o(e){let t=atob(e).split("").map(e=>e.charCodeAt(0));return Uint8Array.from(t)}function s(e){let t="";for(let n of e)t+=String.fromCharCode(n);return btoa(t)}function l(e){return async function(t){let n=t.currentTarget;if(t.defaultPrevented||!n.checkValidity())return;let i=o(n.getAttribute("data-public-key"));for(let o of(t.preventDefault(),n.elements)){let t=o;if(t.id.endsWith("secret")){if(t.disabled=!0,t.required&&!t.value){let e=`${t.name} is invalid!`,n=document.querySelector("template.js-flash-template");n.after(new r.R(n,{className:"flash-error",message:e}));return}let o=`${t.name}_encrypted_value`;if(!t.value){n.elements.namedItem(o).disabled=e;continue}n.elements.namedItem(o).value=s(await a(i,t.value))}}n.submit()}}(0,i.on)("submit","form.js-encrypt-submit",async function(e){let t=e.currentTarget;if(e.defaultPrevented||!t.checkValidity())return;let n=t.elements.namedItem("secret_value");if(n.disabled=!0,!n.value)return;e.preventDefault();let r=o(t.getAttribute("data-public-key"));t.elements.namedItem("encrypted_value").value=s(await a(r,n.value)),t.submit()}),(0,i.on)("submit","form.js-encrypt-bulk-submit",l(!0)),(0,i.on)("submit","form.js-encrypt-bulk-submit-enable-empty",l(!1))},55240:(e,t,n)=>{"use strict";n.d(t,{k:()=>s});var r=n(56959),i=n(4412),a=n(36071),o=n(98105);async function s(e){await i.C,c(e)}function l(e,t){e.style.visibility=t?"hidden":"";let n=e.getAttribute("data-tab-item");if(n){let e=document.querySelector(`[data-menu-item=${n}]`);e instanceof HTMLElement&&(e.hidden=!t)}}function c(e){let t=e.querySelectorAll(".js-responsive-underlinenav-item"),n=e.querySelector(".js-responsive-underlinenav-overflow"),r=(0,o.oE)(n,e);if(!r)return;let i=!1;for(let n of t){let t=(0,o.oE)(n,e);if(t){let e=t.left+n.offsetWidth>=r.left;l(n,e),i=i||e}}n.style.visibility=i?"":"hidden"}(0,a.N7)(".js-responsive-underlinenav",{constructor:HTMLElement,subscribe:e=>(s(e),(0,r.RB)(window,"resize",()=>c(e)))})},79422:(e,t,n)=>{"use strict";n.d(t,{m:()=>c});var r=n(407),i=n(46263),a=n(98973),o=n(36071),s=n(44544);let l=(0,s.Z)("localStorage",{ttl:3e5,throwQuotaErrorsOnSet:!1,sendCacheStats:!0}),c=()=>{(0,r.e6)((0,a.e)()),(0,r.e6)((0,a.e)(),{storage:l})},u=()=>{(0,r.Xm)((0,a.e)(),{selector:".js-session-resumable"}),(0,r.Xm)((0,a.e)(),{selector:".js-local-storage-resumable",storage:l})},d=(0,i.D)(function(){c()},50);function f(){return document.querySelector("html")?.hasAttribute("data-turbo-preview")??!1}window.addEventListener("submit",r.iO,{capture:!0}),window.addEventListener("pageshow",function(){c()}),(0,o.N7)(".js-session-resumable",function(){f()||d()}),window.addEventListener("pagehide",function(){u()}),window.addEventListener("turbo:before-fetch-response",function(){u()}),window.addEventListener("turbo:load",function(){c()})},82565:()=>{function e(e){let t=e&&e.getAttribute("value");if(t)for(let e of document.querySelectorAll(".js-sidenav-container-pjax .js-selected-navigation-item")){let n=(e.getAttribute("data-selected-links")||"").split(" ").indexOf(t)>=0;n?e.setAttribute("aria-current","page"):e.removeAttribute("aria-current"),e.classList.toggle("selected",n)}}let t=new MutationObserver(t=>{for(let n of t)for(let t of n.addedNodes)t instanceof HTMLMetaElement&&"selected-link"===t.getAttribute("name")&&e(t)});t.observe(document.head,{childList:!0}),document.addEventListener("turbo:load",()=>{let t=document.head.querySelector('meta[name="selected-link"]');t&&e(t)})},36543:(e,t,n)=>{"use strict";var r=n(67044),i=n(59753),a=n(36071);let o=class TagInput{setup(){this.container.addEventListener("click",e=>{let t=e.target;t.closest(".js-remove")?this.removeTag(e):this.onFocus()}),this.container.addEventListener("keydown",e=>{"Enter"!==e.key||e.defaultPrevented||!this.input.value||(e.preventDefault(),this.selectTag(this.input.value),this.autoComplete.open=!1)}),this.input.addEventListener("focus",this.onFocus.bind(this)),this.input.addEventListener("blur",this.onBlur.bind(this)),this.input.addEventListener("keydown",this.onKeyDown.bind(this)),this.form.addEventListener("submit",this.onSubmit.bind(this)),this.autoComplete.addEventListener("auto-complete-change",()=>{this.selectTag(this.autoComplete.value)})}onFocus(){this.inputWrap.classList.add("focus"),this.input!==document.activeElement&&this.input.focus()}onBlur(){this.inputWrap.classList.remove("focus"),this.autoComplete.open||this.onSubmit()}onSubmit(){this.input.value&&(this.selectTag(this.input.value),this.autoComplete.open=!1)}onKeyDown(e){switch((0,r.EL)(e)){case"Backspace":this.onBackspace();break;case"Enter":case"Tab":this.taggifyValueWhenSuggesterHidden(e);break;case",":case" ":this.taggifyValue(e)}}taggifyValueWhenSuggesterHidden(e){!this.autoComplete.open&&this.input.value&&(e.preventDefault(),this.selectTag(this.input.value))}taggifyValue(e){this.input.value&&(e.preventDefault(),this.selectTag(this.input.value),this.autoComplete.open=!1)}selectTag(e){let t=this.normalizeTag(e),n=this.selectedTags(),r=!1;for(let e=0;e<t.length;e++){let i=t[e];0>n.indexOf(i)&&(this.selections.appendChild(this.templateTag(i)),r=!0)}r&&(this.input.value="",(0,i.f)(this.form,"tags:changed"))}removeTag(e){let t=e.target;e.preventDefault();let n=t.closest(".js-tag-input-tag");n.remove(),(0,i.f)(this.form,"tags:changed")}templateTag(e){let t=this.tagTemplate.cloneNode(!0);t.querySelector("input").value=e;let n=t.querySelector(".js-placeholder-tag-name");return n.replaceWith(e),t.classList.remove("d-none","js-template"),t}normalizeTag(e){let t=e.toLowerCase().trim();return t?this.multiTagInput?t.split(/[\s,']+/):[t.replace(/[\s,']+/g,"-")]:[]}onBackspace(){if(!this.input.value){let e=this.selections.querySelector("li:last-child .js-remove");e instanceof HTMLElement&&e.click()}}selectedTags(){let e=this.selections.querySelectorAll("input");return Array.from(e).map(e=>e.value).filter(e=>e.length>0)}constructor(e){this.container=e.container,this.selections=e.selections,this.inputWrap=e.inputWrap,this.input=e.input,this.tagTemplate=e.tagTemplate,this.form=this.input.form,this.autoComplete=e.autoComplete,this.multiTagInput=e.multiTagInput}};(0,a.N7)(".js-tag-input-container",{constructor:HTMLElement,initialize(e){new o({container:e,inputWrap:e.querySelector(".js-tag-input-wrapper"),input:e.querySelector('input[type="text"], input:not([type])'),selections:e.querySelector(".js-tag-input-selected-tags"),tagTemplate:e.querySelector(".js-template"),autoComplete:e.querySelector("auto-complete"),multiTagInput:!1}).setup()}}),(0,a.N7)(".js-multi-tag-input-container",{constructor:HTMLElement,initialize(e){new o({container:e,inputWrap:e.querySelector(".js-tag-input-wrapper"),input:e.querySelector('input[type="text"], input:not([type])'),selections:e.querySelector(".js-tag-input-selected-tags"),tagTemplate:e.querySelector(".js-template"),autoComplete:e.querySelector("auto-complete"),multiTagInput:!0}).setup()}})},19052:()=>{function e(e){if(!(e.target instanceof Element))return;let t=e.target.closest(".user-select-contain");if(!t)return;let n=window.getSelection();if(!n||!n.rangeCount||!n.rangeCount||"Range"!==n.type)return;let r=n.getRangeAt(0).commonAncestorContainer;t.contains(r)||n.selectAllChildren(t)}!function(){let e=document.createElement("div");return e.style.cssText="-ms-user-select: element; user-select: contain;","element"===e.style.getPropertyValue("-ms-user-select")||"contain"===e.style.getPropertyValue("-ms-user-select")||"contain"===e.style.getPropertyValue("user-select")}()&&document.addEventListener("click",e)},56334:(e,t,n)=>{"use strict";function r(e){let t=e.match(/#?(?:L)(\d+)((?:C)(\d+))?/g);if(t){if(1===t.length){let e=l(t[0]);if(!e)return;return Object.freeze({start:e,end:e})}if(2!==t.length)return;{let e=l(t[0]),n=l(t[1]);if(!e||!n)return;return f(Object.freeze({start:e,end:n}))}}}function i(e){let{start:t,end:n}=f(e);return null!=t.column&&null!=n.column?`L${t.line}C${t.column}-L${n.line}C${n.column}`:null!=t.column?`L${t.line}C${t.column}-L${n.line}`:null!=n.column?`L${t.line}-L${n.line}C${n.column}`:t.line===n.line?`L${t.line}`:`L${t.line}-L${n.line}`}function a(e){let t=e.length<5e3&&e.match(/(file-.+?-)L\d+?/i);return t?t[1]:""}function o(e){let t=r(e),n=a(e);return{blobRange:t,anchorPrefix:n}}function s({anchorPrefix:e,blobRange:t}){return t?`#${e}${i(t)}`:"#"}function l(e){let t=e.match(/L(\d+)/),n=e.match(/C(\d+)/);return t?Object.freeze({line:parseInt(t[1]),column:n?parseInt(n[1]):null}):null}function c(e,t){let[n,r]=u(e.start,!0,t),[i,a]=u(e.end,!1,t);if(!n||!i)return;let o=r,s=a;if(-1===o&&(o=0),-1===s&&(s=i.childNodes.length),!n.ownerDocument)throw Error("DOMRange needs to be inside document");let l=n.ownerDocument.createRange();return l.setStart(n,o),l.setEnd(i,s),l}function u(e,t,n){let r=[null,0],i=n(e.line);if(!i)return r;if(null==e.column)return[i,-1];let a=e.column-1,o=d(i);for(let e=0;e<o.length;e++){let n=o[e],r=a-(n.textContent||"").length;if(0===r){let r=o[e+1];if(t&&r)return[r,0];return[n,a]}if(r<0)return[n,a];a=r}return r}function d(e){if(e.nodeType===Node.TEXT_NODE)return[e];if(!e.childNodes||!e.childNodes.length)return[];let t=[];for(let n of e.childNodes)t=t.concat(d(n));return t}function f(e){let t=[e.start,e.end];return(t.sort(m),t[0]===e.start&&t[1]===e.end)?e:Object.freeze({start:t[0],end:t[1]})}function m(e,t){return e.line===t.line&&e.column===t.column?0:e.line===t.line&&"number"==typeof e.column&&"number"==typeof t.column?e.column-t.column:e.line-t.line}n.d(t,{Dw:()=>s,G5:()=>r,M9:()=>c,n6:()=>o})},54697:(e,t,n)=>{"use strict";n.d(t,{P:()=>a,g:()=>o});var r=n(59753);let i=new WeakMap;function a(e){return i.get(e)}async function o(e){return i.get(e)||s(await l(e,"codeEditor:ready"))}function s(e){if(!(e instanceof CustomEvent))throw Error("assert: event is not a CustomEvent");let t=e.detail.editor;if(!e.target)throw Error("assert: event.target is null");return i.set(e.target,t),t}function l(e,t){return new Promise(n=>{e.addEventListener(t,n,{once:!0})})}(0,r.on)("codeEditor:ready",".js-code-editor",s)},41982:(e,t,n)=>{"use strict";function*r(e,t){for(let n of e){let e=t(n);null!=e&&(yield e)}}function i(e,t,n){let i=e=>{let n=t(e);return null!=n?[e,n]:null};return[...r(e,i)].sort((e,t)=>n(e[1],t[1])).map(([e])=>e)}n.d(t,{W:()=>i})},87738:(e,t,n)=>{"use strict";function r(e,t,n=.1){let r=s(e,t,n);if(r&&-1===t.indexOf("/")){let i=e.substring(e.lastIndexOf("/")+1);r+=s(i,t,n)}return r}function i(e){let t=e.toLowerCase().split(""),n="";for(let e=0;e<t.length;e++){let r=t[e],i=r.replace(/[\\^$*+?.()|[\]{}]/g,"\\$&");0===e?n+=`(.*)(${i})`:n+=`([^${i}]*?)(${i})`}return RegExp(`${n}(.*?)$`,"i")}function a(e,t,n){if(t){let r=e.innerHTML.trim().match(n||i(t));if(!r)return;let a=!1,o=[];for(let e=1;e<r.length;++e){let t=r[e];t&&(e%2==0?a||(o.push("<mark>"),a=!0):a&&(o.push("</mark>"),a=!1),o.push(t))}e.innerHTML=o.join("")}else{let t=e.innerHTML.trim(),n=t.replace(/<\/?mark>/g,"");t!==n&&(e.innerHTML=n)}}n.d(t,{EW:()=>r,Qw:()=>a,qu:()=>l});let o=new Set([" ","-","_"]);function s(e,t,n=.1){let r=e;if(r===t)return 1;let i=r.length,a=0,s=0;for(let e=0;e<t.length;e++){let l=t[e],c=r.indexOf(l.toLowerCase()),u=r.indexOf(l.toUpperCase()),d=Math.min(c,u),f=d>-1?d:Math.max(c,u);if(-1===f)return 0;a+=.1,r[f]===l&&(a+=.1),0===f&&(a+=.9-n,0===e&&(s=1)),o.has(r.charAt(f-1))&&(a+=.9-n),r=r.substring(f+1,i)}let l=t.length,c=a/l,u=(c*(l/i)+c)/2;return s&&u+n<1&&(u+=n),u}function l(e,t){return e.score>t.score?-1:e.score<t.score?1:e.text<t.text?-1:e.text>t.text?1:0}},97895:(e,t,n)=>{"use strict";n.d(t,{Z:()=>d});var r=n(47142);let i=(e,t,n)=>{if(!(0,r.CD)(e,t))return-1/0;let i=(0,r.Gs)(e,t);return i<n?-1/0:i},a=(e,t,n)=>{e.textContent="";let i=0;for(let a of(0,r.m7)(t,n)){let t=n.slice(i,a);""!==t&&e.appendChild(document.createTextNode(n.slice(i,a))),i=a+1;let r=document.createElement("mark");r.textContent=n[a],e.appendChild(r)}e.appendChild(document.createTextNode(n.slice(i)))},o=new WeakMap,s=new WeakMap,l=new WeakMap,c=e=>{if(!l.has(e)&&e instanceof HTMLElement){let t=(e.getAttribute("data-value")||e.textContent||"").trim();return l.set(e,t),t}return l.get(e)||""},u=class FuzzyListElement extends HTMLElement{connectedCallback(){let e=this.querySelector("ul");if(!e)return;let t=new Set(e.querySelectorAll("li")),n=this.querySelector("input");n instanceof HTMLInputElement&&n.addEventListener("input",()=>{this.value=n.value});let i=new MutationObserver(e=>{let n=!1;for(let i of e)if("childList"===i.type&&i.addedNodes.length){for(let e of i.addedNodes)if(e instanceof HTMLLIElement&&!t.has(e)){let i=c(e);n=n||(0,r.CD)(this.value,i),t.add(e)}}n&&this.sort()});i.observe(e,{childList:!0});let a={handler:i,items:t,lazyItems:new Map,timer:null};s.set(this,a)}disconnectedCallback(){let e=s.get(this);e&&(e.handler.disconnect(),s.delete(this))}addLazyItems(e,t){let n=s.get(this);if(!n)return;let{lazyItems:i}=n,{value:a}=this,o=!1;for(let n of e)i.set(n,t),o=o||Boolean(a)&&(0,r.CD)(a,n);o&&this.sort()}sort(){let e=o.get(this);e&&(e.aborted=!0);let t={aborted:!1};o.set(this,t);let{minScore:n,markSelector:r,maxMatches:u,value:d}=this,f=s.get(this);if(!f||!this.dispatchEvent(new CustomEvent("fuzzy-list-will-sort",{cancelable:!0,detail:d})))return;let{items:m,lazyItems:h}=f,p=this.hasAttribute("mark-selector"),g=this.querySelector("ul");if(!g)return;let b=[];if(d){for(let e of m){let t=c(e),r=i(d,t,n);r!==-1/0&&b.push({item:e,score:r})}for(let[e,t]of h){let r=i(d,e,n);r!==-1/0&&b.push({text:e,render:t,score:r})}b.sort((e,t)=>t.score-e.score).splice(u)}else{let e=b.length;for(let t of m){if(e>=u)break;b.push({item:t,score:1}),e+=1}for(let[t,n]of h){if(e>=u)break;b.push({text:t,render:n,score:1}),e+=1}}requestAnimationFrame(()=>{if(t.aborted)return;let e=g.querySelector('input[type="radio"]:checked');g.textContent="";let n=0,i=()=>{if(t.aborted)return;let o=Math.min(b.length,n+100),s=document.createDocumentFragment();for(let e=n;e<o;e+=1){let t=b[e],n=null;if("render"in t&&"text"in t){let{render:e,text:r}=t;n=e(r),m.add(n),l.set(n,r),h.delete(r)}else"item"in t&&(n=t.item);n instanceof HTMLElement&&(p&&a(r&&n.querySelector(r)||n,p?d:"",c(n)),s.appendChild(n))}n=o;let u=!1;if(e instanceof HTMLInputElement)for(let t of s.querySelectorAll('input[type="radio"]:checked'))t instanceof HTMLInputElement&&t.value!==e.value&&(t.checked=!1,u=!0);for(let e of s.querySelectorAll('button[tabindex="-1"]'))e.setAttribute("tabindex","0");if(g.appendChild(s),e&&u&&e.dispatchEvent(new Event("change",{bubbles:!0})),o<b.length)requestAnimationFrame(i);else{g.hidden=0===b.length;let e=this.querySelector("[data-fuzzy-list-show-on-empty]");e&&(e.hidden=b.length>0),this.dispatchEvent(new CustomEvent("fuzzy-list-sorted",{detail:b.length}))}};i()})}get value(){return this.getAttribute("value")||""}set value(e){this.setAttribute("value",e)}get markSelector(){return this.getAttribute("mark-selector")||""}set markSelector(e){e?this.setAttribute("mark-selector",e):this.removeAttribute("mark-selector")}get minScore(){return Number(this.getAttribute("min-score")||0)}set minScore(e){Number.isNaN(e)||this.setAttribute("min-score",String(e))}get maxMatches(){return Number(this.getAttribute("max-matches")||1/0)}set maxMatches(e){Number.isNaN(e)||this.setAttribute("max-matches",String(e))}static get observedAttributes(){return["value","mark-selector","min-score","max-matches"]}attributeChangedCallback(e,t,n){if(t===n)return;let r=s.get(this);r&&(r.timer&&window.clearTimeout(r.timer),r.timer=window.setTimeout(()=>this.sort(),100))}},d=u;window.customElements.get("fuzzy-list")||(window.FuzzyListElement=u,window.customElements.define("fuzzy-list",u))},29764:(e,t,n)=>{"use strict";n.d(t,{$S:()=>i,Fk:()=>a,sz:()=>o});var r=n(71643);function i(e,t,n){let i={hydroEventPayload:e,hydroEventHmac:t,visitorPayload:"",visitorHmac:"",hydroClientContext:n},a=document.querySelector("meta[name=visitor-payload]");a instanceof HTMLMetaElement&&(i.visitorPayload=a.content);let o=document.querySelector("meta[name=visitor-hmac]")||"";o instanceof HTMLMetaElement&&(i.visitorHmac=o.content),(0,r.b)(i,!0)}function a(e){let t=e.getAttribute("data-hydro-view")||"",n=e.getAttribute("data-hydro-view-hmac")||"",r=e.getAttribute("data-hydro-client-context")||"";i(t,n,r)}function o(e){let t=e.getAttribute("data-hydro-click-payload")||"",n=e.getAttribute("data-hydro-click-hmac")||"",r=e.getAttribute("data-hydro-client-context")||"";i(t,n,r)}},3626:(e,t,n)=>{"use strict";n.d(t,{vt:()=>y,WF:()=>b,DV:()=>g,jW:()=>E,Nc:()=>f,$t:()=>a});let r={frequency:.6,recency:.4};function i(e,t){return e.sort((e,n)=>t(e)-t(n))}function a(e){let t=s(e),n=l(e);return function(e){return o(t.get(e)||0,n.get(e)||0)}}function o(e,t){return e*r.frequency+t*r.recency}function s(e){let t=[...Object.values(e)].reduce((e,t)=>e+t.visitCount,0);return new Map(Object.keys(e).map(n=>[n,e[n].visitCount/t]))}function l(e){let t=i([...Object.keys(e)],t=>e[t].lastVisitedAt),n=t.length;return new Map(t.map((e,t)=>[e,(t+1)/n]))}let c=/^\/orgs\/([a-z0-9-]+)\/teams\/([\w-]+)/,u=[/^\/([^/]+)\/([^/]+)\/?$/,/^\/([^/]+)\/([^/]+)\/blob/,/^\/([^/]+)\/([^/]+)\/tree/,/^\/([^/]+)\/([^/]+)\/issues/,/^\/([^/]+)\/([^/]+)\/pulls?/,/^\/([^/]+)\/([^/]+)\/pulse/],d=[["organization",/^\/orgs\/([a-z0-9-]+)\/projects\/([0-9-]+)/],["repository",/^\/([^/]+)\/([^/]+)\/projects\/([0-9-]+)/]];function f(e){let t,n;let r=e.match(c);if(r){h(g(r[1],r[2]));return}for(let n=0,r=d.length;n<r;n++){let[r,i]=d[n];if(t=e.match(i)){let e=null,n=null;switch(r){case"organization":e=t[1],n=t[2];break;case"repository":e=`${t[1]}/${t[2]}`,n=t[3]}e&&n&&h(y(e,n));return}}for(let t=0,r=u.length;t<r;t++)if(n=e.match(u[t])){h(b(n[1],n[2]));return}}function m(e){let t=Object.keys(e);if(t.length<=100)return e;let n=a(e),r=t.sort((e,t)=>n(t)-n(e)).slice(0,50);return Object.fromEntries(r.map(t=>[t,e[t]]))}function h(e){let t=E(),n=p(),r=t[e]||{lastVisitedAt:n,visitCount:0};r.visitCount+=1,r.lastVisitedAt=n,t[e]=r,S(m(t))}function p(){return Math.floor(Date.now()/1e3)}function g(e,t){return`team:${e}/${t}`}function b(e,t){return`repository:${e}/${t}`}function y(e,t){return`project:${e}/${t}`}let v=/^(team|repository|project):[^/]+\/[^/]+(\/([^/]+))?$/,w="jump_to:page_views";function S(e){L(w,JSON.stringify(e))}function E(){let e;let t=j(w);if(!t)return{};try{e=JSON.parse(t)}catch{return S({}),{}}let n={};for(let t in e)t.match(v)&&(n[t]=e[t]);return n}function L(e,t){try{window.localStorage.setItem(e,t)}catch{}}function j(e){try{return window.localStorage.getItem(e)}catch{return null}}},34090:(e,t,n)=>{"use strict";n.d(t,{I:()=>s,n:()=>o});var r=n(39629);let i=["notification_referrer_id","notifications_before","notifications_after","notifications_query"],a="notification_shelf";function o(e,t=null){return e.has("notification_referrer_id")?(l(e,t),c(e)):null}function s(e=null){let t=u(e);if(!t)return(0,r.cl)(a),null;try{let e=(0,r.rV)(a);if(!e)return null;let n=JSON.parse(e);if(!n||!n.pathname)throw Error("Must have a pathname");if(n.pathname!==t)throw Error("Stored pathname does not match current pathname.");let o={};for(let e of i)o[e]=n[e];return o}catch(e){return(0,r.cl)(a),null}}function l(e,t){let n=u(t);if(!n)return;let o={pathname:n};for(let t of i){let n=e.get(t);n&&(o[t]=n)}(0,r.LS)(a,JSON.stringify(o))}function c(e){for(let t of i)e.delete(t);return e}function u(e){e=e||window.location.pathname;let t=e.match(/^(\/[^/]+\/[^/]+\/pull\/[^/]+)/);return t?t[0]:null}},95005:(e,t,n)=>{"use strict";function r(e,t){let n=e.closest("[data-notification-id]");t.hasAttribute("data-status")&&i(n,t.getAttribute("data-status")),t.hasAttribute("data-subscription-status")&&a(n,t.getAttribute("data-subscription-status")),t.hasAttribute("data-starred-status")&&o(n,t.getAttribute("data-starred-status"))}function i(e,t){e.classList.toggle("notification-archived","archived"===t),e.classList.toggle("notification-unread","unread"===t),e.classList.toggle("notification-read","read"===t)}function a(e,t){e.classList.toggle("notification-unsubscribed","unsubscribed"===t)}function o(e,t){e.classList.toggle("notification-starred","starred"===t)}n.d(t,{a:()=>r})},11445:(e,t,n)=>{"use strict";function r(e,t){t.appendChild(e.extractContents()),e.insertNode(t)}n.d(t,{v:()=>r})},98973:(e,t,n)=>{"use strict";function r(e){let t=e||window.location,n=document.head&&document.head.querySelector("meta[name=session-resume-id]"),r=n instanceof HTMLMetaElement&&n.content;return r||t.pathname}n.d(t,{e:()=>r})},96056:(e,t,n)=>{"use strict";n.d(t,{Hu:()=>p,_8:()=>c,cj:()=>l});var r=n(69567),i=n(36071);let a="github-mobile-auth-flash";function o(e){let t=new r.R(document.querySelector("template.js-flash-template"),{className:`flash-error ${a}`,message:e}),n=document.importNode(t,!0),i=document.querySelector("#js-flash-container");i&&(s(),i.appendChild(n))}function s(){let e=document.querySelector("#js-flash-container");if(e)for(let t of e.children)!t.classList.contains("js-flash-template")&&t.classList.contains(a)&&e.removeChild(t)}function l(){let e=document.getElementById("github-mobile-authenticate-prompt");e&&(e.hidden=!0);let t=document.getElementById("github-mobile-authenticate-error-and-retry");t&&(t.hidden=!1)}function c(){s();let e=document.getElementById("github-mobile-authenticate-prompt");e&&(e.hidden=!1);let t=document.getElementById("github-mobile-authenticate-error-and-retry");t&&(t.hidden=!0)}function u(e){let t;(t=e?new URL(`password_reset/${encodeURIComponent(e)}`,window.location.origin):new URL("",window.location.href)).searchParams.set("redirect","true"),window.location.assign(t)}function d(){document.getElementById("github-mobile-rejected-redirect").click()}function f(e){e&&o(e),l()}function m(e){return document.getElementById("github-mobile-authenticate-error-and-retry").getAttribute(e)}function h(e,t,n,r){return async function i(a){let o,s;if(r&&r())return;let l="STATUS_UNKNOWN";try{let t=document.getElementById("github-mobile-authenticate-form"),n=t.querySelector(".js-data-url-csrf"),r=await self.fetch(new Request(e,{method:"POST",body:new FormData(t),mode:"same-origin",headers:{Accept:"application/json","Scoped-CSRF-Token":n.value,"X-Requested-With":"XMLHttpRequest"}}));if(r.ok){let e=await r.json();l=e.status,o=e.token}else l="STATUS_ERROR"}catch{l="STATUS_ERROR"}switch(l){case"STATUS_APPROVED":return t?t():u(o);case"STATUS_EXPIRED":return s=m("timeout-flash"),n?n(s):f(s);case"STATUS_ACTIVE":case"STATUS_ERROR":case"STATUS_UNKNOWN":break;case"STATUS_REJECTED":return s=m("error-flash"),n?n(s):d();default:return s=m("error-flash"),n?n(s):f(s)}await new Promise(e=>setTimeout(e,3e3)),i(a)}(0)}async function p(e,t,n,r){try{await h(e.getAttribute("data-poll-url"),t,n,r)}catch(t){let e=m("error-flash");return f(e)}}(0,i.N7)(".js-poll-github-mobile-two-factor-authenticate",function(e){p(e)}),(0,i.N7)(".js-poll-github-mobile-verified-device-authenticate",function(e){p(e)}),(0,i.N7)(".js-poll-github-mobile-two-factor-password-reset-authenticate",function(e){p(e)})},98576:(e,t,n)=>{"use strict";n.d(t,{C:()=>s,v:()=>l});var r=n(59753),i=n(254),a=n(65935),o=n(58700);function s(){document.body.classList.add("is-sending"),document.body.classList.remove("is-sent","is-not-sent")}function l(){document.body.classList.add("is-sent"),document.body.classList.remove("is-sending")}function c(e){e&&(document.querySelector(".js-sms-error").textContent=e),document.body.classList.add("is-not-sent"),document.body.classList.remove("is-sending")}(0,a.AC)(".js-send-auth-code",async(e,t)=>{let n;s();try{n=await t.text()}catch(e){c(e.response.text)}n&&l()}),(0,a.AC)(".js-two-factor-set-sms-fallback",async(e,t)=>{let n;try{n=await t.text()}catch(a){let t=e.querySelector(".js-configure-sms-fallback"),n=e.querySelector(".js-verify-sms-fallback"),r=t.hidden?n:t,i=r.querySelector(".flash");switch(a.response.status){case 404:case 422:case 429:i.textContent=JSON.parse(a.response.text).error,i.hidden=!1}}if(n)switch(n.status){case 200:case 201:window.location.reload();break;case 202:e.querySelector(".js-configure-sms-fallback").hidden=!0,e.querySelector(".js-verify-sms-fallback").hidden=!1,e.querySelector(".js-fallback-otp").focus()}}),(0,i.q6)(".js-verification-code-input-auto-submit",function(e){let t=e.currentTarget,n=t.pattern||"[0-9]{6}";RegExp(`^(${n})$`).test(t.value)&&(0,o.Bt)(t.form)}),(0,r.on)("click",".js-toggle-redacted-note-content",async e=>{let t=e.currentTarget,n=t.closest(".note");if(n){let e=n.getElementsByClassName("js-note")[0];if(e){let n=t.getAttribute("data-content").replace(/</g,"&lt;").replace(/>/g,"&gt;");e.innerHTML=n}}let r=n.getElementsByClassName("js-toggle-redacted-note-content");for(let e of r){let t=e;t.hidden=!t.hidden}})},94056:(e,t,n)=>{"use strict";n.d(t,{Z:()=>m});var r=n(19146),i=n(34892),a=n(65935),o=n(63047);let s=!1;function l(e){let t=new URL(e,window.location.origin),n=new URLSearchParams(t.search.slice(1));return n.set("webauthn-support",(0,o.T)()),t.search=n.toString(),t.toString()}async function c(){let e=document.querySelector("link[rel=sudo-modal]"),t=document.querySelector(".js-sudo-prompt");if(t instanceof HTMLTemplateElement)return t;if(e){let t=await (0,i.a_)(document,l(e.href));return document.body.appendChild(t),document.querySelector(".js-sudo-prompt")}throw Error("couldn't load sudo prompt")}let u=!1;async function d(e){if(s)return!1;s=!0,u=!1;let t=await c(),n=t.content.cloneNode(!0),i=await (0,r.W)({content:n}),a=e?.closest("details[open]");return a&&a.removeAttribute("open"),await new Promise(e=>{i.addEventListener("dialog:remove",function(){a&&a.setAttribute("open","open"),s=!1,e()},{once:!0})}),u}async function f(e,t,n="Sudo authentication failed.",r="Too many authentication attemtps. Please try again later.",i=".js-sudo-error",a){try{await t.text()}catch(o){let t;if(!o.response)throw o;switch(o.response.status){case 401:t=n;break;case 429:t=r;break;default:t="An unknown error occurred. Please try again later."}if(e.querySelector(i).textContent=t,e.querySelector(i).hidden=!1,a&&(e.querySelector(a).value=""),401!==o.response.status&&429!==o.response.status)throw o;return}u=!0,e.closest("details").removeAttribute("open")}async function m(e){let t=await fetch("/sessions/in_sudo",{headers:{accept:"application/json","X-Requested-With":"XMLHttpRequest"}});if(t.ok){let e=await t.text();if("true"===e)return!0}return d(e)}(0,a.AC)(".js-sudo-webauthn-form",async function(e,t){await f(e,t)}),(0,a.AC)(".js-sudo-github-mobile-form",async function(e,t){await f(e,t)}),(0,a.AC)(".js-sudo-totp-form",async function(e,t){await f(e,t,void 0,void 0,".flash-error","#totp")}),(0,a.AC)(".js-sudo-password-form",async function(e,t){await f(e,t,"Incorrect password.","Too many password attempts. Please wait and try again.",void 0,".js-sudo-password")})},68423:(e,t,n)=>{"use strict";n.d(t,{dY:()=>u,iU:()=>c,oq:()=>l});let r=new WeakMap;function i(e){let t=r.get(e);t&&(null!=t.timer&&clearTimeout(t.timer),t.timer=window.setTimeout(()=>{null!=t.timer&&(t.timer=null),t.inputed=!1,t.listener.call(null,e)},t.wait))}function a(e){let t=e.currentTarget,n=r.get(t);n&&(n.keypressed=!0,null!=n.timer&&clearTimeout(n.timer))}function o(e){let t=e.currentTarget,n=r.get(t);n&&(n.keypressed=!1,n.inputed&&i(t))}function s(e){let t=e.currentTarget,n=r.get(t);n&&(n.inputed=!0,n.keypressed||i(t))}function l(e,t,n={wait:null}){r.set(e,{keypressed:!1,inputed:!1,timer:void 0,listener:t,wait:null!=n.wait?n.wait:100}),e.addEventListener("keydown",a),e.addEventListener("keyup",o),e.addEventListener("input",s)}function c(e,t){e.removeEventListener("keydown",a),e.removeEventListener("keyup",o),e.removeEventListener("input",s);let n=r.get(e);n&&(null!=n.timer&&n.listener===t&&clearTimeout(n.timer),r.delete(e))}function u(e){let t=r.get(e);t&&t.listener.call(null,e)}},97629:(e,t,n)=>{"use strict";function r(e){return e.offsetWidth<=0&&e.offsetHeight<=0}function i(e){return!r(e)}n.d(t,{Z:()=>i})},63047:(e,t,n)=>{"use strict";n.d(t,{T:()=>i,k:()=>a});var r=n(8433);function i(){return(0,r.Zh)()?"supported":"unsupported"}async function a(){return await window.PublicKeyCredential?.isUserVerifyingPlatformAuthenticatorAvailable()?"supported":"unsupported"}},69202:(e,t,n)=>{"use strict";let r;n.d(t,{G:()=>A});var i=n(21461);let AliveSession=class AliveSession extends i.a2{getUrlFromRefreshUrl(){return a(this.refreshUrl)}constructor(e,t,n,r){super(e,()=>this.getUrlFromRefreshUrl(),n,r),this.refreshUrl=t}};async function a(e){let t=await o(e);return t&&t.url&&t.token?s(t.url,t.token):null}async function o(e){let t=await fetch(e,{headers:{Accept:"application/json"}});if(t.ok)return t.json();if(404===t.status)return null;throw Error("fetch error")}async function s(e,t){let n=await fetch(e,{method:"POST",mode:"same-origin",headers:{"Scoped-CSRF-Token":t}});if(n.ok)return n.text();throw Error("fetch error")}var l=n(46263),c=n(4412),u=n(44544),d=n(22490),f=n(71643),m=n(7180);let h="alive";let InvalidSourceRelError=class InvalidSourceRelError extends m.d{};let p=d.ZO.createPolicy(h,{createScriptURL:e=>m.O.apply({policy:()=>{if(!(0,f.B)())return e;if(!e.startsWith("/"))throw new InvalidSourceRelError("Alive worker src URL must start with a slash");return e},policyName:h,fallback:e,fallbackOnError:!0})});function g(){return"SharedWorker"in window&&"true"!==(0,u.Z)("localStorage").getItem("bypassSharedWorker")}function b(){let e=document.head.querySelector("link[rel=shared-web-socket-src]")?.getAttribute("href")??"";try{return p.createScriptURL(e)}catch(e){if(e instanceof InvalidSourceRelError)return null;throw e}}function y(){return document.head.querySelector("link[rel=shared-web-socket]")?.href??null}function v(){return document.head.querySelector("link[rel=shared-web-socket]")?.getAttribute("data-refresh-url")??null}function w(){return document.head.querySelector("link[rel=shared-web-socket]")?.getAttribute("data-session-id")??null}function S(e,{channel:t,type:n,data:r}){for(let i of e)i.dispatchEvent(new CustomEvent(`socket:${n}`,{bubbles:!1,cancelable:!1,detail:{name:t,data:r}}))}let E=class AliveSessionProxy{subscribe(e){let t=this.subscriptions.add(...e);t.length&&this.worker.port.postMessage({subscribe:t});let n=new Set(t.map(e=>e.name)),r=e.reduce((e,t)=>{let r=t.topic.name;return(0,i.A)(r)&&!n.has(r)&&e.add(r),e},new Set);r.size&&this.worker.port.postMessage({requestPresence:Array.from(r)})}unsubscribeAll(...e){let t=this.subscriptions.drain(...e);t.length&&this.worker.port.postMessage({unsubscribe:t});let n=this.presenceMetadata.removeSubscribers(e);this.sendPresenceMetadataUpdate(n)}updatePresenceMetadata(e){let t=new Set;for(let n of e)this.presenceMetadata.setMetadata(n),t.add(n.channelName);this.sendPresenceMetadataUpdate(t)}sendPresenceMetadataUpdate(e){if(!e.size)return;let t=[];for(let n of e)t.push({channelName:n,metadata:this.presenceMetadata.getChannelMetadata(n)});this.worker.port.postMessage({updatePresenceMetadata:t})}online(){this.worker.port.postMessage({online:!0})}offline(){this.worker.port.postMessage({online:!1})}hangup(){this.worker.port.postMessage({hangup:!0})}receive(e){let{channel:t}=e;if("presence"===e.type){let n=this.notifyPresenceDebouncedByChannel.get(t);n||(n=(0,l.D)((e,n)=>{this.notify(e,n),this.notifyPresenceDebouncedByChannel.delete(t)},100),this.notifyPresenceDebouncedByChannel.set(t,n)),n(this.subscriptions.subscribers(t),e);return}this.notify(this.subscriptions.subscribers(t),e)}constructor(e,t,n,r,a){this.subscriptions=new i.SubscriptionSet,this.presenceMetadata=new i.ah,this.notifyPresenceDebouncedByChannel=new Map,this.notify=a,this.worker=new SharedWorker(e,`github-socket-worker-v2-${r}`),this.worker.port.onmessage=({data:e})=>this.receive(e),this.worker.port.postMessage({connect:{url:t,refreshUrl:n}})}};async function L(){let e=b();if(!e)return;let t=y();if(!t)return;let n=v();if(!n)return;let r=w();if(!r)return;let i=(()=>{if(g())try{return new E(e,t,n,r,S)}catch(e){}return new AliveSession(t,n,!1,S)})();return window.addEventListener("online",()=>i.online()),window.addEventListener("offline",()=>i.offline()),window.addEventListener("pagehide",()=>{"hangup"in i&&i.hangup()}),i}async function j(){return await c.x,L()}function A(){return r||(r=j())}},99550:(e,t,n)=>{"use strict";n.d(t,{l:()=>a,p:()=>i});let r=!0;function i(e){r=e}function a(){return r}},49421:(e,t,n)=>{"use strict";function r(e,t,n){if(!t)return;let r=t.className.includes("cm-content")?t:t.querySelector(".cm-content");r&&r.dispatchEvent(new CustomEvent(e,{detail:n}))}n.d(t,{a:()=>r})},16730:(e,t,n)=>{"use strict";n.d(t,{T:()=>a});var r=n(15345),i=n(86283);function a(e){if(!i.n4)return;let t=i.n4.querySelector("title"),n=i.n4.createElement("title");n.textContent=e,t?t.textContent!==e&&(t.replaceWith(n),(0,r.x)(e)):(i.n4.head.appendChild(n),(0,r.x)(e))}},14873:(e,t,n)=>{"use strict";function r(){return Promise.resolve()}function i(){return new Promise(window.requestAnimationFrame)}async function a(e,t){let n;let r=new Promise(t=>{n=self.setTimeout(t,e)});if(!t)return r;try{await Promise.race([r,o(t)])}catch(e){throw self.clearTimeout(n),e}}function o(e){return new Promise((t,n)=>{let r=Error("aborted");r.name="AbortError",e.aborted?n(r):e.addEventListener("abort",()=>n(r))})}function s(e){let t=[];return function(n){t.push(n),1===t.length&&queueMicrotask(()=>{let n=t.slice(0);t.length=0,e(n)})}}n.d(t,{Dc:()=>a,g:()=>s,gJ:()=>r,rs:()=>i})},78806:(e,t,n)=>{"use strict";n.d(t,{Z:()=>i});let r=(e,t)=>{let n=new URL(e,window.location.origin),r=new URL(t,window.location.origin),i=r.href.includes("#");return i&&n.host===r.host&&n.pathname===r.pathname&&n.search===r.search},i=r},45974:(e,t,n)=>{"use strict";n.d(t,{dy:()=>s.dy,sY:()=>s.sY,Au:()=>s.Au});var r=n(22490),i=n(7180);let a="jtml-no-op",o=r.ZO.createPolicy(a,{createHTML:e=>i.O.apply({policy:()=>e,policyName:a,fallback:e,fallbackOnError:!0})});var s=n(20845);s.js.setCSPTrustedTypesPolicy(o)},57619:(e,t,n)=>{"use strict";function r(e){let t=e.split("\u200D"),n=0;for(let e of t){let t=Array.from(e.split(/[\ufe00-\ufe0f]/).join("")).length;n+=t}return n/t.length}function i(e,t,n,r=!0){let i=e.value.substring(0,e.selectionEnd||0),a=e.value.substring(e.selectionEnd||0);return s(e,(i=i.replace(t,n))+(a=a.replace(t,n)),i.length,r),n}function a(e,t,n){if(null===e.selectionStart||null===e.selectionEnd)return i(e,t,n);let r=e.value.substring(0,e.selectionStart),a=e.value.substring(e.selectionEnd);return s(e,r+n+a,r.length),n}function o(e,t,n={}){let r=e.selectionEnd||0,i=e.value.substring(0,r),a=e.value.substring(r),o=""===e.value||i.match(/\n$/)?"":"\n",s=n.appendNewline?"\n":"",l=o+t+s;e.value=i+l+a;let c=r+l.length;return e.selectionStart=c,e.selectionEnd=c,e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1})),e.focus(),l}function s(e,t,n,r=!0){e.value=t,r&&(e.selectionStart=n,e.selectionEnd=n),e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1}))}function l(e,t){let n=[...e],r=new TextEncoder,i=new Uint8Array(4);for(let e=0;e<n.length;e++){let a=n[e],{written:o,read:s}=r.encodeInto(a,i);if(!o||!s)return -1;let l=o-s;if(0!==l&&(e<t&&(t-=l),e>=t))break}return t}n.d(t,{Om:()=>o,lp:()=>i,rq:()=>r,t4:()=>a,yb:()=>l})},78923:(e,t,n)=>{"use strict";n.d(t,{w:()=>o});var r=n(22490),i=n(7180);let a="turbo",o=r.ZO.createPolicy(a,{createHTML:e=>i.O.apply({policy:()=>e,policyName:`${a}-html`,fallback:e,fallbackOnError:!0,silenceErrorReporting:!0}),createScript:e=>i.O.apply({policy:()=>e,policyName:`${a}-script`,fallback:e,fallbackOnError:!0,silenceErrorReporting:!0}),createScriptURL:e=>i.O.apply({policy:()=>e,policyName:`${a}-url`,fallback:e,fallbackOnError:!0,silenceErrorReporting:!0})})},23243:(e,t,n)=>{"use strict";n.d(t,{AU:()=>l,Ap:()=>E,DT:()=>m,HN:()=>s,Lq:()=>o,T2:()=>w,Yg:()=>v,ag:()=>y,ck:()=>c,lL:()=>p,po:()=>b,q3:()=>u,uL:()=>S,wz:()=>f,xk:()=>h,zH:()=>a});var r=n(78923);let i="data-turbo-loaded";function a(){document.documentElement.setAttribute(i,"")}function o(){return document.documentElement.hasAttribute(i)}let s=e=>e?.tagName==="TURBO-FRAME";function l(e,t){let n=e.split("/",3).join("/"),r=t.split("/",3).join("/");return n===r}function c(e,t){let n=e.split("/",2).join("/"),r=t.split("/",2).join("/");return n===r}async function u(){let e=document.head.querySelectorAll("link[rel=stylesheet]"),t=new Set([...document.styleSheets].map(e=>e.href)),n=[];for(let r of e)""===r.href||t.has(r.href)||n.push(d(r));await Promise.all(n)}let d=(e,t=2e3)=>new Promise(n=>{let r=()=>{e.removeEventListener("error",r),e.removeEventListener("load",r),n()};e.addEventListener("load",r,{once:!0}),e.addEventListener("error",r,{once:!0}),setTimeout(r,t)}),f=(e,t)=>{let n=t||e.querySelectorAll("[data-turbo-replace]"),r=[...document.querySelectorAll("[data-turbo-replace]")];for(let e of n){let t=r.find(t=>t.id===e.id);t&&t.replaceWith(e.cloneNode(!0))}},m=e=>{for(let t of e.querySelectorAll("link[rel=stylesheet]"))document.head.querySelector(`link[href="${t.getAttribute("href")}"],
           link[data-href="${t.getAttribute("data-href")}"]`)||document.head.append(t)},h=e=>{for(let t of e.querySelectorAll("script"))document.head.querySelector(`script[src="${t.getAttribute("src")}"]`)||g(t)},p=e=>{let{src:t}=e;if(!t)return;let n=document.createElement("script"),i=e.getAttribute("type");i&&(n.type=i);let a=r.w.createScriptURL(t);return n.src=a,n},g=e=>{let t=p(e);document.head&&t&&document.head.appendChild(t)},b=e=>{let t=[];for(let n of e.querySelectorAll('meta[data-turbo-track="reload"]'))document.querySelector(`meta[http-equiv="${n.getAttribute("http-equiv")}"]`)?.content!==n.content&&t.push(w(n.getAttribute("http-equiv")||""));return t},y=e=>{let t=e.querySelector("[data-turbo-head]")||e.head;return{title:t.querySelector("title")?.textContent,transients:[...t.querySelectorAll("[data-turbo-transient]")].map(e=>e.cloneNode(!0)),bodyClasses:e.querySelector("meta[name=turbo-body-classes]")?.content,replacedElements:[...e.querySelectorAll("[data-turbo-replace]")].map(e=>e.cloneNode(!0))}},v=()=>[...document.documentElement.attributes],w=e=>e.replace(/^x-/,"").replaceAll("-","_"),S=e=>document.dispatchEvent(new CustomEvent("turbo:reload",{detail:{reason:e}})),E=(e,t)=>{for(let n of e.attributes)t.hasAttribute(n.nodeName)||"aria-busy"===n.nodeName||e.removeAttribute(n.nodeName);for(let n of t.attributes)e.getAttribute(n.nodeName)!==n.nodeValue&&e.setAttribute(n.nodeName,n.nodeValue)}},59840:(e,t,n)=>{"use strict";n.r(t),n.d(t,{State:()=>i,WebauthnGetElement:()=>c});var r,i,a=n(76006),o=n(8433),s=n(58700),l=function(e,t,n,r){var i,a=arguments.length,o=a<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,n,r);else for(var s=e.length-1;s>=0;s--)(i=e[s])&&(o=(a<3?i(o):a>3?i(t,n,o):i(t,n))||o);return a>3&&o&&Object.defineProperty(t,n,o),o};!function(e){e.Initializing="initializing",e.Unsupported="unsupported",e.Ready="ready",e.Waiting="waiting",e.Error="error",e.Submitting="submitting"}(i||(i={}));let c=((r=class WebauthnGetElement extends HTMLElement{async connectedCallback(){this.originalButtonText=this.getCurrentButtonText(),this.originalErrorText=this.errorText.textContent,this.setState((0,o.Zh)()?i.Ready:i.Unsupported),this.passkeySupport=await window.PublicKeyCredential?.isUserVerifyingPlatformAuthenticatorAvailable(),this.state!==i.Unsupported&&!this.passkeySupport&&this.passkeysUnsupportedMessage&&(this.passkeysUnsupportedMessage.hidden=!1),this.subtleLogin?this.handleWebauthnSubtle():this.showWebauthnLoginFragment()}handleWebauthnSubtle(){let e=document.querySelector(".js-webauthn-subtle");e&&(e.hidden=!1,e.addEventListener("webauthn-subtle-submit",()=>{this.showWebauthnLoginFragment(),this.state!==i.Unsupported&&this.prompt()}))}showWebauthnLoginFragment(){let e=document.querySelector(".js-webauthn-login-section");e&&(e.hidden=!1)}getCurrentButtonText(){return this.buttonText.textContent||""}setCurrentButtonText(e){this.buttonText.textContent=e}setState(e){let t=this.button.getAttribute("data-retry-message")||this.originalButtonText,n=this.hasErrored?t:this.originalButtonText;for(let e of(this.setCurrentButtonText(n),this.button.disabled=!1,this.button.hidden=!1,this.errorText.textContent="",this.messages))e.hidden=!0;switch(e){case i.Initializing:this.button.disabled=!0;break;case i.Unsupported:this.button.disabled=!0,this.unsupportedMessage.hidden=!1,this.passkeysUnsupportedMessage&&(this.passkeysUnsupportedMessage.hidden=!0);break;case i.Ready:break;case i.Waiting:this.waitingMessage.hidden=!1,this.button.hidden=!0;break;case i.Error:this.errorMessage.hidden=!1,this.errorText.textContent=this.originalErrorText;break;case i.Submitting:this.setCurrentButtonText("Verifying\u2026"),this.button.disabled=!0;break;default:throw Error("invalid state")}this.state=e}async prompt(e,t){e?.preventDefault(),this.dispatchEvent(new CustomEvent("webauthn-get-prompt"));try{t||this.setState(i.Waiting);let e=JSON.parse(this.dataJson),n=await (0,o.U2)((0,o.wz)(e));this.setState(i.Submitting);let r=this.closest(".js-webauthn-form"),a=r.querySelector(".js-webauthn-response");a.value=JSON.stringify(n),(0,s.Bt)(r)}catch(e){if(!t)throw this.hasErrored=!0,this.setState(i.Error),e}}constructor(...e){super(...e),this.state=i.Initializing,this.hasErrored=!1,this.dataJson="",this.subtleLogin=!1}}).attrPrefix="",r);l([a.fA],c.prototype,"button",void 0),l([a.fA],c.prototype,"buttonText",void 0),l([a.GO],c.prototype,"messages",void 0),l([a.fA],c.prototype,"capitalizedDescription",void 0),l([a.fA],c.prototype,"unsupportedMessage",void 0),l([a.fA],c.prototype,"passkeysUnsupportedMessage",void 0),l([a.fA],c.prototype,"waitingMessage",void 0),l([a.fA],c.prototype,"errorMessage",void 0),l([a.fA],c.prototype,"errorText",void 0),l([a.Lj],c.prototype,"dataJson",void 0),l([a.Lj],c.prototype,"subtleLogin",void 0),c=l([a.Ih],c)}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_dompurify_dist_purify_js","vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183","vendors-node_modules_github_selector-observer_dist_index_esm_js","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc0","vendors-node_modules_lit-html_lit-html_js","vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_alive-client_dist-bf5aa2","vendors-node_modules_morphdom_dist_morphdom-esm_js","vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js","vendors-node_modules_delegated-events_dist_index_js-node_modules_github_details-dialog-elemen-29dc30","vendors-node_modules_color-convert_index_js","vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-59c459","vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_github_hotkey_dist_-8755d2","vendors-node_modules_github_session-resume_dist_index_js-node_modules_primer_behaviors_dist_e-ac74c6","vendors-node_modules_github_paste-markdown_dist_index_esm_js-node_modules_github_quote-select-854ff4","ui_packages_soft-nav_soft-nav_ts","app_assets_modules_github_details-dialog_ts-app_assets_modules_github_fetch_ts","app_assets_modules_github_updatable-content_ts-ui_packages_hydro-analytics_hydro-analytics_ts","app_assets_modules_github_onfocus_ts-app_assets_modules_github_sticky-scroll-into-view_ts","app_assets_modules_github_behaviors_task-list_ts-app_assets_modules_github_sso_ts-ui_packages-7d50ad","app_assets_modules_github_behaviors_ajax-error_ts-app_assets_modules_github_behaviors_include-2e2258","app_assets_modules_github_behaviors_commenting_edit_ts-app_assets_modules_github_behaviors_ht-83c235"],()=>t(70354));var n=e.O()}]);
//# sourceMappingURL=behaviors-18bc3e8d9229.js.map