:root{--h00-size-mobile: 2.5rem;--h0-size-mobile: 2rem;--h1-size-mobile: 1.625rem;--h2-size-mobile: 1.375rem;--h3-size-mobile: 1.125rem;--h00-size: 3rem;--h0-size: 2.5rem;--h1-size: 2rem;--h2-size: 1.5rem;--h3-size: 1.25rem;--h4-size: 1rem;--h5-size: 0.875rem;--h6-size: 0.75rem;--body-font-size: 0.875rem;--font-size-small: 0.75rem}.BorderGrid{display:table;width:100%;margin-top:-16px;margin-bottom:-16px;table-layout:fixed;border-collapse:collapse;border-style:hidden}.BorderGrid .BorderGrid-cell{padding-top:16px;padding-bottom:16px}.BorderGrid--spacious{margin-top:-24px;margin-bottom:-24px}.BorderGrid--spacious .BorderGrid-cell{padding-top:24px;padding-bottom:24px}.BorderGrid-row{display:table-row}.BorderGrid-cell{display:table-cell;border:1px solid var(--borderColor-muted, var(--color-border-muted))}.blame-commit{-webkit-user-select:none;user-select:none}.blame-commit[data-heat="1"]{border-right:2px solid #f66a0a}.blame-commit[data-heat="2"]{border-right:2px solid rgba(246,106,10,.9)}.blame-commit[data-heat="3"]{border-right:2px solid rgba(246,106,10,.8)}.blame-commit[data-heat="4"]{border-right:2px solid rgba(246,106,10,.7)}.blame-commit[data-heat="5"]{border-right:2px solid rgba(246,106,10,.6)}.blame-commit[data-heat="6"]{border-right:2px solid rgba(246,106,10,.5)}.blame-commit[data-heat="7"]{border-right:2px solid rgba(246,106,10,.4)}.blame-commit[data-heat="8"]{border-right:2px solid rgba(246,106,10,.3)}.blame-commit[data-heat="9"]{border-right:2px solid rgba(246,106,10,.2)}.blame-commit[data-heat="10"]{border-right:2px solid rgba(246,106,10,.1)}.heat[data-heat="1"]{background:#f66a0a}.heat[data-heat="2"]{background:rgba(246,106,10,.9)}.heat[data-heat="3"]{background:rgba(246,106,10,.8)}.heat[data-heat="4"]{background:rgba(246,106,10,.7)}.heat[data-heat="5"]{background:rgba(246,106,10,.6)}.heat[data-heat="6"]{background:rgba(246,106,10,.5)}.heat[data-heat="7"]{background:rgba(246,106,10,.4)}.heat[data-heat="8"]{background:rgba(246,106,10,.3)}.heat[data-heat="9"]{background:rgba(246,106,10,.2)}.heat[data-heat="10"]{background:rgba(246,106,10,.1)}.blame-commit-date{font-size:12px;line-height:25px;flex-shrink:0}.blame-commit-date[data-heat="1"]{color:#c24e00}.blame-commit-date[data-heat="2"]{color:#ac571f}.blame-commit-date[data-heat="3"]{color:#a35b2c}.blame-commit-date[data-heat="4"]{color:#9a5f38}.blame-commit-date[data-heat="5"]{color:#926245}.blame-commit-date[data-heat="6"]{color:#896651}.blame-commit-date[data-heat="7"]{color:#806a5e}.blame-commit-date[data-heat="8"]{color:#776d6a}.blame-commit-date[data-heat="9"]{color:#6e7177}.blame-commit-date[data-heat="10"]{color:#6a737d}.line-age-legend .heat{width:2px;height:10px;margin:2px 1px 0}.blame-breadcrumb .css-truncate-target{max-width:680px}.blame-commit-info{width:450px;height:26px}.blame-commit-content{flex-grow:2;overflow:hidden}.blame-commit-message{text-overflow:ellipsis}.blame-commit-message .message.blank{color:var(--fgColor-muted, var(--color-fg-muted))}.blob-reblame{min-width:24px;-webkit-user-select:none;user-select:none}.reblame-link{padding-top:2px;color:var(--fgColor-muted, var(--color-fg-muted));opacity:.3}.blame-hunk g-emoji{font-size:14px !important}.blame-hunk:hover .reblame-link{opacity:1}.blame-container .blame-blob-num,.blame-container .blob-code-inner{padding-top:3px;padding-bottom:3px}.blame-container .blob-code-inner{flex-grow:1}.blame-commit .AvatarStack{margin-top:4px}.hx_details-with-rotating-caret[open]>.btn-link .hx_dropdown-caret-rotatable{border-width:0 4px 4px 4px;border-top-color:transparent;border-bottom-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis))}.branches-tag-list{display:inline;margin-right:8px;margin-left:2px;vertical-align:middle;list-style:none}.branches-tag-list .more-commit-details,.branches-tag-list.open .hidden-text-expander{display:none}.branches-tag-list.open .more-commit-details{display:inline-block}.branches-tag-list li{display:inline-block;padding-left:4px}.branches-tag-list li:first-child{padding-left:0;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.branches-tag-list li.loading{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.branches-tag-list li.abbrev-tags{cursor:pointer}.branches-tag-list li a{color:inherit}@media(max-width: 767px){.branch-info-dropdown-size{top:6px;width:auto;min-width:300px}.branch-contribute-right{right:auto;left:-10px}.branch-contribute-right::before,.branch-contribute-right::after{right:auto;left:10px}}@media(min-width: 767px){.branch-info-dropdown-size{top:6px;width:auto;min-width:300px}}.create-branch-source-branch .SelectMenu-modal{max-height:100%;overflow:visible}.branch-a-b-count .count-half{position:relative;float:left;width:90px;padding-bottom:4px;text-align:right}.branch-a-b-count .count-half:last-child{text-align:left;border-left:1px solid var(--borderColor-default, var(--color-border-default))}.branch-a-b-count .count-value{position:relative;top:-1px;display:block;padding:0 4px;font-size:12px}.branch-a-b-count .bar{position:absolute;min-width:3px;height:4px}.branch-a-b-count .meter{position:absolute;height:4px;background-color:var(--bgColor-neutral-muted, var(--color-neutral-muted))}.branch-a-b-count .meter.zero{background-color:transparent}.branch-a-b-count .bar-behind{right:0;border-radius:6px 0 0 6px}.branch-a-b-count .bar-behind .meter{right:0;border-radius:6px 0 0 6px}.branch-a-b-count .bar-ahead{left:0;border-radius:0 6px 6px 0}.branch-a-b-count .bar-ahead .meter{border-radius:0 6px 6px 0}.branch-a-b-count .bar-ahead.even,.branch-a-b-count .bar-behind.even{min-width:2px;background:#eaecef}.branches .clear-search{display:none}.branches .loading-overlay{position:absolute;top:0;z-index:20;display:none;width:100%;height:100%;padding-top:50px;text-align:center}.branches .loading-overlay::before{position:absolute;top:0;right:0;bottom:0;left:0;content:"";background-color:var(--bgColor-default, var(--color-canvas-default));opacity:.7}.branches .loading-overlay .spinner{display:inline-block}.branches.is-loading .loading-overlay{display:block}.branches.is-search-mode .clear-search{display:inline-block}.commit-loader .loader-error{display:none;margin:0;font-size:12px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-danger, var(--color-danger-fg))}.commit-loader.is-error .loader-error{display:block}@media screen and (max-width: 768px){.truncate-for-mobile{display:none}}.show-for-mobile{display:none}@media screen and (max-width: 768px){.show-for-mobile{display:table-row}}.editor-abort{display:inline;font-size:14px}.file-commit-form{padding-left:64px}.file-commit-form--full{position:absolute;bottom:0;left:0;z-index:10;width:100%;padding-top:16px;padding-left:0;margin-top:16px;margin-bottom:16px;background:var(--bgColor-default, var(--color-canvas-default))}@media(min-width: 1012px){.file-commit-form--full{top:0;right:0;bottom:auto;left:auto;width:auto;margin-top:0;margin-bottom:0}}.file-commit-form--full .commit-form{padding:0;margin-bottom:24px;border:0}.file-commit-form--full .commit-form::before{display:none}.file-commit-form-dropdown{position:fixed;top:0;left:0;width:100%;height:100%}.file-commit-form-dropdown::after{display:none}@media(min-width: 1012px){.file-commit-form-dropdown{position:absolute;top:auto;left:auto;width:420px;height:auto}.file-commit-form-dropdown::after{display:inline-block}}.react-code-view-edit .cm-editor{border-bottom-right-radius:6px;border-bottom-left-radius:6px}.react-code-view-edit .cm-editor .cm-panels-bottom{contain:paint;border-bottom-right-radius:6px;border-bottom-left-radius:6px}.react-code-view-edit .cm-editor .cm-gutters{border-bottom-left-radius:6px}.page-blob.height-full .blob-wrapper{overflow-y:auto}.file-info-divider{display:inline-block;width:1px;height:18px;margin-right:4px;margin-left:4px;vertical-align:middle;border-left:1px solid var(--borderColor-default, var(--color-border-default))}.file-mode{text-transform:capitalize}.linejump .linejump-input{width:340px;background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.linejump .linejump-input,.linejump .btn{padding:8px 16px;font-size:16px}.include-fragment-error{display:none}.is-error .include-fragment-error{display:block}.html-blob{margin-bottom:16px}.file-sidebar-container .file{border-top-right-radius:0;border-bottom-right-radius:0}.file-navigation::before{display:table;content:""}.file-navigation::after{display:table;clear:both;content:""}.file-navigation .select-menu-button .css-truncate-target{max-width:200px}.file-navigation .breadcrumb{float:left;margin-top:0;margin-left:4px;font-size:16px;line-height:26px}.file-navigation+.breadcrumb{margin-bottom:8px}.file-blankslate{border:0;border-radius:0 0 6px 6px}.about-margin{margin-top:12px}@media screen and (max-width: 1011px){.about-margin{width:calc(100% - 24px);margin-left:24px}}.focusable-grid-cell{caret-color:transparent;scroll-margin-top:200px}.focusable-grid-cell:focus{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:-2px;box-shadow:inset 0 0 0 3px var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));box-shadow:none}.diff-line-row{height:var(--diff-line-minimum-height);line-height:24px}.diff-text-cell{position:relative;padding-top:1px;padding-right:24px;padding-left:24px}.diff-text-cell.hunk{display:flex;flex-direction:row;align-items:center;-webkit-user-select:none;user-select:none}.diff-text-cell .diff-text .diff-text-marker{position:absolute;top:1px;left:8px;padding-right:8px;-webkit-user-select:none;user-select:none}.diff-text-cell .diff-text .diff-text-inner{overflow:hidden;color:var(--fgColor-default, var(--color-fg-default));word-wrap:break-word;white-space:pre-wrap}.diff-text-cell .syntax-highlighted-line.addition .x{background-color:var(--diffBlob-addition-bgColor-word, var(--color-diff-blob-addition-word-bg))}.diff-text-cell .syntax-highlighted-line.deletion .x{background-color:var(--diffBlob-deletion-bgColor-word, var(--color-diff-blob-deletion-word-bg))}.diff-text-cell .syntax-highlighted-line .x-first{border-top-left-radius:4px;border-bottom-left-radius:4px}.diff-text-cell .syntax-highlighted-line .x-last{border-top-right-radius:4px;border-bottom-right-radius:4px}.empty-diff-line{background-color:var(--bgColor-neutral-muted, var(--color-neutral-subtle))}.diff-line-number{width:1%;min-width:50px;text-align:right;cursor:pointer;-webkit-user-select:none;user-select:none}.diff-line-number .diff-line-number-button{all:unset;width:100%}.diff-line-number .diff-line-number-button:hover{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.diff-line-number .diff-line-number-button:focus-visible{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:-2px;box-shadow:inset 0 0 0 3px var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));box-shadow:none}.diff-line-number.has-expander{padding-right:0;padding-left:0}:root{--line-number-cell-width: 40px;--diff-line-minimum-height: 24px}.hunk-kebab-icon{display:flex;width:var(--line-number-cell-width);padding-top:4px;background-color:var(--diffBlob-hunk-bgColor-num, var(--color-diff-blob-hunk-num-bg));justify-content:right}table[data-block-diff-cell-selection=left] .left-side-diff-cell{-webkit-user-select:none;user-select:none}table[data-block-diff-cell-selection=right] .right-side-diff-cell{-webkit-user-select:none;user-select:none}.react-code-file-contents{display:flex}.react-line-numbers{position:relative;display:flex;width:72px;min-width:72px;flex-direction:column;align-items:flex-end}.react-code-lines{position:relative;width:100%}.react-line-number{position:relative;padding-right:10px;padding-left:16px;color:var(--fgColor-muted, var(--color-fg-subtle));text-align:right;white-space:nowrap;border:0}.react-line-number.highlighted-line{z-index:1}.react-line-number:not(.prevent-click){cursor:pointer;-webkit-user-select:none;user-select:none}.react-line-number:not(.prevent-click):hover{color:var(--fgColor-default, var(--color-fg-default))}.react-code-line-contents{position:relative;display:flex;width:100%;padding-right:10px;padding-left:10px;overflow:visible;color:var(--fgColor-default, var(--color-fg-default));vertical-align:middle;scroll-margin-top:20vh}.react-code-text{font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;line-height:20px;word-wrap:normal;white-space:pre}.react-code-text span{display:inline-block}.react-code-text-cell{word-wrap:anywhere;white-space:pre-wrap}.react-code-text-cell span{display:inline}.react-code-text-cell span:empty{display:inline-block}.react-line-number.virtual,.react-code-line-contents.virtual{position:absolute;top:0}.react-csv-row{background:var(--bgColor-default, var(--color-canvas-default))}.react-csv-line-number{position:relative;padding-left:4px}.react-csv-line-number .react-line-number{padding-top:9px;padding-bottom:8px;padding-left:12px;line-height:unset}.react-csv-row--highlighted .react-csv-cell{background:var(--bgColor-attention-muted, var(--color-attention-subtle))}.react-csv-row--highlighted .react-csv-cell:nth-of-type(2){box-shadow:inset 2px 0 0 var(--borderColor-attention-emphasis, var(--color-attention-fg))}.react-csv-cell{padding:8px;font-size:12px;white-space:nowrap;border-top:1px solid var(--borderColor-default, var(--color-border-default));border-left:1px solid var(--borderColor-default, var(--color-border-default))}.react-csv-cell--header{font-weight:var(--base-text-weight-semibold, 600);text-align:left;background:var(--bgColor-muted, var(--color-canvas-subtle));border-top:0}.react-file-line.html-div{padding-left:10px}.react-file-line [data-code-text]::before{content:attr(data-code-text)}.bidi-replacement{-webkit-user-select:none;user-select:none;border:1px solid var(--borderColor-danger-emphasis, var(--color-danger-emphasis));border-radius:6px}.bidi-replacement.padded{padding:4px;margin-right:4px;margin-left:4px}.react-last-commit-summary-timestamp{display:none}@media(max-width: 544px){.react-last-commit-summary-timestamp{display:inherit}}.react-last-commit-timestamp{display:none}@media(max-width: 768px){.react-last-commit-timestamp{display:inherit}}@media(max-width: 544px){.react-last-commit-timestamp{display:none}}.react-last-commit-oid-timestamp{display:flex;flex-wrap:nowrap}@media(max-width: 768px){.react-last-commit-oid-timestamp{display:none}}.react-last-commit-message{display:flex}@media(max-width: 544px){.react-last-commit-message{display:none}}.react-last-commit-history-group{display:flex !important}@media(max-width: 768px){.react-last-commit-history-group{display:none !important}}.react-last-commit-history-icon{display:none !important}@media(max-width: 768px){.react-last-commit-history-icon{display:flex !important}}.react-code-size-details-banner{display:none}@media(max-width: 768px){.react-code-size-details-banner{display:flex !important}}@container (max-width: 768px){.react-code-size-details-banner{display:flex !important}}.react-code-size-details-in-header{display:flex;align-items:center}@media(max-width: 768px){.react-code-size-details-in-header{display:none}}@container (max-width: 768px){.react-code-size-details-in-header{display:none}}@media(max-width: 544px){.react-blob-view-header-sticky{position:relative !important}}.react-blob-header-edit-and-raw-actions{display:inherit !important}@media(max-width: 544px){.react-blob-header-edit-and-raw-actions{display:none !important}}.react-blob-header-edit-and-raw-actions-combined{display:none !important}@media(max-width: 544px){.react-blob-header-edit-and-raw-actions-combined{display:inherit !important}}@media(max-width: 430px){.react-contributors-title{display:none}}@media(max-width: 768px){.react-blame-for-range{background:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}}.react-file-upload{display:flex;min-height:0;flex-direction:column}.react-file-upload .file-input-focused{padding:5px 8px;border:2px solid var(--borderColor-accent-emphasis,var(--color-accent-emphasis)) !important}.react-blob-print-hide{font-family:ui-monospace,SFMono-Regular,"SF Mono",Menlo,Consolas,"Liberation Mono",monospace !important}@media print{.react-blob-print-hide{display:none}}@media(forced-colors: active){.code-navigation-cursor{forced-color-adjust:none;background-color:#fff !important}}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight{display:flex;justify-content:space-between;margin-bottom:16px;background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content pre,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight pre{margin-bottom:0}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content .zeroclipboard-container,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight .zeroclipboard-container{display:block;animation:none}.js-snippet-clipboard-copy-unpositioned .markdown-body .snippet-clipboard-content .zeroclipboard-container clipboard-copy,.js-snippet-clipboard-copy-unpositioned .markdown-body .highlight .zeroclipboard-container clipboard-copy{width:var(--control-small-size, 28px);height:var(--control-small-size, 28px)}.markdown-body .anchor{position:absolute}.markdown-body .heading-link{color:unset}.markdown-body .heading-link .octicon-link{margin-left:8px;color:var(--fgColor-muted, var(--color-fg-subtle));visibility:visible}.react-blob-print-hide::selection{background-color:var(--bgColor-accent-muted, var(--color-accent-muted))}.react-button-with-indicator::after{position:absolute;top:0;right:0;display:inline-block;width:var(--base-size-8, 8px);height:var(--base-size-8, 8px);content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border:2px solid var(--bgColor-default, var(--color-canvas-default));border-radius:50%}@media(max-width: 544px){.react-blob-sticky-header{display:none !important;content-visibility:hidden}.react-blob-scroll-marks{display:none}.AvatarShowLarge{display:none !important}.AvatarShowMedium{display:none !important}.popover-container-width{width:320px}}@media(min-width: 544px){.file-tree-view-readme-flash-narrow{display:none !important}}@media(max-width: 768px)and (min-width: 544px){.AvatarShowLarge{display:none !important}.AvatarShowMedium{display:inherit !important}}@media(max-width: 768px){.react-code-view-bottom-padding{margin-bottom:16px}.react-tree-show-tree-items-on-large-screen{display:none}.inner-panel-content-not-narrow{display:none !important}.find-text-help-tooltip{display:none !important}.blob-license-banner-outer{flex-direction:column}.code-nav-file-information{max-height:40vh;overflow-y:auto}.find-in-file-popover{position:absolute;right:0;bottom:0;left:0;z-index:11;width:100%;background:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:var(--borderRadius-large)}}@media(min-width: 768px){.AvatarShowLarge{display:inherit !important}.react-tree-show-tree-items-on-large-screen{display:block}.AvatarShowMedium{display:inherit !important}.react-code-view-bottom-padding{margin-bottom:16px}.panel-content-narrow-styles{width:33%;min-width:320px;max-width:460px}}@media(min-width: 768px)and (max-width: 1151px){.panel-content-narrow-styles{margin-top:33px}}@media(min-width: 768px){.blob-license-banner-outer{flex-direction:row}}@media(min-width: 768px){.find-in-file-popover-stickied{position:absolute;top:98px;right:8px;z-index:11;background:var(--bgColor-default, var(--color-canvas-default));border-bottom:none;border-radius:var(--borderRadius-large);box-shadow:var(--shadow-floating-large, var(--color-shadow-large))}}@media(min-width: 768px){.find-in-file-popover-not-stickied{position:absolute;top:52px;right:8px;z-index:11;background:var(--bgColor-default, var(--color-canvas-default));border-bottom:none;border-radius:var(--borderRadius-large);box-shadow:var(--shadow-floating-large, var(--color-shadow-large))}}.react-blob-scroll-marks{display:"block"}.react-tree-show-tree-items{display:block !important}@media(max-width: 1012px){.org-onboarding-tip-media{display:none}}.react-tree-pane-contents-3-panel{display:block}@media(min-width: 768px)and (max-width: 1350px){.react-tree-pane-contents-3-panel{display:none !important}}.react-tree-pane-contents{display:block}@media(min-width: 768px)and (max-width: 1012px){.react-tree-pane-contents{display:none !important}}.react-tree-pane-overlay-3-panel{display:none}@media(min-width: 768px)and (max-width: 1350px){.react-tree-pane-overlay-3-panel{display:block}}.react-tree-pane-overlay{display:none}@media(min-width: 768px)and (max-width: 1012px){.react-tree-pane-overlay{display:block}}.container{container-type:inline-size}@container (max-width: 768px){.react-code-view-header--wide{display:none !important}.react-code-view-header--narrow{display:flex !important;width:100%}}@container (min-width: 768px){.react-code-view-header--wide{display:flex !important;width:100%}.react-code-view-header--narrow{display:none !important}}@supports not (container-type: inline-size){@media(max-width: 768px){.react-code-view-header--wide{display:none !important}.react-code-view-header--narrow{display:flex !important;width:100%}}@media(min-width: 768px){.react-code-view-header--wide{display:flex !important;width:100%}.react-code-view-header--narrow{display:none !important}}}.react-directory-row{height:40px;font-size:14px}.react-directory-row td{padding-left:16px;text-align:left;border-top:1px solid var(--borderColor-default, var(--color-border-default))}.react-directory-row:hover{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.react-directory-filename-column{display:flex;height:40px;padding-right:16px;align-items:center;row-gap:4px;column-gap:10px}.react-directory-filename-column h3{margin:0;font-size:14px;font-weight:var(--base-text-weight-normal, 400)}.react-directory-filename-column .icon-directory{color:var(--color-icon-directory)}.react-directory-truncate{display:inline-block;max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:pre;vertical-align:top}.react-directory-commit-message{max-width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.react-directory-commit-age{padding-right:16px;color:var(--fgColor-muted, var(--color-fg-muted));text-align:right}.react-tree-toggle-button-with-indicator::after{position:absolute;top:3px;right:2px;display:inline-block;width:var(--base-size-8, 8px);height:var(--base-size-8, 8px);content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border:2px solid var(--bgColor-default, var(--color-canvas-default));border-radius:50%}@media screen and (min-width: 1280px){.react-repos-overview-margin{margin-right:calc((100% - 1356px)/2)}}@media screen and (min-width: 1440px){.react-repos-overview-margin.tree-open{margin-right:0}}@media screen and (min-width: 1856px){.react-repos-overview-margin.tree-open{margin-right:calc((100% - 1856px)/2)}}.react-repos-tree-pane-ref-selector span{justify-content:normal}.react-directory-row-name-cell-small-screen{display:none}@media screen and (max-width: 544px){.react-directory-row-name-cell-small-screen{display:table-cell}}.react-directory-row-name-cell-large-screen{display:table-cell}@media screen and (max-width: 544px){.react-directory-row-name-cell-large-screen{display:none}}.react-directory-row-commit-cell{display:table-cell}@media screen and (max-width: 544px){.react-directory-row-commit-cell{display:none}}.react-directory-add-file-icon{display:block}@media screen and (min-width: 1279px){.react-directory-add-file-icon{display:none}}.react-directory-remove-file-icon{display:block}@media screen and (max-width: 1278px){.react-directory-remove-file-icon{display:none}}.manifest-commit-form{margin-top:16px}.repo-file-upload-outline{width:100%;height:100%}.repo-file-upload-target{position:relative}.repo-file-upload-target.is-uploading .repo-file-upload-text.initial-text,.repo-file-upload-target.is-failed .repo-file-upload-text.initial-text,.repo-file-upload-target.is-default .repo-file-upload-text.initial-text{display:none}.repo-file-upload-target.is-uploading .repo-file-upload-text.alternate-text,.repo-file-upload-target.is-failed .repo-file-upload-text.alternate-text,.repo-file-upload-target.is-default .repo-file-upload-text.alternate-text{display:block}.repo-file-upload-target.is-uploading.dragover .repo-file-upload-text,.repo-file-upload-target.is-failed.dragover .repo-file-upload-text,.repo-file-upload-target.is-default.dragover .repo-file-upload-text{display:none}.repo-file-upload-target .repo-file-upload-text.initial-text{display:block}.repo-file-upload-target .repo-file-upload-text.alternate-text{display:none}.repo-file-upload-target .repo-file-upload-text,.repo-file-upload-target .repo-file-upload-drop-text{margin-bottom:4px}.repo-file-upload-target .repo-file-upload-choose{display:inline-block;margin-top:0;font-size:16px}.repo-file-upload-target .manual-file-chooser{margin-left:0}.repo-file-upload-target .manual-file-chooser:hover+.manual-file-chooser-text{text-decoration:underline}.repo-file-upload-target .manual-file-chooser:focus+.manual-file-chooser-text{text-decoration:underline;outline:var(--focus-outlineColor, var(--color-accent-fg)) solid 2px}.repo-file-upload-target .repo-file-upload-outline{position:absolute;top:3%;left:1%;width:98%;height:94%}.repo-file-upload-target.is-failed .repo-file-upload-outline,.repo-file-upload-target.is-bad-file .repo-file-upload-outline,.repo-file-upload-target.is-too-big .repo-file-upload-outline,.repo-file-upload-target.is-too-many .repo-file-upload-outline,.repo-file-upload-target.is-empty .repo-file-upload-outline{height:85%}.repo-file-upload-target.dragover .repo-file-upload-text{display:none}.repo-file-upload-target.dragover .repo-file-upload-choose{visibility:hidden}.repo-file-upload-target.dragover .repo-file-upload-drop-text{display:block}.repo-file-upload-target.dragover .repo-file-upload-outline{border:6px dashed var(--borderColor-default, var(--color-border-default));border-radius:6px}.repo-file-upload-target .repo-file-upload-drop-text{display:none}.repo-file-upload-errors{display:none}.repo-file-upload-errors .error{display:none}.is-failed .repo-file-upload-errors,.is-bad-file .repo-file-upload-errors,.is-too-big .repo-file-upload-errors,.is-too-many .repo-file-upload-errors,.is-hidden-file .repo-file-upload-errors,.is-empty .repo-file-upload-errors{position:absolute;right:0;bottom:0;left:0;display:block;padding:4px 8px;line-height:1.5;text-align:left;background-color:var(--bgColor-default, var(--color-canvas-default));border-top:1px solid var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:6px;border-bottom-left-radius:6px}.is-file-list .repo-file-upload-errors{border-bottom-right-radius:0;border-bottom-left-radius:0}.is-failed .repo-file-upload-errors .failed-request,.is-bad-file .repo-file-upload-errors .failed-request{display:inline-block}.is-too-big .repo-file-upload-errors .too-big{display:inline-block}.is-hidden-file .repo-file-upload-errors .hidden-file{display:inline-block}.is-too-many .repo-file-upload-errors .too-many{display:inline-block}.is-empty .repo-file-upload-errors .empty{display:inline-block}.repo-file-upload-tree-target{position:fixed;top:0;left:0;z-index:1000;width:100%;height:100%;padding:16px;color:var(--fgColor-default, var(--color-fg-default));visibility:hidden;background:var(--bgColor-default, var(--color-canvas-default));opacity:0}.repo-file-upload-tree-target .repo-file-upload-outline{border:6px dashed var(--borderColor-default, var(--color-border-default));border-radius:6px}.dragover .repo-file-upload-tree-target{visibility:visible;opacity:1;transition:visibility .2s,opacity .2s}.dragover .repo-file-upload-tree-target .repo-file-upload-slate{top:50%;opacity:1}.repo-file-upload-slate{position:absolute;top:50%;width:100%;text-align:center;transform:translateY(-50%)}.repo-file-upload-slate h2{margin-top:4px}.repo-upload-breadcrumb{margin-bottom:16px}.tree-finder-input{min-height:32px;box-sizing:border-box;border-color:transparent}.tree-finder-input,.tree-finder-input:focus{font-size:inherit;box-shadow:none;-webkit-appearance:none;appearance:none}.tree-browser .octicon-chevron-right{color:transparent}.tree-browser-result .octicon-file{color:var(--fgColor-muted, var(--color-fg-muted))}.tree-browser-result:hover,.tree-browser-result[aria-selected=true]{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.tree-browser-result:hover .octicon-file,.tree-browser-result[aria-selected=true] .octicon-file{color:inherit}.tree-browser-result[aria-selected=true] .octicon-chevron-right{color:inherit}.tree-browser-result .css-truncate-target{max-width:870px}.tree-browser-result mark{font-weight:var(--base-text-weight-semibold, 600);color:inherit;background:none}

/*# sourceMappingURL=code-e32e293f8d02.css.map*/