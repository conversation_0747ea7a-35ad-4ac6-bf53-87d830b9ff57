"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["element-registry"],{48293:(e,s,t)=>{var _=t(76006);(0,_.nW)("animated-image",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_hydro-analytics-client_dist_analytics-client_js-node_modules_gith-ffadee2"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_accessibility_animated-image-element_ts")]).then(t.bind(t,38410))),(0,_.nW)("launch-code",()=>t.e("app_components_account_verifications_launch-code-element_ts").then(t.bind(t,34166))),(0,_.nW)("actions-caches-filter",()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_delegated-events_di-94a48b"),t.e("app_assets_modules_github_filter-input_ts"),t.e("app_components_actions_actions-caches-filter-element_ts-node_modules_github_memoize_dist_esm_-7102b0")]).then(t.bind(t,42814))),(0,_.nW)("actions-workflow-filter",()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_delegated-events_di-94a48b"),t.e("app_assets_modules_github_filter-input_ts"),t.e("app_components_actions_actions-workflow-filter-element_ts-node_modules_github_memoize_dist_es-ea404a")]).then(t.bind(t,22455))),(0,_.nW)("variable-value",()=>t.e("app_components_actions_variables_variable-value-element_ts").then(t.bind(t,47664))),(0,_.nW)("variables-input",()=>t.e("app_components_actions_variables_variables-input-element_ts").then(t.bind(t,34268))),(0,_.nW)("variables-pagination",()=>t.e("app_components_actions_variables_variables-pagination-element_ts").then(t.bind(t,82997))),(0,_.nW)("metric-selection",()=>t.e("app_components_advisories_metric-selection-element_ts").then(t.bind(t,86585))),(0,_.nW)("severity-calculator",()=>t.e("app_components_advisories_severity-calculator-element_ts").then(t.bind(t,85825))),(0,_.nW)("severity-score",()=>t.e("app_components_advisories_severity-score-element_ts").then(t.bind(t,56585))),(0,_.nW)("severity-selection",()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("app_components_advisories_severity-selection-element_ts")]).then(t.bind(t,89765))),(0,_.nW)("severity-tracking",()=>t.e("app_components_advisories_severity-tracking-element_ts").then(t.bind(t,81573))),(0,_.nW)("webauthn-status",()=>t.e("app_components_behaviors_webauthn-status-element_ts").then(t.bind(t,76557))),(0,_.nW)("downgrade-dialog",()=>t.e("app_components_billing_settings_downgrade-dialog-element_ts").then(t.bind(t,70862))),(0,_.nW)("manage-subscription",()=>t.e("app_components_billing_settings_upgrade_manage-subscription-element_ts").then(t.bind(t,84656))),(0,_.nW)("pending-cycle-changes-component",()=>t.e("app_components_billing_stafftools_pending-cycle-changes-component-element_ts").then(t.bind(t,67965))),(0,_.nW)("create-branch",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("vendors-node_modules_virtualized-list_es_index_js-node_modules_github_template-parts_lib_index_js"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_ref-selector_ts"),t.e("app_components_branch_create-branch-element_ts")]).then(t.bind(t,32858))),(0,_.nW)("create-repo-from-selector",()=>t.e("app_components_branch_create-repo-from-selector-element_ts").then(t.bind(t,47808))),(0,_.nW)("metered-license-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_components_businesses_metered-license-graph-element_ts")]).then(t.bind(t,95745))),(0,_.nW)("select-all",()=>t.e("app_components_businesses_people_select-all-element_ts").then(t.bind(t,5458))),(0,_.nW)("close-reason-selector",()=>t.e("app_components_closables_buttons_close-reason-selector-element_ts").then(t.bind(t,98775))),(0,_.nW)("reopen-reason-selector",()=>t.e("app_components_closables_buttons_reopen-reason-selector-element_ts").then(t.bind(t,94038))),(0,_.nW)("alert-dismissal-details",()=>t.e("app_components_code_scanning_alert-dismissal-details-element_ts").then(t.bind(t,68497))),(0,_.nW)("pretty-cron",()=>Promise.all([t.e("vendors-node_modules_cronstrue_dist_cronstrue_js"),t.e("app_components_code_scanning_pretty-cron-element_ts")]).then(t.bind(t,25134))),(0,_.nW)("message-list",()=>t.e("app_components_code_scanning_tool_status_message-list-element_ts").then(t.bind(t,26214))),(0,_.nW)("sku-list",()=>t.e("app_components_codespaces_advanced_options_sku-list-element_ts").then(t.bind(t,81590))),(0,_.nW)("create-button",()=>t.e("app_components_codespaces_create-button-element_ts").then(t.bind(t,38883))),(0,_.nW)("editor-forwarder",()=>t.e("app_components_codespaces_editor-forwarder-element_ts").then(t.bind(t,69511))),(0,_.nW)("command-palette-page",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_allex_crc32_lib_crc32_esm_js-node_modules_github_mini-throttle_dist_deco-b38cad"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_command-palette_items_help-item_ts-app_assets_modules_github_comman-48ad9d"),t.e("app_components_command_palette_command-palette-page-element_ts")]).then(t.bind(t,66611))),(0,_.nW)("command-palette-page-stack",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_allex_crc32_lib_crc32_esm_js-node_modules_github_mini-throttle_dist_deco-b38cad"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_command-palette_items_help-item_ts-app_assets_modules_github_comman-48ad9d"),t.e("app_components_command_palette_command-palette-page-stack-element_ts")]).then(t.bind(t,27977))),(0,_.nW)("feed-post",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_conduit_feed-post-element_ts")]).then(t.bind(t,8444))),(0,_.nW)("user-settings",()=>t.e("app_components_copilot_policies_user-settings-element_ts").then(t.bind(t,74252))),(0,_.nW)("copilot-business-signup-seat-management",()=>t.e("app_components_copilot_seat_management_copilot-business-signup-seat-management-element_ts").then(t.bind(t,93159))),(0,_.nW)("loading-context",()=>t.e("app_components_dashboard_loading-context-element_ts").then(t.bind(t,21699))),(0,_.nW)("portal-fragment",()=>t.e("app_components_dashboard_portal-fragment-element_ts").then(t.bind(t,47254))),(0,_.nW)("query-search",()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_delegated-events_di-94a48b"),t.e("app_assets_modules_github_filter-input_ts"),t.e("app_components_dashboard_query-search-element_ts-node_modules_github_memoize_dist_esm_index_js")]).then(t.bind(t,26283))),(0,_.nW)("dependabot-alert-dismissal",()=>t.e("app_components_dependabot_alerts_dependabot-alert-dismissal-element_ts").then(t.bind(t,8300))),(0,_.nW)("dependabot-alert-load-all",()=>t.e("app_components_dependabot_alerts_dependabot-alert-load-all-element_ts").then(t.bind(t,113))),(0,_.nW)("dependabot-alert-row",()=>t.e("app_components_dependabot_alerts_dependabot-alert-row-element_ts").then(t.bind(t,55648))),(0,_.nW)("dependabot-alert-table-header",()=>t.e("app_components_dependabot_alerts_dependabot-alert-table-header-element_ts").then(t.bind(t,96812))),(0,_.nW)("dependabot-updates-paused",()=>t.e("app_components_dependabot_dependabot-updates-paused-element_ts").then(t.bind(t,97380))),(0,_.nW)("deferred-diff-lines",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_diffs_deferred-diff-lines-element_ts")]).then(t.bind(t,49853))),(0,_.nW)("edit-history",()=>t.e("app_components_discussions_edit-history-element_ts").then(t.bind(t,19583))),(0,_.nW)("feed-post-comments",()=>t.e("app_components_feed_posts_feed-post-comments-element_ts").then(t.t.bind(t,40832,23))),(0,_.nW)("conduit-profile-feed-visibility",()=>t.e("app_components_feed_conduit-profile-feed-visibility-element_ts").then(t.bind(t,98388))),(0,_.nW)("readme-toc",()=>t.e("app_components_files_readme-toc-element_ts").then(t.bind(t,63200))),(0,_.nW)("delayed-loading",()=>t.e("app_components_github_delayed-loading-element_ts").then(t.bind(t,85409))),(0,_.nW)("remote-pagination",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_github_remote-pagination-element_ts")]).then(t.bind(t,32625))),(0,_.nW)("dialog-hydro",()=>t.e("app_components_hydro_dialog-hydro-element_ts").then(t.bind(t,68692))),(0,_.nW)("track-view",()=>t.e("app_components_hydro_track-view-element_ts").then(t.bind(t,45814))),(0,_.nW)("development-menu",()=>t.e("app_components_issues_references_development-menu-element_ts").then(t.bind(t,49388))),(0,_.nW)("load-versions",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_marketplace_load-versions-element_ts")]).then(t.bind(t,70456))),(0,_.nW)("math-renderer",()=>Promise.all([t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("app_components_mathjax_math-renderer-element_ts")]).then(t.bind(t,55198))),(0,_.nW)("memex-project-picker",()=>Promise.all([t.e("app_assets_modules_github_virtual-listbox-focus-state_ts-node_modules_github_template-parts_l-448df2"),t.e("app_components_memex_memex-project-picker-element_ts")]).then(t.bind(t,47501))),(0,_.nW)("memex-project-picker-interstitial",()=>t.e("app_components_memex_project_list_memex-project-picker-interstitial-element_ts").then(t.bind(t,31740))),(0,_.nW)("memex-project-picker-unlink",()=>t.e("app_components_memex_project_list_memex-project-picker-unlink-element_ts").then(t.bind(t,80075))),(0,_.nW)("project-buttons-list",()=>t.e("app_components_memex_project_list_project-buttons-list-element_ts").then(t.bind(t,23140))),(0,_.nW)("navigation-list",()=>t.e("app_components_navigation_navigation-list-element_ts").then(t.bind(t,4745))),(0,_.nW)("notification-shelf-watcher",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_updatable-content_ts-ui_packages_hydro-analytics_hydro-analytics_ts"),t.e("node_modules_scroll-anchoring_dist_scroll-anchoring_esm_js-app_assets_modules_github_parse-ht-f4553d")]).then(t.bind(t,51284))),(0,_.nW)("feature-request",()=>t.e("app_components_organizations_member_requests_feature-request-element_ts").then(t.bind(t,40990))),(0,_.nW)("allowed-values-input",()=>t.e("app_components_organizations_settings_codespaces_policy_form_constraint_row_allowed-values-in-672002").then(t.bind(t,80193))),(0,_.nW)("host-setup",()=>t.e("app_components_organizations_settings_codespaces_policy_form_constraint_row_host-setup-element_ts").then(t.bind(t,60302))),(0,_.nW)("max-value",()=>t.e("app_components_organizations_settings_codespaces_policy_form_constraint_row_max-value-element_ts").then(t.bind(t,49952))),(0,_.nW)("codespaces-policy-form",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("app_components_organizations_settings_codespaces-policy-form-element_ts")]).then(t.bind(t,96099))),(0,_.nW)("insights-form",()=>t.e("app_components_organizations_settings_features_insights-form-element_ts").then(t.bind(t,55492))),(0,_.nW)("repository-bulk-selector",()=>t.e("app_components_organizations_settings_repository-bulk-selector-element_ts").then(t.bind(t,11696))),(0,_.nW)("required-workflow-selector",()=>t.e("app_components_organizations_settings_required-workflow-selector-element_ts").then(t.bind(t,62907))),(0,_.nW)("required-workflow-target-selector",()=>t.e("app_components_organizations_settings_required-workflow-target-selector-element_ts").then(t.bind(t,33113))),(0,_.nW)("repository-selection-input",()=>t.e("app_components_packages_repository-selection-input-element_ts").then(t.bind(t,19208))),(0,_.nW)("experimental-action-menu",()=>t.e("app_components_primer_action_menu_experimental-action-menu-element_ts").then(t.bind(t,92585))),(0,_.nW)("nav-list-group",()=>t.e("app_components_primer_experimental_side_panel_nav_list_nav-list-group-element_ts").then(t.bind(t,52674))),(0,_.nW)("split-page-layout",()=>t.e("app_components_primer_experimental_split-page-layout-element_ts").then(t.bind(t,53866))),(0,_.nW)("toggle-switch",()=>t.e("app_components_primer_experimental_toggle-switch-element_ts").then(t.bind(t,11772))),(0,_.nW)("lazy-load-section",()=>t.e("app_components_primer_navigation_list_lazy-load-section-element_ts").then(t.bind(t,295))),(0,_.nW)("profile-timezone",()=>t.e("app_components_profiles_profile-timezone-element_ts").then(t.bind(t,11879))),(0,_.nW)("copilot-marketing-popover",()=>t.e("app_components_pull_requests_copilot-marketing-popover-element_ts").then(t.bind(t,27811))),(0,_.nW)("file-filter",()=>t.e("app_components_pull_requests_file_tree_file-filter-element_ts").then(t.bind(t,44537))),(0,_.nW)("file-tree",()=>t.e("app_components_pull_requests_file_tree_file-tree-element_ts").then(t.bind(t,31980))),(0,_.nW)("file-tree-toggle",()=>t.e("app_components_pull_requests_file_tree_file-tree-toggle-element_ts").then(t.bind(t,35306))),(0,_.nW)("reactions-menu",()=>t.e("app_components_reactions_reactions-menu-element_ts").then(t.bind(t,31010))),(0,_.nW)("pin-organization-repo",()=>t.e("app_components_repositories_pin-organization-repo-element_ts").then(t.bind(t,7673))),(0,_.nW)("custom-scopes",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_hydro-analytics-client_dist_analytics-client_js-node_modules_gith-ffadee0"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_search_custom-scopes-element_ts")]).then(t.bind(t,9655))),(0,_.nW)("feature-preview-auto-enroll",()=>t.e("app_components_search_feature-preview-auto-enroll-element_ts").then(t.bind(t,84947))),(0,_.nW)("qbsearch-input",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js"),t.e("vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_github_hotkey_dist_-8755d2"),t.e("vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_delegated-events_dist_in-7df260"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_blob-anchor_ts-app_assets_modules_github_filter-sort_ts-app_assets_-681869"),t.e("app_assets_modules_github_jump-to_ts"),t.e("app_assets_modules_github_onfocus_ts-app_assets_modules_github_visible_ts-app_components_sear-8eff57")]).then(t.bind(t,58604))),(0,_.nW)("alert-dismissal",()=>t.e("app_components_secret_scanning_alert_centric_view_alert-dismissal-element_ts").then(t.bind(t,20911))),(0,_.nW)("coverage-settings",()=>t.e("app_components_security_center_coverage-settings-element_ts").then(t.bind(t,26367))),(0,_.nW)("multi-repo-enablement-coordinator",()=>t.e("app_components_security_center_coverage_enablement_multi-repo-enablement-coordinator-element_ts").then(t.bind(t,75316))),(0,_.nW)("multi-repo-enablement",()=>t.e("app_components_security_center_coverage_enablement_multi-repo-enablement-element_ts").then(t.bind(t,13116))),(0,_.nW)("multi-repo-enablement-setting",()=>t.e("app_components_security_center_coverage_enablement_multi-repo-enablement-setting-element_ts").then(t.bind(t,17390))),(0,_.nW)("table-item-selection",()=>t.e("app_components_security_center_coverage_enablement_table-item-selection-element_ts").then(t.bind(t,57020))),(0,_.nW)("email-preferences-form",()=>t.e("app_components_settings_email-preferences-form-element_ts").then(t.bind(t,28254))),(0,_.nW)("preview-announcement-button",()=>t.e("app_components_settings_messages_preview-announcement-button-element_ts").then(t.bind(t,187))),(0,_.nW)("recovery-codes",()=>t.e("app_components_settings_recovery-codes-element_ts").then(t.bind(t,56819))),(0,_.nW)("project-picker",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_turbo_dist_turbo_es2017-esm_js"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_virtual-listbox-focus-state_ts-node_modules_github_template-parts_l-448df2"),t.e("app_components_sidebar_project-picker-element_ts")]).then(t.bind(t,73209))),(0,_.nW)("deferred-side-panel",()=>t.e("app_components_site_header_deferred-side-panel-element_ts").then(t.bind(t,37331))),(0,_.nW)("notification-indicator",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_site_header_notification-indicator-element_ts")]).then(t.bind(t,26056))),(0,_.nW)("user-drawer-side-panel",()=>t.e("app_components_site_header_user-drawer-side-panel-element_ts").then(t.bind(t,53751))),(0,_.nW)("slash-command-toolbar-button",()=>t.e("app_components_slash_commands_slash-command-toolbar-button-element_ts").then(t.bind(t,93732))),(0,_.nW)("featured-work",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_sponsors_dashboard_featured-work-element_ts")]).then(t.bind(t,37567))),(0,_.nW)("monthly-spend-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_components_sponsors_orgs_premium_dashboard_monthly-spend-graph-element_ts")]).then(t.bind(t,48365))),(0,_.nW)("variant-menu-item",()=>t.e("app_components_stafftools_azure_exp_variant-menu-item-element_ts").then(t.bind(t,3743))),(0,_.nW)("enterprise-billing-licensing-type-component",()=>t.e("app_components_stafftools_billing_businesses_enterprise-billing-licensing-type-component-element_ts").then(t.bind(t,79083))),(0,_.nW)("metered-billing-settings-component",()=>t.e("app_components_stafftools_billing_businesses_metered-billing-settings-component-element_ts").then(t.bind(t,66412))),(0,_.nW)("billing-transaction-component",()=>t.e("app_components_stafftools_billing_history_billing-transaction-component-element_ts").then(t.bind(t,46436))),(0,_.nW)("sponsorships-tabs",()=>t.e("app_components_stafftools_billing_sponsorships-tabs-element_ts").then(t.bind(t,98014))),(0,_.nW)("bundle-size-stats",()=>Promise.all([t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_stafftools_bundle-size-stats_bundle-size-stats-element_ts-node_modules_github_-d6fe14")]).then(t.bind(t,50058))),(0,_.nW)("datahpc-staffbar",()=>Promise.all([t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_stafftools_data_hpc_datahpc-staffbar-element_ts-node_modules_github_memoize_di-901f39")]).then(t.bind(t,39405))),(0,_.nW)("react-profiling-toggle",()=>Promise.all([t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_stafftools_react_react-profiling-toggle-element_ts-node_modules_github_memoize-0bf68d")]).then(t.bind(t,7765))),(0,_.nW)("react-staffbar",()=>t.e("app_components_stafftools_react_react-staffbar-element_ts").then(t.bind(t,56990))),(0,_.nW)("soft-nav-staffbar",()=>Promise.all([t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_stafftools_soft_nav_soft-nav-staffbar-element_ts-node_modules_github_memoize_d-46bda5")]).then(t.bind(t,43356))),(0,_.nW)("soft-nav-staffbar-preview",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_components_stafftools_soft_nav_soft-nav-staffbar-preview-element_ts")]).then(t.bind(t,76335))),(0,_.nW)("themed-picture",()=>t.e("app_components_themed_pictures_themed-picture-element_ts").then(t.bind(t,15742))),(0,_.nW)("tasklist-block-add-tasklist",()=>Promise.all([t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("vendors-node_modules_github_sortablejs_Sortable_js"),t.e("vendors-node_modules_color-convert_index_js"),t.e("app_components_tracking_blocks_tracking-block-element_ts"),t.e("app_components_tracking_blocks_tasklist-block-add-tasklist-element_ts")]).then(t.bind(t,67920))),(0,_.nW)("tasklist-block-title",()=>t.e("app_components_tracking_blocks_tasklist-block-title-element_ts").then(t.bind(t,98039))),(0,_.nW)("tracking-block",()=>Promise.all([t.e("vendors-node_modules_morphdom_dist_morphdom-esm_js"),t.e("vendors-node_modules_github_sortablejs_Sortable_js"),t.e("vendors-node_modules_color-convert_index_js"),t.e("app_components_tracking_blocks_tracking-block-element_ts")]).then(t.bind(t,17803))),(0,_.nW)("tracking-block-omnibar",()=>t.e("app_components_tracking_blocks_tracking-block-omnibar-element_ts").then(t.bind(t,16556))),(0,_.nW)("two-factor-fallback-sms-config-toggle",()=>t.e("app_components_users_settings_two-factor-fallback-sms-config-toggle-element_ts").then(t.bind(t,75352))),(0,_.nW)("two-factor-inline-expander",()=>t.e("app_components_users_settings_two-factor-inline-expander-element_ts").then(t.bind(t,83123))),(0,_.nW)("action-list",()=>Promise.all([t.e("vendors-node_modules_primer_behaviors_dist_esm_focus-zone_js"),t.e("ui_packages_action-list-element_action-list-element_ts")]).then(t.bind(t,57683))),(0,_.nW)("codespace-share-dialog",()=>t.e("ui_packages_codespace-share-dialog-element_codespace-share-dialog-element_ts").then(t.bind(t,95395))),(0,_.nW)("copy-project",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_hydro-analytics-client_dist_analytics-client_js-node_modules_gith-ffadee1"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("ui_packages_copy-project-element_copy-project-element_ts")]).then(t.bind(t,93605))),(0,_.nW)("dependabot-rules-list",()=>t.e("ui_packages_dependabot-rules-list-element_dependabot-rules-list-element_ts").then(t.bind(t,41165))),(0,_.nW)("flywheel-loading-button",()=>t.e("ui_packages_flywheel-loading-button-element_flywheel-loading-button-element_ts").then(t.bind(t,5117))),(0,_.nW)("issue-create",()=>t.e("ui_packages_issue-create-element_issue-create-element_ts").then(t.bind(t,28076))),(0,_.nW)("query-builder",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("ui_packages_query-builder-element_query-builder-element_ts"),t.e("ui_packages_trusted-types-policies_policy_ts-ui_packages_trusted-types_trusted-types_ts-node_-38f8f1")]).then(t.bind(t,90113))),(0,_.nW)("show-dialog-on-load",()=>t.e("ui_packages_show-dialog-on-load-element_show-dialog-on-load-element_ts").then(t.bind(t,89908))),(0,_.nW)("sidebar-pinned-topics",()=>t.e("ui_packages_sidebar-pinned-topics-element_sidebar-pinned-topics-element_ts").then(t.bind(t,27286))),(0,_.nW)("site-header-logged-in-user-menu",()=>t.e("ui_packages_site-header-logged-in-user-menu-element_site-header-logged-in-user-menu-element_ts").then(t.bind(t,45522))),(0,_.nW)("task-component",()=>t.e("ui_packages_task-component-element_task-component-element_ts").then(t.bind(t,91789))),(0,_.nW)("topic-feeds-toast-trigger",()=>t.e("ui_packages_topic-feeds-toast-trigger-element_topic-feeds-toast-trigger-element_ts").then(t.bind(t,57439))),(0,_.nW)("webauthn-get",()=>t.e("ui_packages_webauthn-get-element_webauthn-get-element_ts").then(t.bind(t,59840))),(0,_.nW)("webauthn-subtle",()=>t.e("ui_packages_webauthn-subtle-element_webauthn-subtle-element_ts").then(t.bind(t,69551))),(0,_.nW)("profile-pins",()=>t.e("app_assets_modules_github_profile_profile-pins-element_ts").then(t.bind(t,87728))),(0,_.nW)("emoji-picker",()=>t.e("app_assets_modules_github_emoji-picker-element_ts").then(t.bind(t,58364))),(0,_.nW)("edit-hook-secret",()=>t.e("app_assets_modules_github_behaviors_edit-hook-secret-element_ts").then(t.bind(t,10523))),(0,_.nW)("insights-query",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_insights-query_ts")]).then(t.bind(t,95742))),(0,_.nW)("remote-clipboard-copy",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_details-dialog_ts-app_assets_modules_github_fetch_ts"),t.e("node_modules_delegated-events_dist_index_js-app_assets_modules_github_behaviors_remote-clipbo-5a7899")]).then(t.bind(t,66453))),(0,_.nW)("series-table",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_series-table_ts")]).then(t.bind(t,41555))),(0,_.nW)("line-chart",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_line-chart_ts")]).then(t.bind(t,42217))),(0,_.nW)("bar-chart",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_bar-chart_ts")]).then(t.bind(t,60213))),(0,_.nW)("column-chart",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_column-chart_ts")]).then(t.bind(t,42560))),(0,_.nW)("stacked-area-chart",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_stacked-area-chart_ts")]).then(t.bind(t,26957))),(0,_.nW)("hero-stat",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_date-fns_esm_addDays_index_js-node_modules_date-fns_esm_addMonths_index_-c2644a"),t.e("vendors-node_modules_date-fns_esm_addWeeks_index_js-node_modules_date-fns_esm_addYears_index_-979617"),t.e("vendors-node_modules_chartjs-adapter-date-fns_dist_chartjs-adapter-date-fns_esm_js-node_modul-c11a2b"),t.e("vendors-node_modules_date-fns_esm_eachDayOfInterval_index_js-node_modules_date-fns_esm_isSame-86aef2"),t.e("ui_packages_insights-charts_src_index_ts"),t.e("app_assets_modules_github_insights_hero-stat_ts")]).then(t.bind(t,77560))),(0,_.nW)("pulse-authors-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_pulse-authors-graph-element_ts")]).then(t.bind(t,47256))),(0,_.nW)("community-contributions-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_community-contributions_ts")]).then(t.bind(t,53946))),(0,_.nW)("discussion-page-views-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_discussion-page-views_ts")]).then(t.bind(t,9378))),(0,_.nW)("discussions-daily-contributors",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_discussions-daily-contributors_ts")]).then(t.bind(t,36187))),(0,_.nW)("discussions-new-contributors",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_discussions-new-contributors_ts")]).then(t.bind(t,46961))),(0,_.nW)("code-frequency-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_code-frequency-graph-element_ts")]).then(t.bind(t,3867))),(0,_.nW)("contributors-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-transition_src_index_js"),t.e("vendors-node_modules_d3-array_src_descending_js-node_modules_d3-axis_src_axis_js-node_modules-35c8e8"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_contributors-graph-element_ts")]).then(t.bind(t,67980))),(0,_.nW)("org-insights-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-array_src_bisector_js-node_modules_d3-axis_src_axis_js-node_modules_d-c87f9c"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_org-insights-graph-element_ts")]).then(t.bind(t,79873))),(0,_.nW)("traffic-clones-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-array_src_bisector_js-node_modules_d3-axis_src_axis_js-node_modules_d-c87f9c"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_traffic-clones-graph-element_ts")]).then(t.bind(t,16859))),(0,_.nW)("traffic-visitors-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-array_src_bisector_js-node_modules_d3-axis_src_axis_js-node_modules_d-c87f9c"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_traffic-visitors-graph-element_ts")]).then(t.bind(t,60323))),(0,_.nW)("commit-activity-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-transition_src_index_js"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_commit-activity-graph-element_ts")]).then(t.bind(t,81799))),(0,_.nW)("marketplace-insights-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-array_src_bisector_js-node_modules_d3-axis_src_axis_js-node_modules_d-c87f9c"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_marketplace-insights-graph-element_ts")]).then(t.bind(t,58505))),(0,_.nW)("user-sessions-map",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("vendors-node_modules_d3-transition_src_index_js"),t.e("vendors-node_modules_d3-ease_src_circle_js-node_modules_d3-format_src_defaultLocale_js-node_m-27b0ee"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_settings_user-sessions-map-element_ts")]).then(t.bind(t,32589))),(0,_.nW)("reload-after-polling",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_behaviors_reload-after-polling-element_ts")]).then(t.bind(t,80557))),(0,_.nW)("query-builder",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("ui_packages_query-builder-element_query-builder-element_ts"),t.e("ui_packages_trusted-types-policies_policy_ts-ui_packages_trusted-types_trusted-types_ts-node_-38f8f1")]).then(t.bind(t,90113))),(0,_.nW)("package-dependencies-security-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("vendors-node_modules_d3-array_src_max_js-node_modules_d3-scale_src_index_js-node_modules_gith-2112c4"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_fetch_ts"),t.e("app_assets_modules_github_graphs_package-dependencies-security-graph-element_ts")]).then(t.bind(t,7112))),(0,_.nW)(".js-sub-dependencies",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_dependencies_ts")]).then(t.bind(t,5708))),(0,_.nW)("network-graph",()=>Promise.all([t.e("vendors-node_modules_dompurify_dist_purify_js"),t.e("vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183"),t.e("ui_packages_soft-nav_soft-nav_ts"),t.e("app_assets_modules_github_graphs_network-graph-element_ts")]).then(t.bind(t,72394))),(0,_.nW)("inline-machine-translation",()=>t.e("app_assets_modules_github_localization_inline-machine-translation-element_ts").then(t.bind(t,21263))),(0,_.nW)("custom-patterns-filter",()=>Promise.all([t.e("vendors-node_modules_github_selector-observer_dist_index_esm_js"),t.e("vendors-node_modules_lit-html_lit-html_js"),t.e("vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_delegated-events_di-94a48b"),t.e("app_assets_modules_github_filter-input_ts"),t.e("app_assets_modules_github_secret-scanning_custom-patterns-filter_ts-node_modules_github_memoi-5c3217")]).then(t.bind(t,94169)))},76006:(e,s,t)=>{let _;t.d(s,{Lj:()=>y,Ih:()=>S,P4:()=>c,nW:()=>$,fA:()=>P,GO:()=>q});let n=new WeakSet;function o(e){n.add(e),e.shadowRoot&&a(e.shadowRoot),r(e),i(e.ownerDocument)}function a(e){r(e),i(e)}let d=new WeakMap;function i(e=document){if(d.has(e))return d.get(e);let s=!1,t=new MutationObserver(e=>{for(let s of e)if("attributes"===s.type&&s.target instanceof Element)p(s.target);else if("childList"===s.type&&s.addedNodes.length)for(let e of s.addedNodes)e instanceof Element&&r(e)});t.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let _={get closed(){return s},unsubscribe(){s=!0,d.delete(e),t.disconnect()}};return d.set(e,_),_}function r(e){for(let s of e.querySelectorAll("[data-action]"))p(s);e instanceof Element&&e.hasAttribute("data-action")&&p(e)}function l(e){let s=e.currentTarget;for(let t of m(s))if(e.type===t.type){let _=s.closest(t.tag);n.has(_)&&"function"==typeof _[t.method]&&_[t.method](e);let o=s.getRootNode();if(o instanceof ShadowRoot&&n.has(o.host)&&o.host.matches(t.tag)){let s=o.host;"function"==typeof s[t.method]&&s[t.method](e)}}}function*m(e){for(let s of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=s.lastIndexOf(":"),t=Math.max(0,s.lastIndexOf("#"))||s.length;yield{type:s.slice(0,e),tag:s.slice(e+1,t),method:s.slice(t+1)||"handleEvent"}}}function p(e){for(let s of m(e))e.addEventListener(s.type,l)}function c(e,s){let t=e.tagName.toLowerCase();if(e.shadowRoot){for(let _ of e.shadowRoot.querySelectorAll(`[data-target~="${t}.${s}"]`))if(!_.closest(t))return _}for(let _ of e.querySelectorAll(`[data-target~="${t}.${s}"]`))if(_.closest(t)===e)return _}function u(e,s){let t=e.tagName.toLowerCase(),_=[];if(e.shadowRoot)for(let n of e.shadowRoot.querySelectorAll(`[data-targets~="${t}.${s}"]`))n.closest(t)||_.push(n);for(let n of e.querySelectorAll(`[data-targets~="${t}.${s}"]`))n.closest(t)===e&&_.push(n);return _}let h=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),b=(e,s="property")=>{let t=h(e);if(!t.includes("-"))throw new DOMException(`${s}: ${String(e)} is not a valid ${s} name`,"SyntaxError");return t};function f(e){let s=h(e.name).replace(/-element$/,"");try{window.customElements.define(s,e),window[e.name]=customElements.get(s)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}return e}function g(e){for(let s of e.querySelectorAll("template[data-shadowroot]"))s.parentElement===e&&e.attachShadow({mode:"closed"===s.getAttribute("data-shadowroot")?"closed":"open"}).append(s.content.cloneNode(!0))}let v="attr";function y(e,s){w(e,v).add(s)}let k=new WeakSet;function j(e,s){if(k.has(e))return;k.add(e);let t=Object.getPrototypeOf(e),_=t?.constructor?.attrPrefix??"data-";for(let n of(s||(s=w(t,v)),s)){let s=e[n],t=b(`${_}${n}`),o={configurable:!0,get(){return this.getAttribute(t)||""},set(e){this.setAttribute(t,e||"")}};"number"==typeof s?o={configurable:!0,get(){return Number(this.getAttribute(t)||0)},set(e){this.setAttribute(t,e)}}:"boolean"==typeof s&&(o={configurable:!0,get(){return this.hasAttribute(t)},set(e){this.toggleAttribute(t,e)}}),Object.defineProperty(e,n,o),n in e&&!e.hasAttribute(t)&&o.set.call(e,s)}}function W(e){let s=e.observedAttributes||[],t=e.attrPrefix??"data-",_=e=>b(`${t}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...w(e.prototype,v)].map(_).concat(s),set(e){s=e}})}let x=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let s=this,t=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){s.connectedCallback(this,t)};let _=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){s.disconnectedCallback(this,_)};let n=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,t,_){s.attributeChangedCallback(this,e,t,_,n)};let o=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return s.observedAttributes(this,o)},set(e){o=e}}),W(e),f(e)}observedAttributes(e,s){return s}connectedCallback(e,s){e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),g(e),j(e),o(e),s?.call(e),e.shadowRoot&&a(e.shadowRoot)}disconnectedCallback(e,s){s?.call(e)}attributeChangedCallback(e,s,t,_,n){j(e),"data-catalyst"!==s&&n&&n.call(e,s,t,_)}};function w(e,s){if(!Object.prototype.hasOwnProperty.call(e,x)){let s=e[x],t=e[x]=new Map;if(s)for(let[e,_]of s)t.set(e,new Set(_))}let t=e[x];return t.has(s)||t.set(s,new Set),t.get(s)}function P(e,s){w(e,"target").add(s),Object.defineProperty(e,s,{configurable:!0,get(){return c(this,s)}})}function q(e,s){w(e,"targets").add(s),Object.defineProperty(e,s,{configurable:!0,get(){return u(this,s)}})}function S(e){new CatalystDelegate(e)}let z=new Map,A=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),O=new Promise(e=>{let s=new AbortController;s.signal.addEventListener("abort",()=>e());let t={once:!0,passive:!0,signal:s.signal},_=()=>s.abort();document.addEventListener("mousedown",_,t),document.addEventListener("touchstart",_,t),document.addEventListener("keydown",_,t),document.addEventListener("pointerdown",_,t)}),C=e=>new Promise(s=>{let t=new IntersectionObserver(e=>{for(let _ of e)if(_.isIntersecting){s(),t.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let s of document.querySelectorAll(e))t.observe(s)}),D={ready:()=>A,firstInteraction:()=>O,visible:C},E=new WeakMap;function M(e){cancelAnimationFrame(E.get(e)||0),E.set(e,requestAnimationFrame(()=>{for(let s of z.keys()){let t=e.matches(s)?e:e.querySelector(s);if(customElements.get(s)||t){let _=t?.getAttribute("data-load-on")||"ready",n=_ in D?D[_]:D.ready;for(let e of z.get(s)||[])n(s).then(e);z.delete(s),E.delete(e)}}}))}function $(e,s){z.has(e)||z.set(e,new Set),z.get(e).add(s),M(document.body),_||(_=new MutationObserver(e=>{if(z.size)for(let s of e)for(let e of s.addedNodes)e instanceof Element&&M(e)})).observe(document,{subtree:!0,childList:!0})}}},e=>{var s=e(e.s=48293)}]);
//# sourceMappingURL=element-registry-a496d1da992c.js.map