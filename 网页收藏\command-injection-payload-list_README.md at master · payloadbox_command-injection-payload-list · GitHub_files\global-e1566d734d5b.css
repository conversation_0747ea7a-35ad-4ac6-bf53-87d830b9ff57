:root{--h00-size-mobile: 2.5rem;--h0-size-mobile: 2rem;--h1-size-mobile: 1.625rem;--h2-size-mobile: 1.375rem;--h3-size-mobile: 1.125rem;--h00-size: 3rem;--h0-size: 2.5rem;--h1-size: 2rem;--h2-size: 1.5rem;--h3-size: 1.25rem;--h4-size: 1rem;--h5-size: 0.875rem;--h6-size: 0.75rem;--body-font-size: 0.875rem;--font-size-small: 0.75rem}.color-border-inverse{border-color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis)) !important}.bg-gray-2,.bg-gray-3{background-color:var(--bgColor-neutral-muted, var(--color-neutral-muted)) !important}.color-text-white{color:var(--color-scale-white) !important}.border-white-fade{border-color:rgba(255,255,255,.15) !important}.lead{color:var(--fgColor-muted, var(--color-fg-muted))}.text-emphasized{color:var(--fgColor-default, var(--color-fg-default))}.Label.Label--orange{color:var(--fgColor-severe, var(--color-severe-fg));border-color:var(--borderColor-severe-emphasis, var(--color-severe-emphasis))}.Label.Label--purple{color:var(--fgColor-done, var(--color-done-fg));border-color:var(--borderColor-done-emphasis, var(--color-done-emphasis))}.Label.Label--pink{color:var(--fgColor-sponsors, var(--color-sponsors-fg));border-color:var(--borderColor-sponsors-emphasis, var(--color-sponsors-emphasis))}/*!
 * GitHub Light v0.5.0
 * Copyright (c) 2012 - 2017 GitHub, Inc.
 * Licensed under MIT (https://github.com/primer/github-syntax-theme-generator/blob/master/LICENSE)
 */.pl-c{color:var(--color-prettylights-syntax-comment)}.pl-c1,.pl-s .pl-v{color:var(--color-prettylights-syntax-constant)}.pl-e,.pl-en{color:var(--color-prettylights-syntax-entity)}.pl-smi,.pl-s .pl-s1{color:var(--color-prettylights-syntax-storage-modifier-import)}.pl-ent{color:var(--color-prettylights-syntax-entity-tag)}.pl-k{color:var(--color-prettylights-syntax-keyword)}.pl-s,.pl-pds,.pl-s .pl-pse .pl-s1,.pl-sr,.pl-sr .pl-cce,.pl-sr .pl-sre,.pl-sr .pl-sra{color:var(--color-prettylights-syntax-string)}.pl-v,.pl-smw{color:var(--color-prettylights-syntax-variable)}.pl-bu{color:var(--color-prettylights-syntax-brackethighlighter-unmatched)}.pl-ii{color:var(--color-prettylights-syntax-invalid-illegal-text);background-color:var(--color-prettylights-syntax-invalid-illegal-bg)}.pl-c2{color:var(--color-prettylights-syntax-carriage-return-text);background-color:var(--color-prettylights-syntax-carriage-return-bg)}.pl-c2::before{content:"^M"}.pl-sr .pl-cce{font-weight:bold;color:var(--color-prettylights-syntax-string-regexp)}.pl-ml{color:var(--color-prettylights-syntax-markup-list)}.pl-mh,.pl-mh .pl-en,.pl-ms{font-weight:bold;color:var(--color-prettylights-syntax-markup-heading)}.pl-mi{font-style:italic;color:var(--color-prettylights-syntax-markup-italic)}.pl-mb{font-weight:bold;color:var(--color-prettylights-syntax-markup-bold)}.pl-md{color:var(--color-prettylights-syntax-markup-deleted-text);background-color:var(--color-prettylights-syntax-markup-deleted-bg)}.pl-mi1{color:var(--color-prettylights-syntax-markup-inserted-text);background-color:var(--color-prettylights-syntax-markup-inserted-bg)}.pl-mc{color:var(--color-prettylights-syntax-markup-changed-text);background-color:var(--color-prettylights-syntax-markup-changed-bg)}.pl-mi2{color:var(--color-prettylights-syntax-markup-ignored-text);background-color:var(--color-prettylights-syntax-markup-ignored-bg)}.pl-mdr{font-weight:bold;color:var(--color-prettylights-syntax-meta-diff-range)}.pl-ba{color:var(--color-prettylights-syntax-brackethighlighter-angle)}.pl-sg{color:var(--color-prettylights-syntax-sublimelinter-gutter-mark)}.pl-corl{text-decoration:underline;color:var(--color-prettylights-syntax-constant-other-reference-link)}.CodeMirror{font-family:monospace;height:300px;color:black;direction:ltr}.CodeMirror-lines{padding:4px 0}.CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{padding:0 4px}.CodeMirror-scrollbar-filler,.CodeMirror-gutter-filler{background-color:white}.CodeMirror-gutters{border-right:1px solid #ddd;background-color:#f7f7f7;white-space:nowrap}.CodeMirror-linenumber{padding:0 3px 0 5px;min-width:20px;text-align:right;color:#999;white-space:nowrap}.CodeMirror-guttermarker{color:black}.CodeMirror-guttermarker-subtle{color:#999}.CodeMirror-cursor{border-left:1px solid black;border-right:none;width:0}.CodeMirror div.CodeMirror-secondarycursor{border-left:1px solid silver}.cm-fat-cursor .CodeMirror-cursor{width:auto;border:0 !important;background:#7e7}.cm-fat-cursor div.CodeMirror-cursors{z-index:1}.cm-fat-cursor-mark{background-color:rgba(20, 255, 20, 0.5);animation:blink 1.06s steps(1) infinite}.cm-animate-fat-cursor{width:auto;border:0;animation:blink 1.06s steps(1) infinite;background-color:#7e7}@keyframes blink{50%{background-color:transparent}}.cm-tab{display:inline-block;text-decoration:inherit}.CodeMirror-rulers{position:absolute;left:0;right:0;top:-50px;bottom:0;overflow:hidden}.CodeMirror-ruler{border-left:1px solid #ccc;top:0;bottom:0;position:absolute}.cm-s-default .cm-header{color:blue}.cm-s-default .cm-quote{color:#090}.cm-negative{color:#d44}.cm-positive{color:#292}.cm-header,.cm-strong{font-weight:bold}.cm-em{font-style:italic}.cm-link{text-decoration:underline}.cm-strikethrough{text-decoration:line-through}.cm-s-default .cm-keyword{color:#708}.cm-s-default .cm-atom{color:#219}.cm-s-default .cm-number{color:#164}.cm-s-default .cm-def{color:blue}.cm-s-default .cm-variable-2{color:#05a}.cm-s-default .cm-variable-3,.cm-s-default .cm-type{color:#085}.cm-s-default .cm-comment{color:#a50}.cm-s-default .cm-string{color:#a11}.cm-s-default .cm-string-2{color:#f50}.cm-s-default .cm-meta{color:#555}.cm-s-default .cm-qualifier{color:#555}.cm-s-default .cm-builtin{color:#30a}.cm-s-default .cm-bracket{color:#997}.cm-s-default .cm-tag{color:#170}.cm-s-default .cm-attribute{color:#00c}.cm-s-default .cm-hr{color:#999}.cm-s-default .cm-link{color:#00c}.cm-s-default .cm-error{color:red}.cm-invalidchar{color:red}.CodeMirror-composing{border-bottom:2px solid}div.CodeMirror span.CodeMirror-matchingbracket{color:#0b0}div.CodeMirror span.CodeMirror-nonmatchingbracket{color:#a22}.CodeMirror-matchingtag{background:rgba(255, 150, 0, 0.3)}.CodeMirror-activeline-background{background:#e8f2ff}.CodeMirror{position:relative;overflow:hidden;background:white}.CodeMirror-scroll{overflow:scroll !important;margin-bottom:-50px;margin-right:-50px;padding-bottom:50px;height:100%;outline:none;position:relative}.CodeMirror-sizer{position:relative;border-right:50px solid transparent}.CodeMirror-vscrollbar,.CodeMirror-hscrollbar,.CodeMirror-scrollbar-filler,.CodeMirror-gutter-filler{position:absolute;z-index:6;display:none;outline:none}.CodeMirror-vscrollbar{right:0;top:0;overflow-x:hidden;overflow-y:scroll}.CodeMirror-hscrollbar{bottom:0;left:0;overflow-y:hidden;overflow-x:scroll}.CodeMirror-scrollbar-filler{right:0;bottom:0}.CodeMirror-gutter-filler{left:0;bottom:0}.CodeMirror-gutters{position:absolute;left:0;top:0;min-height:100%;z-index:3}.CodeMirror-gutter{white-space:normal;height:100%;display:inline-block;vertical-align:top;margin-bottom:-50px}.CodeMirror-gutter-wrapper{position:absolute;z-index:4;background:none !important;border:none !important}.CodeMirror-gutter-background{position:absolute;top:0;bottom:0;z-index:4}.CodeMirror-gutter-elt{position:absolute;cursor:default;z-index:4}.CodeMirror-gutter-wrapper ::selection{background-color:transparent}.CodeMirror-gutter-wrapper ::-moz-selection{background-color:transparent}.CodeMirror-lines{cursor:text;min-height:1px}.CodeMirror pre.CodeMirror-line,.CodeMirror pre.CodeMirror-line-like{border-radius:0;border-width:0;background:transparent;font-family:inherit;font-size:inherit;margin:0;white-space:pre;word-wrap:normal;line-height:inherit;color:inherit;z-index:2;position:relative;overflow:visible;-webkit-tap-highlight-color:transparent;font-variant-ligatures:contextual}.CodeMirror-wrap pre.CodeMirror-line,.CodeMirror-wrap pre.CodeMirror-line-like{word-wrap:break-word;white-space:pre-wrap;word-break:normal}.CodeMirror-linebackground{position:absolute;left:0;right:0;top:0;bottom:0;z-index:0}.CodeMirror-linewidget{position:relative;z-index:2;padding:.1px}.CodeMirror-rtl pre{direction:rtl}.CodeMirror-code{outline:none}.CodeMirror-scroll,.CodeMirror-sizer,.CodeMirror-gutter,.CodeMirror-gutters,.CodeMirror-linenumber{box-sizing:content-box}.CodeMirror-measure{position:absolute;width:100%;height:0;overflow:hidden;visibility:hidden}.CodeMirror-cursor{position:absolute;pointer-events:none}.CodeMirror-measure pre{position:static}div.CodeMirror-cursors{visibility:hidden;position:relative;z-index:3}div.CodeMirror-dragcursors{visibility:visible}.CodeMirror-focused div.CodeMirror-cursors{visibility:visible}.CodeMirror-selected{background:#d9d9d9}.CodeMirror-focused .CodeMirror-selected{background:#d7d4f0}.CodeMirror-crosshair{cursor:crosshair}.CodeMirror-line::selection,.CodeMirror-line>span::selection,.CodeMirror-line>span>span::selection{background:#d7d4f0}.CodeMirror-line::-moz-selection,.CodeMirror-line>span::-moz-selection,.CodeMirror-line>span>span::-moz-selection{background:#d7d4f0}.cm-searching{background-color:#ffa;background-color:rgba(255, 255, 0, 0.4)}.cm-force-border{padding-right:.1px}@media print{.CodeMirror div.CodeMirror-cursors{visibility:hidden}}.cm-tab-wrap-hack:after{content:""}span.CodeMirror-selectedtext{background:none}.CodeMirror-dialog{position:absolute;left:0;right:0;background:inherit;z-index:15;padding:.1em .8em;overflow:hidden;color:inherit}.CodeMirror-dialog-top{border-bottom:1px solid #eee;top:0}.CodeMirror-dialog-bottom{border-top:1px solid #eee;bottom:0}.CodeMirror-dialog input{border:none;outline:none;background:transparent;width:20em;color:inherit;font-family:monospace}.CodeMirror-dialog button{font-size:70%}.CodeMirror-merge{position:relative;border:1px solid #ddd;white-space:pre}.CodeMirror-merge,.CodeMirror-merge .CodeMirror{height:350px}.CodeMirror-merge-2pane .CodeMirror-merge-pane{width:47%}.CodeMirror-merge-2pane .CodeMirror-merge-gap{width:6%}.CodeMirror-merge-3pane .CodeMirror-merge-pane{width:31%}.CodeMirror-merge-3pane .CodeMirror-merge-gap{width:3.5%}.CodeMirror-merge-pane{display:inline-block;white-space:normal;vertical-align:top}.CodeMirror-merge-pane-rightmost{position:absolute;right:0px;z-index:1}.CodeMirror-merge-gap{z-index:2;display:inline-block;height:100%;box-sizing:border-box;overflow:hidden;border-left:1px solid #ddd;border-right:1px solid #ddd;position:relative;background:#f8f8f8}.CodeMirror-merge-scrolllock-wrap{position:absolute;bottom:0;left:50%}.CodeMirror-merge-scrolllock{position:relative;left:-50%;cursor:pointer;color:#555;line-height:1}.CodeMirror-merge-scrolllock:after{content:"⇛  ⇚"}.CodeMirror-merge-scrolllock.CodeMirror-merge-scrolllock-enabled:after{content:"⇛⇚"}.CodeMirror-merge-copybuttons-left,.CodeMirror-merge-copybuttons-right{position:absolute;left:0;top:0;right:0;bottom:0;line-height:1}.CodeMirror-merge-copy{position:absolute;cursor:pointer;color:#44c;z-index:3}.CodeMirror-merge-copy-reverse{position:absolute;cursor:pointer;color:#44c}.CodeMirror-merge-copybuttons-left .CodeMirror-merge-copy{left:2px}.CodeMirror-merge-copybuttons-right .CodeMirror-merge-copy{right:2px}.CodeMirror-merge-r-inserted,.CodeMirror-merge-l-inserted{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAYAAACddGYaAAAAGUlEQVQI12MwuCXy3+CWyH8GBgYGJgYkAABZbAQ9ELXurwAAAABJRU5ErkJggg==);background-position:bottom left;background-repeat:repeat-x}.CodeMirror-merge-r-deleted,.CodeMirror-merge-l-deleted{background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAACCAYAAACddGYaAAAAGUlEQVQI12M4Kyb2/6yY2H8GBgYGJgYkAABURgPz6Ks7wQAAAABJRU5ErkJggg==);background-position:bottom left;background-repeat:repeat-x}.CodeMirror-merge-r-chunk{background:#ffffe0}.CodeMirror-merge-r-chunk-start{border-top:1px solid #ee8}.CodeMirror-merge-r-chunk-end{border-bottom:1px solid #ee8}.CodeMirror-merge-r-connect{fill:#ffffe0;stroke:#ee8;stroke-width:1px}.CodeMirror-merge-l-chunk{background:#eef}.CodeMirror-merge-l-chunk-start{border-top:1px solid #88e}.CodeMirror-merge-l-chunk-end{border-bottom:1px solid #88e}.CodeMirror-merge-l-connect{fill:#eef;stroke:#88e;stroke-width:1px}.CodeMirror-merge-l-chunk.CodeMirror-merge-r-chunk{background:#dfd}.CodeMirror-merge-l-chunk-start.CodeMirror-merge-r-chunk-start{border-top:1px solid #4e4}.CodeMirror-merge-l-chunk-end.CodeMirror-merge-r-chunk-end{border-bottom:1px solid #4e4}.CodeMirror-merge-collapsed-widget:before{content:"(...)"}.CodeMirror-merge-collapsed-widget{cursor:pointer;color:#88b;background:#eef;border:1px solid #ddf;font-size:90%;padding:0 3px;border-radius:4px}.CodeMirror-merge-collapsed-line .CodeMirror-gutter-elt{display:none}/*!
 * GitHub Light v0.4.2
 * Copyright (c) 2012 - 2017 GitHub, Inc.
 * Licensed under MIT (https://github.com/primer/github-syntax-theme-generator/blob/master/LICENSE)
 */.cm-s-github-light.CodeMirror{background:var(--color-codemirror-bg);color:var(--color-codemirror-text)}.cm-s-github-light .CodeMirror-gutters{background:var(--color-codemirror-gutters-bg);border-right-width:0}.cm-s-github-light .CodeMirror-guttermarker{color:var(--color-codemirror-guttermarker-text)}.cm-s-github-light .CodeMirror-guttermarker-subtle{color:var(--color-codemirror-guttermarker-subtle-text)}.cm-s-github-light .CodeMirror-scrollbar-filler,.cm-s-github-light .CodeMirror-gutter-filler{background-color:transparent}.cm-s-github-light .CodeMirror-linenumber{color:var(--color-codemirror-linenumber-text);padding:0 16px 0 16px}.cm-s-github-light .CodeMirror-cursor{border-left:1px solid var(--color-codemirror-cursor)}.cm-s-github-light.CodeMirror-focused .CodeMirror-selected,.cm-s-github-light .CodeMirror-line::selection,.cm-s-github-light .CodeMirror-line>span::selection,.cm-s-github-light .CodeMirror-line>span>span::selection{background:var(--color-codemirror-selection-bg, #d7d4f0)}.cm-s-github-light .CodeMirror-line::-moz-selection,.cm-s-github-light .CodeMirror-line>span::-moz-selection,.cm-s-github-light .CodeMirror-line>span>span::-moz-selection{background:var(--color-codemirror-selection-bg, #d7d4f0)}.cm-s-github-light .CodeMirror-activeline-background{background:var(--color-codemirror-activeline-bg)}.cm-s-github-light .CodeMirror-matchingbracket{text-decoration:underline;color:var(--color-codemirror-matchingbracket-text) !important}.cm-s-github-light .CodeMirror-lines{font-family:"SFMono-Regular",Consolas,"Liberation Mono",Menlo,Courier,monospace;font-size:12px;background:var(--color-codemirror-lines-bg);line-height:1.5}.react-code-view-edit .CodeMirror,.react-code-view-edit .CodeMirror-scroll{display:flex;flex-direction:column;flex:1 1 auto}.react-code-view-edit .cm-s-github-light .CodeMirror-lines{line-height:20px;font-family:"ui-monospace","SFMono-Regular","SF Mono",Menlo,Consolas,Liberation Mono,monospace;padding-top:8px}.react-code-view-edit .cm-s-github-light .CodeMirror-line,.react-code-view-edit .cm-s-github-light .CodeMirror-placeholder{padding-left:16px}.cm-s-github-light .cm-comment{color:var(--color-codemirror-syntax-comment)}.cm-s-github-light .cm-constant{color:var(--color-codemirror-syntax-constant)}.cm-s-github-light .cm-entity{font-weight:normal;font-style:normal;text-decoration:none;color:var(--color-codemirror-syntax-entity)}.cm-s-github-light .cm-keyword{font-weight:normal;font-style:normal;text-decoration:none;color:var(--color-codemirror-syntax-keyword)}.cm-s-github-light .cm-storage{color:var(--color-codemirror-syntax-storage)}.cm-s-github-light .cm-string{font-weight:normal;font-style:normal;text-decoration:none;color:var(--color-codemirror-syntax-string)}.cm-s-github-light .cm-support{font-weight:normal;font-style:normal;text-decoration:none;color:var(--color-codemirror-syntax-support)}.cm-s-github-light .cm-variable{font-weight:normal;font-style:normal;text-decoration:none;color:var(--color-codemirror-syntax-variable)}details-dialog{position:fixed;margin:10vh auto;top:0;left:50%;transform:translateX(-50%);z-index:999;max-height:80vh;max-width:90vw;width:448px;overflow:auto}.user-select-contain{-webkit-user-select:contain;user-select:contain}.ajax-pagination-form .ajax-pagination-btn{width:100%;padding:6px;margin-top:20px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-accent, var(--color-accent-fg));background:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.ajax-pagination-form .ajax-pagination-btn:hover,.ajax-pagination-form .ajax-pagination-btn:focus{color:var(--fgColor-accent, var(--color-accent-fg));background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.ajax-pagination-form.loading .ajax-pagination-btn{text-indent:-3000px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));background-image:url("/images/spinners/octocat-spinner-16px-EAF2F5.gif");background-repeat:no-repeat;background-position:center center;border-color:var(--borderColor-default, var(--color-border-default))}@media only screen and (-webkit-min-device-pixel-ratio: 2),only screen and (-moz-min-device-pixel-ratio: 2),only screen and (min-device-pixel-ratio: 2),only screen and (min-resolution: 192dpi),only screen and (min-resolution: 2dppx){.ajax-pagination-form.loading .ajax-pagination-btn{background-image:url("/images/spinners/octocat-spinner-32-EAF2F5.gif");background-size:16px auto}}body.intent-mouse [role=button]:focus,body.intent-mouse [role=tabpanel][tabindex="0"]:focus,body.intent-mouse button:focus,body.intent-mouse summary:focus,body.intent-mouse a:focus{outline:none;box-shadow:none}body.intent-mouse [tabindex="0"]:focus,body.intent-mouse details-dialog:focus{outline:none}.CodeMirror{height:calc(100vh - 1px)}.file-editor-upload{height:100%}.issue-template-editor{height:100%}.file-editor-textarea{width:100%;padding:5px 4px;font:12px ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;resize:vertical;border:0;border-radius:0;outline:none}.container-preview .tabnav-tabs{margin:-5px 0 -5px -9px}.container-preview .tabnav-tabs .tabnav-tab{padding:12px 16px;border-radius:0}.container-preview .tabnav-tabs>.selected:first-child{border-top-left-radius:6px}.container-preview .tabnav-tabs .selected{font-weight:var(--base-text-weight-semibold, 600)}.container-preview.template-editor .commit-create,.container-preview.template-editor .file-actions{display:block}.container-preview.template-editor .show-code,.container-preview.template-editor .commit-preview,.container-preview.template-editor .loading-preview-msg,.container-preview.template-editor .no-changes-preview-msg,.container-preview.template-editor .error-preview-msg{display:none !important}.container-preview.render-editor .commit-create,.container-preview.render-editor .file-actions{display:block}.container-preview.render-editor .template-editor,.container-preview.render-editor .show-code,.container-preview.render-editor .commit-preview,.container-preview.render-editor .loading-preview-msg,.container-preview.render-editor .no-changes-preview-msg,.container-preview.render-editor .error-preview-msg{display:none !important}.container-preview.show-code .commit-create,.container-preview.show-code .file-actions{display:block}.container-preview.show-code .template-editor,.container-preview.show-code .render-editor,.container-preview.show-code .commit-preview,.container-preview.show-code .loading-preview-msg,.container-preview.show-code .no-changes-preview-msg,.container-preview.show-code .error-preview-msg{display:none !important}.container-preview:not(.show-code) .commit-create,.container-preview:not(.show-code) .file-actions{display:none !important}.container-preview.loading-preview .loading-preview-msg{display:block}.container-preview.loading-preview .template-editor,.container-preview.loading-preview .render-editor,.container-preview.loading-preview .no-changes-preview-msg,.container-preview.loading-preview .error-preview-msg,.container-preview.loading-preview .commit-preview{display:none !important}.container-preview.show-preview .commit-preview{display:block}.container-preview.show-preview .template-editor,.container-preview.show-preview .render-editor,.container-preview.show-preview .loading-preview-msg,.container-preview.show-preview .no-changes-preview-msg,.container-preview.show-preview .error-preview-msg{display:none !important}.container-preview.no-changes-preview .no-changes-preview-msg{display:block}.container-preview.no-changes-preview .template-editor,.container-preview.no-changes-preview .render-editor,.container-preview.no-changes-preview .loading-preview-msg,.container-preview.no-changes-preview .error-preview-msg,.container-preview.no-changes-preview .commit-preview{display:none !important}.container-preview.error-preview .error-preview-msg{display:block}.container-preview.error-preview .template-editor,.container-preview.error-preview .render-editor,.container-preview.error-preview .loading-preview-msg,.container-preview.error-preview .no-changes-preview-msg,.container-preview.error-preview .commit-preview{display:none !important}.container-preview p.preview-msg{padding:30px;font-size:16px}.CodeMirror-merge-header{height:30px}.CodeMirror-merge-header .CodeMirror-merge-pane{height:30px;line-height:30px}.cm-s-github-light .merge-gutter{width:14px}.conflict-background+.CodeMirror-gutter-wrapper .CodeMirror-linenumber{background-color:var(--bgColor-attention-muted, var(--color-attention-subtle))}.form-group .edit-action{opacity:.6}.form-group .form-field-hover{background-color:none;border:1px solid var(--borderColor-default, var(--color-border-default))}.form-group:hover .edit-action{cursor:pointer;opacity:.7}.form-group:hover .form-field-hover{cursor:pointer;border:1px solid var(--borderColor-default, var(--color-border-default))}.placeholder-box{border:1px solid var(--borderColor-default, var(--color-border-default))}.template-previews{max-width:768px}.template-previews .Box .expand-group{display:none;height:0}.template-previews .Box .dismiss-preview-button{display:none}.template-previews .Box.expand-preview .expand-group{display:block;height:100%;transition:height 3s}.template-previews .Box.expand-preview .preview-button{display:none}.template-previews .Box.expand-preview .dismiss-preview-button{display:inline}.template-previews .discussion-sidebar-heading{font-size:14px;color:var(--fgColor-neutral, var(--color-neutral-emphasis))}.template-previews .discussion-sidebar-heading:hover{color:var(--fgColor-accent, var(--color-accent-emphasis))}.edit-labels{display:none}.preview-section{display:block}.edit-section{display:none}.Box .section-focus .preview-section{display:none}.Box .section-focus .edit-section{display:block}.commit-create .CodeMirror{padding-top:8px}auto-complete,details-dialog,details-menu,file-attachment,filter-input,remote-input,tab-container,text-expander,turbo-frame,[data-catalyst]{display:block}[data-catalyst-inline]{display:inline}[data-catalyst-grid]{display:grid}.Details--on .Details-content--shown{display:none !important}.Details:not(.Details--on) .Details-content--hidden{display:none !important}.Details:not(.Details--on) .Details-content--hidden-not-important{display:none}.Details-element[open]>summary .Details-content--closed{display:none !important}.Details-element:not([open])>summary .Details-content--open{display:none !important}g-emoji{display:inline-block;min-width:1ch;font-family:"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol";font-size:1em;font-style:normal !important;font-weight:var(--base-text-weight-normal, 400);line-height:1;vertical-align:-0.075em}g-emoji img{width:1em;height:1em}.emoji-icon{display:inline-block;width:20px;height:20px;vertical-align:middle;background-repeat:no-repeat;background-size:20px 20px}.emoji-result{display:inline-block;height:20px;font-size:16px;font-weight:var(--base-text-weight-normal, 400);vertical-align:middle}.gollum-editor .comment-form-head.tabnav{border:1px solid var(--borderColor-muted, var(--color-border-muted))}.gollum-editor .gollum-editor-body{height:390px;resize:vertical}.active .gollum-editor-function-buttons{display:block !important}.auth-form{width:340px;margin:0 auto}.auth-form .form-group.warn .warning,.auth-form .form-group.warn .error,.auth-form .form-group.errored .warning,.auth-form .form-group.errored .error{max-width:274px}.auth-form-wide{width:440px;margin:0 auto}.auth-form-wide .form-group.warn .warning,.auth-form-wide .form-group.warn .error,.auth-form-wide .form-group.errored .warning,.auth-form-wide .form-group.errored .error{max-width:274px}.auth-form-header{padding:8px 16px;margin:0;color:#fff;text-shadow:0 -1px 0 rgba(0,0,0,.3);background-color:#829aa8;border:1px solid #768995;border-radius:6px 6px 0 0}.auth-form-header h1{font-size:16px}.auth-form-header h1 a{color:#fff}.auth-form-header .octicon{position:absolute;top:10px;right:20px;color:rgba(0,0,0,.4);text-shadow:0 1px 0 rgba(255,255,255,.1)}.auth-form-header .Overlay .octicon{position:static}.inactive-user-avatar{filter:grayscale(1)}.auth-divider{display:flex;flex-basis:100%;align-items:center}.auth-divider::before,.auth-divider::after{position:relative;display:inline-block;width:50%;height:1px;vertical-align:middle;content:"";background-color:var(--borderColor-default, var(--color-border-default))}.auth-divider::before{right:.5em}.auth-divider::after{left:.5em}.auth-form-message{max-height:140px;padding:16px 16px 8px;overflow-y:scroll;border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.auth-form-message ol,.auth-form-message ul{padding-left:inherit;margin-bottom:inherit}.auth-form-body{padding:16px;font-size:14px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:1px solid var(--borderColor-muted, var(--color-border-muted));border-top:0;border-radius:0 0 6px 6px}.auth-form-body .input-block{margin-top:4px;margin-bottom:16px}.auth-form-body p{margin-bottom:0}.auth-form-body ol,.auth-form-body ul{padding-left:inherit;margin-bottom:inherit}.two-factor-help{position:relative;padding:8px 8px 8px 32px;margin:60px 0 auto auto;border:1px solid var(--borderColor-muted, var(--color-border-muted));border-radius:6px}.two-factor-help h4{margin-top:0;margin-bottom:4px}.two-factor-help .octicon-device-mobile,.two-factor-help .octicon-key,.two-factor-help .octicon-shield-lock,.two-factor-help .octicon-circle-slash{position:absolute;top:10px;left:10px}.sms-send-code-spinner{position:relative;bottom:2px;display:none;vertical-align:bottom}.loading .sms-send-code-spinner{display:inline}.auth-form-body .webauthn-form-body{padding:0}.webauthn-form-body{padding:32px 32px 16px;text-align:center}.webauthn-form-body button{margin-top:16px}.flash.sms-error,.flash.sms-success{display:none;margin:0 0 8px}.is-sent .sms-success{display:block}.is-sent .sms-error{display:none}.is-not-sent .sms-success{display:none}.is-not-sent .sms-error{display:block}.session-authentication{background-color:var(--bgColor-default, var(--color-canvas-default))}.session-authentication .header-logged-out{background-color:transparent;border-bottom:0}.session-authentication .header-logo{color:var(--fgColor-default, var(--color-fg-default))}.session-authentication .flash{padding:16px 16px;margin:0 auto;margin-bottom:8px;font-size:14px;border-style:solid;border-width:1px;border-radius:6px}.session-authentication .flash .container{width:auto}.session-authentication .flash .flash-close{height:40px}.session-authentication .flash.flash-banner{width:100%;border-top:0;border-right:0;border-left:0;border-radius:0}.session-authentication .auth-form label{display:block;margin-bottom:8px;font-weight:var(--base-text-weight-normal, 400);text-align:left}.session-authentication .auth-form .btn{margin-top:16px}.session-authentication .auth-form .webauthn-message{margin-bottom:0}.session-authentication .label-link{float:right;font-size:12px}.session-authentication .auth-form-header{margin-bottom:16px;color:var(--fgColor-default, var(--color-fg-default));text-align:center;text-shadow:none;background-color:transparent;border:0}.session-authentication .auth-form-header h1{font-size:24px;font-weight:var(--base-text-weight-light, 300);letter-spacing:-0.5px}.session-authentication .auth-form-body{border-top:1px solid var(--borderColor-muted, var(--color-border-muted));border-radius:6px}.session-authentication .auth-form-body.webauthn-form-body{padding:16px}.session-authentication .login-callout{padding:16px 16px;text-align:center;border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.session-authentication .two-factor-help{padding:0 0 0 16px;margin-top:16px;border:0}.session-authentication .two-factor-help .octicon-device-mobile,.session-authentication .two-factor-help .octicon-key,.session-authentication .two-factor-help .octicon-shield-lock,.session-authentication .two-factor-help .octicon-circle-slash{top:4px;left:0}.session-authentication.enterprise .header-logged-out{padding:48px 0 24px;background-color:transparent}.session-authentication.hosted .header-logged-out{padding:40px 0 16px;background-color:transparent}.session-authentication .notification-shelf{display:none}.two-factor-recovery-modal-prompt.fit-in-box .Button-content,.two-factor-recovery-modal-prompt.fit-in-box .Button-label{display:block;width:274px;text-align:left;white-space:normal}.switch-account-popover-body{width:auto;min-width:250px;max-width:350px}.switch-account-popover-body::before,.switch-account-popover-body::after{display:none}.switch-account-popover-row{width:100%;padding:0;background-color:transparent}.switch-account-popover-row:hover{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));cursor:pointer;background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}@media(prefers-reduced-motion: no-preference){.Header-backdrop,.HeaderMenu--logged-out,.HeaderMenu-link,.HeaderMenu-toggle-bar,.HeaderMenu-icon,.HeaderMenu-dropdown,.HeaderMenu-external-icon{transition-timing-function:cubic-bezier(0.16, 1, 0.3, 1);transition-duration:500ms;transition-property:opacity,transform}}.Header-old{z-index:32;padding-top:12px;padding-bottom:12px;color:#fff;background-color:var(--header-bgColor, var(--color-header-bg))}.server-stats+.Header-old{box-shadow:inset 0 1px 0 rgba(255,255,255,.075)}.Header-old .dropdown-menu{width:300px}.Header-old .notification-indicator:hover::after{content:none}@media(min-width: 1012px){.Header-old .notification-indicator:hover::after{content:attr(aria-label)}}.HeaderMenu-toggle-bar{width:22px;height:2px;background-color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.HeaderMenu-toggle-bar:nth-of-type(1){transform-origin:bottom right}.HeaderMenu-toggle-bar:nth-of-type(3){transform-origin:top right}.open .HeaderMenu-toggle-bar:nth-of-type(1){transform:rotate(-45deg) translateY(-3px)}.open .HeaderMenu-toggle-bar:nth-of-type(2){opacity:0;transform:scale(0)}.open .HeaderMenu-toggle-bar:nth-of-type(3){transform:rotate(45deg) translateY(3px)}@media(max-width: 1011px){body:has(.header-logged-out.open){height:100%;overflow:hidden}.Header-backdrop{visibility:hidden;background:var(--overlay-backdrop-bgColor, var(--color-primer-canvas-backdrop));opacity:0}.open .Header-backdrop{visibility:visible;opacity:1;-webkit-backdrop-filter:blur(3px);backdrop-filter:blur(3px)}}.HeaderMenu--logged-out{--header-menu-shadow: 0 5px 30px rgba(27, 31, 35, 0.1), 0 0 1px rgba(27, 31, 35, 0.4), 0 1px 2px rgba(27, 31, 35, 0.15);contain:layout;z-index:100;width:100%;pointer-events:none}@media(prefers-color-scheme: dark){.HeaderMenu--logged-out{--header-menu-shadow: 0 0 1px #959da5}}@media(min-width: 1012px){.HeaderMenu--logged-out{width:auto;padding:0 !important;transition:none}}.HeaderMenu--logged-out .HeaderMenu-link--sign-in:focus{outline-offset:4px}@media(max-width: 1011px){.HeaderMenu--logged-out{position:fixed;display:flex;height:100%;padding-right:0 !important;transform-origin:top right}.header-logged-out:not(.open) .HeaderMenu--logged-out{position:absolute;visibility:hidden;opacity:0;transform:scale(0.9) translateY(-24px)}.HeaderMenu--logged-out .HeaderMenu-link.HeaderMenu-link--sign-up,.HeaderMenu--logged-out .HeaderMenu-link.HeaderMenu-link--sign-up:hover,.HeaderMenu--logged-out .HeaderMenu-link.HeaderMenu-link--sign-in,.HeaderMenu--logged-out .HeaderMenu-link.HeaderMenu-link--sign-in:hover{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis)) !important;background-color:var(--bgColor-emphasis, var(--color-neutral-emphasis-plus));border-color:var(--bgColor-emphasis, var(--color-neutral-emphasis-plus)) !important;opacity:1}}.HeaderMenu--logged-out .header-menu-wrapper{width:100%;height:100%;overflow:auto;pointer-events:auto;background-color:var(--bgColor-default, var(--color-canvas-default));box-shadow:var(--header-menu-shadow)}@media(min-width: 544px){.HeaderMenu--logged-out .header-menu-wrapper{width:320px}}@media(max-width: 1011px){.HeaderMenu--logged-out .header-menu-wrapper{border-top-right-radius:0 !important;border-bottom-right-radius:0 !important}}@media(min-width: 1012px){.HeaderMenu--logged-out .header-menu-wrapper{width:100%;overflow:visible;background-color:transparent;box-shadow:none}}@media(max-width: 1011px){.HeaderMenu--logged-out .header-search{margin-right:0 !important}.HeaderMenu--logged-out .HeaderMenu-link{font-weight:var(--base-text-weight-semibold, 600)}}.HeaderMenu--logged-out .jump-to-suggestions{top:100%}.HeaderMenu--logged-out .header-search-key-slash{margin-right:8px !important}@media(max-width: 1012px){.HeaderMenu--logged-out .header-search-key-slash{display:none}}.HeaderMenu--logged-out .dropdown-menu{position:static;width:auto;border:0 solid transparent;box-shadow:none}.HeaderMenu--logged-out .dropdown-menu::before,.HeaderMenu--logged-out .dropdown-menu::after{display:none}@media(min-width: 1012px){.HeaderMenu--logged-out .dropdown-menu{position:absolute;width:300px;border:0;box-shadow:var(--header-menu-shadow)}.HeaderMenu--logged-out .dropdown-menu::before,.HeaderMenu--logged-out .dropdown-menu::after{content:""}.HeaderMenu--logged-out .dropdown-menu.dropdown-menu-wide{width:500px}}.HeaderMenu--logged-out .dropdown-menu-s{transform:none}@media(min-width: 1012px){.HeaderMenu--logged-out .dropdown-menu-s{transform:translateX(50%)}}.HeaderMenu--logged-out .header-search{width:auto;border-top:0}@media(min-width: 1012px){.HeaderMenu--logged-out .header-search{width:240px}}.HeaderMenu--logged-out .header-search-wrapper{border-color:var(--borderColor-muted, var(--color-border-muted))}@media(min-width: 1012px){.HeaderMenu--logged-out .header-search-wrapper{border-color:var(--headerSearch-borderColor, var(--color-header-search-border))}}@media(max-width: 1012px){.HeaderMenu--logged-out .header-search-wrapper{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}}@media(min-width: 1012px){.HeaderMenu--logged-out .header-search-input{padding-top:8px;padding-bottom:8px;font-size:14px;-webkit-appearance:none}}.HeaderMenu--logged-out .header-search-input::placeholder{color:var(--color-scale-gray-4)}.HeaderMenu-link{color:var(--fgColor-default, var(--color-fg-default));white-space:nowrap;background:transparent}.HeaderMenu-link:hover{color:var(--fgColor-default, var(--color-fg-default));opacity:.75}@media(min-width: 1012px){.HeaderMenu-link{color:#fff}.HeaderMenu-link:hover{color:#fff;opacity:.75}}.HeaderMenu-item .HeaderMenu-dropdown{visibility:hidden;opacity:0;transform:scale(0.99) translateY(-0.7em);transform-origin:top}.HeaderMenu-item:hover .HeaderMenu-dropdown,.HeaderMenu-item.open .HeaderMenu-dropdown{visibility:visible;opacity:1;transform:scale(1) translateY(0)}@media(min-width: 1012px){.HeaderMenu-item:hover .HeaderMenu-icon,.HeaderMenu-item.open .HeaderMenu-icon{transform:translateY(2px)}}@media(max-width: 1011px){.HeaderMenu-item .HeaderMenu-dropdown{background-color:transparent}.HeaderMenu-item .HeaderMenu-icon{transform:scale(1.2)}.HeaderMenu-item:not(.open) .HeaderMenu-dropdown{position:fixed !important}.HeaderMenu-item:not(.open) .HeaderMenu-icon{transform:rotate(-90deg) scale(1.2)}.HeaderMenu-item .HeaderMenu-link{font-size:20px}}.HeaderMenu-dropdown-link:not(:hover):not(:focus) .HeaderMenu-external-icon{opacity:0;transform:translateX(-0.5em)}.header-logo-invertocat{margin:-1px 16px -1px -2px;color:#fff;white-space:nowrap}.header-logo-invertocat .octicon-mark-github{float:left}.header-logo-invertocat:hover{color:#fff;text-decoration:none}.notification-indicator .mail-status{position:absolute;top:-6px;left:6px;display:none;width:14px;height:14px;color:#fff;background-image:linear-gradient(#54a3ff, #006eed);background-clip:padding-box;border:2px solid var(--header-bgColor, var(--color-header-bg));border-radius:50%}.notification-indicator .mail-status.unread{display:inline-block}.notification-indicator:hover .mail-status{text-decoration:none;background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.header-nav-current-user{padding-bottom:0;font-size:inherit}.header-nav-current-user .css-truncate-target{max-width:100%}.header-nav-current-user .user-profile-link{color:var(--fgColor-default, var(--color-fg-default))}.unread-indicator{position:absolute;top:0;left:13px;z-index:2;width:14px;height:14px;color:#fff;background-image:linear-gradient(#54a3ff, #006eed);background-clip:padding-box;border:2px solid var(--header-bgColor, var(--color-header-bg));border-radius:50%}.unread-indicator--small{width:10px;height:10px;border:0}.unread-indicator-container .unread-indicator{top:9px;right:10px;left:inherit;width:10px;height:10px;border:0}.header-search-wrapper{display:table;width:100%;max-width:100%;padding:0;font-size:inherit;font-weight:var(--base-text-weight-normal, 400);vertical-align:middle;background-color:var(--headerSearch-bgColor, var(--color-header-search-bg));border:1px solid var(--headerSearch-borderColor, var(--color-header-search-border));box-shadow:none}@media(min-width: 1012px){.header-search-wrapper{color:var(--color-scale-white)}}.header-search-wrapper.header-search-wrapper-jump-to .header-search-scope{width:-moz-fit-content;width:fit-content}.header-search-wrapper.header-search-wrapper-jump-to.search-wrapper-suggestions-active{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));border-bottom-right-radius:0;border-bottom-left-radius:0}.header-search-wrapper .truncate-repo-scope{max-width:110px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.header-search-wrapper.focus{background-color:rgba(255,255,255,.175);box-shadow:none}.header-search-wrapper.focus .header-search-scope{color:var(--color-scale-white);background-color:rgba(255,255,255,.075);border-right-color:#282e34}.search-input.search-input-absolute{position:absolute;width:calc(100% - 180px)}.header-search-input{display:table-cell;width:100%;padding-top:0;padding-bottom:0;font-size:inherit;color:inherit;background:none;border:0;box-shadow:none}.header-search-input::placeholder{color:rgba(255,255,255,.75)}.header-search-input:focus{border:0;box-shadow:none}.header-search-input:focus~.header-search-key-slash{display:none !important}.header-search-input::-ms-clear{display:none}.header-search-button{display:table-cell;overflow:hidden;font-size:inherit;color:inherit;word-break:break-word;white-space:pre;background:none}.header-search-button.input-button:focus{border:0 !important}.header-search-button.input-button:focus~.header-search-key-slash{display:none !important}.header-search-button.input-button:focus-visible{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:-2px;box-shadow:none}.header-search-button::-ms-clear{display:none}.Header .header-search-button,.header-logged-out .header-search-button{font-size:14px}.Header .header-search-button .input-parsed-symbol,.header-logged-out .header-search-button .input-parsed-symbol{color:#58a6ff;background:#388bfd26;border-radius:3px;box-shadow:0 0 0 .8px #388bfd26}.Header .header-search-button .pl-c1,.header-logged-out .header-search-button .pl-c1{color:#58a6ff}.Header .header-search-button .pl-en,.header-logged-out .header-search-button .pl-en{color:#58a6ff}.header-search-button.placeholder{color:rgba(255,255,255,.75)}.header-search-scope{display:none;padding-right:8px;padding-left:8px;font-size:inherit;line-height:28px;color:rgba(255,255,255,.7);white-space:nowrap;vertical-align:middle;border-right:1px solid var(--borderColor-muted, var(--color-border-muted));border-right-color:#282e34;border-top-left-radius:6px;border-bottom-left-radius:6px}.header-search-scope:empty+.header-search-input{width:100%}.header-search-scope:hover{color:var(--color-scale-white);background-color:rgba(255,255,255,.12)}.scoped-search .header-search-wrapper{display:flex}.jump-to-field-active{color:var(--fgColor-default, var(--color-fg-default)) !important;background-color:var(--bgColor-default, var(--color-canvas-default))}.jump-to-field-active::placeholder{color:var(--fgColor-muted, var(--color-fg-muted)) !important}.jump-to-field-active~.header-search-key-slash{display:none}.jump-to-field-active.jump-to-dropdown-visible{border-bottom-right-radius:0;border-bottom-left-radius:0}.jump-to-suggestions{top:100%;left:0;z-index:35;width:100%;border-top-left-radius:0;border-top-right-radius:0;border-bottom-right-radius:6px;border-bottom-left-radius:6px;box-shadow:0 4px 10px rgba(0,0,0,.1)}.jump-to-suggestions-path{min-width:0;min-height:44px;color:var(--fgColor-default, var(--color-fg-default))}.jump-to-suggestions-path .jump-to-octicon{width:28px;color:var(--fgColor-muted, var(--color-fg-muted))}.jump-to-suggestions-path .jump-to-suggestion-name{max-width:none}.jump-to-suggestions-path mark{font-weight:var(--base-text-weight-semibold, 600);background-color:transparent}.jump-to-suggestions-results-container .navigation-item{border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.jump-to-suggestions-results-container .navigation-item:last-child{border-bottom:0}.jump-to-suggestions-results-container .d-on-nav-focus{display:none}.jump-to-suggestions-results-container [aria-selected=true] .jump-to-octicon,.jump-to-suggestions-results-container .navigation-focus .jump-to-octicon{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.jump-to-suggestions-results-container [aria-selected=true] .jump-to-suggestions-path,.jump-to-suggestions-results-container .navigation-focus .jump-to-suggestions-path{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.jump-to-suggestions-results-container [aria-selected=true] mark,.jump-to-suggestions-results-container .navigation-focus mark{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.jump-to-suggestions-results-container [aria-selected=true] .d-on-nav-focus,.jump-to-suggestions-results-container .navigation-focus .d-on-nav-focus{display:block}.header-search{max-width:100%;transition:.2s ease-in-out;transition-property:max-width,padding-bottom,padding-top}@media(min-width: 768px){.header-search{max-width:272px}}@media(min-width: 768px){.header-search:focus-within{max-width:544px}}@media(min-width: 768px){.header-search.fixed-width:focus-within{max-width:272px}}.HeaderMenu--logged-out .header-search{min-width:auto;margin-bottom:0 !important}@media(max-width: 1011px){.HeaderMenu--logged-out .header-search .header-search-input{min-height:40px}}.search-input{width:350px}@media only screen and (max-width: 768px){.search-input{width:100%}.search-input-container{margin-right:10px !important;margin-bottom:10px !important;margin-left:10px !important}}.search-input.expanded{flex:1}.search-with-dialog{height:32px;color:var(--color-scale-white);background-color:var(--header-bgColor, var(--color-header-bg));border:1px solid var(--headerSearch-borderColor, var(--color-header-search-border))}.search-with-dialog:hover{background-color:var(--headerSearch-bgColor, var(--color-header-search-bg))}.search-with-dialog .input-parsed-symbol{color:var(--fgColor-accent, var(--color-accent-fg));background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-radius:3px;box-shadow:0 0 0 .8px var(--bgColor-muted, var(--color-canvas-subtle))}.create-custom-scope-form{margin-top:-16px;margin-bottom:-16px}.query-builder-container{padding-top:10px;padding-right:0;padding-left:0}.query-builder-container .QueryBuilder-StyledInput{width:auto;margin-right:var(--base-size-12, 12px);margin-left:var(--base-size-12, 12px)}.query-builder-container .QueryBuilder-sectionTitle{margin-left:8px}.query-builder-container .QueryBuilder-ListItem-trailing{font-size:14px}.query-builder-container .ActionListItem{margin-right:8px;margin-left:8px}.search-feedback-prompt{padding-top:var(--base-size-12, 12px);padding-bottom:var(--base-size-12, 12px);border-top-color:var(--borderColor-muted, var(--color-action-list-item-inline-divider));border-top-style:solid;border-top-width:1px}.search-suggestions{top:-14px;left:-14px;z-index:35;width:calc(100% + 26px) !important;max-height:80vh;padding-top:var(--base-size-12, 12px);border-radius:var(--borderRadius-large, 12px)}.search-suggestions .header-search-input{overflow:hidden}.search-suggestions .octicon{pointer-events:none}.HeaderMenu--logged-out .search-suggestions{width:calc(100% - 16px) !important}@media screen and (max-width: 1012px){.HeaderMenu--logged-out .search-suggestions{top:0;left:0;width:100% !important}}@media screen and (max-width: 1012px){.HeaderMenu--logged-out .search-input{width:100%}}@media screen and (max-width: 1011px){.HeaderMenu--logged-out .search-input-container{margin-right:0 !important;margin-bottom:16px !important;margin-left:0 !important;color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default)) !important}}.dark-backdrop{top:0;right:0;bottom:0;left:0;z-index:32;width:120vw;height:120vh;margin-top:-24px;margin-left:-80px;background-color:var(--overlay-backdrop-bgColor, var(--color-primer-canvas-backdrop))}.Header-item--search{flex-grow:100}.search-query-builder .QueryBuilder-ListWrap{max-height:60vh;padding-right:0;padding-left:0;overflow-y:auto}.Header .search-input{flex:1;width:auto;max-width:350px}.Header .search-input.expanded{max-width:none}.app-banner{background-color:var(--header-bgColor, var(--color-header-bg))}.app-banner-title{color:var(--header-fgColor-logo, var(--color-header-logo))}.app-banner-text{color:var(--header-fgColor-default, var(--color-header-text))}.app-banner-icon{--icon-gradient-start-color: #171a1e;--icon-gradient-end-color: #060606;width:32px;height:32px;padding-top:6px;padding-left:6px;color:#fff;background:var(--icon-gradient-end-color);background:linear-gradient(var(--icon-gradient-start-color), var(--icon-gradient-end-color))}.app-banner-icon .octicon{width:20px;height:20px}.ActionList{padding:8px}.ActionList--full{padding:0}.ActionList--subGroup{padding:0}.ActionList--divided .ActionList-item-label::before{position:absolute;top:-6px;display:block;width:100%;height:1px;content:"";background:var(--borderColor-muted, var(--color-action-list-item-inline-divider))}.ActionList--divided .ActionList-item-descriptionWrap--inline::before{position:absolute;top:-6px;display:block;width:100%;height:1px;content:"";background:var(--borderColor-muted, var(--color-action-list-item-inline-divider))}.ActionList--divided .ActionList-item-descriptionWrap--inline .ActionList-item-label::before{content:unset}.ActionList--divided .ActionList-item--navActive .ActionList-item-label::before,.ActionList--divided .ActionList-item--navActive+.ActionList-item .ActionList-item-label::before{visibility:hidden}.ActionList-item:first-of-type .ActionList-item-label::before,.ActionList-sectionDivider+.ActionList-item .ActionList-item-label::before{visibility:hidden}.ActionList-item:first-of-type .ActionList-item-descriptionWrap--inline::before,.ActionList-sectionDivider+.ActionList-item .ActionList-item-descriptionWrap--inline::before{visibility:hidden}.ActionList--tree{--ActionList-tree-depth: 1}.ActionList--tree .ActionList-item--subItem>.ActionList-content{font-size:14px}.ActionList--tree .ActionList-item.ActionList-item--singleton .ActionList-content{padding-left:32px}.ActionList--tree .ActionList-item.ActionList-item--navActive:not(.ActionList-item--subItem) .ActionList-item-label{font-weight:var(--base-text-weight-normal, 400)}.ActionList--tree .ActionList-content[aria-expanded]+.ActionList--subGroup{position:relative}.ActionList--tree .ActionList-content[aria-expanded]+.ActionList--subGroup .ActionList-content{padding-left:calc(8px * var(--ActionList-tree-depth))}.ActionList--tree .ActionList-content[aria-expanded=true] .ActionList-item-collapseIcon{transition:transform 120ms linear;transform:rotate(0deg)}.ActionList--tree .ActionList-content[aria-expanded=true].ActionList-content--hasActiveSubItem>.ActionList-item-label{font-weight:var(--base-text-weight-normal, 400)}.ActionList--tree .ActionList-content[aria-expanded=false] .ActionList-item-collapseIcon{transition:transform 120ms linear;transform:rotate(-90deg)}.ActionList--tree .ActionList-content[aria-expanded=false].ActionList-content--hasActiveSubItem>.ActionList-item-label{font-weight:var(--base-text-weight-normal, 400)}.ActionList--tree .ActionList-item--hasSubItem .ActionList-item--subItem:not(.ActionList-item--hasSubItem) .ActionList-content>span:first-child{padding-left:24px}.ActionList--tree>[aria-level="1"].ActionList-item--hasSubItem>.ActionList--subGroup::before{position:absolute;left:16px;width:1px;height:100%;content:"";background:var(--borderColor-muted, var(--color-action-list-item-inline-divider))}.ActionList--tree .ActionList-item--hasSubItem:not([aria-level="1"])>.ActionList--subGroup::before{position:absolute;left:calc(8px * (var(--ActionList-tree-depth)) + 7px);width:1px;height:100%;content:"";background:var(--borderColor-muted, var(--color-action-list-item-inline-divider))}.ActionList-item{position:relative;list-style:none;background-color:transparent;border-radius:6px}.ActionList-item:hover,.ActionList-item:active{cursor:pointer}@media(hover: hover){.ActionList-item:not(.ActionList-item--hasSubItem):hover,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover{cursor:pointer;background-color:var(--control-transparent-bgColor-hover, var(--color-action-list-item-default-hover-bg))}.ActionList-item:not(.ActionList-item--hasSubItem):hover:not(.ActionList-item--navActive):not(:focus-visible),.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover:not(.ActionList-item--navActive):not(:focus-visible){outline:solid 1px transparent;outline-offset:-1px;box-shadow:inset 0 0 0 1px var(--control-transparent-borderColor-active, var(--color-action-list-item-default-active-border))}}.ActionList-item:not(.ActionList-item--hasSubItem):active,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:active{background:var(--control-transparent-bgColor-active, var(--color-action-list-item-default-active-bg))}.ActionList-item:not(.ActionList-item--hasSubItem):active:not(.ActionList-item--navActive),.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:active:not(.ActionList-item--navActive){outline:solid 1px transparent;outline-offset:-1px;box-shadow:inset 0 0 0 1px var(--control-transparent-borderColor-active, var(--color-action-list-item-default-active-border))}@media(hover: hover){.ActionList-item:not(.ActionList-item--hasSubItem):hover .ActionList-item-label::before,.ActionList-item:not(.ActionList-item--hasSubItem):hover+.ActionList-item .ActionList-item-label::before,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover .ActionList-item-label::before,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover+.ActionList-item .ActionList-item-label::before{visibility:hidden}.ActionList-item:not(.ActionList-item--hasSubItem):hover .ActionList-item-descriptionWrap--inline::before,.ActionList-item:not(.ActionList-item--hasSubItem):hover+.ActionList-item .ActionList-item-descriptionWrap--inline::before,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover .ActionList-item-descriptionWrap--inline::before,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover+.ActionList-item .ActionList-item-descriptionWrap--inline::before{visibility:hidden}}.ActionList-item:not(.ActionList-item--hasSubItem):active .ActionList-item-label::before,.ActionList-item:not(.ActionList-item--hasSubItem):active+.ActionList-item .ActionList-item-label::before,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:active .ActionList-item-label::before,.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:active+.ActionList-item .ActionList-item-label::before{visibility:hidden}.ActionList-item.ActionList-item--hasSubItem>.ActionList-content{z-index:1}@media(hover: hover){.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:hover{background-color:var(--control-transparent-bgColor-hover, var(--color-action-list-item-default-hover-bg))}}.ActionList-item.ActionList-item--hasSubItem>.ActionList-content:active{background-color:var(--control-transparent-bgColor-active, var(--color-action-list-item-default-active-bg))}.ActionList-item[hidden]+.ActionList-sectionDivider{display:none}.ActionList-item[aria-selected=true]{font-weight:var(--base-text-weight-normal, 400);background:var(--control-transparent-bgColor-selected, var(--color-action-list-item-default-selected-bg))}@media(hover: hover){.ActionList-item[aria-selected=true]:hover{background-color:var(--control-transparent-bgColor-hover, var(--color-action-list-item-default-hover-bg))}}.ActionList-item[aria-selected=true]::before,.ActionList-item[aria-selected=true]+.ActionList-item::before{visibility:hidden}.ActionList-item[aria-selected=true]::after{position:absolute;top:calc(50% - 12px);left:-4px;width:4px;height:24px;content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border-radius:6px}.ActionList-item.ActionList-item--navActive:not(.ActionList-item--subItem) .ActionList-item-label{font-weight:var(--base-text-weight-semibold, 600)}.ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger){background:var(--control-transparent-bgColor-selected, var(--color-action-list-item-default-selected-bg))}@media(hover: hover){.ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger):hover{background-color:var(--control-transparent-bgColor-hover, var(--color-action-list-item-default-hover-bg))}}.ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger)::before,.ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger)+.ActionList-item::before{visibility:hidden}.ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger)::after{position:absolute;top:calc(50% - 12px);left:-8px;width:4px;height:24px;content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border-radius:6px}.ActionList-item[aria-checked=true] .ActionList-item-multiSelectCheckmark,.ActionList-item[aria-selected=true] .ActionList-item-multiSelectCheckmark{visibility:visible;opacity:1;transition:visibility 0 linear 0,opacity 50ms}.ActionList-item[aria-checked=true] .ActionList-item-singleSelectCheckmark,.ActionList-item[aria-selected=true] .ActionList-item-singleSelectCheckmark{visibility:visible}@media screen and (prefers-reduced-motion: no-preference){.ActionList-item[aria-checked=true] .ActionList-item-singleSelectCheckmark,.ActionList-item[aria-selected=true] .ActionList-item-singleSelectCheckmark{animation:checkmarkIn 200ms cubic-bezier(0.11, 0, 0.5, 0) forwards}}.ActionList-item[aria-checked=true] .ActionList-item-multiSelectIcon .ActionList-item-multiSelectIconRect,.ActionList-item[aria-selected=true] .ActionList-item-multiSelectIcon .ActionList-item-multiSelectIconRect{fill:var(--fgColor-accent, var(--color-accent-fg));stroke:var(--fgColor-accent, var(--color-accent-fg));stroke-width:1px}.ActionList-item[aria-checked=true] .ActionList-item-multiSelectIcon .ActionList-item-multiSelectCheckmark,.ActionList-item[aria-selected=true] .ActionList-item-multiSelectIcon .ActionList-item-multiSelectCheckmark{fill:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.ActionList-item[aria-checked=false] .ActionList-item-multiSelectCheckmark,.ActionList-item[aria-selected=false] .ActionList-item-multiSelectCheckmark{visibility:hidden;opacity:0;transition:visibility 0 linear 50ms,opacity 50ms}.ActionList-item[aria-checked=false] .ActionList-item-singleSelectCheckmark,.ActionList-item[aria-selected=false] .ActionList-item-singleSelectCheckmark{visibility:hidden;transition:visibility 0s linear 200ms;clip-path:inset(16px 0 0 0)}@media screen and (prefers-reduced-motion: no-preference){.ActionList-item[aria-checked=false] .ActionList-item-singleSelectCheckmark,.ActionList-item[aria-selected=false] .ActionList-item-singleSelectCheckmark{animation:checkmarkOut 200ms cubic-bezier(0.11, 0, 0.5, 0) forwards}}.ActionList-item[aria-checked=false] .ActionList-item-multiSelectIcon .ActionList-item-multiSelectIconRect,.ActionList-item[aria-selected=false] .ActionList-item-multiSelectIcon .ActionList-item-multiSelectIconRect{fill:var(--bgColor-default, var(--color-canvas-default));stroke:var(--borderColor-default, var(--color-border-default));stroke-width:1px}.ActionList-item[aria-checked=false] .ActionList-item-multiSelectIconRect,.ActionList-item[aria-selected=false] .ActionList-item-multiSelectIconRect{fill:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default))}@keyframes checkmarkIn{from{clip-path:inset(16px 0 0 0)}to{clip-path:inset(0 0 0 0)}}@keyframes checkmarkOut{from{clip-path:inset(0 0 0 0)}to{clip-path:inset(16px 0 0 0)}}.ActionList-item[aria-disabled=true] .ActionList-content .ActionList-item-label,.ActionList-item[aria-disabled=true] .ActionList-content .ActionList-item-description{color:var(--fgColor-disabled, var(--color-primer-fg-disabled))}.ActionList-item[aria-disabled=true] .ActionList-content .ActionList-item-visual{fill:var(--fgColor-disabled, var(--color-primer-fg-disabled))}@media(hover: hover){.ActionList-item[aria-disabled=true]:hover{cursor:not-allowed;background-color:transparent}}.ActionList-item.ActionList-item--danger .ActionList-item-label{color:var(--fgColor-danger, var(--color-danger-fg))}.ActionList-item.ActionList-item--danger .ActionList-item-visual{color:var(--fgColor-danger, var(--color-danger-fg))}@media(hover: hover){.ActionList-item.ActionList-item--danger:hover{background:var(--control-danger-bgColor-hover, var(--color-action-list-item-danger-hover-bg))}.ActionList-item.ActionList-item--danger:hover .ActionList-item-label{color:var(--control-danger-fgColor-hover, var(--color-action-list-item-danger-hover-text))}}.ActionList-item.ActionList-item--danger .ActionList-content:active{background:var(--control-danger-bgColor-active, var(--color-action-list-item-danger-active-bg))}.ActionList-item .ActionList{padding:unset}.ActionList-content{position:relative;display:grid;width:100%;padding:6px 8px;font-size:14px;font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-default, var(--color-fg-default));text-align:left;-webkit-user-select:none;user-select:none;background-color:transparent;border:none;border-radius:6px;transition:background 33.333ms linear;touch-action:manipulation;touch-action:manipulation;-webkit-tap-highlight-color:transparent;grid-template-rows:min-content;grid-template-areas:"leadingAction leadingVisual label trailingVisual trailingAction";grid-template-columns:min-content min-content minmax(0, auto) min-content min-content;align-items:start}.ActionList-content>:not(:last-child){margin-right:8px}.ActionList-content:hover{text-decoration:none}.ActionList-content:focus{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:-2px;box-shadow:none}.ActionList-content:focus:not(:focus-visible){outline:solid 1px transparent}.ActionList-content:focus-visible{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:-2px;box-shadow:none}.ActionList-content[aria-disabled=true] .ActionList-item-label,.ActionList-content[aria-disabled=true] .ActionList-item-description{color:var(--fgColor-disabled, var(--color-primer-fg-disabled))}.ActionList-content[aria-disabled=true] .ActionList-item-visual{fill:var(--fgColor-disabled, var(--color-primer-fg-disabled))}@media(hover: hover){.ActionList-content[aria-disabled=true]:hover{cursor:not-allowed;background-color:transparent}}@media screen and (prefers-reduced-motion: no-preference){.ActionList-content[aria-expanded]+.ActionList--subGroup{transition:opacity 160ms cubic-bezier(0.25, 1, 0.5, 1),transform 160ms cubic-bezier(0.25, 1, 0.5, 1)}}.ActionList-content[aria-expanded]+.ActionList--subGroup .ActionList-content{padding-left:24px}.ActionList-content[aria-expanded].ActionList-content--visual16+.ActionList--subGroup .ActionList-content{padding-left:32px}.ActionList-content[aria-expanded].ActionList-content--visual20+.ActionList--subGroup .ActionList-content{padding-left:36px}.ActionList-content[aria-expanded].ActionList-content--visual24+.ActionList--subGroup .ActionList-content{padding-left:40px}.ActionList-content[aria-expanded=true] .ActionList-item-collapseIcon{transition:transform 120ms linear;transform:scaleY(-1)}.ActionList-content[aria-expanded=true]+.ActionList--subGroup{height:auto;overflow:visible;visibility:visible;opacity:1;transform:translateY(0)}.ActionList-content[aria-expanded=true].ActionList-content--hasActiveSubItem>.ActionList-item-label{font-weight:var(--base-text-weight-semibold, 600)}.ActionList-content[aria-expanded=false] .ActionList-item-collapseIcon{transition:transform 120ms linear;transform:scaleY(1)}.ActionList-content[aria-expanded=false]+.ActionList--subGroup{height:0;overflow:hidden;visibility:hidden;opacity:0;transform:translateY(-16px)}.ActionList-content[aria-expanded=false].ActionList-content--hasActiveSubItem{background:var(--control-transparent-bgColor-selected, var(--color-action-list-item-default-selected-bg))}.ActionList-content[aria-expanded=false].ActionList-content--hasActiveSubItem .ActionList-item-label{font-weight:var(--base-text-weight-semibold, 600)}.ActionList-content[aria-expanded=false].ActionList-content--hasActiveSubItem::before,.ActionList-content[aria-expanded=false].ActionList-content--hasActiveSubItem+.ActionList-item::before{visibility:hidden}.ActionList-content[aria-expanded=false].ActionList-content--hasActiveSubItem::after{position:absolute;top:calc(50% - 12px);left:-8px;width:4px;height:24px;content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-fg));border-radius:6px}.ActionList-content.ActionList-content--sizeMedium{padding:10px 8px}.ActionList-content.ActionList-content--sizeLarge{padding:14px 8px}.ActionList-content.ActionList-content--fontSmall{font-size:12px}@media(pointer: coarse){.ActionList-content{padding:14px 8px}}.ActionList-content.ActionList-content--blockDescription .ActionList-item-visual{place-self:start}.ActionList-item-action--leading{grid-area:leadingAction}.ActionList-item-visual--leading{grid-area:leadingVisual}.ActionList-item-label{grid-area:label}.ActionList-item-visual--trailing{grid-area:trailingVisual}.ActionList-item-action--trailing{grid-area:trailingAction}.ActionList-item-descriptionWrap{grid-area:label;display:flex;flex-direction:column}.ActionList-item-descriptionWrap .ActionList-item-description{margin-top:4px}.ActionList-item-descriptionWrap .ActionList-item-label{font-weight:var(--base-text-weight-semibold, 600)}.ActionList-item-descriptionWrap--inline{position:relative;flex-direction:row;align-items:baseline}.ActionList-item-descriptionWrap--inline .ActionList-item-description{margin-left:8px}.ActionList-item-description{font-size:12px;font-weight:var(--base-text-weight-normal, 400);line-height:1.5;color:var(--fgColor-muted, var(--color-fg-muted))}.ActionList-item-visual,.ActionList-item-action{display:flex;min-height:20px;color:var(--fgColor-muted, var(--color-fg-muted));pointer-events:none;fill:var(--fgColor-muted, var(--color-fg-muted));align-items:center}.ActionList-item-label{position:relative;font-weight:var(--base-text-weight-normal, 400);line-height:20px;color:var(--fgColor-default, var(--color-fg-default))}.ActionList-item-label--truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.ActionList-item--subItem>.ActionList-content{font-size:12px}.ActionList-sectionDivider:not(:empty){display:flex;padding:6px 8px;font-size:12px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-muted, var(--color-fg-muted));flex-direction:column}.ActionList-sectionDivider:empty{display:block;height:1px;padding:0;margin:7px -8px 8px;list-style:none;background:var(--borderColor-muted, var(--color-action-list-item-inline-divider));border:0}.ActionList-sectionDivider .ActionList-sectionDivider-title{font-size:12px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-muted, var(--color-fg-muted))}.ActionList-sectionDivider--filled{margin:8px -8px;background:var(--bgColor-muted, var(--color-canvas-subtle));border-top:1px solid var(--borderColor-muted, var(--color-action-list-item-inline-divider));border-bottom:1px solid var(--borderColor-muted, var(--color-action-list-item-inline-divider))}.ActionList-sectionDivider--filled:empty{height:8px;box-sizing:border-box}.ActionList-sectionDivider--filled:first-child{margin-top:0}.boxed-group{position:relative;margin-bottom:30px;border-radius:6px}.boxed-group .Counter{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-neutral-emphasis, var(--color-neutral-emphasis))}.boxed-group.flush .boxed-group-inner{padding:0}.boxed-group.condensed .boxed-group-inner{padding:0;font-size:12px}.boxed-group>h3,.boxed-group .heading{display:block;padding:9px 10px 10px;margin:0;font-size:14px;line-height:17px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:1px solid var(--borderColor-default, var(--color-border-default));border-bottom:0;border-radius:6px 6px 0 0}.boxed-group>h3 a,.boxed-group .heading a{color:inherit}.boxed-group>h3 a.boxed-group-breadcrumb,.boxed-group .heading a.boxed-group-breadcrumb{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted));text-decoration:none}.boxed-group>h3 .avatar,.boxed-group .heading .avatar{margin-top:-4px}.boxed-group .tabnav.heading{padding:0}.boxed-group .tabnav.heading .tabnav-tab.selected{border-top:0}.boxed-group .tabnav.heading li:first-child .selected{border-left-color:var(--bgColor-default, var(--color-canvas-default));border-top-left-radius:6px}.boxed-group .tabnav-tab{border-top:0;border-radius:0}.boxed-group code.heading{font-size:12px}.boxed-group.dangerzone>h3{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-danger-emphasis, var(--color-danger-emphasis));border:1px solid var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.boxed-group.dangerzone .boxed-group-inner{border-top:0}.boxed-group.condensed>h3{padding:6px 6px 7px;font-size:12px}.boxed-group.condensed>h3 .octicon{padding:0 6px 0 2px}.dashboard-sidebar .boxed-group{margin-bottom:20px}.boxed-group .bleed-flush{width:100%;padding:0 10px;margin-left:-10px}.boxed-group .compact{margin-top:10px;margin-bottom:10px}.boxed-group-inner{padding:10px;color:var(--fgColor-muted, var(--color-fg-muted));background:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:6px;border-bottom-left-radius:6px}.boxed-group-inner .markdown-body{padding:20px 10px 10px;font-size:13px}.boxed-group-inner.markdown-body{padding-top:10px;padding-bottom:10px}.boxed-group-inner.seamless{padding:0}.boxed-group-inner .tabnav{padding-right:10px;padding-left:10px;margin-right:-10px;margin-left:-10px}.boxed-group-inner .tabnav-tab.selected{border-top:1px solid var(--borderColor-default, var(--color-border-default))}.boxed-action{float:right;margin-left:10px}.boxed-action .boxed-action{float:none;margin-left:0}.boxed-group-action{position:relative;z-index:2;float:right;margin:5px 10px 0 0}.boxed-group-action.flush{margin-top:0;margin-right:0}.field-with-errors{display:inline}.boxed-group-list{margin:0;list-style:none}.boxed-group-list:first-child>li:first-child{border-top:0}.boxed-group-list>li{display:block;padding:5px 10px;margin-right:-10px;margin-left:-10px;line-height:23px;border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.boxed-group-list>li:first-child{border-top:1px solid var(--borderColor-default, var(--color-border-default))}.boxed-group-list>li:last-of-type{border-bottom:0}.boxed-group-list>li.selected{background:var(--bgColor-success-muted, var(--color-success-subtle))}.boxed-group-list>li.approved .btn-sm,.boxed-group-list>li.rejected .btn-sm{display:none}.boxed-group-list>li.rejected a{text-decoration:line-through}.boxed-group-list>li .avatar{margin-top:-2px;margin-right:4px}.boxed-group-list>li .octicon{width:24px;margin-right:4px}.boxed-group-list>li .btn-sm{float:right;margin:-1px 0 0 10px}.boxed-group-list>li .BtnGroup{float:right}.boxed-group-list>li .BtnGroup .btn-sm{float:left}.boxed-group.flush .boxed-group-list li{width:auto;padding-right:0;padding-left:0;margin-left:0}.boxed-group-list.standalone{margin-top:-1px}.boxed-group-list.standalone>li:first-child{border-top:0}.boxed-group-table{width:100%;text-align:left}.boxed-group-table tr:last-child td{border-bottom:0}.boxed-group-table th{padding:9px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.boxed-group-table td{padding:9px;vertical-align:top;border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.ajax-error-message{position:fixed;top:0;left:50%;z-index:9999;width:974px;margin:0 3px;margin-left:-487px;transition:top .5s ease-in-out}.ajax-error-message>.octicon-alert{vertical-align:text-top}.container{width:980px;margin-right:auto;margin-left:auto}.container::before{display:table;content:""}.container::after{display:table;clear:both;content:""}.draft.octicon{color:var(--fgColor-muted, var(--color-fg-muted))}.closed.octicon,.reverted.octicon{color:var(--fgColor-closed, var(--color-closed-fg))}.open.octicon{color:var(--fgColor-open, var(--color-open-fg))}.closed.octicon.octicon-issue-closed,.merged.octicon{color:var(--fgColor-done, var(--color-done-fg))}.progress-bar{display:block;height:15px;overflow:hidden;background-color:var(--borderColor-muted, var(--color-border-muted));border-radius:6px}.progress-bar .progress{display:block;height:100%;background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis))}.reverse-progress-container{position:relative;height:3px;background-color:var(--borderColor-muted, var(--color-border-muted));background-image:linear-gradient(to right, var(--bgColor-success-emphasis, var(--color-success-emphasis)), var(--bgColor-accent-emphasis, var(--color-accent-emphasis)), var(--bgColor-done-emphasis, var(--color-done-emphasis)), var(--bgColor-danger-emphasis, var(--color-danger-emphasis)), var(--bgColor-severe-emphasis, var(--color-severe-emphasis)));background-size:100% 3px}.reverse-progress-bar{position:absolute;right:0;height:100%;background-color:var(--borderColor-muted, var(--color-border-muted))}.progress-bar-small{height:10px}.select-menu-button::after{display:inline-block;width:0;height:0;vertical-align:-2px;content:"";border:4px solid;border-right-color:transparent;border-bottom-color:transparent;border-left-color:transparent}.select-menu-button.icon-only{padding-left:7px}.select-menu-button.primary::after{border-top-color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.select-menu-button.primary::after:active{background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis))}.select-menu-button-large::after{margin-left:.25em;border-width:.33em}.select-menu .spinner{float:left;margin:4px 0 0 -24px}.select-menu.active .select-menu-modal-holder{display:block}.select-menu.select-menu-modal-right{position:relative}.select-menu.select-menu-modal-right .select-menu-modal-holder{right:0}.select-menu .select-menu-clear-item{display:block}.select-menu .select-menu-clear-item .octicon{color:inherit}.select-menu .select-menu-clear-item+.select-menu-no-results{display:none !important}.select-menu.is-loading .select-menu-loading-overlay{display:block}.select-menu.is-loading .select-menu-modal{min-height:200px}.select-menu.has-error .select-menu-error{display:block}.select-menu-error{display:none}.select-menu-loading-overlay{position:absolute;top:0;z-index:5;display:none;width:100%;height:100%;background-color:var(--overlay-bgColor, var(--color-canvas-overlay));border:1px solid transparent;border-radius:5px}.select-menu-modal-holder{position:absolute;z-index:30;display:none}.select-menu-modal{position:relative;width:300px;margin-top:4px;margin-bottom:20px;overflow:hidden;font-size:12px;color:var(--fgColor-default, var(--color-fg-default));background-color:var(--overlay-bgColor, var(--color-canvas-overlay));background-clip:padding-box;border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px;box-shadow:var(--shadow-floating-large, var(--color-shadow-large))}.select-menu-header,.select-menu-divider{padding:8px 10px;line-height:16px;background:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.select-menu-header .select-menu-title,.select-menu-divider{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.select-menu-divider{margin-top:-1px;border-top:1px solid var(--borderColor-muted, var(--color-border-muted))}.select-menu-header .close-button,.select-menu-header .octicon{display:block;float:right;color:var(--fgColor-muted, var(--color-fg-muted));cursor:pointer}.select-menu-header .close-button:hover,.select-menu-header .octicon:hover{color:var(--fgColor-default, var(--color-fg-default))}.select-menu-header:focus{outline:none}.select-menu-filters{background-color:var(--overlay-bgColor, var(--color-canvas-overlay))}.select-menu-text-filter{padding:10px 10px 0}.select-menu-text-filter:first-child:last-child{padding-bottom:10px;border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.select-menu-text-filter input{display:block;width:100%;max-width:100%;padding:5px;border:1px solid var(--borderColor-muted, var(--color-border-muted));border-radius:6px}.select-menu-text-filter input::placeholder{color:var(--fgColor-muted, var(--color-fg-subtle))}.select-menu-tabs{padding:10px 10px 0;border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.select-menu-tabs ul{position:relative;bottom:-1px}.select-menu-tabs .select-menu-tab{display:inline-block}.select-menu-tabs a,.select-menu-tabs .select-menu-tab-nav{display:inline-block;padding:4px 8px 2px;font-size:12px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-muted, var(--color-fg-muted));text-decoration:none;cursor:pointer;background:transparent;border:1px solid transparent;border-radius:6px 6px 0 0}.select-menu-tabs a:hover,.select-menu-tabs .select-menu-tab-nav:hover{color:var(--fgColor-default, var(--color-fg-default))}.select-menu-tabs a[aria-selected=true],.select-menu-tabs a.selected,.select-menu-tabs .select-menu-tab-nav[aria-selected=true],.select-menu-tabs .select-menu-tab-nav.selected{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--overlay-bgColor, var(--color-canvas-overlay));border-color:var(--borderColor-muted, var(--color-border-muted));border-bottom-color:var(--overlay-bgColor, var(--color-canvas-overlay))}.select-menu-list{position:relative;max-height:400px;overflow:auto}.select-menu-list.is-showing-new-item-form .select-menu-new-item-form{display:block}.select-menu-list.is-showing-new-item-form .select-menu-no-results,.select-menu-list.is-showing-new-item-form .select-menu-clear-item{display:none}.select-menu-blankslate{padding:16px;text-align:center}.select-menu-blankslate svg{display:block;margin-right:auto;margin-bottom:9px;margin-left:auto;fill:var(--fgColor-muted, var(--color-fg-muted))}.select-menu-blankslate h3{font-size:14px;color:var(--fgColor-default, var(--color-fg-default))}.select-menu-blankslate p{width:195px;margin-right:auto;margin-bottom:0;margin-left:auto}.select-menu-item{display:block;padding:8px 8px 8px 30px;overflow:hidden;color:inherit;cursor:pointer;border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.select-menu-item .select-menu-item-text .octicon-x{display:none;float:right;margin:1px 10px 0 0;opacity:.6}.select-menu-item:hover{text-decoration:none}.select-menu-item.disabled,.select-menu-item[disabled],.select-menu-item[aria-disabled=true],.select-menu-item.disabled.selected{color:var(--fgColor-muted, var(--color-fg-muted));cursor:default}.select-menu-item.disabled .description,.select-menu-item[disabled] .description,.select-menu-item[aria-disabled=true] .description,.select-menu-item.disabled.selected .description{color:var(--fgColor-muted, var(--color-fg-muted))}.select-menu-item.disabled.opaque,.select-menu-item[disabled].opaque,.select-menu-item[aria-disabled=true].opaque,.select-menu-item.disabled.selected.opaque{opacity:.7}.select-menu-item.disabled .select-menu-item-gravatar,.select-menu-item[disabled] .select-menu-item-gravatar,.select-menu-item[aria-disabled=true] .select-menu-item-gravatar,.select-menu-item.disabled.selected .select-menu-item-gravatar{opacity:.5}.select-menu-item .octicon{vertical-align:middle}.select-menu-item .octicon-check,.select-menu-item .octicon-circle-slash,.select-menu-item input[type=radio]:not(:checked)+.octicon-check,.select-menu-item input[type=radio]:not(:checked)+.octicon-circle-slash{visibility:hidden}.select-menu-item.selected .octicon-circle-slash.select-menu-item-icon{color:var(--fgColor-muted, var(--color-fg-muted)) !important}.select-menu-item .octicon-circle-slash{color:var(--fgColor-muted, var(--color-fg-muted))}.select-menu-item.excluded{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.select-menu-item input[type=radio]{display:none}.select-menu-item:focus{outline:none}.select-menu-item:focus .octicon,.select-menu-item:hover .octicon{color:inherit !important}.select-menu-item:hover,.select-menu-item:hover.selected,.select-menu-item:hover.select-menu-action,.select-menu-item:hover .description-inline,.select-menu-item:focus,.select-menu-item:focus.selected,.select-menu-item:focus.select-menu-action,.select-menu-item:focus .description-inline,.select-menu-item.navigation-focus,.select-menu-item.navigation-focus.selected,.select-menu-item.navigation-focus.select-menu-action,.select-menu-item.navigation-focus .description-inline,.select-menu-item.navigation-focus[aria-checked=true],.select-menu-item[aria-checked=true]:focus,.select-menu-item[aria-checked=true]:hover,.select-menu-item[aria-selected=true]:hover,.select-menu-item[aria-selected=true]:focus,.select-menu-item[aria-selected=true].select-menu-action,.select-menu-item[aria-selected=true] .description-inline{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.select-menu-item:hover>.octicon,.select-menu-item:hover.selected>.octicon,.select-menu-item:hover.select-menu-action>.octicon,.select-menu-item:hover .description-inline>.octicon,.select-menu-item:focus>.octicon,.select-menu-item:focus.selected>.octicon,.select-menu-item:focus.select-menu-action>.octicon,.select-menu-item:focus .description-inline>.octicon,.select-menu-item.navigation-focus>.octicon,.select-menu-item.navigation-focus.selected>.octicon,.select-menu-item.navigation-focus.select-menu-action>.octicon,.select-menu-item.navigation-focus .description-inline>.octicon,.select-menu-item.navigation-focus[aria-checked=true]>.octicon,.select-menu-item[aria-checked=true]:focus>.octicon,.select-menu-item[aria-checked=true]:hover>.octicon,.select-menu-item[aria-selected=true]:hover>.octicon,.select-menu-item[aria-selected=true]:focus>.octicon,.select-menu-item[aria-selected=true].select-menu-action>.octicon,.select-menu-item[aria-selected=true] .description-inline>.octicon{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.select-menu-item:hover .description,.select-menu-item:hover .description-warning,.select-menu-item:hover.selected .description,.select-menu-item:hover.selected .description-warning,.select-menu-item:hover.select-menu-action .description,.select-menu-item:hover.select-menu-action .description-warning,.select-menu-item:hover .description-inline .description,.select-menu-item:hover .description-inline .description-warning,.select-menu-item:focus .description,.select-menu-item:focus .description-warning,.select-menu-item:focus.selected .description,.select-menu-item:focus.selected .description-warning,.select-menu-item:focus.select-menu-action .description,.select-menu-item:focus.select-menu-action .description-warning,.select-menu-item:focus .description-inline .description,.select-menu-item:focus .description-inline .description-warning,.select-menu-item.navigation-focus .description,.select-menu-item.navigation-focus .description-warning,.select-menu-item.navigation-focus.selected .description,.select-menu-item.navigation-focus.selected .description-warning,.select-menu-item.navigation-focus.select-menu-action .description,.select-menu-item.navigation-focus.select-menu-action .description-warning,.select-menu-item.navigation-focus .description-inline .description,.select-menu-item.navigation-focus .description-inline .description-warning,.select-menu-item.navigation-focus[aria-checked=true] .description,.select-menu-item.navigation-focus[aria-checked=true] .description-warning,.select-menu-item[aria-checked=true]:focus .description,.select-menu-item[aria-checked=true]:focus .description-warning,.select-menu-item[aria-checked=true]:hover .description,.select-menu-item[aria-checked=true]:hover .description-warning,.select-menu-item[aria-selected=true]:hover .description,.select-menu-item[aria-selected=true]:hover .description-warning,.select-menu-item[aria-selected=true]:focus .description,.select-menu-item[aria-selected=true]:focus .description-warning,.select-menu-item[aria-selected=true].select-menu-action .description,.select-menu-item[aria-selected=true].select-menu-action .description-warning,.select-menu-item[aria-selected=true] .description-inline .description,.select-menu-item[aria-selected=true] .description-inline .description-warning{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.select-menu-item:hover.disabled,.select-menu-item[disabled]:hover,.select-menu-item[aria-disabled=true]:hover,.select-menu-item[aria-selected=true].disabled,.select-menu-item.navigation-focus.disabled{color:var(--fgColor-muted, var(--color-fg-muted));background-color:var(--overlay-bgColor, var(--color-canvas-overlay))}.select-menu-item:hover.disabled .description,.select-menu-item[disabled]:hover .description,.select-menu-item[aria-disabled=true]:hover .description,.select-menu-item[aria-selected=true].disabled .description,.select-menu-item.navigation-focus.disabled .description{color:var(--fgColor-muted, var(--color-fg-muted))}.select-menu-item>.octicon-dash{display:none}.select-menu-item[aria-checked=mixed]>.octicon-check{display:none}.select-menu-item[aria-checked=mixed]>.octicon-dash{display:block}.select-menu-item input:checked+.octicon-check{color:inherit;visibility:visible}details-menu .select-menu-item[aria-checked=true],details-menu .select-menu-item[aria-selected=true],.select-menu-item.selected{color:var(--fgColor-default, var(--color-fg-default))}details-menu .select-menu-item[aria-checked=true] .description,details-menu .select-menu-item[aria-selected=true] .description,.select-menu-item.selected .description{color:var(--fgColor-muted, var(--color-fg-muted))}details-menu .select-menu-item[aria-checked=true]>.octicon,details-menu .select-menu-item[aria-selected=true]>.octicon,.select-menu-item.selected>.octicon{color:var(--fgColor-default, var(--color-fg-default))}details-menu .select-menu-item[aria-checked=true] .octicon-check,details-menu .select-menu-item[aria-checked=true] .octicon-circle-slash,details-menu .select-menu-item[aria-selected=true] .octicon-check,details-menu .select-menu-item[aria-selected=true] .octicon-circle-slash,.select-menu-item.selected .octicon-check,.select-menu-item.selected .octicon-circle-slash{color:inherit;visibility:visible}details-menu .select-menu-item[aria-checked=true] .select-menu-item-text .octicon-x,details-menu .select-menu-item[aria-selected=true] .select-menu-item-text .octicon-x,.select-menu-item.selected .select-menu-item-text .octicon-x{display:block;color:inherit}.select-menu.label-select-menu .select-menu-item:active{background-color:transparent !important}.select-menu-item:hover .Label,.select-menu-item:focus .Label{color:inherit;border-color:currentColor}.select-menu-item a{color:inherit;text-decoration:none}.select-menu-item .hidden-select-button-text{display:none}.select-menu-item .css-truncate-target{max-width:100%}.select-menu-item-icon{float:left;margin-left:-20px}form.select-menu-item>div:first-child{display:none !important}.select-menu-list:last-child .select-menu-item:last-child,.select-menu-item.last-visible{border-bottom:0;border-radius:0 0 6px 6px}.select-menu-action{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-default, var(--color-fg-default))}.select-menu-action>.octicon{color:inherit}.select-menu-action:hover{color:var(--fgColor-accent, var(--color-accent-fg))}.select-menu-no-results{display:none;padding:9px;color:var(--fgColor-muted, var(--color-fg-muted));cursor:auto}.select-menu-list.filterable-empty .select-menu-no-results,.select-menu-no-results:only-child{display:block}.select-menu-button-gravatar,.select-menu-item-gravatar{width:20px;overflow:hidden;line-height:0}.select-menu-button-gravatar img,.select-menu-item-gravatar img{display:inline-block;width:20px;height:20px;border-radius:6px}.select-menu-item-gravatar{float:left;width:20px;height:20px;margin-right:8px;border-radius:6px}.select-menu-button-gravatar{float:left;margin-right:5px}.select-menu-item-text{display:block;text-align:left}.select-menu-item-text .description{display:block;max-width:265px;font-size:12px;color:var(--fgColor-muted, var(--color-fg-muted))}.select-menu-item-text .description-inline{font-size:12px;color:var(--fgColor-muted, var(--color-fg-muted))}.select-menu-item-text .description-warning{color:var(--fgColor-danger, var(--color-danger-fg))}.select-menu-item-text mark{font-weight:var(--base-text-weight-semibold, 600);color:inherit;background-color:inherit}.select-menu-item-heading{display:block;margin-top:0;margin-bottom:0;font-size:14px;font-weight:var(--base-text-weight-semibold, 600)}.select-menu-item-heading .description{display:inline;font-weight:var(--base-text-weight-normal, 400)}.select-menu-new-item-form{display:none}.select-menu-new-item-form .octicon{color:var(--fgColor-accent, var(--color-accent-fg))}.table-list{display:table;width:100%;color:var(--fgColor-muted, var(--color-fg-muted));table-layout:fixed;border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.table-list ol{list-style-type:decimal}.table-list-bordered{border-bottom-color:var(--borderColor-default, var(--color-border-default))}.table-list-bordered .table-list-cell:first-child{border-left:1px solid var(--borderColor-default, var(--color-border-default))}.table-list-bordered .table-list-cell:last-child{border-right:1px solid var(--borderColor-default, var(--color-border-default))}.table-list-item{position:relative;display:table-row;list-style:none}.table-list-item.unread .table-list-cell:first-child{box-shadow:2px 0 0 var(--borderColor-accent-emphasis, var(--color-accent-emphasis)) inset}.table-list-cell{position:relative;display:table-cell;padding:8px 10px;font-size:12px;vertical-align:top;border-top:1px solid var(--borderColor-default, var(--color-border-default))}.table-list-cell.flush-left{padding-left:0}.table-list-cell.flush-right{padding-right:0}.table-list-header{position:relative;margin-top:20px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px 6px 0 0}.table-list-header::before{display:table;content:""}.table-list-header::after{display:table;clear:both;content:""}.table-list-header .btn-link{position:relative;display:inline-block;padding-top:13px;padding-bottom:13px;font-weight:var(--base-text-weight-normal, 400)}.table-list-heading{margin-left:10px}.table-list-header-meta{display:inline-block;padding-top:13px;padding-bottom:13px;color:var(--fgColor-muted, var(--color-fg-muted))}.table-list-header-toggle h4{padding:12px 0}.table-list-filters:first-child .table-list-header-toggle:first-child{padding-left:16px}.table-list-header-toggle.states .selected{font-weight:var(--base-text-weight-semibold, 600)}.table-list-header-toggle .btn-link{color:var(--fgColor-muted, var(--color-fg-muted))}.table-list-header-toggle .btn-link .octicon{margin-right:4px}.table-list-header-toggle .btn-link:hover{color:var(--fgColor-default, var(--color-fg-default));text-decoration:none}.table-list-header-toggle .btn-link.selected,.table-list-header-toggle .btn-link.selected:hover{color:var(--fgColor-default, var(--color-fg-default))}.table-list-header-toggle .btn-link+.btn-link{margin-left:10px}.table-list-header-toggle .btn-link:disabled,.table-list-header-toggle .btn-link.disabled{pointer-events:none;opacity:.5}.table-list-header-toggle .select-menu{position:relative}.table-list-header-toggle .select-menu-item[aria-checked=true],.table-list-header-toggle .select-menu-item.selected{font-weight:var(--base-text-weight-semibold, 600)}.table-list-header-toggle .select-menu-button{padding-right:15px;padding-left:15px}.table-list-header-toggle .select-menu-button:hover,.table-list-header-toggle .select-menu-button.selected,.table-list-header-toggle .select-menu-button.selected:hover{color:var(--fgColor-default, var(--color-fg-default))}.table-list-header-toggle .select-menu-modal-holder{right:10px}.table-list-header-toggle .select-menu-modal-holder .select-menu-modal{margin-top:-1px}.table-list-header-next{margin-top:20px;margin-bottom:-1px}.table-list-header-next .table-list-header-select-all{padding-left:14px}.table-list-header-next .select-all-dropdown{padding-top:10px;padding-bottom:10px}.table-list-triage{display:none}.triage-mode .table-list-filters{display:none !important}.triage-mode .table-list-triage{display:block}.breadcrumb{font-size:16px;color:var(--fgColor-muted, var(--color-fg-muted))}.breadcrumb .separator{white-space:pre-wrap}.breadcrumb .separator::before,.breadcrumb .separator::after{content:" "}.breadcrumb strong.final-path{color:var(--fgColor-default, var(--color-fg-default))}.capped-cards{list-style:none}.capped-card-content{display:block;background:var(--bgColor-muted, var(--color-canvas-subtle))}.capped-card-content::before{display:table;content:""}.capped-card-content::after{display:table;clear:both;content:""}.details-collapse .collapse{position:relative;display:none;height:0;overflow:hidden;transition:height .35s ease-in-out}.details-collapse.open .collapse{display:block;height:auto;overflow:visible}.collapsible-sidebar-widget-button{display:flex;padding:0;align-items:center;background-color:transparent;border:0;justify-content:space-between}.collapsible-sidebar-widget-indicator{transition:transform .25s;transform:translate(0, 0) translate3d(0, 0, 0)}.collapsible-sidebar-widget-loader{display:none;visibility:hidden;opacity:0;transition:opacity .25s;animation-play-state:paused}.collapsible-sidebar-widget-content{width:100%;max-height:0;overflow:hidden;opacity:0;transition:max-height .25s ease-in-out,opacity .25s ease-in-out}.collapsible-sidebar-widget-loading .collapsible-sidebar-widget-indicator{display:none}.collapsible-sidebar-widget-loading .collapsible-sidebar-widget-loader{display:block;visibility:visible;opacity:1;animation-play-state:running}.collapsible-sidebar-widget-active .collapsible-sidebar-widget-content{max-height:100%;overflow:visible;opacity:1}.collapsible-sidebar-widget-active .collapsible-sidebar-widget-indicator{display:block;transform:rotate(180deg)}.collapsible-sidebar-widget-active .collapsible-sidebar-widget-loader{display:none;visibility:hidden;opacity:0}.collapsible-sidebar-widget-active .collapsible-sidebar-widget-active-hidden{display:none;opacity:0}.comment .email-format{line-height:1.5}.previewable-edit .previewable-comment-form{display:none}.previewable-edit .previewable-comment-form::before{display:table;content:""}.previewable-edit .previewable-comment-form::after{display:table;clear:both;content:""}.previewable-edit .previewable-comment-form .tabnav-tabs{display:inline-block}.previewable-edit .previewable-comment-form .form-actions{float:right;margin-right:8px;margin-bottom:8px}.previewable-edit.is-comment-editing .timeline-comment-header:not(.new-comment-box-header){display:none !important}.is-comment-editing .previewable-comment-form{display:block}.is-comment-editing .timeline-comment-actions,.is-comment-editing .edit-comment-hide{display:none}.is-comment-loading .previewable-comment-form{opacity:.5}.comment-show-stale{display:none}.is-comment-stale .comment-show-stale{display:block}.comment-body{width:100%;padding:16px;overflow:visible;font-size:14px;color:var(--fgColor-default, var(--color-fg-default))}.comment-body .highlight{overflow:visible !important;background-color:transparent}.comment-form-textarea{width:100%;max-width:100%;height:100px;min-height:100px;margin:0;line-height:1.6}.comment-form-textarea.dragover{border:solid 1px var(--borderColor-accent-emphasis, var(--color-accent-emphasis))}.hide-reaction-suggestion:hover::before,.hide-reaction-suggestion:hover::after,.hide-reaction-suggestion:active::before,.hide-reaction-suggestion:active::after{display:none}.reaction-suggestion[data-reaction-suggestion-message]:hover::before,.reaction-suggestion[data-reaction-suggestion-message]:hover::after{display:inline-block}.reaction-suggestion[data-reaction-suggestion-message]::before,.reaction-suggestion[data-reaction-suggestion-message]::after{display:inline-block;text-decoration:none;animation-name:tooltip-appear;animation-duration:.1s;animation-fill-mode:forwards;animation-timing-function:ease-in;animation-delay:0s}.reaction-suggestion[data-reaction-suggestion-message]::after{content:attr(data-reaction-suggestion-message)}.discussion-topic-header{position:relative;padding:8px;word-wrap:break-word}.comment-form-error{padding:16px 8px;margin:8px;color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-danger-muted, var(--color-danger-subtle));border:1px solid var(--borderColor-danger-emphasis, var(--color-danger-emphasis));border-radius:6px}.email-format{line-height:1.5em !important}.email-format div{white-space:pre-wrap}.email-format .email-hidden-reply{display:none;white-space:pre-wrap}.email-format .email-hidden-reply.expanded{display:block}.email-format .email-quoted-reply,.email-format .email-signature-reply{padding:0 16px;margin:16px 0;color:var(--fgColor-muted, var(--color-fg-muted));border-left:4px solid var(--borderColor-default, var(--color-border-default))}.email-format .email-hidden-toggle a{display:inline-block;height:12px;padding:0 8px;font-size:12px;font-weight:var(--base-text-weight-semibold, 600);line-height:6px;color:var(--fgColor-default, var(--color-fg-default));text-decoration:none;vertical-align:middle;background:var(--bgColor-neutral-muted, var(--color-neutral-muted));border-radius:1px}.email-format .email-hidden-toggle a:hover{background-color:var(--bgColor-accent-muted, var(--color-accent-muted))}.email-format .email-hidden-toggle a:active{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.comment-email-format div{white-space:normal}.comment-email-format .email-hidden-reply{display:none;white-space:normal}.comment-email-format .email-hidden-reply.expanded{display:block}.comment-email-format blockquote,.comment-email-format p{margin:0}.locked-conversation .write-tab,.locked-conversation .preview-tab{color:#c6cbd1}.write-tab:focus,.preview-tab:focus{outline-offset:-6px !important}.manual-file-chooser-transparent{min-height:0;overflow:hidden;opacity:.01}.manual-file-chooser-transparent::-webkit-file-upload-button{cursor:pointer}.manual-file-chooser-transparent:focus{opacity:1 !important}.rich-diff clipboard-copy{display:none}.css-overflow-wrap-anywhere{overflow-wrap:anywhere}.commit-form{position:relative;padding:16px;border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.commit-form::after,.commit-form::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.commit-form::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-canvas-default), var(--color-canvas-default))}.commit-form::before{background-color:var(--color-border-default)}.commit-form .input-block{margin-top:8px;margin-bottom:8px}.commit-form-avatar{float:left;margin-left:-64px;border-radius:6px}.commit-form-actions::before{display:table;content:""}.commit-form-actions::after{display:table;clear:both;content:""}.commit-form-actions .BtnGroup{margin-right:4px}.merge-commit-message{resize:vertical}@media(max-width: 768px){.commit-form::after,.commit-form::before{display:none !important}}.commit-sha{padding:.2em .4em;font-size:90%;font-weight:var(--base-text-weight-normal, 400);background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:1px solid var(--borderColor-muted, var(--color-border-muted));border-radius:.2em}.commit .commit-title,.commit .commit-title a{color:var(--fgColor-default, var(--color-fg-default))}.commit .commit-title.blank,.commit .commit-title.blank a{color:var(--fgColor-muted, var(--color-fg-muted))}.commit .commit-title .issue-link{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-accent, var(--color-accent-fg))}.commit .sha-block,.commit .sha{font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px}.commit.open .commit-desc{display:block}.commit-link{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-accent, var(--color-accent-fg))}.commit-ref{position:relative;display:inline-block;padding:0 4px;font:.85em/1.8 ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;color:var(--fgColor-muted, var(--color-fg-muted));white-space:nowrap;background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-radius:6px}.commit-ref .user{color:var(--fgColor-accent, var(--color-accent-fg))}a.commit-ref:hover{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none;background-color:var(--bgColor-accent-muted, var(--color-accent-subtle))}.commit-desc{display:none}.commit-desc pre{max-width:700px;margin-top:8px;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:11px;line-height:1.45;color:var(--fgColor-default, var(--color-fg-default));white-space:pre-wrap}.commit-desc+.commit-branches{padding-top:8px;margin-top:2px;border-top:solid 1px var(--borderColor-muted, var(--color-border-subtle))}.commit-author-section{color:var(--fgColor-default, var(--color-fg-default))}.commit-author-section span.user-mention{font-weight:var(--base-text-weight-normal, 400)}.commit-tease-sha{display:inline-block;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:90%;color:var(--fgColor-default, var(--color-fg-default))}.commits-list-item[aria-selected=true],.commits-list-item.navigation-focus{background:#f6fbff}.commits-list-item .commit-title{margin:0;font-size:16px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.commits-list-item .commit-meta{margin-top:1px;font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.commits-list-item .status .octicon{height:14px;line-height:14px}.commits-list-item .commit-author{color:var(--fgColor-muted, var(--color-fg-muted))}.commits-list-item .octicon-arrow-right{margin:0 4px}.commits-list-item .commit-desc pre{margin-top:4px;margin-bottom:8px;color:var(--fgColor-muted, var(--color-fg-muted))}.commits-list-item .commit-desc pre a{word-break:break-word}.commit-indicator{margin-left:4px}.commit-links-group{margin-right:4px}.commits-list-item+.commits-list-item{border-top:1px solid var(--borderColor-default, var(--color-border-default))}.full-commit{padding:8px 8px 0;margin:8px 0;font-size:14px;background:var(--bgColor-neutral-muted, var(--color-neutral-subtle));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.full-commit:first-child{margin-top:0}.full-commit div.commit-title{font-size:16px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.full-commit .branches-list{display:inline;margin-right:8px;margin-left:2px;vertical-align:middle;list-style:none}.full-commit .branches-list li{display:inline-block;padding-left:4px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.full-commit .branches-list li::before{padding-right:4px;font-weight:var(--base-text-weight-normal, 400);content:"+"}.full-commit .branches-list li:first-child{padding-left:0}.full-commit .branches-list li:first-child::before{padding-right:0;content:""}.full-commit .branches-list li.loading{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.full-commit .branches-list li.pull-request{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.full-commit .branches-list li.pull-request::before{margin-left:-8px;content:""}.full-commit .branches-list li.pull-request-error{margin-bottom:-1px}.full-commit .branches-list li a{color:inherit}.full-commit .commit-meta{padding:8px;margin-right:-8px;margin-left:-8px;background:var(--bgColor-default, var(--color-canvas-default));border-top:1px solid var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:6px;border-bottom-left-radius:6px}.full-commit .sha-block{margin-left:16px;font-size:12px;line-height:24px;color:var(--fgColor-muted, var(--color-fg-muted))}.full-commit .sha-block>.sha{color:var(--fgColor-default, var(--color-fg-default))}.full-commit .sha-block>a{color:var(--fgColor-default, var(--color-fg-default));text-decoration:none;border-bottom:1px dotted var(--borderColor-muted, var(--color-border-muted))}.full-commit .sha-block>a:hover{border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.full-commit .commit-desc{display:block;margin:-4px 0 8px}.full-commit .commit-desc pre{max-width:100%;overflow:visible;font-size:13px;word-wrap:break-word}.commit-branches{font-size:12px;color:var(--fgColor-muted, var(--color-fg-muted));vertical-align:middle}.commit-branches .octicon{vertical-align:middle}.commit-build-statuses{position:relative;display:inline-block;text-align:left}.commit-build-statuses .dropdown-menu{min-width:362.6666666667px;max-width:544px;padding-top:0;padding-bottom:0}.commit-build-statuses .dropdown-menu .merge-status-list{max-height:170px;border-bottom:0}.commit-build-statuses .dropdown-menu-w,.commit-build-statuses .dropdown-menu-e{top:-11px}.commit-build-statuses .merge-status-item:last-child{border-radius:0 0 6px 6px}.dropdown-signed-commit .dropdown-menu{width:260px;margin-top:8px;font-size:14px;line-height:1.4;white-space:normal}.dropdown-signed-commit .dropdown-menu::after{border-bottom-color:var(--bgColor-muted, var(--color-canvas-subtle))}.dropdown-signed-commit .dropdown-menu-w{top:-28px;margin-top:0}.dropdown-signed-commit .dropdown-menu-w::after{border-bottom-color:transparent;border-left-color:var(--bgColor-muted, var(--color-canvas-subtle))}.signed-commit-header{line-height:1.3;white-space:normal;border-collapse:separate;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:1px solid var(--borderColor-default, var(--color-border-default));border-top-left-radius:6px;border-top-right-radius:6px}.signed-commit-header .octicon-verified{color:var(--fgColor-success, var(--color-success-fg))}.signed-commit-header .octicon-unverified{color:var(--fgColor-muted, var(--color-fg-muted))}.signed-commit-footer{font-size:12px;line-height:1.5}.signed-commit-cert-info{margin-bottom:4px}.signed-commit-cert-info td{vertical-align:top}.signed-commit-cert-info td:first-child{width:44px;padding-right:12px}.signed-commit-badge{display:inline-block;padding:1px 4px;font-size:12px;color:var(--fgColor-muted, var(--color-fg-muted));vertical-align:middle;-webkit-user-select:none;user-select:none;background:none;border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.signed-commit-badge:hover{text-decoration:none;border-color:var(--borderColor-neutral-muted, var(--color-neutral-muted))}.signed-commit-badge.verified{color:var(--fgColor-success, var(--color-success-fg))}.signed-commit-badge.verified:hover{border-color:var(--borderColor-success-emphasis, var(--color-success-emphasis))}.signed-commit-badge.unverified{color:var(--fgColor-attention, var(--color-attention-fg))}.signed-commit-badge.unverified:hover{border-color:var(--borderColor-attention-emphasis, var(--color-attention-emphasis))}.signed-commit-badge-small{height:20px;margin-top:-2px;margin-right:4px}.signed-commit-badge-medium{height:20px;padding:4px 8px;font-size:12px;border-radius:6px}.signed-commit-badge-large{height:24px;padding:4px 12px;margin-right:8px;font-size:14px;line-height:20px;border-radius:6px}.signed-commit-signer-name{font-size:14px;text-align:left}.signed-commit-signer-name .signer{display:block;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default))}.table-of-contents{margin:16px 0}.table-of-contents li{padding:8px 0;list-style-type:none}.table-of-contents li+li{border-top:1px solid var(--borderColor-muted, var(--color-border-muted))}.table-of-contents li>.octicon{margin-right:4px}.table-of-contents .toc-diff-stats{padding-left:16px;line-height:26px}.table-of-contents .toc-diff-stats .octicon{float:left;margin-top:4px;margin-left:-16px;color:#c6cbd1}.table-of-contents .toc-diff-stats .btn-link{font-weight:var(--base-text-weight-semibold, 600)}.table-of-contents .toc-diff-stats+.content{padding-top:4px}.table-of-contents .octicon-diff-removed{color:var(--fgColor-danger, var(--color-danger-fg))}.table-of-contents .octicon-diff-renamed{color:var(--fgColor-muted, var(--color-fg-muted))}.table-of-contents .octicon-diff-modified{color:var(--fgColor-attention, var(--color-attention-fg))}.table-of-contents .octicon-diff-added{color:var(--fgColor-success, var(--color-success-fg))}.copyable-terminal{position:relative;padding:8px 55px 8px 8px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-radius:6px}.copyable-terminal-content{overflow:auto}.copyable-terminal-button{position:absolute;top:5px;right:5px}.copyable-terminal-button .zeroclipboard-button{float:right}.copyable-terminal-button .zeroclipboard-button .octicon{padding-left:1px;margin:0 auto}.blob-wrapper{overflow-x:auto;overflow-y:hidden}.blob-wrapper table tr:nth-child(2n){background-color:transparent}.page-edit-blob.height-full .CodeMirror{height:300px}.page-edit-blob.height-full .CodeMirror,.page-edit-blob.height-full .CodeMirror-scroll{display:flex;flex-direction:column;flex:1 1 auto}.blob-wrapper-embedded{max-height:240px;overflow-y:auto}.diff-table{width:100%;border-collapse:separate}.diff-table .blob-code.blob-code-inner{padding-left:22px}.diff-table .line-comments{padding:10px;vertical-align:top;border-top:1px solid var(--borderColor-default, var(--color-border-default))}.diff-table .line-comments:first-child+.empty-cell{border-left-width:1px}.diff-table tr:not(:last-child) .line-comments{border-top:1px solid var(--borderColor-default, var(--color-border-default));border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.diff-view .blob-code-marker-context::before,.diff-view .blob-code-marker-injected_context::before,.diff-view .blob-code-marker-addition::before,.diff-view .blob-code-marker-deletion::before{top:4px}.diff-view .line-alert,.diff-table .line-alert{position:absolute;left:-60px;margin:2px}.comment-body .diff-view .line-alert{left:0}.blob-num{position:relative;width:1%;min-width:50px;padding-right:10px;padding-left:10px;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;line-height:20px;color:var(--fgColor-muted, var(--color-fg-subtle));text-align:right;white-space:nowrap;vertical-align:top;cursor:pointer;-webkit-user-select:none;user-select:none}.blob-num:hover{color:var(--fgColor-default, var(--color-fg-default))}.blob-num::before{content:attr(data-line-number)}.blob-num.non-expandable{cursor:default}.blob-num.non-expandable:hover{color:var(--fgColor-muted, var(--color-fg-subtle))}.blob-num-hidden::before{visibility:hidden}.blob-code{position:relative;padding-right:10px;padding-left:10px;line-height:20px;vertical-align:top}.blob-code-inner{display:table-cell;overflow:visible;font-family:ui-monospace,SFMono-Regular,SF Mono,Menlo,Consolas,Liberation Mono,monospace;font-size:12px;color:var(--fgColor-default, var(--color-fg-default));word-wrap:anywhere;white-space:pre}.blob-code-inner .x-first{border-top-left-radius:.2em;border-bottom-left-radius:.2em}.blob-code-inner .x-last{border-top-right-radius:.2em;border-bottom-right-radius:.2em}.blob-code-inner.highlighted,.blob-code-inner .highlighted{background-color:var(--bgColor-attention-muted, var(--color-attention-subtle));box-shadow:inset 2px 0 0 var(--borderColor-attention-muted, var(--color-attention-muted))}.blob-code-inner::selection,.blob-code-inner *::selection{background-color:var(--bgColor-accent-muted, var(--color-accent-muted))}.js-blob-wrapper .blob-code-inner{white-space:pre-wrap}.blob-code-inner.blob-code-addition,.blob-code-inner.blob-code-deletion{position:relative;padding-left:22px !important}.blob-code-marker::before{position:absolute;top:1px;left:8px;padding-right:8px;content:attr(data-code-marker)}.blob-code-context,.blob-code-addition,.blob-code-deletion{padding-left:22px}.blob-code-marker-addition::before{position:absolute;top:1px;left:8px;content:"+ "}.blob-code-marker-deletion::before{position:absolute;top:1px;left:8px;content:"- "}.blob-code-marker-context::before{position:absolute;top:1px;left:8px;content:"  "}.blob-code-marker-injected_context::before{position:absolute;top:1px;left:8px;content:"  "}.soft-wrap .diff-table{table-layout:fixed}.soft-wrap .blob-code{padding-left:18px;text-indent:0}.soft-wrap .blob-code-inner{white-space:pre-wrap}.soft-wrap .no-nl-marker{display:none}.soft-wrap .add-line-comment{margin-top:0;margin-left:-24px}.soft-wrap .blob-code-context,.soft-wrap .blob-code-addition,.soft-wrap .blob-code-deletion{padding-left:22px;text-indent:0}.blob-num-hunk,.blob-code-hunk,.blob-num-expandable{color:var(--fgColor-muted, var(--color-fg-muted));vertical-align:middle}.blob-num-hunk,.blob-num-expandable{background-color:var(--diffBlob-hunk-bgColor-num, var(--color-diff-blob-hunk-num-bg))}.blob-code-hunk{padding-top:4px;padding-bottom:4px;background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-width:1px 0}.blob-expanded .blob-num:not(.blob-num-context-outside-diff),.blob-expanded .blob-code:not(.blob-code-context){background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.blob-expanded+tr.show-top-border:not(.blob-expanded) .blob-num,.blob-expanded+tr.show-top-border:not(.blob-expanded) .blob-code{border-top:1px solid var(--borderColor-muted, var(--color-border-muted))}.blob-expanded tr.show-top-border .blob-num-hunk,.blob-expanded tr.show-top-border .blob-num{border-top:1px solid var(--borderColor-muted, var(--color-border-muted))}tr.show-top-border+.blob-expanded .blob-num,tr.show-top-border+.blob-expanded .blob-code{border-top:1px solid var(--borderColor-muted, var(--color-border-muted))}.blob-num-expandable{width:auto;padding:0;font-size:12px;text-align:center}.blob-num-expandable .directional-expander{display:block;width:auto;height:auto;margin-right:-1px;color:var(--diffBlob-expander-iconColor, var(--color-diff-blob-expander-icon));cursor:pointer}.blob-num-expandable .single-expander{padding-top:4px;padding-bottom:4px}.blob-num-expandable .directional-expander:hover{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));text-shadow:none;background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis));border-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis))}.blob-code-addition{background-color:var(--diffBlob-addition-bgColor-line, var(--color-diff-blob-addition-line-bg));outline:1px dotted transparent}.blob-code-addition .x{color:var(--diffBlob-addition-fgColor-text, var(--color-diff-blob-addition-fg));background-color:var(--diffBlob-addition-bgColor-word, var(--color-diff-blob-addition-word-bg))}.blob-num-addition{color:var(--diffBlob-addition-fgColor-num, var(--color-diff-blob-addition-num-text));background-color:var(--diffBlob-addition-bgColor-num, var(--color-diff-blob-addition-num-bg));border-color:var(--borderColor-success-emphasis, var(--color-success-emphasis))}.blob-num-addition:hover{color:var(--fgColor-default, var(--color-fg-default))}.blob-code-deletion{background-color:var(--diffBlob-deletion-bgColor-line, var(--color-diff-blob-deletion-line-bg));outline:1px dashed transparent}.blob-code-deletion .x{color:var(--diffBlob-deletion-fgColor-text, var(--color-diff-blob-deletion-fg));background-color:var(--diffBlob-deletion-bgColor-word, var(--color-diff-blob-deletion-word-bg))}.blob-num-deletion{color:var(--diffBlob-deletion-fgColor-num, var(--color-diff-blob-deletion-num-text));background-color:var(--diffBlob-deletion-bgColor-num, var(--color-diff-blob-deletion-num-bg));border-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.blob-num-deletion:hover{color:var(--fgColor-default, var(--color-fg-default))}.is-selecting{cursor:ns-resize !important}.is-selecting .blob-num{cursor:ns-resize !important}.is-selecting .add-line-comment,.is-selecting a{pointer-events:none;cursor:ns-resize !important}.is-selecting .is-hovered .add-line-comment{opacity:0}.is-selecting.file-diff-split{cursor:nwse-resize !important}.is-selecting.file-diff-split .blob-num{cursor:nwse-resize !important}.is-selecting.file-diff-split .empty-cell,.is-selecting.file-diff-split .add-line-comment,.is-selecting.file-diff-split a{pointer-events:none;cursor:nwse-resize !important}.selected-line{position:relative}.selected-line::after{position:absolute;top:0;left:0;display:block;width:100%;height:100%;box-sizing:border-box;pointer-events:none;content:"";background:var(--bgColor-attention-muted, var(--color-attention-subtle));mix-blend-mode:var(--color-diff-blob-selected-line-highlight-mix-blend-mode)}.selected-line.selected-line-top::after{border-top:1px solid var(--borderColor-attention-muted, var(--color-attention-muted))}.selected-line.selected-line-bottom::after{border-bottom:1px solid var(--borderColor-attention-muted, var(--color-attention-muted))}.selected-line:first-child::after,.selected-line.selected-line-left::after{border-left:1px solid var(--borderColor-attention-muted, var(--color-attention-muted))}.selected-line:last-child::after,.selected-line.selected-line-right::after{border-right:1px solid var(--borderColor-attention-muted, var(--color-attention-muted))}.is-commenting .selected-line.blob-code::before{position:absolute;top:0;left:-1px;display:block;width:4px;height:100%;content:"";background:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.add-line-comment{position:relative;z-index:1;float:left;width:22px;height:22px;margin:-2px -10px -2px -32px;line-height:21px;color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));text-align:center;text-indent:0;cursor:pointer;background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis));border-radius:6px;box-shadow:var(--shadow-resting-medium, var(--color-shadow-medium));opacity:0;transition:transform .1s ease-in-out;transform:scale(0.8, 0.8)}.add-line-comment:hover{transform:scale(1, 1)}.is-hovered .add-line-comment,.add-line-comment:focus{opacity:1}.add-line-comment .octicon{vertical-align:text-top;pointer-events:none}.add-line-comment.octicon-check{background:#333;opacity:1}.inline-comment-form{border:1px solid #dfe2e5;border-radius:6px}.timeline-inline-comments{width:100%;table-layout:fixed}.timeline-inline-comments .inline-comments,.show-inline-notes .inline-comments{display:table-row}.inline-comments{display:none}.inline-comments .line-comments+.blob-num{border-left-width:1px}.inline-comments .timeline-comment{margin-bottom:10px}.inline-comments .inline-comment-form,.inline-comments .inline-comment-form-container{max-width:780px}.comment-holder{max-width:780px}.comment-holder+.comment-holder{margin-top:16px}.line-comments+.line-comments,.empty-cell+.line-comments{border-left:1px solid var(--borderColor-muted, var(--color-border-muted))}.inline-comment-form-container .inline-comment-form-box,.inline-comment-form-container.open .inline-comment-form-actions{display:none}.inline-comment-form-container .inline-comment-form-actions,.inline-comment-form-container.open .inline-comment-form-box{display:block}body.full-width .container,body.full-width .container-lg:not(.markdown-body),body.full-width .container-xl{width:100%;max-width:none;padding-right:20px;padding-left:20px}body.full-width .repository-content{width:100%}body.full-width .new-pr-form{max-width:980px}.file-diff-split{table-layout:fixed}.file-diff-split .blob-code+.blob-num{border-left:1px solid var(--borderColor-muted, var(--color-border-muted))}.file-diff-split .blob-code-inner{white-space:pre-wrap}.file-diff-split .empty-cell{cursor:default;background-color:var(--bgColor-neutral-muted, var(--color-neutral-subtle));border-right-color:var(--borderColor-muted, var(--color-border-muted))}@media(max-width: 1280px){.file-diff-split .write-selected .comment-form-head.tabnav:not(.CommentBox-header){margin-bottom:80px !important}.file-diff-split .tabnav:not(.CommentBox-header) markdown-toolbar{position:absolute;top:47px;right:0;left:0;height:64px;align-items:center !important}}@media(min-width: 1280px){.file-diff-split .write-selected .comment-form-head.tabnav:not(.CommentBox-header) .tabnav-tabs{align-self:end}}.submodule-diff-stats .octicon-diff-removed{color:var(--fgColor-danger, var(--color-danger-fg))}.submodule-diff-stats .octicon-diff-renamed{color:var(--fgColor-muted, var(--color-fg-muted))}.submodule-diff-stats .octicon-diff-modified{color:var(--fgColor-attention, var(--color-attention-fg))}.submodule-diff-stats .octicon-diff-added{color:var(--fgColor-success, var(--color-success-fg))}.BlobToolbar{left:-17px}.BlobToolbar-dropdown{margin-left:-2px}.pl-token:hover,.pl-token.active{cursor:pointer;background:var(--bgColor-attention-muted, var(--color-attention-subtle))}.diffstat{font-size:12px;font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-muted, var(--color-fg-muted));white-space:nowrap;cursor:default}.diffstat-block-deleted,.diffstat-block-added,.diffstat-block-neutral{display:inline-block;width:8px;height:8px;margin-left:1px}.diffstat-block-deleted{background-color:var(--bgColor-danger-emphasis, var(--color-danger-emphasis))}.diffstat-block-added{background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis))}.diffstat-block-neutral{background-color:var(--bgColor-neutral-muted, var(--color-neutral-muted));outline:1px solid var(--borderColor-muted, var(--color-border-subtle));outline-offset:-1px}.discussion-timeline{position:relative;float:left}.discussion-timeline::before{position:absolute;top:0;bottom:0;left:72px;z-index:0;display:block;width:2px;content:"";background-color:var(--borderColor-default, var(--color-border-default))}.discussion-sidebar-item{padding-top:16px;font-size:12px}.discussion-sidebar-item .btn .octicon{margin-right:0}.discussion-sidebar-item .muted-icon{color:var(--fgColor-muted, var(--color-fg-muted))}.discussion-sidebar-item .muted-icon:hover{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none;cursor:pointer}.discussion-sidebar-item+.discussion-sidebar-item{margin-top:16px;border-top:1px solid var(--borderColor-muted, var(--color-border-muted))}.discussion-sidebar-item .select-menu{position:relative}.discussion-sidebar-item .select-menu-modal-holder{top:25px;right:-1px;left:auto}.discussion-sidebar-heading{margin-bottom:8px;font-size:12px;color:var(--fgColor-muted, var(--color-fg-muted))}.discussion-sidebar-toggle{padding:4px 0;margin:-4px 0 4px}.discussion-sidebar-toggle .octicon{float:right;color:var(--fgColor-muted, var(--color-fg-muted))}.discussion-sidebar-toggle:hover{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none;cursor:pointer}.discussion-sidebar-toggle:hover .octicon{color:inherit}button.discussion-sidebar-toggle{display:block;width:100%;font-weight:var(--base-text-weight-semibold, 600);text-align:left;background:none;border:0}.sidebar-progress-bar .progress-bar{height:8px;margin-bottom:2px;border-radius:6px}.sidebar-assignee .css-truncate-target{max-width:110px}.sidebar-assignee .assignee{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-muted, var(--color-fg-muted));vertical-align:middle}.sidebar-assignee .assignee:hover{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none}.sidebar-assignee .reviewers-status-icon{width:14px}.sidebar-assignee .octicon{margin-top:2px}.sidebar-notifications{position:relative}.sidebar-notifications .thread-subscription-status{padding:0;margin:0;border:0}.sidebar-notifications .thread-subscription-status .thread-subscribe-form{display:block}.sidebar-notifications .thread-subscription-status .reason{padding:0;margin:4px 0 0}.participation .participant-avatar{float:left;margin:4px 0 0 4px}.participation a{color:var(--fgColor-muted, var(--color-fg-muted))}.participation a:hover{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none}.participation-avatars{margin-left:-4px}.participation-avatars::before{display:table;content:""}.participation-avatars::after{display:table;clear:both;content:""}.participation-more{float:left;margin:8px 4px 0}.inline-comment-form .form-actions,.timeline-new-comment .form-actions{padding:0 8px 8px}.inline-comment-form::before{display:table;content:""}.inline-comment-form::after{display:table;clear:both;content:""}.inline-comment-form .tabnav-tabs{display:inline-block}.inline-comment-form .form-actions{float:right}.gh-header-actions{float:right;margin-top:4px}.gh-header-actions .btn-sm{float:left;margin-left:4px}.gh-header-actions .btn-sm .octicon{margin-right:0}.gh-header{background-color:var(--bgColor-default, var(--color-canvas-default))}.gh-header .gh-header-sticky{height:1px}.gh-header .gh-header-sticky .meta{font-size:12px}.gh-header .gh-header-sticky .sticky-content,.gh-header .gh-header-sticky .gh-header-shadow{display:none}.gh-header .gh-header-sticky.is-stuck{z-index:110;height:60px}.gh-header .gh-header-sticky.is-stuck .sticky-content{display:block}.gh-header .gh-header-sticky.is-stuck .css-truncate-target{max-width:150px}.gh-header .gh-header-sticky.is-stuck+.gh-header-shadow{position:fixed;top:0;right:0;left:0;z-index:109;display:block;height:60px;content:"";background-color:var(--bgColor-default, var(--color-canvas-default));border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.gh-header .gh-header-edit{display:none}.gh-header .gh-header-meta .base-ref{display:inline-block}.gh-header .gh-header-meta .commit-ref-dropdown{display:none}.gh-header.open .gh-header-show{display:none}.gh-header.open .gh-header-edit{display:block}.gh-header.open .gh-header-meta .base-ref{display:none}.gh-header.open .gh-header-meta .commit-ref-dropdown{display:inline-block;margin-top:-4px;vertical-align:top}.gh-header-title{margin-right:150px;margin-bottom:0;font-weight:var(--base-text-weight-normal, 400);line-height:1.125;word-wrap:break-word}.gh-header-no-access .gh-header-title{margin-right:0}.gh-header-number{font-weight:var(--base-text-weight-light, 300);color:var(--fgColor-muted, var(--color-fg-muted))}.gh-header-meta{padding-bottom:8px;margin-top:8px;font-size:14px;color:var(--fgColor-muted, var(--color-fg-muted));border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.gh-header.issue .gh-header-meta{margin-bottom:16px}.gh-header.pull .gh-header-meta{padding-bottom:0;border-bottom:0}.gh-header-meta .commit-ref .css-truncate-target,.gh-header-meta .commit-ref:hover .css-truncate-target{max-width:80vw}.gh-header-meta .State{margin-right:8px}.gh-header-meta .avatar{float:left;margin-top:-4px;margin-right:4px}.timeline-comment-wrapper{position:relative;padding-left:56px;margin-top:16px;margin-bottom:16px}.timeline-comment-avatar{float:left;margin-left:-56px;border-radius:6px}.timeline-comment-avatar .avatar{width:40px;height:40px}.timeline-comment-avatar .avatar-child{width:20px;height:20px}.timeline-comment{position:relative;color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.timeline-comment.will-transition-once{transition:border-color .65s ease-in-out}.timeline-comment.will-transition-once .timeline-comment-header{transition:background-color .65s ease,border-bottom-color .65s ease-in-out}.timeline-comment.will-transition-once::before,.timeline-comment.will-transition-once::after{transition:border-right-color .65s ease-in-out}.timeline-comment.current-user{border-color:var(--color-accent-muted)}.timeline-comment.current-user .timeline-comment-header{background-color:var(--color-accent-subtle);border-bottom-color:var(--color-accent-muted)}.timeline-comment.current-user .Label{border-color:var(--color-accent-muted)}.timeline-comment.current-user .previewable-comment-form .comment-form-head.tabnav{color:var(--color-accent-muted);background-color:var(--color-accent-subtle);border-bottom-color:var(--color-accent-muted)}.timeline-comment.unread-item,.timeline-comment.is-internal{border-color:var(--color-attention-muted)}.timeline-comment.unread-item .timeline-comment-header,.timeline-comment.is-internal .timeline-comment-header{background-color:var(--color-attention-subtle);border-bottom-color:var(--color-attention-muted)}.timeline-comment.unread-item .Label,.timeline-comment.is-internal .Label{border-color:var(--color-attention-muted)}.timeline-comment.unread-item .previewable-comment-form .comment-form-head.tabnav,.timeline-comment.is-internal .previewable-comment-form .comment-form-head.tabnav{color:var(--color-attention-muted);background-color:var(--color-attention-subtle);border-bottom-color:var(--color-attention-muted)}.timeline-comment:empty{display:none}.timeline-comment .comment+.comment{border-top:1px solid var(--borderColor-default, var(--color-border-default))}.timeline-comment .comment+.comment::before,.timeline-comment .comment+.comment::after{display:none}.timeline-comment .comment+.comment .timeline-comment-header{border-top-left-radius:0;border-top-right-radius:0}.timeline-comment--caret::after,.timeline-comment--caret::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.timeline-comment--caret::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-canvas-subtle), var(--color-canvas-subtle))}.timeline-comment--caret::before{background-color:var(--color-border-default)}.is-pending .timeline-comment--caret::after,.is-pending .timeline-comment--caret::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.is-pending .timeline-comment--caret::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-attention-subtle), var(--color-attention-subtle))}.is-pending .timeline-comment--caret::before{background-color:var(--color-attention-emphasis)}.timeline-comment--caret.current-user::after,.timeline-comment--caret.current-user::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.timeline-comment--caret.current-user::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-accent-subtle), var(--color-accent-subtle))}.timeline-comment--caret.current-user::before{background-color:var(--color-accent-muted)}.timeline-comment--caret.unread-item::after,.timeline-comment--caret.unread-item::before,.timeline-comment--caret.is-internal::after,.timeline-comment--caret.is-internal::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.timeline-comment--caret.unread-item::after,.timeline-comment--caret.is-internal::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-attention-subtle), var(--color-attention-subtle))}.timeline-comment--caret.unread-item::before,.timeline-comment--caret.is-internal::before{background-color:var(--color-attention-muted)}.timeline-comment--caret.timeline-comment--caret-nw::before,.timeline-comment--caret.timeline-comment--caret-nw::after{transform:rotate(90deg)}.timeline-comment--caret.timeline-comment--caret-nw::before{top:-12px;left:12px}.timeline-comment--caret.timeline-comment--caret-nw::after{top:-10px;left:11px}.page-responsive .timeline-comment--caret::before,.page-responsive .timeline-comment--caret::after{display:none}@media(min-width: 768px){.page-responsive .timeline-comment--caret::before,.page-responsive .timeline-comment--caret::after{display:block}}:target .timeline-comment--caret::before{background-color:var(--bgColor-accent-emphasis, var(--color-accent-fg))}:target .timeline-comment--caret::after{margin-left:2px !important}:target .timeline-comment{border-color:var(--borderColor-accent-emphasis, var(--color-accent-fg));outline:none !important;box-shadow:0 0 0 1px var(--color-accent-fg) !important}.review-comment:target{border-radius:6px;outline:none !important;box-shadow:0 0 0 1px var(--color-accent-fg) !important}.timeline-comment-header{display:flex;align-items:center;padding-right:16px;padding-left:16px;color:var(--fgColor-muted, var(--color-fg-muted));flex-direction:row-reverse;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom:1px solid var(--borderColor-default, var(--color-border-default));border-top-left-radius:6px;border-top-right-radius:6px}.timeline-comment-header:only-child{border-bottom:0;border-radius:6px}.timeline-comment-header .author{color:var(--fgColor-muted, var(--color-fg-muted))}.timeline-comment-header code{word-break:break-all}.timeline-comment-header-text{min-width:0;padding-top:8px;padding-bottom:8px;margin-bottom:1px;flex:1 1 auto}.timeline-comment-header-text code a{color:var(--fgColor-muted, var(--color-fg-muted))}.timeline-comment-actions{float:right;margin-left:8px}.timeline-comment-actions .show-more-popover.dropdown-menu-sw{right:-6px;margin-top:-4px}.timeline-comment-action{display:inline-block;padding:8px 4px;color:var(--fgColor-muted, var(--color-fg-muted))}.timeline-comment-action:hover,.timeline-comment-action:focus{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none;opacity:1}.timeline-comment-action .octicon-check{height:16px}.timeline-comment-action.disabled{color:var(--fgColor-muted, var(--color-fg-muted));cursor:default}.timeline-comment-action.disabled:hover{color:var(--fgColor-muted, var(--color-fg-muted))}.timeline-new-comment{margin-bottom:0}.timeline-new-comment .comment-form-head{margin-bottom:8px}.timeline-new-comment .previewable-comment-form .comment-body{padding-top:0}.comment-form-head .toolbar-commenting{float:right}.discussion-item-icon{float:left;width:32px;height:32px;margin-top:-4px;margin-left:-39px;line-height:28px;color:var(--fgColor-muted, var(--color-fg-muted));text-align:center;background-color:var(--timelineBadge-bgColor, var(--color-timeline-badge-bg));border:2px solid var(--bgColor-default, var(--color-canvas-default));border-radius:50%}.discussion-item-header{color:var(--fgColor-muted, var(--color-fg-muted));word-wrap:break-word}.discussion-item-header .discussion-item-private{vertical-align:-1px}.discussion-item-header:last-child{padding-bottom:0}.discussion-item-header .commit-ref{font-size:85%;vertical-align:baseline}.discussion-item-header .btn-outline{float:right;padding:4px 8px;margin-top:-4px;margin-left:8px}.discussion-item-private{color:var(--fgColor-muted, var(--color-fg-muted))}.previewable-comment-form .comment-form-head.tabnav{padding:8px 8px 0;background:var(--bgColor-muted, var(--color-canvas-subtle));border-radius:6px 6px 0 0}.page-responsive .previewable-comment-form .comment-form-head.tabnav .toolbar-commenting{background:var(--bgColor-default, var(--color-canvas-default))}@media(min-width: 1012px){.page-responsive .previewable-comment-form .comment-form-head.tabnav .toolbar-commenting{background:transparent}}@media(min-width: 768px){.page-responsive .previewable-comment-form .comment-form-head.tabnav{background:var(--bgColor-muted, var(--color-canvas-subtle))}}.previewable-comment-form .comment-body{padding-top:8px;padding-right:8px;padding-bottom:8px;padding-left:8px;background-color:transparent;border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.previewable-comment-form .timeline-comment .timeline-comment-actions{display:none}.new-discussion-timeline .composer .timeline-comment{margin-bottom:8px}.new-discussion-timeline .composer .comment-form-head.tabnav{padding-top:0;background-color:var(--bgColor-default, var(--color-canvas-default))}.composer.composer-responsive{padding-left:0}.composer.composer-responsive .discussion-topic-header{padding:0}.composer.composer-responsive .timeline-comment{border:0}.composer.composer-responsive .timeline-comment::before,.composer.composer-responsive .timeline-comment::after{display:none}.composer.composer-responsive .previewable-comment-form .write-content{margin:0}@media(min-width: 768px){.composer.composer-responsive{padding-left:56px}.composer.composer-responsive .timeline-comment{border:1px solid var(--borderColor-default, var(--color-border-default))}.composer.composer-responsive .timeline-comment::after,.composer.composer-responsive .timeline-comment::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.composer.composer-responsive .timeline-comment::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-canvas-default), var(--color-canvas-default))}.composer.composer-responsive .timeline-comment::before{background-color:var(--color-border-default)}}.discussion-timeline-actions{background-color:var(--bgColor-default, var(--color-canvas-default));border-top:2px solid var(--borderColor-default, var(--color-border-default))}.discussion-timeline-actions .merge-pr{padding-top:0;border-top:0}.discussion-timeline-actions .thread-subscription-status{margin-top:16px}.pagination-loader-container{background-color:var(--bgColor-default, var(--color-canvas-default));background-image:url("/images/modules/pulls/progressive-disclosure-line.svg");background-repeat:repeat-x;background-position:center;background-size:16px}[data-color-mode=light][data-light-theme*=dark] .pagination-loader-container,[data-color-mode=dark][data-dark-theme*=dark] .pagination-loader-container{background-image:url("/images/modules/pulls/progressive-disclosure-line-dark.svg")}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark] .pagination-loader-container{background-image:url("/images/modules/pulls/progressive-disclosure-line-dark.svg")}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark] .pagination-loader-container{background-image:url("/images/modules/pulls/progressive-disclosure-line-dark.svg")}}:target .timeline-comment-group .timeline-comment .timeline-comment-group .timeline-comment{box-shadow:none !important}.is-pending .form-actions{margin-right:8px;margin-bottom:4px}.is-pending .file,.is-pending .file-header,.is-pending .tabnav-tab.selected,.is-pending .comment-form-head.tabnav{border-color:var(--borderColor-attention-emphasis, var(--color-attention-emphasis))}.is-pending .file-header,.is-pending .comment-form-head.tabnav{background-color:var(--bgColor-attention-muted, var(--color-attention-subtle))}.discussion-item-icon-gray{background-color:var(--timelineBadge-bgColor, var(--color-timeline-badge-bg)) !important}.footer-octicon{color:var(--fgColor-muted, var(--color-fg-subtle))}.footer-octicon:hover{color:var(--fgColor-muted, var(--color-fg-muted))}.user-mention,.team-mention{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default));white-space:nowrap}.Popover .user-mention,.Popover .team-mention{white-space:normal}@media(max-width: 543px){.notifications-component-menu-modal{margin:calc(10vh - 16px) 0}}@media(min-width: 544px){.notifications-component-menu-modal,.notifications-component-dialog,.notifications-component-dialog-modal{width:100%}}@media(min-width: 768px){.notifications-component-menu-modal,.notifications-component-dialog,.notifications-component-dialog-modal{min-width:300px}}.notifications-component-dialog:not([hidden])+.notifications-component-dialog-overlay{position:fixed;top:0;right:0;bottom:0;left:0;z-index:80;display:block;cursor:default;content:" ";background:transparent;background:var(--overlay-backdrop-bgColor, var(--color-primer-canvas-backdrop))}@media(min-width: 544px){.notifications-component-dialog:not([hidden])+.notifications-component-dialog-overlay{display:none}}.notifications-component-dialog{z-index:99;animation:none}@keyframes notifications-component-dialog-animation--sm{0%{opacity:0;transform:translateX(16px)}}@media(min-width: 544px){.notifications-component-dialog{position:absolute;top:auto;right:auto;bottom:auto;left:auto;max-height:none;padding-top:0;margin:0;transform:none}}.notifications-component-dialog .notifications-component-dialog-modal{animation:none}.pagehead{position:relative;padding-top:24px;padding-bottom:24px;margin-bottom:24px;border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.pagehead.admin{background:url("/images/modules/pagehead/background-yellowhatch-v3.png") 0 0 repeat-x}.pagehead ul.pagehead-actions{position:relative;z-index:31;float:right;margin:0}.pagehead .path-divider{margin:0 .25em}.pagehead h1{min-height:32px;margin-top:0;margin-bottom:0;font-size:20px;font-weight:var(--base-text-weight-normal, 400)}.pagehead h1 .avatar{margin-top:-2px;margin-right:8px;margin-bottom:-2px}.pagehead .underline-nav{height:69px;margin-top:-16px;margin-bottom:-16px}.pagehead-heading{color:inherit}.pagehead-actions>li{float:left;margin:0 8px 0 0;font-size:12px;color:var(--fgColor-default, var(--color-fg-default));list-style-type:none}.pagehead-actions>li:last-child{margin-right:0}.pagehead-actions .octicon-mute{color:var(--fgColor-danger, var(--color-danger-fg))}.pagehead-actions .select-menu{position:relative}.pagehead-actions .select-menu::before{display:table;content:""}.pagehead-actions .select-menu::after{display:table;clear:both;content:""}.pagehead-actions .select-menu-modal-holder{top:100%}.pagehead-tabs-item{float:left;padding:8px 16px 12px;color:var(--fgColor-muted, var(--color-fg-muted));white-space:nowrap;border:solid transparent;border-width:3px 1px 1px;border-radius:6px 6px 0 0}.pagehead-tabs-item .octicon{color:var(--fgColor-muted, var(--color-fg-muted))}.pagehead-tabs-item:hover{color:var(--fgColor-default, var(--color-fg-default));text-decoration:none}.pagehead-tabs-item.selected{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));border-color:var(--borderColor-severe-emphasis, var(--color-severe-emphasis)) var(--borderColor-default, var(--color-border-default)) transparent}.pagehead-tabs-item.selected>.octicon{color:inherit}.reponav{position:relative;top:1px;margin-top:-4px}.reponav::before{display:table;content:""}.reponav::after{display:table;clear:both;content:""}.reponav-item{float:left;padding:8px 16px 8px;color:var(--fgColor-muted, var(--color-fg-muted));white-space:nowrap;border:solid transparent;border-width:3px 1px 1px;border-radius:6px 6px 0 0}.reponav-item .octicon{color:var(--fgColor-muted, var(--color-fg-muted))}.reponav-item:hover,.reponav-item:focus{color:var(--fgColor-default, var(--color-fg-default));text-decoration:none}.reponav-item.selected{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default));border-color:var(--borderColor-severe-emphasis, var(--color-severe-emphasis)) var(--borderColor-default, var(--color-border-default)) transparent}.reponav-item.selected .octicon{color:inherit}.reponav-wrapper{position:relative;z-index:2;overflow-y:hidden;background-color:var(--bgColor-neutral-emphasis, var(--color-neutral-emphasis))}.reponav-wrapper .reponav{top:0;padding-right:8px;padding-left:8px;margin-top:0;-webkit-overflow-scrolling:touch;overflow-x:auto;color:rgba(255,255,255,.75)}.reponav-wrapper .reponav-item{display:inline-block;float:none;padding:4px 8px 16px;color:var(--fgColor-muted, var(--color-fg-muted));border:0}.reponav-wrapper .reponav-item.selected{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default));background-color:transparent;border:0}@media(max-width: 768px){.PageLayout--responsive-separateRegions.PageLayout--responsive-primary-pane .ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger){background-color:transparent}.PageLayout--responsive-separateRegions.PageLayout--responsive-primary-pane .ActionList-item.ActionList-item--navActive:not(.ActionList-item--subItem) .ActionList-item-label{font-weight:var(--base-text-weight-normal, 400)}.PageLayout--responsive-separateRegions.PageLayout--responsive-primary-pane .ActionList-item--navActive::after{display:none}.PageLayout--responsive-separateRegions.PageLayout--responsive-primary-pane .ActionList-item.ActionList-item--navActive:not(.ActionList-item--danger):hover{background-color:var(--control-transparent-bgColor-hover, var(--color-action-list-item-default-hover-bg))}}.steps{display:table;width:100%;padding:0;margin:32px auto 0;overflow:hidden;list-style:none;border:1px solid #dfe2e5;border-radius:6px;box-shadow:0 1px 3px rgba(27,31,35,.05)}.steps li{display:table-cell;width:33.3%;padding:8px 16px;color:#c6cbd1;cursor:default;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-left:1px solid #dfe2e5}.steps li.current{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default))}.steps li.current .octicon{color:var(--fgColor-accent, var(--color-accent-fg))}.steps li .octicon{float:left;margin-right:16px;margin-bottom:4px}.steps li .step{display:block}.steps li:first-child{border-left:0}.steps .complete{color:var(--fgColor-muted, var(--color-fg-muted))}.steps .complete .octicon{color:var(--fgColor-success, var(--color-success-fg))}.prose-diff .anchor{display:none}.prose-diff .show-rich-diff{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none;cursor:pointer}.prose-diff .show-rich-diff:hover{text-decoration:underline}.prose-diff.collapsed .rich-diff-level-zero.expandable{cursor:pointer}.prose-diff.collapsed .rich-diff-level-zero.expandable .vicinity{display:block}.prose-diff.collapsed .rich-diff-level-zero.expandable .unchanged:not(.vicinity){display:none}.prose-diff.collapsed .rich-diff-level-zero.expandable .octicon{display:block;margin:16px auto;color:var(--fgColor-muted, var(--color-fg-muted))}.prose-diff.collapsed .rich-diff-level-zero.expandable:hover .octicon{color:var(--fgColor-muted, var(--color-fg-muted))}.prose-diff.collapsed .rich-diff-level-zero.expandable:only-child::before{font-size:16px;color:var(--fgColor-muted, var(--color-fg-muted));content:"Sorry, no visible changes to display."}.prose-diff.collapsed .rich-diff-level-zero.expandable:only-child:hover::before{color:var(--fgColor-default, var(--color-fg-default))}.prose-diff.collapsed .rich-diff-level-zero.expandable>.removed,.prose-diff.collapsed .rich-diff-level-zero.expandable>del{display:none;text-decoration:none}.prose-diff .markdown-body{padding:32px;padding-left:16px}.prose-diff .markdown-body>ins{box-shadow:inset 4px 0 0 var(--borderColor-success-muted, var(--color-success-muted))}.prose-diff .markdown-body>del{text-decoration:none;box-shadow:inset 4px 0 0 var(--borderColor-danger-muted, var(--color-danger-muted))}.prose-diff .markdown-body>ins,.prose-diff .markdown-body>del{display:block;border-radius:0}.prose-diff .markdown-body>ins>.rich-diff-level-zero,.prose-diff .markdown-body>ins>.rich-diff-level-one,.prose-diff .markdown-body>del>.rich-diff-level-zero,.prose-diff .markdown-body>del>.rich-diff-level-one{margin-left:16px}.prose-diff .markdown-body>ins:first-child *,.prose-diff .markdown-body>del:first-child *{margin-top:0}.prose-diff .rich-diff-level-zero.added{box-shadow:inset 4px 0 0 var(--borderColor-success-muted, var(--color-success-muted))}.prose-diff .rich-diff-level-zero.removed{box-shadow:inset 4px 0 0 var(--borderColor-danger-muted, var(--color-danger-muted))}.prose-diff .rich-diff-level-zero.changed{box-shadow:inset 4px 0 0 var(--borderColor-attention-muted, var(--color-attention-muted))}.prose-diff .rich-diff-level-zero.unchanged,.prose-diff .rich-diff-level-zero.vicinity{margin-left:16px}.prose-diff .rich-diff-level-zero.added,.prose-diff .rich-diff-level-zero.removed,.prose-diff .rich-diff-level-zero.changed{display:block;border-radius:0}.prose-diff .rich-diff-level-zero.added>.rich-diff-level-one,.prose-diff .rich-diff-level-zero.removed>.rich-diff-level-one,.prose-diff .rich-diff-level-zero.changed>.rich-diff-level-one{margin-left:16px}.prose-diff .rich-diff-level-zero.added:first-child *,.prose-diff .rich-diff-level-zero.removed:first-child *,.prose-diff .rich-diff-level-zero.changed:first-child *{margin-top:0}.prose-diff :not(.changed)>:not(.github-user-ins):not(.github-user-del)>.removed,.prose-diff :not(.changed)>:not(.github-user-ins):not(.github-user-del)>del{text-decoration:none}.prose-diff .changed del,.prose-diff .changed del pre,.prose-diff .changed del code,.prose-diff .changed del>div,.prose-diff .changed .removed,.prose-diff .changed .removed pre,.prose-diff .changed .removed code,.prose-diff .changed .removed>div{color:var(--fgColor-default, var(--color-fg-default));text-decoration:line-through;background:var(--bgColor-danger-muted, var(--color-danger-subtle))}.prose-diff .changed ins,.prose-diff .changed ins code,.prose-diff .changed ins pre,.prose-diff .changed .added{color:var(--fgColor-default, var(--color-fg-default));background:var(--bgColor-success-muted, var(--color-success-subtle));border-bottom:1px solid var(--borderColor-success-muted, var(--color-success-muted))}.prose-diff>.markdown-body .github-user-ins{text-decoration:underline}.prose-diff>.markdown-body .github-user-del{text-decoration:line-through}.prose-diff>.markdown-body li ul.added{background:var(--bgColor-success-muted, var(--color-success-subtle))}.prose-diff>.markdown-body li ul.removed{color:var(--fgColor-default, var(--color-fg-default));background:var(--bgColor-danger-muted, var(--color-danger-subtle))}.prose-diff>.markdown-body li ul.removed:not(.github-user-ins){text-decoration:line-through}.prose-diff>.markdown-body li.added.moved-up .octicon,.prose-diff>.markdown-body li.added.moved-down .octicon{margin-right:4px;margin-left:4px;color:var(--fgColor-muted, var(--color-fg-muted))}.prose-diff>.markdown-body li.added.moved{background:var(--bgColor-attention-muted, var(--color-attention-subtle))}.prose-diff>.markdown-body li.removed.moved{display:none}.prose-diff>.markdown-body pre{padding:8px 16px}.prose-diff>.markdown-body th.changed,.prose-diff>.markdown-body td.changed{background:var(--bgColor-attention-muted, var(--color-attention-subtle));border-left-color:var(--borderColor-default, var(--color-border-default))}.prose-diff>.markdown-body :not(li.moved).removed{color:var(--fgColor-default, var(--color-fg-default));text-decoration:line-through;background:var(--bgColor-danger-muted, var(--color-danger-subtle))}.prose-diff>.markdown-body :not(.github-user-ins):not(li.moved).removed{text-decoration:line-through}.prose-diff>.markdown-body :not(li.moved).added,.prose-diff>.markdown-body li:not(.moved).added{background:var(--bgColor-success-muted, var(--color-success-subtle))}.prose-diff>.markdown-body :not(.github-user-del):not(li.moved).added li:not(.moved):not(.github-user-del).added{text-decoration:none}.prose-diff>.markdown-body li:not(.moved).removed{color:var(--fgColor-default, var(--color-fg-default));background:var(--bgColor-danger-muted, var(--color-danger-subtle))}.prose-diff>.markdown-body li:not(.moved):not(.github-user-ins).removed{text-decoration:line-through}.prose-diff>.markdown-body .added,.prose-diff>.markdown-body ins+.added,.prose-diff>.markdown-body ins{border-top:0;border-bottom:0}.prose-diff>.markdown-body .added:not(.github-user-del):not(.github-user-ins),.prose-diff>.markdown-body ins+.added:not(.github-user-del):not(.github-user-ins),.prose-diff>.markdown-body ins:not(.github-user-del):not(.github-user-ins){text-decoration:none}.prose-diff>.markdown-body img.added,.prose-diff>.markdown-body img.removed{border-style:solid;border-width:1px}.prose-diff>.markdown-body ins pre:not(.github-user-del):not(.github-user-ins),.prose-diff>.markdown-body ins code:not(.github-user-del):not(.github-user-ins),.prose-diff>.markdown-body ins>div:not(.github-user-del):not(.github-user-ins){text-decoration:none}.prose-diff>.markdown-body ul>ins,.prose-diff>.markdown-body ul>del{display:block;padding:0}.prose-diff>.markdown-body .added>li,.prose-diff>.markdown-body .removed>li{margin-top:0;margin-bottom:0}span.changed_tag,em.changed_tag,strong.changed_tag,b.changed_tag,i.changed_tag,code.changed_tag{border-bottom:1px dotted var(--borderColor-default, var(--color-border-default));border-radius:0}a.added_href,a.changed_href,span.removed_href{border-bottom:1px dotted var(--borderColor-default, var(--color-border-default));border-radius:0}.diff-view .file-type-prose .rich-diff{display:none}.diff-view .display-rich-diff .rich-diff{display:block}.diff-view .display-rich-diff .file-diff{display:none}.prose-diff.no-level-zero-box-shadow .rich-diff-level-zero.added,.prose-diff.no-level-zero-box-shadow .rich-diff-level-zero.removed,.prose-diff.no-level-zero-box-shadow .rich-diff-level-zero.changed{box-shadow:none}.protip{margin-top:16px;color:var(--fgColor-muted, var(--color-fg-muted));text-align:center}.protip strong{color:var(--fgColor-default, var(--color-fg-default))}.protip code{padding:2px;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-radius:6px}.add-reactions-options-item{margin-top:-1px;margin-right:-1px;line-height:29px;border:1px solid transparent}.add-reactions-options-item .emoji{display:inline-block;transition:transform .15s cubic-bezier(0.2, 0, 0.13, 2)}.add-reactions-options-item:hover .emoji,.add-reactions-options-item:focus .emoji{text-decoration:none !important;transform:scale(1.2) !important}.add-reactions-options-item:active{background-color:var(--bgColor-accent-muted, var(--color-accent-subtle))}.page-responsive .add-reactions-options-item{height:20vw}@media(min-width: 544px){.page-responsive .add-reactions-options-item{height:auto}}.comment-reactions{display:none}.comment-reactions::before{display:table;content:""}.comment-reactions::after{display:table;clear:both;content:""}.page-responsive .comment-reactions{display:none}@media(min-width: 768px){.page-responsive .comment-reactions{display:none}.page-responsive .comment-reactions.has-reactions{display:flex}}.comment-reactions.has-reactions{display:flex}.comment-reactions.has-reactions:not(.social-reactions){border-top:1px solid var(--borderColor-default, var(--color-border-default))}.comment-reactions .user-has-reacted{background-color:var(--bgColor-accent-muted, var(--color-accent-subtle))}.reactions-container .user-has-reacted{background-color:var(--bgColor-accent-muted, var(--color-accent-subtle))}[data-color-mode=light][data-light-theme*=dark],[data-color-mode=dark][data-dark-theme*=dark]{--color-social-reaction-bg-hover:var(--color-scale-gray-7);--color-social-reaction-bg-reacted-hover:var(--color-scale-blue-8)}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark]{--color-social-reaction-bg-hover:var(--color-scale-gray-7);--color-social-reaction-bg-reacted-hover:var(--color-scale-blue-8)}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark]{--color-social-reaction-bg-hover:var(--color-scale-gray-7);--color-social-reaction-bg-reacted-hover:var(--color-scale-blue-8)}}:root,[data-color-mode=light][data-light-theme*=light],[data-color-mode=dark][data-dark-theme*=light]{--color-social-reaction-bg-hover:var(--color-scale-gray-1);--color-social-reaction-bg-reacted-hover:var(--color-scale-blue-1)}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=light]{--color-social-reaction-bg-hover:var(--color-scale-gray-1);--color-social-reaction-bg-reacted-hover:var(--color-scale-blue-1)}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=light]{--color-social-reaction-bg-hover:var(--color-scale-gray-1);--color-social-reaction-bg-reacted-hover:var(--color-scale-blue-1)}}.social-reaction-summary-item+.social-reaction-summary-item{margin-left:8px}.social-reactions .comment-body{margin-left:16px !important}.social-button-emoji{display:inline-block;width:16px;height:16px;font-size:1em !important;line-height:1.25;vertical-align:-1px}.social-reaction-summary-item{height:26px;padding:0 4px !important;margin-right:0;font-size:12px;line-height:26px;background-color:transparent;border:1px solid var(--borderColor-default, var(--color-border-default, #d2dff0));border-radius:100px}.social-reaction-summary-item:focus,.social-reaction-summary-item:focus-visible{border-radius:100px !important}.social-reaction-summary-item:focus{border-color:var(--focus-outlineColor, var(--color-accent-fg));outline:none;box-shadow:inset 0 0 0 1px var(--focus-outlineColor, var(--color-accent-fg))}.social-reaction-summary-item:focus:not(:focus-visible){border-color:transparent;border-color:var(--focus-outlineColor, var(--color-accent-fg));outline:none;box-shadow:inset 0 0 0 1px transparent}.social-reaction-summary-item:focus-visible{border-color:var(--focus-outlineColor, var(--color-accent-fg));outline:none;box-shadow:inset 0 0 0 1px var(--focus-outlineColor, var(--color-accent-fg))}.social-reaction-summary-item.user-has-reacted{background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border:1px solid var(--borderColor-accent-emphasis, var(--color-accent-emphasis)) !important}.social-reaction-summary-item.user-has-reacted:hover{background-color:var(--color-social-reaction-bg-reacted-hover) !important}.social-reaction-summary-item>span{height:24px;padding:0 4px;margin-left:2px}.social-reaction-summary-item:hover{background-color:var(--color-social-reaction-bg-hover)}.reaction-dropdown-button{color:var(--fgColor-muted, var(--color-fg-muted))}.reaction-dropdown-button:hover{color:var(--fgColor-accent, var(--color-accent-fg))}.reaction-dropdown-button--inline{width:26px;height:26px}.reaction-dropdown-button--inline:hover{background-color:var(--button-default-bgColor-hover, var(--color-btn-hover-bg)) !important;border-color:var(--button-default-borderColor-hover, var(--color-btn-hover-border)) !important}.reaction-dropdown-button:disabled{color:var(--fgColor-disabled, var(--color-primer-fg-disabled)) !important;pointer-events:none}.reactions-with-gap .comment .comment-reactions{margin-left:16px;border-top:0 !important}.new-reactions-dropdown .dropdown-menu-reactions{width:auto;padding:0 2px}.new-reactions-dropdown .dropdown-menu-reactions::before,.new-reactions-dropdown .dropdown-menu-reactions::after{background-color:transparent;border:0}.new-reactions-dropdown .dropdown-item-reaction{width:32px;height:32px;padding:4px;margin:4px 2px}.new-reactions-dropdown .dropdown-item-reaction.user-has-reacted{background-color:var(--bgColor-accent-muted, var(--color-accent-subtle))}.new-reactions-dropdown .dropdown-item-reaction:hover{background-color:var(--button-default-bgColor-hover, var(--color-btn-hover-bg))}.RecentBranches{background-color:var(--bgColor-attention-muted, var(--color-attention-subtle));border:1px solid var(--borderColor-attention-emphasis, var(--color-attention-emphasis));border-radius:6px}.RecentBranches-item{line-height:28px;color:var(--fgColor-default, var(--color-fg-default))}.RecentBranches-item+.RecentBranches-item{border-top:1px solid var(--borderColor-attention-emphasis, var(--color-attention-emphasis))}.RecentBranches-item-link{color:var(--fgColor-default, var(--color-fg-default))}.RecentBranches-item-link.css-truncate-target{max-width:400px}.render-container{padding:32px;line-height:0;text-align:center;background:var(--bgColor-muted, var(--color-canvas-subtle));border-bottom-right-radius:6px;border-bottom-left-radius:6px}.render-container .render-viewer{display:block;width:1px;height:1px;border:0}.render-container .octospinner{display:none}.render-container .render-viewer-error,.render-container .render-viewer-fatal,.render-container .render-viewer-invalid,.render-container .render-fullscreen{display:none}.render-container.is-render-automatic .octospinner{display:inline-block}.render-container.is-render-requested .octospinner{display:inline-block}.render-container.is-render-requested.is-render-failed .render-viewer-error{display:inline-block}.render-container.is-render-requested.is-render-failed .render-viewer,.render-container.is-render-requested.is-render-failed .render-viewer-fatal,.render-container.is-render-requested.is-render-failed .render-viewer-invalid,.render-container.is-render-requested.is-render-failed .octospinner{display:none}.render-container.is-render-requested.is-render-failed-fatal .render-viewer-fatal{display:inline-block}.render-container.is-render-requested.is-render-failed-fatal .render-viewer,.render-container.is-render-requested.is-render-failed-fatal .render-viewer-error,.render-container.is-render-requested.is-render-failed-fatal .render-viewer-invalid,.render-container.is-render-requested.is-render-failed-fatal .octospinner{display:none}.render-container.is-render-requested.is-render-failed-invalid .render-viewer-invalid{display:inline-block}.render-container.is-render-requested.is-render-failed-invalid .render-viewer,.render-container.is-render-requested.is-render-failed-invalid .render-viewer-error,.render-container.is-render-requested.is-render-failed-invalid .render-viewer-fatal,.render-container.is-render-requested.is-render-failed-invalid .octospinner{display:none}.render-container.is-render-ready.is-render-requested:not(.is-render-failed){height:500px;padding:0;background:none}.render-container.is-render-ready.is-render-requested:not(.is-render-failed) .render-viewer{width:100%;height:100%}.render-container.is-render-ready.is-render-requested:not(.is-render-failed) .render-fullscreen{display:flex}.render-container.is-render-ready.is-render-requested:not(.is-render-failed) .render-viewer-error,.render-container.is-render-ready.is-render-requested:not(.is-render-failed) .render-viewer-fatal,.render-container.is-render-ready.is-render-requested:not(.is-render-failed) .octospinner{display:none}.render-needs-enrichment{margin-bottom:16px}.render-needs-enrichment .render-full-screen{width:100%;height:auto;padding:16px;overflow:auto}.render-needs-enrichment .render-full-screen-close{top:0;right:0;padding:4px}.render-needs-enrichment .details{margin-bottom:0}.render-needs-enrichment .render-plaintext-hidden{display:none}.render-notice{padding:16px 16px;font-size:14px;color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-color:var(--borderColor-muted, var(--color-border-subtle))}relative-time{white-space:nowrap}.js-inline-math>mjx-container{overflow-x:auto;overflow-y:hidden}math-renderer mjx-labels{right:0;left:auto}.Skeleton{color:rgba(0,0,0,0);background-image:linear-gradient(270deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.1));background-size:400% 100%;animation:skeleton-loading 8s ease-in-out infinite}.Skeleton *{visibility:hidden}.Skeleton--text{clip-path:inset(4px 0 4px 0 round 3px 3px 3px 3px)}.is-error .Skeleton{display:none}@keyframes skeleton-loading{0%{background-position:200% 0}100%{background-position:-200% 0}}.authors-2 .AvatarStack{min-width:36px !important}.authors-3 .AvatarStack{min-width:46px !important}[aria-selected=true] .AvatarStack-body,.navigation-focus .AvatarStack-body{background:#f6fbff}.tracked-in-parent-pill{position:relative;cursor:default}.tracked-in-parent-pill-truncated{position:absolute;left:100%;display:none;white-space:nowrap;background:var(--bgColor-default, var(--color-canvas-default));border-left-width:0 !important;border-top-left-radius:0 !important;border-bottom-left-radius:0 !important}.tracked-in-parent-pill:hover .tracked-in-parent-pill-truncated{display:block}.wizard-content.horizontal{flex-direction:column}.wizard-content.horizontal .wizard-horizontal-steps{width:30%}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-badge{margin:0;color:var(--fgColor-muted, var(--color-fg-subtle));background-color:var(--bgColor-inset, var(--color-canvas-inset));border-color:var(--borderColor-neutral-emphasis, var(--color-fg-subtle))}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-badge.current{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis));border-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis))}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-bar{border-color:var(--borderColor-neutral-emphasis, var(--color-fg-subtle))}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-bar.complete{border-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis))}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-badge.complete{display:none}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-badge-check{display:none !important}.wizard-content.horizontal .wizard-horizontal-steps .wizard-step-badge-check.complete{display:inherit !important;width:32px !important;height:32px !important}.wizard-content.horizontal .wizard-step[data-single-page-wizard-last-step=true] .wizard-step-buttons{align-items:center;justify-content:center}.wizard-content.horizontal .wizard-step-item{margin-left:0 !important;flex-direction:column !important}.wizard-content.horizontal .wizard-step-item::before,.wizard-content.horizontal .wizard-step-item .wizard-step-badge,.wizard-content.horizontal .wizard-step-item .wizard-step-icon,.wizard-content.horizontal .wizard-step-item .wizard-step-header{display:none !important}.wizard-content.horizontal .wizard-step-item .wizard-step-container::before,.wizard-content.horizontal .wizard-step-item .wizard-step-container::after{display:none !important}.wizard-step-item{position:relative;padding:8px 0;margin-left:16px;flex-direction:row}.wizard-step-item::before{position:absolute;top:32px;bottom:0;left:0;display:block;width:2px;height:100%;content:"";background-color:var(--borderColor-default, var(--color-border-default))}.wizard-step-badge{position:relative;z-index:1;display:flex;width:32px;height:32px;margin-right:8px;margin-left:-16px;color:var(--fgColor-default, var(--color-fg-default));align-items:center;background-color:var(--borderColor-default, var(--color-border-default));border:1px solid var(--bgColor-default, var(--color-canvas-default));border-radius:50%;justify-content:center;flex-shrink:0}.wizard-step-body{min-width:0;max-width:100%;color:var(--fgColor-default, var(--color-fg-default));flex:auto}.wizard-step-body .wizard-step-buttons{display:none;margin-top:24px;justify-content:flex-end}.wizard-step-body .wizard-step-buttons .wizard-step-button{overflow:hidden;text-overflow:ellipsis}.wizard-step-container{border:0}.wizard-step-container .wizard-step-content{display:none;width:100%;padding:16px 24px 24px 24px;overflow:visible;font-size:14px}.wizard-step-container.wizard-step-container-icon .wizard-step-content{padding:24px}.wizard-step-header{padding-top:4px;padding-left:8px}.wizard-step-header>.wizard-step-title{min-width:0;margin-bottom:4px;flex:1 1 auto;color:var(--fgColor-muted, var(--color-fg-muted))}.wizard-step-icon{display:none;height:96px;color:var(--fgColor-accent, var(--color-accent-fg));background-image:linear-gradient(to right, var(--bgColor-accent-muted, var(--color-accent-subtle)), var(--bgColor-default, var(--color-canvas-default)));justify-content:center;align-items:center;border-top-left-radius:6px;border-top-right-radius:6px}.wizard-step[data-single-page-wizard-step-complete=true] .wizard-step-badge{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.wizard-step[data-single-page-wizard-step-complete=true] .wizard-step-item::before{background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.wizard-step[data-single-page-wizard-step-complete=true] .wizard-step-title{color:var(--fgColor-default, var(--color-fg-default))}.wizard-step[data-single-page-wizard-last-step=true] .wizard-step-badge .wizard-step-check{display:block;color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.wizard-step[data-single-page-wizard-last-step=true] .wizard-step-item::before{top:0;display:block;height:16px}@media(min-width: 768px){.wizard-step[data-single-page-wizard-last-step=true] .wizard-step-item::before{display:none}}.wizard-step[data-single-page-wizard-last-step=true] .wizard-step-icon{color:var(--fgColor-success, var(--color-success-fg));background-image:linear-gradient(to right, var(--bgColor-success-muted, var(--color-success-subtle)), var(--bgColor-default, var(--color-canvas-default)))}.wizard-step:not([data-single-page-wizard-last-step=true]) .wizard-step-badge .wizard-step-check{display:none}.wizard-step:not([data-single-page-wizard-last-step=true]) .wizard-step-badge::before{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));content:attr(data-single-page-wizard-step)}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-badge{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.wizard-step[data-single-page-wizard-step-current=true][data-single-page-wizard-last-step=true] .wizard-step-badge{background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis))}.wizard-step[data-single-page-wizard-step-current=true][data-single-page-wizard-last-step=true] .wizard-step-item::before{top:42px;height:16px}.wizard-step[data-single-page-wizard-step-current=true][data-single-page-wizard-last-step=true] .wizard-step-container-icon::after{background-image:linear-gradient(var(--bgColor-success-muted, var(--color-success-subtle)), var(--bgColor-success-muted, var(--color-success-subtle)))}.wizard-step[data-single-page-wizard-step-current=true]:not([data-single-page-wizard-last-step=true]) .wizard-step-container-icon::after{background-image:linear-gradient(var(--bgColor-accent-muted, var(--color-accent-subtle)), var(--bgColor-accent-muted, var(--color-accent-subtle)))}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-icon{display:flex}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-item{flex-direction:column}@media(min-width: 768px){.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-item{flex-direction:row}}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-body{margin-top:16px;margin-left:-16px}@media(min-width: 768px){.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-body{margin-top:0;margin-left:0}}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container{position:relative;background-color:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::after,.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::before{position:absolute;top:11px;right:100%;left:-8px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::after{margin-left:2px;background-color:var(--bgColor-default, var(--color-canvas-default));background-image:linear-gradient(var(--color-canvas-default), var(--color-canvas-default))}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::before{background-color:var(--color-border-default)}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::before,.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::after{transform:rotate(90deg)}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::before{position:absolute;top:-12px;right:100%;left:12px;display:block;width:8px;height:16px;pointer-events:none;content:" ";clip-path:polygon(0 50%, 100% 0, 100% 100%)}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::after{top:-10px;left:11px}@media(min-width: 768px){.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::before,.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::after{top:11px;left:-8px;transform:rotate(0)}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container::after{margin-left:1px}}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container .wizard-step-header{display:none}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container .wizard-step-content-header{margin-bottom:16px}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container .wizard-step-title{color:var(--fgColor-default, var(--color-fg-default))}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-container .wizard-step-content{display:block}.wizard-step[data-single-page-wizard-step-current=true] .wizard-step-buttons{display:flex}[data-a11y-link-underlines=true] .markdown-body a,[data-a11y-link-underlines=true] .markdown-body a:hover,[data-a11y-link-underlines=true] .commit-desc a,[data-a11y-link-underlines=true] .commit-desc a:hover,[data-a11y-link-underlines=true] .Link--inTextBlock,[data-a11y-link-underlines=true] .Link--inTextBlock:hover{text-decoration:underline}[data-a11y-link-underlines=true] .markdown-body :is(h1,h2,h3,h4,h5,h6) a,[data-a11y-link-underlines=true] .markdown-body :is(h1,h2,h3,h4,h5,h6) a:hover{text-decoration:none}.alert-label{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.graph-canvas .alert-label--critical{fill:var(--fgColor-danger, var(--color-danger-emphasis))}.graph-canvas .alert-label--high{fill:var(--fgColor-severe, var(--color-severe-emphasis))}.graph-canvas .alert-label--moderate{fill:var(--fgColor-attention, var(--color-attention-emphasis))}.graph-canvas .alert-label--low{fill:var(--fgColor-neutral, var(--color-neutral-emphasis))}.advisory-form{background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-top:1px solid var(--borderColor-default, var(--color-border-default))}.advisory-form .form-control{background-color:var(--bgColor-default, var(--color-canvas-default))}.advisory-form .form-actions{background-color:var(--bgColor-default, var(--color-canvas-default))}.advisory-form .previewable-comment-form{background-color:var(--bgColor-default, var(--color-canvas-default))}.advisory-credit-window-min{min-height:72px}.advisory-credit-window-max{max-height:370px}.AppHeader{--AppHeader-bg: var(--color-canvas-inset);color:var(--color-fg-default);background:var(--AppHeader-bg);box-shadow:inset 0 calc(var(--borderWidth-thin, 1px)*-1) var(--color-border-default)}.AppHeader .AppHeader-globalBar{display:flex;padding:var(--base-size-16, 16px);gap:var(--base-size-12, 12px)}.AppHeader .AppHeader-globalBar.second-row{display:block}.AppHeader .AppHeader-globalBar.second-row .AppHeader-search{display:block}.AppHeader .AppHeader-globalBar.search-expanded .AppHeader-globalBar-start,.AppHeader .AppHeader-globalBar.always-expanded .AppHeader-globalBar-start{flex:none}.AppHeader .AppHeader-globalBar.search-expanded .AppHeader-context,.AppHeader .AppHeader-globalBar.always-expanded .AppHeader-context{display:none}.AppHeader .AppHeader-globalBar.search-expanded .AppHeader-search .AppHeader-search-whenRegular,.AppHeader .AppHeader-globalBar.always-expanded .AppHeader-search .AppHeader-search-whenRegular{max-width:100%}.AppHeader .AppHeader-globalBar.search-expanded .AppHeader-globalBar-end,.AppHeader .AppHeader-globalBar.always-expanded .AppHeader-globalBar-end{flex:1 1 auto}@media(max-width: 1011.98px){.AppHeader .AppHeader-globalBar.search-expanded .AppHeader-search{position:absolute;top:0;left:0;width:100%}.AppHeader .AppHeader-globalBar.search-expanded .AppHeader-globalBar-end{flex:none}.AppHeader .AppHeader-globalBar.search-expanded .search-suggestions{left:0;top:0;width:100% !important}}.AppHeader .AppHeader-globalBar .AppHeader-globalBar-start{flex:1 1 auto;display:flex;gap:var(--base-size-8, 8px)}.AppHeader .AppHeader-globalBar .AppHeader-globalBar-end{flex:0 1 auto;display:flex;justify-content:flex-end;gap:var(--controlStack-medium-gap-auto, 8px);max-height:calc(var(--base-size-32, 32px))}.AppHeader .AppHeader-globalBar .AppHeader-logo{border:0;width:var(--base-size-32, 32px);height:var(--base-size-32, 32px);outline-offset:2px}.AppHeader .AppHeader-globalBar .AppHeader-logo svg{width:var(--base-size-32, 32px);height:var(--base-size-32, 32px)}.AppHeader .AppHeader-globalBar .AppHeader-user{position:relative;background:var(--color-neutral-muted);border-radius:50%}.AppHeader .AppHeader-globalBar .AppHeader-user img{position:relative;border-radius:50%}.AppHeader .AppHeader-globalBar .AppHeader-context{min-width:0;height:var(--base-size-32, 32px);flex:1 1 auto}@media(min-width: 768px){.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact{display:none}}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-trigger{display:grid;width:100%;height:var(--base-size-48, 48px);color:inherit;text-align:left;cursor:pointer;background-color:transparent;border:0;border-radius:var(--borderRadius-medium, 6px);margin-block:calc(var(--base-size-8, 8px)*-1);padding-block:var(--control-medium-paddingBlock, 6px);padding-inline:var(--control-medium-paddingInline-condensed, 8px);-webkit-appearance:none;appearance:none;align-items:center}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-trigger:hover{background-color:var(--color-action-list-item-default-hover-bg)}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-trigger:active{background-color:var(--color-action-list-item-default-active-bg)}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-lead{display:grid;width:-moz-fit-content;width:fit-content;font-size:var(--text-caption-size, 12px);line-height:var(--text-caption-lineHeight, 1.3333333333);color:var(--color-fg-muted);grid-auto-flow:column;align-items:center}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-parentItem{display:inline;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-separator{white-space:nowrap}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-compact .AppHeader-context-compact-mainItem{display:block;overflow:hidden;line-height:var(--text-body-lineHeight-medium, 1.4285714286);text-overflow:ellipsis;white-space:nowrap}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full{display:inline-flex;width:100%;min-width:0;max-width:100%;overflow:hidden}@media(max-width: 767.98px){.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full{display:none}}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full nav{width:100%}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full ul,.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full li{list-style:none}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full ul{display:flex;flex-direction:row}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full li{display:inline-grid;grid-auto-flow:column;align-items:center;flex:0 99999 auto}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full li:first-child{flex:0 100 max-content}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full li:last-child{flex:0 1 max-content}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-full li:last-child .AppHeader-context-item{font-weight:var(--base-text-weight-semibold, 600)}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-item{display:flex;align-items:center;min-width:3ch;line-height:var(--text-body-lineHeight-medium, 1.4285714286);color:inherit;text-decoration:none !important;border-radius:var(--borderRadius-medium, 6px);padding-inline:var(--control-medium-paddingInline-condensed, 8px);padding-block:var(--control-medium-paddingBlock, 6px)}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-item .AppHeader-context-item-label{display:inline-block;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.AppHeader .AppHeader-globalBar .AppHeader-context a.AppHeader-context-item:hover{background:var(--color-action-list-item-default-hover-bg)}.AppHeader .AppHeader-globalBar .AppHeader-context a.AppHeader-context-item:active{background:var(--color-action-list-item-default-active-bg)}.AppHeader .AppHeader-globalBar .AppHeader-context .AppHeader-context-item-separator{color:var(--color-fg-muted);white-space:nowrap}.AppHeader .AppHeader-globalBar .AppHeader-search{position:relative;display:flex;flex:1 1 auto;justify-content:flex-end}@media(min-width: 1012px){.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-whenNarrow{display:none}}@media(max-width: 1011.98px){.AppHeader .AppHeader-globalBar .AppHeader-search{flex-grow:0}.AppHeader .AppHeader-globalBar .AppHeader-search .search-input{width:auto}.AppHeader .AppHeader-globalBar .AppHeader-search .search-input-container{margin:0 !important}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-whenRegular{display:none}.AppHeader .AppHeader-globalBar .AppHeader-search .header-search{max-width:100%}}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-whenRegular{min-width:12rem;max-width:24rem;flex:1 1 auto}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-wrap{display:grid}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-wrap.AppHeader-search-wrap--hasTrailing input[type=search]{padding-inline-end:calc(var(--control-medium-paddingInline-condensed, 8px) + var(--base-size-16, 16px) + var(--control-medium-gap, 8px) - var(--borderWidth-thin, 1px))}.AppHeader .AppHeader-globalBar .AppHeader-search .search-input-container{height:auto}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-kbd{display:inline-grid;width:var(--base-size-12, 12px);height:var(--base-size-16, 16px);padding:0;font-size:var(--text-caption-size, 12px);line-height:var(--text-caption-lineHeight, 1.3333333333);color:inherit;vertical-align:baseline;background:transparent;border:var(--borderWidth-thin, 1px) solid var(--color-fg-subtle);border-radius:var(--borderRadius-small, 3px);box-shadow:none;align-items:center;justify-content:center}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-placeholder{display:block;width:100%;overflow:hidden;font-weight:var(--base-text-weight-normal, 400);line-height:var(--text-body-lineHeight-medium, 20px);color:var(--color-fg-subtle);text-overflow:ellipsis;white-space:nowrap;pointer-events:none;grid-area:1/1;padding-block:var(--control-medium-paddingBlock, 6px);padding-inline:calc(var(--control-medium-paddingInline-condensed, 8px) + var(--base-size-16, 16px) + var(--control-medium-gap, 8px))}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-control{grid-area:1/1;position:relative;overflow:hidden}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-visual--leading{position:absolute;top:var(--base-size-8, 8px);left:var(--base-size-8, 8px);display:block;width:var(--base-size-16, 16px);height:var(--base-size-16, 16px);color:var(--color-fg-muted);pointer-events:none}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-visual--leading svg{display:block !important}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-searchButton{background:transparent}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search],.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-searchButton{width:100%;padding-block:calc(var(--control-medium-paddingBlock, 6px) - var(--borderWidth-thin, 1px));padding-inline-start:calc(var(--control-medium-paddingInline-condensed, 8px) + var(--base-size-16, 16px) + var(--control-medium-gap, 8px) - var(--borderWidth-thin, 1px));padding-inline-end:var(--control-medium-paddingInline-condensed, 40px);transition:none;border:1px solid var(--borderColor-default, var(--color-border-default))}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search]:placeholder-shown{background:transparent}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search]:not(:placeholder-shown){background:var(--color-canvas-default)}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search]::placeholder{color:transparent;opacity:1}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search]:focus{background:var(--color-canvas-default)}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search]:focus::placeholder{color:var(--color-fg-subtle)}.AppHeader .AppHeader-globalBar .AppHeader-search input[type=search]:focus:placeholder{color:var(--color-fg-subtle);opacity:1}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing{position:absolute;top:var(--base-size-4, 4px);right:var(--base-size-4, 4px);display:grid;width:var(--control-xsmall-size, 24px);height:var(--control-xsmall-size, 24px);padding:0;color:var(--color-fg-muted);background:transparent;border:none;border-radius:var(--borderRadius-small, 3px);align-items:center;justify-content:center}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing:hover{background:var(--color-action-list-item-default-hover-bg)}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing:active{background:var(--color-action-list-item-default-active-bg)}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing::before{position:absolute;top:calc((var(--control-xsmall-size, 24px) - var(--base-size-16, 16px))/2);left:calc(var(--base-size-4, 4px)*-1);display:block;width:var(--borderWidth-thin, 1px);height:var(--base-size-16, 16px);content:"";background:var(--color-border-default)}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing::after{position:absolute;top:50%;left:50%;width:100%;height:100%;min-height:var(--control-medium-size, 32px);content:"";transform:translateX(-50%) translateY(-50%);min-width:var(--control-medium-size, 32px)}@media(pointer: coarse){.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing::after{min-width:var(--control-minTarget-coarse, 44px);min-height:var(--control-minTarget-coarse, 44px)}}.AppHeader .AppHeader-globalBar .AppHeader-search .AppHeader-search-action--trailing svg{color:inherit}.AppHeader .AppHeader-globalBar .AppHeader-divider{width:var(--borderWidth-thin, 1px);height:var(--base-size-20, 20px);height:var(--base-size-20, 20px);margin:calc((var(--control-medium-size, 32px) - var(--base-size-20, 20px))/2) 0;background:var(--color-border-default)}.AppHeader .AppHeader-globalBar .AppHeader-actions{display:grid;grid-auto-flow:column;gap:var(--controlStack-medium-gap-auto, 8px)}.AppHeader .AppHeader-globalBar .AppHeader-actions::before{display:block;width:var(--borderWidth-thin, 1px);height:var(--base-size-20, 20px);height:var(--base-size-20, 20px);content:"";background:var(--color-border-default);margin-block:calc((var(--control-medium-size, 32px) - var(--base-size-20, 20px))/2)}@media(pointer: fine){.AppHeader .AppHeader-globalBar .AppHeader-actions::before{margin-inline:var(--base-size-4, 4px)}}@media(max-width: 767.98px){.AppHeader .AppHeader-globalBar .AppHeader-actions{display:none}}.AppHeader .AppHeader-localBar{padding:0 var(--base-size-16, 16px)}.AppHeader .AppHeader-item{flex:0 0 auto;background:#ffb6c1}.AppHeader .AppHeader-item--full{flex-grow:1}.AppHeader .AppHeader-button{position:relative;display:grid;grid-auto-columns:max-content;width:var(--base-size-32, 32px);height:var(--base-size-32, 32px);color:var(--color-fg-muted);background:transparent;border:var(--borderWidth-thin, 1px) solid var(--color-border-default);border-radius:var(--borderRadius-medium, 6px);align-items:center;justify-content:center}.AppHeader .AppHeader-button svg{color:inherit !important}.AppHeader .AppHeader-button:hover{background:var(--color-action-list-item-default-hover-bg)}.AppHeader .AppHeader-button:active{background:var(--color-action-list-item-default-active-bg)}@media(pointer: coarse){.AppHeader .AppHeader-button::after{position:absolute;top:50%;left:50%;width:100%;height:100%;min-height:var(--control-minTarget-coarse, 44px);content:"";transform:translateX(-50%) translateY(-50%);min-width:var(--control-minTarget-coarse, 44px)}}.AppHeader .AppHeader-button.AppHeader-button--hasIndicator::before{position:absolute;top:calc(var(--base-size-4, 4px)/-2);right:calc(var(--base-size-4, 4px)/-2);display:block;width:var(--base-size-8, 8px);height:var(--base-size-8, 8px);content:"";background:var(--color-accent-fg);border-radius:50%;box-shadow:0 0 0 calc(var(--base-size-4, 4px)/2) var(--AppHeader-bg)}.AppHeader .Overlay-titleWrap{width:100%;overflow-x:hidden}[data-target="animated-image.originalImage"],[data-target="animated-image.replacedImage"],[data-a11y-animated-images=system] [data-animated-image],[data-a11y-animated-images=disabled] [data-animated-image]{display:none}[data-target="animated-image.originalImage"]{width:100%}animated-image[data-catalyst]{display:inline-block}animated-image{max-width:100%}.AnimatedImagePlayer{position:relative;display:inline-block;width:100%}.AnimatedImagePlayer>a:not(.AnimatedImagePlayer-images){display:none}.AnimatedImagePlayer-controls{position:absolute;top:8px;right:8px;z-index:2;display:none;padding:4px;list-style:none;background:var(--bgColor-default, var(--color-canvas-default));border-radius:6px;box-shadow:var(--shadow-floating-small, var(--color-overlay-shadow));opacity:1;transition:opacity 80ms linear}.AnimatedImagePlayer-images .AnimatedImagePlayer-animatedImage{cursor:pointer}.AnimatedImagePlayer-button{display:flex;align-items:center;justify-content:center;width:32px;height:32px;cursor:pointer;background-color:var(--bgColor-default, var(--color-canvas-default));border:0;border-radius:6px}@media(hover: hover)and (pointer: fine){.AnimatedImagePlayer-button:hover{background-color:var(--button-default-bgColor-hover, var(--color-btn-hover-bg));transition:background-color 200ms linear}}.AnimatedImagePlayer-images{display:block;width:100%;padding:0;margin:0;background:none;border:0;outline:none;outline-offset:0}.AnimatedImagePlayer-images:focus-visible{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:0}.AnimatedImagePlayer-button:focus-visible{outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));outline-offset:-2px}.AnimatedImagePlayer-button svg{width:16px;height:16px;pointer-events:none;fill:var(--fgColor-muted, var(--color-fg-muted))}.AnimatedImagePlayer-button:hover svg{fill:var(--fgColor-default, var(--color-fg-default))}.AnimatedImagePlayer-stillImage{position:absolute;top:0;left:0;z-index:1;display:none;width:100%;height:100%;pointer-events:none}.AnimatedImagePlayer-animatedImage{width:100%;max-width:100%;max-height:100%}.AnimatedImagePlayer.playing .AnimatedImagePlayer-controls{opacity:0;transition-delay:1s}.AnimatedImagePlayer.enabled .AnimatedImagePlayer-animatedImage{opacity:0 !important}.AnimatedImagePlayer.enabled.playing .AnimatedImagePlayer-animatedImage{opacity:1 !important}.AnimatedImagePlayer.playing.player-focused .AnimatedImagePlayer-controls{opacity:1;transition-delay:0ms}@media(hover: hover)and (pointer: fine){.AnimatedImagePlayer.playing:hover .AnimatedImagePlayer-controls{opacity:1;transition-delay:0s}}.AnimatedImagePlayer.enabled.playing .AnimatedImagePlayer-stillImage,.AnimatedImagePlayer.enabled.playing .icon-play,.AnimatedImagePlayer .icon-pause{display:none}.AnimatedImagePlayer .icon-play,.AnimatedImagePlayer.enabled.playing .icon-pause,.AnimatedImagePlayer.enabled .AnimatedImagePlayer-stillImage{display:block}.AnimatedImagePlayer.enabled .AnimatedImagePlayer-controls{display:flex}.emoji-tab.UnderlineNav-item{margin-right:4px}.emoji-tab[role=tab][aria-selected=true]{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-default, var(--color-fg-default));border-bottom-color:var(--borderColor-severe-emphasis, var(--color-severe-emphasis))}.emoji-tab[role=tab][aria-selected=true] .UnderlineNav-octicon{color:var(--fgColor-muted, var(--color-fg-muted))}.selected-emoji{z-index:100;background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.emoji-picker-container .emoji-picker-tab g-emoji{margin-right:auto;margin-left:4px}.emoji-tab .emoji-icon{width:auto}.emoji-picker-container{z-index:2;width:395px;max-width:80vw}.emoji-picker-tab{height:136px;padding-top:8px}.emoji-picker-emoji-width{width:32px;height:28px}.emoji-picker-list{list-style:none}.notification-shelf{z-index:34}.notification-shelf.is-stuck{z-index:999}@media(max-width: 767px){.notifications-v2 .commit-ref .css-truncate-target{word-break:break-all;white-space:normal}}@media(max-width: 543px){.notifications-v2 .Box{border-right:0;border-left:0;border-radius:0}}@media(max-width: 543px){.notifications-v2 .Box .Box-header{border-right:0 !important;border-left:0 !important;border-radius:0 !important}}@media(max-width: 767px){.notifications-v2 .AvatarStack--right{width:auto !important;min-width:auto !important;margin-left:53px !important}}@media(max-width: 767px){.notifications-v2 .AvatarStack--right .AvatarStack-body{position:relative !important;right:unset !important;margin-right:8px;flex-direction:row !important}}@media(max-width: 767px){.notifications-v2 .AvatarStack-body .avatar{position:relative !important;margin-right:-12px !important;margin-left:0 !important;border-right:1px solid #fff !important;border-left:0 !important}}.notifications-v2 .thread-subscription-status{background-color:transparent !important}.notifications-v2 .notification-action-mark-archived,.notifications-v2 .notification-action-mark-unread,.notifications-v2 .notification-action-star,.notifications-v2 .notification-action-unsubscribe{display:block !important}.notifications-v2 .notification-action-mark-read,.notifications-v2 .notification-action-mark-unarchived,.notifications-v2 .notification-action-subscribe,.notifications-v2 .notification-action-unstar,.notifications-v2 .notification-is-starred-icon{display:none !important}.notifications-v2 .notification-unsubscribed .notification-action-unsubscribe{display:none !important}.notifications-v2 .notification-unsubscribed .notification-action-subscribe{display:block !important}.notifications-v2 .notification-unread .notification-action-mark-read{display:block !important}.notifications-v2 .notification-unread .notification-action-mark-unread{display:none !important}.notifications-v2 .notification-archived .notification-action-mark-archived,.notifications-v2 .notification-archived .notification-action-mark-read,.notifications-v2 .notification-archived .notification-action-mark-unread{display:none !important}.notifications-v2 .notification-archived .notification-action-mark-unarchived{display:block !important}.notifications-v2 .notification-starred .notification-action-star{display:none !important}.notifications-v2 .notification-starred .notification-is-starred-icon{display:inline-block !important}.notifications-v2 .notification-starred .notification-action-unstar{display:block !important}.notifications-v2 .thread-subscribe-form{display:none !important}.notifications .read .avatar img{opacity:.5}.notifications .read .undo{display:block}.notifications .read .delete{visibility:hidden}.notifications .read[aria-selected=true],.notifications .read.navigation-focus{background-color:#f5f9fc}.notifications .muted .unmute{display:block}.notifications .muted .mute{display:none}.notifications .unmute{display:none}.notifications-list{float:left;width:100%}.thread-subscription-status{padding:8px;margin:40px 0 16px;color:var(--fgColor-muted, var(--color-fg-muted));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.thread-subscription-status .reason{display:inline-block;margin:0 8px;vertical-align:middle}.thread-subscription-status .thread-subscribe-form{display:inline-block;vertical-align:middle}.subscription .loading{opacity:.5}.inline-form{display:inline-block}.inline-form .btn-plain{background-color:transparent;border:0}.drag-and-drop{padding:7px 10px;margin:0;font-size:13px;line-height:16px;color:var(--fgColor-muted, var(--color-fg-muted));background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:1px solid var(--borderColor-default, var(--color-border-default));border-top:0;border-bottom-right-radius:6px;border-bottom-left-radius:6px}.drag-and-drop .default,.drag-and-drop .loading,.drag-and-drop .error{display:none}.drag-and-drop .error{color:var(--fgColor-danger, var(--color-danger-fg))}.drag-and-drop img{vertical-align:top}.file-attachment-errors .Banner--error{display:none}.is-bad-file .file-attachment-errors .bad-file,.is-bad-dimensions .file-attachment-errors .bad-dimensions,.is-too-big .file-attachment-errors .too-big,.is-duplicate-filename .file-attachment-errors .duplicate-filename,.is-too-many .file-attachment-errors .too-many,.is-hidden-file .file-attachment-errors .hidden-file,.is-failed .file-attachment-errors .failed,.is-empty .file-attachment-errors .empty,.is-bad-permissions .file-attachment-errors .bad-permissions,.is-repository-required .file-attachment-errors .repository-required{display:flex}.is-default .drag-and-drop .default{display:inline-block}.is-uploading .drag-and-drop .loading{display:inline-block}.is-bad-file .drag-and-drop .bad-file{display:inline-block}.is-duplicate-filename .drag-and-drop .duplicate-filename{display:inline-block}.is-too-big .drag-and-drop .too-big{display:inline-block}.is-hidden-file .drag-and-drop .hidden-file{display:inline-block}.is-empty .drag-and-drop .empty{display:inline-block}.is-bad-permissions .drag-and-drop .bad-permissions{display:inline-block}.is-repository-required .drag-and-drop .repository-required{display:inline-block}.drag-and-drop-error-info{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.drag-and-drop-error-info a{color:var(--fgColor-accent, var(--color-accent-fg))}.is-failed .drag-and-drop .failed-request{display:inline-block}.manual-file-chooser{position:absolute;width:240px;padding:5px;margin-left:-80px;cursor:pointer;opacity:.0001}.btn .manual-file-chooser{top:0;padding:0;line-height:34px}.upload-enabled textarea{display:block;border-bottom:1px dashed var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:0;border-bottom-left-radius:0}.upload-enabled.focused{border-color:var(--borderColor-accent-emphasis, var(--color-accent-fg));border-radius:6px;outline:none;box-shadow:0 0 0 2px var(--borderColor-accent-emphasis, var(--color-accent-fg))}.upload-enabled.focused .form-control{border-color:transparent;border-bottom-color:var(--borderColor-accent-emphasis, var(--color-accent-fg));box-shadow:none}.upload-enabled.focused .drag-and-drop{border-color:transparent}.dragover textarea,.dragover .drag-and-drop{box-shadow:#c9ff00 0 0 3px}.write-content{position:relative}.previewable-comment-form{position:relative}.previewable-comment-form .tabnav{position:relative;padding:8px 8px 0}.previewable-comment-form .comment{border:1px solid transparent;border-bottom:0}.previewable-comment-form .comment-form-error{margin-bottom:8px}.previewable-comment-form .write-content,.previewable-comment-form .preview-content{display:none;margin:0 8px 8px}.previewable-comment-form.write-selected .write-content,.previewable-comment-form.preview-selected .preview-content{display:block}.previewable-comment-form textarea{display:block;width:100%;min-height:100px;max-height:500px;padding:8px;resize:vertical}.form-action-spacious{margin-top:10px}div.composer{margin-top:0;border:0}.composer .comment-form-textarea{height:200px;min-height:200px}.composer .tabnav{margin:0 0 10px}h2.account{margin:15px 0 0;font-size:18px;font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}p.explain{position:relative;font-size:12px;color:var(--fgColor-muted, var(--color-fg-muted))}p.explain strong{color:var(--fgColor-default, var(--color-fg-default))}p.explain .octicon{margin-right:5px;color:var(--fgColor-muted, var(--color-fg-muted))}p.explain .minibutton{top:-4px;float:right}.progress-pjax-loader{z-index:99999;height:2px !important;background:transparent;opacity:0;transition:opacity .4s linear .4s}.progress-pjax-loader.is-loading{opacity:1;transition:none}.progress-pjax-loader>.progress-pjax-loader-bar{background-color:#79b8ff;transition:width .4s ease}.starred .starred-button-icon{color:var(--color-scale-yellow-2)}.user-lists-menu-action{color:var(--fgColor-default, var(--color-fg-default))}.user-lists-menu-action:hover:not(:disabled){color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.user-lists-menu-action:focus:not(:disabled){color:var(--fgColor-default, var(--color-fg-default));outline:2px solid var(--focus-outlineColor, var(--color-accent-emphasis));outline-offset:2px}.starring-container .BtnGroup-parent:active{z-index:auto}.shelf{padding-top:16px;margin-bottom:16px;background-color:var(--bgColor-default, var(--color-canvas-default));border-bottom:1px solid var(--borderColor-muted, var(--color-border-muted))}.shelf .container{position:relative}.org-sso,.business-sso{width:340px;margin:0 auto}.org-sso .sso-title,.business-sso .sso-title{font-size:24px;font-weight:var(--base-text-weight-light, 300);letter-spacing:-0.5px}.org-sso .org-sso-panel,.org-sso .business-sso-panel,.business-sso .org-sso-panel,.business-sso .business-sso-panel{padding:16px;background-color:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.org-sso .sso-recovery-callout,.business-sso .sso-recovery-callout{padding:16px 8px;text-align:center;border:1px solid var(--borderColor-muted, var(--color-border-muted));border-radius:6px}.sso-modal{padding:16px}.sso-modal .org-sso,.sso-modal .business-sso{width:auto}.sso-modal .org-sso .org-sso-panel,.sso-modal .business-sso .business-sso-panel{border:0}.sso-modal .sso-prompt-success,.sso-modal .sso-prompt-error{display:none}.sso-modal.success .sso-prompt-default{display:none}.sso-modal.success .sso-prompt-success{display:block}.sso-modal.error .sso-prompt-default{display:none}.sso-modal.error .sso-prompt-error{display:block}.sso-modal.error .flash-error{margin-right:-32px;margin-left:-32px;border-right:0;border-left:0;border-radius:0}.status-indicator{display:inline-block;width:16px;height:16px;margin-left:5px}.status-indicator .octicon{display:none}.status-indicator-success::before{content:""}.status-indicator-success .octicon-check{display:inline-block;color:var(--fgColor-success, var(--color-success-fg));fill:var(--fgColor-success, var(--color-success-fg))}.status-indicator-success .octicon-x{display:none}.status-indicator-failed::before{content:""}.status-indicator-failed .octicon-check{display:none}.status-indicator-failed .octicon-x{display:inline-block;color:var(--fgColor-danger, var(--color-danger-fg));fill:var(--fgColor-danger, var(--color-danger-fg))}.status-indicator-loading{width:16px;background-image:url("/images/spinners/octocat-spinner-32-EAF2F5.gif");background-repeat:no-repeat;background-position:0 0;background-size:16px}.tag-input-container{position:relative}.tag-input-container .focus{border-color:transparent !important;box-shadow:none !important}.tag-input-container .suggester{position:absolute;z-index:100;width:100%;margin-top:-1px}.tag-input-container ul{list-style:none}.tag-input input{float:left;padding-left:2px;margin:0;background:none;border:0;box-shadow:none}.tag-input input:focus{box-shadow:none}.task-list-item{list-style-type:none}.task-list-item label{font-weight:var(--base-text-weight-normal, 400)}.task-list-item.enabled label{cursor:pointer}.task-list-item+.task-list-item{margin-top:4px}.task-list-item .handle{display:none}.task-list-item-checkbox{margin:0 .2em .25em -1.4em;vertical-align:middle}.contains-task-list:dir(rtl) .task-list-item-checkbox{margin:0 -1.6em .25em .2em}.convert-to-issue-button{top:2px;right:4px;padding:0 2px;margin-right:8px;-webkit-user-select:none;user-select:none;background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.convert-to-issue-button .octicon{fill:var(--fgColor-default, var(--color-fg-default))}.convert-to-issue-button:hover .octicon,.convert-to-issue-button:focus .octicon{fill:var(--fgColor-accent, var(--color-accent-fg))}.reorderable-task-lists .markdown-body .contains-task-list{padding:0}.reorderable-task-lists .markdown-body li:not(.task-list-item){margin-left:24px}.reorderable-task-lists .markdown-body ol:not(.contains-task-list) li,.reorderable-task-lists .markdown-body ul:not(.contains-task-list) li{margin-left:0}.reorderable-task-lists .markdown-body .task-list-item{padding:2px 15px 2px 42px;margin-right:-15px;margin-left:-15px;line-height:1.5;border:0}.reorderable-task-lists .markdown-body .task-list-item+.task-list-item{margin-top:0}.reorderable-task-lists .markdown-body .task-list-item .handle{display:block;float:left;width:20px;padding:2px 0 0 2px;margin-left:-43px;opacity:0}.reorderable-task-lists .markdown-body .task-list-item .drag-handle{fill:var(--fgColor-default, var(--color-fg-default))}.reorderable-task-lists .markdown-body .task-list-item.hovered>.handle{opacity:1}.reorderable-task-lists .markdown-body .task-list-item.is-dragging{opacity:0}.reorderable-task-lists .markdown-body .contains-task-list:dir(rtl) .task-list-item{margin-right:0}.comment-body .reference{font-weight:var(--base-text-weight-semibold, 600);white-space:nowrap}.comment-body .issue-link{white-space:normal}.comment-body .issue-link .issue-shorthand{font-weight:var(--base-text-weight-normal, 400);color:var(--fgColor-muted, var(--color-fg-muted))}.comment-body .issue-link:hover .issue-shorthand,.comment-body .issue-link:focus .issue-shorthand{color:var(--fgColor-accent, var(--color-accent-fg))}.review-comment-contents .markdown-body .task-list-item{padding-left:42px;margin-right:-12px;margin-left:-12px;border-top-left-radius:6px;border-bottom-left-radius:6px}.convert-to-issue-enabled .task-list-item .contains-task-list{padding:4px 15px 0 43px;margin:0 -15px 0 -42px}.convert-to-issue-enabled .task-list-item.hovered{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.convert-to-issue-enabled .task-list-item.hovered .contains-task-list{background-color:var(--bgColor-default, var(--color-canvas-default))}.convert-to-issue-enabled .task-list-item>.convert-to-issue-button{opacity:0}.convert-to-issue-enabled .task-list-item.hovered>.convert-to-issue-button,.convert-to-issue-enabled .task-list-item>.convert-to-issue-button:focus{z-index:20;opacity:1}.convert-to-issue-enabled .task-list-item.is-loading{color:var(--fgColor-muted, var(--color-fg-muted));background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-top:1px solid var(--borderColor-accent-muted, var(--color-accent-subtle));border-bottom:1px solid var(--bgColor-default, var(--color-canvas-default));border-left:1px solid var(--bgColor-default, var(--color-canvas-default))}.convert-to-issue-enabled .task-list-item.is-loading ul{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-default, var(--color-canvas-default))}.convert-to-issue-enabled .task-list-item.is-loading>.handle{opacity:0}.task-list-item-convert-container{position:absolute !important;top:calc(100% - 4px);right:0;left:0;display:none;margin-top:0}.task-list-item-convert-container:hover,.task-list-item-convert-container:focus{display:block}.task-list-item-convert-button-container{top:4px;right:0;left:auto;width:auto}.contains-task-list{position:relative}.contains-task-list:hover .task-list-item-convert-container,.contains-task-list:focus-within .task-list-item-convert-container{display:block;width:auto;height:24px;overflow:visible;clip:auto}.convert-to-block-button{margin:0 4px}.toolbar-commenting .dropdown-menu-s{width:100px}.toolbar-commenting .dropdown-item{font-weight:var(--base-text-weight-semibold, 600);line-height:1em;background:none;border:0}.toolbar-commenting .dropdown-item:hover{color:var(--fgColor-accent, var(--color-accent-fg))}.toolbar-commenting .dropdown-item:focus{color:var(--fgColor-accent, var(--color-accent-fg));outline:none}.toolbar-item{display:block;float:left;padding:4px;cursor:pointer}.toolbar-item.dropdown,.toolbar-item.select-menu{padding:0}.toolbar-item .select-menu-modal{margin-top:2px}.toolbar-item .select-menu-item{padding-left:8px}.topic-tag{display:inline-block;padding:.3em .9em;margin:0 .5em .5em 0;white-space:nowrap;background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-radius:6px}.topic-tag-link:hover{text-decoration:none;background-color:#def}.delete-topic-button,.delete-topic-link{display:inline-block;width:26px;color:var(--fgColor-muted, var(--color-fg-muted));border-top:0;border-right:0;border-bottom:0;border-left:1px solid #b4d9ff}.topic-tag-action:hover .delete-topic-link{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.topic-tag-outline{background:transparent;box-shadow:inset 0 0 0 1px #c8e1ff}.delete-topic-link{padding-right:8px;padding-left:8px;margin-left:8px;line-height:1.75}.delete-topic-link:hover{text-decoration:none}.invalid-topic .delete-topic-button{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-danger-muted, var(--color-danger-subtle));border-left-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.invalid-topic .delete-topic-button:hover{background-color:#ffc8ce}.topic-tag-action{display:inline-flex;align-items:center;padding-left:.8em;margin:.4em .4em 0 0;background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-radius:6px}.topic-tag-action.invalid-topic{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-danger-muted, var(--color-danger-subtle));border-color:var(--borderColor-danger-emphasis, var(--color-danger-emphasis))}.topic-tag-action .add-topic-button,.topic-tag-action .remove-topic-button{display:inline-block;width:26px;font-size:14px;color:var(--fgColor-muted, var(--color-fg-muted));background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-top:0;border-right:0;border-bottom:0;border-left:1px solid #b4d9ff}.topic-tag-action .add-topic-button:hover,.topic-tag-action .remove-topic-button:hover{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis))}.topic-tag-action .add-topic-button:hover{background-color:var(--bgColor-success-emphasis, var(--color-success-emphasis))}.topic-tag-action .remove-topic-button{border-right:0;border-top-right-radius:6px;border-bottom-right-radius:6px}.topic-tag-action .remove-topic-button:hover{background-color:var(--bgColor-danger-emphasis, var(--color-danger-emphasis))}.topic-input-container .tag-input{width:908px;cursor:text}.topic-input-container .tag-input.org-repo-tag-input{width:100%}.topic-input-container .tag-input .tag-input-inner{min-height:26px;background-image:none}.topic-input-container .topic-tag{margin-top:2px}.topic .css-truncate-target{max-width:75%}.topic-list .topic-list-item+.topic-list-item{border-top:1px solid var(--borderColor-default, var(--color-border-default))}.topic-box .starred{color:var(--fgColor-attention, var(--color-attention-fg));border:0}.topic-box .unstarred{color:var(--fgColor-muted, var(--color-fg-muted));border:0}.user-status-suggestions{height:98px;transition:height 100ms ease-out,opacity 200ms ease-in}.user-status-suggestions.collapsed{height:0;opacity:0}.user-status-container,.user-status-container .team-mention,.user-status-container .user-mention{white-space:normal}.user-status-container{word-break:break-word;word-wrap:break-word}.user-status-container .input-group-button .btn{width:46px;height:34px;line-height:0}.user-status-container .input-group-button g-emoji{font-size:1.3em;line-height:18px}.user-status-container .team-mention,.user-status-container .user-mention{white-space:normal}.user-status-container img.emoji{width:18px;height:18px}.emoji-status-width{width:20px}.user-status-limited-availability-compact{width:8px;height:8px;background-color:var(--bgColor-attention-emphasis, var(--color-attention-emphasis))}.user-status-message-wrapper{color:var(--fgColor-default, var(--color-fg-default))}.toggle-user-status-edit:hover .user-status-message-wrapper,.toggle-user-status-edit:focus .user-status-message-wrapper{color:var(--fgColor-accent, var(--color-accent-fg))}.user-status-message-wrapper div{display:inline}.user-status-header g-emoji{font-size:1.25em}.user-status-message-wrapper .g-emoji{display:inline-block}.user-status-limited-availability-container{margin-top:16px;margin-bottom:16px}@media only screen and (max-height: 560px){.user-status-suggestions{display:none}.user-status-limited-availability-container{margin-top:8px;margin-bottom:8px}}.user-status-circle-badge{background-color:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:2em;box-shadow:var(--shadow-resting-small, var(--color-shadow-small))}.command-palette{box-shadow:var(--shadow-floating-small, var(--color-overlay-shadow))}@media(min-width: 768px){.command-palette-details-dialog{width:512px}}@media(min-width: 1012px){.command-palette-details-dialog{width:640px}}@media(min-width: 1280px){.command-palette-details-dialog{width:720px}}.page-stack-transition-height{overflow-y:scroll;transition-timing-function:cubic-bezier(0.25, 0.46, 0.45, 0.94);transition-duration:.2s;transition-property:max-height,min-height}.page-stack-transition-height.no-transition{transition-duration:0s}.command-palette-input-group{position:relative;z-index:0;padding-left:0;color:var(--fgColor-muted, var(--color-fg-subtle))}.command-palette-input-group .no-focus-indicator:focus,.command-palette-input-group .no-focus-indicator:focus-visible{border:0 !important;outline:none !important}.command-palette-input-group .command-palette-typeahead{position:absolute;z-index:1;padding:inherit;pointer-events:none}.command-palette-input-group .command-palette-typeahead .typeahead-segment{white-space:pre}.command-palette-input-group .command-palette-typeahead .typeahead-segment.input-mirror{opacity:0}.command-palette-input-group .typeahead-input{padding:inherit}.command-palette-input-clear-button{color:var(--fgColor-muted, var(--color-fg-subtle))}.command-palette-input-clear-button:hover{color:var(--fgColor-muted, var(--color-fg-muted))}themed-picture{visibility:hidden}.SidePanel>.Overlay-header>.Overlay-headerContentWrap>.Overlay-titleWrap{padding-top:8px}.SidePanel>.Overlay-header>.Overlay-headerContentWrap>.Overlay-actionWrap{padding:8px}.is-auto-complete-loading :not(input).form-control{padding-right:0;background-image:none}.ActionMenu{position:absolute;z-index:999;display:none;background-color:var(--overlay-bgColor, var(--color-canvas-overlay))}.CommentBox-header{display:flex;background-color:var(--bgColor-muted, var(--color-canvas-subtle));border-top-left-radius:6px;border-top-right-radius:6px}.CommentBox-header.CommentBox-header{margin-bottom:0}.CommentBox-header .tabnav-tabs{margin-top:-1px;margin-left:-1px;flex-shrink:0}.CommentBox-toolbar{display:flex;min-width:0;margin-right:var(--base-size-4, 4px);flex-shrink:1;flex-grow:1}.CommentBox-toolbar .Button--invisible{color:var(--fgColor-muted, var(--color-fg-muted))}.CommentBox-input{display:block;min-height:100px;padding:var(--stack-padding-normal, 16px);line-height:1.5;color:var(--fgColor-default, var(--color-fg-default));resize:vertical;border:0}.CommentBox-input.CommentBox-input.CommentBox-input:focus{box-shadow:none}.CommentBox-input::placeholder{visibility:hidden}.CommentBox-input.CommentBox-input--medium{min-height:150px}.CommentBox-input.CommentBox-input--large{min-height:250px}.CommentBox-placeholder{position:absolute;top:0;left:0;display:none;padding:var(--stack-padding-normal, 16px);color:var(--fgColor-muted, var(--color-fg-subtle));pointer-events:none}.CommentBox-input:placeholder-shown+.CommentBox-placeholder{display:block}.CommentBox:has(.CommentBox-input:focus,.Button:active){outline:2px solid var(--focus-outlineColor, var(--color-accent-emphasis));outline-offset:-1px}@supports not selector(.CommentBox:has(*:focus)){.CommentBox:focus-within{outline:2px solid var(--focus-outlineColor, var(--color-accent-emphasis));outline-offset:-1px}}.CommentBox .dragover .CommentBox-input,.CommentBox .dragover .CommentBox-input:focus{border-radius:10px;outline:2px dashed var(--borderColor-default, var(--color-border-default));outline-offset:-6px;box-shadow:none}.previewable-comment-form textarea.CommentBox-input{padding:var(--stack-padding-normal, 16px)}.previewable-edit .previewable-comment-form .CommentBox-header .tabnav-tabs{display:flex}.previewable-comment-form .CommentBox .preview-content{margin:0}.previewable-comment-form .CommentBox .comment-body{padding:var(--stack-padding-normal, 16px);border-bottom:0}.PageHeader{display:grid;grid-template-columns:1fr;grid-template-areas:"contextBar" "titleBar" "description" "navigation";margin-bottom:24px}@media(max-width: 768px){.PageHeader{margin-bottom:16px}}.PageHeader .PageHeader-contextBar{display:flex;column-gap:16px;align-items:center}.PageHeader .PageHeader-contextBar .PageHeader-parentLink{flex:1 1 auto}.PageHeader .PageHeader-parentLink a{display:inline-grid;grid-template-columns:min-content auto;gap:4px;align-items:center;padding:4px 8px;margin-left:-8px;font-size:14px;line-height:1.25;color:var(--fgColor-muted, var(--color-fg-muted));border-radius:6px}.PageHeader .PageHeader-parentLink a:hover{color:var(--fgColor-default, var(--color-fg-default));text-decoration:none;background:var(--bgColor-muted, var(--color-canvas-subtle))}.PageHeader .PageHeader-parentLink .PageHeader-parentLink-label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.PageHeader .PageHeader-titleBar{display:flex;column-gap:16px}.PageHeader .PageHeader-titleBar .PageHeader-titleWrap{flex:1 1 auto}.PageHeader .PageHeader-titleBar .PageHeader-actions{flex:0 0 auto}.PageHeader .PageHeader-titleWrap{display:flex;column-gap:8px}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--large{font:400 var(--text-title-size-large, 2rem) -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--large .PageHeader-backButton{height:var(--text-title-lineHeight-large, 2.5rem)}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--large .PageHeader-leadingVisual{max-height:var(--text-title-lineHeight-large, 2.5rem)}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--medium{font:600 var(--text-title-size-medium, 1.25rem) -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--subtitle{font:400 var(--text-subtitle-size, 1.25rem) -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"}@media(max-width: 768px){.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--large{font:600 var(--text-title-size-medium, 1.25rem) -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--medium{font:600 var(--text-title-size-medium, 1.25rem) -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--subtitle{font:400 var(--text-title-size-medium, 1.25rem) -apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"}}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--hasLeadingVisual{display:flex}.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--hasBackButton:first-child{margin-left:-4px}@media(min-width: 768px){.PageHeader .PageHeader-titleWrap.PageHeader-titleWrap--hasBackButton:first-child{display:flex !important;align-items:center;margin-left:8px;grid-template-columns:min-content auto}}.PageHeader .PageHeader-titleWrap h1,.PageHeader .PageHeader-titleWrap h2,.PageHeader .PageHeader-titleWrap h3{font-size:inherit;font-weight:inherit;line-height:inherit}.PageHeader .PageHeader-titleWrap .PageHeader-title{display:inline-grid;grid-auto-flow:column;grid-template-columns:auto;align-items:center;gap:8px}.PageHeader .PageHeader-titleWrap .PageHeader-leadingVisual{display:grid;align-items:center;max-height:var(--text-title-lineHeight-large, 1.5)}.PageHeader .PageHeader-titleWrap .PageHeader-trailingVisual{display:grid;align-items:center;max-height:var(--text-title-lineHeight-large, 1.5)}.PageHeader .PageHeader-titleWrap .PageHeader-backButton{position:relative;display:grid;width:16px;height:var(--text-title-lineHeight-medium, 1.5rem);align-items:center;padding:0;color:inherit;border-radius:6px}@media(max-width: 768px){.PageHeader .PageHeader-titleWrap .PageHeader-backButton{display:none}}.PageHeader .PageHeader-description{margin-top:8px;overflow:auto}.PageHeader .PageHeader-navigation{margin-top:16px;overflow:auto}.QueryBuilder-StyledInput{display:inline-flex;width:100%;min-height:var(--control-medium-size, 32px);color:var(--fgColor-default, var(--color-fg-default));vertical-align:middle;cursor:text;background-color:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-color:var(--control-borderColor-rest, var(--color-border-default));border-radius:var(--borderRadius-medium, 6px);outline:none;align-items:center;gap:4px}.QueryBuilder-focus{border-color:transparent;outline:2px solid var(--focus-outlineColor, var(--color-accent-fg));box-shadow:inset 0 0 1px transparent}.QueryBuilder-StyledInputContainer{position:relative;display:flex;overflow-x:auto;overflow-y:hidden;font-size:inherit;align-items:center;-ms-overflow-style:none;scrollbar-width:none;flex:1;align-self:stretch}.QueryBuilder-StyledInputContainer::-webkit-scrollbar{display:none}.QueryBuilder-StyledInputContent{position:absolute;display:inline-flex;padding:0;word-break:break-word;white-space:pre;-webkit-user-select:none;user-select:none;flex:1}.QueryBuilder-leadingVisualWrap{margin:4px 4px 4px 8px;color:var(--fgColor-muted, var(--color-fg-muted))}.QueryBuilder-spacer{width:8px;height:100%}.QueryBuilder-InputWrapper{width:100%;align-self:stretch}.QueryBuilder-Sizer{position:absolute;top:0;left:0;height:0;overflow:scroll;white-space:pre;visibility:hidden}.QueryBuilder-Input{position:relative;display:flex;min-width:100%;padding:0;overflow-x:auto;overflow-y:hidden;color:transparent;resize:none;background:transparent;border:0;outline:none;caret-color:var(--fgColor-default, var(--color-fg-default))}.QueryBuilder-Input:focus{border:0 !important;box-shadow:none !important}query-builder:not(:defined) .QueryBuilder-Input{color:var(--fgColor-default, var(--color-fg-default))}.QueryBuilder-ListItem{display:grid;grid-template-columns:max-content minmax(0, auto) max-content;grid-template-areas:"leadingVisual label trailingLabel";-webkit-user-select:unset;user-select:unset}.QueryBuilder-ListItem-link{color:inherit;text-decoration:none !important}.QueryBuilder-ListItem-trailing{grid-area:trailingLabel}.QueryBuilder-ListWrap{max-height:20em;padding:8px;overflow-x:hidden;overflow-y:auto !important}.QueryBuilder [data-type=filter-value]{color:var(--fgColor-accent, var(--color-accent-fg));background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-radius:var(--borderRadius-small, 3px)}.QueryBuilder .qb-filter-value{color:var(--fgColor-accent, var(--color-accent-fg));background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border-radius:var(--borderRadius-small, 3px)}.QueryBuilder .qb-entity{color:var(--color-prettylights-syntax-entity)}.QueryBuilder .qb-constant{color:var(--color-prettylights-syntax-constant)}.QueryBuilder .ActionList-sectionDivider:not(:empty){padding:0}@media(min-width: 1012px){.hx_actions-sidebar{max-width:320px}}.hx_anim-fade-out{animation-name:hx-fade-out;animation-duration:1s;animation-fill-mode:forwards;animation-timing-function:ease-out}@keyframes hx-fade-out{0%{opacity:1}100%{opacity:0}}.AvatarStack--large{min-width:44px;height:32px}.AvatarStack--large.AvatarStack--two{min-width:48px}.AvatarStack--large.AvatarStack--three-plus{min-width:52px}.AvatarStack--large .AvatarStack-body .avatar{width:32px;height:32px;margin-right:-28px}.AvatarStack--large .AvatarStack-body:hover .avatar{margin-right:4px}.AvatarStack--large .avatar.avatar-more::before{width:32px}.AvatarStack--large .avatar.avatar-more::after{width:30px}.AvatarStack--large .avatar.avatar-more::after,.AvatarStack--large .avatar.avatar-more::before{height:32px}.hx_avatar_stack_commit .AvatarStack{min-width:24px;height:24px}.hx_avatar_stack_commit .AvatarStack .avatar{width:24px;height:24px}.hx_avatar_stack_commit .AvatarStack.AvatarStack--two{min-width:40px}.hx_avatar_stack_commit .AvatarStack.AvatarStack--three-plus{min-width:44px}.hx_flex-avatar-stack{display:flex;align-items:center}.hx_flex-avatar-stack-item{min-width:0;max-width:24px}.hx_flex-avatar-stack-item .avatar{display:block;background-color:var(--bgColor-default, var(--color-canvas-default));border:2px solid var(--bgColor-default, var(--color-canvas-default));box-shadow:none}.hx_flex-avatar-stack-item:last-of-type{flex-shrink:0;max-width:none}.Box-row--focus-gray.navigation-focus .AvatarStack-body{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.AvatarStack-body:not(:hover){background-color:transparent}.AvatarStack--three-plus.AvatarStack--three-plus .avatar-more{display:none}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body .avatar:nth-child(n+4){display:flex;opacity:1}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover) .avatar:nth-of-type(n + 6){display:none;opacity:0}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body>.avatar:nth-of-type(1){z-index:5}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body>.avatar:nth-of-type(2){z-index:4}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body>.avatar:nth-of-type(3){z-index:3}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body>.avatar:nth-of-type(4){z-index:2}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body>.avatar:nth-of-type(5){z-index:1}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more+.avatar:nth-of-type(3) img{opacity:.5}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(4) img{opacity:.33}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(5) img{opacity:.25}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more+.avatar:nth-of-type(3){margin-right:0;margin-left:-6px}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(4){margin-right:0;margin-left:-18px}.AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(5){margin-right:0;margin-left:-18px}.AvatarStack--three-plus.AvatarStack--three-plus.AvatarStack--right .AvatarStack-body:not(:hover)>.avatar-more+.avatar:nth-of-type(3){margin-right:-6px;margin-left:0}.AvatarStack--three-plus.AvatarStack--three-plus.AvatarStack--right .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(4){margin-right:-18px;margin-left:0}.AvatarStack--three-plus.AvatarStack--three-plus.AvatarStack--right .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(5){margin-right:-18px;margin-left:0}.AvatarStack--three-plus.AvatarStack--three-plus.AvatarStack--large .AvatarStack-body:not(:hover)>.avatar-more+.avatar:nth-of-type(3){margin-right:0;margin-left:-2px}.AvatarStack--three-plus.AvatarStack--three-plus.AvatarStack--large .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(4){margin-right:0;margin-left:-30px}.AvatarStack--three-plus.AvatarStack--three-plus.AvatarStack--large .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(5){margin-right:0;margin-left:-30px}.hx_avatar_stack_commit .AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more+.avatar:nth-of-type(3){margin-right:0;margin-left:-10px}.hx_avatar_stack_commit .AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(4){margin-right:0;margin-left:-21px}.hx_avatar_stack_commit .AvatarStack--three-plus.AvatarStack--three-plus .AvatarStack-body:not(:hover)>.avatar-more~.avatar:nth-of-type(5){margin-right:0;margin-left:-21px}.hx_badge-search-container{cursor:text}.hx_badge-search-container .hx_badge-input{border:0;outline:0;box-shadow:none}.hx_badge-search-container .hx_badge-input:focus{border:0 !important;box-shadow:none !important}.hx_badge-search-container .hx_badge-input::placeholder{font-size:12px}.hx_badge-search-container .hx_badge-input-inline{height:30px}.hx_badge{cursor:pointer}.hx_badge[aria-pressed=true]{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis)) !important;background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis)) !important;border-color:var(--borderColor-accent-emphasis, var(--color-accent-emphasis)) !important}.hx_Box--firstRowRounded0 .Box-row:first-of-type{border-top-left-radius:0;border-top-right-radius:0}.Box-row:first-of-type{border-top-color:transparent}.hx_Box-row--with-top-border:first-of-type{border-top-color:inherit}.Box--overlay [data-close-dialog],.Box-overlay--narrow [data-close-dialog],.Box-overlay--wide [data-close-dialog]{z-index:1}.dropdown-item.btn-link:disabled,.dropdown-item.btn-link:disabled:hover,.dropdown-item.btn-link[aria-disabled=true],.dropdown-item.btn-link[aria-disabled=true]:hover{background-color:transparent}@media(-webkit-min-device-pixel-ratio: 2)and (-webkit-min-device-pixel-ratio: 0), (-webkit-min-device-pixel-ratio: 2)and (min-resolution: 0.001dpcm){g-emoji{font-size:1.25em}}.hx_create-pr-button:hover{border-right-width:0}.hx_create-pr-button:hover+.BtnGroup-parent .BtnGroup-item{border-left-width:1px}summary[type=button].btn{-webkit-appearance:none;appearance:none}.form-control:-webkit-autofill{box-shadow:inset 0 0 0 32px var(--bgColor-default, var(--color-canvas-default)) !important;-webkit-text-fill-color:var(--fgColor-default, var(color-fg-default))}.form-control:-webkit-autofill:focus{box-shadow:inset 0 0 0 32px var(--bgColor-default, var(--color-canvas-default)),0 0 0 2px var(--borderColor-accent-emphasis, var(--color-accent-fg)) !important}::-webkit-calendar-picker-indicator{filter:invert(50%)}[data-color-mode=light][data-light-theme*=dark] ::selection,[data-color-mode=dark][data-dark-theme*=dark] ::selection{background-color:var(--bgColor-accent-muted, var(--color-accent-muted))}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark] ::selection{background-color:var(--bgColor-accent-muted, var(--color-accent-muted))}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark] ::selection{background-color:var(--bgColor-accent-muted, var(--color-accent-muted))}}@font-face{font-family:"Noto Sans";src:local(sans-serif);unicode-range:U+60}[data-color-mode=light][data-light-theme*=dark],[data-color-mode=dark][data-dark-theme*=dark]{--color-workflow-card-connector:var(--color-scale-gray-5);--color-workflow-card-connector-bg:var(--color-scale-gray-5);--color-workflow-card-connector-inactive:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-inactive-bg:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-highlight:var(--color-scale-blue-5);--color-workflow-card-connector-highlight-bg:var(--color-scale-blue-5);--color-workflow-card-bg:var(--color-scale-gray-7);--color-workflow-card-inactive-bg:var(--bgColor-inset, var(--color-canvas-inset));--color-workflow-card-header-shadow:rgba(27, 31, 35, 0.04);--color-workflow-card-progress-complete-bg:var(--color-scale-blue-5);--color-workflow-card-progress-incomplete-bg:var(--color-scale-gray-6);--color-discussions-state-answered-icon:var(--color-scale-green-3);--color-bg-discussions-row-emoji-box:var(--color-scale-gray-6);--color-notifications-button-text:var(--color-scale-white);--color-notifications-button-hover-text:var(--color-scale-white);--color-notifications-button-hover-bg:var(--color-scale-blue-4);--color-notifications-row-read-bg:var(--color-canvas-default);--color-notifications-row-bg:var(--bgColor-muted, var(--color-canvas-subtle));--color-icon-directory:var(--fgColor-muted, var(--color-fg-muted));--color-checks-step-error-icon:var(--color-scale-red-4);--color-calendar-halloween-graph-day-L1-bg:#631c03;--color-calendar-halloween-graph-day-L2-bg:#bd561d;--color-calendar-halloween-graph-day-L3-bg:#fa7a18;--color-calendar-halloween-graph-day-L4-bg:#fddf68;--color-calendar-winter-graph-day-L1-bg:#0A3069;--color-calendar-winter-graph-day-L2-bg:#0969DA;--color-calendar-winter-graph-day-L3-bg:#54AEFF;--color-calendar-winter-graph-day-L4-bg:#B6E3FF;--color-calendar-graph-day-bg:var(--color-scale-gray-8);--color-calendar-graph-day-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L1-bg:#0e4429;--color-calendar-graph-day-L2-bg:#006d32;--color-calendar-graph-day-L3-bg:#26a641;--color-calendar-graph-day-L4-bg:#39d353;--color-calendar-graph-day-L1-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L2-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L3-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L4-border:rgba(255, 255, 255, 0.05);--color-user-mention-fg:var(--color-scale-yellow-0);--color-user-mention-bg:var(--color-scale-yellow-8);--color-dashboard-feed-bg:var(--color-scale-gray-9);--color-mktg-btn-shadow-outline:rgba(255, 255, 255, 0.25) 0 0 0 1px inset;--color-marketing-icon-primary:var(--color-scale-blue-2);--color-marketing-icon-secondary:var(--color-scale-blue-5);--color-project-header-bg:var(--color-scale-gray-9);--color-project-sidebar-bg:var(--color-scale-gray-8);--color-project-gradient-in:var(--color-scale-gray-8);--color-project-gradient-out:rgba(22, 27, 34, 0);--color-diff-blob-selected-line-highlight-mix-blend-mode:screen;--color-checks-donut-error:var(--color-scale-red-4);--color-checks-donut-pending:var(--color-scale-yellow-3);--color-checks-donut-success:var(--color-scale-green-4);--color-checks-donut-neutral:var(--color-scale-gray-3);--color-text-white:var(--color-scale-white)}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark]{--color-workflow-card-connector:var(--color-scale-gray-5);--color-workflow-card-connector-bg:var(--color-scale-gray-5);--color-workflow-card-connector-inactive:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-inactive-bg:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-highlight:var(--color-scale-blue-5);--color-workflow-card-connector-highlight-bg:var(--color-scale-blue-5);--color-workflow-card-bg:var(--color-scale-gray-7);--color-workflow-card-inactive-bg:var(--bgColor-inset, var(--color-canvas-inset));--color-workflow-card-header-shadow:rgba(27, 31, 35, 0.04);--color-workflow-card-progress-complete-bg:var(--color-scale-blue-5);--color-workflow-card-progress-incomplete-bg:var(--color-scale-gray-6);--color-discussions-state-answered-icon:var(--color-scale-green-3);--color-bg-discussions-row-emoji-box:var(--color-scale-gray-6);--color-notifications-button-text:var(--color-scale-white);--color-notifications-button-hover-text:var(--color-scale-white);--color-notifications-button-hover-bg:var(--color-scale-blue-4);--color-notifications-row-read-bg:var(--color-canvas-default);--color-notifications-row-bg:var(--bgColor-muted, var(--color-canvas-subtle));--color-icon-directory:var(--fgColor-muted, var(--color-fg-muted));--color-checks-step-error-icon:var(--color-scale-red-4);--color-calendar-halloween-graph-day-L1-bg:#631c03;--color-calendar-halloween-graph-day-L2-bg:#bd561d;--color-calendar-halloween-graph-day-L3-bg:#fa7a18;--color-calendar-halloween-graph-day-L4-bg:#fddf68;--color-calendar-winter-graph-day-L1-bg:#0A3069;--color-calendar-winter-graph-day-L2-bg:#0969DA;--color-calendar-winter-graph-day-L3-bg:#54AEFF;--color-calendar-winter-graph-day-L4-bg:#B6E3FF;--color-calendar-graph-day-bg:var(--color-scale-gray-8);--color-calendar-graph-day-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L1-bg:#0e4429;--color-calendar-graph-day-L2-bg:#006d32;--color-calendar-graph-day-L3-bg:#26a641;--color-calendar-graph-day-L4-bg:#39d353;--color-calendar-graph-day-L1-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L2-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L3-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L4-border:rgba(255, 255, 255, 0.05);--color-user-mention-fg:var(--color-scale-yellow-0);--color-user-mention-bg:var(--color-scale-yellow-8);--color-dashboard-feed-bg:var(--color-scale-gray-9);--color-mktg-btn-shadow-outline:rgba(255, 255, 255, 0.25) 0 0 0 1px inset;--color-marketing-icon-primary:var(--color-scale-blue-2);--color-marketing-icon-secondary:var(--color-scale-blue-5);--color-project-header-bg:var(--color-scale-gray-9);--color-project-sidebar-bg:var(--color-scale-gray-8);--color-project-gradient-in:var(--color-scale-gray-8);--color-project-gradient-out:rgba(22, 27, 34, 0);--color-diff-blob-selected-line-highlight-mix-blend-mode:screen;--color-checks-donut-error:var(--color-scale-red-4);--color-checks-donut-pending:var(--color-scale-yellow-3);--color-checks-donut-success:var(--color-scale-green-4);--color-checks-donut-neutral:var(--color-scale-gray-3);--color-text-white:var(--color-scale-white)}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark]{--color-workflow-card-connector:var(--color-scale-gray-5);--color-workflow-card-connector-bg:var(--color-scale-gray-5);--color-workflow-card-connector-inactive:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-inactive-bg:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-highlight:var(--color-scale-blue-5);--color-workflow-card-connector-highlight-bg:var(--color-scale-blue-5);--color-workflow-card-bg:var(--color-scale-gray-7);--color-workflow-card-inactive-bg:var(--bgColor-inset, var(--color-canvas-inset));--color-workflow-card-header-shadow:rgba(27, 31, 35, 0.04);--color-workflow-card-progress-complete-bg:var(--color-scale-blue-5);--color-workflow-card-progress-incomplete-bg:var(--color-scale-gray-6);--color-discussions-state-answered-icon:var(--color-scale-green-3);--color-bg-discussions-row-emoji-box:var(--color-scale-gray-6);--color-notifications-button-text:var(--color-scale-white);--color-notifications-button-hover-text:var(--color-scale-white);--color-notifications-button-hover-bg:var(--color-scale-blue-4);--color-notifications-row-read-bg:var(--color-canvas-default);--color-notifications-row-bg:var(--bgColor-muted, var(--color-canvas-subtle));--color-icon-directory:var(--fgColor-muted, var(--color-fg-muted));--color-checks-step-error-icon:var(--color-scale-red-4);--color-calendar-halloween-graph-day-L1-bg:#631c03;--color-calendar-halloween-graph-day-L2-bg:#bd561d;--color-calendar-halloween-graph-day-L3-bg:#fa7a18;--color-calendar-halloween-graph-day-L4-bg:#fddf68;--color-calendar-winter-graph-day-L1-bg:#0A3069;--color-calendar-winter-graph-day-L2-bg:#0969DA;--color-calendar-winter-graph-day-L3-bg:#54AEFF;--color-calendar-winter-graph-day-L4-bg:#B6E3FF;--color-calendar-graph-day-bg:var(--color-scale-gray-8);--color-calendar-graph-day-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L1-bg:#0e4429;--color-calendar-graph-day-L2-bg:#006d32;--color-calendar-graph-day-L3-bg:#26a641;--color-calendar-graph-day-L4-bg:#39d353;--color-calendar-graph-day-L1-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L2-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L3-border:rgba(255, 255, 255, 0.05);--color-calendar-graph-day-L4-border:rgba(255, 255, 255, 0.05);--color-user-mention-fg:var(--color-scale-yellow-0);--color-user-mention-bg:var(--color-scale-yellow-8);--color-dashboard-feed-bg:var(--color-scale-gray-9);--color-mktg-btn-shadow-outline:rgba(255, 255, 255, 0.25) 0 0 0 1px inset;--color-marketing-icon-primary:var(--color-scale-blue-2);--color-marketing-icon-secondary:var(--color-scale-blue-5);--color-project-header-bg:var(--color-scale-gray-9);--color-project-sidebar-bg:var(--color-scale-gray-8);--color-project-gradient-in:var(--color-scale-gray-8);--color-project-gradient-out:rgba(22, 27, 34, 0);--color-diff-blob-selected-line-highlight-mix-blend-mode:screen;--color-checks-donut-error:var(--color-scale-red-4);--color-checks-donut-pending:var(--color-scale-yellow-3);--color-checks-donut-success:var(--color-scale-green-4);--color-checks-donut-neutral:var(--color-scale-gray-3);--color-text-white:var(--color-scale-white)}}:root,[data-color-mode=light][data-light-theme*=light],[data-color-mode=dark][data-dark-theme*=light]{--color-workflow-card-connector:var(--color-scale-gray-3);--color-workflow-card-connector-bg:var(--color-scale-gray-3);--color-workflow-card-connector-inactive:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-inactive-bg:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-highlight:var(--color-scale-blue-4);--color-workflow-card-connector-highlight-bg:var(--color-scale-blue-4);--color-workflow-card-bg:var(--color-scale-white);--color-workflow-card-inactive-bg:var(--bgColor-inset, var(--color-canvas-inset));--color-workflow-card-header-shadow:rgba(0, 0, 0, 0);--color-workflow-card-progress-complete-bg:var(--color-scale-blue-4);--color-workflow-card-progress-incomplete-bg:var(--color-scale-gray-2);--color-discussions-state-answered-icon:var(--color-scale-white);--color-bg-discussions-row-emoji-box:rgba(209, 213, 218, 0.5);--color-notifications-button-text:var(--fgColor-muted, var(--color-fg-muted));--color-notifications-button-hover-text:var(--fgColor-default, var(--color-fg-default));--color-notifications-button-hover-bg:var(--color-scale-gray-2);--color-notifications-row-read-bg:var(--bgColor-muted, var(--color-canvas-subtle));--color-notifications-row-bg:var(--color-scale-white);--color-icon-directory:var(--color-scale-blue-3);--color-checks-step-error-icon:var(--color-scale-red-4);--color-calendar-halloween-graph-day-L1-bg:#ffee4a;--color-calendar-halloween-graph-day-L2-bg:#ffc501;--color-calendar-halloween-graph-day-L3-bg:#fe9600;--color-calendar-halloween-graph-day-L4-bg:#03001c;--color-calendar-winter-graph-day-L1-bg:#B6E3FF;--color-calendar-winter-graph-day-L2-bg:#54AEFF;--color-calendar-winter-graph-day-L3-bg:#0969DA;--color-calendar-winter-graph-day-L4-bg:#0A3069;--color-calendar-graph-day-bg:#ebedf0;--color-calendar-graph-day-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L1-bg:#9be9a8;--color-calendar-graph-day-L2-bg:#40c463;--color-calendar-graph-day-L3-bg:#30a14e;--color-calendar-graph-day-L4-bg:#216e39;--color-calendar-graph-day-L1-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L2-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L3-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L4-border:rgba(27, 31, 35, 0.06);--color-user-mention-fg:var(--fgColor-default, var(--color-fg-default));--color-user-mention-bg:var(--bgColor-attention-muted, var(--color-attention-subtle));--color-dashboard-feed-bg:var(--color-scale-white);--color-mktg-btn-shadow-outline:rgba(0, 0, 0, 0.15) 0 0 0 1px inset;--color-marketing-icon-primary:var(--color-scale-blue-4);--color-marketing-icon-secondary:var(--color-scale-blue-3);--color-project-header-bg:var(--color-scale-gray-9);--color-project-sidebar-bg:var(--color-scale-white);--color-project-gradient-in:var(--color-scale-white);--color-project-gradient-out:rgba(255, 255, 255, 0);--color-diff-blob-selected-line-highlight-mix-blend-mode:multiply;--color-checks-donut-error:var(--color-scale-red-4);--color-checks-donut-pending:var(--color-scale-yellow-4);--color-checks-donut-success:var(--color-success-emphasis);--color-checks-donut-neutral:var(--color-scale-gray-3);--color-text-white:var(--color-scale-white)}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=light]{--color-workflow-card-connector:var(--color-scale-gray-3);--color-workflow-card-connector-bg:var(--color-scale-gray-3);--color-workflow-card-connector-inactive:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-inactive-bg:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-highlight:var(--color-scale-blue-4);--color-workflow-card-connector-highlight-bg:var(--color-scale-blue-4);--color-workflow-card-bg:var(--color-scale-white);--color-workflow-card-inactive-bg:var(--bgColor-inset, var(--color-canvas-inset));--color-workflow-card-header-shadow:rgba(0, 0, 0, 0);--color-workflow-card-progress-complete-bg:var(--color-scale-blue-4);--color-workflow-card-progress-incomplete-bg:var(--color-scale-gray-2);--color-discussions-state-answered-icon:var(--color-scale-white);--color-bg-discussions-row-emoji-box:rgba(209, 213, 218, 0.5);--color-notifications-button-text:var(--fgColor-muted, var(--color-fg-muted));--color-notifications-button-hover-text:var(--fgColor-default, var(--color-fg-default));--color-notifications-button-hover-bg:var(--color-scale-gray-2);--color-notifications-row-read-bg:var(--bgColor-muted, var(--color-canvas-subtle));--color-notifications-row-bg:var(--color-scale-white);--color-icon-directory:var(--color-scale-blue-3);--color-checks-step-error-icon:var(--color-scale-red-4);--color-calendar-halloween-graph-day-L1-bg:#ffee4a;--color-calendar-halloween-graph-day-L2-bg:#ffc501;--color-calendar-halloween-graph-day-L3-bg:#fe9600;--color-calendar-halloween-graph-day-L4-bg:#03001c;--color-calendar-winter-graph-day-L1-bg:#B6E3FF;--color-calendar-winter-graph-day-L2-bg:#54AEFF;--color-calendar-winter-graph-day-L3-bg:#0969DA;--color-calendar-winter-graph-day-L4-bg:#0A3069;--color-calendar-graph-day-bg:#ebedf0;--color-calendar-graph-day-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L1-bg:#9be9a8;--color-calendar-graph-day-L2-bg:#40c463;--color-calendar-graph-day-L3-bg:#30a14e;--color-calendar-graph-day-L4-bg:#216e39;--color-calendar-graph-day-L1-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L2-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L3-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L4-border:rgba(27, 31, 35, 0.06);--color-user-mention-fg:var(--fgColor-default, var(--color-fg-default));--color-user-mention-bg:var(--bgColor-attention-muted, var(--color-attention-subtle));--color-dashboard-feed-bg:var(--color-scale-white);--color-mktg-btn-shadow-outline:rgba(0, 0, 0, 0.15) 0 0 0 1px inset;--color-marketing-icon-primary:var(--color-scale-blue-4);--color-marketing-icon-secondary:var(--color-scale-blue-3);--color-project-header-bg:var(--color-scale-gray-9);--color-project-sidebar-bg:var(--color-scale-white);--color-project-gradient-in:var(--color-scale-white);--color-project-gradient-out:rgba(255, 255, 255, 0);--color-diff-blob-selected-line-highlight-mix-blend-mode:multiply;--color-checks-donut-error:var(--color-scale-red-4);--color-checks-donut-pending:var(--color-scale-yellow-4);--color-checks-donut-success:var(--color-success-emphasis);--color-checks-donut-neutral:var(--color-scale-gray-3);--color-text-white:var(--color-scale-white)}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=light]{--color-workflow-card-connector:var(--color-scale-gray-3);--color-workflow-card-connector-bg:var(--color-scale-gray-3);--color-workflow-card-connector-inactive:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-inactive-bg:var(--borderColor-default, var(--color-border-default));--color-workflow-card-connector-highlight:var(--color-scale-blue-4);--color-workflow-card-connector-highlight-bg:var(--color-scale-blue-4);--color-workflow-card-bg:var(--color-scale-white);--color-workflow-card-inactive-bg:var(--bgColor-inset, var(--color-canvas-inset));--color-workflow-card-header-shadow:rgba(0, 0, 0, 0);--color-workflow-card-progress-complete-bg:var(--color-scale-blue-4);--color-workflow-card-progress-incomplete-bg:var(--color-scale-gray-2);--color-discussions-state-answered-icon:var(--color-scale-white);--color-bg-discussions-row-emoji-box:rgba(209, 213, 218, 0.5);--color-notifications-button-text:var(--fgColor-muted, var(--color-fg-muted));--color-notifications-button-hover-text:var(--fgColor-default, var(--color-fg-default));--color-notifications-button-hover-bg:var(--color-scale-gray-2);--color-notifications-row-read-bg:var(--bgColor-muted, var(--color-canvas-subtle));--color-notifications-row-bg:var(--color-scale-white);--color-icon-directory:var(--color-scale-blue-3);--color-checks-step-error-icon:var(--color-scale-red-4);--color-calendar-halloween-graph-day-L1-bg:#ffee4a;--color-calendar-halloween-graph-day-L2-bg:#ffc501;--color-calendar-halloween-graph-day-L3-bg:#fe9600;--color-calendar-halloween-graph-day-L4-bg:#03001c;--color-calendar-winter-graph-day-L1-bg:#B6E3FF;--color-calendar-winter-graph-day-L2-bg:#54AEFF;--color-calendar-winter-graph-day-L3-bg:#0969DA;--color-calendar-winter-graph-day-L4-bg:#0A3069;--color-calendar-graph-day-bg:#ebedf0;--color-calendar-graph-day-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L1-bg:#9be9a8;--color-calendar-graph-day-L2-bg:#40c463;--color-calendar-graph-day-L3-bg:#30a14e;--color-calendar-graph-day-L4-bg:#216e39;--color-calendar-graph-day-L1-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L2-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L3-border:rgba(27, 31, 35, 0.06);--color-calendar-graph-day-L4-border:rgba(27, 31, 35, 0.06);--color-user-mention-fg:var(--fgColor-default, var(--color-fg-default));--color-user-mention-bg:var(--bgColor-attention-muted, var(--color-attention-subtle));--color-dashboard-feed-bg:var(--color-scale-white);--color-mktg-btn-shadow-outline:rgba(0, 0, 0, 0.15) 0 0 0 1px inset;--color-marketing-icon-primary:var(--color-scale-blue-4);--color-marketing-icon-secondary:var(--color-scale-blue-3);--color-project-header-bg:var(--color-scale-gray-9);--color-project-sidebar-bg:var(--color-scale-white);--color-project-gradient-in:var(--color-scale-white);--color-project-gradient-out:rgba(255, 255, 255, 0);--color-diff-blob-selected-line-highlight-mix-blend-mode:multiply;--color-checks-donut-error:var(--color-scale-red-4);--color-checks-donut-pending:var(--color-scale-yellow-4);--color-checks-donut-success:var(--color-success-emphasis);--color-checks-donut-neutral:var(--color-scale-gray-3);--color-text-white:var(--color-scale-white)}}.hx_color-icon-directory{color:var(--color-icon-directory)}.hx_comment-box--tip::after{background-image:linear-gradient(var(--bgColor-default, var(--color-canvas-default)), var(--bgColor-default, var(--color-canvas-default))) !important}.hx_keyword-hl{background-color:var(--highlight-neutral-bgColor, var(--color-search-keyword-hl))}.hx_dot-fill-pending-icon{color:var(--fgColor-attention, var(--color-attention-emphasis)) !important}@media(max-width: 543px){[data-color-mode=light][data-light-theme*=dark],[data-color-mode=dark][data-dark-theme*=dark]{--color-fg-default: var(--color-scale-gray-0);--color-canvas-default: var(--color-scale-gray-8)}}@media(max-width: 543px)and (prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark]{--color-fg-default: var(--color-scale-gray-0);--color-canvas-default: var(--color-scale-gray-8)}}@media(max-width: 543px)and (prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark]{--color-fg-default: var(--color-scale-gray-0);--color-canvas-default: var(--color-scale-gray-8)}}:root[data-color-mode=dark] .entry-content [href$="#gh-light-mode-only"],:root[data-color-mode=dark] .comment-body [href$="#gh-light-mode-only"],:root[data-color-mode=dark] .readme [href$="#gh-light-mode-only"]{display:none}:root[data-color-mode=light] .entry-content [href$="#gh-dark-mode-only"],:root[data-color-mode=light] .comment-body [href$="#gh-dark-mode-only"],:root[data-color-mode=light] .readme [href$="#gh-dark-mode-only"]{display:none}@media(prefers-color-scheme: dark){:root[data-color-mode=auto] .entry-content [href$="#gh-light-mode-only"],:root[data-color-mode=auto] .comment-body [href$="#gh-light-mode-only"],:root[data-color-mode=auto] .readme [href$="#gh-light-mode-only"]{display:none}}@media(prefers-color-scheme: light){:root[data-color-mode=auto] .entry-content [href$="#gh-dark-mode-only"],:root[data-color-mode=auto] .comment-body [href$="#gh-dark-mode-only"],:root[data-color-mode=auto] .readme [href$="#gh-dark-mode-only"]{display:none}}.colorblind-themes-v1{--color-open-fg: var(--fgColor-success, var(--color-success-fg));--color-open-emphasis: var(--bgColor-success-emphasis, var(--color-success-emphasis));--color-open-muted: var(--bgColor-success-muted, var(--color-success-muted));--color-open-subtle: var(--bgColor-success-muted, var(--color-success-subtle));--color-closed-fg: var(--fgColor-danger, var(--color-danger-fg));--color-closed-emphasis: var(--bgColor-danger-emphasis, var(--color-danger-emphasis));--color-closed-muted: var(--bgColor-danger-muted, var(--color-danger-muted));--color-closed-subtle: var(--bgColor-danger-muted, var(--color-danger-subtle))}.dropdown-item:focus [class*=color-text-],.dropdown-item:hover [class*=color-text-]{color:inherit !important}.filter-item.selected [class*=color-text-]{color:inherit !important}body:not(.intent-mouse) .hx_focus-input:focus+.hx_focus-target{box-shadow:0 0 0 2px var(--borderColor-accent-emphasis, var(--color-accent-fg))}.reset-btn-override{font-weight:var(--base-text-weight-semibold, 600);color:var(--fgColor-muted, var(--color-fg-muted))}.reset-btn-override:hover{color:var(--fgColor-accent, var(--color-accent-fg));text-decoration:none}.reset-btn-override:hover .reset-btn-override-icon{background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.reset-btn-override-icon{width:18px;height:18px;padding:1px;margin-right:3px;color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));text-align:center;background-color:var(--bgColor-neutral-emphasis, var(--color-neutral-emphasis));border-radius:6px}.is-auto-complete-loading .form-control{padding-right:30px;background-repeat:no-repeat;background-position-x:center;background-position-y:center;background-size:16px}[data-color-mode=dark] .is-auto-complete-loading .form-control{background-image:url("/images/spinners/octocat-spinner-darkmode.svg") !important}[data-color-mode=light] .is-auto-complete-loading .form-control{background-image:url("/images/spinners/octocat-spinner-lightmode.svg") !important}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme^=light] .is-auto-complete-loading .form-control{background-image:url("/images/spinners/octocat-spinner-lightmode.svg") !important}[data-color-mode=auto][data-dark-theme^=dark] .is-auto-complete-loading .form-control{background-image:url("/images/spinners/octocat-spinner-darkmode.svg") !important}}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme^=light] .is-auto-complete-loading .form-control{background-image:url("/images/spinners/octocat-spinner-lightmode.svg") !important}[data-color-mode=auto][data-light-theme^=dark] .is-auto-complete-loading .form-control{background-image:url("/images/spinners/octocat-spinner-darkmode.svg") !important}}.hx_breadcrumb-header-crumbs .Header-link{transition:opacity .1s ease-out}.hx_breadcrumb-header-crumbs .Header-link:hover{color:var(--header-fgColor-default, var(--color-header-text));opacity:.75}.hx_breadcrumb-header-divider{color:var(--header-borderColor-divider, var(--color-header-divider))}.Header-button{background-color:var(--color-scale-gray-8);border:1px solid var(--color-scale-gray-6);border-radius:6px;transition:background-color .2s cubic-bezier(0.3, 0, 0.5, 1)}.Header-button .octicon{color:var(--header-fgColor-logo, var(--color-header-logo))}.Header-button:hover,.Header-button:focus,.Header-button:active{background-color:transparent}.Header-button:hover .octicon,.Header-button:focus .octicon,.Header-button:active .octicon{color:var(--header-fgColor-default, var(--color-header-text));box-shadow:none}.hx_breadcrumb-header-dropdown::before,.hx_breadcrumb-header-dropdown::after{display:none}.hx_breadcrumb-header-dropdown .dropdown-item{line-height:40px;transition:background-color 60ms ease-out}.hx_breadcrumb-header-dropdown .dropdown-item:hover{color:var(--fgColor-default, var(--color-fg-default));background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.icon-sponsor,.icon-sponsoring{transition:transform .15s cubic-bezier(0.2, 0, 0.13, 2);transform:scale(1)}.btn:hover .icon-sponsor,.btn:focus .icon-sponsor,.Label:hover .icon-sponsor,.Label:focus .icon-sponsor,.btn:hover .icon-sponsoring,.btn:focus .icon-sponsoring,.Label:hover .icon-sponsoring,.Label:focus .icon-sponsoring{transform:scale(1.1)}.icon-sponsor{overflow:visible !important}.hx_kbd{display:inline-block;min-width:21px;padding:0 4px;font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji";font-size:12px;font-weight:var(--base-text-weight-normal, 400);line-height:1.5;color:var(--fgColor-muted, var(--color-fg-muted));text-align:center;background-color:var(--bgColor-default, var(--color-canvas-default));border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px;box-shadow:none}.hx_hit-user em,.hx_hit-package em,.hx_hit-marketplace em,.hx_hit-highlighting-wrapper em,.hx_hit-commit em,.hx_hit-issue em,.hx_hit-repo em,.hx_hit-wiki em{font-style:normal;font-weight:var(--base-text-weight-semibold, 600)}.SelectMenu-list.select-menu-list{max-height:none}@media(max-width: 543px){.SelectMenu-modal{width:unset !important}}.SelectMenu--hasFilter .SelectMenu-list{contain:content}.SelectMenu-item:disabled,.SelectMenu-item[aria-disabled=true]{color:var(--fgColor-muted, var(--color-fg-muted));pointer-events:none}.SelectMenu .SelectMenu-item .is-filtering{color:var(--fgColor-muted, var(--color-fg-muted))}.SelectMenu .SelectMenu-item .is-filtering b{color:var(--fgColor-default, var(--color-fg-default))}label.SelectMenu-item{font-weight:var(--base-text-weight-normal, 400)}label.SelectMenu-item[aria-checked=true]{font-weight:var(--base-text-weight-semibold, 600)}.hx_SelectMenu-modal-no-animation{animation:none}.SelectMenu-item.focused{background-color:var(--bgColor-neutral-muted, var(--color-neutral-subtle))}.Box--responsive{margin-right:-15px;margin-left:-15px;border-right:0;border-left:0;border-radius:0}.Box--responsive .Box-row--unread{position:relative;box-shadow:none}.Box--responsive .Box-row--unread::before{position:absolute;top:36px;left:20px;display:inline-block;width:8px;height:8px;color:#fff;content:"";background-image:linear-gradient(#54a3ff, #006eed);background-clip:padding-box;border-radius:50%}.Box--responsive .Box-header{border-right-width:0;border-left-width:0;border-radius:0}@media(min-width: 544px){.Box--responsive{margin-right:0;margin-left:0;border:1px solid var(--borderColor-default, var(--color-border-default));border-radius:6px}.Box--responsive .Box-header{border-right-width:1px;border-left-width:1px;border-top-left-radius:6px;border-top-right-radius:6px}.Box--responsive .Box-row--unread{box-shadow:2px 0 0 var(--borderColor-accent-emphasis, var(--color-accent-emphasis)) inset}.Box--responsive .Box-row--unread::before{display:none}}@media(max-width: 767px){.page-responsive .dropdown-menu,.page-responsive .dropdown-item{padding-top:8px;padding-bottom:8px}.page-responsive .hx_dropdown-fullscreen[open]>summary::before{background-color:var(--overlay-backdrop-bgColor, var(--color-primer-canvas-backdrop))}.page-responsive .hx_dropdown-fullscreen .dropdown-menu{position:fixed;top:auto;right:16px !important;bottom:20%;left:16px !important;width:auto !important;max-width:none !important;max-height:calc(80% - 16px);margin:0 !important;overflow-y:auto;transform:none;animation:dropdown-menu-animation .24s cubic-bezier(0, 0.1, 0.1, 1) backwards;-webkit-overflow-scrolling:touch}.page-responsive .hx_dropdown-fullscreen .dropdown-menu::before,.page-responsive .hx_dropdown-fullscreen .dropdown-menu::after{display:none}@keyframes dropdown-menu-animation{0%{opacity:0;transform:scale(0.9)}}.page-responsive .hx_dropdown-fullscreen .dropdown-item{padding-top:16px;padding-bottom:16px}}.hx_rsm-close-button{display:none !important}@media(max-width: 767px){.page-responsive .hx_rsm[open]>summary::before{background-color:var(--overlay-backdrop-bgColor, var(--color-primer-canvas-backdrop))}.page-responsive .hx_rsm .select-menu-modal,.page-responsive .hx_rsm-modal{position:fixed !important;top:75px;right:16px !important;left:16px;display:flex;width:auto;height:80%;margin:0;flex-direction:column;animation:hx_rsm-modal-animation .24s .12s cubic-bezier(0, 0.1, 0.1, 1) backwards}.page-responsive .hx_rsm--auto-height .select-menu-modal{top:auto;bottom:20%;height:auto;max-height:calc(80% - 16px)}.page-responsive .hx_rsm .select-menu-header,.page-responsive .hx_rsm .select-menu-text-filter.select-menu-text-filter{padding:16px;border-top-left-radius:inherit;border-top-right-radius:inherit}.page-responsive .hx_rsm tab-container,.page-responsive .hx_rsm-content{display:flex;min-height:0;flex-direction:column;flex:auto}.page-responsive .hx_rsm .select-menu-list{flex:auto;max-height:none;-webkit-overflow-scrolling:touch}.page-responsive .hx_rsm-content>.select-menu-item{flex-shrink:0}.page-responsive .hx_rsm .select-menu-item{padding-top:16px;padding-bottom:16px;padding-left:40px}.page-responsive .hx_rsm .close-button,.page-responsive .hx_rsm-close-button{position:relative;display:block !important}.page-responsive .hx_rsm .close-button::before,.page-responsive .hx_rsm-close-button::before{position:absolute;top:-16px;right:-16px;bottom:-16px;left:-16px;content:""}.page-responsive .hx_rsm .close-button .octicon-x,.page-responsive .hx_rsm-close-button .octicon-x{color:var(--fgColor-muted, var(--color-fg-muted))}.page-responsive .hx_rsm .select-menu-loading-overlay{animation-delay:1s}.page-responsive .hx_rsm .select-menu-button::before,.page-responsive .hx_rsm-trigger::before{animation:hx_rsm-trigger-animation .24s cubic-bezier(0, 0, 0.2, 1) backwards}@keyframes hx_rsm-trigger-animation{0%{opacity:0}}@keyframes hx_rsm-modal-animation{0%{opacity:0;transform:scale(0.9)}}.page-responsive .hx_rsm-dialog{max-width:none;height:auto;max-height:80%;transform:none}.page-responsive .hx_rsm-dialog-content{flex:1;min-height:0}}@media(max-width: 767px)and (max-height: 500px){.page-responsive .hx_rsm .select-menu-modal,.page-responsive .hx_rsm-modal{bottom:16px;height:auto}}.select-menu-modal{border-color:var(--borderColor-default, var(--color-border-default));box-shadow:var(--shadow-floating-large, var(--color-shadow-large))}.select-menu-header,.select-menu-filters{background:var(--overlay-bgColor, var(--color-canvas-overlay))}.select-menu-text-filter input{padding:5px 12px}.select-menu-item{text-align:left;background-color:var(--overlay-bgColor, var(--color-canvas-overlay));border-top:0;border-right:0;border-left:0}.preview-selected .tabnav--responsive{border-bottom:1px solid var(--borderColor-default, var(--color-border-default))}.tabnav--responsive .tabnav-tabs{z-index:1}@media(max-width: 767px){.tabnav--responsive .tabnav-tab{background-color:var(--bgColor-muted, var(--color-canvas-subtle));border:1px solid var(--borderColor-default, var(--color-border-default));border-left:0;border-radius:0}.tabnav--responsive .tabnav-tab:first-child{border-left:1px solid var(--borderColor-default, var(--color-border-default))}.tabnav--responsive .tabnav-tab[aria-selected=true],.tabnav--responsive .tabnav-tab.selected{background-color:var(--bgColor-default, var(--color-canvas-default));border-bottom:0}}@media(max-width: 767px){.hx_sm-hide-drag-drop textarea{border-bottom:1px solid var(--borderColor-default, var(--color-border-default));border-bottom-right-radius:6px;border-bottom-left-radius:6px}.hx_sm-hide-drag-drop .hx_drag-and-drop{display:none !important}}@media(hover: none){.tooltipped:hover::before,.tooltipped:hover::after{display:none}}@media(hover: none){.markdown-body h1 .octicon-link,.markdown-body h2 .octicon-link,.markdown-body h3 .octicon-link,.markdown-body h4 .octicon-link,.markdown-body h5 .octicon-link,.markdown-body h6 .octicon-link{visibility:visible !important}}.min-width-lg{min-width:1012px}.min-width-xl{min-width:1280px}.min-height-0{min-height:0 !important}.ws-pre-wrap{white-space:pre-wrap}.cursor-pointer{cursor:pointer}.cursor-default{cursor:default}@media screen and (prefers-reduced-motion: no-preference){.hide-no-pref-motion{display:none !important;visibility:hidden}}@media screen and (prefers-reduced-motion: reduce){.hide-reduced-motion{display:none !important;visibility:hidden}}.gap-1{gap:4px !important}.gap-2{gap:8px !important}.gap-3{gap:16px !important}.gap-4{gap:24px !important}.color-border-emphasis{border-color:var(--control-borderColor-rest, var(--color-border-default)) !important}.starring-container .unstarred,.starring-container.on .starred{display:flex}.starring-container.on .unstarred,.starring-container .starred{display:none}.starring-container.loading{opacity:.5}.user-following-container .follow,.user-following-container.on .unfollow{display:inline-block}.user-following-container.on .follow,.user-following-container .unfollow{display:none}.user-following-container.loading{opacity:.5}.hidden-when-empty:empty{display:none !important}.cm-number,.cm-atom{color:var(--codeMirror-syntax-fgColor-constant, var(--color-codemirror-syntax-constant))}dl.form-group>dd .form-control.is-autocheck-loading,dl.form-group>dd .form-control.is-autocheck-successful,dl.form-group>dd .form-control.is-autocheck-errored,.form-group>.form-group-body .form-control.is-autocheck-loading,.form-group>.form-group-body .form-control.is-autocheck-successful,.form-group>.form-group-body .form-control.is-autocheck-errored{padding-right:30px}dl.form-group>dd .form-control.is-autocheck-loading,.form-group>.form-group-body .form-control.is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-16px.gif")}dl.form-group>dd .form-control.is-autocheck-successful,.form-group>.form-group-body .form-control.is-autocheck-successful{background-image:url("/images/modules/ajax/success.png")}dl.form-group>dd .form-control.is-autocheck-errored,.form-group>.form-group-body .form-control.is-autocheck-errored{background-image:url("/images/modules/ajax/error.png")}@media only screen and (-webkit-min-device-pixel-ratio: 2),only screen and (-moz-min-device-pixel-ratio: 2),only screen and (min-device-pixel-ratio: 2),only screen and (min-resolution: 192dpi),only screen and (min-resolution: 2dppx){dl.form-group>dd .form-control.is-autocheck-loading,dl.form-group>dd .form-control.is-autocheck-successful,dl.form-group>dd .form-control.is-autocheck-errored,.form-group>.form-group-body .form-control.is-autocheck-loading,.form-group>.form-group-body .form-control.is-autocheck-successful,.form-group>.form-group-body .form-control.is-autocheck-errored{background-size:16px 16px}dl.form-group>dd .form-control.is-autocheck-loading,.form-group>.form-group-body .form-control.is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-32.gif")}dl.form-group>dd .form-control.is-autocheck-successful,.form-group>.form-group-body .form-control.is-autocheck-successful{background-image:url("/images/modules/ajax/<EMAIL>")}dl.form-group>dd .form-control.is-autocheck-errored,.form-group>.form-group-body .form-control.is-autocheck-errored{background-image:url("/images/modules/ajax/<EMAIL>")}}[data-color-mode=dark] auto-check .is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-darkmode.svg") !important;background-size:16px 16px}[data-color-mode=light] auto-check .is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-lightmode.svg") !important;background-size:16px 16px}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme^=light] auto-check .is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-lightmode.svg") !important;background-size:16px 16px}[data-color-mode=auto][data-dark-theme^=dark] auto-check .is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-darkmode.svg") !important;background-size:16px 16px}}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme^=light] auto-check .is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-lightmode.svg") !important;background-size:16px 16px}[data-color-mode=auto][data-light-theme^=dark] auto-check .is-autocheck-loading{background-image:url("/images/spinners/octocat-spinner-darkmode.svg") !important;background-size:16px 16px}}auto-check .is-autocheck-loading,auto-check .is-autocheck-successful,auto-check .is-autocheck-errored{padding-right:30px;background-repeat:no-repeat;background-position:right 8px center}auto-check .is-autocheck-successful{background-image:url("/images/modules/ajax/success.png")}auto-check .is-autocheck-errored{background-image:url("/images/modules/ajax/error.png")}@media only screen and (-webkit-min-device-pixel-ratio: 2),only screen and (-moz-min-device-pixel-ratio: 2),only screen and (min-device-pixel-ratio: 2),only screen and (min-resolution: 192dpi),only screen and (min-resolution: 2dppx){auto-check .is-autocheck-loading,auto-check .is-autocheck-successful,auto-check .is-autocheck-errored{background-size:16px 16px}auto-check .is-autocheck-successful{background-image:url("/images/modules/ajax/<EMAIL>")}auto-check .is-autocheck-errored{background-image:url("/images/modules/ajax/<EMAIL>")}}.hx_text-body{font-family:-apple-system,BlinkMacSystemFont,"Segoe UI","Noto Sans",Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji" !important}.hx_disabled-form-checkbox-label.form-checkbox.disabled{color:var(--fgColor-muted, var(--color-fg-muted))}.autocomplete-item{background-color:transparent}.ColorSwatch{display:inline-block;width:1em;height:1em;vertical-align:middle;border:1px solid var(--borderColor-muted, var(--color-border-subtle));border-radius:6px}.label-select-menu .color,.ColorSwatch{border-radius:2em}.details-overlay[open]>.dropdown-item:hover{color:inherit;background:var(--bgColor-default, var(--color-canvas-default))}remote-input[loading] .form-control{padding-right:30px;background-image:url("/images/spinners/octocat-spinner-32.gif");background-size:16px}.hx_form-control-spinner{position:absolute;top:24px;right:24px;display:none}@media(min-width: 767px){.hx_form-control-spinner{top:18px;right:18px}}.hx_form-control-spinner-wrapper{position:relative}.hx_form-control-spinner-wrapper .is-loading.form-control{padding-right:28px}.hx_form-control-spinner-wrapper .is-loading+.hx_form-control-spinner{display:block}.drag-and-drop{border-color:var(--borderColor-default, var(--color-border-default))}.input-sm{min-height:28px}.btn .octicon-triangle-down{margin-right:0}.UnderlineNav-item.selected .UnderlineNav-octicon,.UnderlineNav-item[aria-current]:not([aria-current=false]) .UnderlineNav-octicon,.UnderlineNav-item[role=tab][aria-selected=true] .UnderlineNav-octicon{color:inherit}.break-line-anywhere{line-break:anywhere !important}.form-checkbox input[type=checkbox],.form-checkbox input[type=radio]{margin-top:4px}.status-indicator-success::before,.status-indicator-failed::before{content:none}.markdown-title code{padding:2px 4px;font-size:.9em;line-height:1;background-color:var(--bgColor-neutral-muted, var(--color-neutral-muted));border-radius:6px}[data-turbo-body]{isolation:isolate}#__primerPortalRoot__{z-index:1}.hx_ActionList-content>.hx_ActionList-item-label,.hx_ActionList-content>.hx_ActionList-item-visual{pointer-events:none}.IssueLabel--big.lh-condensed{display:inline-block;padding:0 10px;font-size:12px;font-weight:var(--base-text-weight-medium, 500);line-height:22px !important;border:1px solid transparent;border-radius:2em}.hx_IssueLabel{--perceived-lightness: calc( ((var(--label-r) * 0.2126) + (var(--label-g) * 0.7152) + (var(--label-b) * 0.0722)) / 255 );--lightness-switch: max(0, min(calc((1/(var(--lightness-threshold) - var(--perceived-lightness)))), 1))}:root .hx_IssueLabel,[data-color-mode=light][data-light-theme*=light] .hx_IssueLabel,[data-color-mode=dark][data-dark-theme*=light] .hx_IssueLabel{--lightness-threshold: 0.453;--border-threshold: 0.96;--border-alpha: max(0, min(calc((var(--perceived-lightness) - var(--border-threshold)) * 100), 1));color:hsl(0deg, 0%, calc(var(--lightness-switch) * 100%));background:rgb(var(--label-r), var(--label-g), var(--label-b));border-color:hsla(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) - 25) * 1%), var(--border-alpha))}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=light] .hx_IssueLabel{--lightness-threshold: 0.453;--border-threshold: 0.96;--border-alpha: max(0, min(calc((var(--perceived-lightness) - var(--border-threshold)) * 100), 1));color:hsl(0deg, 0%, calc(var(--lightness-switch) * 100%));background:rgb(var(--label-r), var(--label-g), var(--label-b));border-color:hsla(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) - 25) * 1%), var(--border-alpha))}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=light] .hx_IssueLabel{--lightness-threshold: 0.453;--border-threshold: 0.96;--border-alpha: max(0, min(calc((var(--perceived-lightness) - var(--border-threshold)) * 100), 1));color:hsl(0deg, 0%, calc(var(--lightness-switch) * 100%));background:rgb(var(--label-r), var(--label-g), var(--label-b));border-color:hsla(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) - 25) * 1%), var(--border-alpha))}}[data-color-mode=light][data-light-theme*=dark] .hx_IssueLabel,[data-color-mode=dark][data-dark-theme*=dark] .hx_IssueLabel{--lightness-threshold: 0.6;--background-alpha: 0.18;--border-alpha: 0.3;--lighten-by: calc(((var(--lightness-threshold) - var(--perceived-lightness)) * 100) * var(--lightness-switch));color:hsl(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) + var(--lighten-by)) * 1%));background:rgba(var(--label-r), var(--label-g), var(--label-b), var(--background-alpha));border-color:hsla(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) + var(--lighten-by)) * 1%), var(--border-alpha))}@media(prefers-color-scheme: light){[data-color-mode=auto][data-light-theme*=dark] .hx_IssueLabel{--lightness-threshold: 0.6;--background-alpha: 0.18;--border-alpha: 0.3;--lighten-by: calc(((var(--lightness-threshold) - var(--perceived-lightness)) * 100) * var(--lightness-switch));color:hsl(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) + var(--lighten-by)) * 1%));background:rgba(var(--label-r), var(--label-g), var(--label-b), var(--background-alpha));border-color:hsla(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) + var(--lighten-by)) * 1%), var(--border-alpha))}}@media(prefers-color-scheme: dark){[data-color-mode=auto][data-dark-theme*=dark] .hx_IssueLabel{--lightness-threshold: 0.6;--background-alpha: 0.18;--border-alpha: 0.3;--lighten-by: calc(((var(--lightness-threshold) - var(--perceived-lightness)) * 100) * var(--lightness-switch));color:hsl(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) + var(--lighten-by)) * 1%));background:rgba(var(--label-r), var(--label-g), var(--label-b), var(--background-alpha));border-color:hsla(var(--label-h), calc(var(--label-s) * 1%), calc((var(--label-l) + var(--lighten-by)) * 1%), var(--border-alpha))}}.signed-commit-badge-small,.signed-commit-badge-medium,.signed-commit-badge-large{display:inline-block;padding:0 7px;font-size:12px;font-weight:var(--base-text-weight-medium, 500);line-height:18px;white-space:nowrap;border:1px solid transparent;border-radius:2em;border-color:var(--borderColor-default, var(--color-border-default))}.signed-commit-badge-small{margin-top:0}.signed-commit-badge-large{padding-right:10px;padding-left:10px;line-height:22px}.topic-tag-action,.delete-topic-button,.topic-tag{display:inline-block;padding:0 7px;font-size:12px;font-weight:var(--base-text-weight-medium, 500);line-height:18px;white-space:nowrap;border:1px solid transparent;border-radius:2em;padding-right:10px;padding-left:10px;line-height:22px;color:var(--fgColor-accent, var(--color-accent-fg));background-color:var(--bgColor-accent-muted, var(--color-accent-subtle));border:1px solid var(--topicTag-borderColor, var(--color-topic-tag-border), transparent)}.topic-tag-action:active,.topic-tag-action:hover,.delete-topic-button:active,.delete-topic-button:hover,.topic-tag:active,.topic-tag:hover{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--bgColor-accent-emphasis, var(--color-accent-emphasis))}.topic-tag{margin:0 .125em .333em 0}.topic-tag-outline{background:transparent}.topic-tag-action{display:inline-flex;padding-right:0;margin:.6em .5em 0 0}.delete-topic-button,.topic-tag-action .add-topic-button,.topic-tag-action .remove-topic-button{display:flex;width:24px;height:24px;padding:0;color:inherit;border-color:transparent;border-left:0;border-radius:2em;align-items:center;justify-content:center}.hx_Layout.hx_Layout--sidebar-hidden{grid-auto-flow:row;grid-gap:0;grid-template-columns:1fr}.hx_Layout.hx_Layout--sidebar-hidden .Layout-sidebar{display:none}.hx_Layout.hx_Layout--sidebar-hidden .Layout-main{grid-column:auto}.hx_Layout--sidebar{top:60px;box-sizing:border-box;overscroll-behavior:contain}.branch-action-item.color-border-default{border-color:var(--borderColor-default, var(--color-border-default)) !important}.user-status-container .input-group-button .btn{height:32px}.reponav-item,.pagehead-tabs-item{border-radius:4px 4px 0 0}.reponav-item.selected,.pagehead-tabs-item.selected{border-top-color:#f9826c}.auto-search-group>.octicon{top:8px}.subnav-search>button.mt-2{margin-top:6px !important}.completeness-indicator-success{color:var(--fgColor-onEmphasis, var(--color-fg-on-emphasis));background-color:var(--button-primary-bgColor-rest, var(--color-btn-primary-bg))}.pagination-loader-container button.color-bg-default.border-0{border-top-left-radius:6px;border-top-right-radius:6px}.avatar-user{border-radius:50% !important}@media(max-width: 543px){.minimized-comment>details>div{padding-left:0 !important}}@media(max-width: 543px){.minimized-comment>details>summary>div{flex-direction:column}.minimized-comment>details>summary>div .review-comment-contents{align-left:flex-start}}.hx_disabled-input{margin-right:-4px !important;margin-left:-4px !important}.hx_disabled-input sidebar-memex-input[disabled] *{cursor:pointer}.hx_disabled-input sidebar-memex-input:not([disabled]) .Box-row--hover-gray{background-color:var(--bgColor-muted, var(--color-canvas-subtle))}.hx_disabled-input .Box-row--hover-gray svg.octicon-pencil{visibility:hidden;opacity:0}.hx_disabled-input .Box-row--hover-gray:hover,.hx_disabled-input .Box-row--hover-gray:focus{padding-top:8px !important;padding-bottom:8px !important}.hx_disabled-input .Box-row--hover-gray:hover svg.octicon-pencil,.hx_disabled-input .Box-row--hover-gray:focus svg.octicon-pencil{visibility:visible;opacity:1}.hx_disabled-input input:not(:disabled){margin-top:8px !important;margin-bottom:8px !important}.hx_disabled-input input[disabled],.hx_disabled-input select[disabled],.hx_disabled-input .form-control[contenteditable=false]{padding-right:0;padding-left:0;margin-right:0;color:var(--fgColor-default, var(--color-fg-default)) !important;background:transparent;border:0;box-shadow:none;opacity:1}.hx_disabled-input text-expander input[type=text][disabled]{display:none}.hx_disabled-input text-expander input[type=text][disabled]+div.form-control{display:block}.hx_disabled-input text-expander input[type=text]+div.form-control{display:none}.hx_disabled-input input[type=number][disabled]{display:none}.hx_disabled-input input[type=number][disabled]+div.form-control{display:block}.hx_disabled-input input[type=number]+div.form-control{display:none}.hx_disabled-input input[type=date][disabled]{display:none}.hx_disabled-input input[type=date][disabled]+div.form-control{display:block}.hx_disabled-input input[type=date]+div.form-control{display:none}.hx_disabled-input input[disabled]::placeholder,.hx_disabled-input selected[disabled]::placeholder{color:var(--fgColor-default, var(--color-fg-default)) !important}.hx_disabled-input .form-select{background-image:none !important}.hx_disabled-input .Box-row--focus-gray:focus{background:var(--bgColor-muted, var(--color-canvas-subtle))}.summary-iteration .inline-status{display:none}.summary-iteration .block-status{display:inline-block}.list-iteration .inline-status{display:inline}.list-iteration .block-status{display:none}.hx_tabnav-in-dropdown{border-radius:5px 5px 0 0}.hx_tabnav-in-dropdown .tabnav-tabs .hx_tabnav-in-dropdown-wrapper:first-child .tabnav-tab.selected,.hx_tabnav-in-dropdown .tabnav-tabs .hx_tabnav-in-dropdown-wrapper:first-child .tabnav-tab[aria-selected=true],.hx_tabnav-in-dropdown .tabnav-tabs .hx_tabnav-in-dropdown-wrapper:first-child .tabnav-tab[aria-current]:not([aria-current=false]){border-left:0}.hx_tabnav-in-dropdown .tabnav-tabs .hx_tabnav-in-dropdown-wrapper:last-child .tabnav-tab.selected,.hx_tabnav-in-dropdown .tabnav-tabs .hx_tabnav-in-dropdown-wrapper:last-child .tabnav-tab[aria-selected=true],.hx_tabnav-in-dropdown .tabnav-tabs .hx_tabnav-in-dropdown-wrapper:last-child .tabnav-tab[aria-current]:not([aria-current=false]){border-right:0}.hx_tabnav-in-dropdown .tabnav-tab.selected,.hx_tabnav-in-dropdown .tabnav-tab[aria-selected=true],.hx_tabnav-in-dropdown .tabnav-tab[aria-current]:not([aria-current=false]){margin-top:-1px;background-color:var(--overlay-bgColor, var(--color-canvas-overlay))}.hx_tabnav-in-dropdown #cloud-tab[aria-selected=false]::after{position:absolute;top:-14px;right:10px;left:auto;z-index:10;display:inline-block;content:"";border:7px solid transparent;border-bottom:7px solid var(--bgColor-muted, var(--color-canvas-subtle))}.details-overlay-dark[open]>summary::before{z-index:111 !important}.turbo-progress-bar{z-index:2147483647}.timeline-comment .previewable-comment-form textarea{max-height:none}.truncate-with-responsive-width{width:50px;min-width:100%}.hx_merge_queue_entry_status_icon{fill:none;background-color:transparent;border:none}.markdown-alert{padding:0 1em;margin-bottom:16px;color:inherit;border-left:.25em solid var(--borderColor-default, var(--color-border-default))}.markdown-alert>:first-child{margin-top:0}.markdown-alert>:last-child{margin-bottom:0}.markdown-alert.markdown-alert-note{border-left-color:var(--borderColor-accent-emphasis, var(--color-accent-fg))}.markdown-alert.markdown-alert-important{border-left-color:var(--borderColor-done-emphasis, var(--color-done-fg))}.markdown-alert.markdown-alert-warning{border-left-color:var(--borderColor-attention-emphasis, var(--color-attention-fg))}

/*# sourceMappingURL=global-b7441a0ed0de.css.map*/