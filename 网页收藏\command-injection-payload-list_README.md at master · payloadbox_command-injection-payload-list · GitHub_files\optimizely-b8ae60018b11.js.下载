"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["optimizely","optimizely-utils","uuid"],{67404:(e,t,n)=>{function o(e){return r(e)[0]}function r(e){let t=[];for(let n of i()){let[o,r]=n.trim().split("=");e===o&&void 0!==r&&t.push({key:o,value:r})}return t}function i(){try{return document.cookie.split(";")}catch{return[]}}function a(e,t,n=null,o=!1,r="lax"){let i=document.domain;if(null==i)throw Error("Unable to get document domain");i.endsWith(".github.com")&&(i="github.com");let a="https:"===location.protocol?"; secure":"",c=n?`; expires=${n}`:"";!1===o&&(i=`.${i}`);try{document.cookie=`${e}=${t}; path=/; domain=${i}${c}${a}; samesite=${r}`}catch{}}function c(e,t=!1){let n=document.domain;if(null==n)throw Error("Unable to get document domain");n.endsWith(".github.com")&&(n="github.com");let o=new Date().getTime(),r=new Date(o-1).toUTCString(),i="https:"===location.protocol?"; secure":"",a=`; expires=${r}`;!1===t&&(n=`.${n}`);try{document.cookie=`${e}=''; path=/; domain=${n}${a}${i}`}catch{}}n.d(t,{$1:()=>r,d8:()=>a,ej:()=>o,kT:()=>c})},31063:(e,t,n)=>{function o(e){return e.toLowerCase().replace(/-(.)/g,function(e,t){return t.toUpperCase()})}function r(e){let t={};for(let{name:n,value:r}of e.attributes)if(n.startsWith("data-optimizely-meta-")){let e=n.replace("data-optimizely-meta-","");r&&r.trim().length&&(t[o(e)]=r)}return t}n.d(t,{t:()=>r})},68379:(e,t,n)=>{let o;var r=n(24601),i=n(89359),a=n(48266),c=n(49237);let l={handleError(e){d(e)}};function u(){s();let e=document.head.querySelector("meta[name=optimizely-datafile]")?.content;return(0,a.Fs)({datafile:e,errorHandler:l})}function s(){let e=m("optimizely.logLevel");e?(0,a.Ub)(e):(0,a.EK)(null)}function m(e){try{return window.localStorage?.getItem(e)}catch(e){return null}}async function d(e){if(!c.Gb||e.message.startsWith("Optimizely::InvalidExperimentError:"))return;let t=document.head?.querySelector('meta[name="browser-optimizely-client-errors-url"]')?.content;if(!t)return;let n={message:e.message,stack:e.stack,stacktrace:(0,r.cI)(e),sanitizedUrl:(0,i.S)()||window.location.href,user:(0,r.aJ)()||void 0};try{await fetch(t,{method:"post",body:JSON.stringify(n)})}catch{}}var f=n(67404),p=n(82918),h=n(36071),y=n(59753),g=n(31063);!async function(){o=u(),await o.onReady()}(),(0,y.on)("click","[data-optimizely-event]",function(e){if(!o)return;let t=e.currentTarget,n=t.getAttribute("data-optimizely-event")||"",[r,i]=n.trim().split(/\s*,\s*/),a=(0,g.t)(t);r&&i?o.track(r,i,a):r&&o.track(r,(0,p.b)(),a)}),(0,h.N7)("[data-optimizely-experiment]",e=>{if(!o)return;let t=e.getAttribute("data-optimizely-experiment");if(!t||e.hidden)return;let n=(0,g.t)(e),r=o.activate(t,(0,p.b)(),n);if(!r)return;let i=e.querySelectorAll("[data-optimizely-variation]");for(let e of i){let t=e.getAttribute("data-optimizely-variation");e.hidden=t!==r}});let b=document.querySelector('meta[name="enabled-homepage-translation-languages"]')?.getAttribute("content")?.split(",")||[],_=(0,f.$1)("_locale_experiment").length>0&&"ko"===(0,f.$1)("_locale_experiment")[0].value&&b.includes("ko");async function w(){let e="ko_homepage_translation",t=(0,p.b)(),n=f.$1("_locale")[0]?.value?.slice(0,2);o.setForcedVariation(e,t,n),o.activate(e,t);let r=document.querySelectorAll("[data-optimizely-variation]");for(let e of r)e.hidden=n!==e.getAttribute("data-optimizely-variation");for(let e of document.querySelectorAll('form[action^="/join"]'))e.addEventListener("submit",()=>{o.track("submit.homepage_signup",t)});for(let e of document.querySelectorAll('a[href^="/join"]'))e.addEventListener("click",()=>{o.track("click.homepage_signup",t)})}async function v(){document.getElementById("signup-form")?.addEventListener("submit",()=>{let e=(0,p.b)();o.activate("ko_homepage_translation",e),o.track("submit.create_account",e)})}async function k(){if(!o)return;let e=(0,p.b)();o.activate("test_experiment",e),o.track("test_event",e)}_&&"/"===window.location.pathname&&w(),_&&"/join"===window.location.pathname&&v(),"/settings/profile"===window.location.pathname&&k()},328:(e,t,n)=>{function o(){return crypto.randomUUID()}n.r(t),n.d(t,{v4:()=>o})},89359:(e,t,n)=>{function o(e){let t=document.querySelectorAll(e);if(t.length>0)return t[t.length-1]}function r(){let e=o("meta[name=analytics-location]");return e?e.content:window.location.pathname}function i(){let e=o("meta[name=analytics-location-query-strip]"),t="";e||(t=window.location.search);let n=o("meta[name=analytics-location-params]");for(let e of(n&&(t+=(t?"&":"?")+n.content),document.querySelectorAll("meta[name=analytics-param-rename]"))){let n=e.content.split(":",2);t=t.replace(RegExp(`(^|[?&])${n[0]}($|=)`,"g"),`$1${n[1]}$2`)}return t}function a(){return`${window.location.protocol}//${window.location.host}${r()+i()}`}n.d(t,{S:()=>a})},24601:(e,t,n)=>{n.d(t,{aJ:()=>E,cI:()=>k,eK:()=>b});var o=n(82918),r=n(49237),i=n(28382),a=n(89359),c=n(68202),l=n(53729),u=n(86283),s=n(46426);let m=!1,d=0,f=Date.now(),p=new Set(["Failed to fetch","NetworkError when attempting to fetch resource."]);function h(e){return e instanceof Error||"object"==typeof e&&null!==e&&"name"in e&&"string"==typeof e.name&&"message"in e&&"string"==typeof e.message}function y(e){try{return JSON.stringify(e)}catch{return"Unserializable"}}function g(e){return!!("AbortError"===e.name||"TypeError"===e.name&&p.has(e.message)||e.name.startsWith("ApiError")&&p.has(e.message))}function b(e,t={}){if((0,s.c)("FAILBOT_HANDLE_NON_ERRORS")){if(!h(e)){if(x(e))return;let n=Error(),o=y(e),r={type:"UnknownError",value:`Unable to report error, due to a thrown non-Error type: ${typeof e}, with value ${o}`,stacktrace:k(n)};_(v(r,t));return}g(e)||_(v(w(e),t))}else g(e)||_(v(w(e),t))}async function _(e){if(!A())return;let t=document.head?.querySelector('meta[name="browser-errors-url"]')?.content;if(t){if(S(e.error.stacktrace)){m=!0;return}d++;try{await fetch(t,{method:"post",body:JSON.stringify(e)})}catch{}}}function w(e){return{type:e.name,value:e.message,stacktrace:k(e)}}function v(e,t={}){return Object.assign({error:e,sanitizedUrl:(0,a.S)()||window.location.href,readyState:document.readyState,referrer:(0,c.wP)(),timeSinceLoad:Math.round(Date.now()-f),user:E()||void 0,bundler:l.A7,ui:Boolean(document.querySelector('meta[name="ui"]'))},t)}function k(e){return(0,i.Q)(e.stack||"").map(e=>({filename:e.file||"",function:String(e.methodName),lineno:(e.lineNumber||0).toString(),colno:(e.column||0).toString()}))}let $=/(chrome|moz|safari)-extension:\/\//;function S(e){return e.some(e=>$.test(e.filename)||$.test(e.function))}function E(){let e=document.head?.querySelector('meta[name="user-login"]')?.content;if(e)return e;let t=(0,o.b)();return`anonymous-${t}`}let z=!1;function A(){return!z&&!m&&d<10&&(0,r.Gb)()}if(u.iG?.addEventListener("pageshow",()=>z=!1),u.iG?.addEventListener("pagehide",()=>z=!0),"function"==typeof BroadcastChannel){let e=new BroadcastChannel("shared-worker-error");e.addEventListener("message",e=>{b(e.data.error)})}let q=["Object Not Found Matching Id","Not implemented on this platform","provider because it's not your default extension"];function x(e){if(!e||"boolean"==typeof e||"number"==typeof e)return!0;if("string"==typeof e){if(q.some(t=>e.includes(t)))return!0}else if("object"==typeof e&&"string"==typeof e.message&&"number"==typeof e.code)return!0;return!1}}},e=>{var t=t=>e(e.s=t);e.O(0,["vendors-node_modules_stacktrace-parser_dist_stack-trace-parser_esm_js-node_modules_github_bro-a4c183","vendors-node_modules_github_selector-observer_dist_index_esm_js","vendors-node_modules_optimizely_optimizely-sdk_dist_optimizely_browser_es_min_js-node_modules-089adc","ui_packages_soft-nav_soft-nav_ts"],()=>t(68379));var n=e.O()}]);
//# sourceMappingURL=optimizely-f92e23eb485f.js.map