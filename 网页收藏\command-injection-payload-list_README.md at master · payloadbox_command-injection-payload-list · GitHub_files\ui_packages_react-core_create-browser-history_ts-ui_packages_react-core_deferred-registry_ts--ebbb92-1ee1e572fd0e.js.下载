"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["ui_packages_react-core_create-browser-history_ts-ui_packages_react-core_deferred-registry_ts--ebbb92"],{34232:(e,t,r)=>{r.d(t,{n:()=>i});var a,s=r(85893),o=r(67294),n=r(87487);function i({children:e,appName:t,category:r,metadata:a}){let i=(0,o.useMemo)(()=>({appName:t,category:r,metadata:a}),[t,r,a]);return(0,s.jsx)(n.f.Provider,{value:i,children:e})}try{(a=i).displayName||(a.displayName="AnalyticsProvider")}catch{}},87487:(e,t,r)=>{r.d(t,{f:()=>s});var a=r(67294);let s=(0,a.createContext)(null)},65722:(e,t,r)=>{r.d(t,{Z:()=>a});let a=()=>void 0},45055:(e,t,r)=>{r.d(t,{I:()=>s});var a=r(67294);let s=(0,a.createContext)(null)},59112:(e,t,r)=>{r.d(t,{l:()=>s});var a=r(12599);function s(e={}){let t;let r=(0,a.lX)({...e,v5Compat:!0}),s=[],o=!1;function n(e){if(s.length>0)for(let t of s)t({retry(){e()}});else e()}return r.listen(e=>{if(o){o=!1;return}if(e.action===a.aU.Pop&&s.length&&null!==e.delta&&s.length>0){let t=e.delta;for(let e of(o=!0,r.go(-1*t),s))e({retry(){r.go(t)}})}else t?.(e)}),{get action(){return r.action},get location(){return r.location},createHref:e=>r.createHref(e),createURL:e=>r.createURL(e),encodeLocation:e=>r.encodeLocation(e),push(e,t){n(()=>r.push(e,t))},replace(e,t){n(()=>r.replace(e,t))},go(e){n(()=>r.go(e))},listen(e){if(t)throw Error("A history only accepts one active listener");t=e;let r=()=>{t=void 0};return r},block(e){s.push(e);let t=()=>{s=s.filter(t=>t!==e)};return t}}}},96843:(e,t,r)=>{var a;r.d(t,{e:()=>DeferredRegistry});let DeferredRegistry=class DeferredRegistry{register(e,t){let r=this.registrationEntries[e];r?r.resolve?.(t):this.registrationEntries[e]={promise:Promise.resolve(t)}}getRegistration(e){return(a=this.registrationEntries)[e]||(a[e]=new s),this.registrationEntries[e].promise}constructor(){this.registrationEntries={}}};let s=class Deferred{constructor(){this.promise=new Promise(e=>{this.resolve=e})}}},51145:(e,t,r)=>{r.d(t,{Z:()=>c});var a=r(44544);let{getItem:s,setItem:o,removeItem:n}=(0,a.Z)("localStorage"),i="REACT_PROFILING_ENABLED",l={enable:()=>o(i,"true"),disable:()=>n(i),isEnabled:()=>!!s(i)},c=l},87634:(e,t,r)=>{r.d(t,{sS:()=>R,F1:()=>b,V6:()=>x});var a,s,o,n,i,l=r(85893),c=r(85529),d=r(70697),u=r(41905),h=r(67294);let m={info:"",success:"Toast--success",error:"Toast--error"},p={info:(0,l.jsx)(c.InfoIcon,{}),success:(0,l.jsx)(c.CheckIcon,{}),error:(0,l.jsx)(c.StopIcon,{})},f=({message:e,timeToLive:t,icon:r,type:a="info",role:s="log"})=>{let[o,n]=h.useState(!0),{safeSetTimeout:i}=(0,d.Z)();return(0,h.useEffect)(()=>{t&&i(()=>n(!1),t-300)},[i,t]),(0,l.jsx)(u.h,{children:(0,l.jsx)("div",{className:"p-1 position-fixed bottom-0 left-0 mb-3 ml-3",children:(0,l.jsxs)("div",{className:`Toast ${m[a]} ${o?"Toast--animateIn":"Toast--animateOut"}`,id:"ui-app-toast","data-testid":`ui-app-toast-${a}`,role:s,children:[(0,l.jsx)("span",{className:"Toast-icon",children:r||p[a]}),(0,l.jsx)("span",{className:"Toast-content",children:e})]})})})};try{(a=f).displayName||(a.displayName="Toast")}catch{}var y=r(65722);let g=(0,h.createContext)({addToast:y.Z,addPersistedToast:y.Z,clearPersistedToast:y.Z}),v=(0,h.createContext)({toasts:[],persistedToast:null});function R({children:e}){let[t,r]=(0,h.useState)([]),[a,s]=(0,h.useState)(null),{safeSetTimeout:o}=(0,d.Z)(),n=(0,h.useCallback)(function(e){r([...t,e]),o(()=>r(t.slice(1)),5e3)},[t,o]),i=(0,h.useCallback)(function(e){s(e)},[s]),c=(0,h.useCallback)(function(){s(null)},[s]),u=(0,h.useMemo)(()=>({addToast:n,addPersistedToast:i,clearPersistedToast:c}),[i,n,c]),m=(0,h.useMemo)(()=>({toasts:t,persistedToast:a}),[t,a]);return(0,l.jsx)(g.Provider,{value:u,children:(0,l.jsx)(v.Provider,{value:m,children:e})})}function x(){return(0,h.useContext)(g)}try{(s=g).displayName||(s.displayName="ToastContext")}catch{}try{(o=v).displayName||(o.displayName="InternalToastsContext")}catch{}try{(n=R).displayName||(n.displayName="ToastContextProvider")}catch{}function b(){let{toasts:e,persistedToast:t}=(0,h.useContext)(v);return(0,l.jsxs)(l.Fragment,{children:[e.map((e,t)=>(0,l.jsx)(f,{message:e.message,icon:e.icon,timeToLive:5e3,type:e.type,role:e.role},t)),t&&(0,l.jsx)(f,{message:t.message,icon:t.icon,type:t.type,role:t.role})]})}try{(i=b).displayName||(i.displayName="Toasts")}catch{}},78249:(e,t,r)=>{r.d(t,{g:()=>o});var a=r(67294),s=r(86283);function o(e,t){s.Qg&&(0,a.useLayoutEffect)(e,t)}},58989:(e,t,r)=>{r.d(t,{i:()=>i});var a,s=r(85893),o=r(67294),n=r(45055);function i({routes:e,history:t,children:r}){let a=(0,o.useMemo)(()=>({routes:e,history:t}),[e,t]);return(0,s.jsx)(n.I.Provider,{value:a,children:r})}try{(a=i).displayName||(a.displayName="AppContextProvider")}catch{}},1343:(e,t,r)=>{let a;r.d(t,{R:()=>v});var s,o=r(85893),n=r(98224),i=r(8386),l=r(67294);let c=globalThis.document;function d(e){switch(e){case"light":return"day";case"dark":return"night";default:return"auto"}}function u(e){let t=e.colorMode;return{colorMode:d(t),dayScheme:e.lightTheme,nightScheme:e.darkTheme}}function h(){return u(a||{})}function m(){let{documentElement:e}=c,[t,r]=(0,l.useState)(()=>u(e.dataset));return(0,l.useEffect)(()=>{let t=new MutationObserver(()=>r(u(e.dataset)));return t.observe(e,{attributes:!0,attributeFilter:["data-color-mode","data-light-theme","data-dark-theme"]}),()=>t.disconnect()},[e]),t}let p=c?m:h;var f=r(87634),y=r(34232);let g={};function v({appName:e,children:t,wasServerRendered:r}){let{colorMode:a,dayScheme:s,nightScheme:l}=p();return(0,o.jsx)(n.DJ,{wasServerRendered:r,children:(0,o.jsx)(y.n,{appName:e,category:"",metadata:g,children:(0,o.jsx)(i.ZP,{colorMode:a,dayScheme:s,nightScheme:l,preventSSRMismatch:!0,children:(0,o.jsx)(f.sS,{children:t})})})})}try{(s=v).displayName||(s.displayName="BaseProviders")}catch{}},77617:(e,t,r)=>{r.d(t,{P:()=>c});var a,s,o=r(85893),n=r(87634),i=r(67294);function l(){let{addToast:e}=(0,n.V6)();return(0,i.useEffect)(()=>{e({type:"error",message:"SSR failed, see console for error details"})},[]),null}try{(a=l).displayName||(a.displayName="SSRErrorToast")}catch{}function c({ssrError:e}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(n.F1,{}),e&&(0,o.jsx)(l,{})]})}try{(s=c).displayName||(s.displayName="CommonElements")}catch{}},88003:(e,t,r)=>{r.d(t,{S:()=>ReactBaseElement});var a=r(85893),s=r(76006),o=r(20745),n=r(67294),i=r(51145),l=function(e,t,r,a){var s,o=arguments.length,n=o<3?t:null===a?a=Object.getOwnPropertyDescriptor(t,r):a;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)n=Reflect.decorate(e,t,r,a);else for(var i=e.length-1;i>=0;i--)(s=e[i])&&(n=(o<3?s(n):o>3?s(t,r,n):s(t,r))||n);return o>3&&n&&Object.defineProperty(t,r,n),n};let ReactBaseElement=class ReactBaseElement extends HTMLElement{get name(){return this.getAttribute(this.nameAttribute)}get embeddedDataText(){let e=this.embeddedData?.textContent;if(!e)throw Error(`No embedded data provided for react element ${this.name}`);return e}get hasSSRContent(){return"true"===this.getAttribute("data-ssr")}connectedCallback(){this.renderReact()}disconnectedCallback(){this.root?.unmount(),this.root=void 0}async renderReact(){let e={createRoot:o.s,hydrateRoot:o.a};i.Z.isEnabled()&&(e=await this.getReactDomWithProfiling());let t=JSON.parse(this.embeddedDataText),r=this.ssrError?.textContent,s=await this.getReactNode(t),l=(0,a.jsx)(n.StrictMode,{children:s});if(r&&this.logSSRError(r),this.hasSSRContent){let t=this.querySelector('style[data-styled="true"]');t&&document.head.appendChild(t),this.root=e.hydrateRoot(this.reactRoot,l,{onRecoverableError:()=>{}}),t&&requestIdleCallback(()=>{t.parentElement?.removeChild(t)})}else this.root=e.createRoot(this.reactRoot),this.root.render(l);this.classList.add("loaded")}getReactDomWithProfiling(){return r.e("react-profiling").then(r.t.bind(r,62518,19))}logSSRError(e){let t=JSON.parse(e),r=c(t);console.error("Error During Alloy SSR:",`${t.type}: ${t.value}
`,t,r)}};function c(e){if(!e.stacktrace)return"";let t="\n ",r=e.stacktrace.map(e=>{let{function:r,filename:a,lineno:s,colno:o}=e,n=`${t} at ${r} (${a}:${s}:${o})`;return t=" ",n});return r.join("\n")}l([s.fA],ReactBaseElement.prototype,"embeddedData",void 0),l([s.fA],ReactBaseElement.prototype,"ssrError",void 0),l([s.fA],ReactBaseElement.prototype,"reactRoot",void 0)},98224:(e,t,r)=>{r.d(t,{DJ:()=>u,i$:()=>o,kb:()=>d});var a,s,o,n=r(85893),i=r(67294),l=r(86283),c=r(78249);!function(e){e.ServerRender="ServerRender",e.ClientHydrate="ClientHydrate",e.ClientRender="ClientRender"}(o||(o={}));let d=(0,i.createContext)("ClientRender");function u({wasServerRendered:e,children:t}){let[r,a]=(0,i.useState)(()=>l.W6?"ServerRender":e?"ClientHydrate":"ClientRender");return(0,c.g)(()=>{"ClientRender"!==r&&a("ClientRender")},[r]),(0,n.jsx)(d.Provider,{value:r,children:t})}try{(a=d).displayName||(a.displayName="RenderPhaseContext")}catch{}try{(s=u).displayName||(s.displayName="RenderPhaseProvider")}catch{}}}]);
//# sourceMappingURL=ui_packages_react-core_create-browser-history_ts-ui_packages_react-core_deferred-registry_ts--ebbb92-02e0780a71ac.js.map