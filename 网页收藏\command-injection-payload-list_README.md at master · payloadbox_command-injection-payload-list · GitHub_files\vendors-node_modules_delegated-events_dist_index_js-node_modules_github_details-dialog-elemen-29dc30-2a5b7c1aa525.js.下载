"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_delegated-events_dist_index_js-node_modules_github_details-dialog-elemen-29dc30"],{59753:(e,t,n)=>{function r(){if(!(this instanceof r))return new r;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{f:()=>M,S:()=>C,on:()=>D});var s,i=window.document.documentElement,l=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.oMatchesSelector||i.msMatchesSelector;r.prototype.matchesSelector=function(e,t){return l.call(e,t)},r.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},r.prototype.indexes=[];var o=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(o))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var a=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),r.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},s="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var u=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var n,r,s,i,l,o,a=(e=e.slice(0).concat(e.default)).length,c=t,d=[];do if(u.exec(""),(s=u.exec(c))&&(c=s[3],s[2]||!c)){for(n=0;n<a;n++)if(l=(o=e[n]).selector(s[1])){for(r=d.length,i=!1;r--;)if(d[r].index===o&&d[r].key===l){i=!0;break}i||d.push({index:o,key:l});break}}while(s)return d}function f(e,t){var n,r,s;for(n=0,r=e.length;n<r;n++)if(s=e[n],t.isPrototypeOf(s))return s}function g(e,t){return e.id-t.id}r.prototype.logDefaultIndexUsed=function(){},r.prototype.add=function(e,t){var n,r,i,l,o,a,c,u,g=this.activeIndexes,h=this.selectors,m=this.selectorObjects;if("string"==typeof e){for(r=0,m[(n={id:this.uid++,selector:e,data:t}).id]=n,c=d(this.indexes,e);r<c.length;r++)l=(u=c[r]).key,(o=f(g,i=u.index))||((o=Object.create(i)).map=new s,g.push(o)),i===this.indexes.default&&this.logDefaultIndexUsed(n),(a=o.map.get(l))||(a=[],o.map.set(l,a)),a.push(n);this.size++,h.push(e)}},r.prototype.remove=function(e,t){if("string"==typeof e){var n,r,s,i,l,o,a,c,u=this.activeIndexes,f=this.selectors=[],g=this.selectorObjects,h={},m=1==arguments.length;for(s=0,n=d(this.indexes,e);s<n.length;s++)for(r=n[s],i=u.length;i--;)if(o=u[i],r.index.isPrototypeOf(o)){if(a=o.map.get(r.key))for(l=a.length;l--;)(c=a[l]).selector===e&&(m||c.data===t)&&(a.splice(l,1),h[c.id]=!0);break}for(s in h)delete g[s],this.size--;for(s in g)f.push(g[s].selector)}},r.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,r,s,i,l,o,a,c={},u=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,r=d.length;t<r;t++)for(n=0,i=d[t],s=(l=this.matches(i)).length;n<s;n++)c[(a=l[n]).id]?o=c[a.id]:(o={id:a.id,selector:a.selector,data:a.data,elements:[]},c[a.id]=o,u.push(o)),o.elements.push(i);return u.sort(g)},r.prototype.matches=function(e){if(!e)return[];var t,n,r,s,i,l,o,a,c,u,d,f=this.activeIndexes,h={},m=[];for(t=0,s=f.length;t<s;t++)if(a=(o=f[t]).element(e)){for(n=0,i=a.length;n<i;n++)if(c=o.map.get(a[n]))for(r=0,l=c.length;r<l;r++)!h[d=(u=c[r]).id]&&this.matchesSelector(e,u.selector)&&(h[d]=!0,m.push(u))}return m.sort(g)};var h={},m={},p=new WeakMap,v=new WeakMap,b=new WeakMap,E=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function k(e,t,n){var r=e[t];return e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)},e}function y(e,t,n){var r=[],s=t;do{if(1!==s.nodeType)break;var i=e.matches(s);if(i.length){var l={node:s,observers:i};n?r.unshift(l):r.push(l)}}while(s=s.parentElement)return r}function L(){p.set(this,!0)}function w(){p.set(this,!0),v.set(this,!0)}function A(){return b.get(this)||null}function T(e,t){E&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||E.get})}function x(e){try{return e.eventPhase,!0}catch(e){return!1}}function S(e){if(x(e)){var t=(1===e.eventPhase?m:h)[e.type];if(t){var n=y(t,e.target,1===e.eventPhase);if(n.length){k(e,"stopPropagation",L),k(e,"stopImmediatePropagation",w),T(e,A);for(var r=0,s=n.length;r<s&&!p.get(e);r++){var i=n[r];b.set(e,i.node);for(var l=0,o=i.observers.length;l<o&&!v.get(e);l++)i.observers[l].data.call(i.node,e)}b.delete(e),T(e)}}}}function D(e,t,n){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!s.capture,l=i?m:h,o=l[e];o||(o=new r,l[e]=o,document.addEventListener(e,S,i)),o.add(t,n)}function C(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=!!r.capture,i=s?m:h,l=i[e];l&&(l.remove(t,n),l.size||(delete i[e],document.removeEventListener(e,S,s)))}function M(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},14840:(e,t,n)=>{n.d(t,{Z:()=>E});let r="data-close-dialog",s=`[${r}]`;function i(e){let t=Array.from(e.querySelectorAll("[autofocus]")).filter(o)[0];t||(t=e,e.setAttribute("tabindex","-1")),t.focus()}function l(e){let t=e.currentTarget;t instanceof Element&&("Escape"===e.key||"Esc"===e.key?(h(t,!1),e.stopPropagation()):"Tab"===e.key&&c(e))}function o(e){return e.tabIndex>=0&&!e.disabled&&a(e)}function a(e){return!e.hidden&&(!e.type||"hidden"!==e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}function c(e){if(!(e.currentTarget instanceof Element))return;let t=e.currentTarget.querySelector("details-dialog");if(!t)return;e.preventDefault();let n=Array.from(t.querySelectorAll("*")).filter(o);if(0===n.length)return;let r=e.shiftKey?-1:1,s=t.getRootNode(),i=t.contains(s.activeElement)?s.activeElement:null,l=-1===r?-1:0;if(i instanceof HTMLElement){let e=n.indexOf(i);-1!==e&&(l=e+r)}l<0?l=n.length-1:l%=n.length,n[l].focus()}function u(e){let t=e.querySelector("details-dialog");return!(t instanceof DetailsDialogElement)||t.dispatchEvent(new CustomEvent("details-dialog-close",{bubbles:!0,cancelable:!0}))}function d(e){if(!(e.currentTarget instanceof Element))return;let t=e.currentTarget.closest("details");t&&t.hasAttribute("open")&&!u(t)&&(e.preventDefault(),e.stopPropagation())}function f(e){let t=e.currentTarget;if(!(t instanceof Element))return;let n=t.querySelector("details-dialog");if(n instanceof DetailsDialogElement){if(t.hasAttribute("open")){let e="getRootNode"in n?n.getRootNode():document;e.activeElement instanceof HTMLElement&&b.set(n,{details:t,activeElement:e.activeElement}),i(n),t.addEventListener("keydown",l)}else{for(let e of n.querySelectorAll("form"))e.reset();let e=g(t,n);e&&e.focus(),t.removeEventListener("keydown",l)}}}function g(e,t){let n=b.get(t);return n&&n.activeElement instanceof HTMLElement?n.activeElement:e.querySelector("summary")}function h(e,t){t!==e.hasAttribute("open")&&(t?e.setAttribute("open",""):u(e)&&e.removeAttribute("open"))}function m(e){let t=e.currentTarget;if(!(t instanceof Element))return;let n=t.querySelector("details-dialog");if(!(n instanceof DetailsDialogElement))return;let r=n.querySelector("include-fragment:not([src])");if(!r)return;let s=n.src;null!==s&&(r.addEventListener("loadend",()=>{t.hasAttribute("open")&&i(n)}),r.setAttribute("src",s),v(t))}function p(e,t,n){v(e),t&&e.addEventListener("toggle",m,{once:!0}),t&&n&&e.addEventListener("mouseover",m,{once:!0})}function v(e){e.removeEventListener("toggle",m),e.removeEventListener("mouseover",m)}let b=new WeakMap;let DetailsDialogElement=class DetailsDialogElement extends HTMLElement{static get CLOSE_ATTR(){return r}static get CLOSE_SELECTOR(){return s}constructor(){super(),b.set(this,{details:null,activeElement:null}),this.addEventListener("click",function({target:e}){if(!(e instanceof Element))return;let t=e.closest("details");t&&e.closest(s)&&h(t,!1)})}get src(){return this.getAttribute("src")}set src(e){this.setAttribute("src",e||"")}get preload(){return this.hasAttribute("preload")}set preload(e){e?this.setAttribute("preload",""):this.removeAttribute("preload")}connectedCallback(){this.setAttribute("role","dialog"),this.setAttribute("aria-modal","true");let e=b.get(this);if(!e)return;let t=this.parentElement;if(!t)return;let n=t.querySelector("summary");n&&(n.hasAttribute("role")||n.setAttribute("role","button"),n.addEventListener("click",d,{capture:!0})),t.addEventListener("toggle",f),e.details=t,p(t,this.src,this.preload)}disconnectedCallback(){let e=b.get(this);if(!e)return;let{details:t}=e;if(!t)return;t.removeEventListener("toggle",f),v(t);let n=t.querySelector("summary");n&&n.removeEventListener("click",d,{capture:!0}),e.details=null}toggle(e){let t=b.get(this);if(!t)return;let{details:n}=t;n&&h(n,e)}static get observedAttributes(){return["src","preload"]}attributeChangedCallback(){let e=b.get(this);if(!e)return;let{details:t}=e;t&&p(t,this.src,this.preload)}};let E=DetailsDialogElement;window.customElements.get("details-dialog")||(window.DetailsDialogElement=DetailsDialogElement,window.customElements.define("details-dialog",DetailsDialogElement))},40987:(e,t,n)=>{n.d(t,{Z:()=>TaskListsElement});let r=new WeakMap,s=null;function i(){return!!s}function l(e,t,n){r.set(e,{sortStarted:t,sortFinished:n}),e.addEventListener("dragstart",c),e.addEventListener("dragenter",u),e.addEventListener("dragend",f),e.addEventListener("drop",d),e.addEventListener("dragover",g)}function o(e,t){if(e.parentNode===t.parentNode){let n=e;for(;n;){if(n===t)return!0;n=n.previousElementSibling}}return!1}function a(e,t){return e.closest("task-lists")===t.closest("task-lists")}function c(e){if(e.currentTarget!==e.target)return;let t=e.currentTarget;if(!(t instanceof Element))return;let n=t.closest(".contains-task-list");if(!n||(t.classList.add("is-ghost"),e.dataTransfer&&e.dataTransfer.setData("text/plain",(t.textContent||"").trim()),!t.parentElement))return;let i=Array.from(t.parentElement.children),l=i.indexOf(t),o=r.get(t);o&&o.sortStarted(n),s={didDrop:!1,dragging:t,dropzone:t,sourceList:n,sourceSibling:i[l+1]||null,sourceIndex:l}}function u(e){if(!s)return;let t=e.currentTarget;if(t instanceof Element){if(!a(s.dragging,t)){e.stopPropagation();return}e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move"),s.dropzone!==t&&(s.dragging.classList.add("is-dragging"),s.dropzone=t,o(s.dragging,t)?t.before(s.dragging):t.after(s.dragging))}}function d(e){if(!s)return;e.preventDefault(),e.stopPropagation();let t=e.currentTarget;if(!(t instanceof Element)||(s.didDrop=!0,!s.dragging.parentElement))return;let n=Array.from(s.dragging.parentElement.children).indexOf(s.dragging),i=t.closest(".contains-task-list");if(!i||s.sourceIndex===n&&s.sourceList===i)return;s.sourceList===i&&s.sourceIndex<n&&n++;let l={list:s.sourceList,index:s.sourceIndex},o={list:i,index:n},a=r.get(s.dragging);a&&a.sortFinished({src:l,dst:o})}function f(){s&&(s.dragging.classList.remove("is-dragging"),s.dragging.classList.remove("is-ghost"),s.didDrop||s.sourceList.insertBefore(s.dragging,s.sourceSibling),s=null)}function g(e){if(!s)return;let t=e.currentTarget;if(t instanceof Element){if(!a(s.dragging,t)){e.stopPropagation();return}e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move")}}let h=new WeakMap;let TaskListsElement=class TaskListsElement extends HTMLElement{connectedCallback(){this.addEventListener("change",e=>{let t=e.target;t instanceof HTMLInputElement&&t.classList.contains("task-list-item-checkbox")&&this.dispatchEvent(new CustomEvent("task-lists-check",{bubbles:!0,detail:{position:w(t),checked:t.checked}}))});let e=new MutationObserver(S.bind(null,this));h.set(this,e),e.observe(this,{childList:!0,subtree:!0}),S(this)}disconnectedCallback(){let e=h.get(this);e&&e.disconnect()}get disabled(){return this.hasAttribute("disabled")}set disabled(e){e?this.setAttribute("disabled",""):this.removeAttribute("disabled")}get sortable(){return this.hasAttribute("sortable")}set sortable(e){e?this.setAttribute("sortable",""):this.removeAttribute("sortable")}static get observedAttributes(){return["disabled"]}attributeChangedCallback(e,t,n){t!==n&&"disabled"===e&&D(this)}};let m=document.createElement("template"),p=document.createElement("span");p.classList.add("handle");let v=document.createElementNS("http://www.w3.org/2000/svg","svg");v.classList.add("drag-handle"),v.setAttribute("aria-hidden","true"),v.setAttribute("width","16"),v.setAttribute("height","16");let b=document.createElementNS("http://www.w3.org/2000/svg","path");b.setAttribute("d","M10 13a1 1 0 100-2 1 1 0 000 2zm-4 0a1 1 0 100-2 1 1 0 000 2zm1-5a1 1 0 11-2 0 1 1 0 012 0zm3 1a1 1 0 100-2 1 1 0 000 2zm1-5a1 1 0 11-2 0 1 1 0 012 0zM6 5a1 1 0 100-2 1 1 0 000 2z"),m.content.appendChild(p),p.appendChild(v),v.appendChild(b);let E=new WeakMap;function k(e){if(E.get(e))return;E.set(e,!0);let t=e.closest("task-lists");if(!(t instanceof TaskListsElement)||t.querySelectorAll(".task-list-item").length<=1)return;let n=m.content.cloneNode(!0),r=n.querySelector(".handle");if(e.prepend(n),!r)throw Error("handle not found");r.addEventListener("mouseenter",z),r.addEventListener("mouseleave",P),l(e,O,I),e.addEventListener("mouseenter",y),e.addEventListener("mouseleave",L)}function y(e){let t=e.currentTarget;if(!(t instanceof Element))return;let n=t.closest("task-lists");n instanceof TaskListsElement&&n.sortable&&!n.disabled&&t.classList.add("hovered")}function L(e){let t=e.currentTarget;t instanceof Element&&t.classList.remove("hovered")}function w(e){let t=A(e);if(!t)throw Error(".contains-task-list not found");let n=e.closest(".task-list-item"),r=Array.from(t.children).filter(e=>"LI"===e.tagName),s=n?r.indexOf(n):-1;return[M(t),s]}function A(e){let t=e.parentElement;return t?t.closest(".contains-task-list"):null}function T(e){return A(e)===x(e)}function x(e){let t=A(e);return t?x(t)||t:null}function S(e){let t=e.querySelectorAll(".contains-task-list > .task-list-item");for(let e of t)T(e)&&k(e);D(e)}function D(e){for(let t of e.querySelectorAll(".task-list-item"))t.classList.toggle("enabled",!e.disabled);for(let t of e.querySelectorAll(".task-list-item-checkbox"))t instanceof HTMLInputElement&&(t.disabled=e.disabled)}function C(e){return Array.from(e.querySelectorAll("ol, ul")).filter(e=>!e.closest("tracking-block"))}function M(e){let t=e.closest("task-lists");if(!t)throw Error("parent not found");return C(t).indexOf(e)}let q=new WeakMap;function O(e){let t=e.closest("task-lists");if(!t)throw Error("parent not found");q.set(t,C(t))}function I({src:e,dst:t}){let n=e.list.closest("task-lists");if(!n)return;let r=q.get(n);r&&(q.delete(n),n.dispatchEvent(new CustomEvent("task-lists-move",{bubbles:!0,detail:{src:[r.indexOf(e.list),e.index],dst:[r.indexOf(t.list),t.index]}})))}function z(e){let t=e.currentTarget;if(!(t instanceof Element))return;let n=t.closest(".task-list-item");if(!n)return;let r=n.closest("task-lists");r instanceof TaskListsElement&&r.sortable&&!r.disabled&&n.setAttribute("draggable","true")}function P(e){if(i())return;let t=e.currentTarget;if(!(t instanceof Element))return;let n=t.closest(".task-list-item");n&&n.setAttribute("draggable","false")}window.customElements.get("task-lists")||(window.TaskListsElement=TaskListsElement,window.customElements.define("task-lists",TaskListsElement))}}]);
//# sourceMappingURL=vendors-node_modules_delegated-events_dist_index_js-node_modules_github_details-dialog-elemen-29dc30-caeeeb3989c6.js.map