(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_dompurify_dist_purify_js"],{27856:function(e){e.exports=function(){"use strict";function e(t){return(e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(t)}function t(e,n){return(t=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,n)}function n(e,r,o){return(n=!function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],function(){})),!0}catch(e){return!1}}()?function(e,n,r){var o=[null];o.push.apply(o,n);var a=new(Function.bind.apply(e,o));return r&&t(a,r.prototype),a}:Reflect.construct).apply(null,arguments)}function r(e){return function(e){if(Array.isArray(e))return a(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||o(e)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function o(e,t){if(e){if("string"==typeof e)return a(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);if("Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return a(e,t)}}function a(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}var i,l=Object.entries,c=Object.setPrototypeOf,u=Object.isFrozen,s=Object.getPrototypeOf,m=Object.getOwnPropertyDescriptor,f=Object.freeze,p=Object.seal,d=Object.create,h="undefined"!=typeof Reflect&&Reflect,y=h.apply,g=h.construct;y||(y=function(e,t,n){return e.apply(t,n)}),f||(f=function(e){return e}),p||(p=function(e){return e}),g||(g=function(e,t){return n(e,r(t))});var b=O(Array.prototype.forEach),v=O(Array.prototype.pop),T=O(Array.prototype.push),N=O(String.prototype.toLowerCase),A=O(String.prototype.toString),E=O(String.prototype.match),w=O(String.prototype.replace),S=O(String.prototype.indexOf),_=O(String.prototype.trim),k=O(RegExp.prototype.test),x=(i=TypeError,function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return g(i,t)});function O(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return y(e,t,r)}}function C(e,t,n){n=n||N,c&&c(e,null);for(var r=t.length;r--;){var o=t[r];if("string"==typeof o){var a=n(o);a!==o&&(u(t)||(t[r]=a),o=a)}e[o]=!0}return e}function D(e){var t,n=d(null),r=function(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=o(e))){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,l=!0,c=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return l=e.done,e},e:function(e){c=!0,i=e},f:function(){try{l||null==n.return||n.return()}finally{if(c)throw i}}}}(l(e));try{for(r.s();!(t=r.n()).done;){var a,i=(a=t.value,function(e){if(Array.isArray(e))return e}(a)||function(e,t){var n,r,o=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=o){var a=[],i=!0,l=!1;try{for(o=o.call(e);!(i=(n=o.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,r=e}finally{try{i||null==o.return||o.return()}finally{if(l)throw r}}return a}}(a,2)||o(a,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),c=i[0],u=i[1];n[c]=u}}catch(e){r.e(e)}finally{r.f()}return n}function R(e,t){for(;null!==e;){var n=m(e,t);if(n){if(n.get)return O(n.get);if("function"==typeof n.value)return O(n.value)}e=s(e)}return function(e){return console.warn("fallback value for",e),null}}var L=f(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),M=f(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),I=f(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),U=f(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),F=f(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),z=f(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),H=f(["#text"]),j=f(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),B=f(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),P=f(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),G=f(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),W=p(/\{\{[\w\W]*|[\w\W]*\}\}/gm),q=p(/<%[\w\W]*|[\w\W]*%>/gm),Y=p(/\${[\w\W]*}/gm),$=p(/^data-[\-\w.\u00B7-\uFFFF]/),K=p(/^aria-[\-\w]+$/),V=p(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),X=p(/^(?:\w+script|data):/i),Z=p(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),J=p(/^html$/i),Q=function(t,n){if("object"!==e(t)||"function"!=typeof t.createPolicy)return null;var r=null,o="data-tt-policy-suffix";n.currentScript&&n.currentScript.hasAttribute(o)&&(r=n.currentScript.getAttribute(o));var a="dompurify"+(r?"#"+r:"");try{return t.createPolicy(a,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+a+" could not be created."),null}};return function t(){var n,o,a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"undefined"==typeof window?null:window,i=function(e){return t(e)};if(i.version="3.0.1",i.removed=[],!a||!a.document||9!==a.document.nodeType)return i.isSupported=!1,i;var c=a.document,u=a.document,s=a.DocumentFragment,m=a.HTMLTemplateElement,p=a.Node,d=a.Element,h=a.NodeFilter,y=a.NamedNodeMap,g=void 0===y?a.NamedNodeMap||a.MozNamedAttrMap:y,O=a.HTMLFormElement,ee=a.DOMParser,et=a.trustedTypes,en=d.prototype,er=R(en,"cloneNode"),eo=R(en,"nextSibling"),ea=R(en,"childNodes"),ei=R(en,"parentNode");if("function"==typeof m){var el=u.createElement("template");el.content&&el.content.ownerDocument&&(u=el.content.ownerDocument)}var ec=Q(et,c),eu=ec?ec.createHTML(""):"",es=u,em=es.implementation,ef=es.createNodeIterator,ep=es.createDocumentFragment,ed=es.getElementsByTagName,eh=c.importNode,ey={};i.isSupported="function"==typeof l&&"function"==typeof ei&&em&&void 0!==em.createHTMLDocument;var eg=V,eb=null,ev=C({},[].concat(r(L),r(M),r(I),r(F),r(H))),eT=null,eN=C({},[].concat(r(j),r(B),r(P),r(G))),eA=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),eE=null,ew=null,eS=!0,e_=!0,ek=!1,ex=!0,eO=!1,eC=!1,eD=!1,eR=!1,eL=!1,eM=!1,eI=!1,eU=!0,eF=!1,ez=!0,eH=!1,ej={},eB=null,eP=C({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),eG=null,eW=C({},["audio","video","img","source","image","track"]),eq=null,eY=C({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),e$="http://www.w3.org/1998/Math/MathML",eK="http://www.w3.org/2000/svg",eV="http://www.w3.org/1999/xhtml",eX=eV,eZ=!1,eJ=null,eQ=C({},[e$,eK,eV],A),e0=["application/xhtml+xml","text/html"],e1=null,e2=u.createElement("form"),e3=function(e){return e instanceof RegExp||e instanceof Function},e9=function(t){e1&&e1===t||(t&&"object"===e(t)||(t={}),t=D(t),o="application/xhtml+xml"===(n=n=-1===e0.indexOf(t.PARSER_MEDIA_TYPE)?"text/html":t.PARSER_MEDIA_TYPE)?A:N,eb="ALLOWED_TAGS"in t?C({},t.ALLOWED_TAGS,o):ev,eT="ALLOWED_ATTR"in t?C({},t.ALLOWED_ATTR,o):eN,eJ="ALLOWED_NAMESPACES"in t?C({},t.ALLOWED_NAMESPACES,A):eQ,eq="ADD_URI_SAFE_ATTR"in t?C(D(eY),t.ADD_URI_SAFE_ATTR,o):eY,eG="ADD_DATA_URI_TAGS"in t?C(D(eW),t.ADD_DATA_URI_TAGS,o):eW,eB="FORBID_CONTENTS"in t?C({},t.FORBID_CONTENTS,o):eP,eE="FORBID_TAGS"in t?C({},t.FORBID_TAGS,o):{},ew="FORBID_ATTR"in t?C({},t.FORBID_ATTR,o):{},ej="USE_PROFILES"in t&&t.USE_PROFILES,eS=!1!==t.ALLOW_ARIA_ATTR,e_=!1!==t.ALLOW_DATA_ATTR,ek=t.ALLOW_UNKNOWN_PROTOCOLS||!1,ex=!1!==t.ALLOW_SELF_CLOSE_IN_ATTR,eO=t.SAFE_FOR_TEMPLATES||!1,eC=t.WHOLE_DOCUMENT||!1,eL=t.RETURN_DOM||!1,eM=t.RETURN_DOM_FRAGMENT||!1,eI=t.RETURN_TRUSTED_TYPE||!1,eR=t.FORCE_BODY||!1,eU=!1!==t.SANITIZE_DOM,eF=t.SANITIZE_NAMED_PROPS||!1,ez=!1!==t.KEEP_CONTENT,eH=t.IN_PLACE||!1,eg=t.ALLOWED_URI_REGEXP||eg,eX=t.NAMESPACE||eV,eA=t.CUSTOM_ELEMENT_HANDLING||{},t.CUSTOM_ELEMENT_HANDLING&&e3(t.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(eA.tagNameCheck=t.CUSTOM_ELEMENT_HANDLING.tagNameCheck),t.CUSTOM_ELEMENT_HANDLING&&e3(t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(eA.attributeNameCheck=t.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),t.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(eA.allowCustomizedBuiltInElements=t.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),eO&&(e_=!1),eM&&(eL=!0),ej&&(eb=C({},r(H)),eT=[],!0===ej.html&&(C(eb,L),C(eT,j)),!0===ej.svg&&(C(eb,M),C(eT,B),C(eT,G)),!0===ej.svgFilters&&(C(eb,I),C(eT,B),C(eT,G)),!0===ej.mathMl&&(C(eb,F),C(eT,P),C(eT,G))),t.ADD_TAGS&&(eb===ev&&(eb=D(eb)),C(eb,t.ADD_TAGS,o)),t.ADD_ATTR&&(eT===eN&&(eT=D(eT)),C(eT,t.ADD_ATTR,o)),t.ADD_URI_SAFE_ATTR&&C(eq,t.ADD_URI_SAFE_ATTR,o),t.FORBID_CONTENTS&&(eB===eP&&(eB=D(eB)),C(eB,t.FORBID_CONTENTS,o)),ez&&(eb["#text"]=!0),eC&&C(eb,["html","head","body"]),eb.table&&(C(eb,["tbody"]),delete eE.tbody),f&&f(t),e1=t)},e8=C({},["mi","mo","mn","ms","mtext"]),e6=C({},["foreignobject","desc","title","annotation-xml"]),e5=C({},["title","style","font","a","script"]),e4=C({},M);C(e4,I),C(e4,U);var e7=C({},F);C(e7,z);var te=function(e){var t=ei(e);t&&t.tagName||(t={namespaceURI:eX,tagName:"template"});var r=N(e.tagName),o=N(t.tagName);return!!eJ[e.namespaceURI]&&(e.namespaceURI===eK?t.namespaceURI===eV?"svg"===r:t.namespaceURI===e$?"svg"===r&&("annotation-xml"===o||e8[o]):Boolean(e4[r]):e.namespaceURI===e$?t.namespaceURI===eV?"math"===r:t.namespaceURI===eK?"math"===r&&e6[o]:Boolean(e7[r]):e.namespaceURI===eV?(t.namespaceURI!==eK||!!e6[o])&&(t.namespaceURI!==e$||!!e8[o])&&!e7[r]&&(e5[r]||!e4[r]):"application/xhtml+xml"===n&&!!eJ[e.namespaceURI])},tt=function(e){T(i.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){e.remove()}},tn=function(e,t){try{T(i.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){T(i.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!eT[e]){if(eL||eM)try{tt(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}}},tr=function(e){if(eR)e="<remove></remove>"+e;else{var t,r,o=E(e,/^[\r\n\t ]+/);r=o&&o[0]}"application/xhtml+xml"===n&&eX===eV&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var a=ec?ec.createHTML(e):e;if(eX===eV)try{t=new ee().parseFromString(a,n)}catch(e){}if(!t||!t.documentElement){t=em.createDocument(eX,"template",null);try{t.documentElement.innerHTML=eZ?eu:a}catch(e){}}var i=t.body||t.documentElement;return(e&&r&&i.insertBefore(u.createTextNode(r),i.childNodes[0]||null),eX===eV)?ed.call(t,eC?"html":"body")[0]:eC?t.documentElement:i},to=function(e){return ef.call(e.ownerDocument||e,e,h.SHOW_ELEMENT|h.SHOW_COMMENT|h.SHOW_TEXT,null,!1)},ta=function(t){return"object"===e(p)?t instanceof p:t&&"object"===e(t)&&"number"==typeof t.nodeType&&"string"==typeof t.nodeName},ti=function(e,t,n){ey[e]&&b(ey[e],function(e){e.call(i,t,n,e1)})},tl=function(e){if(ti("beforeSanitizeElements",e,null),e instanceof O&&("string"!=typeof e.nodeName||"string"!=typeof e.textContent||"function"!=typeof e.removeChild||!(e.attributes instanceof g)||"function"!=typeof e.removeAttribute||"function"!=typeof e.setAttribute||"string"!=typeof e.namespaceURI||"function"!=typeof e.insertBefore||"function"!=typeof e.hasChildNodes))return tt(e),!0;var t,n=o(e.nodeName);if(ti("uponSanitizeElement",e,{tagName:n,allowedTags:eb}),e.hasChildNodes()&&!ta(e.firstElementChild)&&(!ta(e.content)||!ta(e.content.firstElementChild))&&k(/<[/\w]/g,e.innerHTML)&&k(/<[/\w]/g,e.textContent))return tt(e),!0;if(!eb[n]||eE[n]){if(!eE[n]&&tu(n)&&(eA.tagNameCheck instanceof RegExp&&k(eA.tagNameCheck,n)||eA.tagNameCheck instanceof Function&&eA.tagNameCheck(n)))return!1;if(ez&&!eB[n]){var r=ei(e)||e.parentNode,a=ea(e)||e.childNodes;if(a&&r)for(var l=a.length,c=l-1;c>=0;--c)r.insertBefore(er(a[c],!0),eo(e))}return tt(e),!0}return e instanceof d&&!te(e)||("noscript"===n||"noembed"===n)&&k(/<\/no(script|embed)/i,e.innerHTML)?(tt(e),!0):(eO&&3===e.nodeType&&(t=w(t=e.textContent,W," "),t=w(t,q," "),t=w(t,Y," "),e.textContent!==t&&(T(i.removed,{element:e.cloneNode()}),e.textContent=t)),ti("afterSanitizeElements",e,null),!1)},tc=function(e,t,n){if(eU&&("id"===t||"name"===t)&&(n in u||n in e2))return!1;if(e_&&!ew[t]&&k($,t));else if(eS&&k(K,t));else if(!eT[t]||ew[t]){if(!(tu(e)&&(eA.tagNameCheck instanceof RegExp&&k(eA.tagNameCheck,e)||eA.tagNameCheck instanceof Function&&eA.tagNameCheck(e))&&(eA.attributeNameCheck instanceof RegExp&&k(eA.attributeNameCheck,t)||eA.attributeNameCheck instanceof Function&&eA.attributeNameCheck(t))||"is"===t&&eA.allowCustomizedBuiltInElements&&(eA.tagNameCheck instanceof RegExp&&k(eA.tagNameCheck,n)||eA.tagNameCheck instanceof Function&&eA.tagNameCheck(n))))return!1}else if(eq[t]);else if(k(eg,w(n,Z,"")));else if(("src"===t||"xlink:href"===t||"href"===t)&&"script"!==e&&0===S(n,"data:")&&eG[e]);else if(ek&&!k(X,w(n,Z,"")));else if(n)return!1;return!0},tu=function(e){return e.indexOf("-")>0},ts=function(t){ti("beforeSanitizeAttributes",t,null);var n,r,a,l,c=t.attributes;if(c){var u={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:eT};for(l=c.length;l--;){var s=(n=c[l]).name,m=n.namespaceURI;if(r="value"===s?n.value:_(n.value),a=o(s),u.attrName=a,u.attrValue=r,u.keepAttr=!0,u.forceKeepAttr=void 0,ti("uponSanitizeAttribute",t,u),r=u.attrValue,!u.forceKeepAttr&&(tn(s,t),u.keepAttr)){if(!ex&&k(/\/>/i,r)){tn(s,t);continue}eO&&(r=w(r,W," "),r=w(r,q," "),r=w(r,Y," "));var f=o(t.nodeName);if(tc(f,a,r)){if(eF&&("id"===a||"name"===a)&&(tn(s,t),r="user-content-"+r),ec&&"object"===e(et)&&"function"==typeof et.getAttributeType){if(m);else switch(et.getAttributeType(f,a)){case"TrustedHTML":r=ec.createHTML(r);break;case"TrustedScriptURL":r=ec.createScriptURL(r)}}try{m?t.setAttributeNS(m,s,r):t.setAttribute(s,r),v(i.removed)}catch(e){}}}}ti("afterSanitizeAttributes",t,null)}},tm=function e(t){var n,r=to(t);for(ti("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)ti("uponSanitizeShadowNode",n,null),tl(n)||(n.content instanceof s&&e(n.content),ts(n));ti("afterSanitizeShadowDOM",t,null)};return i.sanitize=function(e){var t,n,r,a,l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((eZ=!e)&&(e="<!-->"),"string"!=typeof e&&!ta(e)){if("function"!=typeof e.toString)throw x("toString is not a function");if("string"!=typeof(e=e.toString()))throw x("dirty is not a string, aborting")}if(!i.isSupported)return e;if(eD||e9(l),i.removed=[],"string"==typeof e&&(eH=!1),eH){if(e.nodeName){var u=o(e.nodeName);if(!eb[u]||eE[u])throw x("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof p)1===(n=(t=tr("<!---->")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===n.nodeName?t=n:"HTML"===n.nodeName?t=n:t.appendChild(n);else{if(!eL&&!eO&&!eC&&-1===e.indexOf("<"))return ec&&eI?ec.createHTML(e):e;if(!(t=tr(e)))return eL?null:eI?eu:""}t&&eR&&tt(t.firstChild);for(var m=to(eH?e:t);r=m.nextNode();)tl(r)||(r.content instanceof s&&tm(r.content),ts(r));if(eH)return e;if(eL){if(eM)for(a=ep.call(t.ownerDocument);t.firstChild;)a.appendChild(t.firstChild);else a=t;return(eT.shadowroot||eT.shadowrootmod)&&(a=eh.call(c,a,!0)),a}var f=eC?t.outerHTML:t.innerHTML;return eC&&eb["!doctype"]&&t.ownerDocument&&t.ownerDocument.doctype&&t.ownerDocument.doctype.name&&k(J,t.ownerDocument.doctype.name)&&(f="<!DOCTYPE "+t.ownerDocument.doctype.name+">\n"+f),eO&&(f=w(f,W," "),f=w(f,q," "),f=w(f,Y," ")),ec&&eI?ec.createHTML(f):f},i.setConfig=function(e){e9(e),eD=!0},i.clearConfig=function(){e1=null,eD=!1},i.isValidAttribute=function(e,t,n){return e1||e9({}),tc(o(e),o(t),n)},i.addHook=function(e,t){"function"==typeof t&&(ey[e]=ey[e]||[],T(ey[e],t))},i.removeHook=function(e){if(ey[e])return v(ey[e])},i.removeHooks=function(e){ey[e]&&(ey[e]=[])},i.removeAllHooks=function(){ey={}},i}()}()}}]);
//# sourceMappingURL=vendors-node_modules_dompurify_dist_purify_js-ed050d8cc38d.js.map