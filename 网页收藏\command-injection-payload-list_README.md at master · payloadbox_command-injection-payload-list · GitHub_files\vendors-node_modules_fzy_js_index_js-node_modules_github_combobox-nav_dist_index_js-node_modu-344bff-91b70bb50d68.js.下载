"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_fzy_js_index_js-node_modules_github_combobox-nav_dist_index_js-node_modu-344bff"],{47142:(e,t,n)=>{n.d(t,{CD:()=>b,DU:()=>o,Gs:()=>p,m7:()=>k});var i=-1/0,o=1/0,r=-.005,l=-.005,s=-.01,a=1,d=.9,u=.8,c=.7,m=.6;function f(e){return e.toLowerCase()===e}function h(e){return e.toUpperCase()===e}function w(e){for(var t=e.length,n=Array(t),i="/",o=0;o<t;o++){var r=e[o];"/"===i?n[o]=d:"-"===i||"_"===i||" "===i?n[o]=u:"."===i?n[o]=m:f(i)&&h(r)?n[o]=c:n[o]=0,i=r}return n}function E(e,t,n,o){for(var d=e.length,u=t.length,c=e.toLowerCase(),m=t.toLowerCase(),f=w(t,f),h=0;h<d;h++){n[h]=Array(u),o[h]=Array(u);for(var E=i,p=h===d-1?l:s,k=0;k<u;k++)if(c[h]===m[k]){var b=i;h?k&&(b=Math.max(o[h-1][k-1]+f[k],n[h-1][k-1]+a)):b=k*r+f[k],n[h][k]=b,o[h][k]=E=Math.max(b,E+p)}else n[h][k]=i,o[h][k]=E+=p}}function p(e,t){var n=e.length,r=t.length;if(!n||!r)return i;if(n===r)return o;if(r>1024)return i;var l=Array(n),s=Array(n);return E(e,t,l,s),s[n-1][r-1]}function k(e,t){var n=e.length,o=t.length,r=Array(n);if(!n||!o)return r;if(n===o){for(var l=0;l<n;l++)r[l]=l;return r}if(o>1024)return r;var s=Array(n),d=Array(n);E(e,t,s,d);for(var u=!1,l=n-1,c=o-1;l>=0;l--)for(;c>=0;c--)if(s[l][c]!==i&&(u||s[l][c]===d[l][c])){u=l&&c&&d[l][c]===s[l-1][c-1]+a,r[l]=c--;break}return r}function b(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var n=e.length,i=0,o=0;i<n;i+=1)if(0===(o=t.indexOf(e[i],o)+1))return!1;return!0}},10160:(e,t,n)=>{n.d(t,{Z:()=>Combobox});let Combobox=class Combobox{constructor(e,t,{tabInsertsSuggestions:n,defaultFirstOption:o}={}){this.input=e,this.list=t,this.tabInsertsSuggestions=null==n||n,this.defaultFirstOption=null!=o&&o,this.isComposing=!1,t.id||(t.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=e=>i(e,this),this.compositionEventHandler=e=>a(e,this),this.inputHandler=this.clearSelection.bind(this),e.setAttribute("role","combobox"),e.setAttribute("aria-controls",t.id),e.setAttribute("aria-expanded","false"),e.setAttribute("aria-autocomplete","list"),e.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",o),this.indicateDefaultOption()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",o)}indicateDefaultOption(){var e;this.defaultFirstOption&&(null===(e=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(s)[0])||void 0===e||e.setAttribute("data-combobox-option-default","true"))}navigate(e=1){let t=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(s)[0],n=Array.from(this.list.querySelectorAll('[role="option"]')).filter(s),i=n.indexOf(t);if(i===n.length-1&&1===e||0===i&&-1===e){this.clearSelection(),this.input.focus();return}let o=1===e?0:n.length-1;if(t&&i>=0){let t=i+e;t>=0&&t<n.length&&(o=t)}let r=n[o];if(r)for(let e of n)e.removeAttribute("data-combobox-option-default"),r===e?(this.input.setAttribute("aria-activedescendant",r.id),r.setAttribute("aria-selected","true"),d(this.list,r)):e.removeAttribute("aria-selected")}clearSelection(){for(let e of(this.input.removeAttribute("aria-activedescendant"),this.list.querySelectorAll('[aria-selected="true"]')))e.removeAttribute("aria-selected");this.indicateDefaultOption()}};function i(e,t){if(!e.shiftKey&&!e.metaKey&&!e.altKey&&(t.ctrlBindings||!e.ctrlKey)&&!t.isComposing)switch(e.key){case"Enter":r(t.input,t.list)&&e.preventDefault();break;case"Tab":t.tabInsertsSuggestions&&r(t.input,t.list)&&e.preventDefault();break;case"Escape":t.clearSelection();break;case"ArrowDown":t.navigate(1),e.preventDefault();break;case"ArrowUp":t.navigate(-1),e.preventDefault();break;case"n":t.ctrlBindings&&e.ctrlKey&&(t.navigate(1),e.preventDefault());break;case"p":t.ctrlBindings&&e.ctrlKey&&(t.navigate(-1),e.preventDefault());break;default:if(e.ctrlKey)break;t.clearSelection()}}function o(e){if(!(e.target instanceof Element))return;let t=e.target.closest('[role="option"]');t&&"true"!==t.getAttribute("aria-disabled")&&l(t,{event:e})}function r(e,t){let n=t.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!n&&("true"===n.getAttribute("aria-disabled")||(n.click(),!0))}function l(e,t){e.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:t}))}function s(e){return!e.hidden&&!(e instanceof HTMLInputElement&&"hidden"===e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}function a(e,t){t.isComposing="compositionstart"===e.type;let n=document.getElementById(t.input.getAttribute("aria-controls")||"");n&&t.clearSelection()}function d(e,t){u(e,t)||(e.scrollTop=t.offsetTop)}function u(e,t){let n=e.scrollTop,i=n+e.clientHeight,o=t.offsetTop,r=o+t.clientHeight;return o>=n&&r<=i}},51941:(e,t,n)=>{n.r(t),n.d(t,{default:()=>O});var i,o,r=function(e,t,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!i:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(e):i?i.value:t.get(e)};let l=["[data-md-button]","md-header","md-bold","md-italic","md-quote","md-code","md-link","md-image","md-unordered-list","md-ordered-list","md-task-list","md-mention","md-ref","md-strikethrough"];function s(e){let t=[];for(let n of e.querySelectorAll(l.join(", ")))n.hidden||n.offsetWidth<=0&&n.offsetHeight<=0||n.closest("markdown-toolbar")!==e||t.push(n);return t}function a(e){return function(t){(" "===t.key||"Enter"===t.key)&&e(t)}}let d=new WeakMap,u={"header-1":{prefix:"# "},"header-2":{prefix:"## "},"header-3":{prefix:"### "},"header-4":{prefix:"#### "},"header-5":{prefix:"##### "},"header-6":{prefix:"###### "},bold:{prefix:"**",suffix:"**",trimFirst:!0},italic:{prefix:"_",suffix:"_",trimFirst:!0},quote:{prefix:"> ",multiline:!0,surroundWithNewlines:!0},code:{prefix:"`",suffix:"`",blockPrefix:"```",blockSuffix:"```"},link:{prefix:"[",suffix:"](url)",replaceNext:"url",scanFor:"https?://"},image:{prefix:"![",suffix:"](url)",replaceNext:"url",scanFor:"https?://"},"unordered-list":{prefix:"- ",multiline:!0,unorderedList:!0},"ordered-list":{prefix:"1. ",multiline:!0,orderedList:!0},"task-list":{prefix:"- [ ] ",multiline:!0,surroundWithNewlines:!0},mention:{prefix:"@",prefixSpace:!0},ref:{prefix:"#",prefixSpace:!0},strikethrough:{prefix:"~~",suffix:"~~",trimFirst:!0}};let MarkdownButtonElement=class MarkdownButtonElement extends HTMLElement{constructor(){super();let e=e=>{let t=d.get(this);t&&(e.preventDefault(),D(this,t))};this.addEventListener("keydown",a(e)),this.addEventListener("click",e)}connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","button")}click(){let e=d.get(this);e&&D(this,e)}};let MarkdownHeaderButtonElement=class MarkdownHeaderButtonElement extends MarkdownButtonElement{constructor(){super(...arguments),i.add(this)}connectedCallback(){let e=parseInt(this.getAttribute("level")||"3",10);r(this,i,"m",o).call(this,e)}static get observedAttributes(){return["level"]}attributeChangedCallback(e,t,n){if("level"!==e)return;let l=parseInt(n||"3",10);r(this,i,"m",o).call(this,l)}};i=new WeakSet,o=function(e){if(e<1||e>6)return;let t=`${"#".repeat(e)} `;d.set(this,{prefix:t})},window.customElements.get("md-header")||(window.MarkdownHeaderButtonElement=MarkdownHeaderButtonElement,window.customElements.define("md-header",MarkdownHeaderButtonElement));let MarkdownBoldButtonElement=class MarkdownBoldButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"**",suffix:"**",trimFirst:!0})}};window.customElements.get("md-bold")||(window.MarkdownBoldButtonElement=MarkdownBoldButtonElement,window.customElements.define("md-bold",MarkdownBoldButtonElement));let MarkdownItalicButtonElement=class MarkdownItalicButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"_",suffix:"_",trimFirst:!0})}};window.customElements.get("md-italic")||(window.MarkdownItalicButtonElement=MarkdownItalicButtonElement,window.customElements.define("md-italic",MarkdownItalicButtonElement));let MarkdownQuoteButtonElement=class MarkdownQuoteButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"> ",multiline:!0,surroundWithNewlines:!0})}};window.customElements.get("md-quote")||(window.MarkdownQuoteButtonElement=MarkdownQuoteButtonElement,window.customElements.define("md-quote",MarkdownQuoteButtonElement));let MarkdownCodeButtonElement=class MarkdownCodeButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"`",suffix:"`",blockPrefix:"```",blockSuffix:"```"})}};window.customElements.get("md-code")||(window.MarkdownCodeButtonElement=MarkdownCodeButtonElement,window.customElements.define("md-code",MarkdownCodeButtonElement));let MarkdownLinkButtonElement=class MarkdownLinkButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"[",suffix:"](url)",replaceNext:"url",scanFor:"https?://"})}};window.customElements.get("md-link")||(window.MarkdownLinkButtonElement=MarkdownLinkButtonElement,window.customElements.define("md-link",MarkdownLinkButtonElement));let MarkdownImageButtonElement=class MarkdownImageButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"![",suffix:"](url)",replaceNext:"url",scanFor:"https?://"})}};window.customElements.get("md-image")||(window.MarkdownImageButtonElement=MarkdownImageButtonElement,window.customElements.define("md-image",MarkdownImageButtonElement));let MarkdownUnorderedListButtonElement=class MarkdownUnorderedListButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"- ",multiline:!0,unorderedList:!0})}};window.customElements.get("md-unordered-list")||(window.MarkdownUnorderedListButtonElement=MarkdownUnorderedListButtonElement,window.customElements.define("md-unordered-list",MarkdownUnorderedListButtonElement));let MarkdownOrderedListButtonElement=class MarkdownOrderedListButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"1. ",multiline:!0,orderedList:!0})}};window.customElements.get("md-ordered-list")||(window.MarkdownOrderedListButtonElement=MarkdownOrderedListButtonElement,window.customElements.define("md-ordered-list",MarkdownOrderedListButtonElement));let MarkdownTaskListButtonElement=class MarkdownTaskListButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"- [ ] ",multiline:!0,surroundWithNewlines:!0})}};window.customElements.get("md-task-list")||(window.MarkdownTaskListButtonElement=MarkdownTaskListButtonElement,window.customElements.define("md-task-list",MarkdownTaskListButtonElement));let MarkdownMentionButtonElement=class MarkdownMentionButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"@",prefixSpace:!0})}};window.customElements.get("md-mention")||(window.MarkdownMentionButtonElement=MarkdownMentionButtonElement,window.customElements.define("md-mention",MarkdownMentionButtonElement));let MarkdownRefButtonElement=class MarkdownRefButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"#",prefixSpace:!0})}};window.customElements.get("md-ref")||(window.MarkdownRefButtonElement=MarkdownRefButtonElement,window.customElements.define("md-ref",MarkdownRefButtonElement));let MarkdownStrikethroughButtonElement=class MarkdownStrikethroughButtonElement extends MarkdownButtonElement{connectedCallback(){d.set(this,{prefix:"~~",suffix:"~~",trimFirst:!0})}};function c(e){let{target:t,currentTarget:n}=e;if(!(t instanceof HTMLElement))return;let i=t.closest("[data-md-button]");if(!i||i.closest("markdown-toolbar")!==n)return;let o=t.getAttribute("data-md-button"),r=u[o];r&&(e.preventDefault(),D(t,r))}function m(e){e.addEventListener("keydown",w),e.setAttribute("tabindex","0"),e.addEventListener("focus",h,{once:!0})}function f(e){e.removeEventListener("keydown",w),e.removeAttribute("tabindex"),e.removeEventListener("focus",h)}window.customElements.get("md-strikethrough")||(window.MarkdownStrikethroughButtonElement=MarkdownStrikethroughButtonElement,window.customElements.define("md-strikethrough",MarkdownStrikethroughButtonElement));let MarkdownToolbarElement=class MarkdownToolbarElement extends HTMLElement{connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","toolbar"),this.hasAttribute("data-no-focus")||m(this),this.addEventListener("keydown",a(c)),this.addEventListener("click",c)}attributeChangedCallback(e,t,n){"data-no-focus"===e&&(null===n?m(this):f(this))}disconnectedCallback(){f(this)}get field(){let e;let t=this.getAttribute("for");if(!t)return null;let n="getRootNode"in this?this.getRootNode():document;return(n instanceof Document||n instanceof ShadowRoot)&&(e=n.getElementById(t)),e instanceof HTMLTextAreaElement?e:null}};function h({target:e}){if(!(e instanceof Element))return;e.removeAttribute("tabindex");let t="0";for(let n of s(e))n.setAttribute("tabindex",t),"0"===t&&(n.focus(),t="-1")}function w(e){let t=e.key;if("ArrowRight"!==t&&"ArrowLeft"!==t&&"Home"!==t&&"End"!==t)return;let n=e.currentTarget;if(!(n instanceof HTMLElement))return;let i=s(n),o=i.indexOf(e.target),r=i.length;if(-1===o)return;let l=0;"ArrowLeft"===t&&(l=o-1),"ArrowRight"===t&&(l=o+1),"End"===t&&(l=r-1),l<0&&(l=r-1),l>r-1&&(l=0);for(let e=0;e<r;e+=1)i[e].setAttribute("tabindex",e===l?"0":"-1");e.preventDefault(),i[l].focus()}function E(e){return e.trim().split("\n").length>1}function p(e,t){return Array(t+1).join(e)}function k(e,t){let n=t;for(;e[n]&&null!=e[n-1]&&!e[n-1].match(/\s/);)n--;return n}function b(e,t,n){let i=t,o=n?/\n/:/\s/;for(;e[i]&&!e[i].match(o);)i++;return i}MarkdownToolbarElement.observedAttributes=["data-no-focus"],window.customElements.get("markdown-toolbar")||(window.MarkdownToolbarElement=MarkdownToolbarElement,window.customElements.define("markdown-toolbar",MarkdownToolbarElement));let g=null;function x(e,{text:t,selectionStart:n,selectionEnd:i}){let o=e.selectionStart,r=e.value.slice(0,o),l=e.value.slice(e.selectionEnd);if(null===g||!0===g){e.contentEditable="true";try{g=document.execCommand("insertText",!1,t)}catch(e){g=!1}e.contentEditable="false"}if(g&&!e.value.slice(0,e.selectionStart).endsWith(t)&&(g=!1),!g){try{document.execCommand("ms-beginUndoUnit")}catch(e){}e.value=r+t+l;try{document.execCommand("ms-endUndoUnit")}catch(e){}e.dispatchEvent(new CustomEvent("input",{bubbles:!0,cancelable:!0}))}null!=n&&null!=i?e.setSelectionRange(n,i):e.setSelectionRange(o,e.selectionEnd)}function v(e,t){let n;let i=e.value.slice(e.selectionStart,e.selectionEnd);n=t.orderedList||t.unorderedList?_(e,t):t.multiline&&E(i)?L(e,t):S(e,t),x(e,n)}function M(e){let t=e.value.split("\n"),n=0;for(let i=0;i<t.length;i++){let o=t[i].length+1;e.selectionStart>=n&&e.selectionStart<n+o&&(e.selectionStart=n),e.selectionEnd>=n&&e.selectionEnd<n+o&&(e.selectionEnd=n+o-1),n+=o}}function B(e,t,n,i=!1){if(e.selectionStart===e.selectionEnd)e.selectionStart=k(e.value,e.selectionStart),e.selectionEnd=b(e.value,e.selectionEnd,i);else{let i=e.selectionStart-t.length,o=e.selectionEnd+n.length,r=e.value.slice(i,e.selectionStart)===t,l=e.value.slice(e.selectionEnd,o)===n;r&&l&&(e.selectionStart=i,e.selectionEnd=o)}return e.value.slice(e.selectionStart,e.selectionEnd)}function A(e){let t,n;let i=e.value.slice(0,e.selectionStart),o=e.value.slice(e.selectionEnd),r=i.match(/\n*$/),l=o.match(/^\n*/),s=r?r[0].length:0,a=l?l[0].length:0;return i.match(/\S/)&&s<2&&(t=p("\n",2-s)),o.match(/\S/)&&a<2&&(n=p("\n",2-a)),null==t&&(t=""),null==n&&(n=""),{newlinesToAppend:t,newlinesToPrepend:n}}function S(e,t){let n,i;let{prefix:o,suffix:r,blockPrefix:l,blockSuffix:s,replaceNext:a,prefixSpace:d,scanFor:u,surroundWithNewlines:c}=t,m=e.selectionStart,f=e.selectionEnd,h=e.value.slice(e.selectionStart,e.selectionEnd),w=E(h)&&l.length>0?`${l}
`:o,p=E(h)&&s.length>0?`
${s}`:r;if(d){let t=e.value[e.selectionStart-1];0===e.selectionStart||null==t||t.match(/\s/)||(w=` ${w}`)}h=B(e,w,p,t.multiline);let k=e.selectionStart,b=e.selectionEnd,g=a.length>0&&p.indexOf(a)>-1&&h.length>0;if(c){let t=A(e);n=t.newlinesToAppend,i=t.newlinesToPrepend,w=n+o,p+=i}if(h.startsWith(w)&&h.endsWith(p)){let e=h.slice(w.length,h.length-p.length);if(m===f){let t=m-w.length;k=b=t=Math.min(t=Math.max(t,k),k+e.length)}else b=k+e.length;return{text:e,selectionStart:k,selectionEnd:b}}if(g){if(u.length>0&&h.match(u)){p=p.replace(a,h);let e=w+p;return k=b=k+w.length,{text:e,selectionStart:k,selectionEnd:b}}{let e=w+h+p;return b=(k=k+w.length+h.length+p.indexOf(a))+a.length,{text:e,selectionStart:k,selectionEnd:b}}}{let e=w+h+p;k=m+w.length,b=f+w.length;let n=h.match(/^\s*|\s*$/g);if(t.trimFirst&&n){let t=n[0]||"",i=n[1]||"";e=t+w+h.trim()+p+i,k+=t.length,b-=i.length}return{text:e,selectionStart:k,selectionEnd:b}}}function L(e,t){let{prefix:n,suffix:i,surroundWithNewlines:o}=t,r=e.value.slice(e.selectionStart,e.selectionEnd),l=e.selectionStart,s=e.selectionEnd,a=r.split("\n"),d=a.every(e=>e.startsWith(n)&&e.endsWith(i));if(d)s=l+(r=a.map(e=>e.slice(n.length,e.length-i.length)).join("\n")).length;else if(r=a.map(e=>n+e+i).join("\n"),o){let{newlinesToAppend:t,newlinesToPrepend:n}=A(e);l+=t.length,s=l+r.length,r=t+r+n}return{text:r,selectionStart:l,selectionEnd:s}}function y(e){let t=e.split("\n"),n=/^\d+\.\s+/,i=t.every(e=>n.test(e)),o=t;return i&&(o=t.map(e=>e.replace(n,""))),{text:o.join("\n"),processed:i}}function C(e){let t=e.split("\n"),n=t.every(e=>e.startsWith("- ")),i=t;return n&&(i=t.map(e=>e.slice(2,e.length))),{text:i.join("\n"),processed:n}}function T(e,t){return t?"- ":`${e+1}. `}function H(e,t){let n,i,o;return o=e.orderedList?(n=C((i=y(t)).text)).text:(n=y((i=C(t)).text)).text,[i,n,o]}function _(e,t){let n=e.selectionStart===e.selectionEnd,i=e.selectionStart,o=e.selectionEnd;M(e);let r=e.value.slice(e.selectionStart,e.selectionEnd),[l,s,a]=H(t,r),d=a.split("\n").map((e,n)=>`${T(n,t.unorderedList)}${e}`),u=d.reduce((e,n,i)=>e+T(i,t.unorderedList).length,0),c=d.reduce((e,n,i)=>e+T(i,!t.unorderedList).length,0);if(l.processed)return n?o=i=Math.max(i-T(0,t.unorderedList).length,0):(i=e.selectionStart,o=e.selectionEnd-u),{text:a,selectionStart:i,selectionEnd:o};let{newlinesToAppend:m,newlinesToPrepend:f}=A(e),h=m+d.join("\n")+f;return n?o=i=Math.max(i+T(0,t.unorderedList).length+m.length,0):s.processed?(i=Math.max(e.selectionStart+m.length,0),o=e.selectionEnd+m.length+u-c):(i=Math.max(e.selectionStart+m.length,0),o=e.selectionEnd+m.length+u),{text:h,selectionStart:i,selectionEnd:o}}function D(e,t){let n=e.closest("markdown-toolbar");if(!(n instanceof MarkdownToolbarElement))return;let i=Object.assign(Object.assign({},{prefix:"",suffix:"",blockPrefix:"",blockSuffix:"",multiline:!1,replaceNext:"",prefixSpace:!1,scanFor:"",surroundWithNewlines:!1,orderedList:!1,unorderedList:!1,trimFirst:!1}),t),o=n.field;o&&(o.focus(),v(o,i))}let O=MarkdownToolbarElement}}]);
//# sourceMappingURL=vendors-node_modules_fzy_js_index_js-node_modules_github_combobox-nav_dist_index_js-node_modu-344bff-29e32232fe0d.js.map