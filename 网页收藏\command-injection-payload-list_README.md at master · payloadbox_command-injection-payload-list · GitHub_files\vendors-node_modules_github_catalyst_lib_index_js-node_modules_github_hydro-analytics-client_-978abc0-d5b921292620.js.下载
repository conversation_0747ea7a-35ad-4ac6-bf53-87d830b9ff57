"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc0","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc1","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc2"],{76006:(e,t,n)=>{let o;n.d(t,{Lj:()=>v,Ih:()=>x,P4:()=>h,nW:()=>I,fA:()=>$,GO:()=>S});let r=new WeakSet;function a(e){r.add(e),e.shadowRoot&&i(e.shadowRoot),s(e),c(e.ownerDocument)}function i(e){s(e),c(e)}let l=new WeakMap;function c(e=document){if(l.has(e))return l.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)f(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&s(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let o={get closed(){return t},unsubscribe(){t=!0,l.delete(e),n.disconnect()}};return l.set(e,o),o}function s(e){for(let t of e.querySelectorAll("[data-action]"))f(t);e instanceof Element&&e.hasAttribute("data-action")&&f(e)}function d(e){let t=e.currentTarget;for(let n of u(t))if(e.type===n.type){let o=t.closest(n.tag);r.has(o)&&"function"==typeof o[n.method]&&o[n.method](e);let a=t.getRootNode();if(a instanceof ShadowRoot&&r.has(a.host)&&a.host.matches(n.tag)){let t=a.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*u(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function f(e){for(let t of u(e))e.addEventListener(t.type,d)}function h(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let o of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!o.closest(n))return o}for(let o of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(o.closest(n)===e)return o}function g(e,t){let n=e.tagName.toLowerCase(),o=[];if(e.shadowRoot)for(let r of e.shadowRoot.querySelectorAll(`[data-targets~="${n}.${t}"]`))r.closest(n)||o.push(r);for(let r of e.querySelectorAll(`[data-targets~="${n}.${t}"]`))r.closest(n)===e&&o.push(r);return o}let b=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),m=(e,t="property")=>{let n=b(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n};function p(e){let t=b(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}return e}function y(e){for(let t of e.querySelectorAll("template[data-shadowroot]"))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0))}let w="attr";function v(e,t){k(e,w).add(t)}let _=new WeakSet;function A(e,t){if(_.has(e))return;_.add(e);let n=Object.getPrototypeOf(e),o=n?.constructor?.attrPrefix??"data-";for(let r of(t||(t=k(n,w)),t)){let t=e[r],n=m(`${o}${r}`),a={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?a={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(a={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,r,a),r in e&&!e.hasAttribute(n)&&a.set.call(e,t)}}function E(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",o=e=>m(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...k(e.prototype,w)].map(o).concat(t),set(e){t=e}})}let C=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let o=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,o)};let r=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,o){t.attributeChangedCallback(this,e,n,o,r)};let a=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,a)},set(e){a=e}}),E(e),p(e)}observedAttributes(e,t){return t}connectedCallback(e,t){e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),y(e),A(e),a(e),t?.call(e),e.shadowRoot&&i(e.shadowRoot)}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,o,r){A(e),"data-catalyst"!==t&&r&&r.call(e,t,n,o)}};function k(e,t){if(!Object.prototype.hasOwnProperty.call(e,C)){let t=e[C],n=e[C]=new Map;if(t)for(let[e,o]of t)n.set(e,new Set(o))}let n=e[C];return n.has(t)||n.set(t,new Set),n.get(t)}function $(e,t){k(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return h(this,t)}})}function S(e,t){k(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return g(this,t)}})}function x(e){new CatalystDelegate(e)}let O=new Map,P=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),j=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},o=()=>t.abort();document.addEventListener("mousedown",o,n),document.addEventListener("touchstart",o,n),document.addEventListener("keydown",o,n),document.addEventListener("pointerdown",o,n)}),L=e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)}),R={ready:()=>P,firstInteraction:()=>j,visible:L},q=new WeakMap;function W(e){cancelAnimationFrame(q.get(e)||0),q.set(e,requestAnimationFrame(()=>{for(let t of O.keys()){let n=e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let o=n?.getAttribute("data-load-on")||"ready",r=o in R?R[o]:R.ready;for(let e of O.get(t)||[])r(t).then(e);O.delete(t),q.delete(e)}}}))}function I(e,t){O.has(e)||O.set(e,new Set),O.get(e).add(t),W(document.body),o||(o=new MutationObserver(e=>{if(O.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&W(e)})).observe(document,{subtree:!0,childList:!0})}},86058:(e,t,n)=>{function o(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}function r(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}function a(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}function i(){return navigator.languages?navigator.languages.join(","):navigator.language||""}function l(){return{referrer:o(),user_agent:navigator.userAgent,screen_resolution:r(),browser_resolution:a(),browser_languages:i(),pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}}n.d(t,{R:()=>AnalyticsClient});var c=n(82918);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,c.b)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n={client_id:this.clientId,page_views:e,events:t,request_context:l()},o=JSON.stringify(n);try{if(navigator.sendBeacon){navigator.sendBeacon(this.collectorUrl,o);return}}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:o,keepalive:!1})}}},88149:(e,t,n)=>{n.d(t,{n:()=>o});function o(e="ha"){let t;let n={},o=document.head.querySelectorAll(`meta[name^="${e}-"]`);for(let r of Array.from(o)){let{name:o,content:a}=r,i=o.replace(`${e}-`,"").replace(/-/g,"_");"url"===i?t=a:n[i]=a}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}}}]);
//# sourceMappingURL=vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc0-012fbf1d9f36.js.map