"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_github_mini-th-55cf52"],{57260:(e,t,n)=>{n.d(t,{P:()=>Attachment});let Attachment=class Attachment{constructor(e,t){this.file=e,this.directory=t,this.state="pending",this.id=null,this.href=null,this.name=null,this.percent=0}static traverse(e,t){return i(e,t)}static from(e){let t=[];for(let n of e)if(n instanceof File)t.push(new Attachment(n));else if(n instanceof Attachment)t.push(n);else throw Error("Unexpected type");return t}get fullPath(){return this.directory?`${this.directory}/${this.file.name}`:this.file.name}isImage(){return["image/gif","image/png","image/jpg","image/jpeg","image/svg+xml"].indexOf(this.file.type)>-1}isVideo(){return["video/mp4","video/quicktime"].indexOf(this.file.type)>-1}saving(e){if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saving`);this.state="saving",this.percent=e}saved(e){var t,n,i;if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saved`);this.state="saved",this.id=null!==(t=null==e?void 0:e.id)&&void 0!==t?t:null,this.href=null!==(n=null==e?void 0:e.href)&&void 0!==n?n:null,this.name=null!==(i=null==e?void 0:e.name)&&void 0!==i?i:null}isPending(){return"pending"===this.state}isSaving(){return"saving"===this.state}isSaved(){return"saved"===this.state}};function i(e,t){return t&&c(e)?l("",u(e)):Promise.resolve(s(Array.from(e.files||[])).map(e=>new Attachment(e)))}function r(e){return e.name.startsWith(".")}function s(e){return Array.from(e).filter(e=>!r(e))}function o(e){return new Promise(function(t,n){e.file(t,n)})}function a(e){return new Promise(function(t,n){let i=[],r=e.createReader(),s=()=>{r.readEntries(e=>{e.length>0?(i.push(...e),s()):t(i)},n)};s()})}async function l(e,t){let n=[];for(let i of s(t))if(i.isDirectory)n.push(...await l(i.fullPath,await a(i)));else{let t=await o(i);n.push(new Attachment(t,e))}return n}function c(e){return e.items&&Array.from(e.items).some(e=>{let t=e.webkitGetAsEntry&&e.webkitGetAsEntry();return t&&t.isDirectory})}function u(e){return Array.from(e.items).map(e=>e.webkitGetAsEntry()).filter(e=>null!=e)}let FileAttachmentElement=class FileAttachmentElement extends HTMLElement{connectedCallback(){this.addEventListener("dragenter",h),this.addEventListener("dragover",h),this.addEventListener("dragleave",p),this.addEventListener("drop",m),this.addEventListener("paste",b),this.addEventListener("change",y)}disconnectedCallback(){this.removeEventListener("dragenter",h),this.removeEventListener("dragover",h),this.removeEventListener("dragleave",p),this.removeEventListener("drop",m),this.removeEventListener("paste",b),this.removeEventListener("change",y)}get directory(){return this.hasAttribute("directory")}set directory(e){e?this.setAttribute("directory",""):this.removeAttribute("directory")}async attach(e){let t=e instanceof DataTransfer?await Attachment.traverse(e,this.directory):Attachment.from(e),n=this.dispatchEvent(new CustomEvent("file-attachment-accept",{bubbles:!0,cancelable:!0,detail:{attachments:t}}));n&&t.length&&this.dispatchEvent(new CustomEvent("file-attachment-accepted",{bubbles:!0,detail:{attachments:t}}))}};function d(e){return Array.from(e.types).indexOf("Files")>=0}let f=null;function h(e){let t=e.currentTarget;f&&clearTimeout(f),f=window.setTimeout(()=>t.removeAttribute("hover"),200);let n=e.dataTransfer;n&&d(n)&&(n.dropEffect="copy",t.setAttribute("hover",""),e.preventDefault())}function p(e){e.dataTransfer&&(e.dataTransfer.dropEffect="none");let t=e.currentTarget;t.removeAttribute("hover"),e.stopPropagation(),e.preventDefault()}function m(e){let t=e.currentTarget;if(!(t instanceof FileAttachmentElement))return;t.removeAttribute("hover");let n=e.dataTransfer;n&&d(n)&&(t.attach(n),e.stopPropagation(),e.preventDefault())}let v=/^image\/(gif|png|jpeg)$/;function g(e){for(let t of e)if("file"===t.kind&&v.test(t.type))return t.getAsFile();return null}function b(e){if(!e.clipboardData||!e.clipboardData.items)return;let t=e.currentTarget;if(!(t instanceof FileAttachmentElement))return;let n=g(e.clipboardData.items);n&&(t.attach([n]),e.preventDefault())}function y(e){let t=e.currentTarget;if(!(t instanceof FileAttachmentElement))return;let n=e.target;if(!(n instanceof HTMLInputElement))return;let i=t.getAttribute("input");if(i&&n.id!==i)return;let r=n.files;r&&0!==r.length&&(t.attach(r),n.value="")}window.customElements.get("file-attachment")||(window.FileAttachmentElement=FileAttachmentElement,window.customElements.define("file-attachment",FileAttachmentElement));var E=null},3447:(e,t,n)=>{n.d(t,{D:()=>r});var i=n(46263);function r(e=0,t={}){return(n,r,s)=>{if(!s||"function"!=typeof s.value)throw Error("debounce can only decorate functions");let o=s.value;s.value=(0,i.D)(o,e,t),Object.defineProperty(n,r,s)}}},98105:(e,t,n)=>{function i(e){let t=e.getBoundingClientRect();return{top:t.top+window.pageYOffset,left:t.left+window.pageXOffset}}function r(e){let t=e,n=t.ownerDocument;if(!n||!t.offsetParent)return;let i=n.defaultView.HTMLElement;if(t!==n.body){for(;t!==n.body;){if(!(t.parentElement instanceof i))return;t=t.parentElement;let{position:e,overflowY:n,overflowX:r}=getComputedStyle(t);if("fixed"===e||"auto"===n||"auto"===r||"scroll"===n||"scroll"===r)break}return t instanceof Document?null:t}}function s(e,t){let n=t,i=e.ownerDocument;if(!i)return;let r=i.documentElement;if(!r||e===r)return;let s=o(e,n);if(!s)return;n=s._container;let a=n===i.documentElement&&i.defaultView?{top:i.defaultView.pageYOffset,left:i.defaultView.pageXOffset}:{top:n.scrollTop,left:n.scrollLeft},l=s.top-a.top,c=s.left-a.left,u=n.clientHeight,d=n.clientWidth,f=u-(l+e.offsetHeight),h=d-(c+e.offsetWidth);return{top:l,left:c,bottom:f,right:h,height:u,width:d}}function o(e,t){let n,i,r,s=e,o=s.ownerDocument;if(!o)return;let c=o.documentElement;if(!c)return;let u=o.defaultView.HTMLElement,d=0,f=0,h=s.offsetHeight,p=s.offsetWidth;for(;!(s===o.body||s===t);){if(d+=s.offsetTop||0,f+=s.offsetLeft||0,!(s.offsetParent instanceof u))return;s=s.offsetParent}if(t&&t!==o&&t!==o.defaultView&&t!==o.documentElement&&t!==o.body){if(!(t instanceof u))return;r=t,n=t.scrollHeight,i=t.scrollWidth}else r=c,n=a(o.body,c),i=l(o.body,c);let m=n-(d+h),v=i-(f+p);return{top:d,left:f,bottom:m,right:v,_container:r}}function a(e,t){return Math.max(e.scrollHeight,t.scrollHeight,e.offsetHeight,t.offsetHeight,t.clientHeight)}function l(e,t){return Math.max(e.scrollWidth,t.scrollWidth,e.offsetWidth,t.offsetWidth,t.clientWidth)}n.d(t,{VZ:()=>r,_C:()=>s,cv:()=>i,oE:()=>o})},48858:(e,t,n)=>{let i;n.d(t,{e:()=>l});var r=n(78160);(0,n(44542).O)();let s=[];function o(){let e=s.pop();e&&l(e.container,e.initialFocus,e.originalSignal)}function a(e){let t=new AbortController;return e.addEventListener("abort",()=>{t.abort()}),t}function l(e,t,n){let l;let c=new AbortController,u=null!=n?n:c.signal;e.setAttribute("data-focus-trap","active");let d=document.createElement("span");d.setAttribute("class","sentinel"),d.setAttribute("tabindex","0"),d.setAttribute("aria-hidden","true"),d.onfocus=()=>{let t=(0,r.O)(e,!0);null==t||t.focus()};let f=document.createElement("span");function h(n){if(n instanceof HTMLElement&&document.contains(e)){if(e.contains(n)){l=n;return}if(l&&(0,r.Wq)(l)&&e.contains(l)){l.focus();return}if(t&&e.contains(t)){t.focus();return}{let t=(0,r.O)(e);null==t||t.focus();return}}}f.setAttribute("class","sentinel"),f.setAttribute("tabindex","0"),f.setAttribute("aria-hidden","true"),f.onfocus=()=>{let t=(0,r.O)(e);null==t||t.focus()},e.prepend(d),e.append(f);let p=a(u);if(i){let e=i;i.container.setAttribute("data-focus-trap","suspended"),i.controller.abort(),s.push(e)}p.signal.addEventListener("abort",()=>{i=void 0}),u.addEventListener("abort",()=>{e.removeAttribute("data-focus-trap");let t=e.getElementsByClassName("sentinel");for(;t.length>0;)t[0].remove();let n=s.findIndex(t=>t.container===e);n>=0&&s.splice(n,1),o()}),document.addEventListener("focus",e=>{h(e.target)},{signal:p.signal,capture:!0}),h(document.activeElement),i={container:e,controller:p,initialFocus:t,originalSignal:u};let m=s.findIndex(t=>t.container===e);if(m>=0&&s.splice(m,1),!n)return c}},44542:(e,t,n)=>{n.d(t,{O:()=>a});let i=!1;function r(){}try{let e=Object.create({},{signal:{get(){i=!0}}});window.addEventListener("test",r,e),window.removeEventListener("test",r,e)}catch(e){}function s(){return i}function o(){if("undefined"==typeof window)return;let e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(t,n,i){return"object"==typeof i&&"signal"in i&&i.signal instanceof AbortSignal&&e.call(i.signal,"abort",()=>{this.removeEventListener(t,n,i)}),e.call(this,t,n,i)}}function a(){s()||(o(),i=!0)}},78160:(e,t,n)=>{function*i(e,t={}){var n,i;let r=null!==(n=t.strict)&&void 0!==n&&n,a=null!==(i=t.onlyTabbable)&&void 0!==i&&i?o:s,l=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e instanceof HTMLElement&&a(e,r)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),c=null;if(!t.reverse&&a(e,r)&&(yield e),t.reverse){let e=l.lastChild();for(;e;)c=e,e=l.lastChild()}else c=l.firstChild();for(;c instanceof HTMLElement;)yield c,c=t.reverse?l.previousNode():l.nextNode();t.reverse&&a(e,r)&&(yield e)}function r(e,t=!1){return i(e,{reverse:t,strict:!0,onlyTabbable:!0}).next().value}function s(e,t=!1){let n=["BUTTON","INPUT","SELECT","TEXTAREA","OPTGROUP","OPTION","FIELDSET"].includes(e.tagName)&&e.disabled,i=e.hidden,r=e instanceof HTMLInputElement&&"hidden"===e.type,s=e.classList.contains("sentinel");if(n||i||r||s)return!1;if(t){let t=0===e.offsetWidth||0===e.offsetHeight,n=["hidden","collapse"].includes(getComputedStyle(e).visibility),i=0===e.getClientRects().length;if(t||n||i)return!1}return null!=e.getAttribute("tabindex")||(!(e instanceof HTMLAnchorElement)||null!=e.getAttribute("href"))&&-1!==e.tabIndex}function o(e,t=!1){return s(e,t)&&"-1"!==e.getAttribute("tabindex")}n.d(t,{EB:()=>s,O:()=>r,Wq:()=>o,hT:()=>i})},59753:(e,t,n)=>{function i(){if(!(this instanceof i))return new i;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{f:()=>H,S:()=>C,on:()=>O});var r,s=window.document.documentElement,o=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector;i.prototype.matchesSelector=function(e,t){return o.call(e,t)},i.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},i.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;i.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;i.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;i.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),i.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},r="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var u=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var n,i,r,s,o,a,l=(e=e.slice(0).concat(e.default)).length,c=t,d=[];do if(u.exec(""),(r=u.exec(c))&&(c=r[3],r[2]||!c)){for(n=0;n<l;n++)if(o=(a=e[n]).selector(r[1])){for(i=d.length,s=!1;i--;)if(d[i].index===a&&d[i].key===o){s=!0;break}s||d.push({index:a,key:o});break}}while(r)return d}function f(e,t){var n,i,r;for(n=0,i=e.length;n<i;n++)if(r=e[n],t.isPrototypeOf(r))return r}function h(e,t){return e.id-t.id}i.prototype.logDefaultIndexUsed=function(){},i.prototype.add=function(e,t){var n,i,s,o,a,l,c,u,h=this.activeIndexes,p=this.selectors,m=this.selectorObjects;if("string"==typeof e){for(i=0,m[(n={id:this.uid++,selector:e,data:t}).id]=n,c=d(this.indexes,e);i<c.length;i++)o=(u=c[i]).key,(a=f(h,s=u.index))||((a=Object.create(s)).map=new r,h.push(a)),s===this.indexes.default&&this.logDefaultIndexUsed(n),(l=a.map.get(o))||(l=[],a.map.set(o,l)),l.push(n);this.size++,p.push(e)}},i.prototype.remove=function(e,t){if("string"==typeof e){var n,i,r,s,o,a,l,c,u=this.activeIndexes,f=this.selectors=[],h=this.selectorObjects,p={},m=1==arguments.length;for(r=0,n=d(this.indexes,e);r<n.length;r++)for(i=n[r],s=u.length;s--;)if(a=u[s],i.index.isPrototypeOf(a)){if(l=a.map.get(i.key))for(o=l.length;o--;)(c=l[o]).selector===e&&(m||c.data===t)&&(l.splice(o,1),p[c.id]=!0);break}for(r in p)delete h[r],this.size--;for(r in h)f.push(h[r].selector)}},i.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,i,r,s,o,a,l,c={},u=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,i=d.length;t<i;t++)for(n=0,s=d[t],r=(o=this.matches(s)).length;n<r;n++)c[(l=o[n]).id]?a=c[l.id]:(a={id:l.id,selector:l.selector,data:l.data,elements:[]},c[l.id]=a,u.push(a)),a.elements.push(s);return u.sort(h)},i.prototype.matches=function(e){if(!e)return[];var t,n,i,r,s,o,a,l,c,u,d,f=this.activeIndexes,p={},m=[];for(t=0,r=f.length;t<r;t++)if(l=(a=f[t]).element(e)){for(n=0,s=l.length;n<s;n++)if(c=a.map.get(l[n]))for(i=0,o=c.length;i<o;i++)!p[d=(u=c[i]).id]&&this.matchesSelector(e,u.selector)&&(p[d]=!0,m.push(u))}return m.sort(h)};var p={},m={},v=new WeakMap,g=new WeakMap,b=new WeakMap,y=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function E(e,t,n){var i=e[t];return e[t]=function(){return n.apply(e,arguments),i.apply(e,arguments)},e}function A(e,t,n){var i=[],r=t;do{if(1!==r.nodeType)break;var s=e.matches(r);if(s.length){var o={node:r,observers:s};n?i.unshift(o):i.push(o)}}while(r=r.parentElement)return i}function w(){v.set(this,!0)}function T(){v.set(this,!0),g.set(this,!0)}function x(){return b.get(this)||null}function L(e,t){y&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||y.get})}function k(e){try{return e.eventPhase,!0}catch(e){return!1}}function S(e){if(k(e)){var t=(1===e.eventPhase?m:p)[e.type];if(t){var n=A(t,e.target,1===e.eventPhase);if(n.length){E(e,"stopPropagation",w),E(e,"stopImmediatePropagation",T),L(e,x);for(var i=0,r=n.length;i<r&&!v.get(e);i++){var s=n[i];b.set(e,s.node);for(var o=0,a=s.observers.length;o<a&&!g.get(e);o++)s.observers[o].data.call(s.node,e)}b.delete(e),L(e)}}}}function O(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},s=!!r.capture,o=s?m:p,a=o[e];a||(a=new i,o[e]=a,document.addEventListener(e,S,s)),a.add(t,n)}function C(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=!!i.capture,s=r?m:p,o=s[e];o&&(o.remove(t,n),o.size||(delete s[e],document.removeEventListener(e,S,r)))}function H(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},10160:(e,t,n)=>{n.d(t,{Z:()=>Combobox});let Combobox=class Combobox{constructor(e,t,{tabInsertsSuggestions:n,defaultFirstOption:r}={}){this.input=e,this.list=t,this.tabInsertsSuggestions=null==n||n,this.defaultFirstOption=null!=r&&r,this.isComposing=!1,t.id||(t.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=e=>i(e,this),this.compositionEventHandler=e=>l(e,this),this.inputHandler=this.clearSelection.bind(this),e.setAttribute("role","combobox"),e.setAttribute("aria-controls",t.id),e.setAttribute("aria-expanded","false"),e.setAttribute("aria-autocomplete","list"),e.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",r),this.indicateDefaultOption()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",r)}indicateDefaultOption(){var e;this.defaultFirstOption&&(null===(e=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(a)[0])||void 0===e||e.setAttribute("data-combobox-option-default","true"))}navigate(e=1){let t=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(a)[0],n=Array.from(this.list.querySelectorAll('[role="option"]')).filter(a),i=n.indexOf(t);if(i===n.length-1&&1===e||0===i&&-1===e){this.clearSelection(),this.input.focus();return}let r=1===e?0:n.length-1;if(t&&i>=0){let t=i+e;t>=0&&t<n.length&&(r=t)}let s=n[r];if(s)for(let e of n)e.removeAttribute("data-combobox-option-default"),s===e?(this.input.setAttribute("aria-activedescendant",s.id),s.setAttribute("aria-selected","true"),c(this.list,s)):e.removeAttribute("aria-selected")}clearSelection(){for(let e of(this.input.removeAttribute("aria-activedescendant"),this.list.querySelectorAll('[aria-selected="true"]')))e.removeAttribute("aria-selected");this.indicateDefaultOption()}};function i(e,t){if(!e.shiftKey&&!e.metaKey&&!e.altKey&&(t.ctrlBindings||!e.ctrlKey)&&!t.isComposing)switch(e.key){case"Enter":s(t.input,t.list)&&e.preventDefault();break;case"Tab":t.tabInsertsSuggestions&&s(t.input,t.list)&&e.preventDefault();break;case"Escape":t.clearSelection();break;case"ArrowDown":t.navigate(1),e.preventDefault();break;case"ArrowUp":t.navigate(-1),e.preventDefault();break;case"n":t.ctrlBindings&&e.ctrlKey&&(t.navigate(1),e.preventDefault());break;case"p":t.ctrlBindings&&e.ctrlKey&&(t.navigate(-1),e.preventDefault());break;default:if(e.ctrlKey)break;t.clearSelection()}}function r(e){if(!(e.target instanceof Element))return;let t=e.target.closest('[role="option"]');t&&"true"!==t.getAttribute("aria-disabled")&&o(t,{event:e})}function s(e,t){let n=t.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!n&&("true"===n.getAttribute("aria-disabled")||(n.click(),!0))}function o(e,t){e.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:t}))}function a(e){return!e.hidden&&!(e instanceof HTMLInputElement&&"hidden"===e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}function l(e,t){t.isComposing="compositionstart"===e.type;let n=document.getElementById(t.input.getAttribute("aria-controls")||"");n&&t.clearSelection()}function c(e,t){u(e,t)||(e.scrollTop=t.offsetTop)}function u(e,t){let n=e.scrollTop,i=n+e.clientHeight,r=t.offsetTop,s=r+t.clientHeight;return r>=n&&s<=i}},11793:(e,t,n)=>{n.d(t,{EL:()=>i,N9:()=>b,Tz:()=>y});let Leaf=class Leaf{constructor(e){this.children=[],this.parent=e}delete(e){let t=this.children.indexOf(e);return -1!==t&&(this.children=this.children.slice(0,t).concat(this.children.slice(t+1)),0===this.children.length&&this.parent.delete(this),!0)}add(e){return this.children.push(e),this}};let RadixTrie=class RadixTrie{constructor(e){this.parent=null,this.children={},this.parent=e||null}get(e){return this.children[e]}insert(e){let t=this;for(let n=0;n<e.length;n+=1){let i=e[n],r=t.get(i);if(n===e.length-1)return r instanceof RadixTrie&&(t.delete(r),r=null),r||(r=new Leaf(t),t.children[i]=r),r;r instanceof Leaf&&(r=null),r||(r=new RadixTrie(t),t.children[i]=r),t=r}return t}delete(e){for(let t in this.children){let n=this.children[t];if(n===e){let e=delete this.children[t];return 0===Object.keys(this.children).length&&this.parent&&this.parent.delete(this),e}}return!1}};function i(e){let{ctrlKey:t,altKey:n,metaKey:i,key:o}=e,a=[],l=[t,n,i,s(e)];for(let[e,t]of l.entries())t&&a.push(r[e]);return r.includes(o)||a.push(o),a.join("+")}let r=["Control","Alt","Meta","Shift"];function s(e){let{shiftKey:t,code:n,key:i}=e;return t&&!(n.startsWith("Key")&&i.toUpperCase()===i)}function o(e,t){return c(l(e,t))}let a=/Mac|iPod|iPhone|iPad/i;function l(e,t=navigator.platform){let n=a.test(t)?"Meta":"Control";return e.replace("Mod",n)}function c(e){let t=e.split("+").pop(),n=[];for(let t of["Control","Alt","Meta","Shift"])e.includes(t)&&n.push(t);return n.push(t),n.join("+")}let SequenceTracker=class SequenceTracker{constructor({onReset:e}={}){this._path=[],this.timer=null,this.onReset=e}get path(){return this._path}get sequence(){return this._path.join(" ")}registerKeypress(e){this._path=[...this._path,i(e)],this.startTimer()}reset(){var e;this.killTimer(),this._path=[],null===(e=this.onReset)||void 0===e||e.call(this)}killTimer(){null!=this.timer&&window.clearTimeout(this.timer),this.timer=null}startTimer(){this.killTimer(),this.timer=window.setTimeout(()=>this.reset(),SequenceTracker.CHORD_TIMEOUT)}};function u(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==n&&"reset"!==n&&"checkbox"!==n&&"radio"!==n&&"file"!==n||e.isContentEditable}function d(e,t){let n=new CustomEvent("hotkey-fire",{cancelable:!0,detail:{path:t}}),i=!e.dispatchEvent(n);i||(u(e)?e.focus():e.click())}function f(e){let t=[],n=[""],i=!1;for(let r=0;r<e.length;r++){if(i&&","===e[r]){t.push(n),n=[""],i=!1;continue}if(" "===e[r]){n.push(""),i=!1;continue}i="+"!==e[r],n[n.length-1]+=e[r]}return t.push(n),t.map(e=>e.map(e=>o(e)).filter(e=>""!==e)).filter(e=>e.length>0)}SequenceTracker.CHORD_TIMEOUT=1500;let h=new RadixTrie,p=new WeakMap,m=h,v=new SequenceTracker({onReset(){m=h}});function g(e){if(e.defaultPrevented||!(e.target instanceof Node))return;if(u(e.target)){let t=e.target;if(!t.id||!t.ownerDocument.querySelector(`[data-hotkey-scope="${t.id}"]`))return}let t=m.get(i(e));if(!t){v.reset();return}if(v.registerKeypress(e),m=t,t instanceof Leaf){let n;let i=e.target,r=!1,s=u(i);for(let e=t.children.length-1;e>=0;e-=1){n=t.children[e];let o=n.getAttribute("data-hotkey-scope");if(!s&&!o||s&&i.id===o){r=!0;break}}n&&r&&(d(n,v.path),e.preventDefault()),v.reset()}}function b(e,t){0===Object.keys(h.children).length&&document.addEventListener("keydown",g);let n=f(t||e.getAttribute("data-hotkey")||""),i=n.map(t=>h.insert(t).add(e));p.set(e,i)}function y(e){let t=p.get(e);if(t&&t.length)for(let n of t)n&&n.delete(e);0===Object.keys(h.children).length&&document.removeEventListener("keydown",g)}}}]);
//# sourceMappingURL=vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_github_mini-th-55cf52-098d9882131e.js.map