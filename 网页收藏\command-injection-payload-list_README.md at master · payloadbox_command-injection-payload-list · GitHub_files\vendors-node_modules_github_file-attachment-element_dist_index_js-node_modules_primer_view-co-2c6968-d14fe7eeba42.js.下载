"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-2c6968","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc0","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc1","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc2"],{57260:(t,e,n)=>{n.d(e,{P:()=>Attachment});let Attachment=class Attachment{constructor(t,e){this.file=t,this.directory=e,this.state="pending",this.id=null,this.href=null,this.name=null,this.percent=0}static traverse(t,e){return i(t,e)}static from(t){let e=[];for(let n of t)if(n instanceof File)e.push(new Attachment(n));else if(n instanceof Attachment)e.push(n);else throw Error("Unexpected type");return e}get fullPath(){return this.directory?`${this.directory}/${this.file.name}`:this.file.name}isImage(){return["image/gif","image/png","image/jpg","image/jpeg","image/svg+xml"].indexOf(this.file.type)>-1}isVideo(){return["video/mp4","video/quicktime"].indexOf(this.file.type)>-1}saving(t){if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saving`);this.state="saving",this.percent=t}saved(t){var e,n,i;if("pending"!==this.state&&"saving"!==this.state)throw Error(`Unexpected transition from ${this.state} to saved`);this.state="saved",this.id=null!==(e=null==t?void 0:t.id)&&void 0!==e?e:null,this.href=null!==(n=null==t?void 0:t.href)&&void 0!==n?n:null,this.name=null!==(i=null==t?void 0:t.name)&&void 0!==i?i:null}isPending(){return"pending"===this.state}isSaving(){return"saving"===this.state}isSaved(){return"saved"===this.state}};function i(t,e){return e&&c(t)?l("",u(t)):Promise.resolve(r(Array.from(t.files||[])).map(t=>new Attachment(t)))}function o(t){return t.name.startsWith(".")}function r(t){return Array.from(t).filter(t=>!o(t))}function s(t){return new Promise(function(e,n){t.file(e,n)})}function a(t){return new Promise(function(e,n){let i=[],o=t.createReader(),r=()=>{o.readEntries(t=>{t.length>0?(i.push(...t),r()):e(i)},n)};r()})}async function l(t,e){let n=[];for(let i of r(e))if(i.isDirectory)n.push(...await l(i.fullPath,await a(i)));else{let e=await s(i);n.push(new Attachment(e,t))}return n}function c(t){return t.items&&Array.from(t.items).some(t=>{let e=t.webkitGetAsEntry&&t.webkitGetAsEntry();return e&&e.isDirectory})}function u(t){return Array.from(t.items).map(t=>t.webkitGetAsEntry()).filter(t=>null!=t)}let FileAttachmentElement=class FileAttachmentElement extends HTMLElement{connectedCallback(){this.addEventListener("dragenter",f),this.addEventListener("dragover",f),this.addEventListener("dragleave",p),this.addEventListener("drop",m),this.addEventListener("paste",v),this.addEventListener("change",w)}disconnectedCallback(){this.removeEventListener("dragenter",f),this.removeEventListener("dragover",f),this.removeEventListener("dragleave",p),this.removeEventListener("drop",m),this.removeEventListener("paste",v),this.removeEventListener("change",w)}get directory(){return this.hasAttribute("directory")}set directory(t){t?this.setAttribute("directory",""):this.removeAttribute("directory")}async attach(t){let e=t instanceof DataTransfer?await Attachment.traverse(t,this.directory):Attachment.from(t),n=this.dispatchEvent(new CustomEvent("file-attachment-accept",{bubbles:!0,cancelable:!0,detail:{attachments:e}}));n&&e.length&&this.dispatchEvent(new CustomEvent("file-attachment-accepted",{bubbles:!0,detail:{attachments:e}}))}};function d(t){return Array.from(t.types).indexOf("Files")>=0}let h=null;function f(t){let e=t.currentTarget;h&&clearTimeout(h),h=window.setTimeout(()=>e.removeAttribute("hover"),200);let n=t.dataTransfer;n&&d(n)&&(n.dropEffect="copy",e.setAttribute("hover",""),t.preventDefault())}function p(t){t.dataTransfer&&(t.dataTransfer.dropEffect="none");let e=t.currentTarget;e.removeAttribute("hover"),t.stopPropagation(),t.preventDefault()}function m(t){let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;e.removeAttribute("hover");let n=t.dataTransfer;n&&d(n)&&(e.attach(n),t.stopPropagation(),t.preventDefault())}let g=/^image\/(gif|png|jpeg)$/;function b(t){for(let e of t)if("file"===e.kind&&g.test(e.type))return e.getAsFile();return null}function v(t){if(!t.clipboardData||!t.clipboardData.items)return;let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;let n=b(t.clipboardData.items);n&&(e.attach([n]),t.preventDefault())}function w(t){let e=t.currentTarget;if(!(e instanceof FileAttachmentElement))return;let n=t.target;if(!(n instanceof HTMLInputElement))return;let i=e.getAttribute("input");if(i&&n.id!==i)return;let o=n.files;o&&0!==o.length&&(e.attach(o),n.value="")}window.customElements.get("file-attachment")||(window.FileAttachmentElement=FileAttachmentElement,window.customElements.define("file-attachment",FileAttachmentElement));var y=null},48030:(t,e,n)=>{n.d(e,{N:()=>r});let i={"outside-top":["outside-bottom","outside-right","outside-left","outside-bottom"],"outside-bottom":["outside-top","outside-right","outside-left","outside-bottom"],"outside-left":["outside-right","outside-bottom","outside-top","outside-bottom"],"outside-right":["outside-left","outside-bottom","outside-top","outside-bottom"]},o={start:["end","center"],end:["start","center"],center:["end","start"]};function r(t,e,n={}){let i=s(t),o=l(i),r=getComputedStyle(i),a=i.getBoundingClientRect(),[c,h]=[r.borderTopWidth,r.borderLeftWidth].map(t=>parseInt(t,10)||0),f={top:a.top+c,left:a.left+h};return d(o,f,t.getBoundingClientRect(),e instanceof Element?e.getBoundingClientRect():e,u(n))}function s(t){if(a(t))return document.body;let e=t.parentNode;for(;null!==e;){if(e instanceof HTMLElement&&"static"!==getComputedStyle(e).position)return e;e=e.parentNode}return document.body}function a(t){var e;if("DIALOG"===t.tagName)return!0;try{if(t.matches(":popover-open")&&/native code/.test(null===(e=document.body.showPopover)||void 0===e?void 0:e.toString()))return!0}catch(t){}return!1}function l(t){let e=t;for(;null!==e&&e!==document.body;){let t=getComputedStyle(e);if("visible"!==t.overflow)break;e=e.parentNode}let n=e!==document.body&&e instanceof HTMLElement?e:document.body,i=n.getBoundingClientRect(),o=getComputedStyle(n),[r,s,a,l]=[o.borderTopWidth,o.borderLeftWidth,o.borderRightWidth,o.borderBottomWidth].map(t=>parseInt(t,10)||0);return{top:i.top+r,left:i.left+s,width:i.width-a-s,height:Math.max(i.height-r-l,n===document.body?window.innerHeight:-1/0)}}let c={side:"outside-bottom",align:"start",anchorOffset:4,alignmentOffset:4,allowOutOfBounds:!1};function u(t={}){var e,n,i,o,r;let s=null!==(e=t.side)&&void 0!==e?e:c.side,a=null!==(n=t.align)&&void 0!==n?n:c.align;return{side:s,align:a,anchorOffset:null!==(i=t.anchorOffset)&&void 0!==i?i:"inside-center"===s?0:c.anchorOffset,alignmentOffset:null!==(o=t.alignmentOffset)&&void 0!==o?o:"center"!==a&&s.startsWith("inside")?c.alignmentOffset:0,allowOutOfBounds:null!==(r=t.allowOutOfBounds)&&void 0!==r?r:c.allowOutOfBounds}}function d(t,e,n,r,{side:s,align:a,allowOutOfBounds:l,anchorOffset:c,alignmentOffset:u}){let d={top:t.top-e.top,left:t.left-e.left,width:t.width,height:t.height},m=h(n,r,s,a,c,u),g=s,b=a;if(m.top-=e.top,m.left-=e.left,!l){let l=i[s],v=0;if(l){let t=s;for(;v<l.length&&f(t,m,d,n);){let i=l[v++];t=i,m=h(n,r,i,a,c,u),m.top-=e.top,m.left-=e.left,g=i}}let w=o[a],y=0;if(w){let t=a;for(;y<w.length&&p(t,m,d,n);){let i=w[y++];t=i,m=h(n,r,g,i,c,u),m.top-=e.top,m.left-=e.left,b=i}}m.top<d.top&&(m.top=d.top),m.left<d.left&&(m.left=d.left),m.left+n.width>t.width+d.left&&(m.left=t.width+d.left-n.width),l&&v<l.length&&m.top+n.height>t.height+d.top&&(m.top=t.height+d.top-n.height)}return Object.assign(Object.assign({},m),{anchorSide:g,anchorAlign:b})}function h(t,e,n,i,o,r){let s=e.left+e.width,a=e.top+e.height,l=-1,c=-1;return"outside-top"===n?l=e.top-o-t.height:"outside-bottom"===n?l=a+o:"outside-left"===n?c=e.left-o-t.width:"outside-right"===n&&(c=s+o),("outside-top"===n||"outside-bottom"===n)&&(c="start"===i?e.left+r:"center"===i?e.left-(t.width-e.width)/2+r:s-t.width-r),("outside-left"===n||"outside-right"===n)&&(l="start"===i?e.top+r:"center"===i?e.top-(t.height-e.height)/2+r:a-t.height-r),"inside-top"===n?l=e.top+o:"inside-bottom"===n?l=a-o-t.height:"inside-left"===n?c=e.left+o:"inside-right"===n?c=s-o-t.width:"inside-center"===n&&(c=(s+e.left)/2-t.width/2+o),"inside-top"===n||"inside-bottom"===n?c="start"===i?e.left+r:"center"===i?e.left-(t.width-e.width)/2+r:s-t.width-r:("inside-left"===n||"inside-right"===n||"inside-center"===n)&&(l="start"===i?e.top+r:"center"===i?e.top-(t.height-e.height)/2+r:a-t.height-r),{top:l,left:c}}function f(t,e,n,i){return"outside-top"===t||"outside-bottom"===t?e.top<n.top||e.top+i.height>n.height+n.top:e.left<n.left||e.left+i.width>n.width+n.left}function p(t,e,n,i){return"end"===t?e.left<n.left:"start"===t||"center"===t?e.left+i.width>n.left+n.width||e.left<n.left:void 0}},64686:(t,e,n)=>{var i,o,r,s,a,l,c,u,d,h,f,p,m,g,b,v,w,y,E,A,x,k,L,T,C,M,S,I,P,O,D,j,H,R,W,q,$,B,_,F,N,z,K,V,X,G,Y,U,J,Q,Z,tt,te,tn,ti,to,tr,ts,ta,tl,tc,tu,td,th=n(27034),tf=class extends Event{oldState;newState;constructor(t,{oldState:e="",newState:n="",...i}={}){super(t,i),this.oldState=String(e||""),this.newState=String(n||"")}},tp=new WeakMap;function tm(t,e,n){tp.set(t,setTimeout(()=>{tp.has(t)&&t.dispatchEvent(new tf("toggle",{cancelable:!1,oldState:e,newState:n}))},0))}var tg=globalThis.ShadowRoot||function(){},tb=globalThis.HTMLDialogElement||function(){},tv=new WeakMap,tw=new WeakMap,ty=new WeakMap;function tE(t){return ty.get(t)||"hidden"}var tA=new WeakMap;function tx(t){let e=t.popoverTargetElement;if(!e)return;let n=tE(e);("show"!==t.popoverTargetAction||"showing"!==n)&&("hide"!==t.popoverTargetAction||"hidden"!==n)&&("showing"===n?tW(e,!0,!0):tk(e,!1)&&(tA.set(e,t),tR(e)))}function tk(t,e){return!("auto"!==t.popover&&"manual"!==t.popover||!t.isConnected||e&&"showing"!==tE(t)||!e&&"hidden"!==tE(t)||t instanceof tb&&t.hasAttribute("open"))&&document.fullscreenElement!==t}function tL(t){return t?Array.from(tw.get(t.ownerDocument)||[]).indexOf(t)+1:0}function tT(t){let e=tS(t),n=tI(t);return tL(e)>tL(n)?e:n}function tC(t){let e=tw.get(t);for(let t of e||[]){if(t.isConnected)return t;e.delete(t)}return null}function tM(t){return"function"==typeof t.getRootNode?t.getRootNode():t.parentNode?tM(t.parentNode):t}function tS(t){for(;t;){if(t instanceof HTMLElement&&"auto"===t.popover&&"showing"===ty.get(t))return t;if((t=t.parentElement||tM(t))instanceof tg&&(t=t.host),t instanceof Document)return}}function tI(t){for(;t;){let e=t.popoverTargetElement;if(e)return e;if((t=t.parentElement||tM(t))instanceof tg&&(t=t.host),t instanceof Document)return}}function tP(t){let e=new Map,n=0,i=t.ownerDocument;for(let t of tw.get(i)||[])e.set(t,n),n+=1;e.set(t,n),n+=1;let o=null;return!function(t){let n=tS(t);if(null===n)return;let i=e.get(n);(null===o||e.get(o)<i)&&(o=n)}(t?.parentElement),o}function tO(t){return!(t.hidden||(t instanceof HTMLButtonElement||t instanceof HTMLInputElement||t instanceof HTMLSelectElement||t instanceof HTMLTextAreaElement||t instanceof HTMLOptGroupElement||t instanceof HTMLOptionElement||t instanceof HTMLFieldSetElement)&&t.disabled||t instanceof HTMLInputElement&&"hidden"===t.type||t instanceof HTMLAnchorElement&&""===t.href)&&-1!==t.tabIndex}function tD(t){if(t.shadowRoot&&!0!==t.shadowRoot.delegatesFocus)return null;let e=t;e.shadowRoot&&(e=e.shadowRoot);let n=e.querySelector("[autofocus]");if(n)return n;let i=t.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_ELEMENT),o=i.currentNode;for(;o;){if(tO(o))return o;o=i.nextNode()}}function tj(t){tD(t)?.focus()}var tH=new WeakMap;function tR(t){if(!tk(t,!1))return;let e=t.ownerDocument;if(!t.dispatchEvent(new tf("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!tk(t,!1))return;let n=!1;if("auto"===t.popover){let n=t.getAttribute("popover"),i=tP(t)||e;if(t$(i,!1,!0),n!==t.getAttribute("popover")||!tk(t,!1))return}tC(e)||(n=!0),tH.delete(t);let i=e.activeElement;t.classList.add(":popover-open"),ty.set(t,"showing"),tv.has(e)||tv.set(e,new Set),tv.get(e).add(t),tj(t),"auto"===t.popover&&(tw.has(e)||tw.set(e,new Set),tw.get(e).add(t),tN(tA.get(t),!0)),n&&i&&"auto"===t.popover&&tH.set(t,i),tm(t,"closed","open")}function tW(t,e=!1,n=!1){if(!tk(t,!0))return;let i=t.ownerDocument;if("auto"===t.popover&&(t$(t,e,n),!tk(t,!0))||(tN(tA.get(t),!1),tA.delete(t),n&&(t.dispatchEvent(new tf("beforetoggle",{oldState:"open",newState:"closed"})),!tk(t,!0))))return;tv.get(i)?.delete(t),tw.get(i)?.delete(t),t.classList.remove(":popover-open"),ty.set(t,"hidden"),n&&tm(t,"open","closed");let o=tH.get(t);o&&(tH.delete(t),e&&o.focus())}function tq(t,e=!1,n=!1){let i=tC(t);for(;i;)tW(i,e,n),i=tC(t)}function t$(t,e,n){let i=t.ownerDocument||t;if(t instanceof Document)return tq(i,e,n);let o=null,r=!1;for(let e of tw.get(i)||[])if(e===t)r=!0;else if(r){o=e;break}if(!r)return tq(i,e,n);for(;o&&"showing"===tE(o)&&tw.get(i)?.size;)tW(o,e,n)}var tB=new WeakMap;function t_(t){if(!t.isTrusted)return;let e=t.composedPath()[0];if(!e)return;let n=e.ownerDocument,i=tC(n);if(!i)return;let o=tT(e);if(o&&"pointerdown"===t.type)tB.set(n,o);else if("pointerup"===t.type){let t=tB.get(n)===o;tB.delete(n),t&&t$(o||n,!1,!0)}}var tF=new WeakMap;function tN(t,e=!1){if(!t)return;tF.has(t)||tF.set(t,t.getAttribute("aria-expanded"));let n=t.popoverTargetElement;if(n&&"auto"===n.popover)t.setAttribute("aria-expanded",String(e));else{let e=tF.get(t);e?t.setAttribute("aria-expanded",e):t.removeAttribute("aria-expanded")}}var tz=globalThis.ShadowRoot||function(){};function tK(t,e,n){let i=t[e];Object.defineProperty(t,e,{value(t){return i.call(this,n(t))}})}var tV=/(^|[^\\]):popover-open\b/g;function tX(){var t;function e(t){return t?.includes(":popover-open")&&(t=t.replace(tV,"$1.\\:popover-open")),t}window.ToggleEvent=window.ToggleEvent||tf,tK(Document.prototype,"querySelector",e),tK(Document.prototype,"querySelectorAll",e),tK(Element.prototype,"querySelector",e),tK(Element.prototype,"querySelectorAll",e),tK(Element.prototype,"matches",e),tK(Element.prototype,"closest",e),tK(DocumentFragment.prototype,"querySelectorAll",e),tK(DocumentFragment.prototype,"querySelectorAll",e),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let t=(this.getAttribute("popover")||"").toLowerCase();return""===t||"auto"==t?"auto":"manual"},set(t){this.setAttribute("popover",t)}},showPopover:{enumerable:!0,configurable:!0,value(){tR(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){tW(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(t){"showing"===ty.get(this)&&void 0===t||!1===t?tW(this,!0,!0):(void 0===t||!0===t)&&tR(this)}}});let n=new WeakMap;function i(t){Object.defineProperties(t.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(t){if(null===t)this.removeAttribute("popovertarget"),n.delete(this);else if(t instanceof Element)this.setAttribute("popovertarget",""),n.set(this,t);else throw TypeError("popoverTargetElement must be an element or null")},get(){if("button"!==this.localName&&"input"!==this.localName||"input"===this.localName&&"reset"!==this.type&&"image"!==this.type&&"button"!==this.type||this.disabled||this.form&&"submit"===this.type)return null;let t=n.get(this);if(t&&t.isConnected)return t;if(t&&!t.isConnected)return n.delete(this),null;let e=tM(this),i=this.getAttribute("popovertarget");return(e instanceof Document||e instanceof tz)&&i&&e.getElementById(i)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let t=(this.getAttribute("popovertargetaction")||"").toLowerCase();return"show"===t||"hide"===t?t:"toggle"},set(t){this.setAttribute("popovertargetaction",t)}}})}i(HTMLButtonElement),i(HTMLInputElement);let o=t=>{if(!t.isTrusted)return;let e=t.composedPath()[0];if(!(e instanceof Element)||e?.shadowRoot)return;let n=tM(e);if(!(n instanceof tz||n instanceof Document))return;let i=e.closest("[popovertargetaction],[popovertarget]");if(i){tx(i);return}},r=t=>{let e=t.key,n=t.target;n&&("Escape"===e||"Esc"===e)&&t$(n.ownerDocument,!0,!0)};(t=document).addEventListener("click",o),t.addEventListener("keydown",r),t.addEventListener("pointerdown",t_),t.addEventListener("pointerup",t_)}"undefined"!=typeof HTMLElement&&"object"==typeof HTMLElement.prototype&&"popover"in HTMLElement.prototype||tX();var tG=n(76006),tY=n(22114),tU=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},tJ=function(t,e,n,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,n):o?o.value=n:e.set(t,n),n},tQ=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)};let tZ=new IntersectionObserver(t=>{for(let e of t){let t=e.target;e.isIntersecting&&t instanceof t1&&t.update()}}),t0=new ResizeObserver(t=>{for(let e of t){let t=e.target;t instanceof t1&&t.update(e.contentRect)}}),t1=class ActionBarElement extends HTMLElement{constructor(){super(...arguments),i.add(this),o.set(this,void 0),r.set(this,void 0),s.set(this,null)}connectedCallback(){var t,e,n,i;for(let s of(tJ(this,r,null!==(t=this.offsetWidth)&&void 0!==t?t:1/0,"f"),tJ(this,o,null!==(e=this.itemContainer.offsetWidth)&&void 0!==e?e:1/0,"f"),this.items)){let t=s.getBoundingClientRect().width,e=parseInt(null===(n=window.getComputedStyle(s))||void 0===n?void 0:n.marginLeft,10),o=parseInt(null===(i=window.getComputedStyle(s))||void 0===i?void 0:i.marginRight,10);s.setAttribute("data-offset-width",`${t+e+o}`)}t0.observe(this),tZ.observe(this),setTimeout(()=>{this.style.overflow="visible",this.update()},20)}disconnectedCallback(){t0.unobserve(this),tZ.unobserve(this)}menuItemClick(t){var e;let n=t.currentTarget,i=null==n?void 0:n.getAttribute("data-for");i&&(null===(e=document.getElementById(i))||void 0===e||e.click())}update(t=this.getBoundingClientRect()){t.width<=tQ(this,r,"f")||0===tQ(this,r,"f")?tQ(this,i,"m",u).call(this):t.width>tQ(this,r,"f")&&tQ(this,i,"m",d).call(this),tJ(this,r,t.width,"f"),t.width<=tQ(this,o,"f")?this.style.justifyContent="space-between":this.style.justifyContent="flex-end",tQ(this,s,"f")&&tQ(this,s,"f").abort(),tJ(this,s,(0,tY.km)(this,{bindKeys:tY.Qw.ArrowHorizontal|tY.Qw.HomeAndEnd,focusOutBehavior:"wrap",focusableElementFilter:t=>!t.closest(".ActionBar-item[hidden]")&&!t.closest("li.ActionListItem")}),"f")}};o=new WeakMap,r=new WeakMap,s=new WeakMap,i=new WeakSet,a=function(){var t;return parseInt(null===(t=window.getComputedStyle(this.itemContainer))||void 0===t?void 0:t.columnGap,10)||0},l=function(){return this.offsetWidth-this.itemContainer.offsetWidth},c=function(){return this.moreMenu.hidden?0:this.moreMenu.offsetWidth+tQ(this,i,"m",a).call(this)},u=function(){if(this.items[0].hidden)return;let t=this.items.length-1;for(let e of this.items.reverse()){if(!e.hidden&&tQ(this,i,"m",l).call(this)<tQ(this,i,"m",c).call(this))tQ(this,i,"m",f).call(this,t);else if(tQ(this,i,"m",l).call(this)>=tQ(this,i,"m",c).call(this))return;t--}},d=function(){if(!this.items[this.items.length-1].hidden)return;let t=0;for(let e of this.items){if(e.hidden){let n=Number(e.getAttribute("data-offset-width"));if(!(tQ(this,i,"m",l).call(this)>=tQ(this,i,"m",c).call(this)+n)&&t!==this.items.length-1)return;tQ(this,i,"m",h).call(this,t)}t++}this.items[this.items.length-1].hidden||(this.moreMenu.hidden=!0)},h=function(t){this.items[t].hidden=!1,tQ(this,i,"a",p)[t].hidden=!0},f=function(t){this.items[t].hidden=!0,tQ(this,i,"a",p)[t].hidden=!1,this.moreMenu.hidden&&(this.moreMenu.hidden=!1)},p=function(){return this.moreMenu.querySelectorAll('[role="menu"] > li')},tU([tG.GO],t1.prototype,"items",void 0),tU([tG.fA],t1.prototype,"itemContainer",void 0),tU([tG.fA],t1.prototype,"moreMenu",void 0),t1=tU([tG.Ih],t1),window.ActionBarElement=t1;var t3=n(11095),t2=n(48030),t6=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)},t4=function(t,e,n,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,n):o?o.value=n:e.set(t,n),n};let t5=(()=>{let t=new Set,e=null,n=null;function i(){for(let e of t)e.update()}return o=>{window.addEventListener("resize",i),e||(e=new IntersectionObserver(e=>{for(let n of e){let e=n.target;n.isIntersecting?(e.update(),t.add(e)):t.delete(e)}})),n||(n=new ResizeObserver(()=>{for(let e of t)e.update()})),n.observe(o.ownerDocument.documentElement),e.observe(o)}})();let AnchoredPositionElement=class AnchoredPositionElement extends HTMLElement{constructor(){super(...arguments),m.set(this,null),g.set(this,void 0)}get align(){let t=this.getAttribute("align");return"center"===t||"end"===t?t:"start"}set align(t){this.setAttribute("align",`${t}`)}get side(){let t=this.getAttribute("side");return"inside-top"===t||"inside-bottom"===t||"inside-left"===t||"inside-right"===t||"inside-center"===t||"outside-top"===t||"outside-left"===t||"outside-right"===t?t:"outside-bottom"}set side(t){this.setAttribute("side",`${t}`)}get anchorOffset(){let t=this.getAttribute("anchor-offset");return"spacious"===t||"8"===t?8:4}set anchorOffset(t){this.setAttribute("anchor-offset",`${t}`)}get anchor(){return this.getAttribute("anchor")||""}set anchor(t){this.setAttribute("anchor",`${t}`)}get anchorElement(){if(t6(this,m,"f"))return t6(this,m,"f");let t=this.anchor;return t?this.ownerDocument.getElementById(t):null}set anchorElement(t){t4(this,m,t,"f"),t6(this,m,"f")||this.removeAttribute("anchor")}get alignmentOffset(){return Number(this.getAttribute("alignment-offset"))}set alignmentOffset(t){this.setAttribute("alignment-offset",`${t}`)}get allowOutOfBounds(){return this.hasAttribute("allow-out-of-bounds")}set allowOutOfBounds(t){this.toggleAttribute("allow-out-of-bounds",t)}connectedCallback(){this.update(),this.addEventListener("beforetoggle",()=>this.update()),t5(this)}attributeChangedCallback(){this.update()}update(){this.isConnected&&(cancelAnimationFrame(t6(this,g,"f")),t4(this,g,requestAnimationFrame(()=>{let t=this.anchorElement;if(this.classList.toggle("not-anchored",!t),t){let{left:e,top:n}=(0,t2.N)(this,t,this);this.style.top=`${n}px`,this.style.left=`${e}px`,this.style.bottom="auto",this.style.right="auto"}else this.style.top="0",this.style.left="0",this.style.bottom="0",this.style.right="0"}),"f"))}};m=new WeakMap,g=new WeakMap,AnchoredPositionElement.observedAttributes=["align","side","anchor","alignment-offset","allow-out-of-bounds"],customElements.get("anchored-position")||(window.AnchoredPositionElement=AnchoredPositionElement,customElements.define("anchored-position",AnchoredPositionElement));var t7=function(t,e,n,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,n):o?o.value=n:e.set(t,n),n},t9=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)};let t8=['[role="menuitem"]','[role="menuitemcheckbox"]','[role="menuitemradio"]'],et=t8.map(t=>`:not([hidden]) > ${t}`).join(", "),ee=t=>{var e;return null===(e=t.textContent)||void 0===e?void 0:e.trim()[0].toLowerCase()},en=/^\S$/;let FocusGroupElement=class FocusGroupElement extends HTMLElement{constructor(){super(...arguments),b.add(this),v.set(this,null),w.set(this,null)}get nowrap(){return this.hasAttribute("nowrap")}set nowrap(t){this.toggleAttribute("nowrap",t)}get direction(){return"horizontal"===this.getAttribute("direction")?"horizontal":"vertical"}set direction(t){this.setAttribute("direction",`${t}`)}get retain(){return this.hasAttribute("retain")}set retain(t){this.toggleAttribute("retain",t)}get mnemonics(){return this.hasAttribute("mnemonics")}connectedCallback(){t7(this,w,new AbortController,"f");let{signal:t}=t9(this,w,"f");this.addEventListener("keydown",this,{signal:t}),this.addEventListener("click",this,{signal:t}),this.addEventListener("mouseover",this,{signal:t}),this.addEventListener("focusin",this,{signal:t})}disconnectedCallback(){var t;null===(t=t9(this,w,"f"))||void 0===t||t.abort()}handleEvent(t){var e;let{direction:n,nowrap:i}=this;if("focusin"===t.type){if(this.retain&&t.target instanceof Element&&t.target.matches(et)){null===(e=t9(this,v,"f"))||void 0===e||e.abort();let{signal:n}=t7(this,v,new AbortController,"f");for(let e of t9(this,b,"a",y)){e.setAttribute("tabindex",e===t.target?"0":"-1");let i=t.target.closest("[popover]");e===t.target&&(null==i?void 0:i.popover)==="auto"&&i.closest("focus-group")===this&&i.addEventListener("toggle",t=>{var n,o;if(t.target instanceof Element&&"closed"===t.newState&&(null===(n=t9(this,v,"f"))||void 0===n||n.abort(),e.setAttribute("tabindex","-1"),i.id)){let t=this.querySelector(`[popovertarget="${i.id}"]`);t?t.setAttribute("tabindex","0"):null===(o=t9(this,b,"a",y)[0])||void 0===o||o.setAttribute("tabindex","0")}},{signal:n})}}}else if(t instanceof KeyboardEvent){let e=Array.from(t9(this,b,"a",y)),o=e.indexOf(t.target),r=t.key;if("Up"===r||"ArrowUp"===r)("vertical"===n||"both"===n)&&(o-=o<0?0:1,t.preventDefault());else if("Down"===r||"ArrowDown"===r)("vertical"===n||"both"===n)&&(o+=1,t.preventDefault());else if("Left"===t.key||"ArrowLeft"===t.key)("horizontal"===n||"both"===n)&&(o-=1,t.preventDefault());else if("Right"===t.key||"ArrowRight"===t.key)("horizontal"===n||"both"===n)&&(o+=1,t.preventDefault());else if("Home"===t.key||"PageUp"===t.key)o=0,t.preventDefault();else if("End"===t.key||"PageDown"===t.key)o=e.length-1,t.preventDefault();else{if(!(this.mnemonics&&en.test(r)))return;let n=r.toLowerCase(),s=o>0&&ee(t.target)===n?o:0;(o=e.findIndex((t,e)=>e>s&&ee(t)===n))<0&&!i&&(o=e.findIndex(t=>ee(t)===n))}i&&o<0&&(o=0),!i&&o>=e.length&&(o=0);let s=e.at(Math.min(o,e.length-1));{let e=s;do(null==(e=e.closest("[popover]:not(:popover-open)"))?void 0:e.popover)!=="auto"||["ArrowRight","ArrowLeft"].includes(t.key)||e.showPopover(),e=(null==e?void 0:e.parentElement)||null;while(e)}null==s||s.focus()}}};v=new WeakMap,w=new WeakMap,b=new WeakSet,y=function(){return this.querySelectorAll(et)},customElements.get("focus-group")||(window.FocusGroupElement=FocusGroupElement,customElements.define("focus-group",FocusGroupElement));let ei=new WeakMap,eo=new WeakMap,er=new WeakMap;function es(t){let e=t.currentTarget;if(!(e instanceof ImageCropElement))return;let{box:n,image:i}=er.get(e)||{};if(!n||!i)return;let o=0,r=0;if(t instanceof KeyboardEvent)"ArrowUp"===t.key?r=-1:"ArrowDown"===t.key?r=1:"ArrowLeft"===t.key?o=-1:"ArrowRight"===t.key&&(o=1);else if(eo.has(e)&&t instanceof MouseEvent){let n=eo.get(e);o=t.pageX-n.dragStartX,r=t.pageY-n.dragStartY}else if(eo.has(e)&&t instanceof TouchEvent){let{pageX:n,pageY:i}=t.changedTouches[0],{dragStartX:s,dragStartY:a}=eo.get(e);o=n-s,r=i-a}if(0!==o||0!==r){let t=Math.min(Math.max(0,n.offsetLeft+o),i.width-n.offsetWidth),s=Math.min(Math.max(0,n.offsetTop+r),i.height-n.offsetHeight);n.style.left=`${t}px`,n.style.top=`${s}px`,ef(e,{x:t,y:s,width:n.offsetWidth,height:n.offsetHeight})}if(t instanceof MouseEvent)eo.set(e,{dragStartX:t.pageX,dragStartY:t.pageY});else if(t instanceof TouchEvent){let{pageX:n,pageY:i}=t.changedTouches[0];eo.set(e,{dragStartX:n,dragStartY:i})}}function ea(t){let e,n,i;let o=t.target;if(!(o instanceof HTMLElement))return;let r=el(o);if(!(r instanceof ImageCropElement))return;let{box:s}=er.get(r)||{};if(!s)return;let a=r.getBoundingClientRect();if(t instanceof KeyboardEvent){if("Escape"===t.key)return ed(r);if("-"===t.key&&(i=-10),"="===t.key&&(i=10),!i)return;e=s.offsetWidth+i,n=s.offsetHeight+i,ei.set(r,{startX:s.offsetLeft,startY:s.offsetTop})}else if(t instanceof MouseEvent){let i=ei.get(r);if(!i)return;e=t.pageX-i.startX-a.left-window.pageXOffset,n=t.pageY-i.startY-a.top-window.pageYOffset}else if(t instanceof TouchEvent){let i=ei.get(r);if(!i)return;e=t.changedTouches[0].pageX-i.startX-a.left-window.pageXOffset,n=t.changedTouches[0].pageY-i.startY-a.top-window.pageYOffset}e&&n&&eu(r,e,n,!(t instanceof KeyboardEvent))}function el(t){let e=t.getRootNode();return e instanceof ShadowRoot?e.host:t}function ec(t){let e=t.currentTarget;if(!(e instanceof HTMLElement))return;let n=el(e);if(!(n instanceof ImageCropElement))return;let{box:i}=er.get(n)||{};if(!i)return;let o=t.target;if(o instanceof HTMLElement){if(o.hasAttribute("data-direction")){let e=o.getAttribute("data-direction")||"";n.addEventListener("mousemove",ea),n.addEventListener("touchmove",ea,{passive:!0}),["nw","se"].indexOf(e)>=0&&n.classList.add("nwse"),["ne","sw"].indexOf(e)>=0&&n.classList.add("nesw"),ei.set(n,{startX:i.offsetLeft+(["se","ne"].indexOf(e)>=0?0:i.offsetWidth),startY:i.offsetTop+(["se","sw"].indexOf(e)>=0?0:i.offsetHeight)}),ea(t)}else n.addEventListener("mousemove",es),n.addEventListener("touchmove",es,{passive:!0})}}function eu(t,e,n,i=!0){let o=Math.max(Math.abs(e),Math.abs(n),10),r=ei.get(t);if(!r)return;let{box:s,image:a}=er.get(t)||{};if(!s||!a)return;o=Math.min(o,n>0?a.height-r.startY:r.startY,e>0?a.width-r.startX:r.startX);let l=i?Math.round(Math.max(0,e>0?r.startX:r.startX-o)):s.offsetLeft,c=i?Math.round(Math.max(0,n>0?r.startY:r.startY-o)):s.offsetTop;s.style.left=`${l}px`,s.style.top=`${c}px`,s.style.width=`${o}px`,s.style.height=`${o}px`,ef(t,{x:l,y:c,width:o,height:o})}function ed(t){let{image:e}=er.get(t)||{};if(!e)return;let n=Math.round(e.clientWidth>e.clientHeight?e.clientHeight:e.clientWidth);ei.set(t,{startX:(e.clientWidth-n)/2,startY:(e.clientHeight-n)/2}),eu(t,n,n)}function eh(t){let e=t.currentTarget;e instanceof ImageCropElement&&(eo.delete(e),e.classList.remove("nwse","nesw"),e.removeEventListener("mousemove",ea),e.removeEventListener("mousemove",es),e.removeEventListener("touchmove",ea),e.removeEventListener("touchmove",es))}function ef(t,e){let{image:n}=er.get(t)||{};if(!n)return;let i=n.naturalWidth/n.width;for(let n in e){let o=Math.round(e[n]*i);e[n]=o;let r=t.querySelector(`[data-image-crop-input='${n}']`);r instanceof HTMLInputElement&&(r.value=o.toString())}t.dispatchEvent(new CustomEvent("image-crop-change",{bubbles:!0,detail:e}))}let ImageCropElement=class ImageCropElement extends HTMLElement{connectedCallback(){if(er.has(this))return;let t=this.attachShadow({mode:"open"});t.innerHTML=`
<style>
  :host { touch-action: none; display: block; }
  :host(.nesw) { cursor: nesw-resize; }
  :host(.nwse) { cursor: nwse-resize; }
  :host(.nesw) .crop-box, :host(.nwse) .crop-box { cursor: inherit; }
  :host([loaded]) .crop-image { display: block; }
  :host([loaded]) ::slotted([data-loading-slot]), .crop-image { display: none; }

  .crop-wrapper {
    position: relative;
    font-size: 0;
  }
  .crop-container {
    user-select: none;
    -ms-user-select: none;
    -moz-user-select: none;
    -webkit-user-select: none;
    position: absolute;
    overflow: hidden;
    z-index: 1;
    top: 0;
    width: 100%;
    height: 100%;
  }

  :host([rounded]) .crop-box {
    border-radius: 50%;
    box-shadow: 0 0 0 4000px rgba(0, 0, 0, 0.3);
  }
  .crop-box {
    position: absolute;
    border: 1px dashed #fff;
    box-sizing: border-box;
    cursor: move;
  }

  :host([rounded]) .crop-outline {
    outline: none;
  }
  .crop-outline {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    outline: 4000px solid rgba(0, 0, 0, .3);
  }

  .handle { position: absolute; }
  :host([rounded]) .handle::before { border-radius: 50%; }
  .handle:before {
    position: absolute;
    display: block;
    padding: 4px;
    transform: translate(-50%, -50%);
    content: ' ';
    background: #fff;
    border: 1px solid #767676;
  }
  .ne { top: 0; right: 0; cursor: nesw-resize; }
  .nw { top: 0; left: 0; cursor: nwse-resize; }
  .se { bottom: 0; right: 0; cursor: nwse-resize; }
  .sw { bottom: 0; left: 0; cursor: nesw-resize; }
</style>
<slot></slot>
<div class="crop-wrapper">
  <img width="100%" class="crop-image" alt="">
  <div class="crop-container">
    <div data-crop-box class="crop-box">
      <div class="crop-outline"></div>
      <div data-direction="nw" class="handle nw"></div>
      <div data-direction="ne" class="handle ne"></div>
      <div data-direction="sw" class="handle sw"></div>
      <div data-direction="se" class="handle se"></div>
    </div>
  </div>
</div>
`;let e=t.querySelector("[data-crop-box]");if(!(e instanceof HTMLElement))return;let n=t.querySelector("img");n instanceof HTMLImageElement&&(er.set(this,{box:e,image:n}),n.addEventListener("load",()=>{this.loaded=!0,ed(this)}),this.addEventListener("mouseleave",eh),this.addEventListener("touchend",eh),this.addEventListener("mouseup",eh),e.addEventListener("mousedown",ec),e.addEventListener("touchstart",ec,{passive:!0}),this.addEventListener("keydown",es),this.addEventListener("keydown",ea),this.src&&(n.src=this.src))}static get observedAttributes(){return["src"]}get src(){return this.getAttribute("src")}set src(t){t?this.setAttribute("src",t):this.removeAttribute("src")}get loaded(){return this.hasAttribute("loaded")}set loaded(t){t?this.setAttribute("loaded",""):this.removeAttribute("loaded")}attributeChangedCallback(t,e,n){let{image:i}=er.get(this)||{};"src"===t&&(this.loaded=!1,i&&(i.src=n))}};window.customElements.get("image-crop")||(window.ImageCropElement=ImageCropElement,window.customElements.define("image-crop",ImageCropElement));var ep=n(28585),em=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},eg=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)};let eb=class NavListElement extends HTMLElement{constructor(){super(...arguments),E.add(this)}connectedCallback(){this.setShowMoreItemState()}get showMoreDisabled(){return this.showMoreItem.hasAttribute("aria-disabled")}set showMoreDisabled(t){t?this.showMoreItem.setAttribute("aria-disabled","true"):this.showMoreItem.removeAttribute("aria-disabled"),this.showMoreItem.classList.toggle("disabled",t)}set currentPage(t){this.showMoreItem.setAttribute("data-current-page",t.toString())}get currentPage(){return parseInt(this.showMoreItem.getAttribute("data-current-page"))||1}get totalPages(){return parseInt(this.showMoreItem.getAttribute("data-total-pages"))||1}get paginationSrc(){return this.showMoreItem.getAttribute("src")||""}selectItemById(t){if(!t)return!1;let e=eg(this,E,"m",x).call(this,t);return!!e&&(eg(this,E,"m",T).call(this,e),!0)}selectItemByHref(t){if(!t)return!1;let e=eg(this,E,"m",k).call(this,t);return!!e&&(eg(this,E,"m",T).call(this,e),!0)}selectItemByCurrentLocation(){let t=eg(this,E,"m",L).call(this);return!!t&&(eg(this,E,"m",T).call(this,t),!0)}expandItem(t){var e;null===(e=t.nextElementSibling)||void 0===e||e.removeAttribute("data-hidden"),t.setAttribute("aria-expanded","true")}collapseItem(t){var e;null===(e=t.nextElementSibling)||void 0===e||e.setAttribute("data-hidden",""),t.setAttribute("aria-expanded","false"),t.focus()}itemIsExpanded(t){return(null==t?void 0:t.tagName)==="A"||(null==t?void 0:t.getAttribute("aria-expanded"))==="true"}handleItemWithSubItemClick(t){let e=t.target;if(!(e instanceof HTMLElement))return;let n=e.closest("button");n&&(this.itemIsExpanded(n)?this.collapseItem(n):this.expandItem(n),t.stopPropagation())}handleItemWithSubItemKeydown(t){let e=t.currentTarget;if(!(e instanceof HTMLElement))return;let n=e.closest("button");if(!n){let t=e.getAttribute("aria-labelledby");if(!t)return;n=document.getElementById(t)}this.itemIsExpanded(n)&&"Escape"===t.key&&this.collapseItem(n),t.stopPropagation()}async showMore(t){var e,n;let i;if(t.preventDefault(),this.showMoreDisabled)return;this.showMoreDisabled=!0;try{let t=new URL(this.paginationSrc,window.location.origin);this.currentPage++,t.searchParams.append("page",this.currentPage.toString());let e=await fetch(t);if(!e.ok)return;i=await e.text(),this.currentPage===this.totalPages&&(this.showMoreItem.hidden=!0)}catch(t){this.showMoreDisabled=!1,this.currentPage--;return}let o=eg(this,E,"m",A).call(this,document,i);null===(e=null==o?void 0:o.querySelector("li > a"))||void 0===e||e.setAttribute("data-targets","nav-list.focusMarkers");let r=t.target.closest("button").getAttribute("data-list-id"),s=document.getElementById(r);s.append(o),null===(n=this.focusMarkers.pop())||void 0===n||n.focus(),this.showMoreDisabled=!1}setShowMoreItemState(){this.showMoreItem&&(this.currentPage<this.totalPages?this.showMoreItem.hidden=!1:this.showMoreItem.hidden=!0)}};E=new WeakSet,A=function(t,e){let n=t.createElement("template");return n.innerHTML=e,t.importNode(n.content,!0)},x=function(t){var e;for(let n of this.items){if(n.classList.contains("ActionListItem--hasSubItem"))continue;let i=(null===(e=n.getAttribute("data-item-id"))||void 0===e?void 0:e.split(" "))||[];if(i.includes(t))return n}return null},k=function(t){let e=this.querySelector(`.ActionListContent[href="${t}"]`);return e?e.closest(".ActionListItem"):null},L=function(){return eg(this,E,"m",k).call(this,window.location.pathname)},T=function(t){let e=this.querySelector(".ActionListItem--navActive");e&&eg(this,E,"m",C).call(this,e),t.classList.add("ActionListItem--navActive"),t.children.length>0&&t.children[0].setAttribute("aria-current","page");let n=eg(this,E,"m",M).call(this,t);n&&(this.expandItem(n),n.classList.add("ActionListContent--hasActiveSubItem"))},C=function(t){t.classList.remove("ActionListItem--navActive"),t.children.length>0&&t.children[0].removeAttribute("aria-current");let e=eg(this,E,"m",M).call(this,t);e&&(this.collapseItem(e),e.classList.remove("ActionListContent--hasActiveSubItem"))},M=function(t){var e;if(!t.classList.contains("ActionListItem--subItem"))return null;let n=null===(e=t.closest("li.ActionListItem--hasSubItem"))||void 0===e?void 0:e.querySelector("button.ActionListContent");return n||null},em([tG.GO],eb.prototype,"items",void 0),em([tG.fA],eb.prototype,"showMoreItem",void 0),em([tG.GO],eb.prototype,"focusMarkers",void 0),eb=em([tG.Ih],eb);var ev=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},ew=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)};let ey=class SegmentedControlElement extends HTMLElement{constructor(){super(...arguments),S.add(this)}connectedCallback(){ew(this,S,"m",I).call(this)}select(t){var e,n;let i=t.currentTarget;for(let t of this.items)t.classList.remove("SegmentedControl-item--selected"),null===(e=t.querySelector("[aria-current]"))||void 0===e||e.setAttribute("aria-current","false");null===(n=i.closest("li.SegmentedControl-item"))||void 0===n||n.classList.add("SegmentedControl-item--selected"),i.setAttribute("aria-current","true")}};S=new WeakSet,I=function(){for(let t of this.querySelectorAll(".Button-label"))t.setAttribute("data-content",t.textContent||"")},ev([tG.GO],ey.prototype,"items",void 0),ey=ev([tG.Ih],ey),window.customElements.get("segmented-control")||(window.SegmentedControlElement=ey,window.customElements.define("segmented-control",ey));var eE=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s};let eA=class ToggleSwitchElement extends HTMLElement{constructor(){super(...arguments),this.toggling=!1}get src(){let t=this.getAttribute("src");if(!t)return null;let e=this.ownerDocument.createElement("a");return e.href=t,e.href}get csrf(){let t=this.querySelector("[data-csrf]");return this.getAttribute("csrf")||t instanceof HTMLInputElement&&t.value||null}get csrfField(){return this.getAttribute("csrf-field")||"authenticity_token"}isRemote(){return null!=this.src}async toggle(){if(!this.toggling&&(this.toggling=!0,!this.isDisabled())){if(!this.isRemote()){this.performToggle(),this.toggling=!1;return}this.performToggle(),this.setLoadingState();try{await this.submitForm()}catch(t){t instanceof Error&&(this.setErrorState(t.message||"An error occurred, please try again."),this.performToggle());return}finally{this.toggling=!1}this.setSuccessState()}}turnOn(){this.isDisabled()||(this.switch.setAttribute("aria-pressed","true"),this.classList.add("ToggleSwitch--checked"))}turnOff(){this.isDisabled()||(this.switch.setAttribute("aria-pressed","false"),this.classList.remove("ToggleSwitch--checked"))}isOn(){return"true"===this.switch.getAttribute("aria-pressed")}isOff(){return!this.isOn()}isDisabled(){return null!=this.switch.getAttribute("disabled")}disable(){this.switch.setAttribute("disabled","disabled")}enable(){this.switch.removeAttribute("disabled")}performToggle(){this.isOn()?this.turnOff():this.turnOn()}setLoadingState(){this.errorIcon.setAttribute("hidden","hidden"),this.loadingSpinner.removeAttribute("hidden");let t=new CustomEvent("toggleSwitchLoading",{bubbles:!0});this.dispatchEvent(t)}setSuccessState(){let t=new CustomEvent("toggleSwitchSuccess",{bubbles:!0});this.dispatchEvent(t),this.setFinishedState(!1)}setErrorState(t){let e=new CustomEvent("toggleSwitchError",{bubbles:!0,detail:t});this.dispatchEvent(e),this.setFinishedState(!0)}setFinishedState(t){t&&this.errorIcon.removeAttribute("hidden"),this.loadingSpinner.setAttribute("hidden","hidden")}async submitForm(){let t;let e=new FormData;if(this.csrf&&e.append(this.csrfField,this.csrf),e.append("value",this.isOn()?"1":"0"),!this.src)throw Error("invalid src");try{t=await fetch(this.src,{credentials:"same-origin",method:"POST",headers:{"Requested-With":"XMLHttpRequest"},body:e})}catch(t){throw Error("A network error occurred, please try again.")}if(!t.ok)throw Error(await t.text())}};eE([tG.fA],eA.prototype,"switch",void 0),eE([tG.fA],eA.prototype,"loadingSpinner",void 0),eE([tG.fA],eA.prototype,"errorIcon",void 0),eA=eE([tG.Ih],eA),window.customElements.get("toggle-switch")||(window.ToggleSwitchElement=eA,window.customElements.define("toggle-switch",eA));var ex=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)},ek=function(t,e,n,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,n):o?o.value=n:e.set(t,n),n};let eL=(()=>{let t;function e(e){try{return t=":popover-open",e.matches(t)}catch(n){try{return t=":open",e.matches(":open")}catch(n){return t=".\\:popover-open",e.matches(".\\:popover-open")}}}return n=>t?n.matches(t):e(n)})(),eT="sr-only",eC=["tooltip-n","tooltip-s","tooltip-e","tooltip-w","tooltip-ne","tooltip-se","tooltip-nw","tooltip-sw"];function eM(t){for(let e of eP)e!==t&&(eL(e)?e.hidePopover():eP.delete(e))}function eS(){eM()}let eI=new Set,eP=new Set;let ToolTipElement=class ToolTipElement extends HTMLElement{constructor(){super(...arguments),P.add(this),O.set(this,void 0),D.set(this,"center"),j.set(this,"outside-bottom"),H.set(this,!1)}styles(){return`
      :host {
        padding: .5em .75em !important;
        font: normal normal 11px/1.5 -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
        -webkit-font-smoothing: subpixel-antialiased;
        color: var(--color-fg-on-emphasis) !important;
        text-align: center;
        text-decoration: none;
        text-shadow: none;
        text-transform: none;
        letter-spacing: normal;
        word-wrap: break-word;
        white-space: pre;
        background: var(--color-neutral-emphasis-plus) !important;
        border-radius: 6px;
        border: 0 !important;
        opacity: 0;
        max-width: 250px;
        word-wrap: break-word;
        white-space: normal;
        width: max-content !important;
        inset: var(--tool-tip-position-top, 0) auto auto var(--tool-tip-position-left, 0) !important;
        overflow: visible !important;
      }

      :host:before{
        position: absolute;
        z-index: 1000001;
        color: var(--color-neutral-emphasis-plus);
        content: "";
        border: 6px solid transparent;
        opacity: 0
      }

      @keyframes tooltip-appear {
        from {
          opacity: 0;
        }
        to {
          opacity: 1
        }
      }

      :host:after{
        position: absolute;
        display: block;
        right: 0;
        left: 0;
        height: 12px;
        content: ""
      }

      :host(:popover-open),
      :host(:popover-open):before {
        animation-name: tooltip-appear;
        animation-duration: .1s;
        animation-fill-mode: forwards;
        animation-timing-function: ease-in;
        animation-delay: .4s
      }

      :host(.\\:popover-open),
      :host(.\\:popover-open):before {
        animation-name: tooltip-appear;
        animation-duration: .1s;
        animation-fill-mode: forwards;
        animation-timing-function: ease-in;
        animation-delay: .4s
      }

      :host(.tooltip-s):before,
      :host(.tooltip-n):before {
        right: 50%;
        margin-right: -6px;
      }

      :host(.tooltip-s):before,
      :host(.tooltip-se):before,
      :host(.tooltip-sw):before {
        bottom: 100%;
        border-bottom-color: var(--color-neutral-emphasis-plus)
      }

      :host(.tooltip-s):after,
      :host(.tooltip-se):after,
      :host(.tooltip-sw):after {
        bottom: 100%
      }

      :host(.tooltip-n):before,
      :host(.tooltip-ne):before,
      :host(.tooltip-nw):before {
        top: 100%;
        border-top-color: var(--color-neutral-emphasis-plus)
      }

      :host(.tooltip-n):after,
      :host(.tooltip-ne):after,
      :host(.tooltip-nw):after {
        top: 100%
      }

      :host(.tooltip-se):before,
      :host(.tooltip-ne):before {
        left: 0;
        margin-left: 6px;
      }

      :host(.tooltip-sw):before,
      :host(.tooltip-nw):before {
        right: 0;
        margin-right: 6px;
      }

      :host(.tooltip-w):before {
        top: 50%;
        bottom: 50%;
        left: 100%;
        margin-top: -6px;
        border-left-color: var(--color-neutral-emphasis-plus)
      }

      :host(.tooltip-e):before {
        top: 50%;
        right: 100%;
        bottom: 50%;
        margin-top: -6px;
        border-right-color: var(--color-neutral-emphasis-plus)
      }
    `}get htmlFor(){return this.getAttribute("for")||""}set htmlFor(t){this.setAttribute("for",t)}get type(){let t=this.getAttribute("data-type");return"label"===t?"label":"description"}set type(t){this.setAttribute("data-type",t)}get direction(){return this.getAttribute("data-direction")||"s"}set direction(t){this.setAttribute("data-direction",t)}get control(){return this.ownerDocument.getElementById(this.htmlFor)}set hiddenFromView(t){t&&eL(this)?this.hidePopover():t||eL(this)||this.showPopover()}get hiddenFromView(){return!eL(this)}connectedCallback(){var t,e;if(eI.add(this),ex(this,P,"m",W).call(this),ex(this,P,"m",q).call(this),!this.shadowRoot){let t=this.attachShadow({mode:"open"}),e=t.appendChild(document.createElement("style"));e.textContent=this.styles(),t.appendChild(document.createElement("slot"))}if(ex(this,P,"m",R).call(this,!1),ek(this,H,!0,"f"),!this.control)return;this.setAttribute("role","tooltip"),null===(t=ex(this,O,"f"))||void 0===t||t.abort(),ek(this,O,new AbortController,"f");let{signal:n}=ex(this,O,"f");this.addEventListener("mouseleave",this,{signal:n}),this.addEventListener("toggle",this,{signal:n}),this.control.addEventListener("mouseenter",this,{signal:n}),this.control.addEventListener("mouseleave",this,{signal:n}),this.control.addEventListener("focus",this,{signal:n}),this.control.addEventListener("mousedown",this,{signal:n}),null===(e=this.control.popoverTargetElement)||void 0===e||e.addEventListener("beforetoggle",this,{signal:n}),this.ownerDocument.addEventListener("focusout",eS),this.ownerDocument.addEventListener("keydown",this,{signal:n})}disconnectedCallback(){var t;eI.delete(this),eP.delete(this),null===(t=ex(this,O,"f"))||void 0===t||t.abort()}async handleEvent(t){if(!this.control)return;let e=eL(this),n="mouseenter"===t.type||"focus"===t.type,i="mouseleave"===t.type&&t.relatedTarget!==this.control&&t.relatedTarget!==this,o="keydown"===t.type&&"Escape"===t.key,r="mousedown"===t.type&&t.currentTarget===this.control,s="beforetoggle"===t.type&&t.currentTarget!==this;await Promise.resolve(),e||!n||eL(this)?e&&(i||o||r||s)&&eL(this)&&this.hidePopover():this.showPopover(),"toggle"===t.type&&ex(this,P,"m",R).call(this,"open"===t.newState)}attributeChangedCallback(t){this.isConnected&&("id"===t||"data-type"===t?ex(this,P,"m",W).call(this):"data-direction"===t&&ex(this,P,"m",q).call(this))}};O=new WeakMap,D=new WeakMap,j=new WeakMap,H=new WeakMap,P=new WeakSet,R=function(t){t?(eP.add(this),this.classList.remove(eT),eM(this),ex(this,P,"m",$).call(this)):(eP.delete(this),this.classList.remove(...eC),this.classList.add(eT))},W=function(){if(this.id&&this.control){if("label"===this.type){let t=this.control.getAttribute("aria-labelledby");t=t?t.split(" ").includes(this.id)?`${t}`:`${t} ${this.id}`:this.id,this.control.setAttribute("aria-labelledby",t),this.setAttribute("aria-hidden","true")}else{let t=this.control.getAttribute("aria-describedby");t=t?t.split(" ").includes(this.id)?`${t}`:`${t} ${this.id}`:this.id,this.control.setAttribute("aria-describedby",t)}}},q=function(){this.classList.remove(...eC);let t=this.direction;"n"===t?(ek(this,D,"center","f"),ek(this,j,"outside-top","f")):"ne"===t?(ek(this,D,"start","f"),ek(this,j,"outside-top","f")):"e"===t?(ek(this,D,"center","f"),ek(this,j,"outside-right","f")):"se"===t?(ek(this,D,"start","f"),ek(this,j,"outside-bottom","f")):"s"===t?(ek(this,D,"center","f"),ek(this,j,"outside-bottom","f")):"sw"===t?(ek(this,D,"end","f"),ek(this,j,"outside-bottom","f")):"w"===t?(ek(this,D,"center","f"),ek(this,j,"outside-left","f")):"nw"===t&&(ek(this,D,"end","f"),ek(this,j,"outside-top","f"))},$=function(){if(!this.control||!ex(this,H,"f")||!eL(this))return;let t=(0,t2.N)(this,this.control,{side:ex(this,j,"f"),align:ex(this,D,"f"),anchorOffset:10}),e=t.anchorSide,n=t.anchorAlign;this.style.setProperty("--tool-tip-position-top",`${t.top}px`),this.style.setProperty("--tool-tip-position-left",`${t.left}px`);let i="s";i="outside-left"===e?"w":"outside-right"===e?"e":"outside-top"===e?"center"===n?"n":"start"===n?"ne":"nw":"center"===n?"s":"start"===n?"se":"sw",this.classList.add(`tooltip-${i}`)},ToolTipElement.observedAttributes=["data-type","data-direction","id"],window.customElements.get("tool-tip")||(window.ToolTipElement=ToolTipElement,window.customElements.define("tool-tip",ToolTipElement));var eO=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},eD=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)};let ej=class XBannerElement extends HTMLElement{constructor(){super(...arguments),B.add(this)}dismiss(){let t=this.parentElement;t&&("remove"===eD(this,B,"a",_)?t.removeChild(this):this.hide())}show(){this.style.setProperty("display","initial")}hide(){this.style.setProperty("display","none")}};B=new WeakSet,_=function(){return this.getAttribute("data-dismiss-scheme")},eO([tG.fA],ej.prototype,"titleText",void 0),ej=eO([tG.Ih],ej),window.customElements.get("x-banner")||(window.XBannerElement=ej,window.customElements.define("x-banner",ej));var eH=n(46481);function eR(t){let e=document.createElement("pre");return e.style.width="1px",e.style.height="1px",e.style.position="fixed",e.style.top="5px",e.textContent=t,e}function eW(t){if("clipboard"in navigator)return navigator.clipboard.writeText(t.textContent||"");let e=getSelection();if(null==e)return Promise.reject(Error());e.removeAllRanges();let n=document.createRange();return n.selectNodeContents(t),e.addRange(n),document.execCommand("copy"),e.removeAllRanges(),Promise.resolve()}function eq(t){if("clipboard"in navigator)return navigator.clipboard.writeText(t);let e=document.body;if(!e)return Promise.reject(Error());let n=eR(t);return e.appendChild(n),eW(n),e.removeChild(n),Promise.resolve()}async function e$(t){let e=t.getAttribute("for"),n=t.getAttribute("value");function i(){t.dispatchEvent(new CustomEvent("clipboard-copy",{bubbles:!0}))}if("true"!==t.getAttribute("aria-disabled")){if(n)await eq(n),i();else if(e){let n="getRootNode"in Element.prototype?t.getRootNode():t.ownerDocument;if(!(n instanceof Document||"ShadowRoot"in window&&n instanceof ShadowRoot))return;let o=n.getElementById(e);o&&(await eB(o),i())}}}function eB(t){return t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement?eq(t.value):t instanceof HTMLAnchorElement&&t.hasAttribute("href")?eq(t.href):eW(t)}function e_(t){let e=t.currentTarget;e instanceof HTMLElement&&e$(e)}function eF(t){if(" "===t.key||"Enter"===t.key){let e=t.currentTarget;e instanceof HTMLElement&&(t.preventDefault(),e$(e))}}function eN(t){t.currentTarget.addEventListener("keydown",eF)}function ez(t){t.currentTarget.removeEventListener("keydown",eF)}let clipboard_copy_element_ClipboardCopyElement=class clipboard_copy_element_ClipboardCopyElement extends HTMLElement{static define(t="clipboard-copy",e=customElements){return e.define(t,this),this}constructor(){super(),this.addEventListener("click",e_),this.addEventListener("focus",eN),this.addEventListener("blur",ez)}connectedCallback(){this.hasAttribute("tabindex")||this.setAttribute("tabindex","0"),this.hasAttribute("role")||this.setAttribute("role","button")}get value(){return this.getAttribute("value")||""}set value(t){this.setAttribute("value",t)}};let eK="undefined"!=typeof globalThis?globalThis:window;try{eK.ClipboardCopyElement=clipboard_copy_element_ClipboardCopyElement.define()}catch(t){if(!(eK.DOMException&&t instanceof DOMException&&"NotSupportedError"===t.name)&&!(t instanceof ReferenceError))throw t}function eV(t){t.style.display="inline-block"}function eX(t){t.style.display="none"}function eG(t){let[e,n]=t.querySelectorAll(".octicon");e&&n&&(eV(e),eX(n))}function eY(t){let[e,n]=t.querySelectorAll(".octicon");e&&n&&(eX(e),eV(n))}let eU=new WeakMap;document.addEventListener("clipboard-copy",({target:t})=>{if(!(t instanceof HTMLElement)||!t.hasAttribute("data-view-component"))return;let e=eU.get(t);e?(clearTimeout(e),eU.delete(t)):eY(t),eU.set(t,setTimeout(()=>{eG(t),eU.delete(t)},2e3))});var eJ=n(68897),eQ=n(29501),eZ=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s};let e0=class PrimerMultiInputElement extends HTMLElement{activateField(t){var e,n;let i=this.findField(t);if(i){for(let t of this.fields)t!==i&&(t.setAttribute("disabled","disabled"),t.setAttribute("hidden","hidden"),null===(e=t.parentElement)||void 0===e||e.setAttribute("hidden","hidden"));i.removeAttribute("disabled"),i.removeAttribute("hidden"),null===(n=i.parentElement)||void 0===n||n.removeAttribute("hidden")}}findField(t){for(let e of this.fields)if(e.getAttribute("data-name")===t)return e;return null}};eZ([tG.GO],e0.prototype,"fields",void 0),e0=eZ([tG.Ih],e0),window.customElements.get("primer-multi-input")||(Object.assign(window,{PrimerMultiInputElement:e0}),window.customElements.define("primer-multi-input",e0));var e1=n(38257),e3=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},e2=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)},e6=function(t,e,n,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,n):o?o.value=n:e.set(t,n),n};let e4=class PrimerTextFieldElement extends HTMLElement{constructor(){super(...arguments),F.set(this,void 0)}connectedCallback(){var t;null===(t=e2(this,F,"f"))||void 0===t||t.abort();let{signal:e}=e6(this,F,new AbortController,"f");this.inputElement.addEventListener("auto-check-success",()=>{this.clearError()},{signal:e}),this.inputElement.addEventListener("auto-check-error",async t=>{let e=await t.detail.response.text();this.setError(e)},{signal:e})}disconnectedCallback(){var t;null===(t=e2(this,F,"f"))||void 0===t||t.abort()}clearContents(){this.inputElement.value="",this.inputElement.focus()}clearError(){this.inputElement.removeAttribute("invalid"),this.validationElement.hidden=!0,this.validationMessageElement.textContent=""}setError(t){this.validationMessageElement.textContent=t,this.validationElement.hidden=!1,this.inputElement.setAttribute("invalid","true")}};F=new WeakMap,e3([tG.fA],e4.prototype,"inputElement",void 0),e3([tG.fA],e4.prototype,"validationElement",void 0),e3([tG.fA],e4.prototype,"validationMessageElement",void 0),e4=e3([tG.Ih],e4);var e5=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s};let e7=class ToggleSwitchInputElement extends HTMLElement{connectedCallback(){this.addEventListener("toggleSwitchError",t=>{this.validationMessageElement.textContent=t.detail,this.validationElement.removeAttribute("hidden")}),this.addEventListener("toggleSwitchSuccess",()=>{this.validationMessageElement.textContent="",this.validationElement.setAttribute("hidden","hidden")}),this.addEventListener("toggleSwitchLoading",()=>{this.validationMessageElement.textContent="",this.validationElement.setAttribute("hidden","hidden")})}};e5([tG.fA],e7.prototype,"validationElement",void 0),e5([tG.fA],e7.prototype,"validationMessageElement",void 0),e7=e5([tG.Ih],e7);var e9=function(t,e,n,i){var o,r=arguments.length,s=r<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,i);else for(var a=t.length-1;a>=0;a--)(o=t[a])&&(s=(r<3?o(s):r>3?o(e,n,s):o(e,n))||s);return r>3&&s&&Object.defineProperty(e,n,s),s},e8=function(t,e,n,i,o){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!o)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!o:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?o.call(t,n):o?o.value=n:e.set(t,n),n},nt=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)};let ne=['[role="menuitem"]','[role="menuitemcheckbox"]','[role="menuitemradio"]'],nn=ne.map(t=>`:not([hidden]) > ${t}`),ni=class ActionMenuElement extends HTMLElement{constructor(){super(...arguments),N.add(this),z.set(this,void 0),K.set(this,""),V.set(this,""),X.set(this,!1)}get selectVariant(){return this.getAttribute("data-select-variant")}set selectVariant(t){t?this.setAttribute("data-select-variant",t):this.removeAttribute("variant")}get dynamicLabelPrefix(){let t=this.getAttribute("data-dynamic-label-prefix");return t?`${t}:`:""}set dynamicLabelPrefix(t){this.setAttribute("data-dynamic-label",t)}get dynamicLabel(){return this.hasAttribute("data-dynamic-label")}set dynamicLabel(t){this.toggleAttribute("data-dynamic-label",t)}get popoverElement(){var t;return(null===(t=this.invokerElement)||void 0===t?void 0:t.popoverTargetElement)||null}get invokerElement(){var t;let e=null===(t=this.querySelector("[role=menu]"))||void 0===t?void 0:t.id;if(!e)return null;for(let t of this.querySelectorAll("[aria-controls]"))if(t.getAttribute("aria-controls")===e)return t;return null}get invokerLabel(){return this.invokerElement?this.invokerElement.querySelector(".Button-label"):null}get selectedItems(){let t=this.querySelectorAll("[aria-checked=true]"),e=[];for(let n of t){let t=n.querySelector(".ActionListItem-label");e.push({label:null==t?void 0:t.textContent,value:null==n?void 0:n.getAttribute("data-value"),element:n})}return e}connectedCallback(){let{signal:t}=e8(this,z,new AbortController,"f");this.addEventListener("keydown",this,{signal:t}),this.addEventListener("click",this,{signal:t}),this.addEventListener("mouseover",this,{signal:t}),this.addEventListener("focusout",this,{signal:t}),this.addEventListener("mousedown",this,{signal:t}),nt(this,N,"m",tl).call(this),nt(this,N,"m",tc).call(this),nt(this,N,"m",G).call(this),this.includeFragment&&this.includeFragment.addEventListener("include-fragment-replaced",this,{signal:t})}disconnectedCallback(){nt(this,z,"f").abort()}handleEvent(t){var e,n,i;let o=null===(e=this.invokerElement)||void 0===e?void 0:e.contains(t.target),r=nt(this,N,"m",Q).call(this,t);if(o&&"mousedown"===t.type){e8(this,X,!0,"f");return}if("mousedown"===t.type){t.preventDefault();return}if(o&&r){nt(this,N,"m",Z).call(this,t),e8(this,X,!1,"f");return}if((null===(n=t.target)||void 0===n?void 0:n.closest("dialog"))||(null===(i=t.target)||void 0===i?void 0:i.closest("modal-dialog")))return;if("focusout"===t.type){if(nt(this,X,"f"))return;requestAnimationFrame(()=>{this.contains(document.activeElement)&&document.activeElement!==this.invokerElement||nt(this,N,"m",to).call(this)});return}let s=t.target.closest(nn.join(","));if(null!==s&&r){let e=s.closest("[data-show-dialog-id]");if(e){let n=this.ownerDocument.getElementById(e.getAttribute("data-show-dialog-id")||"");if(n&&this.contains(e)&&this.contains(n)){nt(this,N,"m",tt).call(this,t,n);return}}nt(this,N,"m",tn).call(this,t,s),nt(this,N,"m",te).call(this,t,s);return}"include-fragment-replaced"===t.type&&nt(this,N,"m",ti).call(this)}};z=new WeakMap,K=new WeakMap,V=new WeakMap,X=new WeakMap,N=new WeakSet,G=function(){let{signal:t}=nt(this,z,"f");for(let e of nt(this,N,"a",td))e.addEventListener("click",nt(this,N,"m",Y).bind(this),{signal:t}),e.addEventListener("keydown",nt(this,N,"m",Y).bind(this),{signal:t})},Y=function(t){if(!nt(this,N,"m",Q).call(this,t))return;let e=t.target.closest(nn.join(","));e&&e.getAttribute("aria-disabled")&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation())},U=function(t){return t instanceof KeyboardEvent&&"keydown"===t.type&&!(t.ctrlKey||t.altKey||t.metaKey||t.shiftKey)&&("Enter"===t.key||" "===t.key)},J=function(t){return t instanceof MouseEvent&&"click"===t.type},Q=function(t){return nt(this,N,"m",J).call(this,t)||nt(this,N,"m",U).call(this,t)},Z=function(t){var e;t.preventDefault(),t.stopPropagation(),nt(this,N,"m",ta).call(this)?nt(this,N,"m",ts).call(this):(nt(this,N,"m",tr).call(this),null===(e=nt(this,N,"a",tu))||void 0===e||e.focus())},tt=function(t,e){this.querySelector(".ActionListWrap").style.display="none";let n=new AbortController,{signal:i}=n,o=()=>{n.abort(),this.querySelector(".ActionListWrap").style.display="",nt(this,N,"m",ta).call(this)&&nt(this,N,"m",ts).call(this)};e.addEventListener("close",o,{signal:i}),e.addEventListener("cancel",o,{signal:i})},te=function(t,e){if("multiple"!==this.selectVariant&&setTimeout(()=>{nt(this,N,"m",ta).call(this)&&nt(this,N,"m",ts).call(this)}),"multiple"!==this.selectVariant&&"single"!==this.selectVariant)return;let n=e.getAttribute("aria-checked"),i="true"!==n;if("single"===this.selectVariant){for(let t of(i&&e.setAttribute("aria-checked","true"),this.querySelectorAll("[aria-checked]")))t!==e&&t.setAttribute("aria-checked","false");nt(this,N,"m",tl).call(this)}else e.setAttribute("aria-checked",`${i}`);if(nt(this,N,"m",tc).call(this),t instanceof KeyboardEvent&&t.target instanceof HTMLButtonElement){t.preventDefault();return}},tn=function(t,e){let n=t instanceof MouseEvent&&"click"===t.type||t instanceof KeyboardEvent&&"keydown"===t.type&&!(t.ctrlKey||t.altKey||t.metaKey||t.shiftKey)&&"Enter"===t.key;n||(t.stopPropagation(),e.click())},ti=function(){nt(this,N,"a",tu)&&nt(this,N,"a",tu).focus(),nt(this,N,"m",G).call(this)},to=function(){nt(this,N,"m",ts).call(this)},tr=function(){var t;null===(t=this.popoverElement)||void 0===t||t.showPopover()},ts=function(){var t;null===(t=this.popoverElement)||void 0===t||t.hidePopover()},ta=function(){var t;return null===(t=this.popoverElement)||void 0===t?void 0:t.matches(":popover-open")},tl=function(){if(!this.dynamicLabel)return;let t=this.invokerLabel;if(!t)return;e8(this,K,nt(this,K,"f")||t.textContent||"","f");let e=this.querySelector("[aria-checked=true] .ActionListItem-label");if(e&&this.dynamicLabel){let n=document.createElement("span");n.classList.add("color-fg-muted");let i=document.createElement("span");n.textContent=this.dynamicLabelPrefix,i.textContent=e.textContent||"",t.replaceChildren(n,i)}else t.textContent=nt(this,K,"f")},tc=function(){if("single"===this.selectVariant){let t=this.querySelector("[data-list-inputs=true] input");if(!t)return;let e=this.selectedItems[0];e?(t.value=(e.value||e.label||"").trim(),t.removeAttribute("disabled")):t.setAttribute("disabled","disabled")}else if("none"!==this.selectVariant){let t=this.querySelector("[data-list-inputs=true]");if(!t)return;let e=t.querySelectorAll("input");for(let n of(e.length>0&&e8(this,V,nt(this,V,"f")||e[0].name,"f"),this.selectedItems)){let e=document.createElement("input");e.setAttribute("data-list-input","true"),e.type="hidden",e.autocomplete="off",e.name=nt(this,V,"f"),e.value=(n.value||n.label||"").trim(),t.append(e)}for(let t of e)t.remove()}},tu=function(){return this.querySelector(nn.join(","))},td=function(){return Array.from(this.querySelectorAll(nn.join(",")))},e9([tG.fA],ni.prototype,"includeFragment",void 0),ni=e9([tG.Ih],ni),window.customElements.get("action-menu")||(window.ActionMenuElement=ni,window.customElements.define("action-menu",ni))},38257:()=>{function t(t,e=0,{start:n=!0,middle:i=!0,once:o=!1}={}){let r,s=0,a=!1;function l(...c){if(a)return;let u=Date.now()-s;s=Date.now(),n?(n=!1,t.apply(this,c),o&&l.cancel()):(i&&u<e||!i)&&(clearTimeout(r),r=setTimeout(()=>{s=Date.now(),t.apply(this,c),o&&l.cancel()},i?e-u:e))}return l.cancel=()=>{clearTimeout(r),a=!0},l}function e(e,n=0,{start:i=!1,middle:o=!1,once:r=!1}={}){return t(e,n,{start:i,middle:o,once:r})}let n=new WeakMap;let AutoCheckElement=class AutoCheckElement extends HTMLElement{connectedCallback(){let t=this.input;if(!t)return;let o=e(s.bind(null,this),300);n.set(this,{check:o,controller:null}),t.addEventListener("input",i),t.addEventListener("input",o),t.autocomplete="off",t.spellcheck=!1}disconnectedCallback(){let t=this.input;if(!t)return;let e=n.get(this);e&&(n.delete(this),t.removeEventListener("input",i),t.removeEventListener("input",e.check),t.setCustomValidity(""))}attributeChangedCallback(t){if("required"===t){let t=this.input;t&&(t.required=this.required)}}static get observedAttributes(){return["required"]}get input(){return this.querySelector("input")}get src(){let t=this.getAttribute("src");if(!t)return"";let e=this.ownerDocument.createElement("a");return e.href=t,e.href}set src(t){this.setAttribute("src",t)}get csrf(){let t=this.querySelector("[data-csrf]");return this.getAttribute("csrf")||t instanceof HTMLInputElement&&t.value||""}set csrf(t){this.setAttribute("csrf",t)}get required(){return this.hasAttribute("required")}set required(t){t?this.setAttribute("required",""):this.removeAttribute("required")}get csrfField(){return this.getAttribute("csrf-field")||"authenticity_token"}set csrfField(t){this.setAttribute("csrf-field",t)}};function i(t){let e=t.currentTarget;if(!(e instanceof HTMLInputElement))return;let i=e.closest("auto-check");if(!(i instanceof AutoCheckElement))return;let o=i.src,r=i.csrf,s=n.get(i);if(!o||!r||!s)return;let a="Verifying\u2026",l=t=>a=t;e.dispatchEvent(new CustomEvent("auto-check-start",{bubbles:!0,detail:{setValidity:l}})),i.required&&e.setCustomValidity(a)}function o(){return"AbortController"in window?new AbortController:{signal:null,abort(){}}}async function r(t,e,n){try{let i=await fetch(e,n);return t.dispatchEvent(new CustomEvent("load")),t.dispatchEvent(new CustomEvent("loadend")),i}catch(e){throw"AbortError"!==e.name&&(t.dispatchEvent(new CustomEvent("error")),t.dispatchEvent(new CustomEvent("loadend"))),e}}async function s(t){let e=t.input;if(!e)return;let i=t.csrfField,s=t.src,c=t.csrf,u=n.get(t);if(!s||!c||!u||!e.value.trim()){t.required&&e.setCustomValidity("");return}let d=new FormData;d.append(i,c),d.append("value",e.value),e.dispatchEvent(new CustomEvent("auto-check-send",{bubbles:!0,detail:{body:d}})),u.controller?u.controller.abort():t.dispatchEvent(new CustomEvent("loadstart")),u.controller=o();try{let n=await r(t,s,{credentials:"same-origin",signal:u.controller.signal,method:"POST",body:d});n.ok?a(n,e,t.required):l(n,e,t.required),u.controller=null,e.dispatchEvent(new CustomEvent("auto-check-complete",{bubbles:!0}))}catch(t){"AbortError"!==t.name&&(u.controller=null,e.dispatchEvent(new CustomEvent("auto-check-complete",{bubbles:!0})))}}function a(t,e,n){n&&e.setCustomValidity(""),e.dispatchEvent(new CustomEvent("auto-check-success",{bubbles:!0,detail:{response:t.clone()}}))}function l(t,e,n){let i="Validation failed",o=t=>i=t;e.dispatchEvent(new CustomEvent("auto-check-error",{bubbles:!0,detail:{response:t.clone(),setValidity:o}})),n&&e.setCustomValidity(i)}window.customElements.get("auto-check")||(window.AutoCheckElement=AutoCheckElement,window.customElements.define("auto-check",AutoCheckElement));var c=null},76006:(t,e,n)=>{let i;n.d(e,{Lj:()=>y,Ih:()=>M,P4:()=>f,nW:()=>R,fA:()=>T,GO:()=>C});let o=new WeakSet;function r(t){o.add(t),t.shadowRoot&&s(t.shadowRoot),c(t),l(t.ownerDocument)}function s(t){c(t),l(t)}let a=new WeakMap;function l(t=document){if(a.has(t))return a.get(t);let e=!1,n=new MutationObserver(t=>{for(let e of t)if("attributes"===e.type&&e.target instanceof Element)h(e.target);else if("childList"===e.type&&e.addedNodes.length)for(let t of e.addedNodes)t instanceof Element&&c(t)});n.observe(t,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let i={get closed(){return e},unsubscribe(){e=!0,a.delete(t),n.disconnect()}};return a.set(t,i),i}function c(t){for(let e of t.querySelectorAll("[data-action]"))h(e);t instanceof Element&&t.hasAttribute("data-action")&&h(t)}function u(t){let e=t.currentTarget;for(let n of d(e))if(t.type===n.type){let i=e.closest(n.tag);o.has(i)&&"function"==typeof i[n.method]&&i[n.method](t);let r=e.getRootNode();if(r instanceof ShadowRoot&&o.has(r.host)&&r.host.matches(n.tag)){let e=r.host;"function"==typeof e[n.method]&&e[n.method](t)}}}function*d(t){for(let e of(t.getAttribute("data-action")||"").trim().split(/\s+/)){let t=e.lastIndexOf(":"),n=Math.max(0,e.lastIndexOf("#"))||e.length;yield{type:e.slice(0,t),tag:e.slice(t+1,n),method:e.slice(n+1)||"handleEvent"}}}function h(t){for(let e of d(t))t.addEventListener(e.type,u)}function f(t,e){let n=t.tagName.toLowerCase();if(t.shadowRoot){for(let i of t.shadowRoot.querySelectorAll(`[data-target~="${n}.${e}"]`))if(!i.closest(n))return i}for(let i of t.querySelectorAll(`[data-target~="${n}.${e}"]`))if(i.closest(n)===t)return i}function p(t,e){let n=t.tagName.toLowerCase(),i=[];if(t.shadowRoot)for(let o of t.shadowRoot.querySelectorAll(`[data-targets~="${n}.${e}"]`))o.closest(n)||i.push(o);for(let o of t.querySelectorAll(`[data-targets~="${n}.${e}"]`))o.closest(n)===t&&i.push(o);return i}let m=t=>String("symbol"==typeof t?t.description:t).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),g=(t,e="property")=>{let n=m(t);if(!n.includes("-"))throw new DOMException(`${e}: ${String(t)} is not a valid ${e} name`,"SyntaxError");return n};function b(t){let e=m(t.name).replace(/-element$/,"");try{window.customElements.define(e,t),window[t.name]=customElements.get(e)}catch(t){if(!(t instanceof DOMException&&"NotSupportedError"===t.name))throw t}return t}function v(t){for(let e of t.querySelectorAll("template[data-shadowroot]"))e.parentElement===t&&t.attachShadow({mode:"closed"===e.getAttribute("data-shadowroot")?"closed":"open"}).append(e.content.cloneNode(!0))}let w="attr";function y(t,e){L(t,w).add(e)}let E=new WeakSet;function A(t,e){if(E.has(t))return;E.add(t);let n=Object.getPrototypeOf(t),i=n?.constructor?.attrPrefix??"data-";for(let o of(e||(e=L(n,w)),e)){let e=t[o],n=g(`${i}${o}`),r={configurable:!0,get(){return this.getAttribute(n)||""},set(t){this.setAttribute(n,t||"")}};"number"==typeof e?r={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(t){this.setAttribute(n,t)}}:"boolean"==typeof e&&(r={configurable:!0,get(){return this.hasAttribute(n)},set(t){this.toggleAttribute(n,t)}}),Object.defineProperty(t,o,r),o in t&&!t.hasAttribute(n)&&r.set.call(t,e)}}function x(t){let e=t.observedAttributes||[],n=t.attrPrefix??"data-",i=t=>g(`${n}${t}`);Object.defineProperty(t,"observedAttributes",{configurable:!0,get:()=>[...L(t.prototype,w)].map(i).concat(e),set(t){e=t}})}let k=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(t){let e=this,n=t.prototype.connectedCallback;t.prototype.connectedCallback=function(){e.connectedCallback(this,n)};let i=t.prototype.disconnectedCallback;t.prototype.disconnectedCallback=function(){e.disconnectedCallback(this,i)};let o=t.prototype.attributeChangedCallback;t.prototype.attributeChangedCallback=function(t,n,i){e.attributeChangedCallback(this,t,n,i,o)};let r=t.observedAttributes||[];Object.defineProperty(t,"observedAttributes",{configurable:!0,get(){return e.observedAttributes(this,r)},set(t){r=t}}),x(t),b(t)}observedAttributes(t,e){return e}connectedCallback(t,e){t.toggleAttribute("data-catalyst",!0),customElements.upgrade(t),v(t),A(t),r(t),e?.call(t),t.shadowRoot&&s(t.shadowRoot)}disconnectedCallback(t,e){e?.call(t)}attributeChangedCallback(t,e,n,i,o){A(t),"data-catalyst"!==e&&o&&o.call(t,e,n,i)}};function L(t,e){if(!Object.prototype.hasOwnProperty.call(t,k)){let e=t[k],n=t[k]=new Map;if(e)for(let[t,i]of e)n.set(t,new Set(i))}let n=t[k];return n.has(e)||n.set(e,new Set),n.get(e)}function T(t,e){L(t,"target").add(e),Object.defineProperty(t,e,{configurable:!0,get(){return f(this,e)}})}function C(t,e){L(t,"targets").add(e),Object.defineProperty(t,e,{configurable:!0,get(){return p(this,e)}})}function M(t){new CatalystDelegate(t)}let S=new Map,I=new Promise(t=>{"loading"!==document.readyState?t():document.addEventListener("readystatechange",()=>t(),{once:!0})}),P=new Promise(t=>{let e=new AbortController;e.signal.addEventListener("abort",()=>t());let n={once:!0,passive:!0,signal:e.signal},i=()=>e.abort();document.addEventListener("mousedown",i,n),document.addEventListener("touchstart",i,n),document.addEventListener("keydown",i,n),document.addEventListener("pointerdown",i,n)}),O=t=>new Promise(e=>{let n=new IntersectionObserver(t=>{for(let i of t)if(i.isIntersecting){e(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let e of document.querySelectorAll(t))n.observe(e)}),D={ready:()=>I,firstInteraction:()=>P,visible:O},j=new WeakMap;function H(t){cancelAnimationFrame(j.get(t)||0),j.set(t,requestAnimationFrame(()=>{for(let e of S.keys()){let n=t.matches(e)?t:t.querySelector(e);if(customElements.get(e)||n){let i=n?.getAttribute("data-load-on")||"ready",o=i in D?D[i]:D.ready;for(let t of S.get(e)||[])o(e).then(t);S.delete(e),j.delete(t)}}}))}function R(t,e){S.has(t)||S.set(t,new Set),S.get(t).add(e),H(document.body),i||(i=new MutationObserver(t=>{if(S.size)for(let e of t)for(let t of e.addedNodes)t instanceof Element&&H(t)})).observe(document,{subtree:!0,childList:!0})}},11095:()=>{let DetailsMenuElement=class DetailsMenuElement extends HTMLElement{get preload(){return this.hasAttribute("preload")}set preload(t){t?this.setAttribute("preload",""):this.removeAttribute("preload")}get src(){return this.getAttribute("src")||""}set src(t){this.setAttribute("src",t)}connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","menu");let s=this.parentElement;if(!s)return;let a=s.querySelector("summary");a&&(a.setAttribute("aria-haspopup","menu"),a.hasAttribute("role")||a.setAttribute("role","button"));let l=[n(s,"compositionstart",t=>w(this,t)),n(s,"compositionend",t=>w(this,t)),n(s,"click",t=>u(s,t)),n(s,"change",t=>u(s,t)),n(s,"keydown",t=>f(s,this,t)),n(s,"toggle",()=>i(s,this),{once:!0}),n(s,"toggle",()=>r(s)),this.preload?n(s,"mouseover",()=>i(s,this),{once:!0}):e,...o(s)];t.set(this,{subscriptions:l,loaded:!1,isComposing:!1})}disconnectedCallback(){let e=t.get(this);if(e)for(let n of(t.delete(this),e.subscriptions))n.unsubscribe()}};let t=new WeakMap,e={unsubscribe(){}};function n(t,e,n,i=!1){return t.addEventListener(e,n,i),{unsubscribe:()=>{t.removeEventListener(e,n,i)}}}function i(e,n){let i=n.getAttribute("src");if(!i)return;let o=t.get(n);if(!o||o.loaded)return;o.loaded=!0;let r=n.querySelector("include-fragment");r&&!r.hasAttribute("src")&&(r.addEventListener("loadend",()=>s(e)),r.setAttribute("src",i))}function o(t){let e=!1,i=()=>e=!0,o=()=>e=!1,r=()=>{t.hasAttribute("open")&&(s(t)||e||a(t))};return[n(t,"mousedown",i),n(t,"keydown",o),n(t,"toggle",r)]}function r(t){if(t.hasAttribute("open"))for(let e of document.querySelectorAll("details[open] > details-menu")){let n=e.closest("details");n&&n!==t&&!n.contains(t)&&n.removeAttribute("open")}}function s(t){if(!t.hasAttribute("open"))return!1;let e=t.querySelector("details-menu [autofocus]");return!!e&&(e.focus(),!0)}function a(t){let e=document.activeElement;if(e&&p(e)&&t.contains(e))return;let n=l(t,!0);n&&n.focus()}function l(t,e){let n=Array.from(t.querySelectorAll('[role^="menuitem"]:not([hidden]):not([disabled])')),i=document.activeElement,o=i instanceof HTMLElement?n.indexOf(i):-1,r=e?n[o+1]:n[o-1],s=e?n[0]:n[n.length-1];return r||s}let c=navigator.userAgent.match(/Macintosh/);function u(t,e){let n=e.target;if(n instanceof Element&&n.closest("details")===t){if("click"===e.type){let e=n.closest('[role="menuitem"], [role="menuitemradio"]');if(!e)return;let i=e.querySelector("input");if("LABEL"===e.tagName&&n===i)return;let o="LABEL"===e.tagName&&i&&!i.checked;o||h(e,t)}else if("change"===e.type){let e=n.closest('[role="menuitemradio"], [role="menuitemcheckbox"]');e&&h(e,t)}}}function d(t,e){for(let n of e.querySelectorAll('[role="menuitemradio"], [role="menuitemcheckbox"]')){let e=n.querySelector('input[type="radio"], input[type="checkbox"]'),i=(n===t).toString();e instanceof HTMLInputElement&&(i=e.indeterminate?"mixed":e.checked.toString()),n.setAttribute("aria-checked",i)}}function h(t,e){if(t.hasAttribute("disabled")||"true"===t.getAttribute("aria-disabled"))return;let n=t.closest("details-menu");if(!n)return;let i=n.dispatchEvent(new CustomEvent("details-menu-select",{cancelable:!0,detail:{relatedTarget:t}}));i&&(g(t,e),d(t,e),"menuitemcheckbox"!==t.getAttribute("role")&&m(e),n.dispatchEvent(new CustomEvent("details-menu-selected",{detail:{relatedTarget:t}})))}function f(e,n,i){if(!(i instanceof KeyboardEvent)||e.querySelector("details[open]"))return;let o=t.get(n);if(!o||o.isComposing)return;let r=i.target instanceof Element&&"SUMMARY"===i.target.tagName;switch(i.key){case"Escape":e.hasAttribute("open")&&(m(e),i.preventDefault(),i.stopPropagation());break;case"ArrowDown":{r&&!e.hasAttribute("open")&&e.setAttribute("open","");let t=l(e,!0);t&&t.focus(),i.preventDefault()}break;case"ArrowUp":{r&&!e.hasAttribute("open")&&e.setAttribute("open","");let t=l(e,!1);t&&t.focus(),i.preventDefault()}break;case"n":if(c&&i.ctrlKey){let t=l(e,!0);t&&t.focus(),i.preventDefault()}break;case"p":if(c&&i.ctrlKey){let t=l(e,!1);t&&t.focus(),i.preventDefault()}break;case" ":case"Enter":{let t=document.activeElement;t instanceof HTMLElement&&p(t)&&t.closest("details")===e&&(i.preventDefault(),i.stopPropagation(),t.click())}}}function p(t){let e=t.getAttribute("role");return"menuitem"===e||"menuitemcheckbox"===e||"menuitemradio"===e}function m(t){let e=t.hasAttribute("open");if(!e)return;t.removeAttribute("open");let n=t.querySelector("summary");n&&n.focus()}function g(t,e){let n=e.querySelector("[data-menu-button]");if(!n)return;let i=b(t);if(i)n.textContent=i;else{let e=v(t);e&&(n.innerHTML=e)}}function b(t){if(!t)return null;let e=t.hasAttribute("data-menu-button-text")?t:t.querySelector("[data-menu-button-text]");return e?e.getAttribute("data-menu-button-text")||e.textContent:null}function v(t){if(!t)return null;let e=t.hasAttribute("data-menu-button-contents")?t:t.querySelector("[data-menu-button-contents]");return e?e.innerHTML:null}function w(e,n){let i=t.get(e);i&&(i.isComposing="compositionstart"===n.type)}var y=null;window.customElements.get("details-menu")||(window.DetailsMenuElement=DetailsMenuElement,window.customElements.define("details-menu",DetailsMenuElement))},73921:()=>{function t(){let t=/\bWindows NT 6.1\b/.test(navigator.userAgent),e=/\bWindows NT 6.2\b/.test(navigator.userAgent),n=/\bWindows NT 6.3\b/.test(navigator.userAgent),i=/\bFreeBSD\b/.test(navigator.userAgent),o=/\bLinux\b/.test(navigator.userAgent)&&!/\bAndroid\b/.test(navigator.userAgent);return!(t||e||n||o||i)}let e=new Set(["\u{1F44B}","\u{1F91A}","\u{1F590}\uFE0F","\u270B","\u{1F596}","\u{1F44C}","\u{1F90F}","\u270C\uFE0F","\u{1F91E}","\u{1F91F}","\u{1F918}","\u{1F919}","\u{1F448}","\u{1F449}","\u{1F446}","\u{1F595}","\u{1F447}","\u261D\uFE0F","\u{1F44D}","\u{1F44E}","\u270A","\u{1F44A}","\u{1F91B}","\u{1F91C}","\u{1F44F}","\u{1F64C}","\u{1F450}","\u{1F932}","\u{1F64F}","\u270D\uFE0F","\u{1F485}","\u{1F933}","\u{1F4AA}","\u{1F9B5}","\u{1F9B6}","\u{1F442}","\u{1F9BB}","\u{1F443}","\u{1F476}","\u{1F9D2}","\u{1F466}","\u{1F467}","\u{1F9D1}","\u{1F471}","\u{1F468}","\u{1F9D4}","\u{1F471}\u200D\u2642\uFE0F","\u{1F468}\u200D\u{1F9B0}","\u{1F468}\u200D\u{1F9B1}","\u{1F468}\u200D\u{1F9B3}","\u{1F468}\u200D\u{1F9B2}","\u{1F469}","\u{1F471}\u200D\u2640\uFE0F","\u{1F469}\u200D\u{1F9B0}","\u{1F469}\u200D\u{1F9B1}","\u{1F469}\u200D\u{1F9B3}","\u{1F469}\u200D\u{1F9B2}","\u{1F9D3}","\u{1F474}","\u{1F475}","\u{1F64D}","\u{1F64D}\u200D\u2642\uFE0F","\u{1F64D}\u200D\u2640\uFE0F","\u{1F64E}","\u{1F64E}\u200D\u2642\uFE0F","\u{1F64E}\u200D\u2640\uFE0F","\u{1F645}","\u{1F645}\u200D\u2642\uFE0F","\u{1F645}\u200D\u2640\uFE0F","\u{1F646}","\u{1F646}\u200D\u2642\uFE0F","\u{1F646}\u200D\u2640\uFE0F","\u{1F481}","\u{1F481}\u200D\u2642\uFE0F","\u{1F481}\u200D\u2640\uFE0F","\u{1F64B}","\u{1F64B}\u200D\u2642\uFE0F","\u{1F64B}\u200D\u2640\uFE0F","\u{1F9CF}","\u{1F9CF}\u200D\u2642\uFE0F","\u{1F9CF}\u200D\u2640\uFE0F","\u{1F647}","\u{1F647}\u200D\u2642\uFE0F","\u{1F647}\u200D\u2640\uFE0F","\u{1F926}","\u{1F926}\u200D\u2642\uFE0F","\u{1F926}\u200D\u2640\uFE0F","\u{1F937}","\u{1F937}\u200D\u2642\uFE0F","\u{1F937}\u200D\u2640\uFE0F","\u{1F468}\u200D\u2695\uFE0F","\u{1F469}\u200D\u2695\uFE0F","\u{1F468}\u200D\u{1F393}","\u{1F469}\u200D\u{1F393}","\u{1F468}\u200D\u{1F3EB}","\u{1F469}\u200D\u{1F3EB}","\u{1F468}\u200D\u2696\uFE0F","\u{1F469}\u200D\u2696\uFE0F","\u{1F468}\u200D\u{1F33E}","\u{1F469}\u200D\u{1F33E}","\u{1F468}\u200D\u{1F373}","\u{1F469}\u200D\u{1F373}","\u{1F468}\u200D\u{1F527}","\u{1F469}\u200D\u{1F527}","\u{1F468}\u200D\u{1F3ED}","\u{1F469}\u200D\u{1F3ED}","\u{1F468}\u200D\u{1F4BC}","\u{1F469}\u200D\u{1F4BC}","\u{1F468}\u200D\u{1F52C}","\u{1F469}\u200D\u{1F52C}","\u{1F468}\u200D\u{1F4BB}","\u{1F469}\u200D\u{1F4BB}","\u{1F468}\u200D\u{1F3A4}","\u{1F469}\u200D\u{1F3A4}","\u{1F468}\u200D\u{1F3A8}","\u{1F469}\u200D\u{1F3A8}","\u{1F468}\u200D\u2708\uFE0F","\u{1F469}\u200D\u2708\uFE0F","\u{1F468}\u200D\u{1F680}","\u{1F469}\u200D\u{1F680}","\u{1F468}\u200D\u{1F692}","\u{1F469}\u200D\u{1F692}","\u{1F46E}","\u{1F46E}\u200D\u2642\uFE0F","\u{1F46E}\u200D\u2640\uFE0F","\u{1F575}\uFE0F","\u{1F575}\uFE0F\u200D\u2642\uFE0F","\u{1F575}\uFE0F\u200D\u2640\uFE0F","\u{1F482}","\u{1F482}\u200D\u2642\uFE0F","\u{1F482}\u200D\u2640\uFE0F","\u{1F477}","\u{1F477}\u200D\u2642\uFE0F","\u{1F477}\u200D\u2640\uFE0F","\u{1F934}","\u{1F478}","\u{1F473}","\u{1F473}\u200D\u2642\uFE0F","\u{1F473}\u200D\u2640\uFE0F","\u{1F472}","\u{1F9D5}","\u{1F935}","\u{1F470}","\u{1F930}","\u{1F931}","\u{1F47C}","\u{1F385}","\u{1F936}","\u{1F9B8}","\u{1F9B8}\u200D\u2642\uFE0F","\u{1F9B8}\u200D\u2640\uFE0F","\u{1F9B9}","\u{1F9B9}\u200D\u2642\uFE0F","\u{1F9B9}\u200D\u2640\uFE0F","\u{1F9D9}","\u{1F9D9}\u200D\u2642\uFE0F","\u{1F9D9}\u200D\u2640\uFE0F","\u{1F9DA}","\u{1F9DA}\u200D\u2642\uFE0F","\u{1F9DA}\u200D\u2640\uFE0F","\u{1F9DB}","\u{1F9DB}\u200D\u2642\uFE0F","\u{1F9DB}\u200D\u2640\uFE0F","\u{1F9DC}","\u{1F9DC}\u200D\u2642\uFE0F","\u{1F9DC}\u200D\u2640\uFE0F","\u{1F9DD}","\u{1F9DD}\u200D\u2642\uFE0F","\u{1F9DD}\u200D\u2640\uFE0F","\u{1F486}","\u{1F486}\u200D\u2642\uFE0F","\u{1F486}\u200D\u2640\uFE0F","\u{1F487}","\u{1F487}\u200D\u2642\uFE0F","\u{1F487}\u200D\u2640\uFE0F","\u{1F6B6}","\u{1F6B6}\u200D\u2642\uFE0F","\u{1F6B6}\u200D\u2640\uFE0F","\u{1F9CD}","\u{1F9CD}\u200D\u2642\uFE0F","\u{1F9CD}\u200D\u2640\uFE0F","\u{1F9CE}","\u{1F9CE}\u200D\u2642\uFE0F","\u{1F9CE}\u200D\u2640\uFE0F","\u{1F468}\u200D\u{1F9AF}","\u{1F469}\u200D\u{1F9AF}","\u{1F468}\u200D\u{1F9BC}","\u{1F469}\u200D\u{1F9BC}","\u{1F468}\u200D\u{1F9BD}","\u{1F469}\u200D\u{1F9BD}","\u{1F3C3}","\u{1F3C3}\u200D\u2642\uFE0F","\u{1F3C3}\u200D\u2640\uFE0F","\u{1F483}","\u{1F57A}","\u{1F574}\uFE0F","\u{1F9D6}","\u{1F9D6}\u200D\u2642\uFE0F","\u{1F9D6}\u200D\u2640\uFE0F","\u{1F9D7}","\u{1F9D7}\u200D\u2642\uFE0F","\u{1F9D7}\u200D\u2640\uFE0F","\u{1F3C7}","\u{1F3C2}","\u{1F3CC}\uFE0F","\u{1F3CC}\uFE0F\u200D\u2642\uFE0F","\u{1F3CC}\uFE0F\u200D\u2640\uFE0F","\u{1F3C4}","\u{1F3C4}\u200D\u2642\uFE0F","\u{1F3C4}\u200D\u2640\uFE0F","\u{1F6A3}","\u{1F6A3}\u200D\u2642\uFE0F","\u{1F6A3}\u200D\u2640\uFE0F","\u{1F3CA}","\u{1F3CA}\u200D\u2642\uFE0F","\u{1F3CA}\u200D\u2640\uFE0F","\u26F9\uFE0F","\u26F9\uFE0F\u200D\u2642\uFE0F","\u26F9\uFE0F\u200D\u2640\uFE0F","\u{1F3CB}\uFE0F","\u{1F3CB}\uFE0F\u200D\u2642\uFE0F","\u{1F3CB}\uFE0F\u200D\u2640\uFE0F","\u{1F6B4}","\u{1F6B4}\u200D\u2642\uFE0F","\u{1F6B4}\u200D\u2640\uFE0F","\u{1F6B5}","\u{1F6B5}\u200D\u2642\uFE0F","\u{1F6B5}\u200D\u2640\uFE0F","\u{1F938}","\u{1F938}\u200D\u2642\uFE0F","\u{1F938}\u200D\u2640\uFE0F","\u{1F93D}","\u{1F93D}\u200D\u2642\uFE0F","\u{1F93D}\u200D\u2640\uFE0F","\u{1F93E}","\u{1F93E}\u200D\u2642\uFE0F","\u{1F93E}\u200D\u2640\uFE0F","\u{1F939}","\u{1F939}\u200D\u2642\uFE0F","\u{1F939}\u200D\u2640\uFE0F","\u{1F9D8}","\u{1F9D8}\u200D\u2642\uFE0F","\u{1F9D8}\u200D\u2640\uFE0F","\u{1F6C0}","\u{1F6CC}","\u{1F9D1}\u200D\u{1F91D}\u200D\u{1F9D1}","\u{1F46D}","\u{1F46B}","\u{1F46C}"]);function n(t){return e.has(t)}function i(t,e){let i=r(t);if(!n(i))return t;let o=l(e);return o?i.split("\u200D").map(t=>n(t)?s(t,o):t).join("\u200D"):t}function o(t,e){let i=r(t);if(!n(i))return t;let o=e.map(t=>l(t));return i.split("\u200D").map(t=>{if(!n(t))return t;let e=o.shift();return e?s(t,e):t}).join("\u200D")}function r(t){return[...t].filter(t=>!a(t.codePointAt(0))).join("")}function s(t,e){let n=[...t].map(t=>t.codePointAt(0));return n[1]&&(a(n[1])||65039===n[1])?n[1]=e:n.splice(1,0,e),String.fromCodePoint(...n)}function a(t){return t>=127995&&t<=127999}function l(t){switch(t){case 1:return 127995;case 2:return 127996;case 3:return 127997;case 4:return 127998;case 5:return 127999;default:return null}}let GEmojiElement=class GEmojiElement extends HTMLElement{get image(){return this.firstElementChild instanceof HTMLImageElement?this.firstElementChild:null}get tone(){return(this.getAttribute("tone")||"").split(" ").map(t=>{let e=parseInt(t,10);return e>=0&&e<=5?e:0}).join(" ")}set tone(t){this.setAttribute("tone",t)}connectedCallback(){if(null===this.image&&!t()){let t=this.getAttribute("fallback-src");if(t){this.textContent="";let e=u(this);e.src=t,this.appendChild(e)}}this.hasAttribute("tone")&&c(this)}static get observedAttributes(){return["tone"]}attributeChangedCallback(t){"tone"===t&&c(this)}};function c(t){if(t.image)return;let e=t.tone.split(" ").map(t=>parseInt(t,10));if(0===e.length)t.textContent=r(t.textContent||"");else if(1===e.length){let n=e[0];t.textContent=0===n?r(t.textContent||""):i(t.textContent||"",n)}else t.textContent=o(t.textContent||"",e)}function u(t){let e=document.createElement("img");return e.className="emoji",e.alt=t.getAttribute("alias")||"",e.height=20,e.width=20,e}window.customElements.get("g-emoji")||(window.GEmojiElement=GEmojiElement,window.customElements.define("g-emoji",GEmojiElement));var d=null},86058:(t,e,n)=>{function i(){let t;try{t=window.top.document.referrer}catch(e){if(window.parent)try{t=window.parent.document.referrer}catch(t){}}return""===t&&(t=document.referrer),t}function o(){try{return`${screen.width}x${screen.height}`}catch(t){return"unknown"}}function r(){let t=0,e=0;try{return"number"==typeof window.innerWidth?(e=window.innerWidth,t=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(e=document.documentElement.clientWidth,t=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(e=document.body.clientWidth,t=document.body.clientHeight),`${e}x${t}`}catch(t){return"unknown"}}function s(){return navigator.languages?navigator.languages.join(","):navigator.language||""}function a(){return{referrer:i(),user_agent:navigator.userAgent,screen_resolution:o(),browser_resolution:r(),browser_languages:s(),pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}}n.d(e,{R:()=>AnalyticsClient});var l=n(82918);let AnalyticsClient=class AnalyticsClient{constructor(t){this.options=t}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,l.b)()}createEvent(t){return{page:location.href,title:document.title,context:{...this.options.baseContext,...t}}}sendPageView(t){let e=this.createEvent(t);this.send({page_views:[e]})}sendEvent(t,e){let n={...this.createEvent(e),type:t};this.send({events:[n]})}send({page_views:t,events:e}){let n={client_id:this.clientId,page_views:t,events:e,request_context:a()},i=JSON.stringify(n);try{if(navigator.sendBeacon){navigator.sendBeacon(this.collectorUrl,i);return}}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:i,keepalive:!1})}}},88149:(t,e,n)=>{n.d(e,{n:()=>i});function i(t="ha"){let e;let n={},i=document.head.querySelectorAll(`meta[name^="${t}-"]`);for(let o of Array.from(i)){let{name:i,content:r}=o,s=i.replace(`${t}-`,"").replace(/-/g,"_");"url"===s?e=r:n[s]=r}if(!e)throw Error(`AnalyticsClient ${t}-url meta tag not found`);return{collectorUrl:e,...Object.keys(n).length>0?{baseContext:n}:{}}}},57852:(t,e,n)=>{var i=n(10160);let o=/\s|\(|\[/;function r(t,e,n,{multiWord:i,lookBackIndex:r,lastMatchPosition:s}={multiWord:!1,lookBackIndex:0,lastMatchPosition:null}){let a=t.lastIndexOf(e,n-1);if(-1===a||a<r)return;if(i){if(null!=s){if(s===a)return;a=s-e.length}let i=t[a+1];if(" "===i&&n>=a+e.length+1)return;let o=t.lastIndexOf("\n",n-1);if(o>a)return;let r=t.lastIndexOf(".",n-1);if(r>a)return}else{let e=t.lastIndexOf(" ",n-1);if(e>a)return}let l=t[a-1];if(l&&!o.test(l))return;let c=t.substring(a+e.length,n);return{text:c,position:a+e.length}}let s=["position:absolute;","overflow:auto;","word-wrap:break-word;","top:0px;","left:-9999px;"],a=["box-sizing","font-family","font-size","font-style","font-variant","font-weight","height","letter-spacing","line-height","max-height","min-height","padding-bottom","padding-left","padding-right","padding-top","border-bottom","border-left","border-right","border-top","text-decoration","text-indent","text-transform","width","word-spacing"],l=new WeakMap;function c(t,e){let n,i;let o=t.nodeName.toLowerCase();if("textarea"!==o&&"input"!==o)throw Error("expected textField to a textarea or input");let r=l.get(t);if(r&&r.parentElement===t.parentElement)r.innerHTML="";else{r=document.createElement("div"),l.set(t,r);let e=window.getComputedStyle(t),n=s.slice(0);"textarea"===o?n.push("white-space:pre-wrap;"):n.push("white-space:nowrap;");for(let t=0,i=a.length;t<i;t++){let i=a[t];n.push(`${i}:${e.getPropertyValue(i)};`)}r.style.cssText=n.join(" ")}let c=document.createElement("span");if(c.style.cssText="position: absolute;",c.innerHTML="&nbsp;","number"==typeof e){let o=t.value.substring(0,e);o&&(n=document.createTextNode(o)),(o=t.value.substring(e))&&(i=document.createTextNode(o))}else{let e=t.value;e&&(n=document.createTextNode(e))}if(n&&r.appendChild(n),r.appendChild(c),i&&r.appendChild(i),!r.parentElement){if(!t.parentElement)throw Error("textField must have a parentElement to mirror");t.parentElement.insertBefore(r,t)}return r.scrollTop=t.scrollTop,r.scrollLeft=t.scrollLeft,{mirror:r,marker:c}}function u(t,e=t.selectionEnd){let{mirror:n,marker:i}=c(t,e),o=n.getBoundingClientRect(),r=i.getBoundingClientRect();return setTimeout(()=>{n.remove()},5e3),{top:r.top-o.top,left:r.left-o.left}}let d=new WeakMap;let TextExpander=class TextExpander{constructor(t,e){this.expander=t,this.input=e,this.combobox=null,this.menu=null,this.match=null,this.justPasted=!1,this.lookBackIndex=0,this.oninput=this.onInput.bind(this),this.onpaste=this.onPaste.bind(this),this.onkeydown=this.onKeydown.bind(this),this.oncommit=this.onCommit.bind(this),this.onmousedown=this.onMousedown.bind(this),this.onblur=this.onBlur.bind(this),this.interactingWithList=!1,e.addEventListener("paste",this.onpaste),e.addEventListener("input",this.oninput),e.addEventListener("keydown",this.onkeydown),e.addEventListener("blur",this.onblur)}destroy(){this.input.removeEventListener("paste",this.onpaste),this.input.removeEventListener("input",this.oninput),this.input.removeEventListener("keydown",this.onkeydown),this.input.removeEventListener("blur",this.onblur)}dismissMenu(){this.deactivate()&&(this.lookBackIndex=this.input.selectionEnd||this.lookBackIndex)}activate(t,e){var n,o;if(this.input!==document.activeElement&&this.input!==(null===(o=null===(n=document.activeElement)||void 0===n?void 0:n.shadowRoot)||void 0===o?void 0:o.activeElement))return;this.deactivate(),this.menu=e,e.id||(e.id=`text-expander-${Math.floor(1e5*Math.random()).toString()}`),this.expander.append(e),this.combobox=new i.Z(this.input,e);let{top:r,left:s}=u(this.input,t.position);e.style.top=`${r}px`,e.style.left=`${s}px`,this.combobox.start(),e.addEventListener("combobox-commit",this.oncommit),e.addEventListener("mousedown",this.onmousedown),this.combobox.navigate(1)}deactivate(){let t=this.menu;return!!t&&!!this.combobox&&(this.menu=null,t.removeEventListener("combobox-commit",this.oncommit),t.removeEventListener("mousedown",this.onmousedown),this.combobox.destroy(),this.combobox=null,t.remove(),!0)}onCommit({target:t}){if(!(t instanceof HTMLElement)||!this.combobox)return;let e=this.match;if(!e)return;let n=this.input.value.substring(0,e.position-e.key.length),i=this.input.value.substring(e.position+e.text.length),o={item:t,key:e.key,value:null},r=!this.expander.dispatchEvent(new CustomEvent("text-expander-value",{cancelable:!0,detail:o}));if(r||!o.value)return;let s=`${o.value} `;this.input.value=n+s+i;let a=n.length+s.length;this.deactivate(),this.input.focus({preventScroll:!0}),this.input.selectionStart=a,this.input.selectionEnd=a,this.lookBackIndex=a,this.match=null,this.expander.dispatchEvent(new CustomEvent("text-expander-committed",{cancelable:!1,detail:{input:this.input}}))}onBlur(){if(this.interactingWithList){this.interactingWithList=!1;return}this.deactivate()}onPaste(){this.justPasted=!0}async onInput(){if(this.justPasted){this.justPasted=!1;return}let t=this.findMatch();if(t){this.match=t;let e=await this.notifyProviders(t);if(!this.match)return;e?this.activate(t,e):this.deactivate()}else this.match=null,this.deactivate()}findMatch(){let t=this.input.selectionEnd||0,e=this.input.value;for(let{key:n,multiWord:i}of(t<=this.lookBackIndex&&(this.lookBackIndex=t-1),this.expander.keys)){let o=r(e,n,t,{multiWord:i,lookBackIndex:this.lookBackIndex,lastMatchPosition:this.match?this.match.position:null});if(o)return{text:o.text,key:n,position:o.position}}}async notifyProviders(t){let e=[],n=t=>e.push(t),i=!this.expander.dispatchEvent(new CustomEvent("text-expander-change",{cancelable:!0,detail:{provide:n,text:t.text,key:t.key}}));if(i)return;let o=await Promise.all(e),r=o.filter(t=>t.matched).map(t=>t.fragment);return r[0]}onMousedown(){this.interactingWithList=!0}onKeydown(t){"Escape"===t.key&&(this.match=null,this.deactivate()&&(this.lookBackIndex=this.input.selectionEnd||this.lookBackIndex,t.stopImmediatePropagation(),t.preventDefault()))}};let TextExpanderElement=class TextExpanderElement extends HTMLElement{get keys(){let t=this.getAttribute("keys"),e=t?t.split(" "):[],n=this.getAttribute("multiword"),i=n?n.split(" "):[],o=0===i.length&&this.hasAttribute("multiword");return e.map(t=>({key:t,multiWord:o||i.includes(t)}))}connectedCallback(){let t=this.querySelector('input[type="text"], textarea');if(!(t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement))return;let e=new TextExpander(this,t);d.set(this,e)}disconnectedCallback(){let t=d.get(this);t&&(t.destroy(),d.delete(this))}dismiss(){let t=d.get(this);t&&t.dismissMenu()}};window.customElements.get("text-expander")||(window.TextExpanderElement=TextExpanderElement,window.customElements.define("text-expander",TextExpanderElement));var h=null},88823:()=>{let t="complete"===document.readyState?Promise.resolve():new Promise(t=>{window.addEventListener("load",t)});let TypingEffectElement=class TypingEffectElement extends HTMLElement{async connectedCallback(){await t,this.content&&await n(this.lines,this.content,this.characterDelay,this.lineDelay),this.cursor&&(this.cursor.hidden=!0),this.dispatchEvent(new CustomEvent("typing:complete",{bubbles:!0,cancelable:!0}))}get content(){return this.querySelector('[data-target="typing-effect.content"]')}get cursor(){return this.querySelector('[data-target="typing-effect.cursor"]')}get lines(){let t=this.getAttribute("data-lines");try{return t?JSON.parse(t):[]}catch(t){return[]}}get prefersReducedMotion(){return window.matchMedia("(prefers-reduced-motion)").matches}get characterDelay(){return this.prefersReducedMotion?0:Math.max(0,Math.min(Math.floor(Number(this.getAttribute("data-character-delay"))),2147483647))||40}set characterDelay(t){if(t>2147483647||t<0)throw new DOMException("Value is negative or greater than the allowed amount");this.setAttribute("data-character-delay",String(t))}get lineDelay(){return this.prefersReducedMotion?0:Math.max(0,Math.min(Math.floor(Number(this.getAttribute("data-line-delay"))),2147483647))||40}set lineDelay(t){if(t>2147483647||t<0)throw new DOMException("Value is negative or greater than the allowed amount");this.setAttribute("data-line-delay",String(t))}};var e=null;async function n(t,e,n,o){for(let r=0;r<t.length;r++){if(0===n)e.append(t[r]);else for(let o of t[r].split(""))await i(n),e.innerHTML+=o;0!==o&&await i(o),r<t.length-1&&e.append(document.createElement("br"))}}async function i(t){return new Promise(e=>{setTimeout(e,t)})}window.customElements.get("typing-effect")||(window.TypingEffectElement=TypingEffectElement,window.customElements.define("typing-effect",TypingEffectElement))}}]);
//# sourceMappingURL=vendors-node_modules_github_file-attachment-element_dist_index_js-node_modules_primer_view-co-2c6968-e1e05dc6c4de.js.map