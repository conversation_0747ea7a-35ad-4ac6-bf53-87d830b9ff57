"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_mini-throt-08ab15","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc0","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc1","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc2"],{13002:(e,t,n)=>{n.d(t,{Z:()=>u});let FilterInputElement=class FilterInputElement extends HTMLElement{constructor(){super(),this.currentQuery=null,this.filter=null,this.debounceInputChange=s(()=>r(this,!0)),this.boundFilterResults=()=>{r(this,!1)}}static get observedAttributes(){return["aria-owns"]}attributeChangedCallback(e,t){t&&"aria-owns"===e&&r(this,!1)}connectedCallback(){let e=this.input;e&&(e.setAttribute("autocomplete","off"),e.setAttribute("spellcheck","false"),e.addEventListener("focus",this.boundFilterResults),e.addEventListener("change",this.boundFilterResults),e.addEventListener("input",this.debounceInputChange))}disconnectedCallback(){let e=this.input;e&&(e.removeEventListener("focus",this.boundFilterResults),e.removeEventListener("change",this.boundFilterResults),e.removeEventListener("input",this.debounceInputChange))}get input(){let e=this.querySelector("input");return e instanceof HTMLInputElement?e:null}reset(){let e=this.input;e&&(e.value="",e.dispatchEvent(new Event("change",{bubbles:!0})))}};async function r(e,t=!1){let n=e.input;if(!n)return;let r=n.value.trim(),s=e.getAttribute("aria-owns");if(!s)return;let u=document.getElementById(s);if(!u)return;let c=u.hasAttribute("data-filter-list")?u:u.querySelector("[data-filter-list]");if(!c||(e.dispatchEvent(new CustomEvent("filter-input-start",{bubbles:!0})),t&&e.currentQuery===r))return;e.currentQuery=r;let d=e.filter||o,f=c.childElementCount,h=0,m=!1;for(let e of Array.from(c.children)){if(!(e instanceof HTMLElement))continue;let t=i(e),n=d(e,t,r);!0===n.hideNew&&(m=n.hideNew),e.hidden=!n.match,n.match&&h++}let p=u.querySelector("[data-filter-new-item]"),b=!!p&&r.length>0&&!m;p instanceof HTMLElement&&(p.hidden=!b,b&&l(p,r)),a(u,h>0||b),e.dispatchEvent(new CustomEvent("filter-input-updated",{bubbles:!0,detail:{count:h,total:f}}))}function o(e,t,n){let r=-1!==t.toLowerCase().indexOf(n.toLowerCase());return{match:r,hideNew:t===n}}function i(e){let t=e.querySelector("[data-filter-item-text]")||e;return(t.textContent||"").trim()}function l(e,t){let n=e.querySelector("[data-filter-new-item-text]");n&&(n.textContent=t);let r=e.querySelector("[data-filter-new-item-value]");(r instanceof HTMLInputElement||r instanceof HTMLButtonElement)&&(r.value=t)}function a(e,t){let n=e.querySelector("[data-filter-empty-state]");n instanceof HTMLElement&&(n.hidden=t)}function s(e){let t;return function(){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e()},300)}}let u=FilterInputElement;window.customElements.get("filter-input")||(window.FilterInputElement=FilterInputElement,window.customElements.define("filter-input",FilterInputElement))},46263:(e,t,n)=>{function r(e,t=0,{start:n=!0,middle:r=!0,once:o=!1}={}){let i,l=0,a=!1;function s(...u){if(a)return;let c=Date.now()-l;l=Date.now(),n?(n=!1,e.apply(this,u),o&&s.cancel()):(r&&c<t||!r)&&(clearTimeout(i),i=setTimeout(()=>{l=Date.now(),e.apply(this,u),o&&s.cancel()},r?t-c:t))}return s.cancel=()=>{clearTimeout(i),a=!0},s}function o(e,t=0,{start:n=!1,middle:o=!1,once:i=!1}={}){return r(e,t,{start:n,middle:o,once:i})}n.d(t,{D:()=>o,P:()=>r})},65935:(e,t,n)=>{let r;function o(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}function i(e){let t=new URLSearchParams,n=new FormData(e).entries();for(let[e,r]of[...n])t.append(e,r.toString());return t.toString()}n.d(t,{AC:()=>d,rK:()=>c,uT:()=>u});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function l(){let e,t;let n=new Promise(function(n,r){e=n,t=r});return[n,e,t]}let a=[],s=[];function u(e){a.push(e)}function c(e){s.push(e)}function d(e,t){r||(r=new Map,"undefined"!=typeof document&&document.addEventListener("submit",h));let n=r.get(e)||[];r.set(e,[...n,t])}function f(e){let t=[];for(let n of r.keys())if(e.matches(n)){let e=r.get(n)||[];t.push(...e)}return t}function h(e){if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let t=e.target,n=f(t);if(0===n.length)return;let r=p(t),[o,i,u]=l();e.preventDefault(),m(n,t,r,o).then(async e=>{if(e){for(let e of s)await e(t);b(r).then(i,u).catch(()=>{}).then(()=>{for(let e of a)e(t)})}else t.submit()},e=>{t.submit(),setTimeout(()=>{throw e})})}async function m(e,t,n,r){let o=!1;for(let i of e){let[e,a]=l(),s=()=>(o=!0,a(),r),u={text:s,json:()=>(n.headers.set("Accept","application/json"),s()),html:()=>(n.headers.set("Accept","text/html"),s())};await Promise.race([e,i(t,u,n)])}return o}function p(e){let t={method:e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===t.method.toUpperCase()){let n=i(e);n&&(t.url+=(~t.url.indexOf("?")?"&":"?")+n)}else t.body=new FormData(e);return t}async function b(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=this,t=JSON.parse(e.text);return delete e.json,e.json=t,e.json},get html(){let e=this;return delete e.html,e.html=o(document,e.text),e.html}},r=await t.text();if(n.text=r,t.ok)return n;throw new ErrorWithResponse("request failed",n)}},88309:(e,t,n)=>{n.d(t,{Z:()=>s});let r=new WeakMap;let RemoteInputElement=class RemoteInputElement extends HTMLElement{constructor(){super();let e=i.bind(null,this,!0),t={currentQuery:null,oninput:a(e),fetch:e,controller:null};r.set(this,t)}static get observedAttributes(){return["src"]}attributeChangedCallback(e,t){t&&"src"===e&&i(this,!1)}connectedCallback(){let e=this.input;if(!e)return;e.setAttribute("autocomplete","off"),e.setAttribute("spellcheck","false");let t=r.get(this);t&&(e.addEventListener("focus",t.fetch),e.addEventListener("change",t.fetch),e.addEventListener("input",t.oninput))}disconnectedCallback(){let e=this.input;if(!e)return;let t=r.get(this);t&&(e.removeEventListener("focus",t.fetch),e.removeEventListener("change",t.fetch),e.removeEventListener("input",t.oninput))}get input(){let e=this.querySelector("input, textarea");return e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement?e:null}get src(){return this.getAttribute("src")||""}set src(e){this.setAttribute("src",e)}};function o(){return"AbortController"in window?new AbortController:{signal:null,abort(){}}}async function i(e,t){let n;let i=e.input;if(!i)return;let a=r.get(e);if(!a)return;let s=i.value;if(t&&a.currentQuery===s)return;a.currentQuery=s;let u=e.src;if(!u)return;let c=document.getElementById(e.getAttribute("aria-owns")||"");if(!c)return;let d=new URL(u,window.location.href),f=new URLSearchParams(d.search);f.append(e.getAttribute("param")||"q",s),d.search=f.toString(),a.controller?a.controller.abort():(e.dispatchEvent(new CustomEvent("loadstart")),e.setAttribute("loading","")),a.controller=o();let h="";try{n=await l(e,d.toString(),{signal:a.controller.signal,credentials:"same-origin",headers:{accept:"text/fragment+html"}}),h=await n.text(),e.removeAttribute("loading"),a.controller=null}catch(t){"AbortError"!==t.name&&(e.removeAttribute("loading"),a.controller=null);return}n&&n.ok?(c.innerHTML=h,e.dispatchEvent(new CustomEvent("remote-input-success",{bubbles:!0}))):e.dispatchEvent(new CustomEvent("remote-input-error",{bubbles:!0}))}async function l(e,t,n){try{let r=await fetch(t,n);return e.dispatchEvent(new CustomEvent("load")),e.dispatchEvent(new CustomEvent("loadend")),r}catch(t){throw"AbortError"!==t.name&&(e.dispatchEvent(new CustomEvent("error")),e.dispatchEvent(new CustomEvent("loadend"))),t}}function a(e){let t;return function(){clearTimeout(t),t=setTimeout(()=>{clearTimeout(t),e()},300)}}let s=RemoteInputElement;window.customElements.get("remote-input")||(window.RemoteInputElement=RemoteInputElement,window.customElements.define("remote-input",RemoteInputElement))},47142:(e,t,n)=>{n.d(t,{CD:()=>y,DU:()=>o,Gs:()=>g,m7:()=>w});var r=-1/0,o=1/0,i=-.005,l=-.005,a=-.01,s=1,u=.9,c=.8,d=.7,f=.6;function h(e){return e.toLowerCase()===e}function m(e){return e.toUpperCase()===e}function p(e){for(var t=e.length,n=Array(t),r="/",o=0;o<t;o++){var i=e[o];"/"===r?n[o]=u:"-"===r||"_"===r||" "===r?n[o]=c:"."===r?n[o]=f:h(r)&&m(i)?n[o]=d:n[o]=0,r=i}return n}function b(e,t,n,o){for(var u=e.length,c=t.length,d=e.toLowerCase(),f=t.toLowerCase(),h=p(t,h),m=0;m<u;m++){n[m]=Array(c),o[m]=Array(c);for(var b=r,g=m===u-1?l:a,w=0;w<c;w++)if(d[m]===f[w]){var y=r;m?w&&(y=Math.max(o[m-1][w-1]+h[w],n[m-1][w-1]+s)):y=w*i+h[w],n[m][w]=y,o[m][w]=b=Math.max(y,b+g)}else n[m][w]=r,o[m][w]=b+=g}}function g(e,t){var n=e.length,i=t.length;if(!n||!i)return r;if(n===i)return o;if(i>1024)return r;var l=Array(n),a=Array(n);return b(e,t,l,a),a[n-1][i-1]}function w(e,t){var n=e.length,o=t.length,i=Array(n);if(!n||!o)return i;if(n===o){for(var l=0;l<n;l++)i[l]=l;return i}if(o>1024)return i;var a=Array(n),u=Array(n);b(e,t,a,u);for(var c=!1,l=n-1,d=o-1;l>=0;l--)for(;d>=0;d--)if(a[l][d]!==r&&(c||a[l][d]===u[l][d])){c=l&&d&&u[l][d]===a[l-1][d-1]+s,i[l]=d--;break}return i}function y(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var n=e.length,r=0,o=0;r<n;r+=1)if(0===(o=t.indexOf(e[r],o)+1))return!1;return!0}},76006:(e,t,n)=>{let r;n.d(t,{Lj:()=>v,Ih:()=>S,P4:()=>h,nW:()=>P,fA:()=>x,GO:()=>k});let o=new WeakSet;function i(e){o.add(e),e.shadowRoot&&l(e.shadowRoot),u(e),s(e.ownerDocument)}function l(e){u(e),s(e)}let a=new WeakMap;function s(e=document){if(a.has(e))return a.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)f(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&u(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let r={get closed(){return t},unsubscribe(){t=!0,a.delete(e),n.disconnect()}};return a.set(e,r),r}function u(e){for(let t of e.querySelectorAll("[data-action]"))f(t);e instanceof Element&&e.hasAttribute("data-action")&&f(e)}function c(e){let t=e.currentTarget;for(let n of d(t))if(e.type===n.type){let r=t.closest(n.tag);o.has(r)&&"function"==typeof r[n.method]&&r[n.method](e);let i=t.getRootNode();if(i instanceof ShadowRoot&&o.has(i.host)&&i.host.matches(n.tag)){let t=i.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*d(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function f(e){for(let t of d(e))e.addEventListener(t.type,c)}function h(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let r of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!r.closest(n))return r}for(let r of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(r.closest(n)===e)return r}function m(e,t){let n=e.tagName.toLowerCase(),r=[];if(e.shadowRoot)for(let o of e.shadowRoot.querySelectorAll(`[data-targets~="${n}.${t}"]`))o.closest(n)||r.push(o);for(let o of e.querySelectorAll(`[data-targets~="${n}.${t}"]`))o.closest(n)===e&&r.push(o);return r}let p=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),b=(e,t="property")=>{let n=p(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n};function g(e){let t=p(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}return e}function w(e){for(let t of e.querySelectorAll("template[data-shadowroot]"))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0))}let y="attr";function v(e,t){_(e,y).add(t)}let E=new WeakSet;function A(e,t){if(E.has(e))return;E.add(e);let n=Object.getPrototypeOf(e),r=n?.constructor?.attrPrefix??"data-";for(let o of(t||(t=_(n,y)),t)){let t=e[o],n=b(`${r}${o}`),i={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?i={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(i={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,o,i),o in e&&!e.hasAttribute(n)&&i.set.call(e,t)}}function C(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",r=e=>b(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[..._(e.prototype,y)].map(r).concat(t),set(e){t=e}})}let L=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let r=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,r)};let o=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,r){t.attributeChangedCallback(this,e,n,r,o)};let i=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,i)},set(e){i=e}}),C(e),g(e)}observedAttributes(e,t){return t}connectedCallback(e,t){e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),w(e),A(e),i(e),t?.call(e),e.shadowRoot&&l(e.shadowRoot)}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,r,o){A(e),"data-catalyst"!==t&&o&&o.call(e,t,n,r)}};function _(e,t){if(!Object.prototype.hasOwnProperty.call(e,L)){let t=e[L],n=e[L]=new Map;if(t)for(let[e,r]of t)n.set(e,new Set(r))}let n=e[L];return n.has(t)||n.set(t,new Set),n.get(t)}function x(e,t){_(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return h(this,t)}})}function k(e,t){_(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return m(this,t)}})}function S(e){new CatalystDelegate(e)}let T=new Map,R=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),I=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},r=()=>t.abort();document.addEventListener("mousedown",r,n),document.addEventListener("touchstart",r,n),document.addEventListener("keydown",r,n),document.addEventListener("pointerdown",r,n)}),M=e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let r of e)if(r.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)}),$={ready:()=>R,firstInteraction:()=>I,visible:M},O=new WeakMap;function q(e){cancelAnimationFrame(O.get(e)||0),O.set(e,requestAnimationFrame(()=>{for(let t of T.keys()){let n=e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let r=n?.getAttribute("data-load-on")||"ready",o=r in $?$[r]:$.ready;for(let e of T.get(t)||[])o(t).then(e);T.delete(t),O.delete(e)}}}))}function P(e,t){T.has(e)||T.set(e,new Set),T.get(e).add(t),q(document.body),r||(r=new MutationObserver(e=>{if(T.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&q(e)})).observe(document,{subtree:!0,childList:!0})}},86058:(e,t,n)=>{function r(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}function o(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}function i(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}function l(){return navigator.languages?navigator.languages.join(","):navigator.language||""}function a(){return{referrer:r(),user_agent:navigator.userAgent,screen_resolution:o(),browser_resolution:i(),browser_languages:l(),pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}}n.d(t,{R:()=>AnalyticsClient});var s=n(82918);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,s.b)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n={client_id:this.clientId,page_views:e,events:t,request_context:a()},r=JSON.stringify(n);try{if(navigator.sendBeacon){navigator.sendBeacon(this.collectorUrl,r);return}}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:r,keepalive:!1})}}},88149:(e,t,n)=>{n.d(t,{n:()=>r});function r(e="ha"){let t;let n={},r=document.head.querySelectorAll(`meta[name^="${e}-"]`);for(let o of Array.from(r)){let{name:r,content:i}=o,l=r.replace(`${e}-`,"").replace(/-/g,"_");"url"===l?t=i:n[l]=i}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}}}]);
//# sourceMappingURL=vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_mini-throt-08ab15-7d10671478d3.js.map