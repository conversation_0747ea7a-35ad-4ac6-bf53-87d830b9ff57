"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-59c459"],{13002:(t,e,n)=>{n.d(e,{Z:()=>u});let FilterInputElement=class FilterInputElement extends HTMLElement{constructor(){super(),this.currentQuery=null,this.filter=null,this.debounceInputChange=l(()=>i(this,!0)),this.boundFilterResults=()=>{i(this,!1)}}static get observedAttributes(){return["aria-owns"]}attributeChangedCallback(t,e){e&&"aria-owns"===t&&i(this,!1)}connectedCallback(){let t=this.input;t&&(t.setAttribute("autocomplete","off"),t.setAttribute("spellcheck","false"),t.addEventListener("focus",this.boundFilterResults),t.addEventListener("change",this.boundFilterResults),t.addEventListener("input",this.debounceInputChange))}disconnectedCallback(){let t=this.input;t&&(t.removeEventListener("focus",this.boundFilterResults),t.removeEventListener("change",this.boundFilterResults),t.removeEventListener("input",this.debounceInputChange))}get input(){let t=this.querySelector("input");return t instanceof HTMLInputElement?t:null}reset(){let t=this.input;t&&(t.value="",t.dispatchEvent(new Event("change",{bubbles:!0})))}};async function i(t,e=!1){let n=t.input;if(!n)return;let i=n.value.trim(),l=t.getAttribute("aria-owns");if(!l)return;let u=document.getElementById(l);if(!u)return;let c=u.hasAttribute("data-filter-list")?u:u.querySelector("[data-filter-list]");if(!c||(t.dispatchEvent(new CustomEvent("filter-input-start",{bubbles:!0})),e&&t.currentQuery===i))return;t.currentQuery=i;let d=t.filter||s,h=c.childElementCount,m=0,p=!1;for(let t of Array.from(c.children)){if(!(t instanceof HTMLElement))continue;let e=r(t),n=d(t,e,i);!0===n.hideNew&&(p=n.hideNew),t.hidden=!n.match,n.match&&m++}let f=u.querySelector("[data-filter-new-item]"),b=!!f&&i.length>0&&!p;f instanceof HTMLElement&&(f.hidden=!b,b&&o(f,i)),a(u,m>0||b),t.dispatchEvent(new CustomEvent("filter-input-updated",{bubbles:!0,detail:{count:m,total:h}}))}function s(t,e,n){let i=-1!==e.toLowerCase().indexOf(n.toLowerCase());return{match:i,hideNew:e===n}}function r(t){let e=t.querySelector("[data-filter-item-text]")||t;return(e.textContent||"").trim()}function o(t,e){let n=t.querySelector("[data-filter-new-item-text]");n&&(n.textContent=e);let i=t.querySelector("[data-filter-new-item-value]");(i instanceof HTMLInputElement||i instanceof HTMLButtonElement)&&(i.value=e)}function a(t,e){let n=t.querySelector("[data-filter-empty-state]");n instanceof HTMLElement&&(n.hidden=e)}function l(t){let e;return function(){clearTimeout(e),e=setTimeout(()=>{clearTimeout(e),t()},300)}}let u=FilterInputElement;window.customElements.get("filter-input")||(window.FilterInputElement=FilterInputElement,window.customElements.define("filter-input",FilterInputElement))},88309:(t,e,n)=>{n.d(e,{Z:()=>l});let i=new WeakMap;let RemoteInputElement=class RemoteInputElement extends HTMLElement{constructor(){super();let t=r.bind(null,this,!0),e={currentQuery:null,oninput:a(t),fetch:t,controller:null};i.set(this,e)}static get observedAttributes(){return["src"]}attributeChangedCallback(t,e){e&&"src"===t&&r(this,!1)}connectedCallback(){let t=this.input;if(!t)return;t.setAttribute("autocomplete","off"),t.setAttribute("spellcheck","false");let e=i.get(this);e&&(t.addEventListener("focus",e.fetch),t.addEventListener("change",e.fetch),t.addEventListener("input",e.oninput))}disconnectedCallback(){let t=this.input;if(!t)return;let e=i.get(this);e&&(t.removeEventListener("focus",e.fetch),t.removeEventListener("change",e.fetch),t.removeEventListener("input",e.oninput))}get input(){let t=this.querySelector("input, textarea");return t instanceof HTMLInputElement||t instanceof HTMLTextAreaElement?t:null}get src(){return this.getAttribute("src")||""}set src(t){this.setAttribute("src",t)}};function s(){return"AbortController"in window?new AbortController:{signal:null,abort(){}}}async function r(t,e){let n;let r=t.input;if(!r)return;let a=i.get(t);if(!a)return;let l=r.value;if(e&&a.currentQuery===l)return;a.currentQuery=l;let u=t.src;if(!u)return;let c=document.getElementById(t.getAttribute("aria-owns")||"");if(!c)return;let d=new URL(u,window.location.href),h=new URLSearchParams(d.search);h.append(t.getAttribute("param")||"q",l),d.search=h.toString(),a.controller?a.controller.abort():(t.dispatchEvent(new CustomEvent("loadstart")),t.setAttribute("loading","")),a.controller=s();let m="";try{n=await o(t,d.toString(),{signal:a.controller.signal,credentials:"same-origin",headers:{accept:"text/fragment+html"}}),m=await n.text(),t.removeAttribute("loading"),a.controller=null}catch(e){"AbortError"!==e.name&&(t.removeAttribute("loading"),a.controller=null);return}n&&n.ok?(c.innerHTML=m,t.dispatchEvent(new CustomEvent("remote-input-success",{bubbles:!0}))):t.dispatchEvent(new CustomEvent("remote-input-error",{bubbles:!0}))}async function o(t,e,n){try{let i=await fetch(e,n);return t.dispatchEvent(new CustomEvent("load")),t.dispatchEvent(new CustomEvent("loadend")),i}catch(e){throw"AbortError"!==e.name&&(t.dispatchEvent(new CustomEvent("error")),t.dispatchEvent(new CustomEvent("loadend"))),e}}function a(t){let e;return function(){clearTimeout(e),e=setTimeout(()=>{clearTimeout(e),t()},300)}}let l=RemoteInputElement;window.customElements.get("remote-input")||(window.RemoteInputElement=RemoteInputElement,window.customElements.define("remote-input",RemoteInputElement))},29501:(t,e,n)=>{function i(t){return Array.from(t.querySelectorAll('[role="tablist"] [role="tab"]')).filter(e=>e instanceof HTMLElement&&e.closest(t.tagName)===t)}n.d(e,{Z:()=>TabContainerElement});let TabContainerElement=class TabContainerElement extends HTMLElement{constructor(){super(),this.addEventListener("keydown",t=>{let e=t.target;if(!(e instanceof HTMLElement)||e.closest(this.tagName)!==this||"tab"!==e.getAttribute("role")&&!e.closest('[role="tablist"]'))return;let n=i(this),r=n.indexOf(n.find(t=>t.matches('[aria-selected="true"]')));if("ArrowRight"===t.code){let t=r+1;t>=n.length&&(t=0),s(this,t)}else if("ArrowLeft"===t.code){let t=r-1;t<0&&(t=n.length-1),s(this,t)}else"Home"===t.code?(s(this,0),t.preventDefault()):"End"===t.code&&(s(this,n.length-1),t.preventDefault())}),this.addEventListener("click",t=>{let e=i(this);if(!(t.target instanceof Element)||t.target.closest(this.tagName)!==this)return;let n=t.target.closest('[role="tab"]');if(!(n instanceof HTMLElement)||!n.closest('[role="tablist"]'))return;let r=e.indexOf(n);s(this,r)})}connectedCallback(){for(let t of i(this))t.hasAttribute("aria-selected")||t.setAttribute("aria-selected","false"),t.hasAttribute("tabindex")||("true"===t.getAttribute("aria-selected")?t.setAttribute("tabindex","0"):t.setAttribute("tabindex","-1"))}};function s(t,e){let n=i(t),s=Array.from(t.querySelectorAll('[role="tabpanel"]')).filter(e=>e.closest(t.tagName)===t),r=n[e],o=s[e],a=!t.dispatchEvent(new CustomEvent("tab-container-change",{bubbles:!0,cancelable:!0,detail:{relatedTarget:o}}));if(!a){for(let t of n)t.setAttribute("aria-selected","false"),t.setAttribute("tabindex","-1");for(let t of s)t.hidden=!0,t.hasAttribute("tabindex")||t.hasAttribute("data-tab-container-no-tabstop")||t.setAttribute("tabindex","0");r.setAttribute("aria-selected","true"),r.setAttribute("tabindex","0"),r.focus(),o.hidden=!1,t.dispatchEvent(new CustomEvent("tab-container-changed",{bubbles:!0,detail:{relatedTarget:o}}))}}window.customElements.get("tab-container")||(window.TabContainerElement=TabContainerElement,window.customElements.define("tab-container",TabContainerElement))},48858:(t,e,n)=>{let i;n.d(e,{e:()=>l});var s=n(78160);(0,n(44542).O)();let r=[];function o(){let t=r.pop();t&&l(t.container,t.initialFocus,t.originalSignal)}function a(t){let e=new AbortController;return t.addEventListener("abort",()=>{e.abort()}),e}function l(t,e,n){let l;let u=new AbortController,c=null!=n?n:u.signal;t.setAttribute("data-focus-trap","active");let d=document.createElement("span");d.setAttribute("class","sentinel"),d.setAttribute("tabindex","0"),d.setAttribute("aria-hidden","true"),d.onfocus=()=>{let e=(0,s.O)(t,!0);null==e||e.focus()};let h=document.createElement("span");function m(n){if(n instanceof HTMLElement&&document.contains(t)){if(t.contains(n)){l=n;return}if(l&&(0,s.Wq)(l)&&t.contains(l)){l.focus();return}if(e&&t.contains(e)){e.focus();return}{let e=(0,s.O)(t);null==e||e.focus();return}}}h.setAttribute("class","sentinel"),h.setAttribute("tabindex","0"),h.setAttribute("aria-hidden","true"),h.onfocus=()=>{let e=(0,s.O)(t);null==e||e.focus()},t.prepend(d),t.append(h);let p=a(c);if(i){let t=i;i.container.setAttribute("data-focus-trap","suspended"),i.controller.abort(),r.push(t)}p.signal.addEventListener("abort",()=>{i=void 0}),c.addEventListener("abort",()=>{t.removeAttribute("data-focus-trap");let e=t.getElementsByClassName("sentinel");for(;e.length>0;)e[0].remove();let n=r.findIndex(e=>e.container===t);n>=0&&r.splice(n,1),o()}),document.addEventListener("focus",t=>{m(t.target)},{signal:p.signal,capture:!0}),m(document.activeElement),i={container:t,controller:p,initialFocus:e,originalSignal:c};let f=r.findIndex(e=>e.container===t);if(f>=0&&r.splice(f,1),!n)return u}},28585:(t,e,n)=>{n.d(e,{F:()=>ModalDialogElement});var i,s,r,o,a=n(48858),l=n(78160),u=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)},c=function(t,e,n,i,s){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!s:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?s.call(t,n):s?s.value=n:e.set(t,n),n};function d(t){document.activeElement!==t&&(null==t||t.focus())}let h=[];function m(t){let e=t.target,n=null==e?void 0:e.closest("button");if(!n||n.hasAttribute("disabled")||"true"===n.getAttribute("aria-disabled"))return;let i=null==n?void 0:n.getAttribute("data-show-dialog-id");if(i){t.stopPropagation();let e=document.getElementById(i);if(e instanceof ModalDialogElement){e.openButton=n,e.show(),t.preventDefault();return}}let s=h[h.length-1];s&&((i=n.getAttribute("data-close-dialog-id"))===s.id&&(h.pop(),s.close()),(i=n.getAttribute("data-submit-dialog-id"))===s.id&&(h.pop(),s.close(!0)))}function p(t){!(t instanceof KeyboardEvent)||"keydown"!==t.type||"Enter"!==t.key||t.ctrlKey||t.altKey||t.metaKey||t.shiftKey||m(t)}function f(t){let e=t.target;if(null==e?void 0:e.closest("button"))return;let n=h[h.length-1];if(!n)return;let i=!e.closest(`#${n.getAttribute("id")}`);i&&e.ownerDocument.addEventListener("mouseup",t=>{t.target===e&&(h.pop(),n.close())},{once:!0})}let ModalDialogElement=class ModalDialogElement extends HTMLElement{constructor(){super(...arguments),i.add(this),s.set(this,new AbortController)}get open(){return this.hasAttribute("open")}set open(t){var e,n,o,m;if(t){if(this.open)return;this.setAttribute("open",""),this.setAttribute("aria-disabled","false"),document.body.style.paddingRight=`${window.innerWidth-document.body.clientWidth}px`,document.body.style.overflow="hidden",null===(e=u(this,i,"a",r))||void 0===e||e.classList.remove("Overlay--hidden"),u(this,s,"f").signal.aborted&&c(this,s,new AbortController,"f"),(0,a.e)(this,void 0,u(this,s,"f").signal),h.push(this)}else{if(!this.open)return;this.removeAttribute("open"),this.setAttribute("aria-disabled","true"),null===(n=u(this,i,"a",r))||void 0===n||n.classList.add("Overlay--hidden"),document.body.style.paddingRight="0",document.body.style.overflow="initial",u(this,s,"f").abort();let t=(null===(o=this.openButton)||void 0===o?void 0:o.closest("details"))||(null===(m=this.openButton)||void 0===m?void 0:m.closest("action-menu"));t?d((0,l.O)(t)):d(this.openButton),this.openButton=null}}get showButtons(){return document.querySelectorAll(`button[data-show-dialog-id='${this.id}']`)}connectedCallback(){this.hasAttribute("role")||this.setAttribute("role","dialog"),document.addEventListener("click",m),document.addEventListener("keydown",p),document.addEventListener("mousedown",f),this.addEventListener("keydown",t=>u(this,i,"m",o).call(this,t))}show(){this.open=!0}close(t=!1){if(!1===this.open)return;let e=new Event(t?"close":"cancel");this.dispatchEvent(e),this.open=!1}};s=new WeakMap,i=new WeakSet,r=function(){var t;return(null===(t=this.parentElement)||void 0===t?void 0:t.hasAttribute("data-modal-dialog-overlay"))?this.parentElement:null},o=function(t){if(t instanceof KeyboardEvent&&!t.isComposing&&this.open)switch(t.key){case"Escape":this.close(),t.preventDefault(),t.stopPropagation();break;case"Enter":{let e=t.target;e.getAttribute("data-close-dialog-id")===this.id&&t.stopPropagation()}}},window.customElements.get("modal-dialog")||(window.ModalDialogElement=ModalDialogElement,window.customElements.define("modal-dialog",ModalDialogElement))},46481:(t,e,n)=>{n.d(e,{Z:()=>AutocompleteElement});var i=n(10160);function s(t,e=0){let n;return function(...i){clearTimeout(n),n=window.setTimeout(()=>{clearTimeout(n),t(...i)},e)}}let r=window.testScreenReaderDelay||100;let Autocomplete=class Autocomplete{constructor(t,e,n,r=!1){var o;if(this.container=t,this.input=e,this.results=n,this.combobox=new i.Z(e,n),this.feedback=document.getElementById(`${this.results.id}-feedback`),this.autoselectEnabled=r,this.clearButton=document.getElementById(`${this.input.id||this.input.name}-clear`),this.clientOptions=n.querySelectorAll("[role=option]"),this.feedback&&(this.feedback.setAttribute("aria-live","polite"),this.feedback.setAttribute("aria-atomic","true")),this.clearButton&&!this.clearButton.getAttribute("aria-label")){let t=document.querySelector(`label[for="${this.input.name}"]`);this.clearButton.setAttribute("aria-label","clear:"),this.clearButton.setAttribute("aria-labelledby",`${this.clearButton.id} ${(null==t?void 0:t.id)||""}`)}this.input.getAttribute("aria-expanded")||this.input.setAttribute("aria-expanded","false"),this.results.hidden=!0,this.results.setAttribute("aria-label","results"),this.input.setAttribute("autocomplete","off"),this.input.setAttribute("spellcheck","false"),this.interactingWithList=!1,this.onInputChange=s(this.onInputChange.bind(this),300),this.onResultsMouseDown=this.onResultsMouseDown.bind(this),this.onInputBlur=this.onInputBlur.bind(this),this.onInputFocus=this.onInputFocus.bind(this),this.onKeydown=this.onKeydown.bind(this),this.onCommit=this.onCommit.bind(this),this.handleClear=this.handleClear.bind(this),this.input.addEventListener("keydown",this.onKeydown),this.input.addEventListener("focus",this.onInputFocus),this.input.addEventListener("blur",this.onInputBlur),this.input.addEventListener("input",this.onInputChange),this.results.addEventListener("mousedown",this.onResultsMouseDown),this.results.addEventListener("combobox-commit",this.onCommit),null===(o=this.clearButton)||void 0===o||o.addEventListener("click",this.handleClear)}destroy(){this.input.removeEventListener("keydown",this.onKeydown),this.input.removeEventListener("focus",this.onInputFocus),this.input.removeEventListener("blur",this.onInputBlur),this.input.removeEventListener("input",this.onInputChange),this.results.removeEventListener("mousedown",this.onResultsMouseDown),this.results.removeEventListener("combobox-commit",this.onCommit)}handleClear(t){t.preventDefault(),"true"===this.input.getAttribute("aria-expanded")&&(this.input.setAttribute("aria-expanded","false"),this.updateFeedbackForScreenReaders("Results hidden.")),this.input.value="",this.container.value="",this.input.focus(),this.input.dispatchEvent(new Event("change")),this.container.open=!1}onKeydown(t){if("Enter"===t.key&&this.container.open&&this.autoselectEnabled){let e=this.results.children[0];e&&(t.stopPropagation(),t.preventDefault(),this.onCommit({target:e}))}if("Escape"===t.key&&this.container.open)this.container.open=!1,t.stopPropagation(),t.preventDefault();else if(t.altKey&&"ArrowUp"===t.key&&this.container.open)this.container.open=!1,t.stopPropagation(),t.preventDefault();else if(t.altKey&&"ArrowDown"===t.key&&!this.container.open){if(!this.input.value.trim())return;this.container.open=!0,t.stopPropagation(),t.preventDefault()}}onInputFocus(){this.fetchResults()}onInputBlur(){if(this.interactingWithList){this.interactingWithList=!1;return}this.container.open=!1}onCommit({target:t}){if(!(t instanceof HTMLElement)||(this.container.open=!1,t instanceof HTMLAnchorElement))return;let e=t.getAttribute("data-autocomplete-value")||t.textContent;this.updateFeedbackForScreenReaders(`${t.textContent||""} selected.`),this.container.value=e,e||this.updateFeedbackForScreenReaders("Results hidden.")}onResultsMouseDown(){this.interactingWithList=!0}onInputChange(){this.feedback&&this.feedback.textContent&&(this.feedback.textContent=""),this.container.removeAttribute("value"),this.fetchResults()}identifyOptions(){let t=0;for(let e of this.results.querySelectorAll('[role="option"]:not([id])'))e.id=`${this.results.id}-option-${t++}`}updateFeedbackForScreenReaders(t){setTimeout(()=>{this.feedback&&(this.feedback.textContent=t)},r)}fetchResults(){let t=this.input.value.trim();if(!t){this.container.open=!1;return}let e=this.container.src;if(!e)return;let n=new URL(e,window.location.href),i=new URLSearchParams(n.search.slice(1));i.append("q",t),n.search=i.toString(),this.container.dispatchEvent(new CustomEvent("loadstart")),this.container.fetchResult(this.input,n.toString()).then(t=>{this.results.innerHTML=t,this.identifyOptions();let e=this.results.querySelectorAll('[role="option"]'),n=!!e.length,i=e.length,[s]=e,r=null==s?void 0:s.textContent;this.autoselectEnabled&&r?this.updateFeedbackForScreenReaders(`${i} results. ${r} is the top result: Press Enter to activate.`):this.updateFeedbackForScreenReaders(`${i||"No"} results.`),this.container.open=n,this.container.dispatchEvent(new CustomEvent("load")),this.container.dispatchEvent(new CustomEvent("loadend"))}).catch(()=>{this.container.dispatchEvent(new CustomEvent("error")),this.container.dispatchEvent(new CustomEvent("loadend"))})}open(){this.results.hidden&&(this.combobox.start(),this.results.hidden=!1)}close(){this.results.hidden||(this.combobox.stop(),this.results.hidden=!0)}};let AutocompleteEvent=class AutocompleteEvent extends CustomEvent{constructor(t,e){super(t,e),this.relatedTarget=e.relatedTarget}};let o=new WeakMap;function a(t,e){let n=new XMLHttpRequest;return n.open("GET",e,!0),n.setRequestHeader("Accept","text/fragment+html"),l(t,n)}function l(t,e){let n=o.get(t);n&&n.abort(),o.set(t,e);let i=()=>o.delete(t),s=u(e);return s.then(i,i),s}function u(t){return new Promise((e,n)=>{t.onload=function(){t.status>=200&&t.status<300?e(t.responseText):n(Error(t.responseText))},t.onerror=n,t.send()})}let c=new WeakMap;let AutocompleteElement=class AutocompleteElement extends HTMLElement{constructor(){super(...arguments),this.fetchResult=a}connectedCallback(){let t=this.getAttribute("for");if(!t)return;let e=this.querySelector("input"),n=document.getElementById(t);if(!(e instanceof HTMLInputElement)||!n)return;let i="true"===this.getAttribute("data-autoselect");c.set(this,new Autocomplete(this,e,n,i)),n.setAttribute("role","listbox")}disconnectedCallback(){let t=c.get(this);t&&(t.destroy(),c.delete(this))}get src(){return this.getAttribute("src")||""}set src(t){this.setAttribute("src",t)}get value(){return this.getAttribute("value")||""}set value(t){this.setAttribute("value",t)}get open(){return this.hasAttribute("open")}set open(t){t?this.setAttribute("open",""):this.removeAttribute("open")}static get observedAttributes(){return["open","value"]}attributeChangedCallback(t,e,n){if(e===n)return;let i=c.get(this);if(i)switch(t){case"open":null===n?i.close():i.open();break;case"value":null!==n&&(i.input.value=n),this.dispatchEvent(new AutocompleteEvent("auto-complete-change",{bubbles:!0,relatedTarget:i.input}))}}};window.customElements.get("auto-complete")||(window.AutocompleteElement=AutocompleteElement,window.customElements.define("auto-complete",AutocompleteElement))},27034:(t,e,n)=>{n.d(e,{Z:()=>IncludeFragmentElement});var i,s,r,o,a,l,u,c,d=function(t,e,n,i){if("a"===n&&!i)throw TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===n?i:"a"===n?i.call(t):i?i.value:e.get(t)},h=function(t,e,n,i,s){if("m"===i)throw TypeError("Private method is not writable");if("a"===i&&!s)throw TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!s:!e.has(t))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?s.call(t,n):s?s.value=n:e.set(t,n),n};let m=new WeakMap;function p(t){return t&&!!t.split(",").find(t=>t.match(/^\s*\*\/\*/))}let f=null;let IncludeFragmentElement=class IncludeFragmentElement extends HTMLElement{static setCSPTrustedTypesPolicy(t){f=null===t?t:Promise.resolve(t)}static get observedAttributes(){return["src","loading"]}get src(){let t=this.getAttribute("src");if(!t)return"";{let e=this.ownerDocument.createElement("a");return e.href=t,e.href}}set src(t){this.setAttribute("src",t)}get loading(){return"lazy"===this.getAttribute("loading")?"lazy":"eager"}set loading(t){this.setAttribute("loading",t)}get accept(){return this.getAttribute("accept")||""}set accept(t){this.setAttribute("accept",t)}get data(){return d(this,i,"m",l).call(this)}attributeChangedCallback(t,e){"src"===t?this.isConnected&&"eager"===this.loading&&d(this,i,"m",o).call(this):"loading"===t&&this.isConnected&&"eager"!==e&&"eager"===this.loading&&d(this,i,"m",o).call(this)}constructor(){super(),i.add(this),s.set(this,!1),r.set(this,new IntersectionObserver(t=>{for(let e of t)if(e.isIntersecting){let{target:t}=e;if(d(this,r,"f").unobserve(t),!(t instanceof IncludeFragmentElement))return;"lazy"===t.loading&&d(this,i,"m",o).call(this)}},{rootMargin:"0px 0px 256px 0px",threshold:.01}));let t=this.attachShadow({mode:"open"}),e=document.createElement("style");e.textContent=":host {display: block;}",t.append(e,document.createElement("slot"))}connectedCallback(){this.src&&"eager"===this.loading&&d(this,i,"m",o).call(this),"lazy"===this.loading&&d(this,r,"f").observe(this)}request(){let t=this.src;if(!t)throw Error("missing src");return new Request(t,{method:"GET",credentials:"same-origin",headers:{Accept:this.accept||"text/html"}})}load(){return d(this,i,"m",l).call(this)}fetch(t){return fetch(t)}refetch(){m.delete(this),d(this,i,"m",o).call(this)}};s=new WeakMap,r=new WeakMap,i=new WeakSet,o=async function(){if(!d(this,s,"f")){h(this,s,!0,"f"),d(this,r,"f").unobserve(this);try{let t=await d(this,i,"m",a).call(this);if(t instanceof Error)throw t;let e=document.createElement("template");e.innerHTML=t;let n=document.importNode(e.content,!0),r=!this.dispatchEvent(new CustomEvent("include-fragment-replace",{cancelable:!0,detail:{fragment:n}}));if(r){h(this,s,!1,"f");return}this.replaceWith(n),this.dispatchEvent(new CustomEvent("include-fragment-replaced"))}catch(t){this.classList.add("is-error")}finally{h(this,s,!1,"f")}}},a=async function(){let t=this.src,e=m.get(this);if(e&&e.src===t)return e.data;{let e;return e=t?d(this,i,"m",c).call(this):Promise.reject(Error("missing src")),m.set(this,{src:t,data:e}),e}},l=async function(){let t=await d(this,i,"m",a).call(this);if(t instanceof Error)throw t;return t.toString()},u=async function(t){for(let e of(await new Promise(t=>setTimeout(t,0)),t))this.dispatchEvent(new Event(e))},c=async function(){try{await d(this,i,"m",u).call(this,["loadstart"]);let t=await this.fetch(this.request());if(200!==t.status)throw Error(`Failed to load resource: the server responded with a status of ${t.status}`);let e=t.headers.get("Content-Type");if(!p(this.accept)&&(!e||!e.includes(this.accept?this.accept:"text/html")))throw Error(`Failed to load resource: expected ${this.accept||"text/html"} but was ${e}`);let n=await t.text(),s=n;if(f){let e=await f;s=e.createHTML(n,t)}return d(this,i,"m",u).call(this,["load","loadend"]),s}catch(t){throw d(this,i,"m",u).call(this,["error","loadend"]),t}},window.customElements.get("include-fragment")||(window.IncludeFragmentElement=IncludeFragmentElement,window.customElements.define("include-fragment",IncludeFragmentElement))}}]);
//# sourceMappingURL=vendors-node_modules_github_filter-input-element_dist_index_js-node_modules_github_remote-inp-59c459-e8f4e4ea4ddd.js.map