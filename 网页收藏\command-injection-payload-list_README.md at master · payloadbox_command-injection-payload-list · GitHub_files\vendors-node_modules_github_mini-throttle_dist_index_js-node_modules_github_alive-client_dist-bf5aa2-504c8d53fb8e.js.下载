"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_alive-client_dist-bf5aa2"],{46263:(e,t,s)=>{function n(e,t=0,{start:s=!0,middle:n=!0,once:i=!1}={}){let r,a=0,o=!1;function c(...l){if(o)return;let h=Date.now()-a;a=Date.now(),s?(s=!1,e.apply(this,l),i&&c.cancel()):(n&&h<t||!n)&&(clearTimeout(r),r=setTimeout(()=>{a=Date.now(),e.apply(this,l),i&&c.cancel()},n?t-h:t))}return c.cancel=()=>{clearTimeout(r),o=!0},c}function i(e,t=0,{start:s=!1,middle:i=!1,once:r=!1}={}){return n(e,t,{start:s,middle:i,once:r})}s.d(t,{D:()=>i,P:()=>n})},27907:(e,t,s)=>{s.d(t,{a:()=>AliveSession});var n,i=s(81855),r=s(60835),a=s(16544),o=s(75658),c=s(80955),l=s(29871);function h(){return`${Math.round(2147483647*Math.random())}_${Math.round(Date.now()/1e3)}`}function u(e){let t=e.match(/\/u\/(\d+)\/ws/);return t?+t[1]:0}!function(e){e.Deploy="Alive Redeploy",e.Reconnect="Alive Reconnect"}(n||(n={}));let AliveSession=class AliveSession{constructor(e,t,s,n,a=6e5){this.url=e,this.getUrl=t,this.inSharedWorker=s,this.notify=n,this.maxReconnectBackoff=a,this.subscriptions=new o.v,this.state="online",this.retrying=null,this.connectionCount=0,this.presence=new i.k2,this.presenceMetadata=new r.a,this.intentionallyDisconnected=!1,this.lastCameOnline=0,this.userId=u(e),this.presenceId=h(),this.presenceKey=(0,i.Hw)(this.userId,this.presenceId),this.socket=this.connect()}subscribe(e){let t=this.subscriptions.add(...e);for(let s of(this.sendSubscribe(t),e)){let e=s.topic.name;(0,i.A)(e)&&this.notifyCachedPresence(s.subscriber,e)}}unsubscribe(e){let t=this.subscriptions.delete(...e);this.sendUnsubscribe(t)}unsubscribeAll(...e){let t=this.subscriptions.drain(...e);this.sendUnsubscribe(t);let s=this.presenceMetadata.removeSubscribers(e);this.sendPresenceMetadataUpdate(s)}requestPresence(e,t){for(let s of t)this.notifyCachedPresence(e,s)}notifyCachedPresence(e,t){let s=this.presence.getChannelItems(t);0!==s.length&&this.notifyPresenceChannel(t,s)}updatePresenceMetadata(e){let t=new Set;for(let s of e)this.presenceMetadata.setMetadata(s),t.add(s.channelName);this.sendPresenceMetadataUpdate(t)}sendPresenceMetadataUpdate(e){if(!e.size)return;let t=[];for(let s of e){let e=this.subscriptions.topic(s);e&&t.push(e)}this.sendSubscribe(t)}online(){var e;this.lastCameOnline=Date.now(),this.state="online",null===(e=this.retrying)||void 0===e||e.abort(),this.socket.open()}offline(){var e;this.state="offline",null===(e=this.retrying)||void 0===e||e.abort(),this.socket.close()}shutdown(){this.inSharedWorker&&self.close()}get reconnectWindow(){let e=Date.now()-this.lastCameOnline<6e4;return 0===this.connectionCount||this.intentionallyDisconnected||e?0:1e4}socketDidOpen(){this.intentionallyDisconnected=!1,this.connectionCount++,this.socket.url=this.getUrlWithPresenceId(),this.sendSubscribe(this.subscriptions.topics())}socketDidClose(e,t,s){(void 0!==this.redeployEarlyReconnectTimeout&&clearTimeout(this.redeployEarlyReconnectTimeout),"Alive Reconnect"===s)?this.intentionallyDisconnected=!0:"Alive Redeploy"===s&&(this.intentionallyDisconnected=!0,this.redeployEarlyReconnectTimeout=setTimeout(()=>{this.intentionallyDisconnected=!0,this.socket.close(1e3,"Alive Redeploy Early Client Reconnect")},6e4*(3+22*Math.random())))}socketDidFinish(){"offline"!==this.state&&this.reconnect()}socketDidReceiveMessage(e,t){let s=JSON.parse(t);switch(s.e){case"ack":this.handleAck(s);break;case"msg":this.handleMessage(s)}}handleAck(e){for(let t of this.subscriptions.topics())t.offset=e.off}handleMessage(e){let t=e.ch,s=this.subscriptions.topic(t);if(s){if(s.offset=e.off,"e"in e.data){let s=this.presence.handleMessage(t,e.data);this.notifyPresenceChannel(t,s);return}e.data.wait||(e.data.wait=0),this.notify(this.subscriptions.subscribers(t),{channel:t,type:"message",data:e.data})}}notifyPresenceChannel(e,t){var s,n;let i=new Map;for(let e of t){let{userId:t,metadata:s,presenceKey:n}=e,a=i.get(t)||{userId:t,isOwnUser:t===this.userId,metadata:[]};if(n!==this.presenceKey){for(let e of s){if(r.Z in e){!1!==a.isIdle&&(a.isIdle=Boolean(e[r.Z]));continue}a.metadata.push(e)}i.set(t,a)}}for(let t of this.subscriptions.subscribers(e)){let r=this.userId,a=Array.from(i.values()).filter(e=>e.userId!==r),o=null!==(n=null===(s=i.get(this.userId))||void 0===s?void 0:s.metadata)&&void 0!==n?n:[],c=this.presenceMetadata.getChannelMetadata(e,{subscriber:t,markAllAsLocal:!this.inSharedWorker});this.notify([t],{channel:e,type:"presence",data:[{userId:r,isOwnUser:!0,metadata:[...o,...c]},...a]})}}async reconnect(){if(!this.retrying)try{this.retrying=new AbortController;let e=await (0,l.X)(this.getUrl,1/0,this.maxReconnectBackoff,this.retrying.signal);e?(this.url=e,this.socket=this.connect()):this.shutdown()}catch(e){if("AbortError"!==e.name)throw e}finally{this.retrying=null}}getUrlWithPresenceId(){let e=new URL(this.url,self.location.origin);return e.searchParams.set("shared",this.inSharedWorker.toString()),e.searchParams.set("p",`${this.presenceId}.${this.connectionCount}`),e.toString()}connect(){let e=new a.Oo(this.getUrlWithPresenceId(),this,{timeout:4e3,attempts:7});return e.open(),e}sendSubscribe(e){let t=Array.from(e);for(let e of(0,c.o)(t,25)){let t={};for(let s of e)(0,i.A)(s.name)?t[s.signed]=JSON.stringify(this.presenceMetadata.getChannelMetadata(s.name)):t[s.signed]=s.offset;this.socket.send(JSON.stringify({subscribe:t}))}}sendUnsubscribe(e){let t=Array.from(e,e=>e.signed);for(let e of(0,c.o)(t,25))this.socket.send(JSON.stringify({unsubscribe:e}));for(let t of e)(0,i.A)(t.name)&&this.presence.clearChannel(t.name)}}},29871:(e,t,s)=>{function n(e){return new Promise((t,s)=>{let n=Error("aborted");n.name="AbortError",e.aborted?s(n):e.addEventListener("abort",()=>s(n))})}async function i(e,t){let s;let i=new Promise(t=>{s=self.setTimeout(t,e)});if(!t)return i;try{await Promise.race([i,n(t)])}catch(e){throw self.clearTimeout(s),e}}function r(e){return Math.floor(Math.random()*Math.floor(e))}async function a(e,t,s=1/0,a){let o=a?n(a):null;for(let n=0;n<t;n++)try{let t=o?Promise.race([e(),o]):e();return await t}catch(c){if("AbortError"===c.name||n===t-1)throw c;let e=1e3*Math.pow(2,n),o=r(.1*e);await i(Math.min(s,e+o),a)}throw Error("retry failed")}s.d(t,{X:()=>a})},21461:(e,t,s)=>{s.d(t,{A:()=>r.A,SubscriptionSet:()=>a.v,Topic:()=>o.Z,ZE:()=>i.Z,a2:()=>n.a,ah:()=>i.a});var n=s(27907),i=s(60835),r=s(81855),a=s(75658),o=s(72993)},80955:(e,t,s)=>{s.d(t,{o:()=>n});function*n(e,t){for(let s=0;s<e.length;s+=t)yield e.slice(s,s+t)}},60835:(e,t,s)=>{s.d(t,{Z:()=>n,a:()=>PresenceMetadataSet});let n="_i";function i(e){return Object.assign(Object.assign({},e),{isLocal:!0})}let PresenceMetadataForChannel=class PresenceMetadataForChannel{constructor(){this.subscriberMetadata=new Map}setMetadata(e,t){this.subscriberMetadata.set(e,t)}removeSubscribers(e){let t=!1;for(let s of e)t=this.subscriberMetadata.delete(s)||t;return t}getMetadata(e){if(!e){let e;let t=[];for(let s of this.subscriberMetadata.values())for(let i of s)if(n in i){let t=Boolean(i[n]);e=void 0===e?t:t&&e}else t.push(i);return void 0!==e&&t.push({[n]:e?1:0}),t}let t=[],{subscriber:s,markAllAsLocal:r}=e;for(let[e,n]of this.subscriberMetadata){let a=r||e===s,o=a?n.map(i):n;t.push(...o)}return t}hasSubscribers(){return this.subscriberMetadata.size>0}};let PresenceMetadataSet=class PresenceMetadataSet{constructor(){this.metadataByChannel=new Map}setMetadata({subscriber:e,channelName:t,metadata:s}){let n=this.metadataByChannel.get(t);n||(n=new PresenceMetadataForChannel,this.metadataByChannel.set(t,n)),n.setMetadata(e,s)}removeSubscribers(e){let t=new Set;for(let[s,n]of this.metadataByChannel){let i=n.removeSubscribers(e);i&&t.add(s),n.hasSubscribers()||this.metadataByChannel.delete(s)}return t}getChannelMetadata(e,t){let s=this.metadataByChannel.get(e);return(null==s?void 0:s.getMetadata(t))||[]}}},81855:(e,t,s)=>{function n(e,t){return`${e}:${t}`}function i(e){let[t,s]=e.p.split(".");return{userId:e.u,presenceKey:n(e.u,t),connectionCount:Number(s),metadata:e.m||[]}}function r(e){return e.startsWith("presence-")}s.d(t,{A:()=>r,Hw:()=>n,k2:()=>AlivePresence});let PresenceChannel=class PresenceChannel{constructor(){this.presenceItems=new Map}shouldUsePresenceItem(e){let t=this.presenceItems.get(e.presenceKey);return!t||t.connectionCount<=e.connectionCount}addPresenceItem(e){this.shouldUsePresenceItem(e)&&this.presenceItems.set(e.presenceKey,e)}removePresenceItem(e){this.shouldUsePresenceItem(e)&&this.presenceItems.delete(e.presenceKey)}replacePresenceItems(e){for(let t of(this.presenceItems.clear(),e))this.addPresenceItem(t)}getPresenceItems(){return Array.from(this.presenceItems.values())}};let AlivePresence=class AlivePresence{constructor(){this.presenceChannels=new Map}getPresenceChannel(e){let t=this.presenceChannels.get(e)||new PresenceChannel;return this.presenceChannels.set(e,t),t}handleMessage(e,t){let s=this.getPresenceChannel(e);switch(t.e){case"pf":s.replacePresenceItems(t.d.map(i));break;case"pa":s.addPresenceItem(i(t.d));break;case"pr":s.removePresenceItem(i(t.d))}return this.getChannelItems(e)}getChannelItems(e){let t=this.getPresenceChannel(e);return t.getPresenceItems()}clearChannel(e){this.presenceChannels.delete(e)}}},75658:(e,t,s)=>{s.d(t,{v:()=>SubscriptionSet});var n=s(61268);let SubscriptionSet=class SubscriptionSet{constructor(){this.subscriptions=new n.Z,this.signatures=new Map}add(...e){let t=[];for(let{subscriber:s,topic:n}of e)this.subscriptions.has(n.name)||(t.push(n),this.signatures.set(n.name,n)),this.subscriptions.set(n.name,s);return t}delete(...e){let t=[];for(let{subscriber:s,topic:n}of e){let e=this.subscriptions.delete(n.name,s);e&&!this.subscriptions.has(n.name)&&(t.push(n),this.signatures.delete(n.name))}return t}drain(...e){let t=[];for(let s of e)for(let e of this.subscriptions.drain(s)){let s=this.signatures.get(e);this.signatures.delete(e),t.push(s)}return t}topics(){return this.signatures.values()}topic(e){return this.signatures.get(e)||null}subscribers(e){return this.subscriptions.get(e).values()}}},72993:(e,t,s)=>{s.d(t,{Z:()=>Topic});let Topic=class Topic{constructor(e,t){this.name=e,this.signed=t,this.offset=""}static parse(e){let[t,s]=e.split("--");if(!t||!s)return null;let n=JSON.parse(atob(t));return n.c&&n.t?new Topic(n.c,e):null}}},61268:(e,t,s)=>{s.d(t,{Z:()=>MultiMap});let MultiMap=class MultiMap{constructor(e){if(this.map=new Map,e)for(let[t,s]of e)this.set(t,s)}get(e){let t=this.map.get(e);return t||new Set}set(e,t){let s=this.map.get(e);return s||(s=new Set,this.map.set(e,s)),s.add(t),this}has(e){return this.map.has(e)}delete(e,t){let s=this.map.get(e);if(!s)return!1;if(!t)return this.map.delete(e);let n=s.delete(t);return s.size||this.map.delete(e),n}drain(e){let t=[];for(let s of this.keys())this.delete(s,e)&&!this.has(s)&&t.push(s);return t}keys(){return this.map.keys()}values(){return this.map.values()}entries(){return this.map.entries()}[Symbol.iterator](){return this.entries()}clear(){this.map.clear()}get size(){return this.map.size}}},16544:(e,t,s)=>{async function n(e,t){let s;let n=new Promise((t,n)=>{s=self.setTimeout(()=>n(Error("timeout")),e)});if(!t)return n;try{await Promise.race([n,a(t)])}catch(e){throw self.clearTimeout(s),e}}async function i(e,t){let s;let n=new Promise(t=>{s=self.setTimeout(t,e)});if(!t)return n;try{await Promise.race([n,a(t)])}catch(e){throw self.clearTimeout(s),e}}async function r(e,t,s=1/0,n){let r=n?a(n):null;for(let a=0;a<t;a++)try{let t=r?Promise.race([e(),r]):e();return await t}catch(c){if("AbortError"===c.name||a===t-1)throw c;let e=1e3*Math.pow(2,a),r=o(.1*e);await i(Math.min(s,e+r),n)}throw Error("retry failed")}function a(e){return new Promise((t,s)=>{let n=Error("aborted");n.name="AbortError",e.aborted?s(n):e.addEventListener("abort",()=>s(n))})}function o(e){return Math.floor(Math.random()*Math.floor(e))}async function c(e,t,s){let i=new WebSocket(e),r=u(i);try{return await Promise.race([r,n(t,s)]),i}catch(e){throw l(r),e}}async function l(e){try{let t=await e;t.close()}catch(e){}}function h(e,t){let s=()=>c(e,t.timeout,t.signal);return r(s,t.attempts,t.maxDelay,t.signal)}function u(e){return new Promise((t,s)=>{e.readyState===WebSocket.OPEN?t(e):(e.onerror=()=>{e.onerror=null,e.onopen=null,s(Error("connect failed"))},e.onopen=()=>{e.onerror=null,e.onopen=null,t(e)})})}s.d(t,{Oo:()=>StableSocket});let StableSocket=class StableSocket{constructor(e,t,s){this.socket=null,this.opening=null,this.url=e,this.delegate=t,this.policy=s}async open(){if(this.opening||this.socket)return;this.opening=new AbortController;let e=Object.assign(Object.assign({},this.policy),{signal:this.opening.signal});try{this.socket=await h(this.url,e)}catch(e){this.delegate.socketDidFinish(this);return}finally{this.opening=null}this.socket.onclose=e=>{this.socket=null,this.delegate.socketDidClose(this,e.code,e.reason);let t=this.delegate.socketShouldRetry?!this.delegate.socketShouldRetry(this,e.code):f(e.code);t?this.delegate.socketDidFinish(this):setTimeout(()=>this.open(),d(100,100+(this.delegate.reconnectWindow||50)))},this.socket.onmessage=e=>{this.delegate.socketDidReceiveMessage(this,e.data)},this.delegate.socketDidOpen(this)}close(e,t){this.opening?(this.opening.abort(),this.opening=null):this.socket&&(this.socket.onclose=null,this.socket.close(e,t),this.socket=null,this.delegate.socketDidClose(this,e,t),this.delegate.socketDidFinish(this))}send(e){this.socket&&this.socket.send(e)}isOpen(){return!!this.socket}};function d(e,t){return Math.random()*(t-e)+e}function f(e){return e===p||e===m}let p=1008,m=1011}}]);
//# sourceMappingURL=vendors-node_modules_github_mini-throttle_dist_index_js-node_modules_github_alive-client_dist-bf5aa2-aa134b5f4231.js.map