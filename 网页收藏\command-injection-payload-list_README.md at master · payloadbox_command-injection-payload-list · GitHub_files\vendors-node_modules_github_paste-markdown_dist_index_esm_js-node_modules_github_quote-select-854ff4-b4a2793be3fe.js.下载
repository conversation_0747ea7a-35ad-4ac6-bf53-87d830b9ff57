"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_paste-markdown_dist_index_esm_js-node_modules_github_quote-select-854ff4"],{52769:(e,t,n)=>{function r(e,t){var n,r,i;let o=e.value.slice(0,null!==(n=e.selectionStart)&&void 0!==n?n:void 0),a=e.value.slice(null!==(r=e.selectionEnd)&&void 0!==r?r:void 0),l=!0;e.contentEditable="true";try{l=document.execCommand("insertText",!1,t)}catch(e){l=!1}if(e.contentEditable="false",l&&!e.value.slice(0,null!==(i=e.selectionStart)&&void 0!==i?i:void 0).endsWith(t)&&(l=!1),!l){try{document.execCommand("ms-beginUndoUnit")}catch(e){}e.value=o+t+a;try{document.execCommand("ms-endUndoUnit")}catch(e){}e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!0}))}}n.d(t,{Ld:()=>en});let i=new WeakMap;function o(e){let{currentTarget:t}=e,n="KeyV"===e.code&&(e.ctrlKey||e.metaKey)&&e.shiftKey;(n||n&&e.altKey)&&i.set(t,!0)}function a(e){let{currentTarget:t}=e;i.delete(t)}function l(e){var t;let n=null!==(t=i.get(e))&&void 0!==t&&t;return n}function s(e,t,n){for(let r of(e.addEventListener("keydown",o),t))r(e,n);e.addEventListener("paste",a)}function u(e){e.removeEventListener("keydown",o),e.removeEventListener("paste",a)}function c(e){e.addEventListener("paste",f)}function d(e){e.removeEventListener("paste",f)}function f(e){let t=e.clipboardData,{currentTarget:n}=e;if(l(n)||!t||!g(t))return;let i=e.currentTarget;if(!(i instanceof HTMLTextAreaElement)||h(i))return;let o=t.getData("text/plain"),a=t.getData("text/html"),s=a.replace(/\u00A0/g," ").replace(/\uC2A0/g," ");if(!a||!(o=o.trim()))return;let u=new DOMParser,c=u.parseFromString(s,"text/html"),d=c.createTreeWalker(c.body,NodeFilter.SHOW_ELEMENT,e=>e.parentNode&&v(e.parentNode)?NodeFilter.FILTER_REJECT:NodeFilter.FILTER_ACCEPT),f=p(o,d);f!==o&&(e.stopPropagation(),e.preventDefault(),r(i,f))}function p(e,t){var n;let r=t.firstChild(),i=e,o=0,a=0;for(;r&&a<1e4;){a++;let e=v(r)?(r.textContent||"").replace(/[\t\n\r ]+/g," "):(null===(n=r.firstChild)||void 0===n?void 0:n.wholeText)||"";if(m(e)){r=t.nextNode();continue}let l=i.indexOf(e,o);if(l>=0){if(v(r)){let t=b(r,e);i=i.slice(0,l)+t+i.slice(l+e.length),o=l+t.length}else o=l+e.length}r=t.nextNode()}return 1e4===a?e:i}function h(e){let t=e.selectionStart||0;if(0===t)return!1;let n=e.value.substring(t-1,t);return"@"===n}function m(e){return!e||(null==e?void 0:e.trim().length)===0}function v(e){var t;return(null===(t=e.tagName)||void 0===t?void 0:t.toLowerCase())==="a"&&e.hasAttribute("href")}function g(e){return e.types.includes("text/html")}function b(e,t){let n=e.href||"";return T(e)?t:E(e)||y(n,t)?n:`[${t}](${n})`}function E(e){return e.className.indexOf("commit-link")>=0||!!e.getAttribute("data-hovercard-type")&&"user"!==e.getAttribute("data-hovercard-type")}function y(e,t){return e="/"===e.slice(-1)?e.slice(0,-1):e,t="/"===t.slice(-1)?t.slice(0,-1):t,e.toLowerCase()===t.toLowerCase()}function T(e){var t;return(null===(t=e.textContent)||void 0===t?void 0:t.slice(0,1))==="@"&&"user"===e.getAttribute("data-hovercard-type")}function x(e){e.addEventListener("dragover",A),e.addEventListener("drop",w),e.addEventListener("paste",C)}function L(e){e.removeEventListener("dragover",A),e.removeEventListener("drop",w),e.removeEventListener("paste",C)}function w(e){let t=e.dataTransfer;if(!t||k(t)||!H(t))return;let n=N(t);if(!n.some(M))return;e.stopPropagation(),e.preventDefault();let i=e.currentTarget;i instanceof HTMLTextAreaElement&&r(i,n.map(S).join(""))}function A(e){let t=e.dataTransfer;t&&(t.dropEffect="link")}function C(e){let{currentTarget:t}=e;if(l(t))return;let n=e.clipboardData;if(!n||!H(n))return;let i=N(n);if(!i.some(M))return;e.stopPropagation(),e.preventDefault();let o=e.currentTarget;o instanceof HTMLTextAreaElement&&r(o,i.map(S).join(""))}function S(e){return M(e)?`
![](${e})
`:e}function k(e){return Array.from(e.types).indexOf("Files")>=0}function H(e){return Array.from(e.types).indexOf("text/uri-list")>=0}function N(e){return(e.getData("text/uri-list")||"").split("\r\n")}let D=/\.(gif|png|jpe?g)$/i;function M(e){return D.test(e)}let I=new WeakMap;function O(e,t){var n;I.set(e,(null===(n=null==t?void 0:t.defaultPlainTextPaste)||void 0===n?void 0:n.urlLinks)===!0),e.addEventListener("paste",P)}function $(e){e.removeEventListener("paste",P)}function P(e){var t;let{currentTarget:n}=e,i=null!==(t=I.get(n))&&void 0!==t&&t,o=l(n);if(!i&&o||i&&!o)return;let a=e.clipboardData;if(!a||!F(a))return;let s=e.currentTarget;if(!(s instanceof HTMLTextAreaElement))return;let u=a.getData("text/plain");if(!u||!W(u)||R(s))return;let c=s.value.substring(s.selectionStart,s.selectionEnd);c.length&&(W(c.trim())||(e.stopPropagation(),e.preventDefault(),r(s,q(c,u.trim()))))}function F(e){return Array.from(e.types).includes("text/plain")}function R(e){let t=e.selectionStart||0;if(!(t>1))return!1;{let n=e.value.substring(t-2,t);return"]("===n}}function q(e,t){return`[${e}](${t})`}function W(e){try{let t=new URL(e);return _(t.href).trim()===_(e).trim()}catch(e){return!1}}function _(e){return e.endsWith("/")?e.slice(0,e.length-1):e}function j(e){e.addEventListener("dragover",U),e.addEventListener("drop",B),e.addEventListener("paste",z)}function K(e){e.removeEventListener("dragover",U),e.removeEventListener("drop",B),e.removeEventListener("paste",z)}function B(e){let t=e.dataTransfer;if(!t||X(t))return;let n=V(t);if(!n)return;e.stopPropagation(),e.preventDefault();let i=e.currentTarget;i instanceof HTMLTextAreaElement&&r(i,n)}function U(e){let t=e.dataTransfer;t&&(t.dropEffect="copy")}function z(e){let{currentTarget:t}=e;if(l(t)||!e.clipboardData)return;let n=V(e.clipboardData);if(!n)return;e.stopPropagation(),e.preventDefault();let i=e.currentTarget;i instanceof HTMLTextAreaElement&&r(i,n)}function X(e){return Array.from(e.types).indexOf("Files")>=0}function Y(e){let t=(e.textContent||"").trim().replace(/\|/g,"\\|").replace(/\n/g," ");return t||"\xa0"}function J(e){return Array.from(e.querySelectorAll("td, th")).map(Y)}function Q(e){let t=Array.from(e.querySelectorAll("tr")),n=t.shift();if(!n)return"";let r=J(n),i=r.map(()=>"--"),o=`${r.join(" | ")}
${i.join(" | ")}
`,a=t.map(e=>Array.from(e.querySelectorAll("td")).map(Y).join(" | ")).join("\n");return`
${o}${a}

`}function V(e){if(-1===Array.from(e.types).indexOf("text/html"))return;let t=e.getData("text/html");if(!/<table/i.test(t))return;let n=new DOMParser,r=n.parseFromString(t,"text/html"),i=r.querySelector("table");if(!(i=!i||i.closest("[data-paste-markdown-skip]")?null:i))return;let o=Q(i);return t.replace(/<meta.*?>/,"").replace(/<table[.\S\s]*<\/table>/,`
${o}`)}function G(e){e.addEventListener("paste",ee)}function Z(e){e.removeEventListener("paste",ee)}function ee(e){let{currentTarget:t}=e;if(l(t))return;let n=e.clipboardData;if(!n||!et(n))return;let i=e.currentTarget;if(!(i instanceof HTMLTextAreaElement))return;let o=n.getData("text/x-gfm");o&&(e.stopPropagation(),e.preventDefault(),r(i,o))}function et(e){return Array.from(e.types).indexOf("text/x-gfm")>=0}function en(e,t){return s(e,[j,x,O,G,c],t),{unsubscribe:()=>{u(e),K(e),d(e),L(e),$(e),Z(e)}}}},55498:(e,t,n)=>{function r(e){let t=e.parentNode;if(null===t||!(t instanceof HTMLElement))throw Error();let n=0;t instanceof HTMLOListElement&&1!==t.start&&(n=t.start-1);let r=t.children;for(let t=0;t<r.length;++t)if(r[t]===e)return n+t;return n}function i(e){if(e instanceof HTMLAnchorElement&&1===e.childNodes.length){let t=e.childNodes[0];if(t instanceof HTMLImageElement)return t.src===e.href}return!1}function o(e){return"IMG"===e.nodeName||null!=e.firstChild}function a(e){return"INPUT"===e.nodeName&&e instanceof HTMLInputElement&&"checkbox"===e.type}n.d(t,{I:()=>MarkdownQuote,p:()=>Quote});let l=0;function s(e){let t=e.childNodes[0],n=e.childNodes[1];return!!t&&e.childNodes.length<3&&("OL"===t.nodeName||"UL"===t.nodeName)&&(!n||n.nodeType===Node.TEXT_NODE&&!(n.textContent||"").trim())}function u(e){return e.replace(/&/g,"&amp;").replace(/'/g,"&apos;").replace(/"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}let c={INPUT:e=>e instanceof HTMLInputElement&&e.checked?"[x] ":"[ ] ",CODE(e){let t=e.textContent||"";return e.parentNode&&"PRE"===e.parentNode.nodeName?(e.textContent=`\`\`\`
${t.replace(/\n+$/,"")}
\`\`\`

`,e):t.indexOf("`")>=0?`\`\` ${t} \`\``:`\`${t}\``},P(e){let t=document.createElement("p"),n=e.textContent||"";return t.textContent=n.replace(/<(\/?)(pre|strong|weak|em)>/g,"\\<$1$2\\>"),t},STRONG:e=>`**${e.textContent||""}**`,EM:e=>`_${e.textContent||""}_`,DEL:e=>`~${e.textContent||""}~`,BLOCKQUOTE(e){let t=(e.textContent||"").trim().replace(/^/gm,"> "),n=document.createElement("pre");return n.textContent=`${t}

`,n},A(e){let t=e.textContent||"",n=e.getAttribute("href");return/^https?:/.test(t)&&t===n?t:n?`[${t}](${n})`:t},IMG(e){let t=e.getAttribute("alt")||"",n=e.getAttribute("src");if(!n)throw Error();let r=e.hasAttribute("width")?` width="${u(e.getAttribute("width")||"")}"`:"",i=e.hasAttribute("height")?` height="${u(e.getAttribute("height")||"")}"`:"";return r||i?`<img alt="${u(t)}"${r}${i} src="${u(n)}">`:`![${t}](${n})`},LI(e){let t=e.parentNode;if(!t)throw Error();let n="";if(!s(e)){if("OL"===t.nodeName){if(l>0&&!t.previousSibling){let t=r(e)+l+1;n=`${t}\\. `}else n=`${r(e)+1}. `}else n="* "}let i=n.replace(/\S/g," "),o=(e.textContent||"").trim().replace(/^/gm,i),a=document.createElement("pre");return a.textContent=o.replace(i,n),a},OL(e){let t=document.createElement("li");return t.appendChild(document.createElement("br")),e.append(t),e},H1(e){let t=parseInt(e.nodeName.slice(1));return e.prepend(`${Array(t+1).join("#")} `),e},UL:e=>e};c.UL=c.OL;for(let e=2;e<=6;++e)c[`H${e}`]=c.H1;function d(e){let t=document.createNodeIterator(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e.nodeName in c&&!i(e)&&(o(e)||a(e))?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),n=[],r=t.nextNode();for(;r;)r instanceof HTMLElement&&n.push(r),r=t.nextNode();for(let e of(n.reverse(),n))e.replaceWith(c[e.nodeName](e))}function f(e,t){let n=e.startContainer;if(!n||!n.parentNode||!(n.parentNode instanceof HTMLElement))throw Error("the range must start within an HTMLElement");let i=n.parentNode,o=e.cloneContents();if(t){let e=o.querySelector(t);e&&(o=document.createDocumentFragment()).appendChild(e)}l=0;let a=i.closest("li"),s=i.closest("pre");if(s){let e=document.createElement("pre");e.appendChild(o),(o=document.createDocumentFragment()).appendChild(e)}else if(a&&a.parentNode&&("OL"===a.parentNode.nodeName&&(l=r(a)),!o.querySelector("li"))){let e=document.createElement("li");if(!a.parentNode)throw Error();let t=document.createElement(a.parentNode.nodeName);e.appendChild(o),t.appendChild(e),(o=document.createDocumentFragment()).appendChild(t)}return o}let Quote=class Quote{constructor(){this.selection=window.getSelection()}closest(e){let t=this.range.startContainer,n=t instanceof Element?t:t.parentElement;return n?n.closest(e):null}get active(){var e;return((null===(e=this.selection)||void 0===e?void 0:e.rangeCount)||0)>0}get range(){var e;return(null===(e=this.selection)||void 0===e?void 0:e.rangeCount)?this.selection.getRangeAt(0):new Range}set range(e){var t,n;null===(t=this.selection)||void 0===t||t.removeAllRanges(),null===(n=this.selection)||void 0===n||n.addRange(e)}get selectionText(){var e;return(null===(e=this.selection)||void 0===e?void 0:e.toString().trim())||""}get quotedText(){return`> ${this.selectionText.replace(/\n/g,"\n> ")}

`}select(e){this.selection&&(this.selection.removeAllRanges(),this.selection.selectAllChildren(e))}insert(e){e.value?e.value=`${e.value}

${this.quotedText}`:e.value=this.quotedText,e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1})),e.focus(),e.selectionStart=e.value.length,e.scrollTop=e.scrollHeight}};let MarkdownQuote=class MarkdownQuote extends Quote{constructor(e="",t){super(),this.scopeSelector=e,this.callback=t}get selectionText(){var e,t;if(!this.selection)return"";let n=f(this.range,null!==(e=this.scopeSelector)&&void 0!==e?e:"");null===(t=this.callback)||void 0===t||t.call(this,n),d(n);let r=document.body;if(!r)return"";let i=document.createElement("div");i.appendChild(n),i.style.cssText="position:absolute;left:-9999px;",r.appendChild(i);let o="";try{let e=document.createRange();e.selectNodeContents(i),this.selection.removeAllRanges(),this.selection.addRange(e),o=this.selection.toString(),this.selection.removeAllRanges(),e.detach()}finally{r.removeChild(i)}return o.trim()}}},65935:(e,t,n)=>{let r;function i(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}function o(e){let t=new URLSearchParams,n=new FormData(e).entries();for(let[e,r]of[...n])t.append(e,r.toString());return t.toString()}n.d(t,{AC:()=>d,rK:()=>c,uT:()=>u});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function a(){let e,t;let n=new Promise(function(n,r){e=n,t=r});return[n,e,t]}let l=[],s=[];function u(e){l.push(e)}function c(e){s.push(e)}function d(e,t){r||(r=new Map,"undefined"!=typeof document&&document.addEventListener("submit",p));let n=r.get(e)||[];r.set(e,[...n,t])}function f(e){let t=[];for(let n of r.keys())if(e.matches(n)){let e=r.get(n)||[];t.push(...e)}return t}function p(e){if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let t=e.target,n=f(t);if(0===n.length)return;let r=m(t),[i,o,u]=a();e.preventDefault(),h(n,t,r,i).then(async e=>{if(e){for(let e of s)await e(t);v(r).then(o,u).catch(()=>{}).then(()=>{for(let e of l)e(t)})}else t.submit()},e=>{t.submit(),setTimeout(()=>{throw e})})}async function h(e,t,n,r){let i=!1;for(let o of e){let[e,l]=a(),s=()=>(i=!0,l(),r),u={text:s,json:()=>(n.headers.set("Accept","application/json"),s()),html:()=>(n.headers.set("Accept","text/html"),s())};await Promise.race([e,o(t,u,n)])}return i}function m(e){let t={method:e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===t.method.toUpperCase()){let n=o(e);n&&(t.url+=(~t.url.indexOf("?")?"&":"?")+n)}else t.body=new FormData(e);return t}async function v(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=this,t=JSON.parse(e.text);return delete e.json,e.json=t,e.json},get html(){let e=this;return delete e.html,e.html=i(document,e.text),e.html}},r=await t.text();if(n.text=r,t.ok)return n;throw new ErrorWithResponse("request failed",n)}},54430:(e,t,n)=>{function r(e){var t=null,n=!1,r=void 0,i=void 0,o=void 0;function a(t){if(r!==t.clientX||i!==t.clientY){var l=e.style.height;o&&o!==l&&(n=!0,e.style.maxHeight="",e.removeEventListener("mousemove",a)),o=l}r=t.clientX,i=t.clientY}var l=e.ownerDocument,s=l.documentElement;function u(){if(!n&&e.value!==t&&(!(e.offsetWidth<=0)||!(e.offsetHeight<=0))){var r=function(){for(var t=0,n=e;n!==l.body&&null!==n;)t+=n.offsetTop||0,n=n.offsetParent;var r=t-l.defaultView.pageYOffset,i=s.clientHeight-(r+e.offsetHeight);return{top:r,bottom:i}}(),i=r.top,a=r.bottom;if(!(i<0)&&!(a<0)){var u=Number(getComputedStyle(e).height.replace(/px/,""))+a;e.style.maxHeight=u-100+"px";var c=e.parentElement;if(c instanceof HTMLElement){var d=c.style.height;c.style.height=getComputedStyle(c).height,e.style.height="auto",e.style.height=e.scrollHeight+"px",c.style.height=d,o=e.style.height}t=e.value}}}function c(){n=!1,e.style.height="",e.style.maxHeight=""}e.addEventListener("mousemove",a),e.addEventListener("input",u),e.addEventListener("change",u);var d=e.form;return d&&d.addEventListener("reset",c),e.value&&u(),{unsubscribe:function(){e.removeEventListener("mousemove",a),e.removeEventListener("input",u),e.removeEventListener("change",u),d&&d.removeEventListener("reset",c)}}}n.d(t,{Z:()=>i});let i=r},96776:(e,t,n)=>{function r(e,t){return i(o(e),t)}function i(e,t){var n=e;if(!n)return Promise.resolve(t());var r=n.ownerDocument.documentElement,i=function(e){for(var t=[];e;){var n=e.getBoundingClientRect(),r=n.top,i=n.left;t.push({element:e,top:r,left:i}),e=e.parentElement}return t}(n);return Promise.resolve(t()).then(function(e){var t=function(e){for(var t=0;t<e.length;t++){var n=e[t];if(r.contains(n.element))return n}}(i);if(t){n=t.element;var o=t.top,l=t.left,s=n.getBoundingClientRect(),u=s.top;a(n,s.left-l,u-o)}return e})}function o(e){if(e.activeElement!==e.body)return e.activeElement;var t=e.querySelectorAll(":hover"),n=t.length;if(n)return t[n-1]}function a(e,t,n){var r=e.ownerDocument,i=r.defaultView;function o(e){return e.offsetParent?{top:e.scrollTop,left:e.scrollLeft}:{top:i.pageYOffset,left:i.pageXOffset}}function a(e){var t=e;if(t.offsetParent&&t!==r.body){for(;t!==r.body;){if(!t.parentElement)return;t=t.parentElement;var n=i.getComputedStyle(t),o=n.position,a=n.overflowY,l=n.overflowX;if("fixed"===o||"auto"===a||"auto"===l||"scroll"===a||"scroll"===l)break}return t}}for(var l=a(e),s=0,u=0;l;){var c=function(e,t,n){if(0===t&&0===n)return[0,0];var a=o(e),l=a.top+n,s=a.left+t;e===r||e===i||e===r.documentElement||e===r.body?r.defaultView.scrollTo(s,l):(e.scrollTop=l,e.scrollLeft=s);var u=o(e);return[u.left-a.left,u.top-a.top]}(l,t-s,n-u);if(s+=c[0],u+=c[1],s===t&&u===n)break;l=a(l)}}n.d(t,{_8:()=>r,uQ:()=>i})},58797:(e,t,n)=>{n.d(t,{Z:()=>r});function r(e){let t=!1,n=null;function r(e,t,n,r=!1){t instanceof HTMLInputElement&&(t.indeterminate=r,t.checked!==n&&(t.checked=n,setTimeout(()=>{let n=new CustomEvent("change",{bubbles:!0,cancelable:!1,detail:{relatedTarget:e}});t.dispatchEvent(n)})))}function i(i){let o=i.target;o instanceof Element&&(o.hasAttribute("data-check-all")?function(t){if(t instanceof CustomEvent&&t.detail){let{relatedTarget:e}=t.detail;if(e&&e.hasAttribute("data-check-all-item"))return}let i=t.target;if(i instanceof HTMLInputElement){for(let t of(n=null,e.querySelectorAll("[data-check-all-item]")))r(i,t,i.checked);i.indeterminate=!1,a()}}(i):o.hasAttribute("data-check-all-item")&&function(i){if(i instanceof CustomEvent&&i.detail){let{relatedTarget:e}=i.detail;if(e&&(e.hasAttribute("data-check-all")||e.hasAttribute("data-check-all-item")))return}let o=i.target;if(!(o instanceof HTMLInputElement))return;let l=Array.from(e.querySelectorAll("[data-check-all-item]"));if(t&&n){let[e,t]=[l.indexOf(n),l.indexOf(o)].sort();for(let n of l.slice(e,+t+1||9e9))r(o,n,o.checked)}t=!1,n=o;let s=e.querySelector("[data-check-all]");if(s){let e=l.length,t=l.filter(e=>e instanceof HTMLInputElement&&e.checked).length;r(o,s,t===e,e>t&&t>0)}a()}(i))}function o(e){if(!(e.target instanceof Element))return;let n=e.target instanceof HTMLLabelElement&&e.target.control||e.target;n.hasAttribute("data-check-all-item")&&(t=e.shiftKey)}function a(){let t=e.querySelector("[data-check-all-count]");if(t){let n=e.querySelectorAll("[data-check-all-item]:checked").length;t.textContent=n.toString()}}return e.addEventListener("mousedown",o),e.addEventListener("change",i),{unsubscribe:()=>{e.removeEventListener("mousedown",o),e.removeEventListener("change",i)}}}},10160:(e,t,n)=>{n.d(t,{Z:()=>Combobox});let Combobox=class Combobox{constructor(e,t,{tabInsertsSuggestions:n,defaultFirstOption:i}={}){this.input=e,this.list=t,this.tabInsertsSuggestions=null==n||n,this.defaultFirstOption=null!=i&&i,this.isComposing=!1,t.id||(t.id=`combobox-${Math.random().toString().slice(2,6)}`),this.ctrlBindings=!!navigator.userAgent.match(/Macintosh/),this.keyboardEventHandler=e=>r(e,this),this.compositionEventHandler=e=>s(e,this),this.inputHandler=this.clearSelection.bind(this),e.setAttribute("role","combobox"),e.setAttribute("aria-controls",t.id),e.setAttribute("aria-expanded","false"),e.setAttribute("aria-autocomplete","list"),e.setAttribute("aria-haspopup","listbox")}destroy(){this.clearSelection(),this.stop(),this.input.removeAttribute("role"),this.input.removeAttribute("aria-controls"),this.input.removeAttribute("aria-expanded"),this.input.removeAttribute("aria-autocomplete"),this.input.removeAttribute("aria-haspopup")}start(){this.input.setAttribute("aria-expanded","true"),this.input.addEventListener("compositionstart",this.compositionEventHandler),this.input.addEventListener("compositionend",this.compositionEventHandler),this.input.addEventListener("input",this.inputHandler),this.input.addEventListener("keydown",this.keyboardEventHandler),this.list.addEventListener("click",i),this.indicateDefaultOption()}stop(){this.clearSelection(),this.input.setAttribute("aria-expanded","false"),this.input.removeEventListener("compositionstart",this.compositionEventHandler),this.input.removeEventListener("compositionend",this.compositionEventHandler),this.input.removeEventListener("input",this.inputHandler),this.input.removeEventListener("keydown",this.keyboardEventHandler),this.list.removeEventListener("click",i)}indicateDefaultOption(){var e;this.defaultFirstOption&&(null===(e=Array.from(this.list.querySelectorAll('[role="option"]:not([aria-disabled="true"])')).filter(l)[0])||void 0===e||e.setAttribute("data-combobox-option-default","true"))}navigate(e=1){let t=Array.from(this.list.querySelectorAll('[aria-selected="true"]')).filter(l)[0],n=Array.from(this.list.querySelectorAll('[role="option"]')).filter(l),r=n.indexOf(t);if(r===n.length-1&&1===e||0===r&&-1===e){this.clearSelection(),this.input.focus();return}let i=1===e?0:n.length-1;if(t&&r>=0){let t=r+e;t>=0&&t<n.length&&(i=t)}let o=n[i];if(o)for(let e of n)e.removeAttribute("data-combobox-option-default"),o===e?(this.input.setAttribute("aria-activedescendant",o.id),o.setAttribute("aria-selected","true"),u(this.list,o)):e.removeAttribute("aria-selected")}clearSelection(){for(let e of(this.input.removeAttribute("aria-activedescendant"),this.list.querySelectorAll('[aria-selected="true"]')))e.removeAttribute("aria-selected");this.indicateDefaultOption()}};function r(e,t){if(!e.shiftKey&&!e.metaKey&&!e.altKey&&(t.ctrlBindings||!e.ctrlKey)&&!t.isComposing)switch(e.key){case"Enter":o(t.input,t.list)&&e.preventDefault();break;case"Tab":t.tabInsertsSuggestions&&o(t.input,t.list)&&e.preventDefault();break;case"Escape":t.clearSelection();break;case"ArrowDown":t.navigate(1),e.preventDefault();break;case"ArrowUp":t.navigate(-1),e.preventDefault();break;case"n":t.ctrlBindings&&e.ctrlKey&&(t.navigate(1),e.preventDefault());break;case"p":t.ctrlBindings&&e.ctrlKey&&(t.navigate(-1),e.preventDefault());break;default:if(e.ctrlKey)break;t.clearSelection()}}function i(e){if(!(e.target instanceof Element))return;let t=e.target.closest('[role="option"]');t&&"true"!==t.getAttribute("aria-disabled")&&a(t,{event:e})}function o(e,t){let n=t.querySelector('[aria-selected="true"], [data-combobox-option-default="true"]');return!!n&&("true"===n.getAttribute("aria-disabled")||(n.click(),!0))}function a(e,t){e.dispatchEvent(new CustomEvent("combobox-commit",{bubbles:!0,detail:t}))}function l(e){return!e.hidden&&!(e instanceof HTMLInputElement&&"hidden"===e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}function s(e,t){t.isComposing="compositionstart"===e.type;let n=document.getElementById(t.input.getAttribute("aria-controls")||"");n&&t.clearSelection()}function u(e,t){c(e,t)||(e.scrollTop=t.offsetTop)}function c(e,t){let n=e.scrollTop,r=n+e.clientHeight,i=t.offsetTop,o=i+t.clientHeight;return i>=n&&o<=r}},8433:(e,t,n)=>{function r(e){let t="==".slice(0,(4-e.length%4)%4),n=e.replace(/-/g,"+").replace(/_/g,"/")+t,r=atob(n),i=new ArrayBuffer(r.length),o=new Uint8Array(i);for(let e=0;e<r.length;e++)o[e]=r.charCodeAt(e);return i}function i(e){let t=new Uint8Array(e),n="";for(let e of t)n+=String.fromCharCode(e);let r=btoa(n),i=r.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"");return i}n.d(t,{JO:()=>b,U2:()=>w,Ue:()=>L,Zh:()=>x,wz:()=>y});var o="copy",a="convert";function l(e,t,n){if(t===o)return n;if(t===a)return e(n);if(t instanceof Array)return n.map(n=>l(e,t[0],n));if(t instanceof Object){let r={};for(let[i,o]of Object.entries(t)){if(o.derive){let e=o.derive(n);void 0!==e&&(n[i]=e)}if(!(i in n)){if(o.required)throw Error(`Missing key: ${i}`);continue}if(null==n[i]){r[i]=null;continue}r[i]=l(e,o.schema,n[i])}return r}}function s(e,t){return{required:!0,schema:e,derive:t}}function u(e){return{required:!0,schema:e}}function c(e){return{required:!1,schema:e}}var d={type:u(o),id:u(a),transports:c(o)},f={appid:c(o),appidExclude:c(o),credProps:c(o)},p={appid:c(o),appidExclude:c(o),credProps:c(o)},h={publicKey:u({rp:u(o),user:u({id:u(a),name:u(o),displayName:u(o)}),challenge:u(a),pubKeyCredParams:u(o),timeout:c(o),excludeCredentials:c([d]),authenticatorSelection:c(o),attestation:c(o),extensions:c(f)}),signal:c(o)},m={type:u(o),id:u(o),rawId:u(a),authenticatorAttachment:c(o),response:u({clientDataJSON:u(a),attestationObject:u(a),transports:s(o,e=>{var t;return(null==(t=e.getTransports)?void 0:t.call(e))||[]})}),clientExtensionResults:s(p,e=>e.getClientExtensionResults())},v={mediation:c(o),publicKey:u({challenge:u(a),timeout:c(o),rpId:c(o),allowCredentials:c([d]),userVerification:c(o),extensions:c(f)}),signal:c(o)},g={type:u(o),id:u(o),rawId:u(a),authenticatorAttachment:c(o),response:u({clientDataJSON:u(a),authenticatorData:u(a),signature:u(a),userHandle:u(a)}),clientExtensionResults:s(p,e=>e.getClientExtensionResults())};function b(e){return l(r,h,e)}function E(e){return l(i,m,e)}function y(e){return l(r,v,e)}function T(e){return l(i,g,e)}function x(){return!!(navigator.credentials&&navigator.credentials.create&&navigator.credentials.get&&window.PublicKeyCredential)}async function L(e){let t=await navigator.credentials.create(e);return t.toJSON=()=>E(t),t}async function w(e){let t=await navigator.credentials.get(e);return t.toJSON=()=>T(t),t}},89900:(e,t,n)=>{n.d(t,{Z:()=>a});let r=["direction","boxSizing","width","height","overflowX","overflowY","borderTopWidth","borderRightWidth","borderBottomWidth","borderLeftWidth","borderStyle","paddingTop","paddingRight","paddingBottom","paddingLeft","fontStyle","fontVariant","fontWeight","fontStretch","fontSize","fontSizeAdjust","lineHeight","fontFamily","textAlign","textTransform","textIndent","textDecoration","letterSpacing","wordSpacing","tabSize","MozTabSize"],i="undefined"!=typeof window,o=i&&null!=window.mozInnerScreenX;function a(e,t,n){let i=n&&n.debug||!1;if(i){let e=document.querySelector("#input-textarea-caret-position-mirror-div");e&&e.parentNode.removeChild(e)}let a=document.createElement("div");a.id="input-textarea-caret-position-mirror-div",document.body.appendChild(a);let l=a.style,s=window.getComputedStyle?window.getComputedStyle(e):e.currentStyle,u="INPUT"===e.nodeName;for(let t of(l.whiteSpace="pre-wrap",u||(l.wordWrap="break-word"),l.position="absolute",i||(l.visibility="hidden"),r))if(u&&"lineHeight"===t){if("border-box"===s.boxSizing){let e=parseInt(s.height),t=parseInt(s.paddingTop)+parseInt(s.paddingBottom)+parseInt(s.borderTopWidth)+parseInt(s.borderBottomWidth),n=t+parseInt(s.lineHeight);e>n?l.lineHeight=`${e-t}px`:e===n?l.lineHeight=s.lineHeight:l.lineHeight=0}else l.lineHeight=s.height}else if(u||"width"!==t||"border-box"!==s.boxSizing)l[t]=s[t];else{let n=parseFloat(s.borderLeftWidth)+parseFloat(s.borderRightWidth),r=o?parseFloat(s[t])-n:e.clientWidth+n;l[t]=`${r}px`}o?e.scrollHeight>parseInt(s.height)&&(l.overflowY="scroll"):l.overflow="hidden",a.textContent=e.value.substring(0,t),u&&(a.textContent=a.textContent.replace(/\s/g,"\xa0"));let c=document.createElement("span");c.textContent=e.value.substring(t)||".",a.appendChild(c);let d={top:c.offsetTop+parseInt(s.borderTopWidth),left:c.offsetLeft+parseInt(s.borderLeftWidth),height:parseInt(s.lineHeight)};return i?c.style.backgroundColor="#aaa":document.body.removeChild(a),d}},38085:(e,t,n)=>{n.d(t,{Fu:()=>N,NO:()=>B,Yn:()=>j,a4:()=>y,mr:()=>z,mw:()=>L});var r,i,o,a,l,s=-1,u=function(e){addEventListener("pageshow",function(t){t.persisted&&(s=t.timeStamp,e(t))},!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},d=function(){var e=c();return e&&e.activationStart||0},f=function(e,t){var n=c(),r="navigate";return s>=0?r="back-forward-cache":n&&(r=document.prerendering||d()>0?"prerender":n.type.replace(/_/g,"-")),{name:e,value:void 0===t?-1:t,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(8999999999999*Math.random())+1e12),navigationType:r}},p=function(e,t,n){try{if(PerformanceObserver.supportedEntryTypes.includes(e)){var r=new PerformanceObserver(function(e){t(e.getEntries())});return r.observe(Object.assign({type:e,buffered:!0},n||{})),r}}catch(e){}},h=function(e,t){var n=function n(r){"pagehide"!==r.type&&"hidden"!==document.visibilityState||(e(r),t&&(removeEventListener("visibilitychange",n,!0),removeEventListener("pagehide",n,!0)))};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},m=function(e,t,n,r){var i,o;return function(a){var l;t.value>=0&&(a||r)&&((o=t.value-(i||0))||void 0===i)&&(i=t.value,t.delta=o,t.rating=(l=t.value)>n[1]?"poor":l>n[0]?"needs-improvement":"good",e(t))}},v=-1,g=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},b=function(){h(function(e){v=e.timeStamp},!0)},E=function(){return v<0&&(v=g(),b(),u(function(){setTimeout(function(){v=g(),b()},0)})),{get firstHiddenTime(){return v}}},y=function(e,t){t=t||{};var n,r=[1800,3e3],i=E(),o=f("FCP"),a=function(e){e.forEach(function(e){"first-contentful-paint"===e.name&&(s&&s.disconnect(),e.startTime<i.firstHiddenTime&&(o.value=Math.max(e.startTime-d(),0),o.entries.push(e),n(!0)))})},l=window.performance&&window.performance.getEntriesByName&&window.performance.getEntriesByName("first-contentful-paint")[0],s=l?null:p("paint",a);(l||s)&&(n=m(e,o,r,t.reportAllChanges),l&&a([l]),u(function(i){n=m(e,o=f("FCP"),r,t.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-i.timeStamp,n(!0)})})}))},T=!1,x=-1,L=function(e,t){t=t||{};var n=[.1,.25];T||(y(function(e){x=e.value}),T=!0);var r,i=function(t){x>-1&&e(t)},o=f("CLS",0),a=0,l=[],s=function(e){e.forEach(function(e){if(!e.hadRecentInput){var t=l[0],n=l[l.length-1];a&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(a+=e.value,l.push(e)):(a=e.value,l=[e]),a>o.value&&(o.value=a,o.entries=l,r())}})},c=p("layout-shift",s);c&&(r=m(i,o,n,t.reportAllChanges),h(function(){s(c.takeRecords()),r(!0)}),u(function(){a=0,x=-1,r=m(i,o=f("CLS",0),n,t.reportAllChanges)}))},w={passive:!0,capture:!0},A=new Date,C=function(e,t){r||(r=t,i=e,o=new Date,H(removeEventListener),S())},S=function(){if(i>=0&&i<o-A){var e={entryType:"first-input",name:r.type,target:r.target,cancelable:r.cancelable,startTime:r.timeStamp,processingStart:r.timeStamp+i};a.forEach(function(t){t(e)}),a=[]}},k=function(e){if(e.cancelable){var t,n,r,i=(e.timeStamp>1e12?new Date:performance.now())-e.timeStamp;"pointerdown"==e.type?(t=function(){C(i,e),r()},n=function(){r()},r=function(){removeEventListener("pointerup",t,w),removeEventListener("pointercancel",n,w)},addEventListener("pointerup",t,w),addEventListener("pointercancel",n,w)):C(i,e)}},H=function(e){["mousedown","keydown","touchstart","pointerdown"].forEach(function(t){return e(t,k,w)})},N=function(e,t){t=t||{};var n,o=[100,300],l=E(),s=f("FID"),c=function(e){e.startTime<l.firstHiddenTime&&(s.value=e.processingStart-e.startTime,s.entries.push(e),n(!0))},d=function(e){e.forEach(c)},v=p("first-input",d);n=m(e,s,o,t.reportAllChanges),v&&h(function(){d(v.takeRecords()),v.disconnect()},!0),v&&u(function(){n=m(e,s=f("FID"),o,t.reportAllChanges),a=[],i=-1,r=null,H(addEventListener),a.push(c),S()})},D=0,M=1/0,I=0,O=function(e){e.forEach(function(e){e.interactionId&&(M=Math.min(M,e.interactionId),D=(I=Math.max(I,e.interactionId))?(I-M)/7+1:0)})},$=function(){return l?D:performance.interactionCount||0},P=function(){"interactionCount"in performance||l||(l=p("event",O,{type:"event",buffered:!0,durationThreshold:0}))},F=0,R=function(){return $()-F},q=[],W={},_=function(e){var t=q[q.length-1],n=W[e.interactionId];if(n||q.length<10||e.duration>t.latency){if(n)n.entries.push(e),n.latency=Math.max(n.latency,e.duration);else{var r={id:e.interactionId,latency:e.duration,entries:[e]};W[r.id]=r,q.push(r)}q.sort(function(e,t){return t.latency-e.latency}),q.splice(10).forEach(function(e){delete W[e.id]})}},j=function(e,t){t=t||{};var n=[200,500];P();var r,i=f("INP"),o=function(e){e.forEach(function(e){e.interactionId&&_(e),"first-input"!==e.entryType||q.some(function(t){return t.entries.some(function(t){return e.duration===t.duration&&e.startTime===t.startTime})})||_(e)});var t,n=(t=Math.min(q.length-1,Math.floor(R()/50)),q[t]);n&&n.latency!==i.value&&(i.value=n.latency,i.entries=n.entries,r())},a=p("event",o,{durationThreshold:t.durationThreshold||40});r=m(e,i,n,t.reportAllChanges),a&&(a.observe({type:"first-input",buffered:!0}),h(function(){o(a.takeRecords()),i.value<0&&R()>0&&(i.value=0,i.entries=[]),r(!0)}),u(function(){q=[],F=$(),r=m(e,i=f("INP"),n,t.reportAllChanges)}))},K={},B=function(e,t){t=t||{};var n,r=[2500,4e3],i=E(),o=f("LCP"),a=function(e){var t=e[e.length-1];if(t){var r=Math.max(t.startTime-d(),0);r<i.firstHiddenTime&&(o.value=r,o.entries=[t],n())}},l=p("largest-contentful-paint",a);if(l){n=m(e,o,r,t.reportAllChanges);var s=function(){K[o.id]||(a(l.takeRecords()),l.disconnect(),K[o.id]=!0,n(!0))};["keydown","click"].forEach(function(e){addEventListener(e,s,{once:!0,capture:!0})}),h(s,!0),u(function(i){n=m(e,o=f("LCP"),r,t.reportAllChanges),requestAnimationFrame(function(){requestAnimationFrame(function(){o.value=performance.now()-i.timeStamp,K[o.id]=!0,n(!0)})})})}},U=function e(t){document.prerendering?addEventListener("prerenderingchange",function(){return e(t)},!0):"complete"!==document.readyState?addEventListener("load",function(){return e(t)},!0):setTimeout(t,0)},z=function(e,t){t=t||{};var n=[800,1800],r=f("TTFB"),i=m(e,r,n,t.reportAllChanges);U(function(){var o=c();if(o){if(r.value=Math.max(o.responseStart-d(),0),r.value<0||r.value>performance.now())return;r.entries=[o],i(!0),u(function(){(i=m(e,r=f("TTFB",0),n,t.reportAllChanges))(!0)})}})}}}]);
//# sourceMappingURL=vendors-node_modules_github_paste-markdown_dist_index_esm_js-node_modules_github_quote-select-854ff4-264de76ad42a.js.map