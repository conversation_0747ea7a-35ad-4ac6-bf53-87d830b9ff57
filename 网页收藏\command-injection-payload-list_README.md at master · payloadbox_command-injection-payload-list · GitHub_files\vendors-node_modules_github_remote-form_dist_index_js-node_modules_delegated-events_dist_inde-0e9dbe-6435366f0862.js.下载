"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-0e9dbe"],{65935:(e,t,n)=>{let r;function o(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}function i(e){let t=new URLSearchParams,n=new FormData(e).entries();for(let[e,r]of[...n])t.append(e,r.toString());return t.toString()}n.d(t,{AC:()=>d,rK:()=>u,uT:()=>l});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function s(){let e,t;let n=new Promise(function(n,r){e=n,t=r});return[n,e,t]}let a=[],c=[];function l(e){a.push(e)}function u(e){c.push(e)}function d(e,t){r||(r=new Map,"undefined"!=typeof document&&document.addEventListener("submit",f));let n=r.get(e)||[];r.set(e,[...n,t])}function h(e){let t=[];for(let n of r.keys())if(e.matches(n)){let e=r.get(n)||[];t.push(...e)}return t}function f(e){if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let t=e.target,n=h(t);if(0===n.length)return;let r=m(t),[o,i,l]=s();e.preventDefault(),p(n,t,r,o).then(async e=>{if(e){for(let e of c)await e(t);g(r).then(i,l).catch(()=>{}).then(()=>{for(let e of a)e(t)})}else t.submit()},e=>{t.submit(),setTimeout(()=>{throw e})})}async function p(e,t,n,r){let o=!1;for(let i of e){let[e,a]=s(),c=()=>(o=!0,a(),r),l={text:c,json:()=>(n.headers.set("Accept","application/json"),c()),html:()=>(n.headers.set("Accept","text/html"),c())};await Promise.race([e,i(t,l,n)])}return o}function m(e){let t={method:e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===t.method.toUpperCase()){let n=i(e);n&&(t.url+=(~t.url.indexOf("?")?"&":"?")+n)}else t.body=new FormData(e);return t}async function g(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=this,t=JSON.parse(e.text);return delete e.json,e.json=t,e.json},get html(){let e=this;return delete e.html,e.html=o(document,e.text),e.html}},r=await t.text();if(n.text=r,t.ok)return n;throw new ErrorWithResponse("request failed",n)}},59753:(e,t,n)=>{function r(){if(!(this instanceof r))return new r;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{f:()=>P,S:()=>O,on:()=>A});var o,i=window.document.documentElement,s=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.oMatchesSelector||i.msMatchesSelector;r.prototype.matchesSelector=function(e,t){return s.call(e,t)},r.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},r.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var c=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(c))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var l=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(l))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),r.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},o="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var u=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var n,r,o,i,s,a,c=(e=e.slice(0).concat(e.default)).length,l=t,d=[];do if(u.exec(""),(o=u.exec(l))&&(l=o[3],o[2]||!l)){for(n=0;n<c;n++)if(s=(a=e[n]).selector(o[1])){for(r=d.length,i=!1;r--;)if(d[r].index===a&&d[r].key===s){i=!0;break}i||d.push({index:a,key:s});break}}while(o)return d}function h(e,t){var n,r,o;for(n=0,r=e.length;n<r;n++)if(o=e[n],t.isPrototypeOf(o))return o}function f(e,t){return e.id-t.id}r.prototype.logDefaultIndexUsed=function(){},r.prototype.add=function(e,t){var n,r,i,s,a,c,l,u,f=this.activeIndexes,p=this.selectors,m=this.selectorObjects;if("string"==typeof e){for(r=0,m[(n={id:this.uid++,selector:e,data:t}).id]=n,l=d(this.indexes,e);r<l.length;r++)s=(u=l[r]).key,(a=h(f,i=u.index))||((a=Object.create(i)).map=new o,f.push(a)),i===this.indexes.default&&this.logDefaultIndexUsed(n),(c=a.map.get(s))||(c=[],a.map.set(s,c)),c.push(n);this.size++,p.push(e)}},r.prototype.remove=function(e,t){if("string"==typeof e){var n,r,o,i,s,a,c,l,u=this.activeIndexes,h=this.selectors=[],f=this.selectorObjects,p={},m=1==arguments.length;for(o=0,n=d(this.indexes,e);o<n.length;o++)for(r=n[o],i=u.length;i--;)if(a=u[i],r.index.isPrototypeOf(a)){if(c=a.map.get(r.key))for(s=c.length;s--;)(l=c[s]).selector===e&&(m||l.data===t)&&(c.splice(s,1),p[l.id]=!0);break}for(o in p)delete f[o],this.size--;for(o in f)h.push(f[o].selector)}},r.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,r,o,i,s,a,c,l={},u=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,r=d.length;t<r;t++)for(n=0,i=d[t],o=(s=this.matches(i)).length;n<o;n++)l[(c=s[n]).id]?a=l[c.id]:(a={id:c.id,selector:c.selector,data:c.data,elements:[]},l[c.id]=a,u.push(a)),a.elements.push(i);return u.sort(f)},r.prototype.matches=function(e){if(!e)return[];var t,n,r,o,i,s,a,c,l,u,d,h=this.activeIndexes,p={},m=[];for(t=0,o=h.length;t<o;t++)if(c=(a=h[t]).element(e)){for(n=0,i=c.length;n<i;n++)if(l=a.map.get(c[n]))for(r=0,s=l.length;r<s;r++)!p[d=(u=l[r]).id]&&this.matchesSelector(e,u.selector)&&(p[d]=!0,m.push(u))}return m.sort(f)};var p={},m={},g=new WeakMap,v=new WeakMap,y=new WeakMap,w=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function b(e,t,n){var r=e[t];return e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)},e}function x(e,t,n){var r=[],o=t;do{if(1!==o.nodeType)break;var i=e.matches(o);if(i.length){var s={node:o,observers:i};n?r.unshift(s):r.push(s)}}while(o=o.parentElement)return r}function E(){g.set(this,!0)}function k(){g.set(this,!0),v.set(this,!0)}function S(){return y.get(this)||null}function _(e,t){w&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||w.get})}function j(e){try{return e.eventPhase,!0}catch(e){return!1}}function T(e){if(j(e)){var t=(1===e.eventPhase?m:p)[e.type];if(t){var n=x(t,e.target,1===e.eventPhase);if(n.length){b(e,"stopPropagation",E),b(e,"stopImmediatePropagation",k),_(e,S);for(var r=0,o=n.length;r<o&&!g.get(e);r++){var i=n[r];y.set(e,i.node);for(var s=0,a=i.observers.length;s<a&&!v.get(e);s++)i.observers[s].data.call(i.node,e)}y.delete(e),_(e)}}}}function A(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!o.capture,s=i?m:p,a=s[e];a||(a=new r,s[e]=a,document.addEventListener(e,T,i)),a.add(t,n)}function O(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!!r.capture,i=o?m:p,s=i[e];s&&(s.remove(t,n),s.size||(delete i[e],document.removeEventListener(e,T,o)))}function P(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},86058:(e,t,n)=>{function r(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}function o(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}function i(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}function s(){return navigator.languages?navigator.languages.join(","):navigator.language||""}function a(){return{referrer:r(),user_agent:navigator.userAgent,screen_resolution:o(),browser_resolution:i(),browser_languages:s(),pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}}n.d(t,{R:()=>AnalyticsClient});var c=n(82918);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,c.b)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n={client_id:this.clientId,page_views:e,events:t,request_context:a()},r=JSON.stringify(n);try{if(navigator.sendBeacon){navigator.sendBeacon(this.collectorUrl,r);return}}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:r,keepalive:!1})}}},88149:(e,t,n)=>{n.d(t,{n:()=>r});function r(e="ha"){let t;let n={},r=document.head.querySelectorAll(`meta[name^="${e}-"]`);for(let o of Array.from(r)){let{name:r,content:i}=o,s=r.replace(`${e}-`,"").replace(/-/g,"_");"url"===s?t=i:n[s]=i}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}}}]);
//# sourceMappingURL=vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-0e9dbe-661380916a64.js.map