"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67"],{65935:(e,t,n)=>{let r;function i(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}function o(e){let t=new URLSearchParams,n=new FormData(e).entries();for(let[e,r]of[...n])t.append(e,r.toString());return t.toString()}n.d(t,{AC:()=>p,rK:()=>c,uT:()=>u});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function a(){let e,t;let n=new Promise(function(n,r){e=n,t=r});return[n,e,t]}let s=[],l=[];function u(e){s.push(e)}function c(e){l.push(e)}function p(e,t){r||(r=new Map,"undefined"!=typeof document&&document.addEventListener("submit",f));let n=r.get(e)||[];r.set(e,[...n,t])}function h(e){let t=[];for(let n of r.keys())if(e.matches(n)){let e=r.get(n)||[];t.push(...e)}return t}function f(e){if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let t=e.target,n=h(t);if(0===n.length)return;let r=m(t),[i,o,u]=a();e.preventDefault(),d(n,t,r,i).then(async e=>{if(e){for(let e of l)await e(t);g(r).then(o,u).catch(()=>{}).then(()=>{for(let e of s)e(t)})}else t.submit()},e=>{t.submit(),setTimeout(()=>{throw e})})}async function d(e,t,n,r){let i=!1;for(let o of e){let[e,s]=a(),l=()=>(i=!0,s(),r),u={text:l,json:()=>(n.headers.set("Accept","application/json"),l()),html:()=>(n.headers.set("Accept","text/html"),l())};await Promise.race([e,o(t,u,n)])}return i}function m(e){let t={method:e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===t.method.toUpperCase()){let n=o(e);n&&(t.url+=(~t.url.indexOf("?")?"&":"?")+n)}else t.body=new FormData(e);return t}async function g(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=this,t=JSON.parse(e.text);return delete e.json,e.json=t,e.json},get html(){let e=this;return delete e.html,e.html=i(document,e.text),e.html}},r=await t.text();if(n.text=r,t.ok)return n;throw new ErrorWithResponse("request failed",n)}},59753:(e,t,n)=>{function r(){if(!(this instanceof r))return new r;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{f:()=>j,S:()=>A,on:()=>P});var i,o=window.document.documentElement,a=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.oMatchesSelector||o.msMatchesSelector;r.prototype.matchesSelector=function(e,t){return a.call(e,t)},r.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},r.prototype.indexes=[];var s=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(s))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var u=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;r.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(u))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),r.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},i="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var c=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function p(e,t){var n,r,i,o,a,s,l=(e=e.slice(0).concat(e.default)).length,u=t,p=[];do if(c.exec(""),(i=c.exec(u))&&(u=i[3],i[2]||!u)){for(n=0;n<l;n++)if(a=(s=e[n]).selector(i[1])){for(r=p.length,o=!1;r--;)if(p[r].index===s&&p[r].key===a){o=!0;break}o||p.push({index:s,key:a});break}}while(i)return p}function h(e,t){var n,r,i;for(n=0,r=e.length;n<r;n++)if(i=e[n],t.isPrototypeOf(i))return i}function f(e,t){return e.id-t.id}r.prototype.logDefaultIndexUsed=function(){},r.prototype.add=function(e,t){var n,r,o,a,s,l,u,c,f=this.activeIndexes,d=this.selectors,m=this.selectorObjects;if("string"==typeof e){for(r=0,m[(n={id:this.uid++,selector:e,data:t}).id]=n,u=p(this.indexes,e);r<u.length;r++)a=(c=u[r]).key,(s=h(f,o=c.index))||((s=Object.create(o)).map=new i,f.push(s)),o===this.indexes.default&&this.logDefaultIndexUsed(n),(l=s.map.get(a))||(l=[],s.map.set(a,l)),l.push(n);this.size++,d.push(e)}},r.prototype.remove=function(e,t){if("string"==typeof e){var n,r,i,o,a,s,l,u,c=this.activeIndexes,h=this.selectors=[],f=this.selectorObjects,d={},m=1==arguments.length;for(i=0,n=p(this.indexes,e);i<n.length;i++)for(r=n[i],o=c.length;o--;)if(s=c[o],r.index.isPrototypeOf(s)){if(l=s.map.get(r.key))for(a=l.length;a--;)(u=l[a]).selector===e&&(m||u.data===t)&&(l.splice(a,1),d[u.id]=!0);break}for(i in d)delete f[i],this.size--;for(i in f)h.push(f[i].selector)}},r.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,r,i,o,a,s,l,u={},c=[],p=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,r=p.length;t<r;t++)for(n=0,o=p[t],i=(a=this.matches(o)).length;n<i;n++)u[(l=a[n]).id]?s=u[l.id]:(s={id:l.id,selector:l.selector,data:l.data,elements:[]},u[l.id]=s,c.push(s)),s.elements.push(o);return c.sort(f)},r.prototype.matches=function(e){if(!e)return[];var t,n,r,i,o,a,s,l,u,c,p,h=this.activeIndexes,d={},m=[];for(t=0,i=h.length;t<i;t++)if(l=(s=h[t]).element(e)){for(n=0,o=l.length;n<o;n++)if(u=s.map.get(l[n]))for(r=0,a=u.length;r<a;r++)!d[p=(c=u[r]).id]&&this.matchesSelector(e,c.selector)&&(d[p]=!0,m.push(c))}return m.sort(f)};var d={},m={},g=new WeakMap,v=new WeakMap,y=new WeakMap,b=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function w(e,t,n){var r=e[t];return e[t]=function(){return n.apply(e,arguments),r.apply(e,arguments)},e}function x(e,t,n){var r=[],i=t;do{if(1!==i.nodeType)break;var o=e.matches(i);if(o.length){var a={node:i,observers:o};n?r.unshift(a):r.push(a)}}while(i=i.parentElement)return r}function S(){g.set(this,!0)}function T(){g.set(this,!0),v.set(this,!0)}function E(){return y.get(this)||null}function k(e,t){b&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||b.get})}function C(e){try{return e.eventPhase,!0}catch(e){return!1}}function O(e){if(C(e)){var t=(1===e.eventPhase?m:d)[e.type];if(t){var n=x(t,e.target,1===e.eventPhase);if(n.length){w(e,"stopPropagation",S),w(e,"stopImmediatePropagation",T),k(e,E);for(var r=0,i=n.length;r<i&&!g.get(e);r++){var o=n[r];y.set(e,o.node);for(var a=0,s=o.observers.length;a<s&&!v.get(e);a++)o.observers[a].data.call(o.node,e)}y.delete(e),k(e)}}}}function P(e,t,n){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!!i.capture,a=o?m:d,s=a[e];s||(s=new r,a[e]=s,document.addEventListener(e,O,o)),s.add(t,n)}function A(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!r.capture,o=i?m:d,a=o[e];a&&(a.remove(t,n),a.size||(delete o[e],document.removeEventListener(e,O,i)))}function j(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},15205:(e,t,n)=>{function r(...e){return JSON.stringify(e,(e,t)=>"object"==typeof t?t:String(t))}function i(e,t={}){let{hash:n=r,cache:i=new Map}=t;return function(...t){let r=n.apply(this,t);if(i.has(r))return i.get(r);let o=e.apply(this,t);return o instanceof Promise&&(o=o.catch(e=>{throw i.delete(r),e})),i.set(r,o),o}}n.d(t,{Z:()=>i})},69567:(e,t,n)=>{function*r(e){let t="",n=0,r=!1;for(let i=0;i<e.length;i+=1)"{"!==e[i]||"{"!==e[i+1]||"\\"===e[i-1]||r?"}"===e[i]&&"}"===e[i+1]&&"\\"!==e[i-1]&&r&&(r=!1,yield{type:"part",start:n,end:i+2,value:t.slice(2).trim()},t="",i+=2,n=i):(r=!0,t&&(yield{type:"string",start:n,end:i,value:t}),t="{{",n=i,i+=2),t+=e[i]||"";t&&(yield{type:"string",start:n,end:e.length,value:t})}n.d(t,{R:()=>TemplateInstance,XK:()=>v});var i,o,a,s,l,u=function(e,t,n){if(!t.has(e))throw TypeError("attempted to set private field on non-instance");return t.set(e,n),n},c=function(e,t){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return t.get(e)};let AttributeTemplatePart=class AttributeTemplatePart{constructor(e,t){this.expression=t,i.set(this,void 0),o.set(this,""),u(this,i,e),c(this,i).updateParent("")}get attributeName(){return c(this,i).attr.name}get attributeNamespace(){return c(this,i).attr.namespaceURI}get value(){return c(this,o)}set value(e){u(this,o,e||""),c(this,i).updateParent(e)}get element(){return c(this,i).element}get booleanValue(){return c(this,i).booleanValue}set booleanValue(e){c(this,i).booleanValue=e}};i=new WeakMap,o=new WeakMap;let AttributeValueSetter=class AttributeValueSetter{constructor(e,t){this.element=e,this.attr=t,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(e){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=e?"":null}append(e){this.partList.push(e)}updateParent(e){if(1===this.partList.length&&null===e)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let e=this.partList.map(e=>"string"==typeof e?e:e.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,e)}}};var p=function(e,t,n){if(!t.has(e))throw TypeError("attempted to set private field on non-instance");return t.set(e,n),n},h=function(e,t){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return t.get(e)};let NodeTemplatePart=class NodeTemplatePart{constructor(e,t){this.expression=t,a.set(this,void 0),p(this,a,[e]),e.textContent=""}get value(){return h(this,a).map(e=>e.textContent).join("")}set value(e){this.replace(e)}get previousSibling(){return h(this,a)[0].previousSibling}get nextSibling(){return h(this,a)[h(this,a).length-1].nextSibling}replace(...e){let t=e.map(e=>"string"==typeof e?new Text(e):e);for(let e of(t.length||t.push(new Text("")),h(this,a)[0].before(...t),h(this,a)))e.remove();p(this,a,t)}};function f(e){return{createCallback(e,t,n){this.processCallback(e,t,n)},processCallback(t,n,r){var i;if("object"==typeof r&&r){for(let t of n)if(t.expression in r){let n=null!==(i=r[t.expression])&&void 0!==i?i:"";e(t,n)}}}}}function d(e,t){e.value=String(t)}function m(e,t){return"boolean"==typeof t&&e instanceof AttributeTemplatePart&&"boolean"==typeof e.element[e.attributeName]&&(e.booleanValue=t,!0)}a=new WeakMap;let g=f(d),v=f((e,t)=>{m(e,t)||d(e,t)});var y=function(e,t,n){if(!t.has(e))throw TypeError("attempted to set private field on non-instance");return t.set(e,n),n},b=function(e,t){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return t.get(e)};function*w(e){let t;let n=e.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null,!1);for(;t=n.nextNode();)if(t instanceof Element&&t.hasAttributes())for(let e=0;e<t.attributes.length;e+=1){let n=t.attributes.item(e);if(n&&n.value.includes("{{")){let e=new AttributeValueSetter(t,n);for(let t of r(n.value))if("string"===t.type)e.append(t.value);else{let n=new AttributeTemplatePart(e,t.value);e.append(n),yield n}}}else if(t instanceof Text&&t.textContent&&t.textContent.includes("{{"))for(let e of r(t.textContent)){e.end<t.textContent.length&&t.splitText(e.end),"part"===e.type&&(yield new NodeTemplatePart(t,e.value));break}}let TemplateInstance=class TemplateInstance extends DocumentFragment{constructor(e,t,n=g){var r,i;super(),s.set(this,void 0),l.set(this,void 0),Object.getPrototypeOf(this!==TemplateInstance.prototype)&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(e.content.cloneNode(!0)),y(this,l,Array.from(w(this))),y(this,s,n),null===(i=(r=b(this,s)).createCallback)||void 0===i||i.call(r,this,b(this,l),t)}update(e){b(this,s).processCallback(this,b(this,l),e)}};s=new WeakMap,l=new WeakMap},8433:(e,t,n)=>{function r(e){let t="==".slice(0,(4-e.length%4)%4),n=e.replace(/-/g,"+").replace(/_/g,"/")+t,r=atob(n),i=new ArrayBuffer(r.length),o=new Uint8Array(i);for(let e=0;e<r.length;e++)o[e]=r.charCodeAt(e);return i}function i(e){let t=new Uint8Array(e),n="";for(let e of t)n+=String.fromCharCode(e);let r=btoa(n),i=r.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"");return i}n.d(t,{JO:()=>y,U2:()=>E,Ue:()=>T,Zh:()=>S,wz:()=>w});var o="copy",a="convert";function s(e,t,n){if(t===o)return n;if(t===a)return e(n);if(t instanceof Array)return n.map(n=>s(e,t[0],n));if(t instanceof Object){let r={};for(let[i,o]of Object.entries(t)){if(o.derive){let e=o.derive(n);void 0!==e&&(n[i]=e)}if(!(i in n)){if(o.required)throw Error(`Missing key: ${i}`);continue}if(null==n[i]){r[i]=null;continue}r[i]=s(e,o.schema,n[i])}return r}}function l(e,t){return{required:!0,schema:e,derive:t}}function u(e){return{required:!0,schema:e}}function c(e){return{required:!1,schema:e}}var p={type:u(o),id:u(a),transports:c(o)},h={appid:c(o),appidExclude:c(o),credProps:c(o)},f={appid:c(o),appidExclude:c(o),credProps:c(o)},d={publicKey:u({rp:u(o),user:u({id:u(a),name:u(o),displayName:u(o)}),challenge:u(a),pubKeyCredParams:u(o),timeout:c(o),excludeCredentials:c([p]),authenticatorSelection:c(o),attestation:c(o),extensions:c(h)}),signal:c(o)},m={type:u(o),id:u(o),rawId:u(a),authenticatorAttachment:c(o),response:u({clientDataJSON:u(a),attestationObject:u(a),transports:l(o,e=>{var t;return(null==(t=e.getTransports)?void 0:t.call(e))||[]})}),clientExtensionResults:l(f,e=>e.getClientExtensionResults())},g={mediation:c(o),publicKey:u({challenge:u(a),timeout:c(o),rpId:c(o),allowCredentials:c([p]),userVerification:c(o),extensions:c(h)}),signal:c(o)},v={type:u(o),id:u(o),rawId:u(a),authenticatorAttachment:c(o),response:u({clientDataJSON:u(a),authenticatorData:u(a),signature:u(a),userHandle:u(a)}),clientExtensionResults:l(f,e=>e.getClientExtensionResults())};function y(e){return s(r,d,e)}function b(e){return s(i,m,e)}function w(e){return s(r,g,e)}function x(e){return s(i,v,e)}function S(){return!!(navigator.credentials&&navigator.credentials.create&&navigator.credentials.get&&window.PublicKeyCredential)}async function T(e){let t=await navigator.credentials.create(e);return t.toJSON=()=>b(t),t}async function E(e){let t=await navigator.credentials.get(e);return t.toJSON=()=>x(t),t}}}]);
//# sourceMappingURL=vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-94fd67-c330af77adfd.js.map