"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-c537341","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc0","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc1","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-c537340","vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js-b4a243","vendors-node_modules_github_catalyst_lib_index_js-node_modules_github_hydro-analytics-client_-978abc2","vendors-node_modules_delegated-events_dist_index_js-node_modules_github_catalyst_lib_index_js","vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-0e9dbe"],{65935:(e,t,n)=>{let o;function r(e,t){let n=e.createElement("template");return n.innerHTML=t,e.importNode(n.content,!0)}function i(e){let t=new URLSearchParams,n=new FormData(e).entries();for(let[e,o]of[...n])t.append(e,o.toString());return t.toString()}n.d(t,{AC:()=>u,rK:()=>d,uT:()=>c});let ErrorWithResponse=class ErrorWithResponse extends Error{constructor(e,t){super(e),this.response=t}};function s(){let e,t;let n=new Promise(function(n,o){e=n,t=o});return[n,e,t]}let a=[],l=[];function c(e){a.push(e)}function d(e){l.push(e)}function u(e,t){o||(o=new Map,"undefined"!=typeof document&&document.addEventListener("submit",h));let n=o.get(e)||[];o.set(e,[...n,t])}function f(e){let t=[];for(let n of o.keys())if(e.matches(n)){let e=o.get(n)||[];t.push(...e)}return t}function h(e){if(!(e.target instanceof HTMLFormElement)||e.defaultPrevented)return;let t=e.target,n=f(t);if(0===n.length)return;let o=m(t),[r,i,c]=s();e.preventDefault(),p(n,t,o,r).then(async e=>{if(e){for(let e of l)await e(t);g(o).then(i,c).catch(()=>{}).then(()=>{for(let e of a)e(t)})}else t.submit()},e=>{t.submit(),setTimeout(()=>{throw e})})}async function p(e,t,n,o){let r=!1;for(let i of e){let[e,a]=s(),l=()=>(r=!0,a(),o),c={text:l,json:()=>(n.headers.set("Accept","application/json"),l()),html:()=>(n.headers.set("Accept","text/html"),l())};await Promise.race([e,i(t,c,n)])}return r}function m(e){let t={method:e.method||"GET",url:e.action,headers:new Headers({"X-Requested-With":"XMLHttpRequest"}),body:null};if("GET"===t.method.toUpperCase()){let n=i(e);n&&(t.url+=(~t.url.indexOf("?")?"&":"?")+n)}else t.body=new FormData(e);return t}async function g(e){let t=await window.fetch(e.url,{method:e.method,body:null!==e.body?e.body:void 0,headers:e.headers,credentials:"same-origin"}),n={url:t.url,status:t.status,statusText:t.statusText,headers:t.headers,text:"",get json(){let e=this,t=JSON.parse(e.text);return delete e.json,e.json=t,e.json},get html(){let e=this;return delete e.html,e.html=r(document,e.text),e.html}},o=await t.text();if(n.text=o,t.ok)return n;throw new ErrorWithResponse("request failed",n)}},59753:(e,t,n)=>{function o(){if(!(this instanceof o))return new o;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}n.d(t,{f:()=>P,S:()=>O,on:()=>C});var r,i=window.document.documentElement,s=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.oMatchesSelector||i.msMatchesSelector;o.prototype.matchesSelector=function(e,t){return s.call(e,t)},o.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},o.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;o.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),o.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},r="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var d=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function u(e,t){var n,o,r,i,s,a,l=(e=e.slice(0).concat(e.default)).length,c=t,u=[];do if(d.exec(""),(r=d.exec(c))&&(c=r[3],r[2]||!c)){for(n=0;n<l;n++)if(s=(a=e[n]).selector(r[1])){for(o=u.length,i=!1;o--;)if(u[o].index===a&&u[o].key===s){i=!0;break}i||u.push({index:a,key:s});break}}while(r)return u}function f(e,t){var n,o,r;for(n=0,o=e.length;n<o;n++)if(r=e[n],t.isPrototypeOf(r))return r}function h(e,t){return e.id-t.id}o.prototype.logDefaultIndexUsed=function(){},o.prototype.add=function(e,t){var n,o,i,s,a,l,c,d,h=this.activeIndexes,p=this.selectors,m=this.selectorObjects;if("string"==typeof e){for(o=0,m[(n={id:this.uid++,selector:e,data:t}).id]=n,c=u(this.indexes,e);o<c.length;o++)s=(d=c[o]).key,(a=f(h,i=d.index))||((a=Object.create(i)).map=new r,h.push(a)),i===this.indexes.default&&this.logDefaultIndexUsed(n),(l=a.map.get(s))||(l=[],a.map.set(s,l)),l.push(n);this.size++,p.push(e)}},o.prototype.remove=function(e,t){if("string"==typeof e){var n,o,r,i,s,a,l,c,d=this.activeIndexes,f=this.selectors=[],h=this.selectorObjects,p={},m=1==arguments.length;for(r=0,n=u(this.indexes,e);r<n.length;r++)for(o=n[r],i=d.length;i--;)if(a=d[i],o.index.isPrototypeOf(a)){if(l=a.map.get(o.key))for(s=l.length;s--;)(c=l[s]).selector===e&&(m||c.data===t)&&(l.splice(s,1),p[c.id]=!0);break}for(r in p)delete h[r],this.size--;for(r in h)f.push(h[r].selector)}},o.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,n,o,r,i,s,a,l,c={},d=[],u=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,o=u.length;t<o;t++)for(n=0,i=u[t],r=(s=this.matches(i)).length;n<r;n++)c[(l=s[n]).id]?a=c[l.id]:(a={id:l.id,selector:l.selector,data:l.data,elements:[]},c[l.id]=a,d.push(a)),a.elements.push(i);return d.sort(h)},o.prototype.matches=function(e){if(!e)return[];var t,n,o,r,i,s,a,l,c,d,u,f=this.activeIndexes,p={},m=[];for(t=0,r=f.length;t<r;t++)if(l=(a=f[t]).element(e)){for(n=0,i=l.length;n<i;n++)if(c=a.map.get(l[n]))for(o=0,s=c.length;o<s;o++)!p[u=(d=c[o]).id]&&this.matchesSelector(e,d.selector)&&(p[u]=!0,m.push(d))}return m.sort(h)};var p={},m={},g=new WeakMap,b=new WeakMap,y=new WeakMap,w=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function v(e,t,n){var o=e[t];return e[t]=function(){return n.apply(e,arguments),o.apply(e,arguments)},e}function _(e,t,n){var o=[],r=t;do{if(1!==r.nodeType)break;var i=e.matches(r);if(i.length){var s={node:r,observers:i};n?o.unshift(s):o.push(s)}}while(r=r.parentElement)return o}function x(){g.set(this,!0)}function A(){g.set(this,!0),b.set(this,!0)}function E(){return y.get(this)||null}function S(e,t){w&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||w.get})}function k(e){try{return e.eventPhase,!0}catch(e){return!1}}function j(e){if(k(e)){var t=(1===e.eventPhase?m:p)[e.type];if(t){var n=_(t,e.target,1===e.eventPhase);if(n.length){v(e,"stopPropagation",x),v(e,"stopImmediatePropagation",A),S(e,E);for(var o=0,r=n.length;o<r&&!g.get(e);o++){var i=n[o];y.set(e,i.node);for(var s=0,a=i.observers.length;s<a&&!b.get(e);s++)i.observers[s].data.call(i.node,e)}y.delete(e),S(e)}}}}function C(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!r.capture,s=i?m:p,a=s[e];a||(a=new o,s[e]=a,document.addEventListener(e,j,i)),a.add(t,n)}function O(e,t,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},r=!!o.capture,i=r?m:p,s=i[e];s&&(s.remove(t,n),s.size||(delete i[e],document.removeEventListener(e,j,r)))}function P(e,t,n){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:n}))}},76006:(e,t,n)=>{let o;n.d(t,{Lj:()=>v,Ih:()=>C,P4:()=>h,nW:()=>F,fA:()=>k,GO:()=>j});let r=new WeakSet;function i(e){r.add(e),e.shadowRoot&&s(e.shadowRoot),c(e),l(e.ownerDocument)}function s(e){c(e),l(e)}let a=new WeakMap;function l(e=document){if(a.has(e))return a.get(e);let t=!1,n=new MutationObserver(e=>{for(let t of e)if("attributes"===t.type&&t.target instanceof Element)f(t.target);else if("childList"===t.type&&t.addedNodes.length)for(let e of t.addedNodes)e instanceof Element&&c(e)});n.observe(e,{childList:!0,subtree:!0,attributeFilter:["data-action"]});let o={get closed(){return t},unsubscribe(){t=!0,a.delete(e),n.disconnect()}};return a.set(e,o),o}function c(e){for(let t of e.querySelectorAll("[data-action]"))f(t);e instanceof Element&&e.hasAttribute("data-action")&&f(e)}function d(e){let t=e.currentTarget;for(let n of u(t))if(e.type===n.type){let o=t.closest(n.tag);r.has(o)&&"function"==typeof o[n.method]&&o[n.method](e);let i=t.getRootNode();if(i instanceof ShadowRoot&&r.has(i.host)&&i.host.matches(n.tag)){let t=i.host;"function"==typeof t[n.method]&&t[n.method](e)}}}function*u(e){for(let t of(e.getAttribute("data-action")||"").trim().split(/\s+/)){let e=t.lastIndexOf(":"),n=Math.max(0,t.lastIndexOf("#"))||t.length;yield{type:t.slice(0,e),tag:t.slice(e+1,n),method:t.slice(n+1)||"handleEvent"}}}function f(e){for(let t of u(e))e.addEventListener(t.type,d)}function h(e,t){let n=e.tagName.toLowerCase();if(e.shadowRoot){for(let o of e.shadowRoot.querySelectorAll(`[data-target~="${n}.${t}"]`))if(!o.closest(n))return o}for(let o of e.querySelectorAll(`[data-target~="${n}.${t}"]`))if(o.closest(n)===e)return o}function p(e,t){let n=e.tagName.toLowerCase(),o=[];if(e.shadowRoot)for(let r of e.shadowRoot.querySelectorAll(`[data-targets~="${n}.${t}"]`))r.closest(n)||o.push(r);for(let r of e.querySelectorAll(`[data-targets~="${n}.${t}"]`))r.closest(n)===e&&o.push(r);return o}let m=e=>String("symbol"==typeof e?e.description:e).replace(/([A-Z]($|[a-z]))/g,"-$1").replace(/--/g,"-").replace(/^-|-$/,"").toLowerCase(),g=(e,t="property")=>{let n=m(e);if(!n.includes("-"))throw new DOMException(`${t}: ${String(e)} is not a valid ${t} name`,"SyntaxError");return n};function b(e){let t=m(e.name).replace(/-element$/,"");try{window.customElements.define(t,e),window[e.name]=customElements.get(t)}catch(e){if(!(e instanceof DOMException&&"NotSupportedError"===e.name))throw e}return e}function y(e){for(let t of e.querySelectorAll("template[data-shadowroot]"))t.parentElement===e&&e.attachShadow({mode:"closed"===t.getAttribute("data-shadowroot")?"closed":"open"}).append(t.content.cloneNode(!0))}let w="attr";function v(e,t){S(e,w).add(t)}let _=new WeakSet;function x(e,t){if(_.has(e))return;_.add(e);let n=Object.getPrototypeOf(e),o=n?.constructor?.attrPrefix??"data-";for(let r of(t||(t=S(n,w)),t)){let t=e[r],n=g(`${o}${r}`),i={configurable:!0,get(){return this.getAttribute(n)||""},set(e){this.setAttribute(n,e||"")}};"number"==typeof t?i={configurable:!0,get(){return Number(this.getAttribute(n)||0)},set(e){this.setAttribute(n,e)}}:"boolean"==typeof t&&(i={configurable:!0,get(){return this.hasAttribute(n)},set(e){this.toggleAttribute(n,e)}}),Object.defineProperty(e,r,i),r in e&&!e.hasAttribute(n)&&i.set.call(e,t)}}function A(e){let t=e.observedAttributes||[],n=e.attrPrefix??"data-",o=e=>g(`${n}${e}`);Object.defineProperty(e,"observedAttributes",{configurable:!0,get:()=>[...S(e.prototype,w)].map(o).concat(t),set(e){t=e}})}let E=Symbol.for("catalyst");let CatalystDelegate=class CatalystDelegate{constructor(e){let t=this,n=e.prototype.connectedCallback;e.prototype.connectedCallback=function(){t.connectedCallback(this,n)};let o=e.prototype.disconnectedCallback;e.prototype.disconnectedCallback=function(){t.disconnectedCallback(this,o)};let r=e.prototype.attributeChangedCallback;e.prototype.attributeChangedCallback=function(e,n,o){t.attributeChangedCallback(this,e,n,o,r)};let i=e.observedAttributes||[];Object.defineProperty(e,"observedAttributes",{configurable:!0,get(){return t.observedAttributes(this,i)},set(e){i=e}}),A(e),b(e)}observedAttributes(e,t){return t}connectedCallback(e,t){e.toggleAttribute("data-catalyst",!0),customElements.upgrade(e),y(e),x(e),i(e),t?.call(e),e.shadowRoot&&s(e.shadowRoot)}disconnectedCallback(e,t){t?.call(e)}attributeChangedCallback(e,t,n,o,r){x(e),"data-catalyst"!==t&&r&&r.call(e,t,n,o)}};function S(e,t){if(!Object.prototype.hasOwnProperty.call(e,E)){let t=e[E],n=e[E]=new Map;if(t)for(let[e,o]of t)n.set(e,new Set(o))}let n=e[E];return n.has(t)||n.set(t,new Set),n.get(t)}function k(e,t){S(e,"target").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return h(this,t)}})}function j(e,t){S(e,"targets").add(t),Object.defineProperty(e,t,{configurable:!0,get(){return p(this,t)}})}function C(e){new CatalystDelegate(e)}let O=new Map,P=new Promise(e=>{"loading"!==document.readyState?e():document.addEventListener("readystatechange",()=>e(),{once:!0})}),$=new Promise(e=>{let t=new AbortController;t.signal.addEventListener("abort",()=>e());let n={once:!0,passive:!0,signal:t.signal},o=()=>t.abort();document.addEventListener("mousedown",o,n),document.addEventListener("touchstart",o,n),document.addEventListener("keydown",o,n),document.addEventListener("pointerdown",o,n)}),L=e=>new Promise(t=>{let n=new IntersectionObserver(e=>{for(let o of e)if(o.isIntersecting){t(),n.disconnect();return}},{rootMargin:"0px 0px 256px 0px",threshold:.01});for(let t of document.querySelectorAll(e))n.observe(t)}),M={ready:()=>P,firstInteraction:()=>$,visible:L},I=new WeakMap;function q(e){cancelAnimationFrame(I.get(e)||0),I.set(e,requestAnimationFrame(()=>{for(let t of O.keys()){let n=e.matches(t)?e:e.querySelector(t);if(customElements.get(t)||n){let o=n?.getAttribute("data-load-on")||"ready",r=o in M?M[o]:M.ready;for(let e of O.get(t)||[])r(t).then(e);O.delete(t),I.delete(e)}}}))}function F(e,t){O.has(e)||O.set(e,new Set),O.get(e).add(t),q(document.body),o||(o=new MutationObserver(e=>{if(O.size)for(let t of e)for(let e of t.addedNodes)e instanceof Element&&q(e)})).observe(document,{subtree:!0,childList:!0})}},86058:(e,t,n)=>{function o(){let e;try{e=window.top.document.referrer}catch(t){if(window.parent)try{e=window.parent.document.referrer}catch(e){}}return""===e&&(e=document.referrer),e}function r(){try{return`${screen.width}x${screen.height}`}catch(e){return"unknown"}}function i(){let e=0,t=0;try{return"number"==typeof window.innerWidth?(t=window.innerWidth,e=window.innerHeight):null!=document.documentElement&&null!=document.documentElement.clientWidth?(t=document.documentElement.clientWidth,e=document.documentElement.clientHeight):null!=document.body&&null!=document.body.clientWidth&&(t=document.body.clientWidth,e=document.body.clientHeight),`${t}x${e}`}catch(e){return"unknown"}}function s(){return navigator.languages?navigator.languages.join(","):navigator.language||""}function a(){return{referrer:o(),user_agent:navigator.userAgent,screen_resolution:r(),browser_resolution:i(),browser_languages:s(),pixel_ratio:window.devicePixelRatio,timestamp:Date.now(),tz_seconds:-60*new Date().getTimezoneOffset()}}n.d(t,{R:()=>AnalyticsClient});var l=n(82918);let AnalyticsClient=class AnalyticsClient{constructor(e){this.options=e}get collectorUrl(){return this.options.collectorUrl}get clientId(){return this.options.clientId?this.options.clientId:(0,l.b)()}createEvent(e){return{page:location.href,title:document.title,context:{...this.options.baseContext,...e}}}sendPageView(e){let t=this.createEvent(e);this.send({page_views:[t]})}sendEvent(e,t){let n={...this.createEvent(t),type:e};this.send({events:[n]})}send({page_views:e,events:t}){let n={client_id:this.clientId,page_views:e,events:t,request_context:a()},o=JSON.stringify(n);try{if(navigator.sendBeacon){navigator.sendBeacon(this.collectorUrl,o);return}}catch{}fetch(this.collectorUrl,{method:"POST",cache:"no-cache",headers:{"Content-Type":"application/json"},body:o,keepalive:!1})}}},88149:(e,t,n)=>{n.d(t,{n:()=>o});function o(e="ha"){let t;let n={},o=document.head.querySelectorAll(`meta[name^="${e}-"]`);for(let r of Array.from(o)){let{name:o,content:i}=r,s=o.replace(`${e}-`,"").replace(/-/g,"_");"url"===s?t=i:n[s]=i}if(!t)throw Error(`AnalyticsClient ${e}-url meta tag not found`);return{collectorUrl:t,...Object.keys(n).length>0?{baseContext:n}:{}}}}}]);
//# sourceMappingURL=vendors-node_modules_github_remote-form_dist_index_js-node_modules_delegated-events_dist_inde-c537341-7ce9f5e6ced9.js.map