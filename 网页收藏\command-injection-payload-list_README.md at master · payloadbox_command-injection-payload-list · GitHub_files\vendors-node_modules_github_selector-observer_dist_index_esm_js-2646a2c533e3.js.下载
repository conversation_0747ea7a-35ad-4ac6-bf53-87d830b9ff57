"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_selector-observer_dist_index_esm_js"],{36071:(e,t,r)=>{r.d(t,{N7:()=>P});var n=r(8439),o=null,i=null,s=[];function a(e,t){var r=[];function n(){var e=r;r=[],t(e)}return function(){for(var t=arguments.length,o=Array(t),i=0;i<t;i++)o[i]=arguments[i];r.push(o),1===r.length&&l(e,n)}}function l(e,t){i||(i=new MutationObserver(c)),o||(o=e.createElement("div"),i.observe(o,{attributes:!0})),s.push(t),o.setAttribute("data-twiddle",""+Date.now())}function c(){var e=s;s=[];for(var t=0;t<e.length;t++)try{e[t]()}catch(e){setTimeout(function(){throw e},0)}}var u=new WeakMap,d=new WeakMap,f=new WeakMap,h=new WeakMap;function v(e,t){for(var r=0;r<t.length;r++){var n=t[r],o=n[0],i=n[1],s=n[2];o===x?(p(s,i),m(s,i)):o===A?b(s,i):o===O&&g(e.observers,i)}}function p(e,t){if(t instanceof e.elementConstructor){var r=u.get(t);if(r||(r=[],u.set(t,r)),-1===r.indexOf(e.id)){var n=void 0;if(e.initialize&&(n=e.initialize.call(void 0,t)),n){var o=d.get(t);o||(o={},d.set(t,o)),o[""+e.id]=n}r.push(e.id)}}}function m(e,t){if(t instanceof e.elementConstructor){var r=h.get(t);if(r||(r=[],h.set(t,r)),-1===r.indexOf(e.id)){e.elements.push(t);var n=d.get(t),o=n?n[""+e.id]:null;if(o&&o.add&&o.add.call(void 0,t),e.subscribe){var i=e.subscribe.call(void 0,t);if(i){var s=f.get(t);s||(s={},f.set(t,s)),s[""+e.id]=i}}e.add&&e.add.call(void 0,t),r.push(e.id)}}}function b(e,t){if(t instanceof e.elementConstructor){var r=h.get(t);if(r){var n=e.elements.indexOf(t);if(-1!==n&&e.elements.splice(n,1),-1!==(n=r.indexOf(e.id))){var o=d.get(t),i=o?o[""+e.id]:null;if(i&&i.remove&&i.remove.call(void 0,t),e.subscribe){var s=f.get(t),a=s?s[""+e.id]:null;a&&a.unsubscribe&&a.unsubscribe()}e.remove&&e.remove.call(void 0,t),r.splice(n,1)}0===r.length&&h.delete(t)}}}function g(e,t){var r=h.get(t);if(r){for(var n=r.slice(0),o=0;o<n.length;o++){var i=e[n[o]];if(i){var s=i.elements.indexOf(t);-1!==s&&i.elements.splice(s,1);var a=d.get(t),l=a?a[""+i.id]:null;l&&l.remove&&l.remove.call(void 0,t);var c=f.get(t),u=c?c[""+i.id]:null;u&&u.unsubscribe&&u.unsubscribe(),i.remove&&i.remove.call(void 0,t)}}h.delete(t)}}var y=null;function w(e){if(null===y){var t=e.createElement("div"),r=e.createElement("div"),n=e.createElement("div");t.appendChild(r),r.appendChild(n),t.textContent="",y=n.parentNode!==r}return y}function S(e){return"matches"in e||"webkitMatchesSelector"in e||"mozMatchesSelector"in e||"oMatchesSelector"in e||"msMatchesSelector"in e}var x=1,A=2,O=3;function k(e,t,r){for(var n=0;n<r.length;n++){var o=r[n];"childList"===o.type?(M(e,t,o.addedNodes),N(e,t,o.removedNodes)):"attributes"===o.type&&C(e,t,o.target)}w(e.ownerDocument)&&D(e,t)}function M(e,t,r){for(var n=0;n<r.length;n++){var o=r[n];if(S(o))for(var i=e.selectorSet.matches(o),s=0;s<i.length;s++){var a=i[s].data;t.push([x,o,a])}if("querySelectorAll"in o)for(var l=e.selectorSet.queryAll(o),c=0;c<l.length;c++)for(var u=l[c],d=u.data,f=u.elements,h=0;h<f.length;h++)t.push([x,f[h],d])}}function N(e,t,r){for(var n=0;n<r.length;n++){var o=r[n];if("querySelectorAll"in o){t.push([O,o]);for(var i=o.querySelectorAll("*"),s=0;s<i.length;s++)t.push([O,i[s]])}}}function C(e,t,r){if(S(r))for(var n=e.selectorSet.matches(r),o=0;o<n.length;o++){var i=n[o].data;t.push([x,r,i])}if("querySelectorAll"in r){var s=h.get(r);if(s)for(var a=0;a<s.length;a++){var l=e.observers[s[a]];l&&!e.selectorSet.matchesSelector(r,l.selector)&&t.push([A,r,l])}}}function _(e,t,r){if("querySelectorAll"in r){C(e,t,r);for(var n=r.querySelectorAll("*"),o=0;o<n.length;o++)C(e,t,n[o])}}function q(e,t,r){for(var n=0;n<r.length;n++)for(var o=r[n],i=o.form?o.form.elements:e.rootNode.querySelectorAll("input"),s=0;s<i.length;s++)C(e,t,i[s])}function D(e,t){for(var r=0;r<e.observers.length;r++){var n=e.observers[r];if(n)for(var o=n.elements,i=0;i<o.length;i++){var s=o[i];s.parentNode||t.push([O,s])}}}function F(e,t){var r=e.readyState;"interactive"===r||"complete"===r?l(e,t):e.addEventListener("DOMContentLoaded",l(e,t))}var z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},E=0;function T(e){this.rootNode=9===e.nodeType?e.documentElement:e,this.ownerDocument=9===e.nodeType?e:e.ownerDocument,this.observers=[],this.selectorSet=new n.Z,this.mutationObserver=new MutationObserver(L.bind(this,this)),this._scheduleAddRootNodes=a(this.ownerDocument,j.bind(this,this)),this._handleThrottledChangedTargets=a(this.ownerDocument,R.bind(this,this)),this.rootNode.addEventListener("change",U.bind(this,this),!1),F(this.ownerDocument,I.bind(this,this))}function I(e){e.mutationObserver.observe(e.rootNode,{childList:!0,attributes:!0,subtree:!0}),e._scheduleAddRootNodes()}function j(e){var t=[];M(e,t,[e.rootNode]),v(e,t)}function L(e,t){var r=[];k(e,r,t),v(e,r)}function U(e,t){e._handleThrottledChangedTargets(t.target)}function R(e,t){var r=[];q(e,r,t),v(e,r)}T.prototype.disconnect=function(){this.mutationObserver.disconnect()},T.prototype.observe=function(e,t){var r=void 0;"function"==typeof t?r={selector:e,initialize:t}:(void 0===t?"undefined":z(t))==="object"?(r=t).selector=e:r=e;var n=this,o={id:E++,selector:r.selector,initialize:r.initialize,add:r.add,remove:r.remove,subscribe:r.subscribe,elements:[],elementConstructor:r.hasOwnProperty("constructor")?r.constructor:this.ownerDocument.defaultView.Element,abort:function(){n._abortObserving(o)}};return this.selectorSet.add(o.selector,o),this.observers[o.id]=o,this._scheduleAddRootNodes(),o},T.prototype._abortObserving=function(e){for(var t=e.elements,r=0;r<t.length;r++)b(e,t[r]);this.selectorSet.remove(e.selector,e),delete this.observers[e.id]},T.prototype.triggerObservers=function(e){var t=[];_(this,t,e),v(this,t)};var V=void 0;function W(){return V||(V=new T(window.document)),V}function P(){var e;return(e=W()).observe.apply(e,arguments)}var Z=null},8439:(e,t,r)=>{function n(){if(!(this instanceof n))return new n;this.size=0,this.uid=0,this.selectors=[],this.indexes=Object.create(this.indexes),this.activeIndexes=[]}r.d(t,{Z:()=>n});var o,i=window.document.documentElement,s=i.matches||i.webkitMatchesSelector||i.mozMatchesSelector||i.oMatchesSelector||i.msMatchesSelector;n.prototype.matchesSelector=function(e,t){return s.call(e,t)},n.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},n.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var l=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(l))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var c=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(c))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),n.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},o="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var u=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function d(e,t){var r,n,o,i,s,a,l=(e=e.slice(0).concat(e.default)).length,c=t,d=[];do if(u.exec(""),(o=u.exec(c))&&(c=o[3],o[2]||!c)){for(r=0;r<l;r++)if(s=(a=e[r]).selector(o[1])){for(n=d.length,i=!1;n--;)if(d[n].index===a&&d[n].key===s){i=!0;break}i||d.push({index:a,key:s});break}}while(o)return d}function f(e,t){var r,n,o;for(r=0,n=e.length;r<n;r++)if(o=e[r],t.isPrototypeOf(o))return o}function h(e,t){return e.id-t.id}n.prototype.logDefaultIndexUsed=function(){},n.prototype.add=function(e,t){var r,n,i,s,a,l,c,u,h=this.activeIndexes,v=this.selectors;if("string"==typeof e){for(n=0,r={id:this.uid++,selector:e,data:t},c=d(this.indexes,e);n<c.length;n++)s=(u=c[n]).key,(a=f(h,i=u.index))||((a=Object.create(i)).map=new o,h.push(a)),i===this.indexes.default&&this.logDefaultIndexUsed(r),(l=a.map.get(s))||(l=[],a.map.set(s,l)),l.push(r);this.size++,v.push(e)}},n.prototype.remove=function(e,t){if("string"==typeof e){var r,n,o,i,s,a,l,c,u=this.activeIndexes,f={},h=1==arguments.length;for(o=0,r=d(this.indexes,e);o<r.length;o++)for(n=r[o],i=u.length;i--;)if(a=u[i],n.index.isPrototypeOf(a)){if(l=a.map.get(n.key))for(s=l.length;s--;)(c=l[s]).selector===e&&(h||c.data===t)&&(l.splice(s,1),f[c.id]=!0);break}this.size-=Object.keys(f).length}},n.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,r,n,o,i,s,a,l,c={},u=[],d=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,n=d.length;t<n;t++)for(r=0,i=d[t],o=(s=this.matches(i)).length;r<o;r++)c[(l=s[r]).id]?a=c[l.id]:(a={id:l.id,selector:l.selector,data:l.data,elements:[]},c[l.id]=a,u.push(a)),a.elements.push(i);return u.sort(h)},n.prototype.matches=function(e){if(!e)return[];var t,r,n,o,i,s,a,l,c,u,d,f=this.activeIndexes,v={},p=[];for(t=0,o=f.length;t<o;t++)if(l=(a=f[t]).element(e)){for(r=0,i=l.length;r<i;r++)if(c=a.map.get(l[r]))for(n=0,s=c.length;n<s;n++)!v[d=(u=c[n]).id]&&this.matchesSelector(e,u.selector)&&(v[d]=!0,p.push(u))}return p.sort(h)}}}]);
//# sourceMappingURL=vendors-node_modules_github_selector-observer_dist_index_esm_js-e156dfefd276.js.map