"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_github_session-resume_dist_index_js-node_modules_primer_behaviors_dist_e-ac74c6"],{407:(e,t,n)=>{n.d(t,{Xm:()=>a,e6:()=>l,iO:()=>o});let r=null;function i(e){return!!e.id&&e.value!==e.defaultValue&&e.form!==r}function a(e,t){var n,r,a;let l;let o=null!==(n=null==t?void 0:t.selector)&&void 0!==n?n:".js-session-resumable",s=null!==(r=null==t?void 0:t.keyPrefix)&&void 0!==r?r:"session-resume:";try{l=null!==(a=null==t?void 0:t.storage)&&void 0!==a?a:sessionStorage}catch(e){return}let u=`${s}${e}`,c=[];for(let e of document.querySelectorAll(o))(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement)&&c.push(e);let f=c.filter(e=>i(e)).map(e=>[e.id,e.value]);if(f.length)try{let e=l.getItem(u);if(null!==e){let t=JSON.parse(e),n=t.filter(function(e){return!f.some(t=>t[0]===e[0])});f=f.concat(n)}l.setItem(u,JSON.stringify(f))}catch(e){}}function l(e,t){var n,r;let i,a;let l=null!==(n=null==t?void 0:t.keyPrefix)&&void 0!==n?n:"session-resume:";try{i=null!==(r=null==t?void 0:t.storage)&&void 0!==r?r:sessionStorage}catch(e){return}let o=`${l}${e}`;try{a=i.getItem(o)}catch(e){}if(!a)return;let s=[],u=[];for(let[e,t]of JSON.parse(a)){let n=new CustomEvent("session:resume",{bubbles:!0,cancelable:!0,detail:{targetId:e,targetValue:t}});if(document.dispatchEvent(n)){let n=document.getElementById(e);n&&(n instanceof HTMLInputElement||n instanceof HTMLTextAreaElement)?n.value===n.defaultValue&&(n.value=t,s.push(n)):u.push([e,t])}}if(0===u.length)try{i.removeItem(o)}catch(e){}else i.setItem(o,JSON.stringify(u));setTimeout(function(){for(let e of s)e.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!0}))},0)}function o(e){r=e.target,setTimeout(function(){e.defaultPrevented&&(r=null)},0)}},44542:(e,t,n)=>{n.d(t,{O:()=>o});let r=!1;function i(){}try{let e=Object.create({},{signal:{get(){r=!0}}});window.addEventListener("test",i,e),window.removeEventListener("test",i,e)}catch(e){}function a(){return r}function l(){if("undefined"==typeof window)return;let e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(t,n,r){return"object"==typeof r&&"signal"in r&&r.signal instanceof AbortSignal&&e.call(r.signal,"abort",()=>{this.removeEventListener(t,n,r)}),e.call(this,t,n,r)}}function o(){a()||(l(),r=!0)}},78160:(e,t,n)=>{function*r(e,t={}){var n,r;let i=null!==(n=t.strict)&&void 0!==n&&n,o=null!==(r=t.onlyTabbable)&&void 0!==r&&r?l:a,s=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e instanceof HTMLElement&&o(e,i)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),u=null;if(!t.reverse&&o(e,i)&&(yield e),t.reverse){let e=s.lastChild();for(;e;)u=e,e=s.lastChild()}else u=s.firstChild();for(;u instanceof HTMLElement;)yield u,u=t.reverse?s.previousNode():s.nextNode();t.reverse&&o(e,i)&&(yield e)}function i(e,t=!1){return r(e,{reverse:t,strict:!0,onlyTabbable:!0}).next().value}function a(e,t=!1){let n=["BUTTON","INPUT","SELECT","TEXTAREA","OPTGROUP","OPTION","FIELDSET"].includes(e.tagName)&&e.disabled,r=e.hidden,i=e instanceof HTMLInputElement&&"hidden"===e.type,a=e.classList.contains("sentinel");if(n||r||i||a)return!1;if(t){let t=0===e.offsetWidth||0===e.offsetHeight,n=["hidden","collapse"].includes(getComputedStyle(e).visibility),r=0===e.getClientRects().length;if(t||n||r)return!1}return null!=e.getAttribute("tabindex")||(!(e instanceof HTMLAnchorElement)||null!=e.getAttribute("href"))&&-1!==e.tabIndex}function l(e,t=!1){return a(e,t)&&"-1"!==e.getAttribute("tabindex")}n.d(t,{EB:()=>a,O:()=>i,Wq:()=>l,hT:()=>r})},47142:(e,t,n)=>{n.d(t,{CD:()=>y,DU:()=>i,Gs:()=>g,m7:()=>b});var r=-1/0,i=1/0,a=-.005,l=-.005,o=-.01,s=1,u=.9,c=.8,f=.7,d=.6;function h(e){return e.toLowerCase()===e}function p(e){return e.toUpperCase()===e}function v(e){for(var t=e.length,n=Array(t),r="/",i=0;i<t;i++){var a=e[i];"/"===r?n[i]=u:"-"===r||"_"===r||" "===r?n[i]=c:"."===r?n[i]=d:h(r)&&p(a)?n[i]=f:n[i]=0,r=a}return n}function m(e,t,n,i){for(var u=e.length,c=t.length,f=e.toLowerCase(),d=t.toLowerCase(),h=v(t,h),p=0;p<u;p++){n[p]=Array(c),i[p]=Array(c);for(var m=r,g=p===u-1?l:o,b=0;b<c;b++)if(f[p]===d[b]){var y=r;p?b&&(y=Math.max(i[p-1][b-1]+h[b],n[p-1][b-1]+s)):y=b*a+h[b],n[p][b]=y,i[p][b]=m=Math.max(y,m+g)}else n[p][b]=r,i[p][b]=m+=g}}function g(e,t){var n=e.length,a=t.length;if(!n||!a)return r;if(n===a)return i;if(a>1024)return r;var l=Array(n),o=Array(n);return m(e,t,l,o),o[n-1][a-1]}function b(e,t){var n=e.length,i=t.length,a=Array(n);if(!n||!i)return a;if(n===i){for(var l=0;l<n;l++)a[l]=l;return a}if(i>1024)return a;var o=Array(n),u=Array(n);m(e,t,o,u);for(var c=!1,l=n-1,f=i-1;l>=0;l--)for(;f>=0;f--)if(o[l][f]!==r&&(c||o[l][f]===u[l][f])){c=l&&f&&u[l][f]===o[l-1][f-1]+s,a[l]=f--;break}return a}function y(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var n=e.length,r=0,i=0;r<n;r+=1)if(0===(i=t.indexOf(e[r],i)+1))return!1;return!0}},69567:(e,t,n)=>{function*r(e){let t="",n=0,r=!1;for(let i=0;i<e.length;i+=1)"{"!==e[i]||"{"!==e[i+1]||"\\"===e[i-1]||r?"}"===e[i]&&"}"===e[i+1]&&"\\"!==e[i-1]&&r&&(r=!1,yield{type:"part",start:n,end:i+2,value:t.slice(2).trim()},t="",i+=2,n=i):(r=!0,t&&(yield{type:"string",start:n,end:i,value:t}),t="{{",n=i,i+=2),t+=e[i]||"";t&&(yield{type:"string",start:n,end:e.length,value:t})}n.d(t,{R:()=>TemplateInstance,XK:()=>g});var i,a,l,o,s,u=function(e,t,n){if(!t.has(e))throw TypeError("attempted to set private field on non-instance");return t.set(e,n),n},c=function(e,t){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return t.get(e)};let AttributeTemplatePart=class AttributeTemplatePart{constructor(e,t){this.expression=t,i.set(this,void 0),a.set(this,""),u(this,i,e),c(this,i).updateParent("")}get attributeName(){return c(this,i).attr.name}get attributeNamespace(){return c(this,i).attr.namespaceURI}get value(){return c(this,a)}set value(e){u(this,a,e||""),c(this,i).updateParent(e)}get element(){return c(this,i).element}get booleanValue(){return c(this,i).booleanValue}set booleanValue(e){c(this,i).booleanValue=e}};i=new WeakMap,a=new WeakMap;let AttributeValueSetter=class AttributeValueSetter{constructor(e,t){this.element=e,this.attr=t,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(e){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=e?"":null}append(e){this.partList.push(e)}updateParent(e){if(1===this.partList.length&&null===e)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let e=this.partList.map(e=>"string"==typeof e?e:e.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,e)}}};var f=function(e,t,n){if(!t.has(e))throw TypeError("attempted to set private field on non-instance");return t.set(e,n),n},d=function(e,t){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return t.get(e)};let NodeTemplatePart=class NodeTemplatePart{constructor(e,t){this.expression=t,l.set(this,void 0),f(this,l,[e]),e.textContent=""}get value(){return d(this,l).map(e=>e.textContent).join("")}set value(e){this.replace(e)}get previousSibling(){return d(this,l)[0].previousSibling}get nextSibling(){return d(this,l)[d(this,l).length-1].nextSibling}replace(...e){let t=e.map(e=>"string"==typeof e?new Text(e):e);for(let e of(t.length||t.push(new Text("")),d(this,l)[0].before(...t),d(this,l)))e.remove();f(this,l,t)}};function h(e){return{createCallback(e,t,n){this.processCallback(e,t,n)},processCallback(t,n,r){var i;if("object"==typeof r&&r){for(let t of n)if(t.expression in r){let n=null!==(i=r[t.expression])&&void 0!==i?i:"";e(t,n)}}}}}function p(e,t){e.value=String(t)}function v(e,t){return"boolean"==typeof t&&e instanceof AttributeTemplatePart&&"boolean"==typeof e.element[e.attributeName]&&(e.booleanValue=t,!0)}l=new WeakMap;let m=h(p),g=h((e,t)=>{v(e,t)||p(e,t)});var b=function(e,t,n){if(!t.has(e))throw TypeError("attempted to set private field on non-instance");return t.set(e,n),n},y=function(e,t){if(!t.has(e))throw TypeError("attempted to get private field on non-instance");return t.get(e)};function*T(e){let t;let n=e.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null,!1);for(;t=n.nextNode();)if(t instanceof Element&&t.hasAttributes())for(let e=0;e<t.attributes.length;e+=1){let n=t.attributes.item(e);if(n&&n.value.includes("{{")){let e=new AttributeValueSetter(t,n);for(let t of r(n.value))if("string"===t.type)e.append(t.value);else{let n=new AttributeTemplatePart(e,t.value);e.append(n),yield n}}}else if(t instanceof Text&&t.textContent&&t.textContent.includes("{{"))for(let e of r(t.textContent)){e.end<t.textContent.length&&t.splitText(e.end),"part"===e.type&&(yield new NodeTemplatePart(t,e.value));break}}let TemplateInstance=class TemplateInstance extends DocumentFragment{constructor(e,t,n=m){var r,i;super(),o.set(this,void 0),s.set(this,void 0),Object.getPrototypeOf(this!==TemplateInstance.prototype)&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(e.content.cloneNode(!0)),b(this,s,Array.from(T(this))),b(this,o,n),null===(i=(r=y(this,o)).createCallback)||void 0===i||i.call(r,this,y(this,s),t)}update(e){y(this,o).processCallback(this,y(this,s),e)}};o=new WeakMap,s=new WeakMap}}]);
//# sourceMappingURL=vendors-node_modules_github_session-resume_dist_index_js-node_modules_primer_behaviors_dist_e-ac74c6-57d774d79baf.js.map