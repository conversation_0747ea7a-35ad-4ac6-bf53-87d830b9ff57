"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_morphdom_dist_morphdom-esm_js"],{39492:(e,t,n)=>{n.d(t,{Z:()=>y});var r,a=11;function i(e,t){var n,r,i,o,d=t.attributes;if(t.nodeType!==a&&e.nodeType!==a){for(var l=d.length-1;l>=0;l--)r=(n=d[l]).name,i=n.namespaceURI,o=n.value,i?(r=n.localName||r,e.getAttributeNS(i,r)!==o&&("xmlns"===n.prefix&&(r=n.name),e.setAttributeNS(i,r,o))):e.getAttribute(r)!==o&&e.setAttribute(r,o);for(var u=e.attributes,c=u.length-1;c>=0;c--)r=(n=u[c]).name,(i=n.namespaceURI)?(r=n.localName||r,t.hasAttributeNS(i,r)||e.removeAttributeNS(i,r)):t.hasAttribute(r)||e.removeAttribute(r)}}var o="http://www.w3.org/1999/xhtml",d="undefined"==typeof document?void 0:document,l=!!d&&"content"in d.createElement("template"),u=!!d&&d.createRange&&"createContextualFragment"in d.createRange();function c(e){var t=d.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}function f(e){return r||(r=d.createRange()).selectNode(d.body),r.createContextualFragment(e).childNodes[0]}function s(e){var t=d.createElement("body");return t.innerHTML=e,t.childNodes[0]}function m(e){return(e=e.trim(),l)?c(e):u?f(e):s(e)}function v(e,t){var n,r,a=e.nodeName,i=t.nodeName;return a===i||((n=a.charCodeAt(0),r=i.charCodeAt(0),n<=90&&r>=97)?a===i.toUpperCase():r<=90&&n>=97&&i===a.toUpperCase())}function p(e,t){return t&&t!==o?d.createElementNS(t,e):d.createElement(e)}function h(e,t){for(var n=e.firstChild;n;){var r=n.nextSibling;t.appendChild(n),n=r}return t}function N(e,t,n){e[n]!==t[n]&&(e[n]=t[n],e[n]?e.setAttribute(n,""):e.removeAttribute(n))}var b={OPTION:function(e,t){var n=e.parentNode;if(n){var r=n.nodeName.toUpperCase();"OPTGROUP"===r&&(r=(n=n.parentNode)&&n.nodeName.toUpperCase()),"SELECT"!==r||n.hasAttribute("multiple")||(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),n.selectedIndex=-1)}N(e,t,"selected")},INPUT:function(e,t){N(e,t,"checked"),N(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var n=t.value;e.value!==n&&(e.value=n);var r=e.firstChild;if(r){var a=r.nodeValue;if(a==n||!n&&a==e.placeholder)return;r.nodeValue=n}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var n,r,a=-1,i=0,o=e.firstChild;o;)if("OPTGROUP"===(r=o.nodeName&&o.nodeName.toUpperCase()))o=(n=o).firstChild;else{if("OPTION"===r){if(o.hasAttribute("selected")){a=i;break}i++}(o=o.nextSibling)||!n||(o=n.nextSibling,n=null)}e.selectedIndex=a}}},A=1,C=11,T=3,g=8;function S(){}function E(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}var x=function(e,t,n){if(n||(n={}),"string"==typeof t){if("#document"===e.nodeName||"HTML"===e.nodeName||"BODY"===e.nodeName){var r=t;(t=d.createElement("html")).innerHTML=r}else t=m(t)}var a=n.getNodeKey||E,o=n.onBeforeNodeAdded||S,l=n.onNodeAdded||S,u=n.onBeforeElUpdated||S,c=n.onElUpdated||S,f=n.onBeforeNodeDiscarded||S,s=n.onNodeDiscarded||S,N=n.onBeforeElChildrenUpdated||S,x=!0===n.childrenOnly,y=Object.create(null),U=[];function O(e){U.push(e)}function R(e,t,n){!1!==f(e)&&(t&&t.removeChild(e),s(e),function e(t,n){if(t.nodeType===A)for(var r=t.firstChild;r;){var i=void 0;n&&(i=a(r))?O(i):(s(r),r.firstChild&&e(r,n)),r=r.nextSibling}}(e,n))}!function e(t){if(t.nodeType===A||t.nodeType===C)for(var n=t.firstChild;n;){var r=a(n);r&&(y[r]=n),e(n),n=n.nextSibling}}(e);var V=e,w=V.nodeType,I=t.nodeType;if(!x){if(w===A)I===A?v(e,t)||(s(e),V=h(e,p(t.nodeName,t.namespaceURI))):V=t;else if(w===T||w===g){if(I===w)return V.nodeValue!==t.nodeValue&&(V.nodeValue=t.nodeValue),V;V=t}}if(V===t)s(e);else{if(t.isSameNode&&t.isSameNode(V))return;if(function e(t,n,r){var f=a(n);f&&delete y[f],(r||!1!==u(t,n)&&(i(t,n),c(t),!1!==N(t,n)))&&("TEXTAREA"!==t.nodeName?function(t,n){var r,i,u,c,f,s=n.firstChild,m=t.firstChild;e:for(;s;){for(c=s.nextSibling,r=a(s);m;){if(u=m.nextSibling,s.isSameNode&&s.isSameNode(m)){s=c,m=u;continue e}i=a(m);var p=m.nodeType,h=void 0;if(p===s.nodeType&&(p===A?(r?r!==i&&((f=y[r])?u===f?h=!1:(t.insertBefore(f,m),i?O(i):R(m,t,!0),m=f):h=!1):i&&(h=!1),(h=!1!==h&&v(m,s))&&e(m,s)):(p===T||p==g)&&(h=!0,m.nodeValue!==s.nodeValue&&(m.nodeValue=s.nodeValue))),h){s=c,m=u;continue e}i?O(i):R(m,t,!0),m=u}if(r&&(f=y[r])&&v(f,s))t.appendChild(f),e(f,s);else{var N=o(s);!1!==N&&(N&&(s=N),s.actualize&&(s=s.actualize(t.ownerDocument||d)),t.appendChild(s),function t(n){l(n);for(var r=n.firstChild;r;){var i=r.nextSibling,o=a(r);if(o){var d=y[o];d&&v(r,d)?(r.parentNode.replaceChild(d,r),e(d,r)):t(r)}else t(r);r=i}}(s))}s=c,m=u}!function(e,t,n){for(;t;){var r=t.nextSibling;(n=a(t))?O(n):R(t,e,!0),t=r}}(t,m,i);var C=b[t.nodeName];C&&C(t,n)}(t,n):b.TEXTAREA(t,n))}(V,t,x),U)for(var P=0,k=U.length;P<k;P++){var B=y[U[P]];B&&R(B,B.parentNode,!1)}}return!x&&V!==e&&e.parentNode&&(V.actualize&&(V=V.actualize(e.ownerDocument||d)),e.parentNode.replaceChild(V,e)),V};let y=x}}]);
//# sourceMappingURL=vendors-node_modules_morphdom_dist_morphdom-esm_js-9202af65c2cb.js.map