(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_optimizely_optimizely-sdk_dist_optimizely_browser_es_min_js-node_modules-089adc"],{53949:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(13196);function i(){return Math.round(1e3*Math.random())}var o=function(){function e(){this.errorCount=0}return e.prototype.getDelay=function(){return 0===this.errorCount?0:1e3*n.BACKOFF_BASE_WAIT_SECONDS_BY_ERROR_COUNT[Math.min(n.BACKOFF_BASE_WAIT_SECONDS_BY_ERROR_COUNT.length-1,this.errorCount)]+i()},e.prototype.countError=function(){this.errorCount<n.BACKOFF_BASE_WAIT_SECONDS_BY_ERROR_COUNT.length-1&&this.errorCount++},e.prototype.reset=function(){this.errorCount=0},e}();t.default=o},20731:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)t.hasOwnProperty(r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var s=r(80064),a=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return i(t,e),t.prototype.makeGetRequest=function(e,t){return s.makeGetRequest(e,t)},t.prototype.getConfigDefaults=function(){return{autoUpdate:!1}},t}(o(r(20377)).default);t.default=a},80064:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=r(13196),i=r(98125).getLogger("DatafileManager"),o="GET",s=4;function a(e){var t=e.getAllResponseHeaders();if(null===t)return{};var r=t.split("\r\n"),n={};return r.forEach(function(e){var t=e.indexOf(": ");if(t>-1){var r=e.slice(0,t),i=e.slice(t+2);i.length>0&&(n[r]=i)}}),n}function u(e,t){Object.keys(e).forEach(function(r){var n=e[r];t.setRequestHeader(r,n)})}function l(e,t){var r=new XMLHttpRequest;return{responsePromise:new Promise(function(l,c){r.open(o,e,!0),u(t,r),r.onreadystatechange=function(){if(r.readyState===s){if(0===r.status){c(Error("Request error"));return}var e=a(r);l({statusCode:r.status,body:r.responseText,headers:e})}},r.timeout=n.REQUEST_TIMEOUT_MS,r.ontimeout=function(){i.error("Request timed out")},r.send()}),abort:function(){r.abort()}}}t.makeGetRequest=l},13196:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_UPDATE_INTERVAL=3e5,t.MIN_UPDATE_INTERVAL=1e3,t.DEFAULT_URL_TEMPLATE="https://cdn.optimizely.com/datafiles/%s.json",t.DEFAULT_AUTHENTICATED_URL_TEMPLATE="https://config.optimizely.com/datafiles/auth/%s.json",t.BACKOFF_BASE_WAIT_SECONDS_BY_ERROR_COUNT=[0,8,16,32,64,128,256,512],t.REQUEST_TIMEOUT_MS=6e4},10181:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(){this.listeners={},this.listenerId=1}return e.prototype.on=function(e,t){var r=this;this.listeners[e]||(this.listeners[e]={});var n=String(this.listenerId);return this.listenerId++,this.listeners[e][n]=t,function(){r.listeners[e]&&delete r.listeners[e][n]}},e.prototype.emit=function(e,t){var r=this.listeners[e];r&&Object.keys(r).forEach(function(e){(0,r[e])(t)})},e.prototype.removeAllListeners=function(){this.listeners={}},e}();t.default=r},20377:function(e,t,r){"use strict";var n=this&&this.__assign||function(){return(n=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)},i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});var o=r(98125),s=r(27378),a=i(r(10181)),u=r(13196),l=i(r(53949)),c=o.getLogger("DatafileManager");function E(e){return e>=200&&e<400}var f={get:function(){return Promise.resolve("")},set:function(){return Promise.resolve()},contains:function(){return Promise.resolve(!1)},remove:function(){return Promise.resolve()}},d=function(){function e(e){var t=this,r=n(n({},this.getConfigDefaults()),e),i=r.datafile,o=r.autoUpdate,E=r.sdkKey,d=r.updateInterval,p=void 0===d?u.DEFAULT_UPDATE_INTERVAL:d,g=r.urlTemplate,I=void 0===g?u.DEFAULT_URL_TEMPLATE:g,h=r.cache;(this.cache=void 0===h?f:h,this.cacheKey="opt-datafile-"+E,this.isReadyPromiseSettled=!1,this.readyPromiseResolver=function(){},this.readyPromiseRejecter=function(){},this.readyPromise=new Promise(function(e,r){t.readyPromiseResolver=e,t.readyPromiseRejecter=r}),i?(this.currentDatafile=i,E||this.resolveReadyPromise()):this.currentDatafile="",this.isStarted=!1,this.datafileUrl=s.sprintf(I,E),this.emitter=new a.default,this.autoUpdate=void 0!==o&&o,p>=u.MIN_UPDATE_INTERVAL)?this.updateInterval=p:(c.warn("Invalid updateInterval %s, defaulting to %s",p,u.DEFAULT_UPDATE_INTERVAL),this.updateInterval=u.DEFAULT_UPDATE_INTERVAL),this.currentTimeout=null,this.currentRequest=null,this.backoffController=new l.default,this.syncOnCurrentRequestComplete=!1}return e.prototype.get=function(){return this.currentDatafile},e.prototype.start=function(){this.isStarted||(c.debug("Datafile manager started"),this.isStarted=!0,this.backoffController.reset(),this.setDatafileFromCacheIfAvailable(),this.syncDatafile())},e.prototype.stop=function(){return c.debug("Datafile manager stopped"),this.isStarted=!1,this.currentTimeout&&(clearTimeout(this.currentTimeout),this.currentTimeout=null),this.emitter.removeAllListeners(),this.currentRequest&&(this.currentRequest.abort(),this.currentRequest=null),Promise.resolve()},e.prototype.onReady=function(){return this.readyPromise},e.prototype.on=function(e,t){return this.emitter.on(e,t)},e.prototype.onRequestRejected=function(e){this.isStarted&&(this.backoffController.countError(),e instanceof Error?c.error("Error fetching datafile: %s",e.message,e):"string"==typeof e?c.error("Error fetching datafile: %s",e):c.error("Error fetching datafile"))},e.prototype.onRequestResolved=function(e){if(this.isStarted){void 0!==e.statusCode&&E(e.statusCode)?this.backoffController.reset():this.backoffController.countError(),this.trySavingLastModified(e.headers);var t=this.getNextDatafileFromResponse(e);""!==t&&(c.info("Updating datafile from response"),this.currentDatafile=t,this.cache.set(this.cacheKey,t),this.isReadyPromiseSettled?this.emitter.emit("update",{datafile:t}):this.resolveReadyPromise())}},e.prototype.onRequestComplete=function(){this.isStarted&&(this.currentRequest=null,this.isReadyPromiseSettled||this.autoUpdate||this.rejectReadyPromise(Error("Failed to become ready")),this.autoUpdate&&this.syncOnCurrentRequestComplete&&this.syncDatafile(),this.syncOnCurrentRequestComplete=!1)},e.prototype.syncDatafile=function(){var e=this,t={};this.lastResponseLastModified&&(t["if-modified-since"]=this.lastResponseLastModified),c.debug("Making datafile request to url %s with headers: %s",this.datafileUrl,function(){return JSON.stringify(t)}),this.currentRequest=this.makeGetRequest(this.datafileUrl,t);var r=function(){e.onRequestComplete()};this.currentRequest.responsePromise.then(function(t){e.onRequestResolved(t)},function(t){e.onRequestRejected(t)}).then(r,r),this.autoUpdate&&this.scheduleNextUpdate()},e.prototype.resolveReadyPromise=function(){this.readyPromiseResolver(),this.isReadyPromiseSettled=!0},e.prototype.rejectReadyPromise=function(e){this.readyPromiseRejecter(e),this.isReadyPromiseSettled=!0},e.prototype.scheduleNextUpdate=function(){var e=this,t=Math.max(this.backoffController.getDelay(),this.updateInterval);c.debug("Scheduling sync in %s ms",t),this.currentTimeout=setTimeout(function(){e.currentRequest?e.syncOnCurrentRequestComplete=!0:e.syncDatafile()},t)},e.prototype.getNextDatafileFromResponse=function(e){return(c.debug("Response status code: %s",e.statusCode),void 0===e.statusCode||304===e.statusCode)?"":E(e.statusCode)?e.body:""},e.prototype.trySavingLastModified=function(e){var t=e["last-modified"]||e["Last-Modified"];void 0!==t&&(this.lastResponseLastModified=t,c.debug("Saved last modified header value from response: %s",this.lastResponseLastModified))},e.prototype.setDatafileFromCacheIfAvailable=function(){var e=this;this.cache.get(this.cacheKey).then(function(t){e.isStarted&&!e.isReadyPromiseSettled&&""!==t&&(c.debug("Using datafile from cache"),e.currentDatafile=t,e.resolveReadyPromise())})},e}();t.default=d},62002:(e,t,r)=>{"use strict";var n=r(20731);t.z=n.default},67473:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},24909:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendEventNotification=t.getQueue=t.validateAndGetBatchSize=t.validateAndGetFlushInterval=t.DEFAULT_BATCH_SIZE=t.DEFAULT_FLUSH_INTERVAL=void 0;var n=r(31459),i=r(98125),o=r(27378);t.DEFAULT_FLUSH_INTERVAL=3e4,t.DEFAULT_BATCH_SIZE=10;var s=i.getLogger("EventProcessor");function a(e){return e<=0&&(s.warn("Invalid flushInterval "+e+", defaulting to "+t.DEFAULT_FLUSH_INTERVAL),e=t.DEFAULT_FLUSH_INTERVAL),e}function u(e){return(e=Math.floor(e))<1&&(s.warn("Invalid batchSize "+e+", defaulting to "+t.DEFAULT_BATCH_SIZE),e=t.DEFAULT_BATCH_SIZE),e=Math.max(1,e)}function l(e,t,r,i){return e>1?new n.DefaultEventQueue({flushInterval:t,maxQueueSize:e,sink:r,batchComparator:i}):new n.SingleEventQueue({sink:r})}function c(e,t){e&&e.sendNotifications(o.NOTIFICATION_TYPES.LOG_EVENT,t)}t.validateAndGetFlushInterval=a,t.validateAndGetBatchSize=u,t.getQueue=l,t.sendEventNotification=c},31459:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultEventQueue=t.SingleEventQueue=void 0;var n=r(98125).getLogger("EventProcessor"),i=function(){function e(e){var t=e.timeout,r=e.callback;this.timeout=Math.max(t,0),this.callback=r}return e.prototype.start=function(){this.timeoutId=setTimeout(this.callback,this.timeout)},e.prototype.refresh=function(){this.stop(),this.start()},e.prototype.stop=function(){this.timeoutId&&clearTimeout(this.timeoutId)},e}(),o=function(){function e(e){var t=e.sink;this.sink=t}return e.prototype.start=function(){},e.prototype.stop=function(){return Promise.resolve()},e.prototype.enqueue=function(e){this.sink([e])},e}();t.SingleEventQueue=o;var s=function(){function e(e){var t=e.flushInterval,r=e.maxQueueSize,n=e.sink,o=e.batchComparator;this.buffer=[],this.maxQueueSize=Math.max(r,1),this.sink=n,this.batchComparator=o,this.timer=new i({callback:this.flush.bind(this),timeout:t}),this.started=!1}return e.prototype.start=function(){this.started=!0},e.prototype.stop=function(){this.started=!1;var e=this.sink(this.buffer);return this.buffer=[],this.timer.stop(),e},e.prototype.enqueue=function(e){if(!this.started){n.warn("Queue is stopped, not accepting event");return}var t=this.buffer[0];t&&!this.batchComparator(t,e)&&this.flush(),0===this.buffer.length&&this.timer.refresh(),this.buffer.push(e),this.buffer.length>=this.maxQueueSize&&this.flush()},e.prototype.flush=function(){this.sink(this.buffer),this.buffer=[],this.timer.stop()},e}();t.DefaultEventQueue=s},51074:(e,t)=>{"use strict";function r(e,t){var r=e.context,n=t.context;return r.accountId===n.accountId&&r.projectId===n.projectId&&r.clientName===n.clientName&&r.clientVersion===n.clientVersion&&r.revision===n.revision&&r.anonymizeIP===n.anonymizeIP&&r.botFiltering===n.botFiltering}Object.defineProperty(t,"__esModule",{value:!0}),t.areEventContextsEqual=void 0,t.areEventContextsEqual=r},65001:function(e,t,r){"use strict";var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r),Object.defineProperty(e,n,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),i=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||n(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),i(r(51074),t),i(r(24909),t),i(r(67473),t),i(r(21310),t),i(r(36896),t),i(r(97168),t),i(r(18994),t)},21310:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},36896:function(e,t,r){"use strict";var n,i=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},function(e,t){function r(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(t,"__esModule",{value:!0}),t.LocalStoragePendingEventsDispatcher=t.PendingEventsDispatcher=void 0;var o=r(98125),s=r(36587),a=r(27378),u=o.getLogger("EventProcessor"),l=function(){function e(e){var t=e.eventDispatcher,r=e.store;this.dispatcher=t,this.store=r}return e.prototype.dispatchEvent=function(e,t){this.send({uuid:a.generateUUID(),timestamp:a.getTimestamp(),request:e},t)},e.prototype.sendPendingEvents=function(){var e=this,t=this.store.values();u.debug("Sending %s pending events from previous page",t.length),t.forEach(function(t){try{e.send(t,function(){})}catch(e){}})},e.prototype.send=function(e,t){var r=this;this.store.set(e.uuid,e),this.dispatcher.dispatchEvent(e.request,function(n){r.store.remove(e.uuid),t(n)})},e}();t.PendingEventsDispatcher=l;var c=function(e){function t(t){var r=t.eventDispatcher;return e.call(this,{eventDispatcher:r,store:new s.LocalStorageStore({maxValues:100,key:"fs_optly_pending_events"})})||this}return i(t,e),t}(l);t.LocalStoragePendingEventsDispatcher=c},36587:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.LocalStorageStore=void 0;var n=r(27378),i=r(98125).getLogger("EventProcessor"),o=function(){function e(e){var t=e.key,r=e.maxValues;this.LS_KEY=t,this.maxValues=void 0===r?1e3:r}return e.prototype.get=function(e){return this.getMap()[e]||null},e.prototype.set=function(e,t){var r=this.getMap();r[e]=t,this.replace(r)},e.prototype.remove=function(e){var t=this.getMap();delete t[e],this.replace(t)},e.prototype.values=function(){return n.objectValues(this.getMap())},e.prototype.clear=function(){this.replace({})},e.prototype.replace=function(e){try{window.localStorage&&localStorage.setItem(this.LS_KEY,JSON.stringify(e)),this.clean()}catch(e){i.error(e)}},e.prototype.clean=function(){var e=this.getMap(),t=Object.keys(e),r=t.length-this.maxValues;if(!(r<1)){var n=t.map(function(t){return{key:t,value:e[t]}});n.sort(function(e,t){return e.value.timestamp-t.value.timestamp});for(var i=0;i<r;i++)delete e[n[i].key];this.replace(e)}},e.prototype.getMap=function(){try{var e=window.localStorage&&localStorage.getItem(this.LS_KEY);if(e)return JSON.parse(e)||{}}catch(e){i.error(e)}return{}},e}();t.LocalStorageStore=o},90522:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(){this.reqsInFlightCount=0,this.reqsCompleteResolvers=[]}return e.prototype.trackRequest=function(e){var t=this;this.reqsInFlightCount++;var r=function(){t.reqsInFlightCount--,0===t.reqsInFlightCount&&(t.reqsCompleteResolvers.forEach(function(e){return e()}),t.reqsCompleteResolvers=[])};e.then(r,r)},e.prototype.onRequestsComplete=function(){var e=this;return new Promise(function(t){0===e.reqsInFlightCount?t():e.reqsCompleteResolvers.push(t)})},e}();t.default=r},97168:function(e,t){"use strict";var r=this&&this.__assign||function(){return(r=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0}),t.formatEvents=t.buildConversionEventV1=t.buildImpressionEventV1=t.makeBatchedEventV1=void 0;var n="$opt_bot_filtering";function i(e){var t=[],r=e[0];return e.forEach(function(e){if("conversion"===e.type||"impression"===e.type){var r=a(e);"impression"===e.type?r.snapshots.push(s(e)):"conversion"===e.type&&r.snapshots.push(o(e)),t.push(r)}}),{client_name:r.context.clientName,client_version:r.context.clientVersion,account_id:r.context.accountId,project_id:r.context.projectId,revision:r.context.revision,anonymize_ip:r.context.anonymizeIP,enrich_decisions:!0,visitors:t}}function o(e){var t=r({},e.tags);delete t.revenue,delete t.value;var n={entity_id:e.event.id,key:e.event.key,timestamp:e.timestamp,uuid:e.uuid};return e.tags&&(n.tags=e.tags),null!=e.value&&(n.value=e.value),null!=e.revenue&&(n.revenue=e.revenue),{events:[n]}}function s(e){var t,r,n=e.layer,i=e.experiment,o=e.variation,s=e.ruleKey,a=e.flagKey,u=e.ruleType,l=e.enabled,c=n?n.id:null;return{decisions:[{campaign_id:c,experiment_id:null!==(t=null==i?void 0:i.id)&&void 0!==t?t:"",variation_id:null!==(r=null==o?void 0:o.id)&&void 0!==r?r:"",metadata:{flag_key:a,rule_key:s,rule_type:u,variation_key:o?o.key:"",enabled:l}}],events:[{entity_id:c,timestamp:e.timestamp,key:"campaign_activated",uuid:e.uuid}]}}function a(e){var t={snapshots:[],visitor_id:e.user.id,attributes:[]};return e.user.attributes.forEach(function(e){t.attributes.push({entity_id:e.entityId,key:e.key,type:"custom",value:e.value})}),"boolean"==typeof e.context.botFiltering&&t.attributes.push({entity_id:n,key:n,type:"custom",value:e.context.botFiltering}),t}t.makeBatchedEventV1=i,t.buildImpressionEventV1=function(e){var t=a(e);return t.snapshots.push(s(e)),{client_name:e.context.clientName,client_version:e.context.clientVersion,account_id:e.context.accountId,project_id:e.context.projectId,revision:e.context.revision,anonymize_ip:e.context.anonymizeIP,enrich_decisions:!0,visitors:[t]}},t.buildConversionEventV1=function(e){var t=a(e);return t.snapshots.push(o(e)),{client_name:e.context.clientName,client_version:e.context.clientVersion,account_id:e.context.accountId,project_id:e.context.projectId,revision:e.context.revision,anonymize_ip:e.context.anonymizeIP,enrich_decisions:!0,visitors:[t]}},t.formatEvents=function(e){return{url:"https://logx.optimizely.com/v1/events",httpVerb:"POST",params:i(e)}}},18994:function(e,t,r){"use strict";var n=this&&this.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{u(n.next(e))}catch(e){o(e)}}function a(e){try{u(n.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}u((n=n.apply(e,t||[])).next())})},i=this&&this.__generator||function(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}},o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.LogTierV1EventProcessor=void 0;var s=r(98125),a=r(24909),u=o(r(90522)),l=r(51074),c=r(97168),E=s.getLogger("LogTierV1EventProcessor"),f=function(){function e(e){var t=e.dispatcher,r=e.flushInterval,n=void 0===r?a.DEFAULT_FLUSH_INTERVAL:r,i=e.batchSize,o=void 0===i?a.DEFAULT_BATCH_SIZE:i,s=e.notificationCenter;this.dispatcher=t,this.notificationCenter=s,this.requestTracker=new u.default,n=a.validateAndGetFlushInterval(n),o=a.validateAndGetBatchSize(o),this.queue=a.getQueue(o,n,this.drainQueue.bind(this),l.areEventContextsEqual)}return e.prototype.drainQueue=function(e){var t=this,r=new Promise(function(r){if(E.debug("draining queue with %s events",e.length),0===e.length){r();return}var n=c.formatEvents(e);t.dispatcher.dispatchEvent(n,function(){r()}),a.sendEventNotification(t.notificationCenter,n)});return this.requestTracker.trackRequest(r),r},e.prototype.process=function(e){this.queue.enqueue(e)},e.prototype.stop=function(){try{return this.queue.stop(),this.requestTracker.onRequestsComplete()}catch(e){E.error('Error stopping EventProcessor: "%s"',e.message,e)}return Promise.resolve()},e.prototype.start=function(){return n(this,void 0,void 0,function(){return i(this,function(e){return this.queue.start(),[2]})})},e}();t.LogTierV1EventProcessor=f},27987:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(){}return e.prototype.handleError=function(e){},e}();t.NoopErrorHandler=r;var n=new r;function i(e){n=e}function o(){return n}function s(){n=new r}t.setErrorHandler=i,t.getErrorHandler=o,t.resetErrorHandler=s},98125:(e,t,r)=>{"use strict";function n(e){for(var r in e)t.hasOwnProperty(r)||(t[r]=e[r])}Object.defineProperty(t,"__esModule",{value:!0}),n(r(27987)),n(r(99623)),n(r(46773))},46773:function(e,t,r){"use strict";var n=this&&this.__spreadArrays||function(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;for(var n=Array(e),i=0,t=0;t<r;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)n[i]=o[s];return n};Object.defineProperty(t,"__esModule",{value:!0});var i=r(27987),o=r(27378),s=r(99623),a={NOTSET:0,DEBUG:1,INFO:2,WARNING:3,ERROR:4};function u(e){return"string"!=typeof e?e:("WARN"===(e=e.toUpperCase())&&(e="WARNING"),a[e])?a[e]:e}var l=function(){function e(){this.defaultLoggerFacade=new d,this.loggers={}}return e.prototype.getLogger=function(e){return e?(this.loggers[e]||(this.loggers[e]=new d({messagePrefix:e})),this.loggers[e]):this.defaultLoggerFacade},e}(),c=function(){function e(e){void 0===e&&(e={}),this.logLevel=s.LogLevel.NOTSET,void 0!==e.logLevel&&o.isValidEnum(s.LogLevel,e.logLevel)&&this.setLogLevel(e.logLevel),this.logToConsole=void 0===e.logToConsole||!!e.logToConsole,this.prefix=void 0!==e.prefix?e.prefix:"[OPTIMIZELY]"}return e.prototype.log=function(e,t){if(this.shouldLog(e)&&this.logToConsole){var r=this.prefix+" - "+this.getLogLevelName(e)+" "+this.getTime()+" "+t;this.consoleLog(e,[r])}},e.prototype.setLogLevel=function(e){e=u(e),o.isValidEnum(s.LogLevel,e)&&void 0!==e?this.logLevel=e:this.logLevel=s.LogLevel.ERROR},e.prototype.getTime=function(){return new Date().toISOString()},e.prototype.shouldLog=function(e){return e>=this.logLevel},e.prototype.getLogLevelName=function(e){switch(e){case s.LogLevel.DEBUG:return"DEBUG";case s.LogLevel.INFO:return"INFO ";case s.LogLevel.WARNING:return"WARN ";case s.LogLevel.ERROR:return"ERROR";default:return"NOTSET"}},e.prototype.consoleLog=function(e,t){switch(e){case s.LogLevel.DEBUG:console.log.apply(console,t);break;case s.LogLevel.INFO:console.info.apply(console,t);break;case s.LogLevel.WARNING:console.warn.apply(console,t);break;case s.LogLevel.ERROR:console.error.apply(console,t);break;default:console.log.apply(console,t)}},e}();t.ConsoleLogHandler=c;var E=s.LogLevel.NOTSET,f=null,d=function(){function e(e){void 0===e&&(e={}),this.messagePrefix="",e.messagePrefix&&(this.messagePrefix=e.messagePrefix)}return e.prototype.log=function(e,t){for(var r=[],n=2;n<arguments.length;n++)r[n-2]=arguments[n];this.internalLog(u(e),{message:t,splat:r})},e.prototype.info=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.namedLog(s.LogLevel.INFO,e,t)},e.prototype.debug=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.namedLog(s.LogLevel.DEBUG,e,t)},e.prototype.warn=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.namedLog(s.LogLevel.WARNING,e,t)},e.prototype.error=function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];this.namedLog(s.LogLevel.ERROR,e,t)},e.prototype.format=function(e){return(this.messagePrefix?this.messagePrefix+": ":"")+o.sprintf.apply(void 0,n([e.message],e.splat))},e.prototype.internalLog=function(e,t){f&&!(e<E)&&(f.log(e,this.format(t)),t.error&&t.error instanceof Error&&i.getErrorHandler().handleError(t.error))},e.prototype.namedLog=function(e,t,r){if(t instanceof Error){t=(n=t).message,this.internalLog(e,{error:n,message:t,splat:r});return}if(0===r.length){this.internalLog(e,{message:t,splat:r});return}var n,i=r[r.length-1];i instanceof Error&&(n=i,r.splice(-1)),this.internalLog(e,{message:t,error:n,splat:r})},e}(),p=new l;t.getLogger=function(e){return p.getLogger(e)},t.setLogHandler=function(e){f=e},t.setLogLevel=function(e){e=u(e),E=o.isValidEnum(s.LogLevel,e)&&void 0!==e?e:s.LogLevel.ERROR},t.getLogLevel=function(){return E},t.resetLogger=function(){p=new l,E=s.LogLevel.NOTSET}},99623:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.NOTSET=0]="NOTSET",e[e.DEBUG=1]="DEBUG",e[e.INFO=2]="INFO",e[e.WARNING=3]="WARNING",e[e.ERROR=4]="ERROR"}(r=t.LogLevel||(t.LogLevel={}))},27378:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,i=r(328);function o(){return i.v4()}function s(){return new Date().getTime()}function a(e,t){for(var r=!1,n=Object.keys(e),i=0;i<n.length;i++)if(t===e[n[i]]){r=!0;break}return r}function u(e,t){var r={};return e.forEach(function(e){var n=t(e);r[n]=r[n]||[],r[n].push(e)}),l(r)}function l(e){return Object.keys(e).map(function(t){return e[t]})}function c(e){return Object.keys(e).map(function(t){return[t,e[t]]})}function E(e,t){for(var r,n=0;n<e.length;n++){var i=e[n];if(t(i)){r=i;break}}return r}function f(e,t){var r={};return e.forEach(function(e){r[t(e)]=e}),r}function d(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];var n=0;return e.replace(/%s/g,function(){var e=t[n++],r=typeof e;return"function"===r?e():"string"===r?e:String(e)})}t.generateUUID=o,t.getTimestamp=s,t.isValidEnum=a,t.groupBy=u,t.objectValues=l,t.objectEntries=c,t.find=E,t.keyBy=f,t.sprintf=d,function(e){e.ACTIVATE="ACTIVATE:experiment, user_id,attributes, variation, event",e.DECISION="DECISION:type, userId, attributes, decisionInfo",e.LOG_EVENT="LOG_EVENT:logEvent",e.OPTIMIZELY_CONFIG_UPDATE="OPTIMIZELY_CONFIG_UPDATE",e.TRACK="TRACK:event_key, user_id, attributes, event_tags, event"}(n=t.NOTIFICATION_TYPES||(t.NOTIFICATION_TYPES={}))},48266:(e,t,r)=>{"use strict";r.d(t,{EK:()=>n.setLogHandler,Fs:()=>e1,Ub:()=>n.setLogLevel});var n=r(98125),i=r.n(n),o=r(65001),s=r.n(o),a=r(27378),u=r(58053),l=r.n(u),c=r(62002),E=function(){return(E=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function f(){for(var e=0,t=0,r=arguments.length;t<r;t++)e+=arguments[t].length;var n=Array(e),i=0;for(t=0;t<r;t++)for(var o=arguments[t],s=0,a=o.length;s<a;s++,i++)n[i]=o[s];return n}var d={NOTSET:0,DEBUG:1,INFO:2,WARNING:3,ERROR:4},p={CONDITION_EVALUATOR_ERROR:"%s: Error evaluating audience condition of type %s: %s",DATAFILE_AND_SDK_KEY_MISSING:"%s: You must provide at least one of sdkKey or datafile. Cannot start Optimizely",EXPERIMENT_KEY_NOT_IN_DATAFILE:"%s: Experiment key %s is not in datafile.",FEATURE_NOT_IN_DATAFILE:"%s: Feature key %s is not in datafile.",IMPROPERLY_FORMATTED_EXPERIMENT:"%s: Experiment key %s is improperly formatted.",INVALID_ATTRIBUTES:"%s: Provided attributes are in an invalid format.",INVALID_BUCKETING_ID:"%s: Unable to generate hash for bucketing ID %s: %s",INVALID_DATAFILE:"%s: Datafile is invalid - property %s: %s",INVALID_DATAFILE_MALFORMED:"%s: Datafile is invalid because it is malformed.",INVALID_CONFIG:"%s: Provided Optimizely config is in an invalid format.",INVALID_JSON:"%s: JSON object is not valid.",INVALID_ERROR_HANDLER:'%s: Provided "errorHandler" is in an invalid format.',INVALID_EVENT_DISPATCHER:'%s: Provided "eventDispatcher" is in an invalid format.',INVALID_EVENT_TAGS:"%s: Provided event tags are in an invalid format.",INVALID_EXPERIMENT_KEY:"%s: Experiment key %s is not in datafile. It is either invalid, paused, or archived.",INVALID_EXPERIMENT_ID:"%s: Experiment ID %s is not in datafile.",INVALID_GROUP_ID:"%s: Group ID %s is not in datafile.",INVALID_LOGGER:'%s: Provided "logger" is in an invalid format.',INVALID_ROLLOUT_ID:"%s: Invalid rollout ID %s attached to feature %s",INVALID_USER_ID:"%s: Provided user ID is in an invalid format.",INVALID_USER_PROFILE_SERVICE:"%s: Provided user profile service instance is in an invalid format: %s.",NO_DATAFILE_SPECIFIED:"%s: No datafile specified. Cannot start optimizely.",NO_JSON_PROVIDED:"%s: No JSON object to validate against schema.",NO_VARIATION_FOR_EXPERIMENT_KEY:"%s: No variation key %s defined in datafile for experiment %s.",UNDEFINED_ATTRIBUTE:"%s: Provided attribute: %s has an undefined value.",UNRECOGNIZED_ATTRIBUTE:"%s: Unrecognized attribute %s provided. Pruning before sending event to Optimizely.",UNABLE_TO_CAST_VALUE:"%s: Unable to cast value %s to type %s, returning null.",USER_NOT_IN_FORCED_VARIATION:"%s: User %s is not in the forced variation map. Cannot remove their forced variation.",USER_PROFILE_LOOKUP_ERROR:'%s: Error while looking up user profile for user ID "%s": %s.',USER_PROFILE_SAVE_ERROR:'%s: Error while saving user profile for user ID "%s": %s.',VARIABLE_KEY_NOT_IN_DATAFILE:'%s: Variable with key "%s" associated with feature with key "%s" is not in datafile.',VARIATION_ID_NOT_IN_DATAFILE:"%s: No variation ID %s defined in datafile for experiment %s.",VARIATION_ID_NOT_IN_DATAFILE_NO_EXPERIMENT:"%s: Variation ID %s is not in the datafile.",INVALID_INPUT_FORMAT:"%s: Provided %s is in an invalid format.",INVALID_DATAFILE_VERSION:"%s: This version of the JavaScript SDK does not support the given datafile version: %s",INVALID_VARIATION_KEY:"%s: Provided variation key is in an invalid format."},g={ACTIVATE_USER:"%s: Activating user %s in experiment %s.",DISPATCH_CONVERSION_EVENT:"%s: Dispatching conversion event to URL %s with params %s.",DISPATCH_IMPRESSION_EVENT:"%s: Dispatching impression event to URL %s with params %s.",DEPRECATED_EVENT_VALUE:"%s: Event value is deprecated in %s call.",EVENT_KEY_NOT_FOUND:"%s: Event key %s is not in datafile.",EXPERIMENT_NOT_RUNNING:"%s: Experiment %s is not running.",FEATURE_ENABLED_FOR_USER:"%s: Feature %s is enabled for user %s.",FEATURE_NOT_ENABLED_FOR_USER:"%s: Feature %s is not enabled for user %s.",FEATURE_HAS_NO_EXPERIMENTS:"%s: Feature %s is not attached to any experiments.",FAILED_TO_PARSE_VALUE:'%s: Failed to parse event value "%s" from event tags.',FAILED_TO_PARSE_REVENUE:'%s: Failed to parse revenue value "%s" from event tags.',FORCED_BUCKETING_FAILED:"%s: Variation key %s is not in datafile. Not activating user %s.",INVALID_OBJECT:"%s: Optimizely object is not valid. Failing %s.",INVALID_CLIENT_ENGINE:"%s: Invalid client engine passed: %s. Defaulting to node-sdk.",INVALID_DEFAULT_DECIDE_OPTIONS:"%s: Provided default decide options is not an array.",INVALID_DECIDE_OPTIONS:"%s: Provided decide options is not an array. Using default decide options.",INVALID_VARIATION_ID:"%s: Bucketed into an invalid variation ID. Returning null.",NOTIFICATION_LISTENER_EXCEPTION:"%s: Notification listener for (%s) threw exception: %s",NO_ROLLOUT_EXISTS:"%s: There is no rollout of feature %s.",NOT_ACTIVATING_USER:"%s: Not activating user %s for experiment %s.",NOT_TRACKING_USER:"%s: Not tracking user %s.",PARSED_REVENUE_VALUE:'%s: Parsed revenue value "%s" from event tags.',PARSED_NUMERIC_VALUE:'%s: Parsed event value "%s" from event tags.',RETURNING_STORED_VARIATION:'%s: Returning previously activated variation "%s" of experiment "%s" for user "%s" from user profile.',ROLLOUT_HAS_NO_EXPERIMENTS:"%s: Rollout of feature %s has no experiments",SAVED_VARIATION:'%s: Saved variation "%s" of experiment "%s" for user "%s".',SAVED_VARIATION_NOT_FOUND:"%s: User %s was previously bucketed into variation with ID %s for experiment %s, but no matching variation was found.",SHOULD_NOT_DISPATCH_ACTIVATE:'%s: Experiment %s is not in "Running" state. Not activating user.',SKIPPING_JSON_VALIDATION:"%s: Skipping JSON schema validation.",TRACK_EVENT:"%s: Tracking event %s for user %s.",UNRECOGNIZED_DECIDE_OPTION:"%s: Unrecognized decide option %s provided.",USER_ASSIGNED_TO_EXPERIMENT_BUCKET:"%s: Assigned bucket %s to user with bucketing ID %s.",USER_BUCKETED_INTO_EXPERIMENT_IN_GROUP:"%s: User %s is in experiment %s of group %s.",USER_BUCKETED_INTO_TARGETING_RULE:"%s: User %s bucketed into targeting rule %s.",USER_IN_FEATURE_EXPERIMENT:"%s: User %s is in variation %s of experiment %s on the feature %s.",USER_IN_ROLLOUT:"%s: User %s is in rollout of feature %s.",USER_NOT_BUCKETED_INTO_EVERYONE_TARGETING_RULE:"%s: User %s not bucketed into everyone targeting rule due to traffic allocation.",USER_NOT_BUCKETED_INTO_EXPERIMENT_IN_GROUP:"%s: User %s is not in experiment %s of group %s.",USER_NOT_BUCKETED_INTO_ANY_EXPERIMENT_IN_GROUP:"%s: User %s is not in any experiment of group %s.",USER_NOT_BUCKETED_INTO_TARGETING_RULE:"%s User %s not bucketed into targeting rule %s due to traffic allocation. Trying everyone rule.",USER_NOT_IN_FEATURE_EXPERIMENT:"%s: User %s is not in any experiment on the feature %s.",USER_NOT_IN_ROLLOUT:"%s: User %s is not in rollout of feature %s.",USER_FORCED_IN_VARIATION:"%s: User %s is forced in variation %s.",USER_MAPPED_TO_FORCED_VARIATION:"%s: Set variation %s for experiment %s and user %s in the forced variation map.",USER_DOESNT_MEET_CONDITIONS_FOR_TARGETING_RULE:"%s: User %s does not meet conditions for targeting rule %s.",USER_MEETS_CONDITIONS_FOR_TARGETING_RULE:"%s: User %s meets conditions for targeting rule %s.",USER_HAS_VARIATION:"%s: User %s is in variation %s of experiment %s.",USER_HAS_FORCED_DECISION_WITH_RULE_SPECIFIED:"Variation (%s) is mapped to flag (%s), rule (%s) and user (%s) in the forced decision map.",USER_HAS_FORCED_DECISION_WITH_NO_RULE_SPECIFIED:"Variation (%s) is mapped to flag (%s) and user (%s) in the forced decision map.",USER_HAS_FORCED_DECISION_WITH_RULE_SPECIFIED_BUT_INVALID:"Invalid variation is mapped to flag (%s), rule (%s) and user (%s) in the forced decision map.",USER_HAS_FORCED_DECISION_WITH_NO_RULE_SPECIFIED_BUT_INVALID:"Invalid variation is mapped to flag (%s) and user (%s) in the forced decision map.",USER_HAS_FORCED_VARIATION:"%s: Variation %s is mapped to experiment %s and user %s in the forced variation map.",USER_HAS_NO_VARIATION:"%s: User %s is in no variation of experiment %s.",USER_HAS_NO_FORCED_VARIATION:"%s: User %s is not in the forced variation map.",USER_HAS_NO_FORCED_VARIATION_FOR_EXPERIMENT:"%s: No experiment %s mapped to user %s in the forced variation map.",USER_NOT_IN_ANY_EXPERIMENT:"%s: User %s is not in any experiment of group %s.",USER_NOT_IN_EXPERIMENT:"%s: User %s does not meet conditions to be in experiment %s.",USER_RECEIVED_DEFAULT_VARIABLE_VALUE:'%s: User "%s" is not in any variation or rollout rule. Returning default value for variable "%s" of feature flag "%s".',FEATURE_NOT_ENABLED_RETURN_DEFAULT_VARIABLE_VALUE:'%s: Feature "%s" is not enabled for user %s. Returning the default variable value "%s".',VARIABLE_NOT_USED_RETURN_DEFAULT_VARIABLE_VALUE:'%s: Variable "%s" is not used in variation "%s". Returning default value.',USER_RECEIVED_VARIABLE_VALUE:'%s: Got variable value "%s" for variable "%s" of feature flag "%s"',VALID_DATAFILE:"%s: Datafile is valid.",VALID_USER_PROFILE_SERVICE:"%s: Valid user profile service provided.",VARIATION_REMOVED_FOR_USER:"%s: Variation mapped to experiment %s has been removed for user %s.",VARIABLE_REQUESTED_WITH_WRONG_TYPE:'%s: Requested variable type "%s", but variable is of type "%s". Use correct API to retrieve value. Returning None.',VALID_BUCKETING_ID:'%s: BucketingId is valid: "%s"',BUCKETING_ID_NOT_STRING:"%s: BucketingID attribute is not a string. Defaulted to userId",EVALUATING_AUDIENCE:'%s: Starting to evaluate audience "%s" with conditions: %s.',EVALUATING_AUDIENCES_COMBINED:'%s: Evaluating audiences for %s "%s": %s.',AUDIENCE_EVALUATION_RESULT:'%s: Audience "%s" evaluated to %s.',AUDIENCE_EVALUATION_RESULT_COMBINED:"%s: Audiences for %s %s collectively evaluated to %s.",MISSING_ATTRIBUTE_VALUE:'%s: Audience condition %s evaluated to UNKNOWN because no value was passed for user attribute "%s".',UNEXPECTED_CONDITION_VALUE:"%s: Audience condition %s evaluated to UNKNOWN because the condition value is not supported.",UNEXPECTED_TYPE:'%s: Audience condition %s evaluated to UNKNOWN because a value of type "%s" was passed for user attribute "%s".',UNEXPECTED_TYPE_NULL:'%s: Audience condition %s evaluated to UNKNOWN because a null value was passed for user attribute "%s".',UNKNOWN_CONDITION_TYPE:"%s: Audience condition %s has an unknown condition type. You may need to upgrade to a newer release of the Optimizely SDK.",UNKNOWN_MATCH_TYPE:"%s: Audience condition %s uses an unknown match type. You may need to upgrade to a newer release of the Optimizely SDK.",UPDATED_OPTIMIZELY_CONFIG:"%s: Updated Optimizely config to revision %s (project id %s)",OUT_OF_BOUNDS:'%s: Audience condition %s evaluated to UNKNOWN because the number value for user attribute "%s" is not in the range [-2^53, +2^53].',UNABLE_TO_ATTACH_UNLOAD:'%s: unable to bind optimizely.close() to page unload event: "%s"'},I={BOT_FILTERING:"$opt_bot_filtering",BUCKETING_ID:"$opt_bucketing_id",STICKY_BUCKETING_KEY:"$opt_experiment_bucket_map",USER_AGENT:"$opt_user_agent",FORCED_DECISION_NULL_RULE_KEY:"$opt_null_rule_key"},h=a.NOTIFICATION_TYPES,_={AB_TEST:"ab-test",FEATURE:"feature",FEATURE_TEST:"feature-test",FEATURE_VARIABLE:"feature-variable",ALL_FEATURE_VARIABLES:"all-feature-variables",FLAG:"flag"},v={FEATURE_TEST:"feature-test",ROLLOUT:"rollout",EXPERIMENT:"experiment"},R={RULE:"rule",EXPERIMENT:"experiment"},O={BOOLEAN:"boolean",DOUBLE:"double",INTEGER:"integer",STRING:"string",JSON:"json"},y={V2:"2",V3:"3",V4:"4"},N={SDK_NOT_READY:"Optimizely SDK not configured properly yet.",FLAG_KEY_INVALID:'No flag was found for key "%s".',VARIABLE_VALUE_INVALID:'Variable value for key "%s" is invalid or wrong type.'},T=Object.freeze({__proto__:null,LOG_LEVEL:d,ERROR_MESSAGES:p,LOG_MESSAGES:g,CONTROL_ATTRIBUTES:I,JAVASCRIPT_CLIENT_ENGINE:"javascript-sdk",NODE_CLIENT_ENGINE:"node-sdk",REACT_CLIENT_ENGINE:"react-sdk",REACT_NATIVE_CLIENT_ENGINE:"react-native-sdk",REACT_NATIVE_JS_CLIENT_ENGINE:"react-native-js-sdk",NODE_CLIENT_VERSION:"4.9.1",NOTIFICATION_TYPES:h,DECISION_NOTIFICATION_TYPES:_,DECISION_SOURCES:v,AUDIENCE_EVALUATION_TYPES:R,FEATURE_VARIABLE_TYPES:O,DATAFILE_VERSIONS:y,DECISION_MESSAGES:N}),A="CONFIG_VALIDATOR",m=[y.V2,y.V3,y.V4],L=function(e){if("object"==typeof e&&null!==e){var t=e.errorHandler,r=e.eventDispatcher,n=e.logger;if(t&&"function"!=typeof t.handleError)throw Error((0,a.sprintf)(p.INVALID_ERROR_HANDLER,A));if(r&&"function"!=typeof r.dispatchEvent)throw Error((0,a.sprintf)(p.INVALID_EVENT_DISPATCHER,A));if(n&&"function"!=typeof n.log)throw Error((0,a.sprintf)(p.INVALID_LOGGER,A));return!0}throw Error((0,a.sprintf)(p.INVALID_CONFIG,A))},U=function(e){if(!e)throw Error((0,a.sprintf)(p.NO_DATAFILE_SPECIFIED,A));if("string"==typeof e)try{e=JSON.parse(e)}catch(e){throw Error((0,a.sprintf)(p.INVALID_DATAFILE_MALFORMED,A))}if("object"==typeof e&&!Array.isArray(e)&&null!==e&&-1===m.indexOf(e.version))throw Error((0,a.sprintf)(p.INVALID_DATAFILE_VERSION,A,e.version));return e},S={handleError:function(){}},D=function(e){return Object.keys(e).map(function(t){return encodeURIComponent(t)+"="+encodeURIComponent(e[t])}).join("&")},C={dispatchEvent:function(e,t){var r,n=e.params,i=e.url;"POST"===e.httpVerb?((r=new XMLHttpRequest).open("POST",i,!0),r.setRequestHeader("Content-Type","application/json"),r.onreadystatechange=function(){if(4===r.readyState&&t&&"function"==typeof t)try{t({statusCode:r.status})}catch(e){}},r.send(JSON.stringify(n))):(i+="?wxhr=true",n&&(i+="&"+D(n)),(r=new XMLHttpRequest).open("GET",i,!0),r.onreadystatechange=function(){if(4===r.readyState&&t&&"function"==typeof t)try{t({statusCode:r.status})}catch(e){}},r.send())}},V=function(){function e(){}return e.prototype.log=function(){},e}();function b(e){return new n.ConsoleLogHandler(e)}var P,F,M=Object.freeze({__proto__:null,NoOpLogger:V,createLogger:b,createNoOpLogger:function(){return new V}});function k(e,t,r){return{variationKey:null,enabled:!1,variables:{},ruleKey:null,flagKey:e,userContext:t,reasons:r}}!function(e){e.BOOLEAN="boolean",e.DOUBLE="double",e.INTEGER="integer",e.STRING="string",e.JSON="json"}(P||(P={})),function(e){e.DISABLE_DECISION_EVENT="DISABLE_DECISION_EVENT",e.ENABLED_FLAGS_ONLY="ENABLED_FLAGS_ONLY",e.IGNORE_USER_PROFILE_SERVICE="IGNORE_USER_PROFILE_SERVICE",e.INCLUDE_REASONS="INCLUDE_REASONS",e.EXCLUDE_VARIABLES="EXCLUDE_VARIABLES"}(F||(F={}));var x=function(){function e(e){var t,r=e.optimizely,n=e.userId,i=e.attributes;this.optimizely=r,this.userId=n,this.attributes=null!==(t=E({},i))&&void 0!==t?t:{},this.forcedDecisionsMap={}}return e.prototype.setAttribute=function(e,t){this.attributes[e]=t},e.prototype.getUserId=function(){return this.userId},e.prototype.getAttributes=function(){return E({},this.attributes)},e.prototype.getOptimizely=function(){return this.optimizely},e.prototype.decide=function(e,t){return void 0===t&&(t=[]),this.optimizely.decide(this.cloneUserContext(),e,t)},e.prototype.decideForKeys=function(e,t){return void 0===t&&(t=[]),this.optimizely.decideForKeys(this.cloneUserContext(),e,t)},e.prototype.decideAll=function(e){return void 0===e&&(e=[]),this.optimizely.decideAll(this.cloneUserContext(),e)},e.prototype.trackEvent=function(e,t){this.optimizely.track(e,this.userId,this.attributes,t)},e.prototype.setForcedDecision=function(e,t){var r,n=e.flagKey,i=null!==(r=e.ruleKey)&&void 0!==r?r:I.FORCED_DECISION_NULL_RULE_KEY,o={variationKey:t.variationKey};return this.forcedDecisionsMap[n]||(this.forcedDecisionsMap[n]={}),this.forcedDecisionsMap[n][i]=o,!0},e.prototype.getForcedDecision=function(e){return this.findForcedDecision(e)},e.prototype.removeForcedDecision=function(e){var t,r=null!==(t=e.ruleKey)&&void 0!==t?t:I.FORCED_DECISION_NULL_RULE_KEY,n=e.flagKey,i=!1;return this.forcedDecisionsMap.hasOwnProperty(n)&&(this.forcedDecisionsMap[n].hasOwnProperty(r)&&(delete this.forcedDecisionsMap[n][r],i=!0),0===Object.keys(this.forcedDecisionsMap[n]).length&&delete this.forcedDecisionsMap[n]),i},e.prototype.removeAllForcedDecisions=function(){return this.forcedDecisionsMap={},!0},e.prototype.findForcedDecision=function(e){var t,r=null!==(t=e.ruleKey)&&void 0!==t?t:I.FORCED_DECISION_NULL_RULE_KEY,n=e.flagKey;if(this.forcedDecisionsMap.hasOwnProperty(e.flagKey)){var i=this.forcedDecisionsMap[n];if(i.hasOwnProperty(r))return{variationKey:i[r].variationKey}}return null},e.prototype.cloneUserContext=function(){var t=new e({optimizely:this.getOptimizely(),userId:this.getUserId(),attributes:this.getAttributes()});return Object.keys(this.forcedDecisionsMap).length>0&&(t.forcedDecisionsMap=E({},this.forcedDecisionsMap)),t},e}(),B=["and","or","not"];function K(e,t){if(Array.isArray(e)){var r=e[0],n=e.slice(1);switch("string"==typeof r&&-1===B.indexOf(r)&&(r="or",n=e),r){case"and":return function(e,t){var r=!1;if(Array.isArray(e)){for(var n=0;n<e.length;n++){var i=K(e[n],t);if(!1===i)return!1;null===i&&(r=!0)}return!r||null}return null}(n,t);case"not":return function(e,t){if(Array.isArray(e)&&e.length>0){var r=K(e[0],t);return null===r?null:!r}return null}(n,t);default:return function(e,t){var r=!1;if(Array.isArray(e)){for(var n=0;n<e.length;n++){var i=K(e[n],t);if(!0===i)return!0;null===i&&(r=!0)}return!!r&&null}return null}(n,t)}}return t(e)}var G=function(){function e(t,r){this.sdkKey=null!==(n=t.sdkKey)&&void 0!==n?n:"",this.environmentKey=null!==(i=t.environmentKey)&&void 0!==i?i:"",this.attributes=t.attributes,this.audiences=e.getAudiences(t),this.events=t.events,this.revision=t.revision;var n,i,o=(t.featureFlags||[]).reduce(function(e,t){return e[t.id]=t.variables,e},{}),s=e.getExperimentsMapById(t,o);this.experimentsMap=e.getExperimentsKeyMap(s),this.featuresMap=e.getFeaturesMap(t,o,s),this.datafile=r}return e.prototype.getDatafile=function(){return this.datafile},e.getAudiences=function(e){var t=[],r=[];return(e.typedAudiences||[]).forEach(function(e){t.push({id:e.id,conditions:JSON.stringify(e.conditions),name:e.name}),r.push(e.id)}),(e.audiences||[]).forEach(function(e){-1===r.indexOf(e.id)&&"$opt_dummy_audience"!=e.id&&t.push({id:e.id,conditions:JSON.stringify(e.conditions),name:e.name})}),t},e.getSerializedAudiences=function(t,r){var n="";if(t){var i="";t.forEach(function(t){var o="";if(t instanceof Array)o="("+(o=e.getSerializedAudiences(t,r))+")";else if(B.indexOf(t)>-1)i=t.toUpperCase();else{var s=r[t]?r[t].name:t;n||"NOT"===i?(i=""===i?"OR":i,n=""===n?i+' "'+r[t].name+'"':n.concat(" "+i+' "'+s+'"')):n='"'+s+'"'}""!==o&&(""!==n||"NOT"===i?(i=""===i?"OR":i,n=""===n?i+" "+o:n.concat(" "+i+" "+o)):n=n.concat(o))})}return n},e.getExperimentAudiences=function(t,r){return t.audienceConditions?e.getSerializedAudiences(t.audienceConditions,r.audiencesById):""},e.mergeFeatureVariables=function(e,t,r,n,i){var o=(e[r]||[]).reduce(function(e,t){return e[t.key]={id:t.id,key:t.key,type:t.type,value:t.defaultValue},e},{});return(n||[]).forEach(function(e){var r=t[e.id],n={id:e.id,key:r.key,type:r.type,value:i?e.value:r.defaultValue};o[r.key]=n}),o},e.getVariationsMap=function(t,r,n,i){return t.reduce(function(t,o){var s=e.mergeFeatureVariables(r,n,i,o.variables,o.featureEnabled);return t[o.key]={id:o.id,key:o.key,featureEnabled:o.featureEnabled,variablesMap:s},t},{})},e.getVariableIdMap=function(e){return(e.featureFlags||[]).reduce(function(e,t){return t.variables.forEach(function(t){e[t.id]=t}),e},{})},e.getDeliveryRules=function(t,r,n,i){var o=e.getVariableIdMap(t);return i.map(function(i){return{id:i.id,key:i.key,audiences:e.getExperimentAudiences(i,t),variationsMap:e.getVariationsMap(i.variations,r,o,n)}})},e.getRolloutExperimentIds=function(e){var t=[];return(e||[]).forEach(function(e){e.experiments.forEach(function(e){t.push(e.id)})}),t},e.getExperimentsMapById=function(t,r){var n=e.getVariableIdMap(t),i=this.getRolloutExperimentIds(t.rollouts);return(t.experiments||[]).reduce(function(o,s){if(-1===i.indexOf(s.id)){var a=t.experimentFeatureMap[s.id],u="";a&&a.length>0&&(u=a[0]);var l=e.getVariationsMap(s.variations,r,n,u.toString());o[s.id]={id:s.id,key:s.key,audiences:e.getExperimentAudiences(s,t),variationsMap:l}}return o},{})},e.getExperimentsKeyMap=function(e){var t={};for(var r in e){var n=e[r];t[n.key]=n}return t},e.getFeaturesMap=function(t,r,n){var i={};return t.featureFlags.forEach(function(o){var s={},a=[];o.experimentIds.forEach(function(e){var t=n[e];t&&(s[t.key]=t),a.push(n[e])});var u=(o.variables||[]).reduce(function(e,t){return e[t.key]={id:t.id,key:t.key,type:t.type,value:t.defaultValue},e},{}),l=[],c=t.rolloutIdMap[o.rolloutId];c&&(l=e.getDeliveryRules(t,r,o.id,c.experiments)),i[o.key]={id:o.id,key:o.key,experimentRules:a,deliveryRules:l,experimentsMap:s,variablesMap:u}}),i},e}(),w=9007199254740992,j={assign:function(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(!e)return{};if("function"==typeof Object.assign)return Object.assign.apply(Object,f([e],t));for(var n=Object(e),i=0;i<t.length;i++){var o=t[i];if(null!=o)for(var s in o)Object.prototype.hasOwnProperty.call(o,s)&&(n[s]=o[s])}return n},currentTimestamp:function(){return Math.round((new Date).getTime())},isSafeInteger:function(e){return"number"==typeof e&&Math.abs(e)<=w},keyBy:function(e,t){return e?(0,a.keyBy)(e,function(e){return e[t]}):{}},uuid:a.generateUUID,isNumber:function(e){return"number"==typeof e}},H="PROJECT_CONFIG",Y=function(e,t){void 0===t&&(t=null);var r,n,i,o=((i=j.assign({},e)).audiences=(e.audiences||[]).map(function(e){return j.assign({},e)}),i.experiments=(e.experiments||[]).map(function(e){return j.assign({},e)}),i.featureFlags=(e.featureFlags||[]).map(function(e){return j.assign({},e)}),i.groups=(e.groups||[]).map(function(e){var t=j.assign({},e);return t.experiments=(e.experiments||[]).map(function(e){return j.assign({},e)}),t}),i.rollouts=(e.rollouts||[]).map(function(e){var t=j.assign({},e);return t.experiments=(e.experiments||[]).map(function(e){return j.assign({},e)}),t}),i.environmentKey=null!==(r=e.environmentKey)&&void 0!==r?r:"",i.sdkKey=null!==(n=e.sdkKey)&&void 0!==n?n:"",i);return o.__datafileStr=null===t?JSON.stringify(e):t,(o.audiences||[]).forEach(function(e){e.conditions=JSON.parse(e.conditions)}),o.audiencesById=j.keyBy(o.audiences,"id"),j.assign(o.audiencesById,j.keyBy(o.typedAudiences,"id")),o.attributeKeyMap=j.keyBy(o.attributes,"key"),o.eventKeyMap=j.keyBy(o.events,"key"),o.groupIdMap=j.keyBy(o.groups,"id"),Object.keys(o.groupIdMap||{}).forEach(function(e){(o.groupIdMap[e].experiments||[]).forEach(function(t){o.experiments.push(j.assign(t,{groupId:e}))})}),o.rolloutIdMap=j.keyBy(o.rollouts||[],"id"),(0,a.objectValues)(o.rolloutIdMap||{}).forEach(function(e){(e.experiments||[]).forEach(function(e){o.experiments.push(e),e.variationKeyMap=j.keyBy(e.variations,"key")})}),o.experimentKeyMap=j.keyBy(o.experiments,"key"),o.experimentIdMap=j.keyBy(o.experiments,"id"),o.variationIdMap={},o.variationVariableUsageMap={},(o.experiments||[]).forEach(function(e){e.variationKeyMap=j.keyBy(e.variations,"key"),j.assign(o.variationIdMap,j.keyBy(e.variations,"id")),(0,a.objectValues)(e.variationKeyMap||{}).forEach(function(e){e.variables&&(o.variationVariableUsageMap[e.id]=j.keyBy(e.variables,"id"))})}),o.experimentFeatureMap={},o.featureKeyMap=j.keyBy(o.featureFlags||[],"key"),(0,a.objectValues)(o.featureKeyMap||{}).forEach(function(e){e.variables.forEach(function(e){e.type===O.STRING&&e.subType===O.JSON&&(e.type=O.JSON,delete e.subType)}),e.variableKeyMap=j.keyBy(e.variables,"key"),(e.experimentIds||[]).forEach(function(t){o.experimentFeatureMap[t]?o.experimentFeatureMap[t].push(e.id):o.experimentFeatureMap[t]=[e.id]})}),o.flagRulesMap={},(o.featureFlags||[]).forEach(function(e){var t=[];e.experimentIds.forEach(function(e){var r=o.experimentIdMap[e];r&&t.push(r)});var r=o.rolloutIdMap[e.rolloutId];r&&t.push.apply(t,r.experiments),o.flagRulesMap[e.key]=t}),o.flagVariationsMap={},(0,a.objectEntries)(o.flagRulesMap||{}).forEach(function(e){var t=e[0],r=e[1],n=[];r.forEach(function(e){e.variations.forEach(function(e){(0,a.find)(n,function(t){return t.id===e.id})||n.push(e)})}),o.flagVariationsMap[t]=n}),o},z=function(e,t){var r=e.experimentIdMap[t];if(!r)throw Error((0,a.sprintf)(p.INVALID_EXPERIMENT_ID,H,t));return r.layerId},X=function(e,t,r){var n=e.attributeKeyMap[t],i=0===t.indexOf("$opt_");return n?(i&&r.log(d.WARNING,"Attribute %s unexpectedly has reserved prefix %s; using attribute ID instead of reserved attribute name.",t,"$opt_"),n.id):i?t:(r.log(d.DEBUG,p.UNRECOGNIZED_ATTRIBUTE,H,t),null)},q=function(e,t){var r=e.eventKeyMap[t];return r?r.id:null},J=function(e,t){var r=e.experimentKeyMap[t];if(!r)throw Error((0,a.sprintf)(p.INVALID_EXPERIMENT_KEY,H,t));return r.status},W=function(e,t){return e.variationIdMap.hasOwnProperty(t)?e.variationIdMap[t].key:null},Z=function(e,t){if(e.experimentKeyMap.hasOwnProperty(t)){var r=e.experimentKeyMap[t];if(r)return r}throw Error((0,a.sprintf)(p.EXPERIMENT_KEY_NOT_IN_DATAFILE,H,t))},Q=function(e,t){var r=e.experimentIdMap[t];if(!r)throw Error((0,a.sprintf)(p.INVALID_EXPERIMENT_ID,H,t));return r.trafficAllocation},$=function(e,t,r){if(e.experimentIdMap.hasOwnProperty(t)){var n=e.experimentIdMap[t];if(n)return n}return r.log(d.ERROR,p.INVALID_EXPERIMENT_ID,H,t),null},ee=function(e,t,r){if(!e)return null;var n=e.flagVariationsMap[t];return(0,a.find)(n,function(e){return e.key===r})||null},et=function(e,t,r){if(e.featureKeyMap.hasOwnProperty(t)){var n=e.featureKeyMap[t];if(n)return n}return r.log(d.ERROR,p.FEATURE_NOT_IN_DATAFILE,H,t),null},er=function(e){return e.__datafileStr},en=function(e){try{t=U(e.datafile)}catch(e){return{configObj:null,error:e}}if(e.jsonSchemaValidator)try{e.jsonSchemaValidator.validate(t),e.logger.log(d.INFO,g.VALID_DATAFILE,H)}catch(e){return{configObj:null,error:e}}else e.logger.log(d.INFO,g.SKIPPING_JSON_VALIDATION,H);var t,r=[t];return"string"==typeof e.datafile&&r.push(e.datafile),{configObj:Y.apply(void 0,r),error:null}},ei=function(e){return!!e.sendFlagDecisions},eo=(0,n.getLogger)();function es(e,t){return e instanceof Error?e.message:t||"Unknown error"}var ea=function(){function e(e){this.updateListeners=[],this.configObj=null,this.optimizelyConfigObj=null,this.datafileManager=null;try{if(this.jsonSchemaValidator=e.jsonSchemaValidator,!e.datafile&&!e.sdkKey){var t=Error((0,a.sprintf)(p.DATAFILE_AND_SDK_KEY_MISSING,"PROJECT_CONFIG_MANAGER"));return this.readyPromise=Promise.resolve({success:!1,reason:es(t)}),void eo.error(t)}var r=null;e.datafile&&(r=this.handleNewDatafile(e.datafile)),e.sdkKey&&e.datafileManager?(this.datafileManager=e.datafileManager,this.datafileManager.start(),this.readyPromise=this.datafileManager.onReady().then(this.onDatafileManagerReadyFulfill.bind(this),this.onDatafileManagerReadyReject.bind(this)),this.datafileManager.on("update",this.onDatafileManagerUpdate.bind(this))):this.configObj?this.readyPromise=Promise.resolve({success:!0}):this.readyPromise=Promise.resolve({success:!1,reason:es(r,"Invalid datafile")})}catch(e){eo.error(e),this.readyPromise=Promise.resolve({success:!1,reason:es(e,"Error in initialize")})}}return e.prototype.onDatafileManagerReadyFulfill=function(){if(this.datafileManager){var e=this.handleNewDatafile(this.datafileManager.get());return e?{success:!1,reason:es(e)}:{success:!0}}return{success:!1,reason:es(null,"Datafile manager is not provided")}},e.prototype.onDatafileManagerReadyReject=function(e){return{success:!1,reason:es(e,"Failed to become ready")}},e.prototype.onDatafileManagerUpdate=function(){this.datafileManager&&this.handleNewDatafile(this.datafileManager.get())},e.prototype.handleNewDatafile=function(e){var t=en({datafile:e,jsonSchemaValidator:this.jsonSchemaValidator,logger:eo}),r=t.configObj,n=t.error;if(n)eo.error(n);else{var i=this.configObj?this.configObj.revision:"null";r&&i!==r.revision&&(this.configObj=r,this.optimizelyConfigObj=null,this.updateListeners.forEach(function(e){return e(r)}))}return n},e.prototype.getConfig=function(){return this.configObj},e.prototype.getOptimizelyConfig=function(){var e,t;return!this.optimizelyConfigObj&&this.configObj&&(this.optimizelyConfigObj=(e=this.configObj,t=er(this.configObj),new G(e,t))),this.optimizelyConfigObj},e.prototype.onReady=function(){return this.readyPromise},e.prototype.onUpdate=function(e){var t=this;return this.updateListeners.push(e),function(){var r=t.updateListeners.indexOf(e);r>-1&&t.updateListeners.splice(r,1)}},e.prototype.stop=function(){this.datafileManager&&this.datafileManager.stop(),this.updateListeners=[]},e}(),eu=4294967296,el=function(e){var t=[],r=e.experimentIdMap[e.experimentId].groupId;if(r){var n=e.groupIdMap[r];if(!n)throw Error((0,a.sprintf)(p.INVALID_GROUP_ID,"BUCKETER",r));if("random"===n.policy){var i=ec(n,e.bucketingId,e.userId,e.logger);if(null===i)return e.logger.log(d.INFO,g.USER_NOT_IN_ANY_EXPERIMENT,"BUCKETER",e.userId,r),t.push([g.USER_NOT_IN_ANY_EXPERIMENT,"BUCKETER",e.userId,r]),{result:null,reasons:t};if(i!==e.experimentId)return e.logger.log(d.INFO,g.USER_NOT_BUCKETED_INTO_EXPERIMENT_IN_GROUP,"BUCKETER",e.userId,e.experimentKey,r),t.push([g.USER_NOT_BUCKETED_INTO_EXPERIMENT_IN_GROUP,"BUCKETER",e.userId,e.experimentKey,r]),{result:null,reasons:t};e.logger.log(d.INFO,g.USER_BUCKETED_INTO_EXPERIMENT_IN_GROUP,"BUCKETER",e.userId,e.experimentKey,r),t.push([g.USER_BUCKETED_INTO_EXPERIMENT_IN_GROUP,"BUCKETER",e.userId,e.experimentKey,r])}}var o=ef(""+e.bucketingId+e.experimentId);e.logger.log(d.DEBUG,g.USER_ASSIGNED_TO_EXPERIMENT_BUCKET,"BUCKETER",o,e.userId),t.push([g.USER_ASSIGNED_TO_EXPERIMENT_BUCKET,"BUCKETER",o,e.userId]);var s=eE(o,e.trafficAllocationConfig);return null===s||e.variationIdMap[s]?{result:s,reasons:t}:(s&&(e.logger.log(d.WARNING,g.INVALID_VARIATION_ID,"BUCKETER"),t.push([g.INVALID_VARIATION_ID,"BUCKETER"])),{result:null,reasons:t})},ec=function(e,t,r,n){var i=ef(""+t+e.id);return n.log(d.DEBUG,g.USER_ASSIGNED_TO_EXPERIMENT_BUCKET,"BUCKETER",i,r),eE(i,e.trafficAllocation)},eE=function(e,t){for(var r=0;r<t.length;r++)if(e<t[r].endOfRange)return t[r].entityId;return null},ef=function(e){try{var t=l().v3(e,1)/eu;return Math.floor(1e4*t)}catch(t){throw Error((0,a.sprintf)(p.INVALID_BUCKETING_ID,"BUCKETER",e,t.message))}},ed=(0,n.getLogger)();function ep(e){return/^\d+$/.test(e)}function eg(e){var t=e.indexOf("-"),r=e.indexOf("+");return!(t<0)&&(r<0||t<r)}function eI(e){var t=e.indexOf("-"),r=e.indexOf("+");return!(r<0)&&(t<0||r<t)}function eh(e){var t=e,r="";if(/\s/.test(e))return ed.warn(g.UNKNOWN_MATCH_TYPE,"SEMANTIC VERSION",e),null;if(eg(e)?(t=e.substring(0,e.indexOf("-")),r=e.substring(e.indexOf("-")+1)):eI(e)&&(t=e.substring(0,e.indexOf("+")),r=e.substring(e.indexOf("+")+1)),"string"!=typeof t||"string"!=typeof r)return null;var n=t.split(".").length-1;if(n>2)return ed.warn(g.UNKNOWN_MATCH_TYPE,"SEMANTIC VERSION",e),null;var i=t.split(".");if(i.length!=n+1)return ed.warn(g.UNKNOWN_MATCH_TYPE,"SEMANTIC VERSION",e),null;for(var o=0;o<i.length;o++)if(!ep(i[o]))return ed.warn(g.UNKNOWN_MATCH_TYPE,"SEMANTIC VERSION",e),null;return r&&i.push(r),i}var e_="CUSTOM_ATTRIBUTE_CONDITION_EVALUATOR",ev=(0,n.getLogger)(),eR=["exact","exists","gt","ge","lt","le","substring","semver_eq","semver_lt","semver_le","semver_gt","semver_ge"],eO={};function ey(e){return"string"==typeof e||"boolean"==typeof e||j.isNumber(e)}function eN(e,t){var r=e.value,n=e.name,i=t[n],o=typeof i;return!ey(r)||j.isNumber(r)&&!j.isSafeInteger(r)?(ev.warn(g.UNEXPECTED_CONDITION_VALUE,e_,JSON.stringify(e)),null):null===i?(ev.debug(g.UNEXPECTED_TYPE_NULL,e_,JSON.stringify(e),n),null):ey(i)&&typeof r===o?j.isNumber(i)&&!j.isSafeInteger(i)?(ev.warn(g.OUT_OF_BOUNDS,e_,JSON.stringify(e),n),null):r===i:(ev.warn(g.UNEXPECTED_TYPE,e_,JSON.stringify(e),o,n),null)}function eT(e,t){var r=e.name,n=t[r],i=e.value;return null!==i&&j.isSafeInteger(i)?null===n?(ev.debug(g.UNEXPECTED_TYPE_NULL,e_,JSON.stringify(e),r),!1):j.isNumber(n)?!!j.isSafeInteger(n)||(ev.warn(g.OUT_OF_BOUNDS,e_,JSON.stringify(e),r),!1):(ev.warn(g.UNEXPECTED_TYPE,e_,JSON.stringify(e),typeof n,r),!1):(ev.warn(g.UNEXPECTED_CONDITION_VALUE,e_,JSON.stringify(e)),!1)}function eA(e,t){var r=e.name,n=t[r],i=e.value;return"string"!=typeof i?(ev.warn(g.UNEXPECTED_CONDITION_VALUE,e_,JSON.stringify(e)),null):null===n?(ev.debug(g.UNEXPECTED_TYPE_NULL,e_,JSON.stringify(e),r),null):"string"!=typeof n?(ev.warn(g.UNEXPECTED_TYPE,e_,JSON.stringify(e),typeof n,r),null):function(e,t){var r=eh(t),n=eh(e);if(!r||!n)return null;for(var i=r.length,o=0;o<n.length;o++){if(i<=o)return eg(e)||eI(e)?1:-1;if(ep(r[o])){var s=parseInt(r[o]),a=parseInt(n[o]);if(s>a)return 1;if(s<a)return -1}else{if(r[o]<n[o])return eg(e)&&!eg(t)?1:-1;if(r[o]>n[o])return!eg(e)&&eg(t)?-1:1}}return eg(t)&&!eg(e)?-1:0}(i,n)}eO.exact=eN,eO.exists=function(e,t){return null!=t[e.name]},eO.gt=function(e,t){var r=t[e.name],n=e.value;return eT(e,t)&&null!==n?r>n:null},eO.ge=function(e,t){var r=t[e.name],n=e.value;return eT(e,t)&&null!==n?r>=n:null},eO.lt=function(e,t){var r=t[e.name],n=e.value;return eT(e,t)&&null!==n?r<n:null},eO.le=function(e,t){var r=t[e.name],n=e.value;return eT(e,t)&&null!==n?r<=n:null},eO.substring=function(e,t){var r=e.name,n=t[e.name],i=e.value;return"string"!=typeof i?(ev.warn(g.UNEXPECTED_CONDITION_VALUE,e_,JSON.stringify(e)),null):null===n?(ev.debug(g.UNEXPECTED_TYPE_NULL,e_,JSON.stringify(e),r),null):"string"!=typeof n?(ev.warn(g.UNEXPECTED_TYPE,e_,JSON.stringify(e),typeof n,r),null):-1!==n.indexOf(i)},eO.semver_eq=function(e,t){var r=eA(e,t);return null===r?null:0===r},eO.semver_gt=function(e,t){var r=eA(e,t);return null===r?null:r>0},eO.semver_ge=function(e,t){var r=eA(e,t);return null===r?null:r>=0},eO.semver_lt=function(e,t){var r=eA(e,t);return null===r?null:r<0},eO.semver_le=function(e,t){var r=eA(e,t);return null===r?null:r<=0};var em=Object.freeze({__proto__:null,evaluate:function(e,t){var r=e.match;if(void 0!==r&&-1===eR.indexOf(r))return ev.warn(g.UNKNOWN_MATCH_TYPE,e_,JSON.stringify(e)),null;var n=e.name;return t.hasOwnProperty(n)||"exists"==r?(r&&eO[r]||eN)(e,t):(ev.debug(g.MISSING_ATTRIBUTE_VALUE,e_,JSON.stringify(e),n),null)}}),eL=(0,n.getLogger)(),eU=function(){function e(e){this.typeToEvaluatorMap=j.assign({},e,{custom_attribute:em})}return e.prototype.evaluate=function(e,t,r){var n=this;return void 0===r&&(r={}),!e||0===e.length||!!K(e,function(e){var i=t[e];if(i){eL.log(d.DEBUG,g.EVALUATING_AUDIENCE,"AUDIENCE_EVALUATOR",e,JSON.stringify(i.conditions));var o=K(i.conditions,n.evaluateConditionWithUserAttributes.bind(n,r)),s=null===o?"UNKNOWN":o.toString().toUpperCase();return eL.log(d.DEBUG,g.AUDIENCE_EVALUATION_RESULT,"AUDIENCE_EVALUATOR",e,s),o}return null})},e.prototype.evaluateConditionWithUserAttributes=function(e,t){var r=this.typeToEvaluatorMap[t.type];if(!r)return eL.log(d.WARNING,g.UNKNOWN_CONDITION_TYPE,"AUDIENCE_EVALUATOR",JSON.stringify(t)),null;try{return r.evaluate(t,e)}catch(e){eL.log(d.ERROR,p.CONDITION_EVALUATOR_ERROR,"AUDIENCE_EVALUATOR",t.type,e.message)}return null},e}();function eS(e){return"string"==typeof e&&""!==e}var eD="DECISION_SERVICE",eC=function(){function e(e){var t;this.audienceEvaluator=(t=e.UNSTABLE_conditionEvaluators,new eU(t)),this.forcedVariationMap={},this.logger=e.logger,this.userProfileService=e.userProfileService||null}return e.prototype.getVariation=function(e,t,r,n){void 0===n&&(n={});var i=r.getUserId(),o=r.getAttributes(),s=this.getBucketingId(i,o),a=[],u=t.key;if(!this.checkIfExperimentIsActive(e,u))return this.logger.log(d.INFO,g.EXPERIMENT_NOT_RUNNING,eD,u),a.push([g.EXPERIMENT_NOT_RUNNING,eD,u]),{result:null,reasons:a};var l=this.getForcedVariation(e,u,i);a.push.apply(a,l.reasons);var c=l.result;if(c)return{result:c,reasons:a};var E=this.getWhitelistedVariation(t,i);a.push.apply(a,E.reasons);var f=E.result;if(f)return{result:f.key,reasons:a};var p=n[F.IGNORE_USER_PROFILE_SERVICE],I=this.resolveExperimentBucketMap(i,o);if(!p&&(f=this.getStoredVariation(e,t,i,I)))return this.logger.log(d.INFO,g.RETURNING_STORED_VARIATION,eD,f.key,u,i),a.push([g.RETURNING_STORED_VARIATION,eD,f.key,u,i]),{result:f.key,reasons:a};var h=this.checkIfUserIsInAudience(e,t,R.EXPERIMENT,o,"");if(a.push.apply(a,h.reasons),!h.result)return this.logger.log(d.INFO,g.USER_NOT_IN_EXPERIMENT,eD,i,u),a.push([g.USER_NOT_IN_EXPERIMENT,eD,i,u]),{result:null,reasons:a};var _=el(this.buildBucketerParams(e,t,s,i));a.push.apply(a,_.reasons);var v=_.result;return v&&(f=e.variationIdMap[v]),f?(this.logger.log(d.INFO,g.USER_HAS_VARIATION,eD,i,f.key,u),a.push([g.USER_HAS_VARIATION,eD,i,f.key,u]),p||this.saveUserProfile(t,f,i,I),{result:f.key,reasons:a}):(this.logger.log(d.DEBUG,g.USER_HAS_NO_VARIATION,eD,i,u),a.push([g.USER_HAS_NO_VARIATION,eD,i,u]),{result:null,reasons:a})},e.prototype.resolveExperimentBucketMap=function(e,t){t=t||{};var r=this.getUserProfile(e)||{},n=t[I.STICKY_BUCKETING_KEY];return j.assign({},r.experiment_bucket_map,n)},e.prototype.checkIfExperimentIsActive=function(e,t){return"Running"===J(e,t)},e.prototype.getWhitelistedVariation=function(e,t){var r=[];if(e.forcedVariations&&e.forcedVariations.hasOwnProperty(t)){var n=e.forcedVariations[t];return e.variationKeyMap.hasOwnProperty(n)?(this.logger.log(d.INFO,g.USER_FORCED_IN_VARIATION,eD,t,n),r.push([g.USER_FORCED_IN_VARIATION,eD,t,n]),{result:e.variationKeyMap[n],reasons:r}):(this.logger.log(d.ERROR,g.FORCED_BUCKETING_FAILED,eD,n,t),r.push([g.FORCED_BUCKETING_FAILED,eD,n,t]),{result:null,reasons:r})}return{result:null,reasons:r}},e.prototype.checkIfUserIsInAudience=function(e,t,r,n,i){var o=[],s=function(e,t){var r=e.experimentIdMap[t];if(!r)throw Error((0,a.sprintf)(p.INVALID_EXPERIMENT_ID,H,t));return r.audienceConditions||r.audienceIds}(e,t.id),u=e.audiencesById;this.logger.log(d.DEBUG,g.EVALUATING_AUDIENCES_COMBINED,eD,r,i||t.key,JSON.stringify(s)),o.push([g.EVALUATING_AUDIENCES_COMBINED,eD,r,i||t.key,JSON.stringify(s)]);var l=this.audienceEvaluator.evaluate(s,u,n);return this.logger.log(d.INFO,g.AUDIENCE_EVALUATION_RESULT_COMBINED,eD,r,i||t.key,l.toString().toUpperCase()),o.push([g.AUDIENCE_EVALUATION_RESULT_COMBINED,eD,r,i||t.key,l.toString().toUpperCase()]),{result:l,reasons:o}},e.prototype.buildBucketerParams=function(e,t,r,n){return{bucketingId:r,experimentId:t.id,experimentKey:t.key,experimentIdMap:e.experimentIdMap,experimentKeyMap:e.experimentKeyMap,groupIdMap:e.groupIdMap,logger:this.logger,trafficAllocationConfig:Q(e,t.id),userId:n,variationIdMap:e.variationIdMap}},e.prototype.getStoredVariation=function(e,t,r,n){if(n.hasOwnProperty(t.id)){var i=n[t.id],o=i.variation_id;if(e.variationIdMap.hasOwnProperty(o))return e.variationIdMap[i.variation_id];this.logger.log(d.INFO,g.SAVED_VARIATION_NOT_FOUND,eD,r,o,t.key)}return null},e.prototype.getUserProfile=function(e){if(!this.userProfileService)return{user_id:e,experiment_bucket_map:{}};try{return this.userProfileService.lookup(e)}catch(t){this.logger.log(d.ERROR,p.USER_PROFILE_LOOKUP_ERROR,eD,e,t.message)}return null},e.prototype.saveUserProfile=function(e,t,r,n){if(this.userProfileService)try{n[e.id]={variation_id:t.id},this.userProfileService.save({user_id:r,experiment_bucket_map:n}),this.logger.log(d.INFO,g.SAVED_VARIATION,eD,t.key,e.key,r)}catch(e){this.logger.log(d.ERROR,p.USER_PROFILE_SAVE_ERROR,eD,r,e.message)}},e.prototype.getVariationForFeature=function(e,t,r,n){void 0===n&&(n={});var i=[],o=this.getVariationForFeatureExperiment(e,t,r,n);i.push.apply(i,o.reasons);var s=o.result;if(null!==s.variation)return{result:s,reasons:i};var a=this.getVariationForRollout(e,t,r);i.push.apply(i,a.reasons);var u=a.result,l=r.getUserId();return u.variation?(this.logger.log(d.DEBUG,g.USER_IN_ROLLOUT,eD,l,t.key),i.push([g.USER_IN_ROLLOUT,eD,l,t.key]),{result:u,reasons:i}):(this.logger.log(d.DEBUG,g.USER_NOT_IN_ROLLOUT,eD,l,t.key),i.push([g.USER_NOT_IN_ROLLOUT,eD,l,t.key]),{result:u,reasons:i})},e.prototype.getVariationForFeatureExperiment=function(e,t,r,n){void 0===n&&(n={});var i,o,s=[],a=null;if(t.experimentIds.length>0)for(o=0;o<t.experimentIds.length;o++){var u=$(e,t.experimentIds[o],this.logger);if(u&&(i=this.getVariationFromExperimentRule(e,t.key,u,r,n),s.push.apply(s,i.reasons),a=i.result)){var l=null;return(l=u.variationKeyMap[a])||(l=ee(e,t.key,a)),{result:{experiment:u,variation:l,decisionSource:v.FEATURE_TEST},reasons:s}}}else this.logger.log(d.DEBUG,g.FEATURE_HAS_NO_EXPERIMENTS,eD,t.key),s.push([g.FEATURE_HAS_NO_EXPERIMENTS,eD,t.key]);return{result:{experiment:null,variation:null,decisionSource:v.FEATURE_TEST},reasons:s}},e.prototype.getVariationForRollout=function(e,t,r){var n=[];if(!t.rolloutId)return this.logger.log(d.DEBUG,g.NO_ROLLOUT_EXISTS,eD,t.key),n.push([g.NO_ROLLOUT_EXISTS,eD,t.key]),{result:{experiment:null,variation:null,decisionSource:v.ROLLOUT},reasons:n};var i=e.rolloutIdMap[t.rolloutId];if(!i)return this.logger.log(d.ERROR,p.INVALID_ROLLOUT_ID,eD,t.rolloutId,t.key),n.push([p.INVALID_ROLLOUT_ID,eD,t.rolloutId,t.key]),{result:{experiment:null,variation:null,decisionSource:v.ROLLOUT},reasons:n};var o,s,a,u=i.experiments;if(0===u.length)return this.logger.log(d.ERROR,g.ROLLOUT_HAS_NO_EXPERIMENTS,eD,t.rolloutId),n.push([g.ROLLOUT_HAS_NO_EXPERIMENTS,eD,t.rolloutId]),{result:{experiment:null,variation:null,decisionSource:v.ROLLOUT},reasons:n};for(var l=0;l<u.length;){if(o=this.getVariationFromDeliveryRule(e,t.key,u,l,r),n.push.apply(n,o.reasons),a=o.result,s=o.skipToEveryoneElse,a)return{result:{experiment:e.experimentIdMap[u[l].id],variation:a,decisionSource:v.ROLLOUT},reasons:n};l=s?u.length-1:l+1}return{result:{experiment:null,variation:null,decisionSource:v.ROLLOUT},reasons:n}},e.prototype.getBucketingId=function(e,t){var r=e;return null!=t&&"object"==typeof t&&t.hasOwnProperty(I.BUCKETING_ID)&&("string"==typeof t[I.BUCKETING_ID]?(r=t[I.BUCKETING_ID],this.logger.log(d.DEBUG,g.VALID_BUCKETING_ID,eD,r)):this.logger.log(d.WARNING,g.BUCKETING_ID_NOT_STRING,eD)),r},e.prototype.findValidatedForcedDecision=function(e,t,r,n){var i,o=[],s=t.getForcedDecision({flagKey:r,ruleKey:n}),a=null,u=t.getUserId();return e&&s&&(i=s.variationKey,(a=ee(e,r,i))?n?(this.logger.log(d.INFO,g.USER_HAS_FORCED_DECISION_WITH_RULE_SPECIFIED,i,r,n,u),o.push([g.USER_HAS_FORCED_DECISION_WITH_RULE_SPECIFIED,i,r,n,u])):(this.logger.log(d.INFO,g.USER_HAS_FORCED_DECISION_WITH_NO_RULE_SPECIFIED,i,r,u),o.push([g.USER_HAS_FORCED_DECISION_WITH_NO_RULE_SPECIFIED,i,r,u])):n?(this.logger.log(d.INFO,g.USER_HAS_FORCED_DECISION_WITH_RULE_SPECIFIED_BUT_INVALID,r,n,u),o.push([g.USER_HAS_FORCED_DECISION_WITH_RULE_SPECIFIED_BUT_INVALID,r,n,u])):(this.logger.log(d.INFO,g.USER_HAS_FORCED_DECISION_WITH_NO_RULE_SPECIFIED_BUT_INVALID,r,u),o.push([g.USER_HAS_FORCED_DECISION_WITH_NO_RULE_SPECIFIED_BUT_INVALID,r,u]))),{result:a,reasons:o}},e.prototype.removeForcedVariation=function(e,t,r){if(!e)throw Error((0,a.sprintf)(p.INVALID_USER_ID,eD));if(!this.forcedVariationMap.hasOwnProperty(e))throw Error((0,a.sprintf)(p.USER_NOT_IN_FORCED_VARIATION,eD,e));delete this.forcedVariationMap[e][t],this.logger.log(d.DEBUG,g.VARIATION_REMOVED_FOR_USER,eD,r,e)},e.prototype.setInForcedVariationMap=function(e,t,r){this.forcedVariationMap.hasOwnProperty(e)||(this.forcedVariationMap[e]={}),this.forcedVariationMap[e][t]=r,this.logger.log(d.DEBUG,g.USER_MAPPED_TO_FORCED_VARIATION,eD,r,t,e)},e.prototype.getForcedVariation=function(e,t,r){var n,i=[],o=this.forcedVariationMap[r];if(!o)return this.logger.log(d.DEBUG,g.USER_HAS_NO_FORCED_VARIATION,eD,r),{result:null,reasons:i};try{var s=Z(e,t);if(!s.hasOwnProperty("id"))return this.logger.log(d.ERROR,p.IMPROPERLY_FORMATTED_EXPERIMENT,eD,t),i.push([p.IMPROPERLY_FORMATTED_EXPERIMENT,eD,t]),{result:null,reasons:i};n=s.id}catch(e){return this.logger.log(d.ERROR,e.message),i.push(e.message),{result:null,reasons:i}}var a=o[n];if(!a)return this.logger.log(d.DEBUG,g.USER_HAS_NO_FORCED_VARIATION_FOR_EXPERIMENT,eD,t,r),{result:null,reasons:i};var u=W(e,a);return u?(this.logger.log(d.DEBUG,g.USER_HAS_FORCED_VARIATION,eD,u,t,r),i.push([g.USER_HAS_FORCED_VARIATION,eD,u,t,r])):this.logger.log(d.DEBUG,g.USER_HAS_NO_FORCED_VARIATION_FOR_EXPERIMENT,eD,t,r),{result:u,reasons:i}},e.prototype.setForcedVariation=function(e,t,r,n){if(null!=n&&!eS(n))return this.logger.log(d.ERROR,p.INVALID_VARIATION_KEY,eD),!1;try{var i,o,s=Z(e,t);if(!s.hasOwnProperty("id"))return this.logger.log(d.ERROR,p.IMPROPERLY_FORMATTED_EXPERIMENT,eD,t),!1;o=s.id}catch(e){return this.logger.log(d.ERROR,e.message),!1}if(null==n)try{return this.removeForcedVariation(r,o,t),!0}catch(e){return this.logger.log(d.ERROR,e.message),!1}var a=(i=e.experimentKeyMap[t]).variationKeyMap.hasOwnProperty(n)?i.variationKeyMap[n].id:null;if(!a)return this.logger.log(d.ERROR,p.NO_VARIATION_FOR_EXPERIMENT_KEY,eD,n,t),!1;try{return this.setInForcedVariationMap(r,o,a),!0}catch(e){return this.logger.log(d.ERROR,e.message),!1}},e.prototype.getVariationFromExperimentRule=function(e,t,r,n,i){void 0===i&&(i={});var o=[],s=this.findValidatedForcedDecision(e,n,t,r.key);o.push.apply(o,s.reasons);var a=s.result;if(a)return{result:a.key,reasons:o};var u=this.getVariation(e,r,n,i);return o.push.apply(o,u.reasons),{result:u.result,reasons:o}},e.prototype.getVariationFromDeliveryRule=function(e,t,r,n,i){var o=[],s=!1,a=r[n],u=this.findValidatedForcedDecision(e,i,t,a.key);o.push.apply(o,u.reasons);var l=u.result;if(l)return{result:l,reasons:o,skipToEveryoneElse:s};var c,E,f=i.getUserId(),p=i.getAttributes(),I=this.getBucketingId(f,p),h=n===r.length-1,_=h?"Everyone Else":n+1,v=null,O=this.checkIfUserIsInAudience(e,a,R.RULE,p,_);return o.push.apply(o,O.reasons),O.result?(this.logger.log(d.DEBUG,g.USER_MEETS_CONDITIONS_FOR_TARGETING_RULE,eD,f,_),o.push([g.USER_MEETS_CONDITIONS_FOR_TARGETING_RULE,eD,f,_]),E=el(this.buildBucketerParams(e,a,I,f)),o.push.apply(o,E.reasons),(c=E.result)&&(v=e.variationIdMap.hasOwnProperty(c)?e.variationIdMap[c]:null),v?(this.logger.log(d.DEBUG,g.USER_BUCKETED_INTO_TARGETING_RULE,eD,f,_),o.push([g.USER_BUCKETED_INTO_TARGETING_RULE,eD,f,_])):h||(this.logger.log(d.DEBUG,g.USER_NOT_BUCKETED_INTO_TARGETING_RULE,eD,f,_),o.push([g.USER_NOT_BUCKETED_INTO_TARGETING_RULE,eD,f,_]),s=!0)):(this.logger.log(d.DEBUG,g.USER_DOESNT_MEET_CONDITIONS_FOR_TARGETING_RULE,eD,f,_),o.push([g.USER_DOESNT_MEET_CONDITIONS_FOR_TARGETING_RULE,eD,f,_])),{result:v,reasons:o,skipToEveryoneElse:s}},e}();function eV(e,t){if(e.hasOwnProperty("revenue")){var r=e.revenue,n=void 0;return"string"==typeof r?isNaN(n=parseInt(r))?(t.log(d.INFO,g.FAILED_TO_PARSE_REVENUE,"EVENT_TAG_UTILS",r),null):(t.log(d.INFO,g.PARSED_REVENUE_VALUE,"EVENT_TAG_UTILS",n),n):"number"==typeof r?(n=r,t.log(d.INFO,g.PARSED_REVENUE_VALUE,"EVENT_TAG_UTILS",n),n):null}return null}function eb(e,t){if(e.hasOwnProperty("value")){var r=e.value,n=void 0;return"string"==typeof r?isNaN(n=parseFloat(r))?(t.log(d.INFO,g.FAILED_TO_PARSE_VALUE,"EVENT_TAG_UTILS",r),null):(t.log(d.INFO,g.PARSED_NUMERIC_VALUE,"EVENT_TAG_UTILS",n),n):"number"==typeof r?(n=r,t.log(d.INFO,g.PARSED_NUMERIC_VALUE,"EVENT_TAG_UTILS",n),n):null}return null}function eP(e,t){return"string"==typeof e&&("string"==typeof t||"boolean"==typeof t||j.isNumber(t)&&j.isSafeInteger(t))}var eF="https://logx.optimizely.com/v1/events";function eM(e){var t=e.attributes,r=e.userId,n=e.clientEngine,i=e.clientVersion,o=e.configObj,s=e.logger,a=!!o.anonymizeIP&&o.anonymizeIP,u=o.botFiltering,l={account_id:o.accountId,project_id:o.projectId,visitors:[{snapshots:[],visitor_id:r,attributes:[]}],revision:o.revision,client_name:n,client_version:i,anonymize_ip:a,enrich_decisions:!0};return t&&Object.keys(t||{}).forEach(function(e){if(eP(e,t[e])){var r=X(o,e,s);r&&l.visitors[0].attributes.push({entity_id:r,key:e,type:"custom",value:t[e]})}}),"boolean"==typeof u&&l.visitors[0].attributes.push({entity_id:I.BOT_FILTERING,key:I.BOT_FILTERING,type:"custom",value:u}),l}function ek(e){var t,r,n,i,o,s,a,u,l,c=eM(e),E=(t=e.configObj,r=e.experimentId,n=e.variationId,i=e.ruleKey,o=e.ruleType,s=e.flagKey,a=e.enabled,u=r?z(t,r):null,l=n?W(t,n):null,{decisions:[{campaign_id:u,experiment_id:r,variation_id:n,metadata:{flag_key:s,rule_key:i,rule_type:o,variation_key:l=l||"",enabled:a}}],events:[{entity_id:u,timestamp:j.currentTimestamp(),key:"campaign_activated",uuid:j.uuid()}]});return c.visitors[0].snapshots.push(E),{httpVerb:"POST",url:eF,params:c}}function ex(e){var t=eM(e),r=function(e,t,r,n){var i={events:[]},o={entity_id:q(e,t),timestamp:j.currentTimestamp(),uuid:j.uuid(),key:t};if(n){var s=eV(n,r);null!==s&&(o.revenue=s);var a=eb(n,r);null!==a&&(o.value=a),o.tags=n}return i.events.push(o),i}(e.configObj,e.eventKey,e.logger,e.eventTags);return t.visitors[0].snapshots=[r],{httpVerb:"POST",url:eF,params:t}}function eB(e){var t,r;return null!==(r=null===(t=e.experiment)||void 0===t?void 0:t.key)&&void 0!==r?r:""}function eK(e){var t,r;return null!==(r=null===(t=e.variation)||void 0===t?void 0:t.key)&&void 0!==r?r:""}function eG(e){var t,r;return null!==(r=null===(t=e.variation)||void 0===t?void 0:t.featureEnabled)&&void 0!==r&&r}function ew(e){var t,r;return null!==(r=null===(t=e.experiment)||void 0===t?void 0:t.id)&&void 0!==r?r:null}function ej(e){var t,r;return null!==(r=null===(t=e.variation)||void 0===t?void 0:t.id)&&void 0!==r?r:null}var eH=(0,n.getLogger)("EVENT_BUILDER");function eY(e,t){var r=[];return t&&Object.keys(t||{}).forEach(function(n){if(eP(n,t[n])){var i=X(e,n,eH);i&&r.push({entityId:i,key:n,value:t[n]})}}),r}var ez="USER_PROFILE_SERVICE_VALIDATOR",eX=function(){function e(e){var t,r=this,n=e.clientEngine;n||(e.logger.log(d.INFO,g.INVALID_CLIENT_ENGINE,"OPTIMIZELY",n),n="node-sdk"),this.clientEngine=n,this.clientVersion=e.clientVersion||"4.9.1",this.errorHandler=e.errorHandler,this.isOptimizelyConfigValid=e.isValidInstance,this.logger=e.logger;var i=null!==(t=e.defaultDecideOptions)&&void 0!==t?t:[];Array.isArray(i)||(this.logger.log(d.DEBUG,g.INVALID_DEFAULT_DECIDE_OPTIONS,"OPTIMIZELY"),i=[]);var o={};i.forEach(function(e){F[e]?o[e]=!0:r.logger.log(d.WARNING,g.UNRECOGNIZED_DECIDE_OPTION,"OPTIMIZELY",e)}),this.defaultDecideOptions=o,this.projectConfigManager=(s={datafile:e.datafile,jsonSchemaValidator:e.jsonSchemaValidator,sdkKey:e.sdkKey,datafileManager:e.datafileManager},new ea(s)),this.disposeOnUpdate=this.projectConfigManager.onUpdate(function(e){r.logger.log(d.INFO,g.UPDATED_OPTIMIZELY_CONFIG,"OPTIMIZELY",e.revision,e.projectId),r.notificationCenter.sendNotifications(h.OPTIMIZELY_CONFIG_UPDATE)});var s,u,l=this.projectConfigManager.onReady(),c=null;if(e.userProfileService)try{(function(e){if("object"==typeof e&&null!==e){if("function"!=typeof e.lookup)throw Error((0,a.sprintf)(p.INVALID_USER_PROFILE_SERVICE,ez,"Missing function 'lookup'"));if("function"!=typeof e.save)throw Error((0,a.sprintf)(p.INVALID_USER_PROFILE_SERVICE,ez,"Missing function 'save'"));return!0}throw Error((0,a.sprintf)(p.INVALID_USER_PROFILE_SERVICE,ez))})(e.userProfileService)&&(c=e.userProfileService,this.logger.log(d.INFO,g.VALID_USER_PROFILE_SERVICE,"OPTIMIZELY"))}catch(e){this.logger.log(d.WARNING,e.message)}this.decisionService=(u={userProfileService:c,logger:this.logger,UNSTABLE_conditionEvaluators:e.UNSTABLE_conditionEvaluators},new eC(u)),this.notificationCenter=e.notificationCenter,this.eventProcessor=e.eventProcessor;var E=this.eventProcessor.start();this.readyPromise=Promise.all([l,E]).then(function(e){return e[0]}),this.readyTimeouts={},this.nextReadyTimeoutId=0}return e.prototype.isValidInstance=function(){return this.isOptimizelyConfigValid&&!!this.projectConfigManager.getConfig()},e.prototype.activate=function(e,t,r){try{if(!this.isValidInstance())return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","activate"),null;if(!this.validateInputs({experiment_key:e,user_id:t},r))return this.notActivatingExperiment(e,t);var n=this.projectConfigManager.getConfig();if(!n)return null;try{var i=this.getVariation(e,t,r);if(null===i)return this.notActivatingExperiment(e,t);if("Running"!==J(n,e))return this.logger.log(d.DEBUG,g.SHOULD_NOT_DISPATCH_ACTIVATE,"OPTIMIZELY",e),i;var o=Z(n,e),s={experiment:o,variation:o.variationKeyMap[i],decisionSource:v.EXPERIMENT};return this.sendImpressionEvent(s,"",t,!0,r),i}catch(r){return this.logger.log(d.ERROR,r.message),this.logger.log(d.INFO,g.NOT_ACTIVATING_USER,"OPTIMIZELY",t,e),this.errorHandler.handleError(r),null}}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.sendImpressionEvent=function(e,t,r,n,i){var o=this.projectConfigManager.getConfig();if(o){var s,a,u,l,c,E,f,d,p,g,I,h,_,v,R,O=(a=(s={decisionObj:e,flagKey:t,enabled:n,userId:r,userAttributes:i,clientEngine:this.clientEngine,clientVersion:this.clientVersion,configObj:o}).configObj,u=s.decisionObj,l=s.userId,c=s.flagKey,E=s.enabled,f=s.userAttributes,d=s.clientEngine,p=s.clientVersion,g=u.decisionSource,I=eB(u),h=ew(u),_=eK(u),v=ej(u),R=null!==h?z(a,h):null,{type:"impression",timestamp:j.currentTimestamp(),uuid:j.uuid(),user:{id:l,attributes:eY(a,f)},context:{accountId:a.accountId,projectId:a.projectId,revision:a.revision,clientName:d,clientVersion:p,anonymizeIP:a.anonymizeIP||!1,botFiltering:a.botFiltering},layer:{id:R},experiment:{id:h,key:I},variation:{id:v,key:_},ruleKey:I,flagKey:c,ruleType:g,enabled:E});this.eventProcessor.process(O),this.emitNotificationCenterActivate(e,t,r,n,i)}},e.prototype.emitNotificationCenterActivate=function(e,t,r,n,i){var o=this.projectConfigManager.getConfig();if(o){var s,a=e.decisionSource,u=eB(e),l=ew(e),c=eK(e),E=ej(e);null!==l&&""!==c&&(s=o.experimentIdMap[l]);var f,d=ek({attributes:i,clientEngine:this.clientEngine,clientVersion:this.clientVersion,configObj:o,experimentId:l,ruleKey:u,flagKey:t,ruleType:a,userId:r,enabled:n,variationId:E,logger:this.logger});s&&s.variationKeyMap&&""!==c&&(f=s.variationKeyMap[c]),this.notificationCenter.sendNotifications(h.ACTIVATE,{experiment:s,userId:r,attributes:i,variation:f,logEvent:d})}},e.prototype.track=function(e,t,r,n){try{if(!this.isValidInstance())return void this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","track");if(!this.validateInputs({user_id:t,event_key:e},r,n))return;var i,o,s,a,u,l,c,E,f,p,I,h=this.projectConfigManager.getConfig();if(!h)return;if(!h.eventKeyMap.hasOwnProperty(e))return this.logger.log(d.WARNING,g.EVENT_KEY_NOT_FOUND,"OPTIMIZELY",e),void this.logger.log(d.WARNING,g.NOT_TRACKING_USER,"OPTIMIZELY",t);var _=(o=(i={eventKey:e,eventTags:n=this.filterEmptyValues(n),userId:t,userAttributes:r,clientEngine:this.clientEngine,clientVersion:this.clientVersion,configObj:h}).configObj,s=i.userId,a=i.userAttributes,u=i.clientEngine,l=i.clientVersion,c=i.eventKey,E=i.eventTags,f=q(o,c),p=E?eV(E,eH):null,I=E?eb(E,eH):null,{type:"conversion",timestamp:j.currentTimestamp(),uuid:j.uuid(),user:{id:s,attributes:eY(o,a)},context:{accountId:o.accountId,projectId:o.projectId,revision:o.revision,clientName:u,clientVersion:l,anonymizeIP:o.anonymizeIP||!1,botFiltering:o.botFiltering},event:{id:f,key:c},revenue:p,value:I,tags:E});this.logger.log(d.INFO,g.TRACK_EVENT,"OPTIMIZELY",e,t),this.eventProcessor.process(_),this.emitNotificationCenterTrack(e,t,r,n)}catch(e){this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),this.logger.log(d.ERROR,g.NOT_TRACKING_USER,"OPTIMIZELY",t)}},e.prototype.emitNotificationCenterTrack=function(e,t,r,n){try{var i=this.projectConfigManager.getConfig();if(!i)return;var o=ex({attributes:r,clientEngine:this.clientEngine,clientVersion:this.clientVersion,configObj:i,eventKey:e,eventTags:n,logger:this.logger,userId:t});this.notificationCenter.sendNotifications(h.TRACK,{eventKey:e,userId:t,attributes:r,eventTags:n,logEvent:o})}catch(e){this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e)}},e.prototype.getVariation=function(e,t,r){try{if(!this.isValidInstance())return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getVariation"),null;try{if(!this.validateInputs({experiment_key:e,user_id:t},r))return null;var n,i=this.projectConfigManager.getConfig();if(!i)return null;var o=i.experimentKeyMap[e];if(!o)return this.logger.log(d.DEBUG,p.INVALID_EXPERIMENT_KEY,"OPTIMIZELY",e),null;var s=this.decisionService.getVariation(i,o,this.createUserContext(t,r)).result,a=(n=o.id,i.experimentFeatureMap.hasOwnProperty(n)?_.FEATURE_TEST:_.AB_TEST);return this.notificationCenter.sendNotifications(h.DECISION,{type:a,userId:t,attributes:r||{},decisionInfo:{experimentKey:e,variationKey:s}}),s}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.setForcedVariation=function(e,t,r){if(!this.validateInputs({experiment_key:e,user_id:t}))return!1;var n=this.projectConfigManager.getConfig();if(!n)return!1;try{return this.decisionService.setForcedVariation(n,e,t,r)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),!1}},e.prototype.getForcedVariation=function(e,t){if(!this.validateInputs({experiment_key:e,user_id:t}))return null;var r=this.projectConfigManager.getConfig();if(!r)return null;try{return this.decisionService.getForcedVariation(r,e,t).result}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.validateInputs=function(e,t,r){try{if(e.hasOwnProperty("user_id")){var n=e.user_id;if("string"!=typeof n||null===n||"undefined"===n)throw Error((0,a.sprintf)(p.INVALID_INPUT_FORMAT,"OPTIMIZELY","user_id"));delete e.user_id}return Object.keys(e).forEach(function(t){if(!eS(e[t]))throw Error((0,a.sprintf)(p.INVALID_INPUT_FORMAT,"OPTIMIZELY",t))}),t&&function(e){if("object"!=typeof e||Array.isArray(e)||null===e)throw Error((0,a.sprintf)(p.INVALID_ATTRIBUTES,"ATTRIBUTES_VALIDATOR"));Object.keys(e).forEach(function(t){if(void 0===e[t])throw Error((0,a.sprintf)(p.UNDEFINED_ATTRIBUTE,"ATTRIBUTES_VALIDATOR",t))})}(t),r&&function(e){if("object"!=typeof e||Array.isArray(e)||null===e)throw Error((0,a.sprintf)(p.INVALID_EVENT_TAGS,"EVENT_TAGS_VALIDATOR"))}(r),!0}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),!1}},e.prototype.notActivatingExperiment=function(e,t){return this.logger.log(d.INFO,g.NOT_ACTIVATING_USER,"OPTIMIZELY",t,e),null},e.prototype.filterEmptyValues=function(e){for(var t in e)e.hasOwnProperty(t)&&(null===e[t]||void 0===e[t])&&delete e[t];return e},e.prototype.isFeatureEnabled=function(e,t,r){try{if(!this.isValidInstance())return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","isFeatureEnabled"),!1;if(!this.validateInputs({feature_key:e,user_id:t},r))return!1;var n=this.projectConfigManager.getConfig();if(!n)return!1;var i=et(n,e,this.logger);if(!i)return!1;var o={},s=this.createUserContext(t,r),a=this.decisionService.getVariationForFeature(n,i,s).result,u=a.decisionSource,l=eB(a),c=eK(a),E=eG(a);u===v.FEATURE_TEST&&(o={experimentKey:l,variationKey:c}),(u===v.FEATURE_TEST||u===v.ROLLOUT&&ei(n))&&this.sendImpressionEvent(a,i.key,t,E,r),!0===E?this.logger.log(d.INFO,g.FEATURE_ENABLED_FOR_USER,"OPTIMIZELY",e,t):(this.logger.log(d.INFO,g.FEATURE_NOT_ENABLED_FOR_USER,"OPTIMIZELY",e,t),E=!1);var f={featureKey:e,featureEnabled:E,source:a.decisionSource,sourceInfo:o};return this.notificationCenter.sendNotifications(h.DECISION,{type:_.FEATURE,userId:t,attributes:r||{},decisionInfo:f}),E}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),!1}},e.prototype.getEnabledFeatures=function(e,t){var r=this;try{var n=[];if(!this.isValidInstance())return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getEnabledFeatures"),n;if(!this.validateInputs({user_id:e}))return n;var i=this.projectConfigManager.getConfig();return i&&(0,a.objectValues)(i.featureKeyMap).forEach(function(i){r.isFeatureEnabled(i.key,e,t)&&n.push(i.key)}),n}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),[]}},e.prototype.getFeatureVariable=function(e,t,r,n){try{return this.isValidInstance()?this.getFeatureVariableForType(e,t,null,r,n):(this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getFeatureVariable"),null)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getFeatureVariableForType=function(e,t,r,n,i){if(!this.validateInputs({feature_key:e,variable_key:t,user_id:n},i))return null;var o,s,a=this.projectConfigManager.getConfig();if(!a)return null;var u=et(a,e,this.logger);if(!u)return null;var l=(o=this.logger,(s=a.featureKeyMap[e])?s.variableKeyMap[t]||(o.log(d.ERROR,p.VARIABLE_KEY_NOT_IN_DATAFILE,H,t,e),null):(o.log(d.ERROR,p.FEATURE_NOT_IN_DATAFILE,H,e),null));if(!l)return null;if(r&&l.type!==r)return this.logger.log(d.WARNING,g.VARIABLE_REQUESTED_WITH_WRONG_TYPE,"OPTIMIZELY",r,l.type),null;var c=this.createUserContext(n,i),E=this.decisionService.getVariationForFeature(a,u,c).result,f=eG(E),I=this.getFeatureVariableValueFromVariation(e,f,E.variation,l,n),R={};return E.decisionSource===v.FEATURE_TEST&&null!==E.experiment&&null!==E.variation&&(R={experimentKey:E.experiment.key,variationKey:E.variation.key}),this.notificationCenter.sendNotifications(h.DECISION,{type:_.FEATURE_VARIABLE,userId:n,attributes:i||{},decisionInfo:{featureKey:e,featureEnabled:f,source:E.decisionSource,variableKey:t,variableValue:I,variableType:l.type,sourceInfo:R}}),I},e.prototype.getFeatureVariableValueFromVariation=function(e,t,r,n,i){var o=this.projectConfigManager.getConfig();if(!o)return null;var s=n.defaultValue;if(null!==r){var a=function(e,t,r,n){if(!t||!r)return null;if(!e.variationVariableUsageMap.hasOwnProperty(r.id))return n.log(d.ERROR,p.VARIATION_ID_NOT_IN_DATAFILE_NO_EXPERIMENT,H,r.id),null;var i=e.variationVariableUsageMap[r.id][t.id];return i?i.value:null}(o,n,r,this.logger);null!==a?t?(s=a,this.logger.log(d.INFO,g.USER_RECEIVED_VARIABLE_VALUE,"OPTIMIZELY",s,n.key,e)):this.logger.log(d.INFO,g.FEATURE_NOT_ENABLED_RETURN_DEFAULT_VARIABLE_VALUE,"OPTIMIZELY",e,i,s):this.logger.log(d.INFO,g.VARIABLE_NOT_USED_RETURN_DEFAULT_VARIABLE_VALUE,"OPTIMIZELY",n.key,r.key)}else this.logger.log(d.INFO,g.USER_RECEIVED_DEFAULT_VARIABLE_VALUE,"OPTIMIZELY",i,n.key,e);return function(e,t,r){var n;switch(t){case O.BOOLEAN:"true"!==e&&"false"!==e?(r.log(d.ERROR,p.UNABLE_TO_CAST_VALUE,H,e,t),n=null):n="true"===e;break;case O.INTEGER:isNaN(n=parseInt(e,10))&&(r.log(d.ERROR,p.UNABLE_TO_CAST_VALUE,H,e,t),n=null);break;case O.DOUBLE:isNaN(n=parseFloat(e))&&(r.log(d.ERROR,p.UNABLE_TO_CAST_VALUE,H,e,t),n=null);break;case O.JSON:try{n=JSON.parse(e)}catch(i){r.log(d.ERROR,p.UNABLE_TO_CAST_VALUE,H,e,t),n=null}break;default:n=e}return n}(s,n.type,this.logger)},e.prototype.getFeatureVariableBoolean=function(e,t,r,n){try{return this.isValidInstance()?this.getFeatureVariableForType(e,t,O.BOOLEAN,r,n):(this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getFeatureVariableBoolean"),null)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getFeatureVariableDouble=function(e,t,r,n){try{return this.isValidInstance()?this.getFeatureVariableForType(e,t,O.DOUBLE,r,n):(this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getFeatureVariableDouble"),null)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getFeatureVariableInteger=function(e,t,r,n){try{return this.isValidInstance()?this.getFeatureVariableForType(e,t,O.INTEGER,r,n):(this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getFeatureVariableInteger"),null)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getFeatureVariableString=function(e,t,r,n){try{return this.isValidInstance()?this.getFeatureVariableForType(e,t,O.STRING,r,n):(this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getFeatureVariableString"),null)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getFeatureVariableJSON=function(e,t,r,n){try{return this.isValidInstance()?this.getFeatureVariableForType(e,t,O.JSON,r,n):(this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getFeatureVariableJSON"),null)}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getAllFeatureVariables=function(e,t,r){var n=this;try{if(!this.isValidInstance())return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","getAllFeatureVariables"),null;if(!this.validateInputs({feature_key:e,user_id:t},r))return null;var i=this.projectConfigManager.getConfig();if(!i)return null;var o=et(i,e,this.logger);if(!o)return null;var s=this.createUserContext(t,r),a=this.decisionService.getVariationForFeature(i,o,s).result,u=eG(a),l={};o.variables.forEach(function(r){l[r.key]=n.getFeatureVariableValueFromVariation(e,u,a.variation,r,t)});var c={};return a.decisionSource===v.FEATURE_TEST&&null!==a.experiment&&null!==a.variation&&(c={experimentKey:a.experiment.key,variationKey:a.variation.key}),this.notificationCenter.sendNotifications(h.DECISION,{type:_.ALL_FEATURE_VARIABLES,userId:t,attributes:r||{},decisionInfo:{featureKey:e,featureEnabled:u,source:a.decisionSource,variableValues:l,sourceInfo:c}}),l}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.getOptimizelyConfig=function(){try{return this.projectConfigManager.getConfig()?this.projectConfigManager.getOptimizelyConfig():null}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),null}},e.prototype.close=function(){var e=this;try{var t=this.eventProcessor.stop();return this.disposeOnUpdate&&(this.disposeOnUpdate(),this.disposeOnUpdate=null),this.projectConfigManager&&this.projectConfigManager.stop(),Object.keys(this.readyTimeouts).forEach(function(t){var r=e.readyTimeouts[t];clearTimeout(r.readyTimeout),r.onClose()}),this.readyTimeouts={},t.then(function(){return{success:!0}},function(e){return{success:!1,reason:String(e)}})}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),Promise.resolve({success:!1,reason:String(e)})}},e.prototype.onReady=function(e){var t,r,n=this;"object"==typeof e&&null!==e&&void 0!==e.timeout&&(t=e.timeout),j.isSafeInteger(t)||(t=3e4);var i=new Promise(function(e){r=e}),o=this.nextReadyTimeoutId;this.nextReadyTimeoutId++;var s=setTimeout(function(){delete n.readyTimeouts[o],r({success:!1,reason:(0,a.sprintf)("onReady timeout expired after %s ms",t)})},t);return this.readyTimeouts[o]={readyTimeout:s,onClose:function(){r({success:!1,reason:"Instance closed"})}},this.readyPromise.then(function(){clearTimeout(s),delete n.readyTimeouts[o],r({success:!0})}),Promise.race([this.readyPromise,i])},e.prototype.createUserContext=function(e,t){return this.validateInputs({user_id:e},t)?new x({optimizely:this,userId:e,attributes:t}):null},e.prototype.decide=function(e,t,r){var n,i,o,s,u=this;void 0===r&&(r=[]);var l,c=e.getUserId(),E=e.getAttributes(),I=this.projectConfigManager.getConfig(),R=[];if(!this.isValidInstance()||!I)return this.logger.log(d.INFO,g.INVALID_OBJECT,"OPTIMIZELY","decide"),k(t,e,[N.SDK_NOT_READY]);var O=I.featureKeyMap[t];if(!O)return this.logger.log(d.ERROR,p.FEATURE_NOT_IN_DATAFILE,"OPTIMIZELY",t),k(t,e,[(0,a.sprintf)(N.FLAG_KEY_INVALID,t)]);var y=this.getAllDecideOptions(r),T=this.decisionService.findValidatedForcedDecision(I,e,t);R.push.apply(R,T.reasons);var A=T.result;if(A)l={experiment:null,variation:A,decisionSource:v.FEATURE_TEST};else{var m=this.decisionService.getVariationForFeature(I,O,e,y);R.push.apply(R,m.reasons),l=m.result}var L=l.decisionSource,U=null!==(i=null===(n=l.experiment)||void 0===n?void 0:n.key)&&void 0!==i?i:null,S=null!==(s=null===(o=l.variation)||void 0===o?void 0:o.key)&&void 0!==s?s:null,D=eG(l);!0===D?this.logger.log(d.INFO,g.FEATURE_ENABLED_FOR_USER,"OPTIMIZELY",t,c):this.logger.log(d.INFO,g.FEATURE_NOT_ENABLED_FOR_USER,"OPTIMIZELY",t,c);var C={},V=!1;y[F.EXCLUDE_VARIABLES]||O.variables.forEach(function(e){C[e.key]=u.getFeatureVariableValueFromVariation(t,D,l.variation,e,c)}),!y[F.DISABLE_DECISION_EVENT]&&(L===v.FEATURE_TEST||L===v.ROLLOUT&&ei(I))&&(this.sendImpressionEvent(l,t,c,D,E),V=!0);var b=[];y[F.INCLUDE_REASONS]&&(b=R.map(function(e){return a.sprintf.apply(void 0,f([e[0]],e.slice(1)))}));var P={flagKey:t,enabled:D,variationKey:S,ruleKey:U,variables:C,reasons:b,decisionEventDispatched:V};return this.notificationCenter.sendNotifications(h.DECISION,{type:_.FLAG,userId:c,attributes:E,decisionInfo:P}),{variationKey:S,enabled:D,variables:C,ruleKey:U,flagKey:t,userContext:e,reasons:b}},e.prototype.getAllDecideOptions=function(e){var t=this,r=E({},this.defaultDecideOptions);return Array.isArray(e)?e.forEach(function(e){F[e]?r[e]=!0:t.logger.log(d.WARNING,g.UNRECOGNIZED_DECIDE_OPTION,"OPTIMIZELY",e)}):this.logger.log(d.DEBUG,g.INVALID_DECIDE_OPTIONS,"OPTIMIZELY"),r},e.prototype.decideForKeys=function(e,t,r){var n=this;void 0===r&&(r=[]);var i={};if(!this.isValidInstance())return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","decideForKeys"),i;if(0===t.length)return i;var o=this.getAllDecideOptions(r);return t.forEach(function(t){var s=n.decide(e,t,r);o[F.ENABLED_FLAGS_ONLY]&&!s.enabled||(i[t]=s)}),i},e.prototype.decideAll=function(e,t){void 0===t&&(t=[]);var r=this.projectConfigManager.getConfig();if(!this.isValidInstance()||!r)return this.logger.log(d.ERROR,g.INVALID_OBJECT,"OPTIMIZELY","decideAll"),{};var n=Object.keys(r.featureKeyMap);return this.decideForKeys(e,n,t)},e}(),eq=function(e){return!("number"!=typeof e||!j.isSafeInteger(e))&&e>=1},eJ=function(e){return!("number"!=typeof e||!j.isSafeInteger(e))&&e>0},eW=function(){function e(e){var t=this;this.logger=e.logger,this.errorHandler=e.errorHandler,this.notificationListeners={},(0,a.objectValues)(h).forEach(function(e){t.notificationListeners[e]=[]}),this.listenerId=1}return e.prototype.addNotificationListener=function(e,t){try{if(!((0,a.objectValues)(h).indexOf(e)>-1))return -1;this.notificationListeners[e]||(this.notificationListeners[e]=[]);var r=!1;if((this.notificationListeners[e]||[]).forEach(function(e){e.callback!==t||(r=!0)}),r)return -1;this.notificationListeners[e].push({id:this.listenerId,callback:t});var n=this.listenerId;return this.listenerId+=1,n}catch(e){return this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e),-1}},e.prototype.removeNotificationListener=function(e){var t,r,n=this;try{if(Object.keys(this.notificationListeners).some(function(i){return(n.notificationListeners[i]||[]).every(function(n,o){return n.id!==e||(t=o,r=i,!1)}),void 0!==t&&void 0!==r}),void 0!==t&&void 0!==r)return this.notificationListeners[r].splice(t,1),!0}catch(e){this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e)}return!1},e.prototype.clearAllNotificationListeners=function(){var e=this;try{(0,a.objectValues)(h).forEach(function(t){e.notificationListeners[t]=[]})}catch(e){this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e)}},e.prototype.clearNotificationListeners=function(e){try{this.notificationListeners[e]=[]}catch(e){this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e)}},e.prototype.sendNotifications=function(e,t){var r=this;try{(this.notificationListeners[e]||[]).forEach(function(n){var i=n.callback;try{i(t)}catch(t){r.logger.log(d.ERROR,g.NOTIFICATION_LISTENER_EXCEPTION,"NOTIFICATION_CENTER",e,t.message)}})}catch(e){this.logger.log(d.ERROR,e.message),this.errorHandler.handleError(e)}},e}(),eZ={createEventProcessor:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return new(o.LogTierV1EventProcessor.bind.apply(o.LogTierV1EventProcessor,f([void 0],e)))},LocalStoragePendingEventsDispatcher:o.LocalStoragePendingEventsDispatcher};function eQ(e,t,r,n){var i={sdkKey:e};if((void 0===n||"object"==typeof n&&null!==n)&&j.assign(i,n),r){var o=en({datafile:r,jsonSchemaValidator:void 0,logger:t}),s=o.configObj,a=o.error;a&&t.error(a),s&&(i.datafile=er(s))}return new c.z(i)}var e$=(0,n.getLogger)();(0,n.setLogHandler)(b()),(0,n.setLogLevel)(n.LogLevel.INFO);var e0=!1,e1=function(e){try{e.errorHandler&&(0,n.setErrorHandler)(e.errorHandler),e.logger&&((0,n.setLogHandler)(e.logger),(0,n.setLogLevel)(n.LogLevel.NOTSET)),void 0!==e.logLevel&&(0,n.setLogLevel)(e.logLevel);try{L(e),e.isValidInstance=!0}catch(t){e$.error(t),e.isValidInstance=!1}var t=void 0;null==e.eventDispatcher?(t=new o.LocalStoragePendingEventsDispatcher({eventDispatcher:C}),e0||(t.sendPendingEvents(),e0=!0)):t=e.eventDispatcher;var r=e.eventBatchSize,i=e.eventFlushInterval;eq(e.eventBatchSize)||(e$.warn("Invalid eventBatchSize %s, defaulting to %s",e.eventBatchSize,10),r=10),eJ(e.eventFlushInterval)||(e$.warn("Invalid eventFlushInterval %s, defaulting to %s",e.eventFlushInterval,1e3),i=1e3);var s=(0,n.getErrorHandler)(),a=new eW({logger:e$,errorHandler:s}),u={dispatcher:t,flushInterval:i,batchSize:r,maxQueueSize:e.eventMaxQueueSize||1e4,notificationCenter:a},l=E(E({clientEngine:"javascript-sdk"},e),{eventProcessor:eZ.createEventProcessor(u),logger:e$,errorHandler:s,datafileManager:e.sdkKey?eQ(e.sdkKey,e$,e.datafile,e.datafileOptions):void 0,notificationCenter:a}),c=new eX(l);try{if("function"==typeof window.addEventListener){var f="onpagehide"in window?"pagehide":"unload";window.addEventListener(f,function(){c.close()},!1)}}catch(e){e$.error(g.UNABLE_TO_ATTACH_UNLOAD,"INDEX_BROWSER",e.message)}return c}catch(e){return e$.error(e),null}},e5=function(){e0=!1},e3={logging:M,errorHandler:S,eventDispatcher:C,enums:T,setLogger:n.setLogHandler,setLogLevel:n.setLogLevel,createInstance:e1,__internalResetRetryState:e5,OptimizelyDecideOption:F},e2=null},59753:(e,t,r)=>{"use strict";function n(){if(!(this instanceof n))return new n;this.size=0,this.uid=0,this.selectors=[],this.selectorObjects={},this.indexes=Object.create(this.indexes),this.activeIndexes=[]}r.d(t,{f:()=>D,S:()=>S,on:()=>U});var i,o=window.document.documentElement,s=o.matches||o.webkitMatchesSelector||o.mozMatchesSelector||o.oMatchesSelector||o.msMatchesSelector;n.prototype.matchesSelector=function(e,t){return s.call(e,t)},n.prototype.querySelectorAll=function(e,t){return t.querySelectorAll(e)},n.prototype.indexes=[];var a=/^#((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"ID",selector:function(e){var t;if(t=e.match(a))return t[0].slice(1)},element:function(e){if(e.id)return[e.id]}});var u=/^\.((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"CLASS",selector:function(e){var t;if(t=e.match(u))return t[0].slice(1)},element:function(e){var t=e.className;if(t){if("string"==typeof t)return t.split(/\s/);if("object"==typeof t&&"baseVal"in t)return t.baseVal.split(/\s/)}}});var l=/^((?:[\w\u00c0-\uFFFF\-]|\\.)+)/g;n.prototype.indexes.push({name:"TAG",selector:function(e){var t;if(t=e.match(l))return t[0].toUpperCase()},element:function(e){return[e.nodeName.toUpperCase()]}}),n.prototype.indexes.default={name:"UNIVERSAL",selector:function(){return!0},element:function(){return[!0]}},i="function"==typeof window.Map?window.Map:function(){function e(){this.map={}}return e.prototype.get=function(e){return this.map[e+" "]},e.prototype.set=function(e,t){this.map[e+" "]=t},e}();var c=/((?:\((?:\([^()]+\)|[^()]+)+\)|\[(?:\[[^\[\]]*\]|['"][^'"]*['"]|[^\[\]'"]+)+\]|\\.|[^ >+~,(\[\\]+)+|[>+~])(\s*,\s*)?((?:.|\r|\n)*)/g;function E(e,t){var r,n,i,o,s,a,u=(e=e.slice(0).concat(e.default)).length,l=t,E=[];do if(c.exec(""),(i=c.exec(l))&&(l=i[3],i[2]||!l)){for(r=0;r<u;r++)if(s=(a=e[r]).selector(i[1])){for(n=E.length,o=!1;n--;)if(E[n].index===a&&E[n].key===s){o=!0;break}o||E.push({index:a,key:s});break}}while(i)return E}function f(e,t){var r,n,i;for(r=0,n=e.length;r<n;r++)if(i=e[r],t.isPrototypeOf(i))return i}function d(e,t){return e.id-t.id}n.prototype.logDefaultIndexUsed=function(){},n.prototype.add=function(e,t){var r,n,o,s,a,u,l,c,d=this.activeIndexes,p=this.selectors,g=this.selectorObjects;if("string"==typeof e){for(n=0,g[(r={id:this.uid++,selector:e,data:t}).id]=r,l=E(this.indexes,e);n<l.length;n++)s=(c=l[n]).key,(a=f(d,o=c.index))||((a=Object.create(o)).map=new i,d.push(a)),o===this.indexes.default&&this.logDefaultIndexUsed(r),(u=a.map.get(s))||(u=[],a.map.set(s,u)),u.push(r);this.size++,p.push(e)}},n.prototype.remove=function(e,t){if("string"==typeof e){var r,n,i,o,s,a,u,l,c=this.activeIndexes,f=this.selectors=[],d=this.selectorObjects,p={},g=1==arguments.length;for(i=0,r=E(this.indexes,e);i<r.length;i++)for(n=r[i],o=c.length;o--;)if(a=c[o],n.index.isPrototypeOf(a)){if(u=a.map.get(n.key))for(s=u.length;s--;)(l=u[s]).selector===e&&(g||l.data===t)&&(u.splice(s,1),p[l.id]=!0);break}for(i in p)delete d[i],this.size--;for(i in d)f.push(d[i].selector)}},n.prototype.queryAll=function(e){if(!this.selectors.length)return[];var t,r,n,i,o,s,a,u,l={},c=[],E=this.querySelectorAll(this.selectors.join(", "),e);for(t=0,n=E.length;t<n;t++)for(r=0,o=E[t],i=(s=this.matches(o)).length;r<i;r++)l[(u=s[r]).id]?a=l[u.id]:(a={id:u.id,selector:u.selector,data:u.data,elements:[]},l[u.id]=a,c.push(a)),a.elements.push(o);return c.sort(d)},n.prototype.matches=function(e){if(!e)return[];var t,r,n,i,o,s,a,u,l,c,E,f=this.activeIndexes,p={},g=[];for(t=0,i=f.length;t<i;t++)if(u=(a=f[t]).element(e)){for(r=0,o=u.length;r<o;r++)if(l=a.map.get(u[r]))for(n=0,s=l.length;n<s;n++)!p[E=(c=l[n]).id]&&this.matchesSelector(e,c.selector)&&(p[E]=!0,g.push(c))}return g.sort(d)};var p={},g={},I=new WeakMap,h=new WeakMap,_=new WeakMap,v=Object.getOwnPropertyDescriptor(Event.prototype,"currentTarget");function R(e,t,r){var n=e[t];return e[t]=function(){return r.apply(e,arguments),n.apply(e,arguments)},e}function O(e,t,r){var n=[],i=t;do{if(1!==i.nodeType)break;var o=e.matches(i);if(o.length){var s={node:i,observers:o};r?n.unshift(s):n.push(s)}}while(i=i.parentElement)return n}function y(){I.set(this,!0)}function N(){I.set(this,!0),h.set(this,!0)}function T(){return _.get(this)||null}function A(e,t){v&&Object.defineProperty(e,"currentTarget",{configurable:!0,enumerable:!0,get:t||v.get})}function m(e){try{return e.eventPhase,!0}catch(e){return!1}}function L(e){if(m(e)){var t=(1===e.eventPhase?g:p)[e.type];if(t){var r=O(t,e.target,1===e.eventPhase);if(r.length){R(e,"stopPropagation",y),R(e,"stopImmediatePropagation",N),A(e,T);for(var n=0,i=r.length;n<i&&!I.get(e);n++){var o=r[n];_.set(e,o.node);for(var s=0,a=o.observers.length;s<a&&!h.get(e);s++)o.observers[s].data.call(o.node,e)}_.delete(e),A(e)}}}}function U(e,t,r){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=!!i.capture,s=o?g:p,a=s[e];a||(a=new n,s[e]=a,document.addEventListener(e,L,o)),a.add(t,r)}function S(e,t,r){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},i=!!n.capture,o=i?g:p,s=o[e];s&&(s.remove(t,r),s.size||(delete o[e],document.removeEventListener(e,L,i)))}function D(e,t,r){return e.dispatchEvent(new CustomEvent(t,{bubbles:!0,cancelable:!0,detail:r}))}},58053:e=>{!function(){function t(e,t){var r,n,i,o,s,a;for(r=3&e.length,n=e.length-r,i=t,a=0;a<n;)s=255&e.charCodeAt(a)|(255&e.charCodeAt(++a))<<8|(255&e.charCodeAt(++a))<<16|(255&e.charCodeAt(++a))<<24,++a,i^=s=(65535&(s=(s=(65535&s)*3432918353+(((s>>>16)*3432918353&65535)<<16)&4294967295)<<15|s>>>17))*461845907+(((s>>>16)*461845907&65535)<<16)&4294967295,i=(65535&(o=(65535&(i=i<<13|i>>>19))*5+(((i>>>16)*5&65535)<<16)&4294967295))+27492+(((o>>>16)+58964&65535)<<16);switch(s=0,r){case 3:s^=(255&e.charCodeAt(a+2))<<16;case 2:s^=(255&e.charCodeAt(a+1))<<8;case 1:s^=255&e.charCodeAt(a),i^=s=(65535&(s=(s=(65535&s)*3432918353+(((s>>>16)*3432918353&65535)<<16)&4294967295)<<15|s>>>17))*461845907+(((s>>>16)*461845907&65535)<<16)&4294967295}return i^=e.length,i^=i>>>16,i=(65535&i)*2246822507+(((i>>>16)*2246822507&65535)<<16)&4294967295,i^=i>>>13,i=(65535&i)*3266489909+(((i>>>16)*3266489909&65535)<<16)&4294967295,(i^=i>>>16)>>>0}var r=t;r.v2=function(e,t){for(var r,n=e.length,i=t^n,o=0;n>=4;)r=(65535&(r=255&e.charCodeAt(o)|(255&e.charCodeAt(++o))<<8|(255&e.charCodeAt(++o))<<16|(255&e.charCodeAt(++o))<<24))*1540483477+(((r>>>16)*1540483477&65535)<<16),r^=r>>>24,i=(65535&i)*1540483477+(((i>>>16)*1540483477&65535)<<16)^(r=(65535&r)*1540483477+(((r>>>16)*1540483477&65535)<<16)),n-=4,++o;switch(n){case 3:i^=(255&e.charCodeAt(o+2))<<16;case 2:i^=(255&e.charCodeAt(o+1))<<8;case 1:i^=255&e.charCodeAt(o),i=(65535&i)*1540483477+(((i>>>16)*1540483477&65535)<<16)}return i^=i>>>13,i=(65535&i)*1540483477+(((i>>>16)*1540483477&65535)<<16),(i^=i>>>15)>>>0},r.v3=t,e.exports=r}()}}]);
//# sourceMappingURL=vendors-node_modules_optimizely_optimizely-sdk_dist_optimizely_browser_es_min_js-node_modules-089adc-0646a402d4dd.js.map