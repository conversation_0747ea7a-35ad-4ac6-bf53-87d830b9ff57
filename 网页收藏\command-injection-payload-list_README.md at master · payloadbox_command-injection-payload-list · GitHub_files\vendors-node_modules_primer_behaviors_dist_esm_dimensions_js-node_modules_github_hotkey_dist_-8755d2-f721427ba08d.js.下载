"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_github_hotkey_dist_-8755d2"],{98105:(e,t,n)=>{function i(e){let t=e.getBoundingClientRect();return{top:t.top+window.pageYOffset,left:t.left+window.pageXOffset}}function r(e){let t=e,n=t.ownerDocument;if(!n||!t.offsetParent)return;let i=n.defaultView.HTMLElement;if(t!==n.body){for(;t!==n.body;){if(!(t.parentElement instanceof i))return;t=t.parentElement;let{position:e,overflowY:n,overflowX:r}=getComputedStyle(t);if("fixed"===e||"auto"===n||"auto"===r||"scroll"===n||"scroll"===r)break}return t instanceof Document?null:t}}function l(e,t){let n=t,i=e.ownerDocument;if(!i)return;let r=i.documentElement;if(!r||e===r)return;let l=s(e,n);if(!l)return;n=l._container;let a=n===i.documentElement&&i.defaultView?{top:i.defaultView.pageYOffset,left:i.defaultView.pageXOffset}:{top:n.scrollTop,left:n.scrollLeft},o=l.top-a.top,u=l.left-a.left,c=n.clientHeight,h=n.clientWidth,f=c-(o+e.offsetHeight),p=h-(u+e.offsetWidth);return{top:o,left:u,bottom:f,right:p,height:c,width:h}}function s(e,t){let n,i,r,l=e,s=l.ownerDocument;if(!s)return;let u=s.documentElement;if(!u)return;let c=s.defaultView.HTMLElement,h=0,f=0,p=l.offsetHeight,d=l.offsetWidth;for(;!(l===s.body||l===t);){if(h+=l.offsetTop||0,f+=l.offsetLeft||0,!(l.offsetParent instanceof c))return;l=l.offsetParent}if(t&&t!==s&&t!==s.defaultView&&t!==s.documentElement&&t!==s.body){if(!(t instanceof c))return;r=t,n=t.scrollHeight,i=t.scrollWidth}else r=u,n=a(s.body,u),i=o(s.body,u);let m=n-(h+p),g=i-(f+d);return{top:h,left:f,bottom:m,right:g,_container:r}}function a(e,t){return Math.max(e.scrollHeight,t.scrollHeight,e.offsetHeight,t.offsetHeight,t.clientHeight)}function o(e,t){return Math.max(e.scrollWidth,t.scrollWidth,e.offsetWidth,t.offsetWidth,t.clientWidth)}n.d(t,{VZ:()=>r,_C:()=>l,cv:()=>i,oE:()=>s})},11793:(e,t,n)=>{n.d(t,{EL:()=>i,N9:()=>T,Tz:()=>y});let Leaf=class Leaf{constructor(e){this.children=[],this.parent=e}delete(e){let t=this.children.indexOf(e);return -1!==t&&(this.children=this.children.slice(0,t).concat(this.children.slice(t+1)),0===this.children.length&&this.parent.delete(this),!0)}add(e){return this.children.push(e),this}};let RadixTrie=class RadixTrie{constructor(e){this.parent=null,this.children={},this.parent=e||null}get(e){return this.children[e]}insert(e){let t=this;for(let n=0;n<e.length;n+=1){let i=e[n],r=t.get(i);if(n===e.length-1)return r instanceof RadixTrie&&(t.delete(r),r=null),r||(r=new Leaf(t),t.children[i]=r),r;r instanceof Leaf&&(r=null),r||(r=new RadixTrie(t),t.children[i]=r),t=r}return t}delete(e){for(let t in this.children){let n=this.children[t];if(n===e){let e=delete this.children[t];return 0===Object.keys(this.children).length&&this.parent&&this.parent.delete(this),e}}return!1}};function i(e){let{ctrlKey:t,altKey:n,metaKey:i,key:s}=e,a=[],o=[t,n,i,l(e)];for(let[e,t]of o.entries())t&&a.push(r[e]);return r.includes(s)||a.push(s),a.join("+")}let r=["Control","Alt","Meta","Shift"];function l(e){let{shiftKey:t,code:n,key:i}=e;return t&&!(n.startsWith("Key")&&i.toUpperCase()===i)}function s(e,t){return u(o(e,t))}let a=/Mac|iPod|iPhone|iPad/i;function o(e,t=navigator.platform){let n=a.test(t)?"Meta":"Control";return e.replace("Mod",n)}function u(e){let t=e.split("+").pop(),n=[];for(let t of["Control","Alt","Meta","Shift"])e.includes(t)&&n.push(t);return n.push(t),n.join("+")}let SequenceTracker=class SequenceTracker{constructor({onReset:e}={}){this._path=[],this.timer=null,this.onReset=e}get path(){return this._path}get sequence(){return this._path.join(" ")}registerKeypress(e){this._path=[...this._path,i(e)],this.startTimer()}reset(){var e;this.killTimer(),this._path=[],null===(e=this.onReset)||void 0===e||e.call(this)}killTimer(){null!=this.timer&&window.clearTimeout(this.timer),this.timer=null}startTimer(){this.killTimer(),this.timer=window.setTimeout(()=>this.reset(),SequenceTracker.CHORD_TIMEOUT)}};function c(e){if(!(e instanceof HTMLElement))return!1;let t=e.nodeName.toLowerCase(),n=(e.getAttribute("type")||"").toLowerCase();return"select"===t||"textarea"===t||"input"===t&&"submit"!==n&&"reset"!==n&&"checkbox"!==n&&"radio"!==n&&"file"!==n||e.isContentEditable}function h(e,t){let n=new CustomEvent("hotkey-fire",{cancelable:!0,detail:{path:t}}),i=!e.dispatchEvent(n);i||(c(e)?e.focus():e.click())}function f(e){let t=[],n=[""],i=!1;for(let r=0;r<e.length;r++){if(i&&","===e[r]){t.push(n),n=[""],i=!1;continue}if(" "===e[r]){n.push(""),i=!1;continue}i="+"!==e[r],n[n.length-1]+=e[r]}return t.push(n),t.map(e=>e.map(e=>s(e)).filter(e=>""!==e)).filter(e=>e.length>0)}SequenceTracker.CHORD_TIMEOUT=1500;let p=new RadixTrie,d=new WeakMap,m=p,g=new SequenceTracker({onReset(){m=p}});function b(e){if(e.defaultPrevented||!(e.target instanceof Node))return;if(c(e.target)){let t=e.target;if(!t.id||!t.ownerDocument.querySelector(`[data-hotkey-scope="${t.id}"]`))return}let t=m.get(i(e));if(!t){g.reset();return}if(g.registerKeypress(e),m=t,t instanceof Leaf){let n;let i=e.target,r=!1,l=c(i);for(let e=t.children.length-1;e>=0;e-=1){n=t.children[e];let s=n.getAttribute("data-hotkey-scope");if(!l&&!s||l&&i.id===s){r=!0;break}}n&&r&&(h(n,g.path),e.preventDefault()),g.reset()}}function T(e,t){0===Object.keys(p.children).length&&document.addEventListener("keydown",b);let n=f(t||e.getAttribute("data-hotkey")||""),i=n.map(t=>p.insert(t).add(e));d.set(e,i)}function y(e){let t=d.get(e);if(t&&t.length)for(let n of t)n&&n.delete(e);0===Object.keys(p.children).length&&document.removeEventListener("keydown",b)}},20845:(e,t,n)=>{n.d(t,{js:()=>TemplateResult,dy:()=>W,sY:()=>T,Au:()=>O});let i=new Map;function r(e){if(i.has(e))return i.get(e);let t=e.length,n=0,r=0,l=0,s=[];for(let i=0;i<t;i+=1){let t=e[i],a=e[i+1],o=e[i-1];"{"===t&&"{"===a&&"\\"!==o?(1===(l+=1)&&(r=i),i+=1):"}"===t&&"}"===a&&"\\"!==o&&l&&0==(l-=1)&&(r>n&&(s.push(Object.freeze({type:"string",start:n,end:r,value:e.slice(n,r)})),n=r),s.push(Object.freeze({type:"part",start:r,end:i+2,value:e.slice(n+2,i).trim()})),i+=1,n=i+1)}return n<t&&s.push(Object.freeze({type:"string",start:n,end:t,value:e.slice(n,t)})),i.set(e,Object.freeze(s)),i.get(e)}let l=new WeakMap,s=new WeakMap;let AttributeTemplatePart=class AttributeTemplatePart{constructor(e,t){this.expression=t,l.set(this,e),e.updateParent("")}get attributeName(){return l.get(this).attr.name}get attributeNamespace(){return l.get(this).attr.namespaceURI}get value(){return s.get(this)}set value(e){s.set(this,e||""),l.get(this).updateParent(e)}get element(){return l.get(this).element}get booleanValue(){return l.get(this).booleanValue}set booleanValue(e){l.get(this).booleanValue=e}};let AttributeValueSetter=class AttributeValueSetter{constructor(e,t){this.element=e,this.attr=t,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(e){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=e?"":null}append(e){this.partList.push(e)}updateParent(e){if(1===this.partList.length&&null===e)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let e=this.partList.map(e=>"string"==typeof e?e:e.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,e)}}};let a=new WeakMap;let NodeTemplatePart=class NodeTemplatePart{constructor(e,t){this.expression=t,a.set(this,[e]),e.textContent=""}get value(){return a.get(this).map(e=>e.textContent).join("")}set value(e){this.replace(e)}get previousSibling(){return a.get(this)[0].previousSibling}get nextSibling(){return a.get(this)[a.get(this).length-1].nextSibling}replace(...e){let t=e.map(e=>"string"==typeof e?new Text(e):e);for(let e of(t.length||t.push(new Text("")),a.get(this)[0].before(...t),a.get(this)))e.remove();a.set(this,t)}};function o(e){return{processCallback(t,n,i){var r;if("object"==typeof i&&i){for(let t of n)if(t.expression in i){let n=null!==(r=i[t.expression])&&void 0!==r?r:"";e(t,n)}}}}}function u(e,t){e.value=String(t)}function c(e,t){return"boolean"==typeof t&&e instanceof AttributeTemplatePart&&"boolean"==typeof e.element[e.attributeName]&&(e.booleanValue=t,!0)}let h=o(u);function*f(e){let t;let n=e.ownerDocument.createTreeWalker(e,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null,!1);for(;t=n.nextNode();)if(t instanceof Element&&t.hasAttributes())for(let e=0;e<t.attributes.length;e+=1){let n=t.attributes.item(e);if(n&&n.value.includes("{{")){let e=new AttributeValueSetter(t,n);for(let t of r(n.value))if("string"===t.type)e.append(t.value);else{let n=new AttributeTemplatePart(e,t.value);e.append(n),yield n}}}else if(t instanceof Text&&t.textContent&&t.textContent.includes("{{")){let e=r(t.textContent);for(let n=0;n<e.length;n+=1){let i=e[n];i.end<t.textContent.length&&t.splitText(i.end),"part"===i.type&&(yield new NodeTemplatePart(t,i.value));break}}}o((e,t)=>{c(e,t)||u(e,t)});let p=new WeakMap,d=new WeakMap;let TemplateInstance=class TemplateInstance extends DocumentFragment{constructor(e,t,n=h){var i,r;super(),Object.getPrototypeOf(this)!==TemplateInstance.prototype&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(e.content.cloneNode(!0)),d.set(this,Array.from(f(this))),p.set(this,n),null===(r=(i=p.get(this)).createCallback)||void 0===r||r.call(i,this,d.get(this),t),p.get(this).processCallback(this,d.get(this),t)}update(e){p.get(this).processCallback(this,d.get(this),e)}};let m=new WeakMap,g=new WeakMap,b=new WeakMap;let TemplateResult=class TemplateResult{constructor(e,t,n){this.strings=e,this.values=t,this.processor=n}static setCSPTrustedTypesPolicy(e){TemplateResult.cspTrustedTypesPolicy=e}get template(){var e,t;if(m.has(this.strings))return m.get(this.strings);{let n=document.createElement("template"),i=this.strings.length-1,r=this.strings.reduce((e,t,n)=>e+t+(n<i?`{{ ${n} }}`:""),""),l=null!==(t=null===(e=TemplateResult.cspTrustedTypesPolicy)||void 0===e?void 0:e.createHTML(r))&&void 0!==t?t:r;return n.innerHTML=l,m.set(this.strings,n),n}}renderInto(e){let t=this.template;if(g.get(e)!==t){g.set(e,t);let n=new TemplateInstance(t,this.values,this.processor);b.set(e,n),e instanceof NodeTemplatePart?e.replace(...n.children):e.appendChild(n);return}b.get(e).update(this.values)}};function T(e,t){e.renderInto(t)}TemplateResult.cspTrustedTypesPolicy=null;let y=new WeakSet;function v(e){return y.has(e)}function w(e,t){return!!v(t)&&(t(e),!0)}function k(e){return(...t)=>{let n=e(...t);return y.add(n),n}}let E=new WeakMap;let EventHandler=class EventHandler{constructor(e,t){this.element=e,this.type=t,this.element.addEventListener(this.type,this),E.get(this.element).set(this.type,this)}set(e){"function"==typeof e?this.handleEvent=e.bind(this.element):"object"==typeof e&&"function"==typeof e.handleEvent?this.handleEvent=e.handleEvent.bind(e):(this.element.removeEventListener(this.type,this),E.get(this.element).delete(this.type))}static for(e){E.has(e.element)||E.set(e.element,new Map);let t=e.attributeName.slice(2),n=E.get(e.element);return n.has(t)?n.get(t):new EventHandler(e.element,t)}};function N(e,t){return!!(e instanceof AttributeTemplatePart&&e.attributeName.startsWith("on"))&&(EventHandler.for(e).set(t),e.element.removeAttributeNS(e.attributeNamespace,e.attributeName),!0)}function M(e){return"object"==typeof e&&Symbol.iterator in e}function P(e,t){if(!M(t))return!1;if(!(e instanceof NodeTemplatePart))return e.value=Array.from(t).join(" "),!0;{let n=[];for(let e of t)if(e instanceof TemplateResult){let t=document.createDocumentFragment();e.renderInto(t),n.push(...t.childNodes)}else e instanceof DocumentFragment?n.push(...e.childNodes):n.push(String(e));return n.length&&e.replace(...n),!0}}function x(e,t){return t instanceof DocumentFragment&&e instanceof NodeTemplatePart&&(t.childNodes.length&&e.replace(...t.childNodes),!0)}function C(e,t){return t instanceof TemplateResult&&e instanceof NodeTemplatePart&&(t.renderInto(e),!0)}function L(e,t){w(e,t)||c(e,t)||N(e,t)||C(e,t)||x(e,t)||P(e,t)||u(e,t)}let S=o(L);function W(e,...t){return new TemplateResult(e,t,S)}let _=new WeakMap;k((...e)=>t=>{_.has(t)||_.set(t,{i:e.length});let n=_.get(t);for(let i=0;i<e.length;i+=1)e[i]instanceof Promise?Promise.resolve(e[i]).then(e=>{i<n.i&&(n.i=i,L(t,e))}):i<=n.i&&(n.i=i,L(t,e[i]))});let O=k(e=>t=>{var n,i;if(!(t instanceof NodeTemplatePart))return;let r=document.createElement("template"),l=null!==(i=null===(n=TemplateResult.cspTrustedTypesPolicy)||void 0===n?void 0:n.createHTML(e))&&void 0!==i?i:e;r.innerHTML=l;let s=document.importNode(r.content,!0);t.replace(...s.childNodes)})}}]);
//# sourceMappingURL=vendors-node_modules_primer_behaviors_dist_esm_dimensions_js-node_modules_github_hotkey_dist_-8755d2-f5b35290ea60.js.map