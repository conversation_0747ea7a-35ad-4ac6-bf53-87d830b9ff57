"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_behaviors_dist_esm_focus-zone_js"],{22114:(e,t,n)=>{n.d(t,{Qw:()=>r,LM:()=>A,v5:()=>g,km:()=>w,pd:()=>b,BG:()=>v});var r,i=n(44542),o=n(62769),l=n(78160);let a=1e4;function d(){return`__primer_id_${a++}`}(0,i.O)(),function(e){e[e.ArrowHorizontal=1]="ArrowHorizontal",e[e.ArrowVertical=2]="ArrowVertical",e[e.JK=4]="JK",e[e.HL=8]="HL",e[e.HomeAndEnd=16]="HomeAndEnd",e[e.PageUpDown=256]="PageUpDown",e[e.WS=32]="WS",e[e.AD=64]="AD",e[e.Tab=128]="Tab",e[e.Backspace=512]="Backspace",e[e.ArrowAll=3]="ArrowAll",e[e.HJKL=12]="HJKL",e[e.WASD=96]="WASD",e[e.All=511]="All"}(r||(r={}));let s={ArrowLeft:r.ArrowHorizontal,ArrowDown:r.ArrowVertical,ArrowUp:r.ArrowVertical,ArrowRight:r.ArrowHorizontal,h:r.HL,j:r.JK,k:r.JK,l:r.HL,a:r.AD,s:r.WS,w:r.WS,d:r.AD,Tab:r.Tab,Home:r.HomeAndEnd,End:r.HomeAndEnd,PageUp:r.PageUpDown,PageDown:r.PageUpDown,Backspace:r.Backspace},u={ArrowLeft:"previous",ArrowDown:"next",ArrowUp:"previous",ArrowRight:"next",h:"previous",j:"next",k:"previous",l:"next",a:"previous",s:"next",w:"previous",d:"next",Tab:"next",Home:"start",End:"end",PageUp:"start",PageDown:"end",Backspace:"previous"};function c(e){let t=u[e.key];if("Tab"===e.key&&e.shiftKey)return"previous";let n=(0,o.e)();if(n&&e.metaKey||!n&&e.ctrlKey){if("ArrowLeft"===e.key||"ArrowUp"===e.key)return"start";if("ArrowRight"===e.key||"ArrowDown"===e.key)return"end"}return t}function f(e,t){let n=e.key,r=[...n].length,i=t instanceof HTMLInputElement&&"text"===t.type||t instanceof HTMLTextAreaElement;if(i&&(1===r||"Home"===n||"End"===n)||t instanceof HTMLSelectElement&&(1===r||"ArrowDown"===n&&(0,o.e)()&&!e.metaKey||"ArrowDown"===n&&!(0,o.e)()&&e.altKey)||t instanceof HTMLTextAreaElement&&("PageUp"===n||"PageDown"===n))return!0;if(i){let e=0===t.selectionStart&&0===t.selectionEnd,r=t.selectionStart===t.value.length&&t.selectionEnd===t.value.length;if("ArrowLeft"===n&&!e||"ArrowRight"===n&&!r||t instanceof HTMLTextAreaElement&&("ArrowUp"===n&&!e||"ArrowDown"===n&&!r))return!0}return!1}let v="data-is-active-descendant",A="activated-directly",g="activated-indirectly",b="data-has-active-descendant";function w(e,t){var n,i,o,a,w;let E,m,p;let T=[],h=new WeakMap,L=null!==(n=null==t?void 0:t.bindKeys)&&void 0!==n?n:((null==t?void 0:t.getNextFocusable)?r.ArrowAll:r.ArrowVertical)|r.HomeAndEnd,y=null!==(i=null==t?void 0:t.focusOutBehavior)&&void 0!==i?i:"stop",H=null!==(o=null==t?void 0:t.focusInStrategy)&&void 0!==o?o:"previous",k=null==t?void 0:t.activeDescendantControl,x=null==t?void 0:t.onActiveDescendantChanged,D=null!==(a=null==t?void 0:t.preventScroll)&&void 0!==a&&a;function M(e,t=!1){let n=E;if(E=e,k){e&&document.activeElement===k?N(n,e,t):P();return}n&&n!==e&&h.has(n)&&n.setAttribute("tabindex","-1"),null==e||e.setAttribute("tabindex","0")}function N(t,n,r=!1){n.id||n.setAttribute("id",d()),t&&t!==n&&t.removeAttribute(v),k&&(r||k.getAttribute("aria-activedescendant")!==n.id)&&(k.setAttribute("aria-activedescendant",n.id),e.setAttribute(b,n.id),n.setAttribute(v,r?A:g),null==x||x(n,t,r))}function P(t=E){"first"===H&&(E=void 0),null==k||k.removeAttribute("aria-activedescendant"),e.removeAttribute(b),null==t||t.removeAttribute(v),null==x||x(void 0,t,!1)}function O(...e){let n=e.filter(e=>{var n,r;return null===(r=null===(n=null==t?void 0:t.focusableElementFilter)||void 0===n?void 0:n.call(t,e))||void 0===r||r});if(0!==n.length){for(let e of(T.splice(function(e){let t=e[0];if(0===T.length)return 0;let n=0,r=T.length-1;for(;n<=r;){let e=Math.floor((n+r)/2),i=T[e];(i.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_PRECEDING)>0?r=e-1:n=e+1}return n}(n),0,...n),n))h.has(e)||h.set(e,e.getAttribute("tabindex")),e.setAttribute("tabindex","-1");E||M(T[0])}}function S(...e){for(let t of e){let e=T.indexOf(t);e>=0&&T.splice(e,1);let n=h.get(t);if(void 0!==n&&(null===n?t.removeAttribute("tabindex"):t.setAttribute("tabindex",n),h.delete(t)),t===E){let e=T[0];M(e)}}}O(...(0,l.hT)(e));let _="function"==typeof H?H(document.body):T[0];M(_);let C=new MutationObserver(e=>{for(let t of e)for(let e of t.removedNodes)e instanceof HTMLElement&&S(...(0,l.hT)(e));for(let t of e)for(let e of t.addedNodes)e instanceof HTMLElement&&O(...(0,l.hT)(e))});C.observe(e,{subtree:!0,childList:!0});let U=new AbortController,I=null!==(w=null==t?void 0:t.abortSignal)&&void 0!==w?w:U.signal;return I.addEventListener("abort",()=>{S(...T)}),e.addEventListener("mousedown",e=>{e.target instanceof HTMLElement&&e.target!==document.activeElement&&(m=T.indexOf(e.target))},{signal:I}),k?(e.addEventListener("focusin",e=>{e.target instanceof HTMLElement&&T.includes(e.target)&&(k.focus({preventScroll:D}),M(e.target))}),e.addEventListener("mousemove",({target:e})=>{if(!(e instanceof Node))return;let t=T.find(t=>t.contains(e));t&&M(t)},{signal:I,capture:!0}),k.addEventListener("focusin",()=>{E?N(void 0,E):M(T[0])}),k.addEventListener("focusout",()=>{P()})):e.addEventListener("focusin",t=>{if(t.target instanceof HTMLElement){if(void 0!==m)m>=0&&T[m]!==E&&M(T[m]),m=void 0;else if("previous"===H)M(t.target);else if("closest"===H||"first"===H){if(t.relatedTarget instanceof Element&&!e.contains(t.relatedTarget)){let e="previous"===p?T.length-1:0,t=T[e];null==t||t.focus({preventScroll:D});return}M(t.target)}else if("function"==typeof H){if(t.relatedTarget instanceof Element&&!e.contains(t.relatedTarget)){let e=H(t.relatedTarget),n=e?T.indexOf(e):-1;if(n>=0&&e instanceof HTMLElement){e.focus({preventScroll:D});return}console.warn("Element requested is not a known focusable element.")}else M(t.target)}}p=void 0},{signal:I}),"closest"===H&&document.addEventListener("keydown",e=>{"Tab"===e.key&&(p=c(e))},{signal:I,capture:!0}),(null!=k?k:e).addEventListener("keydown",n=>{var r;if(n.key in u){let i=s[n.key];if(!n.defaultPrevented&&(i&L)>0&&!f(n,document.activeElement)){let i;let o=c(n);if((null==t?void 0:t.getNextFocusable)&&(i=t.getNextFocusable(o,null!==(r=document.activeElement)&&void 0!==r?r:void 0,n)),!i){let t=function(){if(!E)return 0;let t=T.indexOf(E),n=E===e?-1:0;return -1!==t?t:n}(),r=t;"previous"===o?r-=1:"start"===o?r=0:"next"===o?r+=1:r=T.length-1,r<0&&(r="wrap"===y&&"Tab"!==n.key?T.length-1:0),r>=T.length&&(r="wrap"===y&&"Tab"!==n.key?0:T.length-1),t!==r&&(i=T[r])}k?M(i||E,!0):i&&(p=o,i.focus({preventScroll:D})),("Tab"!==n.key||i)&&n.preventDefault()}}},{signal:I}),U}},44542:(e,t,n)=>{n.d(t,{O:()=>a});let r=!1;function i(){}try{let e=Object.create({},{signal:{get(){r=!0}}});window.addEventListener("test",i,e),window.removeEventListener("test",i,e)}catch(e){}function o(){return r}function l(){if("undefined"==typeof window)return;let e=EventTarget.prototype.addEventListener;EventTarget.prototype.addEventListener=function(t,n,r){return"object"==typeof r&&"signal"in r&&r.signal instanceof AbortSignal&&e.call(r.signal,"abort",()=>{this.removeEventListener(t,n,r)}),e.call(this,t,n,r)}}function a(){o()||(l(),r=!0)}},78160:(e,t,n)=>{function*r(e,t={}){var n,r;let i=null!==(n=t.strict)&&void 0!==n&&n,a=null!==(r=t.onlyTabbable)&&void 0!==r&&r?l:o,d=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>e instanceof HTMLElement&&a(e,i)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}),s=null;if(!t.reverse&&a(e,i)&&(yield e),t.reverse){let e=d.lastChild();for(;e;)s=e,e=d.lastChild()}else s=d.firstChild();for(;s instanceof HTMLElement;)yield s,s=t.reverse?d.previousNode():d.nextNode();t.reverse&&a(e,i)&&(yield e)}function i(e,t=!1){return r(e,{reverse:t,strict:!0,onlyTabbable:!0}).next().value}function o(e,t=!1){let n=["BUTTON","INPUT","SELECT","TEXTAREA","OPTGROUP","OPTION","FIELDSET"].includes(e.tagName)&&e.disabled,r=e.hidden,i=e instanceof HTMLInputElement&&"hidden"===e.type,o=e.classList.contains("sentinel");if(n||r||i||o)return!1;if(t){let t=0===e.offsetWidth||0===e.offsetHeight,n=["hidden","collapse"].includes(getComputedStyle(e).visibility),r=0===e.getClientRects().length;if(t||n||r)return!1}return null!=e.getAttribute("tabindex")||(!(e instanceof HTMLAnchorElement)||null!=e.getAttribute("href"))&&-1!==e.tabIndex}function l(e,t=!1){return o(e,t)&&"-1"!==e.getAttribute("tabindex")}n.d(t,{EB:()=>o,O:()=>i,Wq:()=>l,hT:()=>r})},62769:(e,t,n)=>{let r;function i(){return void 0===r&&(r=/^mac/i.test(window.navigator.platform)),r}n.d(t,{e:()=>i})}}]);
//# sourceMappingURL=vendors-node_modules_primer_behaviors_dist_esm_focus-zone_js-d4b39bbd29cd.js.map