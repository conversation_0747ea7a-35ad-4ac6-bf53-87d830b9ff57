"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_behaviors_dist_esm_scroll-into-view_js-node_modules_primer_react_-04bb1b"],{13275:(e,t,o)=>{o.d(t,{z:()=>i});function i(e,t,{direction:o="vertical",startMargin:i=0,endMargin:r=0,behavior:n="smooth"}={}){let l="vertical"===o?"top":"left",a="vertical"===o?"bottom":"right",d="vertical"===o?"scrollTop":"scrollLeft",{[l]:s,[a]:c}=e.getBoundingClientRect(),{[l]:m,[a]:u}=t.getBoundingClientRect();if(s<m+i){let e=s-m+t[d];t.scrollTo({behavior:n,[l]:e-i})}else if(c>u-r){let e=c-u+t[d];t.scrollTo({behavior:n,[l]:e+r})}}},33827:(e,t,o)=>{o.d(t,{S:()=>z});var i=o(67294),r=o(15388),n=o(15173),l=o(42379);function a(){return(a=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let d=r.ZP.div.withConfig({displayName:"Header__StyledHeader",componentId:"sc-cjezay-0"})(["{}padding:6px ",";font-size:",";font-weight:",";color:",";"," ",""],(0,l.U2)("space.3"),(0,l.U2)("fontSizes.0"),(0,l.U2)("fontWeights.bold"),(0,l.U2)("colors.fg.muted"),({variant:e})=>"filled"===e&&(0,r.iv)(["background:",";margin:"," 0;border-top:1px solid ",";border-bottom:1px solid ",";&:first-child{margin-top:0;}"],(0,l.U2)("colors.canvas.subtle"),(0,l.U2)("space.2"),(0,l.U2)("colors.neutral.muted"),(0,l.U2)("colors.neutral.muted")),n.Z);function s({variant:e="subtle",title:t,auxiliaryText:o,children:r,...n}){return i.createElement(d,a({role:"heading",variant:e},n),t,o&&i.createElement("span",null,o))}s.displayName="Header";let c=r.ZP.div.withConfig({displayName:"Group__StyledGroup",componentId:"sc-1s2aw76-0"})(["",""],n.Z);function m({header:e,items:t,...o}){return i.createElement(c,o,e&&i.createElement(s,e),t)}m.displayName="Group";var u=o(89283),p=o(14890);let g=r.ZP.div.withConfig({displayName:"Divider__StyledDivider",componentId:"sc-1s7tlfq-0"})(["height:1px;background:",";margin-top:calc("," - 1px);margin-bottom:",";"],(0,l.U2)("colors.border.muted"),(0,l.U2)("space.2"),(0,l.U2)("space.2"));function f(){return i.createElement(g,null)}f.displayName="Divider",f.renderItem=f;var v=o(8386),h=o(22114),b=o(44288),y=o(79902);function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let I=(e="default",t)=>t?{color:(0,l.U2)("colors.primer.fg.disabled"),iconColor:(0,l.U2)("colors.primer.fg.disabled"),annotationColor:(0,l.U2)("colors.primer.fg.disabled"),hoverCursor:"default"}:"danger"===e?{color:(0,l.U2)("colors.danger.fg"),iconColor:(0,l.U2)("colors.danger.fg"),annotationColor:(0,l.U2)("colors.fg.muted"),hoverCursor:"pointer",hoverBg:(0,l.U2)("colors.actionListItem.danger.hoverBg"),focusBg:(0,l.U2)("colors.actionListItem.danger.activeBg"),hoverText:(0,l.U2)("colors.actionListItem.danger.hoverText")}:{color:(0,l.U2)("colors.fg.default"),iconColor:(0,l.U2)("colors.fg.muted"),annotationColor:(0,l.U2)("colors.fg.muted"),hoverCursor:"pointer",hoverBg:(0,l.U2)("colors.actionListItem.default.hoverBg"),focusBg:(0,l.U2)("colors.actionListItem.default.activeBg")},C=r.ZP.div.withConfig({displayName:"Item__DividedContent",componentId:"sc-y6iv6t-0"})(["display:flex;min-width:0;position:relative;flex-grow:1;"]),U=r.ZP.div.withConfig({displayName:"Item__MainContent",componentId:"sc-y6iv6t-1"})(["align-items:baseline;display:flex;min-width:0;flex-direction:var(--main-content-flex-direction);flex-grow:1;"]),x=r.ZP.div.withConfig({displayName:"Item__StyledItem",componentId:"sc-y6iv6t-2"})(["padding:6px ",";display:flex;border-radius:",";color:",";transition:background 33.333ms linear;text-decoration:none;@media (hover:hover) and (pointer:fine){:hover{background:var( --item-hover-bg-override,"," );color:",";cursor:",";}}:not(:first-of-type):not("," + &):not("," + &){margin-top:",";","::before{content:' ';display:block;position:absolute;width:100%;top:-7px;border:0 solid ",";border-top-width:",";}}&:hover ","::before,:hover + * ","::before{border-color:var(--item-hover-divider-border-color-override,transparent) !important;}&:focus ","::before,:focus + * ","::before,&[","] ","::before,[","] + & ","::before{border-color:transparent !important;}&[","='","']{background:",";}&[","='","']{background:",";}&:focus{background:",";outline:none;}&:active{background:",";}",""],(0,l.U2)("space.2"),(0,l.U2)("radii.2"),({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).color,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).hoverBg,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).hoverText,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).hoverCursor,g,d,({showDivider:e})=>e?"1px":"0",C,(0,l.U2)("colors.border.muted"),({showDivider:e})=>e?"1px":"0",C,C,C,C,h.BG,C,h.BG,C,h.BG,h.LM,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).focusBg,h.BG,h.v5,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).hoverBg,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).focusBg,({variant:e,item:t})=>I(e,null==t?void 0:t.disabled).focusBg,n.Z),_=r.ZP.span.withConfig({displayName:"Item__TextContainer",componentId:"sc-y6iv6t-3"})([""]),E=r.ZP.div.withConfig({displayName:"Item__BaseVisualContainer",componentId:"sc-y6iv6t-4"})(["height:20px;width:",";margin-right:",";display:flex;justify-content:center;align-items:center;flex-shrink:0;"],(0,l.U2)("space.3"),(0,l.U2)("space.2")),k=(0,r.ZP)(E).withConfig({displayName:"Item__ColoredVisualContainer",componentId:"sc-y6iv6t-5"})(["svg{fill:",";font-size:",";}"],({variant:e,disabled:t})=>I(e,t).iconColor,(0,l.U2)("fontSizes.0")),B=(0,r.ZP)(k).withConfig({displayName:"Item__LeadingVisualContainer",componentId:"sc-y6iv6t-6"})(["display:flex;flex-direction:column;justify-content:center;"]),P=(0,r.ZP)(k).withConfig({displayName:"Item__TrailingContent",componentId:"sc-y6iv6t-7"})(["color:",";margin-left:",";margin-right:0;width:auto;div:nth-child(2){margin-left:",";}"],({variant:e,disabled:t})=>I(e,t).annotationColor,(0,l.U2)("space.2"),(0,l.U2)("space.2")),N=r.ZP.span.withConfig({displayName:"Item__DescriptionContainer",componentId:"sc-y6iv6t-8"})(["color:",";font-size:",";line-height:16px;margin-left:var(--description-container-margin-left);min-width:0;flex-grow:1;flex-basis:var(--description-container-flex-basis);"],(0,l.U2)("colors.fg.muted"),(0,l.U2)("fontSizes.0")),Z=r.ZP.svg.withConfig({displayName:"Item__MultiSelectIcon",componentId:"sc-y6iv6t-9"})(["rect{fill:",";stroke:",";shape-rendering:auto;}path{fill:",";boxshadow:",";opacity:",";}"],({selected:e})=>e?(0,l.U2)("colors.accent.fg"):(0,l.U2)("colors.canvas.default"),({selected:e})=>e?(0,l.U2)("colors.accent.fg"):(0,l.U2)("colors.border.default"),(0,l.U2)("colors.fg.onEmphasis"),(0,l.U2)("shadow.small"),({selected:e})=>e?1:0),S=i.forwardRef((e,t)=>{let{as:o,text:r,description:n,descriptionVariant:a="inline",selected:d,selectionVariant:s,leadingVisual:c,trailingIcon:m,trailingVisual:g,trailingText:f,variant:h="default",showDivider:I,disabled:k,onAction:S,onKeyPress:j,children:G,onClick:O,id:L,...T}=e,z=(0,b.M)(),D=(0,b.M)(),M=(0,i.useCallback)(t=>{!k&&(null==j||j(t),!t.defaultPrevented&&[" ","Enter"].includes(t.key)&&(null==S||S(e,t)))},[S,k,e,j]),V=(0,i.useCallback)(t=>{!k&&(null==O||O(t),t.defaultPrevented||null==S||S(e,t))},[S,k,e,O]),{theme:R}=(0,v.Fg)();return i.createElement(x,w({ref:t,as:o,tabIndex:k?void 0:-1,variant:h,showDivider:I,"aria-selected":d,"aria-labelledby":r?z:void 0,"aria-describedby":n?D:void 0},T,{"data-id":L,onKeyPress:M,onClick:V}),!!d===d&&i.createElement(E,null,"multiple"===s?i.createElement(i.Fragment,null,i.createElement(Z,{selected:d,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true"},i.createElement("rect",{x:"2",y:"2",width:"12",height:"12",rx:"4"}),i.createElement("path",{fillRule:"evenodd",strokeWidth:"0",d:"M4.03231 8.69862C3.84775 8.20646 4.49385 7.77554 4.95539 7.77554C5.41693 7.77554 6.80154 9.85246 6.80154 9.85246C6.80154 9.85246 10.2631 4.314 10.4938 4.08323C10.7246 3.85246 11.8785 4.08323 11.4169 5.00631C11.0081 5.82388 7.26308 11.4678 7.26308 11.4678C7.26308 11.4678 6.80154 12.1602 6.34 11.4678C5.87846 10.7755 4.21687 9.19077 4.03231 8.69862Z"}))):d&&i.createElement(u.nQG,{fill:null==R?void 0:R.colors.fg.default})),c&&i.createElement(B,{variant:h,disabled:k},i.createElement(c,null)),i.createElement(C,null,i.createElement(U,{style:{"--main-content-flex-direction":"inline"===a?"row":"column"}},G,r?i.createElement(_,{id:z},r):null,n?i.createElement(N,{id:D,style:{"--description-container-margin-left":"inline"===a?(0,l.U2)("space.2")(R):0,"--description-container-flex-basis":"inline"===a?0:"auto"}},"block"===a?n:i.createElement(y.Z,{title:n,inline:!0,maxWidth:"100%"},n)):null),g?i.createElement(P,{variant:h,disabled:k},"string"!=typeof g&&(0,p.isValidElementType)(g)?i.createElement(g,null):g):m||f?i.createElement(P,{variant:h,disabled:k},f,m&&i.createElement(m,null)):null))});function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}function G(e){return"groupMetadata"in e}S.displayName="ActionList.Item";let O=r.ZP.div.withConfig({displayName:"List__StyledList",componentId:"sc-hkz3q0-0"})(["font-size:",";line-height:20px;&[","],&:focus-within{--item-hover-bg-override:none;--item-hover-divider-border-color-override:",";}"],(0,l.U2)("fontSizes.1"),h.pd,(0,l.U2)("colors.border.muted"));function L(e="inset"){return"full"===e?{headerStyle:{paddingX:(0,l.U2)("space.2")},itemStyle:{borderRadius:0}}:{firstGroupStyle:{marginTop:(0,l.U2)("space.2")},lastGroupStyle:{marginBottom:(0,l.U2)("space.2")},itemStyle:{marginX:(0,l.U2)("space.2")}}}let T=i.forwardRef((e,t)=>{let{firstGroupStyle:o,lastGroupStyle:r,headerStyle:n,itemStyle:l}=L(e.variant),a=t=>{var o;let r=(null!==(o="renderGroup"in t&&t.renderGroup)&&void 0!==o?o:e.renderGroup)||m;return i.createElement(r,j({},t,{key:t.groupId}))},d=(t,o,r)=>{var n,a,d;let s="renderItem"in t&&t.renderItem||e.renderItem||S,c=null!==(n=null!==(a="key"in t?t.key:void 0)&&void 0!==a?a:null===(d=t.id)||void 0===d?void 0:d.toString())&&void 0!==n?n:r.toString();return i.createElement(s,j({showDivider:e.showItemDividers,selectionVariant:e.selectionVariant},t,{key:c,sx:{...l,...t.sx},item:o}))},s=[];if(G(e)){let t=e.groupMetadata.reduce((e,t)=>e.set(t.groupId,t),new Map);for(let o of e.items){var c,u,p;let e=t.get(o.groupId),i=null!==(c=null==e?void 0:null===(u=e.items)||void 0===u?void 0:u.length)&&void 0!==c?c:0;t.set(o.groupId,{...e,items:[...null!==(p=null==e?void 0:e.items)&&void 0!==p?p:[],d({showDivider:null==e?void 0:e.showItemDividers,...e&&"renderItem"in e&&{renderItem:e.renderItem},...o},o,i)]})}s=[...t.values()]}else s=[{items:e.items.map((e,t)=>d(e,e,t)),groupId:"0"}];return i.createElement(O,j({},e,{ref:t}),s.map(({header:e,...t},l)=>{let d=(null==e?void 0:e.variant)==="filled",c=l>0&&!d;return i.createElement(i.Fragment,{key:t.groupId},c?i.createElement(f,{key:`${t.groupId}-divider`}):null,a({sx:{...0===l&&o,...l===s.length-1&&r,...l>0&&!c&&{mt:2}},...e&&{header:{...e,sx:{...n,...e.sx}}},...t}))}))});T.displayName="ActionList";let z=Object.assign(T,{Group:m,Item:S,Divider:f})}}]);
//# sourceMappingURL=vendors-node_modules_primer_behaviors_dist_esm_scroll-into-view_js-node_modules_primer_react_-04bb1b-d2936f5e0e61.js.map