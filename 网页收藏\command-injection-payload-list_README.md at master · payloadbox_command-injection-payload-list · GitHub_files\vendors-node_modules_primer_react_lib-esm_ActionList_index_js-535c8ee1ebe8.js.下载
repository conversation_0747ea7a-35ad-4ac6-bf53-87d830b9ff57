"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_ActionList_index_js"],{5186:(e,t,i)=>{i.d(t,{O:()=>r});var n=i(67294);let r=n.createContext({})},92992:(e,t,i)=>{i.d(t,{i:()=>s});var n=i(67294),r=i(42379),a=i(42483),o=i(9996),l=i.n(o);let s=({sx:e={}})=>n.createElement(a.Z,{as:"li","aria-hidden":"true",sx:l()({height:1,backgroundColor:"actionListItem.inlineDivider",marginTop:e=>`calc(${(0,r.U2)("space.2")(e)} - 1px)`,marginBottom:2,listStyle:"none"},e),"data-component":"ActionList.Divider"});s.displayName="Divider"},52516:(e,t,i)=>{i.d(t,{S:()=>Q});var n=i(67294),r=i(15388),a=i(15173),o=i(5186),l=i(7261),s=i(69889),d=i(53670),c=i(34918),p=i(31171),u=i(75308),g=i(9996),m=i.n(g);function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let v=(0,n.forwardRef)(({as:e,children:t,sx:i=l.P,visuallyHidden:r=!1,...a},s)=>{var g;let v=n.useRef(null);(0,p.z)(s,v);let{headingId:f,variant:h}=n.useContext(x),{container:M}=n.useContext(o.O);"ActionMenu"!==M||(0,c.k)(!1);let y={marginBottom:2,marginX:"full"===h?2:3};return n.createElement(d.Z,{isVisible:!r},n.createElement(u.Z,b({as:e,ref:v,id:null!==(g=a.id)&&void 0!==g?g:f,sx:m()(y,i)},a),t))});v.displayName="ActionList.Heading";var f=i(44288);function h(){return(h=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let x=n.createContext({}),M=r.ZP.ul.withConfig({displayName:"List__ListBox",componentId:"sc-1x7olzq-0"})(a.Z),y=n.forwardRef(({variant:e="inset",selectionVariant:t,showDividers:i=!1,role:r,sx:a=l.P,...d},c)=>{var p;let u={margin:0,paddingInlineStart:0,paddingY:"inset"===e?2:0},[g,b]=(0,s.R)(d.children,{heading:v}),y=(0,f.M)(),{listRole:C,listLabelledBy:k,selectionVariant:I}=n.useContext(o.O),w=g.heading?null!==(p=g.heading.props.id)&&void 0!==p?p:y:k;return n.createElement(x.Provider,{value:{variant:e,selectionVariant:t||I,showDividers:i,role:r||C,headingId:y}},g.heading,n.createElement(M,h({sx:m()(u,a),role:r||C,"aria-labelledby":w},d,{ref:c}),b))});y.displayName="ActionList";var C=i(42483);function k(){return(k=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let I=n.createContext({}),w=({title:e,variant:t="subtle",auxiliaryText:i,selectionVariant:r,role:a,sx:o={},...l})=>{let s=(0,f.M)(),{role:d}=n.useContext(x);return n.createElement(C.Z,k({as:"li",role:d?"none":void 0,sx:{"&:not(:first-child)":{marginTop:2},listStyle:"none",...o}},l),e&&n.createElement(j,{title:e,variant:t,auxiliaryText:i,labelId:s}),n.createElement(I.Provider,{value:{selectionVariant:r}},n.createElement(C.Z,{as:"ul",sx:{paddingInlineStart:0},"aria-labelledby":e?s:void 0,role:a||d&&"group"},l.children)))};w.displayName="Group";let j=({variant:e,title:t,auxiliaryText:i,labelId:r,...a})=>{let{variant:o}=n.useContext(x),l={paddingY:"6px",paddingX:"full"===o?2:3,fontSize:0,fontWeight:"bold",color:"fg.muted",..."filled"===e&&{backgroundColor:"canvas.subtle",marginX:0,marginBottom:2,borderTop:"1px solid",borderBottom:"1px solid",borderColor:"neutral.muted"}};return n.createElement(C.Z,k({sx:l,role:"presentation","aria-hidden":"true"},a),n.createElement("span",{id:r},t),i&&n.createElement("span",null,i))};j.displayName="Header";var D=i(8386);let N=n.createContext({}),O=(e,t)=>t?{color:"primer.fg.disabled",iconColor:"primer.fg.disabled",annotationColor:"primer.fg.disabled"}:"danger"===e?{color:"danger.fg",iconColor:"danger.fg",annotationColor:"fg.muted",hoverColor:"actionListItem.danger.hoverText"}:{color:"fg.default",iconColor:"fg.muted",annotationColor:"fg.muted",hoverColor:"fg.default"},E="20px";var L=i(79902);let A=({variant:e="inline",sx:t={},...i})=>{let r={fontSize:0,lineHeight:"16px",flexGrow:1,flexBasis:0,minWidth:0,marginLeft:"block"===e?0:2,color:"fg.muted",'li[aria-disabled="true"] &':{color:"inherit"},'li[data-variant="danger"]:hover &, li[data-variant="danger"]:active &':{color:"inherit"}},{blockDescriptionId:a,inlineDescriptionId:o}=n.useContext(N);return"block"===e?n.createElement(C.Z,{as:"span",sx:m()(r,t),id:a},i.children):n.createElement(L.Z,{id:o,sx:m()(r,t),title:i.children,inline:!0,maxWidth:"100%"},i.children)};var z=i(89283),T=i(42379);function Z(){return(Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let P=({sx:e={},...t})=>n.createElement(C.Z,Z({as:"span",sx:m()({height:E,minWidth:(0,T.U2)("space.3"),maxWidth:E,display:"flex",justifyContent:"center",alignItems:"center",flexShrink:0,marginRight:2},e)},t));P.displayName="LeadingVisualContainer";let S=({sx:e={},...t})=>{let{variant:i,disabled:r}=n.useContext(N);return n.createElement(P,Z({sx:m()({color:O(i,r).iconColor,svg:{fontSize:0},'[data-variant="danger"]:hover &, [data-variant="danger"]:active &':{color:O(i,r).hoverColor}},e)},t),t.children)};S.displayName="LeadingVisual";let U=({sx:e={},...t})=>{let{variant:i,disabled:r}=n.useContext(N);return n.createElement(C.Z,Z({as:"span",sx:m()({height:"20px",flexShrink:0,color:O(i,r).annotationColor,marginLeft:2,fontWeight:"initial",'[data-variant="danger"]:hover &, [data-variant="danger"]:active &':{color:O(i,r).hoverColor}},e)},t),t.children)};U.displayName="TrailingVisual";let R=({selected:e})=>{let t;let{selectionVariant:i}=n.useContext(x),{selectionVariant:r}=n.useContext(I);if(!(t=void 0!==r?r:i)){if(!e)return null;throw Error("For Item to be selected, ActionList or ActionList.Group needs to have a selectionVariant defined")}if("single"===t)return n.createElement(P,null,e&&n.createElement(z.nQG,null));let a={from:{clipPath:"inset(var(--base-size-16, 16px) 0 0 0)"},to:{clipPath:"inset(0 0 0 0)"}},o={from:{clipPath:"inset(0 0 0 0)"},to:{clipPath:"inset(var(--base-size-16, 16px) 0 0 0)"}};return n.createElement(P,null,n.createElement(C.Z,{sx:{borderColor:e?"accent.fg":"neutral.emphasis",borderStyle:"solid",borderWidth:"1",borderRadius:"1",cursor:"pointer",display:"grid",height:"var(--base-size-16, 16px)",margin:"0",placeContent:"center",width:"var(--base-size-16, 16px)",backgroundColor:e?"accent.fg":"canvas.default",transition:e?"background-color, border-color 80ms cubic-bezier(0.33, 1, 0.68, 1)":"background-color, border-color 80ms cubic-bezier(0.32, 0, 0.67, 0) 0ms","::before":{width:"var(--base-size-16, 16px)",height:"var(--base-size-16, 16px)",visibility:e?"visible":"hidden",content:'""',backgroundColor:"fg.onEmphasis",transition:e?"visibility 0s linear 0s":"visibility 0s linear 230ms",clipPath:"inset(var(--base-size-16, 16px) 0 0 0)",maskImage:"url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEuNzgwMyAwLjIxOTYyNUMxMS45MjEgMC4zNjA0MjcgMTIgMC41NTEzMDUgMTIgMC43NTAzMTNDMTIgMC45NDkzMjEgMTEuOTIxIDEuMTQwMTkgMTEuNzgwMyAxLjI4MUw0LjUxODYgOC41NDA0MkM0LjM3Nzc1IDguNjgxIDQuMTg2ODIgOC43NiAzLjk4Nzc0IDguNzZDMy43ODg2NyA4Ljc2IDMuNTk3NzMgOC42ODEgMy40NTY4OSA4LjU0MDQyTDAuMjAxNjIyIDUuMjg2MkMwLjA2ODkyNzcgNS4xNDM4MyAtMC4wMDMzMDkwNSA0Ljk1NTU1IDAuMDAwMTE2NDkzIDQuNzYwOThDMC4wMDM1NTIwNSA0LjU2NjQzIDAuMDgyMzg5NCA0LjM4MDgxIDAuMjIwMDMyIDQuMjQzMjFDMC4zNTc2NjUgNC4xMDU2MiAwLjU0MzM1NSA0LjAyNjgxIDAuNzM3OTcgNC4wMjMzOEMwLjkzMjU4NCA0LjAxOTk0IDEuMTIwOTMgNC4wOTIxNyAxLjI2MzM0IDQuMjI0ODJMMy45ODc3NCA2Ljk0ODM1TDEwLjcxODYgMC4yMTk2MjVDMTAuODU5NSAwLjA3ODk5MjMgMTEuMDUwNCAwIDExLjI0OTUgMEMxMS40NDg1IDAgMTEuNjM5NSAwLjA3ODk5MjMgMTEuNzgwMyAwLjIxOTYyNVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=')",maskSize:"75%",maskRepeat:"no-repeat",maskPosition:"center",animation:e?"checkmarkIn 80ms cubic-bezier(0.65, 0, 0.35, 1) forwards 80ms":"checkmarkOut 80ms cubic-bezier(0.65, 0, 0.35, 1) forwards","@keyframes checkmarkIn":a,"@keyframes checkmarkOut":o}},"data-component":"ActionList.Checkbox"}))};function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}R.displayName="Selection";let G=r.ZP.li.withConfig({displayName:"Item__LiBox",componentId:"sc-yeql7o-0"})(a.Z),W=n.forwardRef(({variant:e="default",disabled:t=!1,selected:i,active:r=!1,onSelect:a,sx:d=l.P,id:c,role:p,_PrivateItemWrapper:u,...g},b)=>{var v,h,M;let y;let[k,w]=(0,s.R)(g.children,{leadingVisual:S,trailingVisual:U,description:A}),{variant:j,showDividers:L,selectionVariant:z}=n.useContext(x),{selectionVariant:T}=n.useContext(I),{container:Z,afterSelect:P,selectionAttribute:W}=n.useContext(o.O),B=n.useCallback((e,t)=>{"function"==typeof a&&a(e),e.defaultPrevented||"function"!=typeof t||t()},[a]),Y=T||z;("ActionMenu"===Z||"DropdownMenu"===Z)&&(y="single"===Y?"menuitemradio":"multiple"===Y?"menuitemcheckbox":"menuitem");let{theme:H}=(0,D.Fg)(),X={fontWeight:"bold",bg:"actionListItem.default.selectedBg","&::after":{position:"absolute",top:"calc(50% - 12px)",left:-2,width:"4px",height:"24px",content:'""',bg:"accent.fg",borderRadius:2}},Q={position:"relative",display:"flex",paddingX:2,fontSize:1,paddingY:"6px",lineHeight:E,minHeight:5,marginX:"inset"===j?2:0,borderRadius:"inset"===j?2:0,transition:"background 33.333ms linear",color:O(e,t).color,cursor:"pointer","&[aria-disabled]":{cursor:"not-allowed",'[data-component="ActionList.Checkbox"]':{bg:i?"fg.muted":"var(--color-input-disabled-bg, rgba(175, 184, 193, 0.2))",borderColor:i?"fg.muted":"var(--color-input-disabled-bg, rgba(175, 184, 193, 0.2))"}},appearance:"none",background:"unset",border:"unset",width:"inset"===j?"calc(100% - 16px)":"100%",fontFamily:"unset",textAlign:"unset",marginY:"unset","@media (hover: hover) and (pointer: fine)":{":hover:not([aria-disabled])":{backgroundColor:`actionListItem.${e}.hoverBg`,color:O(e,t).hoverColor},"&:focus-visible, > a:focus-visible":{outline:"none",border:"2 solid",boxShadow:`0 0 0 2px ${null==H?void 0:H.colors.accent.emphasis}`},":active:not([aria-disabled])":{backgroundColor:`actionListItem.${e}.activeBg`,color:O(e,t).hoverColor}},"@media (forced-colors: active)":{":focus":{outline:"solid 1px transparent !important"}},'[data-component="ActionList.Item--DividerContainer"]':{position:"relative"},'[data-component="ActionList.Item--DividerContainer"]::before':{content:'" "',display:"block",position:"absolute",width:"100%",top:"-7px",border:"0 solid",borderTopWidth:L?"1px":"0",borderColor:"var(--divider-color, transparent)"},":not(:first-of-type)":{"--divider-color":null==H?void 0:H.colors.actionListItem.inlineDivider},'[data-component="ActionList.Divider"] + &':{"--divider-color":"transparent !important"},"&:hover:not([aria-disabled]), &:focus:not([aria-disabled]), &[data-focus-visible-added]:not([aria-disabled])":{"--divider-color":"transparent"},"&:hover:not([aria-disabled]) + &, &[data-focus-visible-added] + li":{"--divider-color":"transparent"},...r?X:{}},$=n.useCallback(e=>{t||B(e,P)},[B,t,P]),F=n.useCallback(e=>{!t&&[" ","Enter"].includes(e.key)&&B(e,P)},[B,t,P]),J=(0,f.M)(c),q=(0,f.M)(c&&`${c}--inline-description`),K=(0,f.M)(c&&`${c}--block-description`),ee=u||n.Fragment,et={onClick:$,onKeyPress:F,"aria-disabled":!!t||void 0,tabIndex:t?void 0:0,"aria-labelledby":`${J} ${k.description&&"block"!==k.description.props.variant?q:""}`,"aria-describedby":(null===(v=k.description)||void 0===v?void 0:v.props.variant)==="block"?K:void 0,...W&&{[W]:i},role:p||y},ei=u?{role:p||y?"none":void 0}:et,en=u?et:{};return n.createElement(N.Provider,{value:{variant:e,disabled:t,inlineDescriptionId:q,blockDescriptionId:K}},n.createElement(G,V({ref:b,sx:m()(Q,d),"data-variant":"danger"===e?e:void 0,"aria-selected":"option"===ei.role?i:void 0},ei,g),n.createElement(ee,en,n.createElement(R,{selected:i}),k.leadingVisual,n.createElement(C.Z,{"data-component":"ActionList.Item--DividerContainer",sx:{display:"flex",flexDirection:"column",flexGrow:1,minWidth:0}},n.createElement(_,{if:Boolean(k.trailingVisual),sx:{display:"flex",flexGrow:1}},n.createElement(_,{if:!!k.description&&"block"!==k.description.props.variant,sx:{display:"flex",flexGrow:1,alignItems:"baseline",minWidth:0}},n.createElement(C.Z,{as:"span",id:J,sx:{flexGrow:k.description&&"block"!==k.description.props.variant?0:1,fontWeight:k.description?"bold":"normal",marginBlockEnd:k.description&&"inline"!==k.description.props.variant?"4px":void 0}},w),(null===(h=k.description)||void 0===h?void 0:h.props.variant)!=="block"?k.description:null),k.trailingVisual),(null===(M=k.description)||void 0===M?void 0:M.props.variant)==="block"?k.description:null))))});W.displayName="ActionList.Item";let _=e=>{let{if:t,...i}=e;return t?n.createElement(C.Z,i,e.children):n.createElement(n.Fragment,null,e.children)};var B=i(73290);function Y(){return(Y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let H=n.forwardRef(({sx:e={},active:t,as:i,...r},a)=>{let o={paddingX:2,paddingY:"6px",display:"flex",flexGrow:1,borderRadius:2,color:"inherit","&:hover":{color:"inherit",textDecoration:"none"}};return n.createElement(W,{active:t,sx:{paddingY:0,paddingX:0},_PrivateItemWrapper:({children:t,onClick:l,...s})=>{let d=e=>{l&&l(e),r.onClick&&r.onClick(e)};return n.createElement(B.Z,Y({as:i,sx:m()(o,e)},s,r,{onClick:d,ref:a}),t)}},r.children)});var X=i(92992);let Q=Object.assign(y,{Group:w,Item:W,LinkItem:H,Divider:X.i,Description:A,LeadingVisual:S,TrailingVisual:U,Heading:v})},75308:(e,t,i)=>{i.d(t,{Z:()=>p});var n=i(67294),r=i(15388),a=i(42379),o=i(15173),l=i(31171);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let d=r.ZP.h2.withConfig({displayName:"Heading__StyledHeading",componentId:"sc-1c1dgg0-0"})(["font-weight:",";font-size:",";margin:0;",";"],(0,a.U2)("fontWeights.bold"),(0,a.U2)("fontSizes.5"),o.Z),c=(0,n.forwardRef)(({as:e="h2",...t},i)=>{let r=n.useRef(null);return(0,l.z)(i,r),n.createElement(d,s({as:e},t,{ref:r}))});c.displayName="Heading";var p=c},73290:(e,t,i)=>{i.d(t,{Z:()=>g});var n=i(67294),r=i(15388),a=i(27999),o=i(42379),l=i(15173),s=i(31171);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var i=arguments[t];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(e[n]=i[n])}return e}).apply(this,arguments)}let c=(0,a.system)({hoverColor:{property:"color",scale:"colors"}}),p=r.ZP.a.withConfig({displayName:"Link__StyledLink",componentId:"sc-14289xe-0"})(["color:",";text-decoration:",";&:hover{text-decoration:",";",";}&:is(button){display:inline-block;padding:0;font-size:inherit;white-space:nowrap;cursor:pointer;user-select:none;background-color:transparent;border:0;appearance:none;}",";"],e=>e.muted?(0,o.U2)("colors.fg.muted")(e):(0,o.U2)("colors.accent.fg")(e),e=>e.underline?"underline":"none",e=>e.muted?"none":"underline",e=>e.hoverColor?c:e.muted?`color: ${(0,o.U2)("colors.accent.fg")(e)}`:"",l.Z),u=(0,n.forwardRef)(({as:e="a",...t},i)=>{let r=n.useRef(null);return(0,s.z)(i,r),n.createElement(p,d({as:e},t,{ref:r}))});u.displayName="Link";var g=u},34918:(e,t,i)=>{function n(){}i.d(t,{k:()=>r});let r=n}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_ActionList_index_js-31011c984192.js.map