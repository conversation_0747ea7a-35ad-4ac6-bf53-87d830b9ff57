"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_ActionMenu_ActionMenu_js"],{48858:(e,t,n)=>{let o;n.d(t,{e:()=>u});var r=n(78160);(0,n(44542).O)();let i=[];function l(){let e=i.pop();e&&u(e.container,e.initialFocus,e.originalSignal)}function a(e){let t=new AbortController;return e.addEventListener("abort",()=>{t.abort()}),t}function u(e,t,n){let u;let s=new AbortController,c=null!=n?n:s.signal;e.setAttribute("data-focus-trap","active");let d=document.createElement("span");d.setAttribute("class","sentinel"),d.setAttribute("tabindex","0"),d.setAttribute("aria-hidden","true"),d.onfocus=()=>{let t=(0,r.O)(e,!0);null==t||t.focus()};let p=document.createElement("span");function f(n){if(n instanceof HTMLElement&&document.contains(e)){if(e.contains(n)){u=n;return}if(u&&(0,r.Wq)(u)&&e.contains(u)){u.focus();return}if(t&&e.contains(t)){t.focus();return}{let t=(0,r.O)(e);null==t||t.focus();return}}}p.setAttribute("class","sentinel"),p.setAttribute("tabindex","0"),p.setAttribute("aria-hidden","true"),p.onfocus=()=>{let t=(0,r.O)(e);null==t||t.focus()},e.prepend(d),e.append(p);let m=a(c);if(o){let e=o;o.container.setAttribute("data-focus-trap","suspended"),o.controller.abort(),i.push(e)}m.signal.addEventListener("abort",()=>{o=void 0}),c.addEventListener("abort",()=>{e.removeAttribute("data-focus-trap");let t=e.getElementsByClassName("sentinel");for(;t.length>0;)t[0].remove();let n=i.findIndex(t=>t.container===e);n>=0&&i.splice(n,1),l()}),document.addEventListener("focus",e=>{f(e.target)},{signal:m.signal,capture:!0}),f(document.activeElement),o={container:e,controller:m,initialFocus:t,originalSignal:c};let v=i.findIndex(t=>t.container===e);if(v>=0&&i.splice(v,1),!n)return s}},6324:(e,t,n)=>{n.d(t,{P:()=>ek});var o=n(67294),r=n(89283),i=n(92992),l=n(5186),a=n(78912),u=n(44288),s=n(15173),c=n(34918),d=n(15388),p=n(42379),f=n(48030),m=class extends Event{oldState;newState;constructor(e,{oldState:t="",newState:n="",...o}={}){super(e,o),this.oldState=String(t||""),this.newState=String(n||"")}},v=new WeakMap;function h(e,t,n){v.set(e,setTimeout(()=>{v.has(e)&&e.dispatchEvent(new m("toggle",{cancelable:!1,oldState:t,newState:n}))},0))}var g=globalThis.ShadowRoot||function(){},b=globalThis.HTMLDialogElement||function(){},w=new WeakMap,E=new WeakMap,y=new WeakMap;function k(e){return y.get(e)||"hidden"}var A=new WeakMap;function T(e){let t=e.popoverTargetElement;if(!t)return;let n=k(t);("show"!==e.popoverTargetAction||"showing"!==n)&&("hide"!==e.popoverTargetAction||"hidden"!==n)&&("showing"===n?I(t,!0,!0):L(t,!1)&&(A.set(t,e),F(t)))}function L(e,t){return!("auto"!==e.popover&&"manual"!==e.popover||!e.isConnected||t&&"showing"!==k(e)||!t&&"hidden"!==k(e)||e instanceof b&&e.hasAttribute("open"))&&document.fullscreenElement!==e}function x(e){return e?Array.from(E.get(e.ownerDocument)||[]).indexOf(e)+1:0}function M(e){let t=O(e),n=R(e);return x(t)>x(n)?t:n}function S(e){let t=E.get(e);for(let e of t||[]){if(e.isConnected)return e;t.delete(e)}return null}function C(e){return"function"==typeof e.getRootNode?e.getRootNode():e.parentNode?C(e.parentNode):e}function O(e){for(;e;){if(e instanceof HTMLElement&&"auto"===e.popover&&"showing"===y.get(e))return e;if((e=e.parentElement||C(e))instanceof g&&(e=e.host),e instanceof Document)return}}function R(e){for(;e;){let t=e.popoverTargetElement;if(t)return t;if((e=e.parentElement||C(e))instanceof g&&(e=e.host),e instanceof Document)return}}function P(e){let t=new Map,n=0,o=e.ownerDocument;for(let e of E.get(o)||[])t.set(e,n),n+=1;t.set(e,n),n+=1;let r=null;return!function(e){let n=O(e);if(null===n)return;let o=t.get(n);(null===r||t.get(r)<o)&&(r=n)}(e?.parentElement),r}function D(e){return!(e.hidden||(e instanceof HTMLButtonElement||e instanceof HTMLInputElement||e instanceof HTMLSelectElement||e instanceof HTMLTextAreaElement||e instanceof HTMLOptGroupElement||e instanceof HTMLOptionElement||e instanceof HTMLFieldSetElement)&&e.disabled||e instanceof HTMLInputElement&&"hidden"===e.type||e instanceof HTMLAnchorElement&&""===e.href)&&-1!==e.tabIndex}function H(e){if(e.shadowRoot&&!0!==e.shadowRoot.delegatesFocus)return null;let t=e;t.shadowRoot&&(t=t.shadowRoot);let n=t.querySelector("[autofocus]");if(n)return n;let o=e.ownerDocument.createTreeWalker(t,NodeFilter.SHOW_ELEMENT),r=o.currentNode;for(;r;){if(D(r))return r;r=o.nextNode()}}function N(e){H(e)?.focus()}var j=new WeakMap;function F(e){if(!L(e,!1))return;let t=e.ownerDocument;if(!e.dispatchEvent(new m("beforetoggle",{cancelable:!0,oldState:"closed",newState:"open"}))||!L(e,!1))return;let n=!1;if("auto"===e.popover){let n=e.getAttribute("popover"),o=P(e)||t;if(U(o,!1,!0),n!==e.getAttribute("popover")||!L(e,!1))return}S(t)||(n=!0),j.delete(e);let o=t.activeElement;e.classList.add(":popover-open"),y.set(e,"showing"),w.has(t)||w.set(t,new Set),w.get(t).add(e),N(e),"auto"===e.popover&&(E.has(t)||E.set(t,new Set),E.get(t).add(e),$(A.get(e),!0)),n&&o&&"auto"===e.popover&&j.set(e,o),h(e,"closed","open")}function I(e,t=!1,n=!1){if(!L(e,!0))return;let o=e.ownerDocument;if("auto"===e.popover&&(U(e,t,n),!L(e,!0))||($(A.get(e),!1),A.delete(e),n&&(e.dispatchEvent(new m("beforetoggle",{oldState:"open",newState:"closed"})),!L(e,!0))))return;w.get(o)?.delete(e),E.get(o)?.delete(e),e.classList.remove(":popover-open"),y.set(e,"hidden"),n&&h(e,"open","closed");let r=j.get(e);r&&(j.delete(e),t&&r.focus())}function W(e,t=!1,n=!1){let o=S(e);for(;o;)I(o,t,n),o=S(e)}function U(e,t,n){let o=e.ownerDocument||e;if(e instanceof Document)return W(o,t,n);let r=null,i=!1;for(let t of E.get(o)||[])if(t===e)i=!0;else if(i){r=t;break}if(!i)return W(o,t,n);for(;r&&"showing"===k(r)&&E.get(o)?.size;)I(r,t,n)}var _=new WeakMap;function B(e){if(!e.isTrusted)return;let t=e.composedPath()[0];if(!t)return;let n=t.ownerDocument,o=S(n);if(!o)return;let r=M(t);if(r&&"pointerdown"===e.type)_.set(n,r);else if("pointerup"===e.type){let e=_.get(n)===r;_.delete(n),e&&U(r||n,!1,!0)}}var q=new WeakMap;function $(e,t=!1){if(!e)return;q.has(e)||q.set(e,e.getAttribute("aria-expanded"));let n=e.popoverTargetElement;if(n&&"auto"===n.popover)e.setAttribute("aria-expanded",String(t));else{let t=q.get(e);t?e.setAttribute("aria-expanded",t):e.removeAttribute("aria-expanded")}}var Z=globalThis.ShadowRoot||function(){};function z(){return"undefined"!=typeof HTMLElement&&"object"==typeof HTMLElement.prototype&&"popover"in HTMLElement.prototype}function K(e,t,n){let o=e[t];Object.defineProperty(e,t,{value(e){return o.call(this,n(e))}})}var G=/(^|[^\\]):popover-open\b/g;function V(){var e;function t(e){return e?.includes(":popover-open")&&(e=e.replace(G,"$1.\\:popover-open")),e}window.ToggleEvent=window.ToggleEvent||m,K(Document.prototype,"querySelector",t),K(Document.prototype,"querySelectorAll",t),K(Element.prototype,"querySelector",t),K(Element.prototype,"querySelectorAll",t),K(Element.prototype,"matches",t),K(Element.prototype,"closest",t),K(DocumentFragment.prototype,"querySelectorAll",t),K(DocumentFragment.prototype,"querySelectorAll",t),Object.defineProperties(HTMLElement.prototype,{popover:{enumerable:!0,configurable:!0,get(){if(!this.hasAttribute("popover"))return null;let e=(this.getAttribute("popover")||"").toLowerCase();return""===e||"auto"==e?"auto":"manual"},set(e){this.setAttribute("popover",e)}},showPopover:{enumerable:!0,configurable:!0,value(){F(this)}},hidePopover:{enumerable:!0,configurable:!0,value(){I(this,!0,!0)}},togglePopover:{enumerable:!0,configurable:!0,value(e){"showing"===y.get(this)&&void 0===e||!1===e?I(this,!0,!0):(void 0===e||!0===e)&&F(this)}}});let n=new WeakMap;function o(e){Object.defineProperties(e.prototype,{popoverTargetElement:{enumerable:!0,configurable:!0,set(e){if(null===e)this.removeAttribute("popovertarget"),n.delete(this);else if(e instanceof Element)this.setAttribute("popovertarget",""),n.set(this,e);else throw TypeError("popoverTargetElement must be an element or null")},get(){if("button"!==this.localName&&"input"!==this.localName||"input"===this.localName&&"reset"!==this.type&&"image"!==this.type&&"button"!==this.type||this.disabled||this.form&&"submit"===this.type)return null;let e=n.get(this);if(e&&e.isConnected)return e;if(e&&!e.isConnected)return n.delete(this),null;let t=C(this),o=this.getAttribute("popovertarget");return(t instanceof Document||t instanceof Z)&&o&&t.getElementById(o)||null}},popoverTargetAction:{enumerable:!0,configurable:!0,get(){let e=(this.getAttribute("popovertargetaction")||"").toLowerCase();return"show"===e||"hide"===e?e:"toggle"},set(e){this.setAttribute("popovertargetaction",e)}}})}o(HTMLButtonElement),o(HTMLInputElement);let r=e=>{if(!e.isTrusted)return;let t=e.composedPath()[0];if(!(t instanceof Element)||t?.shadowRoot)return;let n=C(t);if(!(n instanceof Z||n instanceof Document))return;let o=t.closest("[popovertargetaction],[popovertarget]");if(o){T(o);return}},i=e=>{let t=e.key,n=e.target;n&&("Escape"===t||"Esc"===t)&&U(n.ownerDocument,!0,!0)};(e=document).addEventListener("click",r),e.addEventListener("keydown",i),e.addEventListener("pointerdown",B),e.addEventListener("pointerup",B)}var X=n(66044),J=n(42483);function Q(){return(Q=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}let Y=`
    padding: 0.5em 0.75em;
    width: max-content;
    height: fit-content;
    margin: auto;
    clip: auto;
    white-space: normal;
    /* for scrollbar */
    overflow: visible;
`,ee=`
  animation-name: tooltip-appear;
  animation-duration: 0.1s;
  animation-fill-mode: forwards;
  animation-timing-function: ease-in;
  animation-delay: 0s;
`,et=d.ZP.div.withConfig({displayName:"Tooltip__StyledTooltip",componentId:"sc-5xkifj-0"})(["width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;position:fixed;font:normal normal 11px/1.5 ",";-webkit-font-smoothing:subpixel-antialiased;color:",";text-align:center;word-wrap:break-word;background:",";border-radius:",";border:0;opacity:0;max-width:250px;inset:auto;@media (forced-colors:active){outline:1px solid transparent;}z-index:2147483647;display:block;&:popover-open{","}&.\\:popover-open{","}&::after{position:absolute;display:block;right:0;left:0;height:8px;content:'';}&[data-direction='n']::after,&[data-direction='ne']::after,&[data-direction='nw']::after{top:100%;}&[data-direction='s']::after,&[data-direction='se']::after,&[data-direction='sw']::after{bottom:100%;}&[data-direction='w']::after{position:absolute;display:block;height:100%;width:8px;content:'';bottom:0;left:100%;}&[data-direction='e']::after{position:absolute;display:block;height:100%;width:8px;content:'';bottom:0;right:100%;margin-left:-8px;}@keyframes tooltip-appear{from{opacity:0;}to{opacity:1;}}&:popover-open,&:popover-open::before{","}&.\\:popover-open,&.\\:popover-open::before{","}",";"],(0,p.U2)("fonts.normal"),(0,p.U2)("colors.fg.onEmphasis"),(0,p.U2)("colors.neutral.emphasisPlus"),(0,p.U2)("radii.2"),Y,Y,ee,ee,s.Z),en={nw:{side:"outside-top",align:"start"},n:{side:"outside-top",align:"center"},ne:{side:"outside-top",align:"end"},e:{side:"outside-right",align:"center"},se:{side:"outside-bottom",align:"end"},s:{side:"outside-bottom",align:"center"},sw:{side:"outside-bottom",align:"start"},w:{side:"outside-left",align:"center"}},eo={"outside-top-start":"nw","outside-top-center":"n","outside-top-end":"ne","outside-right-center":"e","outside-bottom-end":"se","outside-bottom-center":"s","outside-bottom-start":"sw","outside-left-center":"w"},er=["a[href]","button:not(:disabled)","summary","select","input:not([type=hidden])","textarea"],ei=e=>er.some(t=>e.matches(t))||e.hasAttribute("role")&&"button"===e.getAttribute("role"),el=o.createContext({}),ea=o.forwardRef(({direction:e="s",text:t,type:n="description",children:r,...i},l)=>{let a=(0,u.M)(),s=o.Children.only(r),d=(0,X.i)(l),p=(0,o.useRef)(null),[m,v]=(0,o.useState)(e),h=()=>{p.current&&d.current&&!p.current.matches(":popover-open")&&p.current.showPopover()},g=()=>{p.current&&d.current&&p.current.matches(":popover-open")&&p.current.hidePopover()};return(0,o.useEffect)(()=>{if(!p.current||!d.current)return;let t=ei(d.current),o=d.current.childNodes,r=Array.from(o).some(e=>e instanceof HTMLElement&&ei(e));t||r||(0,c.k)(!1),"label"===n&&(d.current.hasAttribute("aria-label"),Array.from(d.current.childNodes).some(e=>e instanceof HTMLElement&&e.hasAttribute("aria-label"))),"undefined"==typeof window||z()||V();let i=p.current,l=d.current;i.setAttribute("popover","auto");let a={side:en[e].side,align:en[e].align},u=()=>{let{top:e,left:t,anchorAlign:n,anchorSide:o}=(0,f.N)(i,l,a);i.style.top=`${e}px`,i.style.left=`${t}px`;let r=eo[`${o}-${n}`];v(r)};return i.addEventListener("toggle",u),()=>{i.removeEventListener("toggle",u)}},[p,d,e,n]),o.createElement(el.Provider,{value:{tooltipId:a}},o.createElement(J.Z,{sx:{display:"inline-block"},onMouseLeave:()=>g()},o.isValidElement(s)&&o.cloneElement(s,{ref:d,"aria-describedby":"description"===n?`tooltip-${a}`:void 0,"aria-labelledby":"label"===n?`tooltip-${a}`:void 0,onBlur:e=>{var t,n;g(),null===(t=(n=s.props).onBlur)||void 0===t||t.call(n,e)},onFocus:e=>{var t,n;h(),null===(t=(n=s.props).onFocus)||void 0===t||t.call(n,e)},onMouseEnter:e=>{var t,n;h(),null===(t=(n=s.props).onMouseEnter)||void 0===t||t.call(n,e)}}),o.createElement(et,Q({ref:p,"data-direction":m},i,{role:"description"===n?"tooltip":void 0,"aria-hidden":"label"===n||void 0,id:`tooltip-${a}`}),t)))});var eu=n(81322),es=n(78160);let ec=(e,t,n)=>{let[r,i]=o.useState(void 0);o.useEffect(function(){let e=n.current,t=e=>{0!==e.detail&&i("mouse-click")},o=e=>{["Space","Enter","ArrowDown","ArrowUp"].includes(e.code)&&i(e.code)};return null==e||e.addEventListener("click",t),null==e||e.addEventListener("keydown",o),()=>{null==e||e.removeEventListener("click",t),null==e||e.removeEventListener("keydown",o)}},[n]),o.useEffect(function(){if(!e||!t.current)return;let o=(0,es.hT)(t.current);if("mouse-click"===r){if(n.current)n.current.focus();else throw Error("For focus management, please attach anchorRef")}else if(r&&["ArrowDown","Space","Enter"].includes(r)){let e=o.next().value;setTimeout(()=>null==e?void 0:e.focus())}else if("ArrowUp"===r){let e=[...o],t=e[e.length-1];setTimeout(()=>t.focus())}else{let e=o.next().value;setTimeout(()=>null==e?void 0:e.focus())}},[e,r,n])},ed=(e,t)=>{let n=(0,X.i)(t);o.useEffect(function(){if(!e||!n.current)return;let t=n.current,o=[...(0,es.hT)(t)];o.map(e=>{var t;if(e.getAttribute("aria-keyshortcuts"))return;let n=null===(t=e.textContent)||void 0===t?void 0:t.toLowerCase()[0];n&&e.setAttribute("aria-keyshortcuts",n)})},[e,n]),o.useEffect(function(){if(!e||!n.current)return;let t=n.current,o=e=>{var n;let o=document.activeElement;if("INPUT"===o.tagName||"TEXTAREA"===o.tagName)return;let i=e.ctrlKey||e.altKey||e.metaKey;if(i||!r(e))return;e.stopPropagation();let l=e.key.toLowerCase(),a=[...(0,es.hT)(t)],u=a.filter(e=>{var t;let n=null===(t=e.getAttribute("aria-keyshortcuts"))||void 0===t?void 0:t.split(" ").map(e=>e.toLowerCase());return n&&n.includes(l)}),s=u.indexOf(o);null===(n=s===u.length-1?u[0]:u.find((e,t)=>t>s))||void 0===n||n.focus()};return t.addEventListener("keydown",o),()=>t.removeEventListener("keydown",o)},[e,n]);let r=e=>1===e.key.length&&/[a-z\d]/i.test(e.key);return{containerRef:n}},ep=(e,t,n,o)=>{ec(e,n,o),ed(e,n),ef(e,t,n,o),em(e,n,o)},ef=(e,t,n,r)=>{o.useEffect(()=>{let o=n.current,i=r.current,l=n=>{e&&"Tab"===n.key&&(null==t||t("tab"))};return null==o||o.addEventListener("keydown",l),null==i||i.addEventListener("keydown",l),()=>{null==o||o.removeEventListener("keydown",l),null==i||i.removeEventListener("keydown",l)}},[e,t,n,r])},em=(e,t,n)=>{o.useEffect(()=>{let o=t.current,r=n.current,i=t=>{if(!e||!o)return;let n=(0,es.hT)(o);if("ArrowDown"===t.key){let e=n.next().value;setTimeout(()=>null==e?void 0:e.focus())}else if("ArrowUp"===t.key){let e=[...n],t=e[e.length-1];setTimeout(()=>t.focus())}};return null==r||r.addEventListener("keydown",i),()=>null==r?void 0:r.addEventListener("keydown",i)},[e,t,n])};var ev=n(2708);function eh(){return(eh=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}let eg=o.createContext({renderAnchor:null,open:!1}),eb=({anchorRef:e,open:t,onOpenChange:n,children:r})=>{let[i,l]=(0,eu.R)(t,n,!1),a=o.useCallback(()=>l(!0),[l]),s=o.useCallback(()=>l(!1),[l]),c=(0,X.i)(e),d=(0,u.M)(),p=null,f=o.Children.map(r,e=>{if(e.type===ea){let t=e.props.children;return t.type===eE&&(p=n=>{let r=o.cloneElement(t,{...n});return o.cloneElement(e,{children:r,ref:c})}),null}if(e.type===ew){let t=e.props.children,n=void 0!==t&&t.type===ea;return n?null!==t.props.children&&(p=n=>{let r=t.props.children,i=o.cloneElement(r,{...n}),l=o.cloneElement(t,{children:i});return o.cloneElement(e,{children:l,ref:c})}):p=t=>o.cloneElement(e,t),null}return e.type===eE?(p=t=>o.cloneElement(e,t),null):e});return o.createElement(eg.Provider,{value:{anchorRef:c,renderAnchor:p,anchorId:d,open:i,onOpen:a,onClose:s}},f)};eb.displayName="Menu";let ew=o.forwardRef(({children:e,...t},n)=>o.cloneElement(e,{...t,ref:n})),eE=o.forwardRef(({...e},t)=>o.createElement(ew,{ref:t},o.createElement(a.z,eh({type:"button",trailingAction:r.AS7},e)))),ey=({children:e,align:t="start",side:n="outside-bottom","aria-labelledby":r,...i})=>{let{anchorRef:a,renderAnchor:u,anchorId:s,open:c,onOpen:d,onClose:p}=o.useContext(eg),f=o.useRef(null);return ep(c,p,f,a),o.createElement(ev.w,{anchorRef:a,renderAnchor:u,anchorId:s,open:c,onOpen:d,onClose:p,align:t,side:n,overlayProps:i,focusZoneSettings:{focusOutBehavior:"wrap"}},o.createElement("div",{ref:f},o.createElement(l.O.Provider,{value:{container:"ActionMenu",listRole:"menu",listLabelledBy:r||s,selectionAttribute:"aria-checked",afterSelect:p}},e)))};ey.displayName="Overlay",eb.displayName="ActionMenu";let ek=Object.assign(eb,{Button:eE,Anchor:ew,Overlay:ey,Divider:i.i})},2708:(e,t,n)=>{n.d(t,{w:()=>p});var o=n(67294),r=n(16545),i=n(17840),l=n(44288),a=n(66044),u=n(55744),s=n(48158),c=n(8677);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e}).apply(this,arguments)}let p=({renderAnchor:e,anchorRef:t,anchorId:n,children:p,open:f,onOpen:m,onClose:v,height:h,width:g,overlayProps:b,focusTrapSettings:w,focusZoneSettings:E,side:y="outside-bottom",align:k="start",alignmentOffset:A,anchorOffset:T})=>{let L=(0,a.i)(t),[x,M]=(0,u.d)(),S=(0,l.M)(n),C=(0,o.useCallback)(()=>null==v?void 0:v("click-outside"),[v]),O=(0,o.useCallback)(()=>null==v?void 0:v("escape"),[v]),R=(0,o.useCallback)(e=>{!e.defaultPrevented&&!f&&["ArrowDown","ArrowUp"," ","Enter"].includes(e.key)&&(null==m||m("anchor-key-press",e),e.preventDefault())},[f,m]),P=(0,o.useCallback)(e=>{e.defaultPrevented||0!==e.button||(f?null==v||v("anchor-click"):null==m||m("anchor-click"))},[f,m,v]),{position:D}=(0,s.a)({anchorElementRef:L,floatingElementRef:x,side:y,align:k,alignmentOffset:A,anchorOffset:T},[x.current]);return(0,o.useEffect)(()=>{!f&&x.current&&M(null)},[f,x,M]),(0,i.v)({containerRef:x,disabled:!f||!D,...E}),(0,r.P)({containerRef:x,disabled:!f||!D,...w}),o.createElement(o.Fragment,null,e&&e({ref:L,id:S,"aria-haspopup":"true","aria-expanded":f?"true":void 0,tabIndex:0,onClick:P,onKeyDown:R}),f?o.createElement(c.Z,d({returnFocusRef:L,onClickOutside:C,ignoreClickRefs:[L],onEscape:O,ref:M,role:"none",visibility:D?"visible":"hidden",height:h,width:g,top:(null==D?void 0:D.top)||0,left:(null==D?void 0:D.left)||0,anchorSide:null==D?void 0:D.anchorSide},b),p):null)};p.displayName="AnchoredOverlay"},16545:(e,t,n)=>{n.d(t,{P:()=>l});var o=n(67294),r=n(48858),i=n(66044);function l(e,t=[]){let n=(0,i.i)(null==e?void 0:e.containerRef),l=(0,i.i)(null==e?void 0:e.initialFocusRef),a=null==e?void 0:e.disabled,u=o.useRef(),s=o.useRef(null);function c(){var t;null===(t=u.current)||void 0===t||t.abort(),null!=e&&e.restoreFocusOnCleanUp&&s.current instanceof HTMLElement&&(s.current.focus(),s.current=null)}return s.current||null!=e&&e.disabled||(s.current=document.activeElement),o.useEffect(()=>{if(n.current instanceof HTMLElement){if(a)c();else{var e;return u.current=(0,r.e)(n.current,null!==(e=l.current)&&void 0!==e?e:void 0),()=>{c()}}}},[n,l,a,...t]),{containerRef:n,initialFocusRef:l}}},81322:(e,t,n)=>{n.d(t,{R:()=>r});var o=n(67294);function r(e,t,n){let[r,i]=(0,o.useState)(n),l=(0,o.useCallback)(e=>{i(e),t&&t(e)},[t]);return[null!=e?e:r,l]}}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_ActionMenu_ActionMenu_js-3317c72ad8d6.js.map