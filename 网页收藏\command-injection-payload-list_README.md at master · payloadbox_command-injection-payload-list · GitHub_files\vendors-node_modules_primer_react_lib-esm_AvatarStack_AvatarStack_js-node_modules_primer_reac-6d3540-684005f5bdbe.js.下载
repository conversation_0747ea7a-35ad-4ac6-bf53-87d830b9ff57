"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_AvatarStack_AvatarStack_js-node_modules_primer_reac-6d3540"],{90836:(e,t,r)=>{r.d(t,{Z:()=>y});var a=r(86010),i=r(67294),n=r(15388),l=r(42379),o=r(15173),s=r(26012),c=r(11791),d=r(20917),u=r(7261),m=r(42483),v=r(9996),p=r.n(v);let f=n.ZP.span.withConfig({displayName:"AvatarStack__AvatarStackWrapper",componentId:"sc-4pdg6v-0"})(["--avatar-border-width:1px;--avatar-two-margin:calc(var(--avatar-stack-size) * -0.55);--avatar-three-margin:calc(var(--avatar-stack-size) * -0.85);--avatar-stack-three-plus-min-width:calc( var(--avatar-stack-size) + calc( calc(var(--avatar-stack-size) + var(--avatar-two-margin)) + calc(var(--avatar-stack-size) + var(--avatar-three-margin)) * 2 ) + calc(var(--avatar-border-width) * 3) );display:flex;position:relative;height:var(--avatar-stack-size);min-width:",";.pc-AvatarStackBody{display:flex;position:absolute;width:var(--avatar-stack-three-plus-min-width);}.pc-AvatarItem{--avatar-size:var(--avatar-stack-size);flex-shrink:0;height:var(--avatar-stack-size);width:var(--avatar-stack-size);box-shadow:0 0 0 var(--avatar-border-width) ",";position:relative;overflow:hidden;&:first-child{margin-left:0;z-index:10;}&:nth-child(n + 2){margin-left:var(--avatar-two-margin);z-index:9;}&:nth-child(n + 3){margin-left:var(--avatar-three-margin);opacity:","%;z-index:8;}&:nth-child(n + 4){opacity:","%;z-index:7;}&:nth-child(n + 5){opacity:","%;z-index:6;}&:nth-child(n + 6){opacity:0;visibility:hidden;}}&.pc-AvatarStack--two{min-width:calc( var(--avatar-stack-size) + calc(var(--avatar-stack-size) + var(--avatar-two-margin)) + var(--avatar-border-width) );}&.pc-AvatarStack--three-plus{min-width:var(--avatar-stack-three-plus-min-width);}&.pc-AvatarStack--right{justify-content:flex-end;.pc-AvatarItem{margin-left:0 !important;&:first-child{margin-right:0;}&:nth-child(n + 2){margin-right:var(--avatar-two-margin);}&:nth-child(n + 3){margin-right:var(--avatar-three-margin);}}.pc-AvatarStackBody{flex-direction:row-reverse;&:hover{.pc-AvatarItem{margin-right:","!important;margin-left:0 !important;&:first-child{margin-right:0 !important;}}}}}.pc-AvatarStackBody:not(.pc-AvatarStack--disableExpand):hover{width:auto;.pc-AvatarItem{margin-left:",";opacity:100%;visibility:visible;box-shadow:0 0 0 4px ",";transition:margin 0.2s ease-in-out,opacity 0.2s ease-in-out,visibility 0.2s ease-in-out,box-shadow 0.1s ease-in-out;&:first-child{margin-left:0;}}}",";"],e=>1===e.count?"var(--avatar-stack-size)":2===e.count?"30px":"38px",(0,l.U2)("colors.canvas.default"),55,40,25,(0,l.U2)("space.1"),(0,l.U2)("space.1"),(0,l.U2)("colors.canvas.default"),o.Z),g=e=>i.Children.map(e,e=>i.isValidElement(e)?i.cloneElement(e,{...e.props,className:(0,a.Z)(e.props.className,"pc-AvatarItem")}):e),h=({children:e,alignRight:t,disableExpand:r,size:n,sx:l=u.P})=>{let o=i.Children.count(e),v=(0,a.Z)({"pc-AvatarStack--two":2===o,"pc-AvatarStack--three-plus":o>2,"pc-AvatarStack--right":t}),h=(0,a.Z)("pc-AvatarStackBody",{"pc-AvatarStack--disableExpand":r}),y=()=>{let t={narrow:[],regular:[],wide:[]};return i.Children.toArray(e).reduce((e,r)=>{if(!i.isValidElement(r))return e;for(let a of Object.keys(t))(0,c.fd)(r.props.size)?t[a].push(r.props.size[a]||s.e):t[a].push(r.props.size||s.e),e[a]=Math.min(...t[a]);return e},{narrow:s.e,regular:s.e,wide:s.e})},E=()=>n?(0,c.fd)(n)?(0,d.X)(n,"--avatar-stack-size",e=>`${e||s.e}px`):{"--avatar-stack-size":`${n}px`}:(0,d.X)(y(),"--avatar-stack-size",e=>`${e}px`),w=p()(E(),l);return i.createElement(f,{count:o,className:v,sx:w},i.createElement(m.Z,{className:h}," ",g(e)))};h.displayName="AvatarStack";var y=h},84915:(e,t,r)=>{r.d(t,{Z:()=>l});var a=r(67294),i=r(42483);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}function l(e){let{size:t=32,as:r,icon:l,bg:o,...s}=e;return a.createElement(i.Z,{as:r,bg:o,overflow:"hidden",borderWidth:0,size:t,borderRadius:"50%",borderStyle:"solid",borderColor:"border.default"},a.createElement(i.Z,n({display:"flex",as:r,size:t},s,{alignItems:"center",justifyContent:"center"}),a.createElement(l,{size:t})))}l.displayName="CircleOcticon"},14783:(e,t,r)=>{r.d(t,{$:()=>S});var a=r(89283),i=r(67294),n=r(15388),l=r(52516),o=r(15173),s=r(7261),c=r(44288),d=r(69848),u=r(42483),m=r(98833),v=r(9996),p=r.n(v);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}let g=e=>({paddingLeft:e>0?e+2:null,fontSize:e>0?0:null,fontWeight:e>0?"normal":null}),h=n.ZP.nav.withConfig({displayName:"NavList__NavBox",componentId:"sc-1c8ygf7-0"})(o.Z),y=i.forwardRef(({children:e,...t},r)=>i.createElement(h,f({},t,{ref:r}),i.createElement(l.S,null,e)));y.displayName="NavList";let E=i.forwardRef(({"aria-current":e,children:t,defaultOpen:r,sx:a=s.P,...n},o)=>{let{depth:c}=i.useContext(x),d=i.Children.toArray(t).find(e=>(0,i.isValidElement)(e)&&e.type===T),u=i.Children.toArray(t).filter(e=>!(0,i.isValidElement)(e)||e.type!==T);return(!(0,i.isValidElement)(d)&&r&&console.error("NavList.Item must have a NavList.SubNav to use defaultOpen."),d&&(0,i.isValidElement)(d))?i.createElement(b,{subNav:d,depth:c,defaultOpen:r,sx:a},u):i.createElement(l.S.LinkItem,f({ref:o,"aria-current":e,active:Boolean(e)&&"false"!==e,sx:p()(g(c),a)},n),t)});E.displayName="NavList.Item";let w=i.createContext({buttonId:"",subNavId:"",isOpen:!1});function b({children:e,subNav:t,depth:r,defaultOpen:n,sx:o=s.P}){var v;let f=(0,c.M)(),h=(0,c.M)(),[y,E]=i.useState(null!==(v=n||null)&&void 0!==v&&v),b=i.useRef(null),[x,T]=i.useState(!1);return(0,d.Z)(()=>{if(b.current){let e=b.current.querySelector("[aria-current]:not([aria-current=false])");e&&(T(!0),E(!0))}},[t,f]),i.createElement(w.Provider,{value:{buttonId:f,subNavId:h,isOpen:y}},i.createElement(u.Z,{as:"li","aria-labelledby":f,sx:{listStyle:"none"}},i.createElement(l.S.Item,{as:"button",id:f,"aria-expanded":y,"aria-controls":h,active:!y&&x,onClick:()=>E(e=>!e),sx:p()({...g(r),fontWeight:x?"bold":null},o)},e,i.createElement(l.S.TrailingVisual,null,i.createElement(m.Z,{icon:a.v4q,sx:{transform:y?"rotate(180deg)":"rotate(0deg)"}}))),i.createElement("div",{ref:b},t)))}b.displayName="ItemWithSubNav";let x=i.createContext({depth:0}),T=({children:e,sx:t=s.P})=>{let{buttonId:r,subNavId:a,isOpen:n}=i.useContext(w),{depth:l}=i.useContext(x);return(r&&a||console.error("NavList.SubNav must be a child of a NavList.Item"),l>3)?(console.error("NavList.SubNav only supports four levels of nesting"),null):i.createElement(x.Provider,{value:{depth:l+1}},i.createElement(u.Z,{as:"ul",id:a,"aria-labelledby":r,sx:p()({padding:0,margin:0,display:n?"block":"none"},t)},e))};T.displayName="SubNav",T.displayName="NavList.SubNav";let V=l.S.LeadingVisual;V.displayName="NavList.LeadingVisual";let A=l.S.TrailingVisual;A.displayName="NavList.TrailingVisual";let k=l.S.Divider;k.displayName="NavList.Divider";let N={},I=({title:e,children:t,sx:r=N,...a})=>i.createElement(i.Fragment,null,i.createElement(l.S.Divider,{sx:{"&:first-child":{display:"none"}}}),i.createElement(l.S.Group,f({},a,{title:e,sx:r}),t));I.displayName="NavList.Group";let S=Object.assign(y,{Item:E,SubNav:T,LeadingVisual:V,TrailingVisual:A,Divider:k,Group:I})},81248:(e,t,r)=>{r.d(t,{jw:()=>u});var a=r(67294),i=r(81313);function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e}).apply(this,arguments)}let l=e=>a.createElement(i.X,n({containerWidth:"full",padding:"none",columnGap:"none",rowGap:"none",_slotsConfig:{header:o,footer:d}},e));l.displayName="Root",l.displayName="SplitPageLayout";let o=({padding:e="normal",divider:t="line",...r})=>a.createElement(i.X.Header,n({padding:e,divider:t},r));o.displayName="Header",o.displayName="SplitPageLayout.Header";let s=({width:e="large",padding:t="normal",...r})=>a.createElement(i.X.Content,n({width:e,padding:t},r));s.displayName="Content",s.displayName="SplitPageLayout.Content";let c=({position:e="start",sticky:t=!0,padding:r="normal",divider:l="line",...o})=>a.createElement(i.X.Pane,n({position:e,sticky:t,padding:r,divider:l},o));c.displayName="Pane",c.displayName="SplitPageLayout.Pane";let d=({padding:e="normal",divider:t="line",...r})=>a.createElement(i.X.Footer,n({padding:e,divider:t},r));d.displayName="Footer",d.displayName="SplitPageLayout.Footer";let u=Object.assign(l,{Header:o,Content:s,Pane:c,Footer:d})},81455:(e,t,r)=>{r.d(t,{L:()=>G});var a=r(89283),i=r(86010),n=r(67294),l=r(15388),o=r(37990),s=r(53670),c=r(42379);function d({name:e="custom",defaultValue:t,value:r,onChange:a}){let[i,l]=n.useState(null!=r?r:t),o=n.useRef(null),s=n.useRef(a);n.useEffect(()=>{s.current=a}),null===o.current&&(o.current=void 0!==r);let c=n.useCallback(e=>{var t;let r="function"==typeof e?e(i):e;!1===o.current&&l(r),null===(t=s.current)||void 0===t||t.call(s,r)},[i]);return(n.useEffect(()=>{o.current,o.current},[e,r]),!0===o.current)?[r,c]:[i,c]}var u=r(44288),m=r(70697),v=r(69889),p=r(15173);function f(e){var t,r,a;let i=e.getAttribute("aria-label"),n=e.getAttribute("aria-labelledby");return i||(n?null!==(t=null===(r=document.getElementById(n))||void 0===r?void 0:r.textContent)&&void 0!==t?t:"":null!==(a=e.textContent)&&void 0!==a?a:"")}var g=r(17840),h=r(87691),y=r(22114);function E({containerRef:e,mouseDownRef:t}){(0,g.v)({containerRef:e,bindKeys:y.Qw.ArrowVertical|y.Qw.ArrowHorizontal|y.Qw.HomeAndEnd|y.Qw.Backspace|y.Qw.PageUpDown,preventScroll:!0,getNextFocusable:(e,t,r)=>{var a;if(t instanceof HTMLElement)return null!==(a=w(t,r))&&void 0!==a?a:t},focusInStrategy:()=>{var r,a,i;if(t.current)return;let n=null===(r=e.current)||void 0===r?void 0:r.querySelector("[aria-current]"),l=null===(a=e.current)||void 0===a?void 0:a.querySelector('[role="treeitem"]');return n instanceof HTMLElement?n:document.activeElement instanceof HTMLElement&&null!==(i=e.current)&&void 0!==i&&i.contains(document.activeElement)?document.activeElement:l instanceof HTMLElement?l:void 0}})}function w(e,t){let r=b(e);switch(`${r} ${t.key}`){case"open ArrowRight":return T(e);case"open ArrowLeft":case"closed ArrowRight":case"end ArrowRight":return;case"closed ArrowLeft":case"end ArrowLeft":return V(e)}switch(t.key){case"ArrowUp":return x(e,"previous");case"ArrowDown":return x(e,"next");case"Backspace":return V(e);case"Home":return A(e);case"End":return k(e);case"PageUp":return P(e);case"PageDown":return S(e)}}function b(e){if("treeitem"!==e.getAttribute("role"))throw Error("Element is not a treeitem");switch(e.getAttribute("aria-expanded")){case"true":return"open";case"false":return"closed";default:return"end"}}function x(e,t){let r=e.closest("[role=tree]");if(!r)return;let a=document.createTreeWalker(r,NodeFilter.SHOW_ELEMENT,e=>e instanceof HTMLElement&&"treeitem"===e.getAttribute("role")?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP),i=a.firstChild();for(;i!==e;)i=a.nextNode();let n="next"===t?a.nextNode():a.previousNode();for(;n instanceof HTMLElement&&null!==(l=n.parentElement)&&void 0!==l&&l.closest("[role=treeitem][aria-expanded=false]");){var l;n="next"===t?a.nextNode():a.previousNode()}return n instanceof HTMLElement?n:void 0}function T(e){let t=e.querySelector("[role=treeitem]");return t instanceof HTMLElement?t:void 0}function V(e){let t=e.closest("[role=group]"),r=null==t?void 0:t.closest("[role=treeitem]");return r instanceof HTMLElement?r:void 0}function A(e){let t=e.closest("[role=tree]"),r=null==t?void 0:t.querySelector("[role=treeitem]");return r instanceof HTMLElement?r:void 0}function k(e){let t=e.closest("[role=tree]"),r=Array.from((null==t?void 0:t.querySelectorAll("[role=treeitem]"))||[]);if(0===r.length)return;let a=r.length-1,i=r[a];for(;a>0&&i instanceof HTMLElement&&null!==(n=i.parentElement)&&void 0!==n&&n.closest("[role=treeitem][aria-expanded=false]");){var n;a-=1,i=r[a]}return i instanceof HTMLElement?i:void 0}let N={height:32};function I(e,t){var r,a;let i=(0,h.J)(e),{height:n}=null!==(r=null==t?void 0:t.getBoundingClientRect())&&void 0!==r?r:N,l=null!==(a=null==i?void 0:i.clientHeight)&&void 0!==a?a:window.innerHeight;return Math.floor(l/n)}function S(e){let t=e.closest('[role="tree"]');if(!t)return;let r=Array.from(t.querySelectorAll('[role="treeitem"]'));if(0===r.length)return;let a=r[0].firstElementChild,i=I(t,a),n=Math.floor(r.indexOf(e)/i),l=r.indexOf(e)-i*n;return r[Math.min(r.length-1,(n+1)*i+l)]}function P(e){let t=e.closest('[role="tree"]');if(!t)return;let r=Array.from(t.querySelectorAll('[role="treeitem"]'));if(0===r.length)return;let a=r[0].firstElementChild,i=I(t,a),n=Math.floor(r.indexOf(e)/i),l=r.indexOf(e)-i*n;return r[Math.max(0,(n-1)*i+l)]}function C({containerRef:e,onFocusChange:t}){let r=n.useRef(""),a=n.useRef(0),i=n.useRef(t),{safeSetTimeout:l,safeClearTimeout:o}=(0,m.Z)();n.useEffect(()=>{i.current=t},[t]);let s=n.useCallback(t=>{if(!t||!e.current)return;let r=e.current,a=Array.from(r.querySelectorAll('[role="treeitem"]')),n=a.findIndex(e=>e===document.activeElement),l=R(a,n);1===t.length&&(l=l.slice(1));let o=l.find(e=>{let r=f(e).toLowerCase();return r.startsWith(t.toLowerCase())});o&&i.current(o)},[e]);n.useEffect(()=>{if(!e.current)return;let t=e.current;function i(e){e.key&&!(e.key.length>1)&&(e.ctrlKey||e.altKey||e.metaKey||(r.current+=e.key,s(r.current),o(a.current),a.current=l(()=>r.current="",300),e.preventDefault(),e.stopPropagation()))}return t.addEventListener("keydown",i),()=>t.removeEventListener("keydown",i)},[e,s,o,l])}function R(e,t){return e.map((r,a)=>e[(t+a)%e.length])}var L=r(74121),_=r(97011);let z=n.createContext({announceUpdate:()=>{},expandedStateCache:{current:new Map}}),U=n.createContext({itemId:"",level:1,isSubTreeEmpty:!1,setIsSubTreeEmpty:()=>{},isExpanded:!1,setIsExpanded:()=>{},leadingVisualId:"",trailingVisualId:""}),M=l.ZP.ul.withConfig({displayName:"TreeView__UlBox",componentId:"sc-4ex6b6-0"})(["list-style:none;padding:0;margin:0;.PRIVATE_TreeView-item{outline:none;&:focus-visible > div,&.focus-visible > div{box-shadow:inset 0 0 0 2px ",";@media (forced-colors:active){outline:2px solid HighlightText;outline-offset:-2;}}}.PRIVATE_TreeView-item-container{--level:1;--toggle-width:1rem;position:relative;display:grid;grid-template-columns:calc(calc(var(--level) - 1) * (var(--toggle-width) / 2)) var(--toggle-width) 1fr;grid-template-areas:'spacer toggle content';width:100%;min-height:2rem;font-size:",";color:",";border-radius:",";cursor:pointer;&:hover{background-color:",";@media (forced-colors:active){outline:2px solid transparent;outline-offset:-2px;}}@media (pointer:coarse){--toggle-width:1.5rem;min-height:2.75rem;}&:has(.PRIVATE_TreeView-item-skeleton):hover{background-color:transparent;cursor:default;@media (forced-colors:active){outline:none;}}}&[data-omit-spacer='true'] .PRIVATE_TreeView-item-container{grid-template-columns:0 0 1fr;}.PRIVATE_TreeView-item[aria-current='true'] > .PRIVATE_TreeView-item-container{background-color:",";&::after{content:'';position:absolute;top:calc(50% - 0.75rem);left:-",";width:0.25rem;height:1.5rem;background-color:",";border-radius:",";@media (forced-colors:active){background-color:HighlightText;}}}.PRIVATE_TreeView-item-toggle{grid-area:toggle;display:flex;align-items:center;justify-content:center;height:100%;color:",";}.PRIVATE_TreeView-item-toggle--hover:hover{background-color:",";}.PRIVATE_TreeView-item-toggle--end{border-top-left-radius:",";border-bottom-left-radius:",";}.PRIVATE_TreeView-item-content{grid-area:content;display:flex;align-items:center;height:100%;padding:0 ",";gap:",";}.PRIVATE_TreeView-item-content-text{flex:1 1 auto;width:0;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;}.PRIVATE_TreeView-item-visual{display:flex;color:",";}.PRIVATE_TreeView-item-level-line{width:100%;height:100%;border-right:1px solid;border-color:",";}@media (hover:hover){.PRIVATE_TreeView-item-level-line{border-color:transparent;}&:hover .PRIVATE_TreeView-item-level-line,&:focus-within .PRIVATE_TreeView-item-level-line{border-color:",";}}.PRIVATE_TreeView-directory-icon{display:grid;color:",";}.PRIVATE_VisuallyHidden{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border-width:0;}",""],(0,c.U2)("colors.accent.fg"),(0,c.U2)("fontSizes.1"),(0,c.U2)("colors.fg.default"),(0,c.U2)("radii.2"),(0,c.U2)("colors.actionListItem.default.hoverBg"),(0,c.U2)("colors.actionListItem.default.selectedBg"),(0,c.U2)("space.2"),(0,c.U2)("colors.accent.fg"),(0,c.U2)("radii.2"),(0,c.U2)("colors.fg.muted"),(0,c.U2)("colors.treeViewItem.chevron.hoverBg"),(0,c.U2)("radii.2"),(0,c.U2)("radii.2"),(0,c.U2)("space.2"),(0,c.U2)("space.2"),(0,c.U2)("colors.fg.muted"),(0,c.U2)("colors.border.subtle"),(0,c.U2)("colors.border.subtle"),(0,c.U2)("colors.treeViewItem.directory.fill"),p.Z),H=({"aria-label":e,"aria-labelledby":t,children:r,flat:a})=>{let i=n.useRef(null),l=n.useRef(!1),[o,c]=n.useState(""),d=n.useCallback(e=>{c(e)},[]),u=(0,n.useCallback)(()=>{l.current=!0},[]);(0,n.useEffect)(()=>{function e(){l.current=!1}return document.addEventListener("mouseup",e),()=>{document.removeEventListener("mouseup",e)}},[]),E({containerRef:i,mouseDownRef:l}),C({containerRef:i,onFocusChange:e=>{e instanceof HTMLElement&&e.focus()}});let m=n.useRef(null);return null===m.current&&(m.current=new Map),n.createElement(z.Provider,{value:{announceUpdate:d,expandedStateCache:m}},n.createElement(n.Fragment,null,n.createElement(s.Z,{role:"status","aria-live":"polite","aria-atomic":"true"},o),n.createElement(M,{ref:i,role:"tree","aria-label":e,"aria-labelledby":t,"data-omit-spacer":a,onMouseDown:u},r)))};H.displayName="Root",H.displayName="TreeView";let O=n.forwardRef(({id:e,containIntrinsicSize:t,current:r=!1,defaultExpanded:l,expanded:o,onExpandedChange:s,onSelect:c,children:m},p)=>{let[f,g]=(0,v.R)(m,{leadingVisual:$,trailingVisual:W}),{expandedStateCache:h}=n.useContext(z),y=(0,u.M)(),E=(0,u.M)(),w=(0,u.M)(),[b,x]=d({name:e,defaultValue:()=>{var t,a,i;return null!==(t=null!==(a=null===(i=h.current)||void 0===i?void 0:i.get(e))&&void 0!==a?a:l)&&void 0!==t?t:r},value:o,onChange:s}),{level:T}=n.useContext(U),{hasSubTree:V,subTree:A,childrenWithoutSubTree:k}=K(g),[N,I]=n.useState(!V),[S,P]=n.useState(!1),C=n.useCallback(t=>{var r;x(t),null===(r=h.current)||void 0===r||r.set(e,t)},[e,x,h]),R=n.useCallback(e=>{C(!b),null==e||e.stopPropagation()},[b,C]),L=n.useCallback(e=>{switch(e.key){case"Enter":c?c(e):R(e);break;case"ArrowRight":if(e.altKey||e.metaKey)return;e.preventDefault(),e.stopPropagation(),C(!0);break;case"ArrowLeft":if(e.altKey||e.metaKey)return;e.preventDefault(),e.stopPropagation(),C(!1)}},[c,C,R]);return n.createElement(U.Provider,{value:{itemId:e,level:T+1,isSubTreeEmpty:N,setIsSubTreeEmpty:I,isExpanded:b,setIsExpanded:C,leadingVisualId:E,trailingVisualId:w}},n.createElement("li",{className:"PRIVATE_TreeView-item",ref:p,tabIndex:0,id:e,role:"treeitem","aria-labelledby":y,"aria-describedby":`${E} ${w}`,"aria-level":T,"aria-expanded":N?void 0:b,"aria-current":r?"true":void 0,"aria-selected":S?"true":"false",onKeyDown:L,onFocus:e=>{var t;null===(t=e.currentTarget.firstElementChild)||void 0===t||t.scrollIntoView({block:"nearest",inline:"nearest"}),P(!0),e.stopPropagation()},onBlur:()=>P(!1)},n.createElement("div",{className:"PRIVATE_TreeView-item-container",style:{"--level":T,contentVisibility:t?"auto":void 0,containIntrinsicSize:t},onClick:e=>{c?c(e):R(e)},onAuxClick:e=>{c&&1===e.button&&c(e)}},n.createElement("div",{style:{gridArea:"spacer",display:"flex"}},n.createElement(Z,{level:T})),V?n.createElement("div",{className:(0,i.Z)("PRIVATE_TreeView-item-toggle",c&&"PRIVATE_TreeView-item-toggle--hover",1===T&&"PRIVATE_TreeView-item-toggle--end"),onClick:e=>{c&&R(e)}},b?n.createElement(a.v4q,{size:12}):n.createElement(a.XCv,{size:12})):null,n.createElement("div",{id:y,className:"PRIVATE_TreeView-item-content"},f.leadingVisual,n.createElement("span",{className:"PRIVATE_TreeView-item-content-text"},k),f.trailingVisual)),A))}),Z=({level:e})=>n.createElement("div",{style:{width:"100%",display:"flex"}},Array.from({length:e-1}).map((e,t)=>n.createElement("div",{key:t,className:"PRIVATE_TreeView-item-level-line"})));Z.displayName="LevelIndicatorLines",O.displayName="TreeView.Item";let B=({count:e,state:t,children:r})=>{let{announceUpdate:a}=n.useContext(z),{itemId:i,isExpanded:l,isSubTreeEmpty:o,setIsSubTreeEmpty:s}=n.useContext(U),c=n.useRef(null),d=n.useRef(null),[u,v]=n.useState(!1),p=j(t),{safeSetTimeout:g}=(0,m.Z)();return(n.useEffect(()=>{(void 0===t||"done"===t)&&(o||r?o&&r&&s(!1):s(!0))},[t,o,s,r]),n.useEffect(()=>{if("loading"===p&&"done"===t){var e;let t=document.getElementById(i);if(!t)return;let r=f(t);if(null!==(e=d.current)&&void 0!==e&&e.childElementCount?a(`${r} content loaded`):a(`${r} is empty`),u){let e=T(t);e?g(()=>{e.focus()}):g(()=>{t.focus()}),v(!1)}}},[u,p,t,i,a,d,g]),n.useEffect(()=>{function e(){v(!0)}function t(e){e.relatedTarget&&v(!1)}let r=c.current;if(r)return r.addEventListener("focus",e),r.addEventListener("blur",t),()=>{r.removeEventListener("focus",e),r.removeEventListener("blur",t)}},[c,t]),l)?n.createElement("ul",{role:"group",style:{listStyle:"none",padding:0,margin:0},ref:d},"loading"===t?n.createElement(q,{ref:c,count:e}):r):null};function j(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e},[e]),t.current}B.displayName="SubTree",B.displayName="TreeView.SubTree";let D=(0,l.F4)(["from{mask-position:200%;}to{mask-position:0%;}"]),F=l.ZP.span.attrs({className:"PRIVATE_TreeView-item-skeleton"}).withConfig({displayName:"TreeView__SkeletonItem",componentId:"sc-4ex6b6-1"})(["display:flex;align-items:center;column-gap:0.5rem;height:2rem;@media (pointer:coarse){height:2.75rem;}@media (prefers-reduced-motion:no-preference){mask-image:linear-gradient(75deg,#000 30%,rgba(0,0,0,0.65) 80%);mask-size:200%;animation:",";animation-duration:1s;animation-iteration-count:infinite;}&::before{content:'';display:block;width:1rem;height:1rem;background-color:",";border-radius:3px;@media (forced-colors:active){outline:1px solid transparent;outline-offset:-1px;}}&::after{content:'';display:block;width:var(--tree-item-loading-width,67%);height:1rem;background-color:",";border-radius:3px;@media (forced-colors:active){outline:1px solid transparent;outline-offset:-1px;}}&:nth-of-type(5n + 1){--tree-item-loading-width:67%;}&:nth-of-type(5n + 2){--tree-item-loading-width:47%;}&:nth-of-type(5n + 3){--tree-item-loading-width:73%;}&:nth-of-type(5n + 4){--tree-item-loading-width:64%;}&:nth-of-type(5n + 5){--tree-item-loading-width:50%;}"],D,(0,c.U2)("colors.neutral.subtle"),(0,c.U2)("colors.neutral.subtle")),q=n.forwardRef(({count:e},t)=>{let r=(0,u.M)();return e?n.createElement(O,{id:r,ref:t},Array.from({length:e}).map((e,t)=>n.createElement(F,{"aria-hidden":!0,key:t})),n.createElement("div",{className:"PRIVATE_VisuallyHidden"},"Loading ",e," items")):n.createElement(O,{id:r,ref:t},n.createElement($,null,n.createElement(L.Z,{size:"small"})),n.createElement(_.Z,{sx:{color:"fg.muted"}},"Loading..."))});function K(e){return n.useMemo(()=>{let t=n.Children.toArray(e).find(e=>n.isValidElement(e)&&e.type===B),r=n.Children.toArray(e).filter(e=>!(n.isValidElement(e)&&e.type===B));return{subTree:t,childrenWithoutSubTree:r,hasSubTree:Boolean(t)}},[e])}let $=e=>{let{isExpanded:t,leadingVisualId:r}=n.useContext(U),a="function"==typeof e.children?e.children({isExpanded:t}):e.children;return n.createElement(n.Fragment,null,n.createElement("div",{className:"PRIVATE_VisuallyHidden","aria-hidden":!0,id:r},e.label),n.createElement("div",{className:"PRIVATE_TreeView-item-visual","aria-hidden":!0},a))};$.displayName="TreeView.LeadingVisual";let W=e=>{let{isExpanded:t,trailingVisualId:r}=n.useContext(U),a="function"==typeof e.children?e.children({isExpanded:t}):e.children;return n.createElement(n.Fragment,null,n.createElement("div",{className:"PRIVATE_VisuallyHidden","aria-hidden":!0,id:r},e.label),n.createElement("div",{className:"PRIVATE_TreeView-item-visual","aria-hidden":!0},a))};W.displayName="TreeView.TrailingVisual";let X=()=>{let{isExpanded:e}=n.useContext(U),t=e?a.BHf:a.SlO;return n.createElement("div",{className:"PRIVATE_TreeView-directory-icon"},n.createElement(t,null))};X.displayName="DirectoryIcon";let Q=({title:e="Error",children:t,onRetry:r,onDismiss:a})=>{let{itemId:i,setIsExpanded:l}=n.useContext(U);return n.createElement("div",{onKeyDown:e=>{["Backspace","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Enter"].includes(e.key)&&e.stopPropagation()}},n.createElement(o.U,{title:e,onClose:e=>{setTimeout(()=>{let e=document.getElementById(i);null==e||e.focus()}),"confirm"===e?null==r||r():(l(!1),null==a||a())},confirmButtonContent:"Retry",cancelButtonContent:"Dismiss"},t))};Q.displayName="ErrorDialog",Q.displayName="TreeView.ErrorDialog";let G=Object.assign(H,{Item:O,SubTree:B,LeadingVisual:$,TrailingVisual:W,DirectoryIcon:X,ErrorDialog:Q})}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_AvatarStack_AvatarStack_js-node_modules_primer_reac-6d3540-d71a74ef7bf0.js.map