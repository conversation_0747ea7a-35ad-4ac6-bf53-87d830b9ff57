"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_Avatar_Avatar_js-node_modules_primer_react_lib-esm_-9bd36c"],{26012:(e,t,r)=>{r.d(t,{Z:()=>h,e:()=>m});var o=r(67294),n=r(15388),a=r(42379),l=r(15173),i=r(11791),s=r(20917),d=r(7261),c=r(9996),p=r.n(c);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let m=20,g=n.ZP.img.attrs(e=>({height:e.size,width:e.size})).withConfig({displayName:"Avatar__StyledAvatar",componentId:"sc-2lv0r8-0"})(["display:inline-block;overflow:hidden;line-height:",";vertical-align:middle;border-radius:",";box-shadow:0 0 0 1px ",";height:var(--avatar-size);width:var(--avatar-size);",""],(0,a.U2)("lineHeights.condensedUltra"),e=>e.square?"clamp(4px, var(--avatar-size) - 24px, 6px)":"50%",(0,a.U2)("colors.avatar.border"),l.Z),b=o.forwardRef(function({alt:e="",size:t=m,square:r=!1,sx:n=d.P,...a},l){let c=(0,i.fd)(t)?p()((0,s.X)(t,"--avatar-size",e=>`${e||m}px`),n):p()({"--avatar-size":`${t}px`},n);return o.createElement(g,u({ref:l,alt:e,size:t,square:r,sx:c},a))});var h=b},48170:(e,t,r)=>{r.d(t,{Z:()=>i});var o=r(15388),n=r(42379),a=r(15173);let l=o.ZP.a.withConfig({displayName:"BranchName",componentId:"sc-sg8jsy-0"})(["display:inline-block;padding:2px 6px;font-size:",";font-family:",";color:",";background-color:",";border-radius:",";text-decoration:none;",";"],(0,n.U2)("fontSizes.0"),(0,n.U2)("fonts.mono"),(0,n.U2)("colors.accent.fg"),(0,n.U2)("colors.accent.subtle"),(0,n.U2)("radii.2"),a.Z);var i=l},89042:(e,t,r)=>{r.d(t,{Q:()=>i});var o=r(67294),n=r(21413),a=r(7261);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let i=(0,o.forwardRef)(({children:e,as:t="a",sx:r=a.P,...i},s)=>o.createElement(n.X,l({as:t,ref:s,sx:r},i),e))},33831:(e,t,r)=>{r.d(t,{Z:()=>i});var o=r(15388),n=r(42379),a=r(15173);let l=o.ZP.div.withConfig({displayName:"ButtonGroup",componentId:"sc-1gxhls1-0"})(["display:inline-flex;vertical-align:middle;isolation:isolate;&& > *{margin-inline-end:-1px;position:relative;border-radius:0;:first-child{border-top-left-radius:",";border-bottom-left-radius:",";}:last-child{border-top-right-radius:",";border-bottom-right-radius:",";}:focus,:active,:hover{z-index:1;}}",";"],(0,n.U2)("radii.2"),(0,n.U2)("radii.2"),(0,n.U2)("radii.2"),(0,n.U2)("radii.2"),a.Z);var i=l},8760:(e,t,r)=>{r.d(t,{Z:()=>g});var o=r(67294),n=r(48987),a=r(4776),l=r(14682),i=r(99782),s=r(56167),d=r(9770),c=r(55744),p=r(49297);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let m=({children:e,disabled:t,onChange:r,...n})=>{let a=o.Children.toArray(e).filter(e=>o.isValidElement(e)&&e.type===d.Z).map(e=>o.isValidElement(e)?e.props.children:[]).flat(),l=o.Children.toArray(a).filter(e=>o.isValidElement(e)&&e.type===i.Z).map(e=>o.isValidElement(e)&&(e.props.checked||e.props.defaultChecked)&&e.props.value).filter(Boolean),[m,g]=(0,c.d)(l),b=e=>{let{value:t,checked:r}=e.currentTarget;if(r){g([...m.current||[],t]);return}g((m.current||[]).filter(e=>e!==t))};return o.createElement(s.w.Provider,{value:{disabled:t,onChange:e=>{r&&(b(e),r(m.current||[],e))}}},o.createElement(p.Z,u({disabled:t},n),e))};m.displayName="CheckboxGroup";var g=Object.assign(m,{Caption:n.Z,Label:a.Z,Validation:l.Z})},66280:(e,t,r)=>{r.d(t,{V:()=>$});var o=r(67294),n=r(15388),a=r(42379),l=r(16545),i=r(15173),s=r(89283),d=r(17840),c=r(22114),p=r(41905),u=r(31171),m=r(44288),g=r(53226),b=r(35048),h=r(66044),v=r(98833),y=r(42483),x=r(75381);let w=(0,n.ZP)(x.Z).withConfig({displayName:"ButtonPrimary",componentId:"sc-1awfvt4-0"})(["color:",";border:1px solid ",";background-color:",";box-shadow:",";&:hover{color:",";background-color:",";border-color:",";box-shadow:",";}&:focus{border-color:",";box-shadow:",";}&:active{background-color:",";box-shadow:",";}&:disabled{color:",";background-color:",";border-color:",";}",";"],(0,a.U2)("colors.btn.primary.text"),(0,a.U2)("colors.btn.primary.border"),(0,a.U2)("colors.btn.primary.bg"),(0,a.U2)("shadows.btn.primary.shadow"),(0,a.U2)("colors.btn.primary.hoverText"),(0,a.U2)("colors.btn.primary.hoverBg"),(0,a.U2)("colors.btn.primary.hoverBorder"),(0,a.U2)("shadows.btn.primary.hoverShadow"),(0,a.U2)("colors.btn.primary.focusBorder"),(0,a.U2)("shadows.btn.primary.focusShadow"),(0,a.U2)("colors.btn.primary.selectedBg"),(0,a.U2)("shadows.btn.primary.selectedShadow"),(0,a.U2)("colors.btn.primary.disabledText"),(0,a.U2)("colors.btn.primary.disabledBg"),(0,a.U2)("colors.btn.primary.disabledBorder"),i.Z);var C=w;let U=(0,n.ZP)(x.Z).withConfig({displayName:"ButtonDanger",componentId:"sc-j9bmd7-0"})(["color:",";border:1px solid ",";background-color:",";box-shadow:",";&:hover{color:",";background-color:",";border-color:",";box-shadow:",";}&:focus{border-color:",";box-shadow:",";}&:active{color:",";background-color:",";box-shadow:",";border-color:",";}&:disabled{color:",";background-color:",";border-color:",";}",";"],(0,a.U2)("colors.btn.danger.text"),(0,a.U2)("colors.btn.border"),(0,a.U2)("colors.btn.bg"),(0,a.U2)("shadows.btn.shadow"),(0,a.U2)("colors.btn.danger.hoverText"),(0,a.U2)("colors.btn.danger.hoverBg"),(0,a.U2)("colors.btn.danger.hoverBorder"),(0,a.U2)("shadows.btn.danger.hoverShadow"),(0,a.U2)("colors.btn.danger.focusBorder"),(0,a.U2)("shadows.btn.danger.focusShadow"),(0,a.U2)("colors.btn.danger.selectedText"),(0,a.U2)("colors.btn.danger.selectedBg"),(0,a.U2)("shadows.btn.danger.selectedShadow"),(0,a.U2)("colors.btn.danger.selectedBorder"),(0,a.U2)("colors.btn.danger.disabledText"),(0,a.U2)("colors.btn.danger.disabledBg"),(0,a.U2)("colors.btn.danger.disabledBorder"),i.Z);var E=U;function Z(){return(Z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let k="200ms",j=(0,n.ZP)("div").withConfig({displayName:"Dialog__Backdrop",componentId:"sc-uaxjsn-0"})(["position:fixed;top:0;left:0;bottom:0;right:0;display:flex;align-items:center;justify-content:center;background-color:",";animation:dialog-backdrop-appear "," ",";@keyframes dialog-backdrop-appear{0%{opacity:0;}100%{opacity:1;}}"],(0,a.U2)("colors.primer.canvas.backdrop"),k,(0,a.U2)("animation.easeOutCubic")),O={small:"480px",large:"640px",auto:"auto"},S={small:"296px",medium:"320px",large:"480px",xlarge:"640px"},_=n.ZP.div.withConfig({displayName:"Dialog__StyledDialog",componentId:"sc-uaxjsn-1"})(["display:flex;flex-direction:column;background-color:",";box-shadow:",";min-width:296px;max-width:calc(100vw - 64px);max-height:calc(100vh - 64px);width:",";height:",";border-radius:12px;opacity:1;animation:overlay--dialog-appear "," ",";@keyframes overlay--dialog-appear{0%{opacity:0;transform:scale(0.5);}100%{opacity:1;transform:scale(1);}}",";"],(0,a.U2)("colors.canvas.overlay"),(0,a.U2)("shadows.overlay.shadow"),e=>{var t;return S[null!==(t=e.width)&&void 0!==t?t:"xlarge"]},e=>{var t;return O[null!==(t=e.height)&&void 0!==t?t:"auto"]},k,(0,a.U2)("animation.easeOutCubic"),i.Z),N=({dialogLabelId:e,title:t,subtitle:r,dialogDescriptionId:n,onClose:a})=>{let l=(0,o.useCallback)(()=>{a("close-button")},[a]);return o.createElement($.Header,null,o.createElement(y.Z,{display:"flex"},o.createElement(y.Z,{display:"flex",px:2,py:"6px",flexDirection:"column",flexGrow:1},o.createElement($.Title,{id:e},null!=t?t:"Dialog"),r&&o.createElement($.Subtitle,{id:n},r)),o.createElement($.CloseButton,{onClose:l})))};N.displayName="DefaultHeader";let B=({children:e})=>o.createElement($.Body,null,e);B.displayName="DefaultBody";let P=({footerButtons:e})=>{let{containerRef:t}=(0,d.v)({bindKeys:c.Qw.ArrowHorizontal|c.Qw.Tab,focusInStrategy:"closest"});return e?o.createElement($.Footer,{ref:t},o.createElement($.Buttons,{buttons:e})):null},I=o.forwardRef((e,t)=>{let{title:r="Dialog",subtitle:n="",renderHeader:a,renderBody:i,renderFooter:s,onClose:d,role:c="dialog",width:g="xlarge",height:h="auto",footerButtons:v=[],sx:y}=e,x=(0,m.M)(),w=(0,m.M)(),C=(0,o.useRef)(null);for(let e of v)e.autoFocus&&(e.ref=C);let U={...e,title:r,subtitle:n,role:c,dialogLabelId:x,dialogDescriptionId:w},E=(0,o.useRef)(null);(0,u.z)(t,E);let Z=(0,o.useRef)(null);(0,l.P)({containerRef:E,restoreFocusOnCleanUp:!0,initialFocusRef:C}),(0,b.o)(e=>{d("escape"),e.preventDefault()},[d]),o.useEffect(()=>{let e=document.body.style.overflow||"";if("hidden"!==e)return document.body.style.overflow="hidden",()=>{document.body.style.overflow=e}},[]);let k=(null!=a?a:N)(U),O=(null!=i?i:B)(U),S=(null!=s?s:P)(U);return o.createElement(o.Fragment,null,o.createElement(p.h,null,o.createElement(j,{ref:Z},o.createElement(_,{width:g,height:h,ref:E,role:c,"aria-labelledby":x,"aria-describedby":w,"aria-modal":!0,sx:y},k,O,S))))});I.displayName="Dialog";let z=n.ZP.div.withConfig({displayName:"Dialog__Header",componentId:"sc-uaxjsn-2"})(["box-shadow:0 1px 0 ",";padding:",";z-index:1;flex-shrink:0;"],(0,a.U2)("colors.border.default"),(0,a.U2)("space.2")),D=n.ZP.h1.withConfig({displayName:"Dialog__Title",componentId:"sc-uaxjsn-3"})(["font-size:",";font-weight:",";margin:0;",";"],(0,a.U2)("fontSizes.1"),(0,a.U2)("fontWeights.bold"),i.Z),R=n.ZP.h2.withConfig({displayName:"Dialog__Subtitle",componentId:"sc-uaxjsn-4"})(["font-size:",";color:",";margin:0;margin-top:",";",";"],(0,a.U2)("fontSizes.0"),(0,a.U2)("colors.fg.muted"),(0,a.U2)("space.1"),i.Z),V=n.ZP.div.withConfig({displayName:"Dialog__Body",componentId:"sc-uaxjsn-5"})(["flex-grow:1;overflow:auto;padding:",";",";"],(0,a.U2)("space.3"),i.Z),T=n.ZP.div.withConfig({displayName:"Dialog__Footer",componentId:"sc-uaxjsn-6"})(["box-shadow:0 -1px 0 ",";padding:",";display:flex;flex-flow:wrap;justify-content:flex-end;z-index:1;flex-shrink:0;button{margin-left:",";&:first-child{margin-left:0;}}",";"],(0,a.U2)("colors.border.default"),(0,a.U2)("space.3"),(0,a.U2)("space.1"),i.Z),A={normal:g.Z,primary:C,danger:E},F=({buttons:e})=>{var t;let r=(0,h.i)(null===(t=e.find(e=>e.autoFocus))||void 0===t?void 0:t.ref),n=0,[a,l]=(0,o.useState)(0);return(0,o.useEffect)(()=>{if(1===a){var e;null===(e=r.current)||void 0===e||e.focus()}else l(a+1)},[r,a]),o.createElement(o.Fragment,null,e.map((e,t)=>{let{content:a,buttonType:l="normal",autoFocus:i=!1,...s}=e,d=A[l];return o.createElement(d,Z({key:t},s,{variant:l,ref:i&&0===n?(n++,r):null}),a)}))},L=(0,n.ZP)(g.Z).withConfig({displayName:"Dialog__DialogCloseButton",componentId:"sc-uaxjsn-7"})(["border-radius:4px;background:transparent;border:0;vertical-align:middle;color:",";padding:",";align-self:flex-start;line-height:normal;box-shadow:none;"],(0,a.U2)("colors.fg.muted"),(0,a.U2)("space.2")),W=({onClose:e})=>o.createElement(L,{"aria-label":"Close",onClick:e},o.createElement(v.Z,{icon:s.b0D}));W.displayName="CloseButton";let $=Object.assign(I,{Header:z,Title:D,Subtitle:R,Body:V,Footer:T,Buttons:F,CloseButton:W})},47001:(e,t,r)=>{r.d(t,{Z:()=>d});var o=r(67294),n=r(15388),a=r(68897),l=r(73418),i=r(15173);let s=(0,n.ZP)((0,l.L)(o,"relative-time",a.nJ)).withConfig({displayName:"RelativeTime",componentId:"sc-lqbqy3-0"})(i.Z);var d=s},72278:(e,t,r)=>{r.d(t,{s:()=>B});var o=r(67294),n=r(15388),a=r(15173),l=r(42379);let i={":first-child":{marginLeft:"-1px"},":last-child":{marginRight:"-1px"}},s={marginRight:"1px",":after":{backgroundColor:"var(--separator-color)",content:'""',position:"absolute",right:"-2px",top:2,bottom:2,width:"1px"}},d=e=>({"--segmented-control-button-inner-padding":"12px","--segmented-control-button-bg-inset":"4px","--segmented-control-outer-radius":(0,l.U2)("radii.2")(e),backgroundColor:"transparent",borderColor:"transparent",borderRadius:"var(--segmented-control-outer-radius)",borderWidth:0,color:"currentColor",cursor:"pointer",fontFamily:"inherit",fontSize:"inherit",fontWeight:null!=e&&e.selected?"bold":"normal",padding:null!=e&&e.selected?0:"var(--segmented-control-button-bg-inset)",height:"100%",width:"100%",".segmentedControl-content":{alignItems:"center",backgroundColor:null!=e&&e.selected?"segmentedControl.button.bg":"transparent",borderColor:null!=e&&e.selected?"segmentedControl.button.selected.border":"transparent",borderStyle:"solid",borderWidth:1,borderRadius:null!=e&&e.selected?"var(--segmented-control-outer-radius)":"calc(var(--segmented-control-outer-radius) - var(--segmented-control-button-bg-inset) / 2)",display:"flex",height:"100%",justifyContent:"center",paddingLeft:null!=e&&e.selected?"var(--segmented-control-button-inner-padding)":"calc(var(--segmented-control-button-inner-padding) - var(--segmented-control-button-bg-inset))",paddingRight:null!=e&&e.selected?"var(--segmented-control-button-inner-padding)":"calc(var(--segmented-control-button-inner-padding) - var(--segmented-control-button-bg-inset))"},svg:{fill:"fg.muted"},":hover .segmentedControl-content":{backgroundColor:null!=e&&e.selected?void 0:"segmentedControl.button.hover.bg"},":active .segmentedControl-content":{backgroundColor:null!=e&&e.selected?void 0:"segmentedControl.button.active.bg"},":focus:focus-visible:not(:last-child):after":{width:0},".segmentedControl-text":{":after":{content:`"${null==e?void 0:e.children}"`,display:"block",fontWeight:"bold",height:0,overflow:"hidden",pointerEvents:"none",userSelect:"none",visibility:"hidden"}},"@media (pointer: coarse)":{":before":{content:'""',position:"absolute",left:0,right:0,transform:"translateY(-50%)",top:"50%",minHeight:"44px"}}}),c=()=>({display:"block",position:"relative",flexGrow:1,marginTop:"-1px",marginBottom:"-1px",":not(:last-child)":s,":focus-within:has(:focus-visible)":{"--separator-color":"transparent"},...i});var p=r(7261),u=r(42483),m=r(9996),g=r.n(m);function b(){return(b=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let h=n.ZP.button.withConfig({displayName:"SegmentedControlButton__SegmentedControlButtonStyled",componentId:"sc-8lkgxl-0"})(["",";"],a.Z),v=({children:e,leadingIcon:t,selected:r,sx:n=p.P,...a})=>{let l=g()(c(),n);return o.createElement(u.Z,{as:"li",sx:l},o.createElement(h,b({"aria-current":r,sx:d({selected:r,children:e})},a),o.createElement("span",{className:"segmentedControl-content"},t&&o.createElement(u.Z,{mr:1},o.createElement(t,null)),o.createElement(u.Z,{className:"segmentedControl-text"},e))))};v.displayName="SegmentedControlButton";var y=v;function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let w=n.ZP.button.withConfig({displayName:"SegmentedControlIconButton__SegmentedControlIconButtonStyled",componentId:"sc-oxh6a9-0"})(["",";"],a.Z),C=({"aria-label":e,icon:t,selected:r,sx:n=p.P,...a})=>{let l=g()({width:"32px",...c()},n);return o.createElement(u.Z,{as:"li",sx:l},o.createElement(w,x({"aria-label":e,"aria-current":r,sx:d({selected:r,isIconOnly:!0})},a),o.createElement("span",{className:"segmentedControl-content"},o.createElement(t,null))))};C.displayName="SegmentedControlIconButton";var U=C,E=r(52516),Z=r(8386),k=r(11791),j=r(6324);function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}let S=n.ZP.ul.withConfig({displayName:"SegmentedControl__SegmentedControlList",componentId:"sc-1rzig82-0"})(["",";"],a.Z),_=e=>({backgroundColor:"segmentedControl.bg",borderRadius:2,display:e.isFullWidth?"flex":"inline-flex",fontSize:"small"===e.size?0:1,height:"small"===e.size?"28px":"32px",margin:0,padding:0,width:e.isFullWidth?"100%":void 0}),N=({"aria-label":e,"aria-labelledby":t,children:r,fullWidth:n,onChange:a,size:l,sx:i=p.P,variant:s="default",...d})=>{let c=(0,o.useRef)(null),{theme:u}=(0,Z.Fg)(),m=void 0===a||o.Children.toArray(r).some(e=>o.isValidElement(e)&&void 0!==e.props.defaultSelected),b=(0,k.Vt)(s,"default"),h=(0,k.Vt)(n,!1),v=o.Children.toArray(r).map(e=>o.isValidElement(e)&&(e.props.defaultSelected||e.props.selected)),x=v.some(e=>e),w=x?v.indexOf(!0):0,[C,N]=(0,o.useState)(w),B=m?C:w,P=o.isValidElement(o.Children.toArray(r)[B])?o.Children.toArray(r)[B]:void 0,I=e=>o.isValidElement(e)&&e.type===y&&e.props.leadingIcon?e.props.leadingIcon:o.isValidElement(e)?e.props.icon:null,z=e=>o.isValidElement(e)&&e.type===y?e.props.children:o.isValidElement(e)?e.props["aria-label"]:null,D=g()(_({isFullWidth:h,size:l}),i);return e||t||console.warn("Use the `aria-label` or `aria-labelledby` prop to provide an accessible label for assistive technologies"),"dropdown"===b?o.createElement(o.Fragment,null,o.createElement(j.P,null,o.createElement(j.P.Button,{"aria-label":e,leadingIcon:I(P)},z(P)),o.createElement(j.P.Overlay,{"aria-labelledby":t},o.createElement(E.S,{selectionVariant:"single"},o.Children.map(r,(e,t)=>{let r=I(e);return o.isValidElement(e)?o.createElement(E.S.Item,{key:`segmented-control-action-btn-${t}`,selected:t===B,onSelect:r=>{m&&N(t),a&&a(t),e.props.onClick&&e.props.onClick(r)}},r&&o.createElement(r,null)," ",z(e)):null}))))):o.createElement(S,O({sx:D,"aria-label":e,"aria-labelledby":t,ref:c},d),o.Children.map(r,(e,t)=>{if(!o.isValidElement(e))return null;let r={onClick:a?r=>{a(t),m&&N(t),e.props.onClick&&e.props.onClick(r)}:r=>{e.props.onClick&&e.props.onClick(r),m&&N(t)},selected:t===B,sx:{"--separator-color":t===B||t===B-1?"transparent":null==u?void 0:u.colors.border.default,...e.props.sx}};if("hideLabels"===b&&o.isValidElement(e)&&e.type===y){let{"aria-label":t,leadingIcon:n,children:a,...l}=e.props,{sx:i,...s}=r;if(n)return o.createElement(U,O({"aria-label":t||a,icon:n,sx:{...i,width:h?"100%":"32px"}},s,l));console.warn("A `leadingIcon` prop is required when hiding visible labels")}return o.cloneElement(e,r)}))};N.displayName="SegmentedControl";let B=Object.assign(N,{Button:y,IconButton:U})},20917:(e,t,r)=>{r.d(t,{X:()=>p});var o=r(11791),n=r(72774);let a={xsmall:{width:"320px"},small:{width:n.Z.breakpoints[0]},medium:{width:n.Z.breakpoints[1]},large:{width:n.Z.breakpoints[2]},xlarge:{width:n.Z.breakpoints[3]},xxlarge:{width:"1400px"}};function l(e){return`@media screen and (min-width: ${e})`}function i(e){return`@media screen and (max-width: calc(${e} - 0.02px))`}let s={narrow:i(a.medium.width),regular:l(a.medium.width),wide:l(a.xxlarge.width)};function d(e){if("narrow"in e&&"regular"in e&&"wide"in e){let t=Object.values(e);return t.every(e=>e===t[0])}return!1}function c(e){return"regular"in e&&"wide"in e&&e.regular===e.wide}function p(e,t,r){if(!(0,o.fd)(e))return{[t]:r(e)};{let o="narrow"in e?{[s.narrow]:{[t]:r(e.narrow)}}:{},n="regular"in e?{[s.regular]:{[t]:r(e.regular)}}:{},a="wide"in e?{[s.wide]:{[t]:r(e.wide)}}:{};return d(e)?{[t]:r(e.narrow)}:c(e)?{...o,...n}:{...o,...n,...a}}}},73418:(e,t,r)=>{r.d(t,{L:()=>l});/**
 * @license
 * Copyright 2018 Google LLC
 * SPDX-License-Identifier: BSD-3-Clause
 */let o=new Set(["children","localName","ref","style","className"]),n=new WeakMap,a=(e,t,r,o,a)=>{let l=null==a?void 0:a[t];void 0===l||r===o?null==r&&t in HTMLElement.prototype?e.removeAttribute(t):e[t]=r:((e,t,r)=>{let o=n.get(e);void 0===o&&n.set(e,o=new Map);let a=o.get(t);void 0!==r?void 0===a?(o.set(t,a={handleEvent:r}),e.addEventListener(t,a)):a.handleEvent=r:void 0!==a&&(o.delete(t),e.removeEventListener(t,a))})(e,l,r)};function l(e=window.React,t,r,n,l){let i,s,d;void 0===t?({tagName:s,elementClass:d,events:n,displayName:l}=e,i=e.react):(i=e,d=r,s=t);let c=i.Component,p=i.createElement,u=new Set(Object.keys(null!=n?n:{}));let f=class f extends c{constructor(){super(...arguments),this.o=null}t(e){if(null!==this.o)for(let t in this.i)a(this.o,t,this.props[t],e?e[t]:void 0,n)}componentDidMount(){this.t()}componentDidUpdate(e){this.t(e)}render(){let{_$Gl:e,...t}=this.props;this.h!==e&&(this.u=t=>{var r;null!==e&&("function"==typeof(r=e)?r(t):r.current=t),this.o=t,this.h=e}),this.i={};let r={ref:this.u};for(let[e,n]of Object.entries(t))o.has(e)?r["className"===e?"class":e]=n:u.has(e)||e in d.prototype?this.i[e]=n:r[e]=n;return p(s,r)}};f.displayName=null!=l?l:d.name;let m=i.forwardRef((e,t)=>p(f,{...e,_$Gl:t},null==e?void 0:e.children));return m.displayName=f.displayName,m}}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_Avatar_Avatar_js-node_modules_primer_react_lib-esm_-9bd36c-fdbbe01aaf34.js.map