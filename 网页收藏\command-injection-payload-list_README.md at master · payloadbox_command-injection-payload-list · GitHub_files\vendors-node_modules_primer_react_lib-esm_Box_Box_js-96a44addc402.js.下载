"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_Box_Box_js"],{42483:(r,e,o)=>{o.d(e,{Z:()=>d});var t=o(15388),a=o(27999),i=o(15173);let n=t.ZP.div.withConfig({displayName:"Box",componentId:"sc-g0xbh4-0"})(a.Dh,a.$_,a.cp,a.bK,a.GQ,a.eC,a.Oq,a.Cg,a.FK,a.AF,i.Z);var d=n},15173:(r,e,o)=>{o.d(e,{Z:()=>d});var t=o(37947),a=o(9996),i=o.n(a);let n=r=>(0,t.ZP)(r.sx);var d=n},44547:(r,e,o)=>{o.d(e,{By:()=>m,U2:()=>l,jo:()=>c,kB:()=>u,qC:()=>b});var t=o(27418),a=o.n(t),i=function(r,e){var o,t=a()({},r,e);for(var i in r)r[i]&&"object"==typeof e[i]&&a()(t,((o={})[i]=a()(r[i],e[i]),o));return t},n=function(r){var e={};return Object.keys(r).sort(function(r,e){return r.localeCompare(e,void 0,{numeric:!0,sensitivity:"base"})}).forEach(function(o){e[o]=r[o]}),e},d={breakpoints:[40,52,64].map(function(r){return r+"em"})},p=function(r){return"@media screen and (min-width: "+r+")"},s=function(r,e){return l(e,r,r)},l=function(r,e,o,t,a){for(t=0,e=e&&e.split?e.split("."):[e];t<e.length;t++)r=r?r[e[t]]:a;return r===a?o:r},c=function r(e){var o={},t=function(r){var t={},s=!1,c=r.theme&&r.theme.disableStyledSystemCache;for(var u in r)if(e[u]){var m=e[u],b=r[u],h=l(r.theme,m.scale,m.defaults);if("object"==typeof b){if(o.breakpoints=!c&&o.breakpoints||l(r.theme,"breakpoints",d.breakpoints),Array.isArray(b)){o.media=!c&&o.media||[null].concat(o.breakpoints.map(p)),t=i(t,g(o.media,m,h,b,r));continue}null!==b&&(t=i(t,f(o.breakpoints,m,h,b,r)),s=!0);continue}a()(t,m(b,h,r))}return s&&(t=n(t)),t};t.config=e,t.propNames=Object.keys(e),t.cache=o;var s=Object.keys(e).filter(function(r){return"config"!==r});return s.length>1&&s.forEach(function(o){var a;t[o]=r(((a={})[o]=e[o],a))}),t},g=function(r,e,o,t,i){var n={};return t.slice(0,r.length).forEach(function(t,d){var p,s=r[d],l=e(t,o,i);s?a()(n,((p={})[s]=a()({},n[s],l),p)):a()(n,l)}),n},f=function(r,e,o,t,i){var n={};for(var d in t){var s=r[d],l=e(t[d],o,i);if(s){var c,g=p(s);a()(n,((c={})[g]=a()({},n[g],l),c))}else a()(n,l)}return n},u=function(r){var e=r.properties,o=r.property,t=r.scale,a=r.transform,i=void 0===a?s:a,n=r.defaultScale;e=e||[o];var d=function(r,o,t){var a={},n=i(r,o,t);if(null!==n)return e.forEach(function(r){a[r]=n}),a};return d.scale=t,d.defaults=n,d},m=function(r){void 0===r&&(r={});var e={};return Object.keys(r).forEach(function(o){var t=r[o];if(!0===t){e[o]=u({property:o,scale:o});return}if("function"==typeof t){e[o]=t;return}e[o]=u(t)}),c(e)},b=function(){for(var r={},e=arguments.length,o=Array(e),t=0;t<e;t++)o[t]=arguments[t];return o.forEach(function(e){e&&e.config&&a()(r,e.config)}),c(r)}},37947:(r,e,o)=>{function t(){return(t=Object.assign||function(r){for(var e=1;e<arguments.length;e++){var o=arguments[e];for(var t in o)Object.prototype.hasOwnProperty.call(o,t)&&(r[t]=o[t])}return r}).apply(this,arguments)}o.d(e,{ZP:()=>u});var a=function(r,e,o,t,a){for(t=0,e=e&&e.split?e.split("."):[e];t<e.length;t++)r=r?r[e[t]]:a;return r===a?o:r},i=[40,52,64].map(function(r){return r+"em"}),n={space:[0,4,8,16,32,64,128,256,512],fontSizes:[12,14,16,20,24,32,48,64,72]},d={bg:"backgroundColor",m:"margin",mt:"marginTop",mr:"marginRight",mb:"marginBottom",ml:"marginLeft",mx:"marginX",my:"marginY",p:"padding",pt:"paddingTop",pr:"paddingRight",pb:"paddingBottom",pl:"paddingLeft",px:"paddingX",py:"paddingY"},p={marginX:["marginLeft","marginRight"],marginY:["marginTop","marginBottom"],paddingX:["paddingLeft","paddingRight"],paddingY:["paddingTop","paddingBottom"],size:["width","height"]},s={color:"colors",backgroundColor:"colors",borderColor:"colors",margin:"space",marginTop:"space",marginRight:"space",marginBottom:"space",marginLeft:"space",marginX:"space",marginY:"space",padding:"space",paddingTop:"space",paddingRight:"space",paddingBottom:"space",paddingLeft:"space",paddingX:"space",paddingY:"space",top:"space",right:"space",bottom:"space",left:"space",gridGap:"space",gridColumnGap:"space",gridRowGap:"space",gap:"space",columnGap:"space",rowGap:"space",fontFamily:"fonts",fontSize:"fontSizes",fontWeight:"fontWeights",lineHeight:"lineHeights",letterSpacing:"letterSpacings",border:"borders",borderTop:"borders",borderRight:"borders",borderBottom:"borders",borderLeft:"borders",borderWidth:"borderWidths",borderStyle:"borderStyles",borderRadius:"radii",borderTopRightRadius:"radii",borderTopLeftRadius:"radii",borderBottomRightRadius:"radii",borderBottomLeftRadius:"radii",borderTopWidth:"borderWidths",borderTopColor:"colors",borderTopStyle:"borderStyles",borderBottomWidth:"borderWidths",borderBottomColor:"colors",borderBottomStyle:"borderStyles",borderLeftWidth:"borderWidths",borderLeftColor:"colors",borderLeftStyle:"borderStyles",borderRightWidth:"borderWidths",borderRightColor:"colors",borderRightStyle:"borderStyles",outlineColor:"colors",boxShadow:"shadows",textShadow:"shadows",zIndex:"zIndices",width:"sizes",minWidth:"sizes",maxWidth:"sizes",height:"sizes",minHeight:"sizes",maxHeight:"sizes",flexBasis:"sizes",size:"sizes",fill:"colors",stroke:"colors"},l=function(r,e){if("number"!=typeof e||e>=0)return a(r,e,e);var o=Math.abs(e),t=a(r,o,o);return"string"==typeof t?"-"+t:-1*t},c=["margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","top","bottom","left","right"].reduce(function(r,e){var o;return t({},r,((o={})[e]=l,o))},{}),g=function(r){return function(e){var o={},t=[null].concat(a(e,"breakpoints",i).map(function(r){return"@media screen and (min-width: "+r+")"}));for(var n in r){var d="function"==typeof r[n]?r[n](e):r[n];if(null!=d){if(!Array.isArray(d)){o[n]=d;continue}for(var p=0;p<d.slice(0,t.length).length;p++){var s=t[p];if(!s){o[n]=d[p];continue}o[s]=o[s]||{},null!=d[p]&&(o[s][n]=d[p])}}}return o}},f=function r(e){return function(o){void 0===o&&(o={});var i=t({},n,{},o.theme||o),l={},f=g("function"==typeof e?e(i):e)(i);for(var u in f){var m=f[u],b="function"==typeof m?m(i):m;if("variant"===u){var h=r(a(i,b))(i);l=t({},l,{},h);continue}if(b&&"object"==typeof b){l[u]=r(b)(i);continue}var y=a(d,u,u),S=a(s,y),v=a(i,S,a(i,y,{})),R=a(c,y,a)(v,b,b);if(p[y])for(var B=p[y],k=0;k<B.length;k++)l[B[k]]=R;else l[y]=R}return l}};let u=f},27418:r=>{/*
object-assign
(c) Sindre Sorhus
@license MIT
*/var e=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable;function a(r){if(null==r)throw TypeError("Object.assign cannot be called with null or undefined");return Object(r)}function i(){try{if(!Object.assign)return!1;var r=new String("abc");if(r[5]="de","5"===Object.getOwnPropertyNames(r)[0])return!1;for(var e={},o=0;o<10;o++)e["_"+String.fromCharCode(o)]=o;var t=Object.getOwnPropertyNames(e).map(function(r){return e[r]});if("**********"!==t.join(""))return!1;var a={};if("abcdefghijklmnopqrst".split("").forEach(function(r){a[r]=r}),"abcdefghijklmnopqrst"!==Object.keys(Object.assign({},a)).join(""))return!1;return!0}catch(r){return!1}}r.exports=i()?Object.assign:function(r,i){for(var n,d,p=a(r),s=1;s<arguments.length;s++){for(var l in n=Object(arguments[s]))o.call(n,l)&&(p[l]=n[l]);if(e){d=e(n);for(var c=0;c<d.length;c++)t.call(n,d[c])&&(p[d[c]]=n[d[c]])}}return p}},27999:(r,e,o)=>{o.d(e,{Oq:()=>S,Cg:()=>h,jn:()=>A,$_:()=>p,ui:()=>I,compose:()=>t.qC,jf:()=>U,GQ:()=>g,get:()=>t.U2,eC:()=>m,bK:()=>n,kk:()=>F,ih:()=>P,FK:()=>B,AF:()=>j,Dh:()=>L,oB:()=>rU,system:()=>t.By,YK:()=>G,cp:()=>l,bU:()=>O,bf:()=>Y});var t=o(44547),a=function(r){return"number"==typeof r&&!isNaN(r)},i={width:{property:"width",scale:"sizes",transform:function(r,e){return(0,t.U2)(e,r,!a(r)||r>1?r:100*r+"%")}},height:{property:"height",scale:"sizes"},minWidth:{property:"minWidth",scale:"sizes"},minHeight:{property:"minHeight",scale:"sizes"},maxWidth:{property:"maxWidth",scale:"sizes"},maxHeight:{property:"maxHeight",scale:"sizes"},size:{properties:["width","height"],scale:"sizes"},overflow:!0,overflowX:!0,overflowY:!0,display:!0,verticalAlign:!0},n=(0,t.By)(i),d={color:{property:"color",scale:"colors"},backgroundColor:{property:"backgroundColor",scale:"colors"},opacity:!0};d.bg=d.backgroundColor;var p=(0,t.By)(d),s={fontFamily:{property:"fontFamily",scale:"fonts"},fontSize:{property:"fontSize",scale:"fontSizes",defaultScale:[12,14,16,20,24,32,48,64,72]},fontWeight:{property:"fontWeight",scale:"fontWeights"},lineHeight:{property:"lineHeight",scale:"lineHeights"},letterSpacing:{property:"letterSpacing",scale:"letterSpacings"},textAlign:!0,fontStyle:!0},l=(0,t.By)(s),c={alignItems:!0,alignContent:!0,justifyItems:!0,justifyContent:!0,flexWrap:!0,flexDirection:!0,flex:!0,flexGrow:!0,flexShrink:!0,flexBasis:!0,justifySelf:!0,alignSelf:!0,order:!0},g=(0,t.By)(c),f={space:[0,4,8,16,32,64,128,256,512]},u={gridGap:{property:"gridGap",scale:"space",defaultScale:f.space},gridColumnGap:{property:"gridColumnGap",scale:"space",defaultScale:f.space},gridRowGap:{property:"gridRowGap",scale:"space",defaultScale:f.space},gridColumn:!0,gridRow:!0,gridAutoFlow:!0,gridAutoColumns:!0,gridAutoRows:!0,gridTemplateColumns:!0,gridTemplateRows:!0,gridTemplateAreas:!0,gridArea:!0},m=(0,t.By)(u),b={border:{property:"border",scale:"borders"},borderWidth:{property:"borderWidth",scale:"borderWidths"},borderStyle:{property:"borderStyle",scale:"borderStyles"},borderColor:{property:"borderColor",scale:"colors"},borderRadius:{property:"borderRadius",scale:"radii"},borderTop:{property:"borderTop",scale:"borders"},borderTopLeftRadius:{property:"borderTopLeftRadius",scale:"radii"},borderTopRightRadius:{property:"borderTopRightRadius",scale:"radii"},borderRight:{property:"borderRight",scale:"borders"},borderBottom:{property:"borderBottom",scale:"borders"},borderBottomLeftRadius:{property:"borderBottomLeftRadius",scale:"radii"},borderBottomRightRadius:{property:"borderBottomRightRadius",scale:"radii"},borderLeft:{property:"borderLeft",scale:"borders"},borderX:{properties:["borderLeft","borderRight"],scale:"borders"},borderY:{properties:["borderTop","borderBottom"],scale:"borders"}};b.borderTopWidth={property:"borderTopWidth",scale:"borderWidths"},b.borderTopColor={property:"borderTopColor",scale:"colors"},b.borderTopStyle={property:"borderTopStyle",scale:"borderStyles"},b.borderTopLeftRadius={property:"borderTopLeftRadius",scale:"radii"},b.borderTopRightRadius={property:"borderTopRightRadius",scale:"radii"},b.borderBottomWidth={property:"borderBottomWidth",scale:"borderWidths"},b.borderBottomColor={property:"borderBottomColor",scale:"colors"},b.borderBottomStyle={property:"borderBottomStyle",scale:"borderStyles"},b.borderBottomLeftRadius={property:"borderBottomLeftRadius",scale:"radii"},b.borderBottomRightRadius={property:"borderBottomRightRadius",scale:"radii"},b.borderLeftWidth={property:"borderLeftWidth",scale:"borderWidths"},b.borderLeftColor={property:"borderLeftColor",scale:"colors"},b.borderLeftStyle={property:"borderLeftStyle",scale:"borderStyles"},b.borderRightWidth={property:"borderRightWidth",scale:"borderWidths"},b.borderRightColor={property:"borderRightColor",scale:"colors"},b.borderRightStyle={property:"borderRightStyle",scale:"borderStyles"};var h=(0,t.By)(b),y={background:!0,backgroundImage:!0,backgroundSize:!0,backgroundPosition:!0,backgroundRepeat:!0};y.bgImage=y.backgroundImage,y.bgSize=y.backgroundSize,y.bgPosition=y.backgroundPosition,y.bgRepeat=y.backgroundRepeat;var S=(0,t.By)(y),v={space:[0,4,8,16,32,64,128,256,512]},R={position:!0,zIndex:{property:"zIndex",scale:"zIndices"},top:{property:"top",scale:"space",defaultScale:v.space},right:{property:"right",scale:"space",defaultScale:v.space},bottom:{property:"bottom",scale:"space",defaultScale:v.space},left:{property:"left",scale:"space",defaultScale:v.space}},B=(0,t.By)(R),k={space:[0,4,8,16,32,64,128,256,512]},w=function(r){return"number"==typeof r&&!isNaN(r)},C=function(r,e){if(!w(r))return(0,t.U2)(e,r,r);var o=r<0,a=Math.abs(r),i=(0,t.U2)(e,a,a);return w(i)?i*(o?-1:1):o?"-"+i:i},T={};T.margin={margin:{property:"margin",scale:"space",transform:C,defaultScale:k.space},marginTop:{property:"marginTop",scale:"space",transform:C,defaultScale:k.space},marginRight:{property:"marginRight",scale:"space",transform:C,defaultScale:k.space},marginBottom:{property:"marginBottom",scale:"space",transform:C,defaultScale:k.space},marginLeft:{property:"marginLeft",scale:"space",transform:C,defaultScale:k.space},marginX:{properties:["marginLeft","marginRight"],scale:"space",transform:C,defaultScale:k.space},marginY:{properties:["marginTop","marginBottom"],scale:"space",transform:C,defaultScale:k.space}},T.margin.m=T.margin.margin,T.margin.mt=T.margin.marginTop,T.margin.mr=T.margin.marginRight,T.margin.mb=T.margin.marginBottom,T.margin.ml=T.margin.marginLeft,T.margin.mx=T.margin.marginX,T.margin.my=T.margin.marginY,T.padding={padding:{property:"padding",scale:"space",defaultScale:k.space},paddingTop:{property:"paddingTop",scale:"space",defaultScale:k.space},paddingRight:{property:"paddingRight",scale:"space",defaultScale:k.space},paddingBottom:{property:"paddingBottom",scale:"space",defaultScale:k.space},paddingLeft:{property:"paddingLeft",scale:"space",defaultScale:k.space},paddingX:{properties:["paddingLeft","paddingRight"],scale:"space",defaultScale:k.space},paddingY:{properties:["paddingTop","paddingBottom"],scale:"space",defaultScale:k.space}},T.padding.p=T.padding.padding,T.padding.pt=T.padding.paddingTop,T.padding.pr=T.padding.paddingRight,T.padding.pb=T.padding.paddingBottom,T.padding.pl=T.padding.paddingLeft,T.padding.px=T.padding.paddingX,T.padding.py=T.padding.paddingY;var x=(0,t.By)(T.margin),W=(0,t.By)(T.padding),L=(0,t.qC)(x,W),j=(0,t.By)({boxShadow:{property:"boxShadow",scale:"shadows"},textShadow:{property:"textShadow",scale:"shadows"}}),z=o(37947),O=function(r){var e,o,a=r.scale,i=r.prop,n=r.variants,d=void 0===n?{}:n,p=r.key;(o=Object.keys(d).length?function(r,e,o){return(0,z.ZP)((0,t.U2)(e,r,null))(o.theme)}:function(r,e){return(0,t.U2)(e,r,null)}).scale=a||p,o.defaults=d;var s=((e={})[void 0===i?"variant":i]=o,e);return(0,t.jo)(s)},A=O({key:"buttons"}),G=O({key:"textStyles",prop:"textStyle"}),I=O({key:"colorStyles",prop:"colors"}),Y=n.width,H=n.height,P=n.minWidth,X=n.minHeight,F=n.maxWidth,_=n.maxHeight,E=n.size,N=n.verticalAlign,U=n.display,q=n.overflow,Z=n.overflowX,K=n.overflowY,D=p.opacity,M=l.fontSize,Q=l.fontFamily,$=l.fontWeight,V=l.lineHeight,J=l.textAlign,rr=l.fontStyle,re=l.letterSpacing,ro=g.alignItems,rt=g.alignContent,ra=g.justifyItems,ri=g.justifyContent,rn=g.flexWrap,rd=g.flexDirection,rp=g.flex,rs=g.flexGrow,rl=g.flexShrink,rc=g.flexBasis,rg=g.justifySelf,rf=g.alignSelf,ru=g.order,rm=m.gridGap,rb=m.gridColumnGap,rh=m.gridRowGap,ry=m.gridColumn,rS=m.gridRow,rv=m.gridAutoFlow,rR=m.gridAutoColumns,rB=m.gridAutoRows,rk=m.gridTemplateColumns,rw=m.gridTemplateRows,rC=m.gridTemplateAreas,rT=m.gridArea,rx=h.borderWidth,rW=h.borderStyle,rL=h.borderColor,rj=h.borderTop,rz=h.borderRight,rO=h.borderBottom,rA=h.borderLeft,rG=h.borderRadius,rI=S.backgroundImage,rY=S.backgroundSize,rH=S.backgroundPosition,rP=S.backgroundRepeat,rX=B.zIndex,rF=B.top,r_=B.right,rE=B.bottom,rN=B.left,rU=function(r){var e=r.prop,o=r.cssProperty,a=r.alias,i=r.key,n=r.transformValue,d=r.scale,p=r.properties,s={};return s[e]=(0,t.kB)({properties:p,property:o||e,scale:i,defaultScale:d,transform:n}),a&&(s[a]=s[e]),(0,t.jo)(s)}}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_Box_Box_js-67ecc3eeca3d.js.map