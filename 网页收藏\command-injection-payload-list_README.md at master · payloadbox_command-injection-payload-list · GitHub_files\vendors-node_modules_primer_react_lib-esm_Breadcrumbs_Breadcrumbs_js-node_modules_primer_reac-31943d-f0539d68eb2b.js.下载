"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_Breadcrumbs_Breadcrumbs_js-node_modules_primer_reac-31943d"],{98641:(e,t,o)=>{o.d(t,{Z:()=>f});var r=o(86010),a=o(67294),n=o(15388),i=o(42379),l=o(15173),s=o(42483);let c=n.ZP.li.withConfig({displayName:"Breadcrumbs__Wrapper",componentId:"sc-9m4wsf-0"})(["display:inline-block;white-space:nowrap;list-style:none;&::after{padding-right:0.5em;padding-left:0.5em;color:",";font-size:",";content:'/';}&:first-child{margin-left:0;}&:last-child{&::after{content:none;}}"],(0,i.U2)("colors.fg.muted"),(0,i.U2)("fontSizes.1")),d=n.ZP.nav.withConfig({displayName:"Breadcrumbs__BreadcrumbsBase",componentId:"sc-9m4wsf-1"})(["display:flex;justify-content:space-between;",";"],l.Z);function p({className:e,children:t,sx:o}){let r=a.Children.map(t,e=>a.createElement(c,null,e));return a.createElement(d,{className:e,"aria-label":"Breadcrumbs",sx:o},a.createElement(s.Z,{as:"ol",my:0,pl:0},r))}p.displayName="Breadcrumbs";let u=n.ZP.a.attrs(e=>({activeClassName:"string"==typeof e.to?"selected":void 0,className:(0,r.Z)(e.selected&&"selected",e.className),"aria-current":e.selected?"page":null})).withConfig({displayName:"Breadcrumbs__BreadcrumbsItem",componentId:"sc-9m4wsf-2"})(["color:",";display:inline-block;font-size:",";text-decoration:none;&:hover{text-decoration:underline;}&.selected{color:",";pointer-events:none;}",";"],(0,i.U2)("colors.accent.fg"),(0,i.U2)("fontSizes.1"),(0,i.U2)("colors.fg.default"),l.Z);p.displayName="Breadcrumbs",u.displayName="Breadcrumbs.Item";var f=Object.assign(p,{Item:u});Object.assign(p,{Item:u})},99782:(e,t,o)=>{o.d(t,{Z:()=>b});var r=o(15388),a=o(67294),n=o(15173),i=o(69848),l=o(56167),s=o(23383),c=o(42379),d=o(29178),p=o(66044);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e}).apply(this,arguments)}let f=r.ZP.input.withConfig({displayName:"Checkbox__StyledCheckbox",componentId:"sc-1ga3qj3-0"})(["",";border-radius:",";transition:background-color,border-color 80ms cubic-bezier(0.33,1,0.68,1);&::before{width:var(--base-size-16,16px);height:var(--base-size-16,16px);visibility:hidden;content:'';background-color:",";transition:visibility 0s linear 230ms;clip-path:inset(var(--base-size-16,16px) 0 0 0);mask-image:url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMTEuNzgwMyAwLjIxOTYyNUMxMS45MjEgMC4zNjA0MjcgMTIgMC41NTEzMDUgMTIgMC43NTAzMTNDMTIgMC45NDkzMjEgMTEuOTIxIDEuMTQwMTkgMTEuNzgwMyAxLjI4MUw0LjUxODYgOC41NDA0MkM0LjM3Nzc1IDguNjgxIDQuMTg2ODIgOC43NiAzLjk4Nzc0IDguNzZDMy43ODg2NyA4Ljc2IDMuNTk3NzMgOC42ODEgMy40NTY4OSA4LjU0MDQyTDAuMjAxNjIyIDUuMjg2MkMwLjA2ODkyNzcgNS4xNDM4MyAtMC4wMDMzMDkwNSA0Ljk1NTU1IDAuMDAwMTE2NDkzIDQuNzYwOThDMC4wMDM1NTIwNSA0LjU2NjQzIDAuMDgyMzg5NCA0LjM4MDgxIDAuMjIwMDMyIDQuMjQzMjFDMC4zNTc2NjUgNC4xMDU2MiAwLjU0MzM1NSA0LjAyNjgxIDAuNzM3OTcgNC4wMjMzOEMwLjkzMjU4NCA0LjAxOTk0IDEuMTIwOTMgNC4wOTIxNyAxLjI2MzM0IDQuMjI0ODJMMy45ODc3NCA2Ljk0ODM1TDEwLjcxODYgMC4yMTk2MjVDMTAuODU5NSAwLjA3ODk5MjMgMTEuMDUwNCAwIDExLjI0OTUgMEMxMS40NDg1IDAgMTEuNjM5NSAwLjA3ODk5MjMgMTEuNzgwMyAwLjIxOTYyNVoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=');mask-size:75%;mask-repeat:no-repeat;mask-position:center;animation:checkmarkOut 80ms cubic-bezier(0.65,0,0.35,1) forwards;}&:checked,&:indeterminate{background:",";border-color:",";&::before{animation:checkmarkIn 80ms cubic-bezier(0.65,0,0.35,1) forwards 80ms;}}&:checked{transition:background-color,border-color 80ms cubic-bezier(0.32,0,0.67,0) 0ms;&::before{visibility:visible;transition:visibility 0s linear 0s;}&:disabled{cursor:not-allowed;background-color:",";border-color:",";opacity:1;&::before{background-color:",";}}@media (forced-colors:active){background-color:canvastext;border-color:canvastext;}}&:indeterminate{background:",";&::before{mask-image:url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMiIgdmlld0JveD0iMCAwIDEwIDIiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGZpbGwtcnVsZT0iZXZlbm9kZCIgY2xpcC1ydWxlPSJldmVub2RkIiBkPSJNMCAxQzAgMC40NDc3MTUgMC40NDc3MTUgMCAxIDBIOUM5LjU1MjI5IDAgMTAgMC40NDc3MTUgMTAgMUMxMCAxLjU1MjI4IDkuNTUyMjkgMiA5IDJIMUMwLjQ0NzcxNSAyIDAgMS41NTIyOCAwIDFaIiBmaWxsPSJ3aGl0ZSIvPgo8L3N2Zz4K');visibility:visible;}}",";",";@keyframes checkmarkIn{from{clip-path:inset(var(--base-size-16,16px) 0 0 0);}to{clip-path:inset(0 0 0 0);}}@keyframes checkmarkOut{from{clip-path:inset(0 0 0 0);}to{clip-path:inset(var(--base-size-16,16px) 0 0 0);}}"],d.m,(0,c.U2)("radii.1"),(0,c.U2)("colors.fg.onEmphasis"),(0,c.U2)("colors.accent.fg"),(0,c.U2)("colors.accent.fg"),(0,c.U2)("colors.fg.muted"),(0,c.U2)("colors.fg.muted"),(0,c.U2)("colors.fg.onEmphasis"),(0,c.U2)("colors.accent.fg"),(0,s.Z)(),n.Z),m=a.forwardRef(({checked:e,indeterminate:t,disabled:o,onChange:r,sx:n,required:s,validationStatus:c,value:d,...m},b)=>{let g=(0,p.i)(b),h=(0,a.useContext)(l.w),v=e=>{h.onChange&&h.onChange(e),r&&r(e)};return(0,i.Z)(()=>{g.current&&(g.current.indeterminate=t||!1)},[t,e,g]),a.createElement(f,u({type:"checkbox",disabled:o,ref:b||g,checked:!t&&e,"aria-checked":t?"mixed":e?"true":"false",sx:n,required:s,"aria-required":s?"true":"false","aria-invalid":"error"===c?"true":"false",onChange:v,value:d,name:d},m))});m.displayName="Checkbox";var b=m},56167:(e,t,o)=>{o.d(t,{w:()=>a});var r=o(67294);let a=(0,r.createContext)({})},37990:(e,t,o)=>{o.d(t,{N:()=>N,U:()=>M});var r=o(67294),a=o(73935),n=o(15388),i=o(8386),l=o(22114),s=o(42379),c=o(66280),d=o(17840),p=o(42483);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e}).apply(this,arguments)}let f=n.ZP.div.withConfig({displayName:"ConfirmationDialog__StyledConfirmationHeader",componentId:"sc-vurs1e-0"})(["padding:",";display:flex;flex-direction:row;"],(0,s.U2)("space.2")),m=(0,n.ZP)(p.Z).attrs({as:"h1"}).withConfig({displayName:"ConfirmationDialog__StyledTitle",componentId:"sc-vurs1e-1"})(["font-size:",";font-weight:",";padding:6px ",";flex-grow:1;margin:0;"],(0,s.U2)("fontSizes.3"),(0,s.U2)("fontWeights.bold"),(0,s.U2)("space.2")),b=({title:e,onClose:t,dialogLabelId:o})=>{let a=(0,r.useCallback)(()=>{t("close-button")},[t]);return r.createElement(f,null,r.createElement(m,{id:o},e),r.createElement(c.V.CloseButton,{onClose:a}))};b.displayName="ConfirmationHeader";let g=(0,n.ZP)(p.Z).withConfig({displayName:"ConfirmationDialog__StyledConfirmationBody",componentId:"sc-vurs1e-2"})(["font-size:",";padding:0 "," "," ",";color:",";flex-grow:1;"],(0,s.U2)("fontSizes.1"),(0,s.U2)("space.3"),(0,s.U2)("space.3"),(0,s.U2)("space.3"),(0,s.U2)("colors.fg.muted")),h=({children:e})=>r.createElement(g,null,e);h.displayName="ConfirmationBody";let v=(0,n.ZP)(p.Z).withConfig({displayName:"ConfirmationDialog__StyledConfirmationFooter",componentId:"sc-vurs1e-3"})(["display:grid;grid-auto-flow:column;grid-auto-columns:max-content;grid-gap:",";align-items:end;justify-content:end;padding:"," "," ",";"],(0,s.U2)("space.2"),(0,s.U2)("space.1"),(0,s.U2)("space.3"),(0,s.U2)("space.3")),y=({footerButtons:e})=>{let{containerRef:t}=(0,d.v)({bindKeys:l.Qw.ArrowHorizontal|l.Qw.Tab,focusInStrategy:"closest"});return r.createElement(v,{ref:t},r.createElement(c.V.Buttons,{buttons:null!=e?e:[]}))};y.displayName="ConfirmationFooter";let M=e=>{let{onClose:t,title:o,cancelButtonContent:a="Cancel",confirmButtonContent:n="OK",confirmButtonType:i="normal",children:l}=e,s=(0,r.useCallback)(()=>{t("cancel")},[t]),d=(0,r.useCallback)(()=>{t("confirm")},[t]),p="danger"===i,u={content:a,onClick:s,autoFocus:p},f={content:n,buttonType:i,onClick:d,autoFocus:!p},m=[u,f];return r.createElement(c.V,{onClose:t,title:o,footerButtons:m,role:"alertdialog",width:"medium",renderHeader:b,renderBody:h,renderFooter:y},l)};async function w(e,t){let{content:o,...n}=t;return new Promise(t=>{let l=document.createElement("div"),s=e=>{a.unmountComponentAtNode(l),"confirm"===e?t(!0):t(!1)};a.render(r.createElement(i.f6,e,r.createElement(M,u({},n,{onClose:s}),o)),l)})}function N(){let{theme:e,colorMode:t,dayScheme:o,nightScheme:a}=(0,i.Fg)(),n=(0,r.useCallback)(r=>w({theme:e,colorMode:t,dayScheme:o,nightScheme:a},r),[e,t,o,a]);return n}M.displayName="ConfirmationDialog"},98833:(e,t,o)=>{o.d(t,{Z:()=>c});var r=o(67294),a=o(15388),n=o(15173);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e}).apply(this,arguments)}let l=r.forwardRef((e,t)=>{let{icon:o,...a}=e;return r.createElement(o,i({},a,{ref:t}))}),s=(0,a.ZP)(l).withConfig({displayName:"Octicon",componentId:"sc-9kayk9-0"})(["",""],({color:e,sx:t})=>(0,n.Z)({sx:{color:e,...t}}));var c=s},62719:(e,t,o)=>{o.d(t,{Z:()=>h});var r=o(67294),a=o(15388),n=o(27999);let i={top:"Bottom",right:"Left",bottom:"Top",left:"Right"},l={top:"Left",right:"Top",bottom:"Left",left:"Top"};function s(e){let[t,o]=e.split("-");return[t,o]}function c(e,t,o){let r=i[e].toLowerCase(),a=l[e].toLowerCase();return{[r]:"100%",[t||a]:t?o:"50%"}}let d=(0,n.oB)({prop:"bg",key:"colors"}),p=(0,n.oB)({prop:"borderColor",key:"colors"}),u=(0,n.oB)({prop:"borderWidth",key:"borderWidths",scale:[0,1]});function f(e){var t;let o=r.useContext(a.Ni),n={...e,bg:e.bg||"canvas.default",borderColor:e.borderColor||"border.default",borderWidth:e.borderWidth||1,theme:null!==(t=e.theme)&&void 0!==t?t:o},{bg:i}=d(n),{borderColor:f}=p(n),{borderWidth:m}=u(n),{size:b=8,location:g="bottom"}=e,[h,v]=s(g),y=l[h],M=[-b,0],w=[0,b],N=[b,0],C=`M${M}L${w}L${N}L${M}Z`,U=`M${M}L${w}L${N}`,x={top:`translate(${[b,2*b]}) rotate(180)`,right:`translate(${[0,b]}) rotate(-90)`,bottom:`translate(${[b,0]})`,left:`translate(${[2*b,b]}) rotate(90)`}[h];return r.createElement("svg",{width:2*b,height:2*b,style:{pointerEvents:"none",position:"absolute",...c(h,v,b),[`margin${y}`]:v?null:-b},role:"presentation"},r.createElement("g",{transform:x},r.createElement("path",{d:C,fill:null==o?void 0:o.colors.canvas.default}),r.createElement("path",{d:C,fill:i}),r.createElement("path",{d:U,fill:"none",stroke:f,strokeWidth:m})))}f.displayName="Caret",f.locations=["top","top-left","top-right","right","right-top","right-bottom","bottom","bottom-left","bottom-right","left","left-top","left-bottom"];var m=o(42379),b=o(42483);function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e}).apply(this,arguments)}function h(e){let t=r.useContext(a.Ni),{bg:o,border:n,borderColor:i,theme:l,sx:s}=e,{caret:c,children:d,...p}=e,{bg:u,backgroundColor:h,...v}=s||{},y=l||t,M=o||u||h,w={bg:o||(null==s?void 0:s.bg)||(null==s?void 0:s.backgroundColor),borderColor:i||(null==s?void 0:s.borderColor),borderWidth:n,location:c,theme:y};return r.createElement(b.Z,g({},{borderWidth:"1px",borderStyle:"solid",borderColor:"border.default",borderRadius:2},p,{sx:{...v,"--custom-bg":(0,m.U2)(`colors.${M}`)({theme:y}),backgroundImage:M?`linear-gradient(var(--custom-bg), var(--custom-bg)), linear-gradient(${y.colors.canvas.default}, ${y.colors.canvas.default})`:void 0,position:"relative"}}),d,r.createElement(f,w))}h.displayName="PointerBox"},96604:(e,t,o)=>{o.d(t,{Z:()=>c});var r=o(86010),a=o(15388),n=o(42379),i=o(15173);let l=a.ZP.div.attrs(({className:e,caret:t="top"})=>({className:(0,r.Z)(e,`caret-pos--${t}`)})).withConfig({displayName:"Popover",componentId:"sc-q9r75g-0"})(["position:",";z-index:100;display:",";",";"],e=>e.relative?"relative":"absolute",e=>e.open?"block":"none",i.Z),s=a.ZP.div.withConfig({displayName:"Popover__PopoverContent",componentId:"sc-q9r75g-1"})(["border:1px solid ",";border-radius:",";position:relative;width:232px;margin-right:auto;margin-left:auto;padding:",";background-color:",";&::before,&::after{position:absolute;left:50%;display:inline-block;content:'';}&::before{top:-",";margin-left:-9px;border:"," solid transparent;border-bottom-color:",";}&::after{top:-14px;margin-left:-",";border:7px solid transparent;border-bottom-color:",";}",".caret-pos--bottom &,",".caret-pos--bottom-right &,",".caret-pos--bottom-left &{&::before,&::after{top:auto;border-bottom-color:transparent;}&::before{bottom:-",";border-top-color:",";}&::after{bottom:-14px;border-top-color:",";}}",".caret-pos--top-right &,",".caret-pos--bottom-right &{right:-9px;margin-right:0;&::before,&::after{left:auto;margin-left:0;}&::before{right:20px;}&::after{right:21px;}}",".caret-pos--top-left &,",".caret-pos--bottom-left &{left:-9px;margin-left:0;&::before,&::after{left:",";margin-left:0;}&::after{left:calc("," + 1px);}}",".caret-pos--right &,",".caret-pos--right-top &,",".caret-pos--right-bottom &,",".caret-pos--left &,",".caret-pos--left-top &,",".caret-pos--left-bottom &{&::before,&::after{top:50%;left:auto;margin-left:0;border-bottom-color:transparent;}&::before{margin-top:calc(("," + 1px) * -1);}&::after{margin-top:-",";}}",".caret-pos--right &,",".caret-pos--right-top &,",".caret-pos--right-bottom &{&::before{right:-",";border-left-color:",";}&::after{right:-14px;border-left-color:",";}}",".caret-pos--left &,",".caret-pos--left-top &,",".caret-pos--left-bottom &{&::before{left:-",";border-right-color:",";}&::after{left:-14px;border-right-color:",";}}",".caret-pos--right-top &,",".caret-pos--left-top &{&::before,&::after{top:",";}}",".caret-pos--right-bottom &,",".caret-pos--left-bottom &{&::before,&::after{top:auto;}&::before{bottom:",";}&::after{bottom:calc("," + 1px);}}",";"],(0,n.U2)("colors.border.default"),(0,n.U2)("radii.2"),(0,n.U2)("space.4"),(0,n.U2)("colors.canvas.overlay"),(0,n.U2)("space.3"),(0,n.U2)("space.2"),(0,n.U2)("colors.border.default"),(0,n.U2)("space.2"),(0,n.U2)("colors.canvas.overlay"),l,l,l,(0,n.U2)("space.3"),(0,n.U2)("colors.border.default"),(0,n.U2)("colors.canvas.overlay"),l,l,l,l,(0,n.U2)("space.4"),(0,n.U2)("space.4"),l,l,l,l,l,l,(0,n.U2)("space.2"),(0,n.U2)("space.2"),l,l,l,(0,n.U2)("space.3"),(0,n.U2)("colors.border.default"),(0,n.U2)("colors.canvas.overlay"),l,l,l,(0,n.U2)("space.3"),(0,n.U2)("colors.border.default"),(0,n.U2)("colors.canvas.overlay"),l,l,(0,n.U2)("space.4"),l,l,(0,n.U2)("space.3"),(0,n.U2)("space.3"),i.Z);s.displayName="Popover.Content";var c=Object.assign(l,{Content:s})},53226:(e,t,o)=>{o.d(t,{Z:()=>s});var r=o(15388),a=o(42379),n=o(15173),i=o(75381);let l=(0,r.ZP)(i.Z).withConfig({displayName:"Button",componentId:"sc-ybpnzh-0"})(["color:",";background-color:",";border:1px solid ",";box-shadow:",",",";&:hover{background-color:",";border-color:",";}&:focus{outline:solid 2px ",";}&:active{background-color:",";box-shadow:",";}&:disabled{color:",";background-color:",";border-color:",";}",";"],(0,a.U2)("colors.btn.text"),(0,a.U2)("colors.btn.bg"),(0,a.U2)("colors.btn.border"),(0,a.U2)("shadows.btn.shadow"),(0,a.U2)("shadows.btn.insetShadow"),(0,a.U2)("colors.btn.hoverBg"),(0,a.U2)("colors.btn.hoverBorder"),(0,a.U2)("colors.accent.fg"),(0,a.U2)("colors.btn.selectedBg"),(0,a.U2)("shadows.btn.shadowActive"),(0,a.U2)("colors.primer.fg.disabled"),(0,a.U2)("colors.btn.bg"),(0,a.U2)("colors.btn.border"),n.Z);var s=l},75381:(e,t,o)=>{o.d(t,{Z:()=>c});var r=o(15388),a=o(27999),n=o(42379),i=(0,r.iv)(["position:relative;display:inline-block;padding:6px 16px;font-family:inherit;font-weight:",";line-height:20px;white-space:nowrap;vertical-align:middle;cursor:pointer;user-select:none;border-radius:",";appearance:none;text-decoration:none;text-align:center;&:hover{text-decoration:none;}&:focus{outline:none;}&:disabled{cursor:default;}&:disabled svg{opacity:0.6;}"],(0,n.U2)("fontWeights.bold"),(0,n.U2)("radii.2"));let l=(0,a.bU)({variants:{small:{p:"4px 12px",fontSize:0},medium:{fontSize:1},large:{fontSize:2,p:"10px 20px"}}}),s=r.ZP.button.attrs(({disabled:e,onClick:t})=>({onClick:e?void 0:t})).withConfig({displayName:"ButtonBase",componentId:"sc-bqtwic-0"})([""," ",""],i,l);s.defaultProps={variant:"medium"};var c=s},29178:(e,t,o)=>{o.d(t,{m:()=>n});var r=o(15388),a=o(42379);let n=(0,r.iv)(["appearance:none;border-color:",";border-style:solid;border-width:",";cursor:pointer;display:grid;height:var(--base-size-16,16px);margin:0;margin-top:0.125rem;place-content:center;position:relative;width:var(--base-size-16,16px);&:disabled{background-color:var(--color-input-disabled-bg,rgba(175,184,193,0.2));border-color:",";}"],(0,a.U2)("colors.neutral.emphasis"),(0,a.U2)("borderWidths.1"),(0,a.U2)("colors.border.default"))},47142:(e,t,o)=>{o.d(t,{CD:()=>y,DU:()=>a,Gs:()=>h,m7:()=>v});var r=-1/0,a=1/0,n=-.005,i=-.005,l=-.01,s=1,c=.9,d=.8,p=.7,u=.6;function f(e){return e.toLowerCase()===e}function m(e){return e.toUpperCase()===e}function b(e){for(var t=e.length,o=Array(t),r="/",a=0;a<t;a++){var n=e[a];"/"===r?o[a]=c:"-"===r||"_"===r||" "===r?o[a]=d:"."===r?o[a]=u:f(r)&&m(n)?o[a]=p:o[a]=0,r=n}return o}function g(e,t,o,a){for(var c=e.length,d=t.length,p=e.toLowerCase(),u=t.toLowerCase(),f=b(t,f),m=0;m<c;m++){o[m]=Array(d),a[m]=Array(d);for(var g=r,h=m===c-1?i:l,v=0;v<d;v++)if(p[m]===u[v]){var y=r;m?v&&(y=Math.max(a[m-1][v-1]+f[v],o[m-1][v-1]+s)):y=v*n+f[v],o[m][v]=y,a[m][v]=g=Math.max(y,g+h)}else o[m][v]=r,a[m][v]=g+=h}}function h(e,t){var o=e.length,n=t.length;if(!o||!n)return r;if(o===n)return a;if(n>1024)return r;var i=Array(o),l=Array(o);return g(e,t,i,l),l[o-1][n-1]}function v(e,t){var o=e.length,a=t.length,n=Array(o);if(!o||!a)return n;if(o===a){for(var i=0;i<o;i++)n[i]=i;return n}if(a>1024)return n;var l=Array(o),c=Array(o);g(e,t,l,c);for(var d=!1,i=o-1,p=a-1;i>=0;i--)for(;p>=0;p--)if(l[i][p]!==r&&(d||l[i][p]===c[i][p])){d=i&&p&&c[i][p]===l[i-1][p-1]+s,n[i]=p--;break}return n}function y(e,t){e=e.toLowerCase(),t=t.toLowerCase();for(var o=e.length,r=0,a=0;r<o;r+=1)if(0===(a=t.indexOf(e[r],a)+1))return!1;return!0}},12464:(e,t,o)=>{o.d(t,{Nq:()=>p,oD:()=>f,t0:()=>u});var r=o(67294),a=o(73935),n=function(){var e=function(t,o){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)t.hasOwnProperty(o)&&(e[o]=t[o])})(t,o)};return function(t,o){function r(){this.constructor=t}e(t,o),t.prototype=null===o?Object.create(o):(r.prototype=o.prototype,new r)}}(),i="html",l="svg",s="http://www.w3.org/2000/svg",c=function(e,t){if(t===i)return e instanceof HTMLElement;if(t===l)return e instanceof SVGElement;throw Error('Unrecognized element type "'+t+'" for validateElementType.')},d=function(e,t){var o,r,a,n={};if(e===i)a=document.createElement("div");else if(e===l)a=document.createElementNS(s,"g");else throw Error('Invalid element type "'+e+'" for createPortalNode: must be "html" or "svg".');if(t&&"object"==typeof t)for(var d=0,p=Object.entries(t.attributes);d<p.length;d++){var u=p[d],f=u[0],m=u[1];a.setAttribute(f,m)}var b={element:a,elementType:e,setPortalProps:function(e){n=e},getInitialPortalProps:function(){return n},mount:function(t,a){if(a!==r){if(b.unmount(),t!==o&&!c(t,e))throw Error('Invalid element type for portal: "'+e+'" portalNodes must be used with '+e+" elements, but OutPortal is within <"+t.tagName+">.");t.replaceChild(b.element,a),o=t,r=a}},unmount:function(e){(!e||e===r)&&o&&r&&(o.replaceChild(r,b.element),o=void 0,r=void 0)}};return b},p=function(e){function t(t){var o=e.call(this,t)||this;return o.addPropsChannel=function(){Object.assign(o.props.node,{setPortalProps:function(e){o.setState({nodeProps:e})}})},o.state={nodeProps:o.props.node.getInitialPortalProps()},o}return n(t,e),t.prototype.componentDidMount=function(){this.addPropsChannel()},t.prototype.componentDidUpdate=function(){this.addPropsChannel()},t.prototype.render=function(){var e=this,t=this.props,o=t.children,n=t.node;return a.createPortal(r.Children.map(o,function(t){return r.isValidElement(t)?r.cloneElement(t,e.state.nodeProps):t}),n.element)},t}(r.PureComponent),u=function(e){function t(t){var o=e.call(this,t)||this;return o.placeholderNode=r.createRef(),o.passPropsThroughPortal(),o}return n(t,e),t.prototype.passPropsThroughPortal=function(){var e=Object.assign({},this.props,{node:void 0});this.props.node.setPortalProps(e)},t.prototype.componentDidMount=function(){var e=this.props.node;this.currentPortalNode=e;var t=this.placeholderNode.current,o=t.parentNode;e.mount(o,t),this.passPropsThroughPortal()},t.prototype.componentDidUpdate=function(){var e=this.props.node;this.currentPortalNode&&e!==this.currentPortalNode&&(this.currentPortalNode.unmount(this.placeholderNode.current),this.currentPortalNode.setPortalProps({}),this.currentPortalNode=e);var t=this.placeholderNode.current,o=t.parentNode;e.mount(o,t),this.passPropsThroughPortal()},t.prototype.componentWillUnmount=function(){var e=this.props.node;e.unmount(this.placeholderNode.current),e.setPortalProps({})},t.prototype.render=function(){return r.createElement("div",{ref:this.placeholderNode})},t}(r.PureComponent),f=d.bind(null,i),m=d.bind(null,l)}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_Breadcrumbs_Breadcrumbs_js-node_modules_primer_reac-31943d-7309cf0b4950.js.map