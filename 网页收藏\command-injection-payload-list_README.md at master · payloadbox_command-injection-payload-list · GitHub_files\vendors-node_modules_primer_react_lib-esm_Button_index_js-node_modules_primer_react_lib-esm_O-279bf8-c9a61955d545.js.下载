"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_Button_index_js-node_modules_primer_react_lib-esm_O-279bf8"],{48030:(t,e,n)=>{n.d(e,{N:()=>r});let o={"outside-top":["outside-bottom","outside-right","outside-left","outside-bottom"],"outside-bottom":["outside-top","outside-right","outside-left","outside-bottom"],"outside-left":["outside-right","outside-bottom","outside-top","outside-bottom"],"outside-right":["outside-left","outside-bottom","outside-top","outside-bottom"]},i={start:["end","center"],end:["start","center"],center:["end","start"]};function r(t,e,n={}){let o=l(t),i=u(o),r=getComputedStyle(o),a=o.getBoundingClientRect(),[d,f]=[r.borderTopWidth,r.borderLeftWidth].map(t=>parseInt(t,10)||0),h={top:a.top+d,left:a.left+f};return c(i,h,t.getBoundingClientRect(),e instanceof Element?e.getBoundingClientRect():e,s(n))}function l(t){if(a(t))return document.body;let e=t.parentNode;for(;null!==e;){if(e instanceof HTMLElement&&"static"!==getComputedStyle(e).position)return e;e=e.parentNode}return document.body}function a(t){var e;if("DIALOG"===t.tagName)return!0;try{if(t.matches(":popover-open")&&/native code/.test(null===(e=document.body.showPopover)||void 0===e?void 0:e.toString()))return!0}catch(t){}return!1}function u(t){let e=t;for(;null!==e&&e!==document.body;){let t=getComputedStyle(e);if("visible"!==t.overflow)break;e=e.parentNode}let n=e!==document.body&&e instanceof HTMLElement?e:document.body,o=n.getBoundingClientRect(),i=getComputedStyle(n),[r,l,a,u]=[i.borderTopWidth,i.borderLeftWidth,i.borderRightWidth,i.borderBottomWidth].map(t=>parseInt(t,10)||0);return{top:o.top+r,left:o.left+l,width:o.width-a-l,height:Math.max(o.height-r-u,n===document.body?window.innerHeight:-1/0)}}let d={side:"outside-bottom",align:"start",anchorOffset:4,alignmentOffset:4,allowOutOfBounds:!1};function s(t={}){var e,n,o,i,r;let l=null!==(e=t.side)&&void 0!==e?e:d.side,a=null!==(n=t.align)&&void 0!==n?n:d.align;return{side:l,align:a,anchorOffset:null!==(o=t.anchorOffset)&&void 0!==o?o:"inside-center"===l?0:d.anchorOffset,alignmentOffset:null!==(i=t.alignmentOffset)&&void 0!==i?i:"center"!==a&&l.startsWith("inside")?d.alignmentOffset:0,allowOutOfBounds:null!==(r=t.allowOutOfBounds)&&void 0!==r?r:d.allowOutOfBounds}}function c(t,e,n,r,{side:l,align:a,allowOutOfBounds:u,anchorOffset:d,alignmentOffset:s}){let c={top:t.top-e.top,left:t.left-e.left,width:t.width,height:t.height},m=f(n,r,l,a,d,s),v=l,g=a;if(m.top-=e.top,m.left-=e.left,!u){let u=o[l],b=0;if(u){let t=l;for(;b<u.length&&h(t,m,c,n);){let o=u[b++];t=o,m=f(n,r,o,a,d,s),m.top-=e.top,m.left-=e.left,v=o}}let y=i[a],w=0;if(y){let t=a;for(;w<y.length&&p(t,m,c,n);){let o=y[w++];t=o,m=f(n,r,v,o,d,s),m.top-=e.top,m.left-=e.left,g=o}}m.top<c.top&&(m.top=c.top),m.left<c.left&&(m.left=c.left),m.left+n.width>t.width+c.left&&(m.left=t.width+c.left-n.width),u&&b<u.length&&m.top+n.height>t.height+c.top&&(m.top=t.height+c.top-n.height)}return Object.assign(Object.assign({},m),{anchorSide:v,anchorAlign:g})}function f(t,e,n,o,i,r){let l=e.left+e.width,a=e.top+e.height,u=-1,d=-1;return"outside-top"===n?u=e.top-i-t.height:"outside-bottom"===n?u=a+i:"outside-left"===n?d=e.left-i-t.width:"outside-right"===n&&(d=l+i),("outside-top"===n||"outside-bottom"===n)&&(d="start"===o?e.left+r:"center"===o?e.left-(t.width-e.width)/2+r:l-t.width-r),("outside-left"===n||"outside-right"===n)&&(u="start"===o?e.top+r:"center"===o?e.top-(t.height-e.height)/2+r:a-t.height-r),"inside-top"===n?u=e.top+i:"inside-bottom"===n?u=a-i-t.height:"inside-left"===n?d=e.left+i:"inside-right"===n?d=l-i-t.width:"inside-center"===n&&(d=(l+e.left)/2-t.width/2+i),"inside-top"===n||"inside-bottom"===n?d="start"===o?e.left+r:"center"===o?e.left-(t.width-e.width)/2+r:l-t.width-r:("inside-left"===n||"inside-right"===n||"inside-center"===n)&&(u="start"===o?e.top+r:"center"===o?e.top-(t.height-e.height)/2+r:a-t.height-r),{top:u,left:d}}function h(t,e,n,o){return"outside-top"===t||"outside-bottom"===t?e.top<n.top||e.top+o.height>n.height+n.top:e.left<n.left||e.left+o.width>n.width+n.left}function p(t,e,n,o){return"end"===t?e.left<n.left:"start"===t||"center"===t?e.left+o.width>n.left+n.width||e.left<n.left:void 0}},78912:(t,e,n)=>{n.d(e,{z:()=>d});var o=n(88216),i=n(67294),r=n(7261),l=n(54901);function a(){return(a=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}let u=({children:t,sx:e=r.P,...n})=>{let o={'&[data-component="ButtonCounter"]':e};return i.createElement(l.Z,a({"data-component":"ButtonCounter",sx:{ml:2,...o}},n),t)};u.displayName="Counter";let d=Object.assign(o.r,{Counter:u})},54901:(t,e,n)=>{n.d(e,{Z:()=>c});var o=n(67294),i=n(53670),r=n(7261),l=n(42483),a=n(9996),u=n.n(a);function d(){return(d=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}let s=(0,o.forwardRef)(({scheme:t="secondary",sx:e=r.P,children:n,...a},s)=>o.createElement(o.Fragment,null,o.createElement(l.Z,d({"aria-hidden":"true",sx:u()({display:"inline-block",padding:"2px 5px",fontSize:0,fontWeight:"bold",lineHeight:"condensedUltra",borderRadius:"20px",backgroundColor:"primary"===t?"neutral.emphasis":"neutral.muted",color:"primary"===t?"fg.onEmphasis":"fg.default","&:empty":{display:"none"}},e)},a,{as:"span",ref:s}),n),o.createElement(i.Z,null,"\xa0(",n,")")));s.displayName="CounterLabel";var c=s},8677:(t,e,n)=>{n.d(e,{Z:()=>b});var o=n(15388),i=n(67294),r=n(69848),l=n(42379),a=n(41905),u=n(15173),d=n(31171),s=n(8386),c=n(16903);function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t}).apply(this,arguments)}let h={xsmall:"192px",small:"256px",medium:"320px",large:"432px",xlarge:"600px",auto:"auto",initial:"auto"},p={small:"256px",medium:"320px",large:"480px",xlarge:"640px",xxlarge:"960px",auto:"auto"};function m(t){return null!=t&&t.endsWith("bottom")?{x:0,y:-1}:null!=t&&t.endsWith("top")?{x:0,y:1}:null!=t&&t.endsWith("right")?{x:-1,y:0}:null!=t&&t.endsWith("left")?{x:1,y:0}:{x:0,y:0}}let v=o.ZP.div.withConfig({displayName:"Overlay__StyledOverlay",componentId:"sc-51280t-0"})(["background-color:",";box-shadow:",";position:absolute;min-width:192px;max-width:",";height:",";max-height:",";width:",";border-radius:12px;overflow:hidden;animation:overlay-appear ","ms ",";@keyframes overlay-appear{0%{opacity:0;}100%{opacity:1;}}visibility:var(--styled-overlay-visibility);:focus{outline:none;}@media (forced-colors:active){outline:solid 1px transparent;}",";"],(0,l.U2)("colors.canvas.overlay"),(0,l.U2)("shadows.overlay.shadow"),t=>t.maxWidth&&p[t.maxWidth],t=>h[t.height||"auto"],t=>t.maxHeight&&h[t.maxHeight],t=>p[t.width||"auto"],200,(0,l.U2)("animation.easeOutCubic"),u.Z),g=i.forwardRef(({onClickOutside:t,role:e="none",initialFocusRef:n,returnFocusRef:o,ignoreClickRefs:u,onEscape:h,visibility:p="visible",height:g="auto",width:b="auto",top:y,left:w,right:x,bottom:O,anchorSide:E,portalContainerName:C,preventFocusOnOpen:k,position:R,...j},W)=>{let _=(0,i.useRef)(null);(0,d.z)(W,_);let{theme:B}=(0,s.Fg)(),L=parseInt((0,l.U2)("space.2")(B).replace("px","")),P=(0,l.U2)("animation.easeOutCubic")(B);(0,c.I)({overlayRef:_,returnFocusRef:o,onEscape:h,ignoreClickRefs:u,onClickOutside:t,initialFocusRef:n,preventFocusOnOpen:k}),(0,i.useEffect)(()=>{var t;"initial"===g&&null!==(t=_.current)&&void 0!==t&&t.clientHeight&&(_.current.style.height=`${_.current.clientHeight}px`)},[g]),(0,r.Z)(()=>{var t;let{x:e,y:n}=m(E);(e||n)&&null!==(t=_.current)&&void 0!==t&&t.animate&&"hidden"!==p&&_.current.animate({transform:[`translate(${L*e}px, ${L*n}px)`,"translate(0, 0)"]},{duration:200,easing:P})},[E,L,P,p]);let Z=void 0===w&&void 0===x?{left:0}:{left:w};return i.createElement(a.h,{containerName:C},i.createElement(v,f({height:g,width:b,role:e},j,{ref:_,style:{...Z,right:x,top:y,bottom:O,position:R,"--styled-overlay-visibility":p}})))});var b=g},48158:(t,e,n)=>{n.d(e,{a:()=>u});var o=n(67294),i=n(48030),r=n(66044),l=n(8930),a=n(69848);function u(t,e=[]){let n=(0,r.i)(null==t?void 0:t.floatingElementRef),u=(0,r.i)(null==t?void 0:t.anchorElementRef),[d,s]=o.useState(void 0),c=o.useCallback(()=>{n.current instanceof Element&&u.current instanceof Element?s((0,i.N)(n.current,u.current,t)):s(void 0)},[n,u,...e]);return(0,a.Z)(c,[c]),(0,l.y)(c),{floatingElementRef:n,anchorElementRef:u,position:d}}},17840:(t,e,n)=>{n.d(e,{v:()=>l});var o=n(67294),i=n(22114),r=n(66044);function l(t={},e=[]){let n=(0,r.i)(t.containerRef),l=!!t.activeDescendantFocus,a="boolean"!=typeof t.activeDescendantFocus&&t.activeDescendantFocus?t.activeDescendantFocus:void 0,u=(0,r.i)(a),d=t.disabled,s=o.useRef();return(0,o.useEffect)(()=>{if(n.current instanceof HTMLElement&&(!l||u.current instanceof HTMLElement)){var e,o;if(d)null===(o=s.current)||void 0===o||o.abort();else{let o={...t,activeDescendantControl:null!==(e=u.current)&&void 0!==e?e:void 0};return s.current=(0,i.km)(n.current,o),()=>{var t;null===(t=s.current)||void 0===t||t.abort()}}}},[d,...e]),{containerRef:n,activeDescendantControlRef:u}}},35048:(t,e,n)=>{n.d(e,{o:()=>d});var o=n(67294);function i(t){if(!t.defaultPrevented){for(let e of Object.values(r).reverse())if(e(t),t.defaultPrevented)break}}let r={};function l(t,e){r[t]=e}function a(t){delete r[t]}let u=0,d=(t,e=[t])=>{let n=(0,o.useCallback)(t,e),d=(0,o.useCallback)(t=>{"Escape"===t.key&&n(t)},[n]),s=(0,o.useMemo)(()=>u++,[]);(0,o.useEffect)(()=>(0===Object.keys(r).length&&document.addEventListener("keydown",i),l(s,d),()=>{a(s),0===Object.keys(r).length&&document.removeEventListener("keydown",i)}),[s,d])}},24178:(t,e,n)=>{n.d(e,{t:()=>d});var o=n(67294);function i(t){if(!t.defaultPrevented){for(let e of Object.values(r).reverse())if(!0===e(t)||t.defaultPrevented)break}}let r={};function l(t,e){r[t]=e}function a(t){delete r[t]}let u=0,d=({containerRef:t,ignoreClickRefs:e,onClickOutside:n})=>{let d=(0,o.useMemo)(()=>u++,[]),s=(0,o.useCallback)(o=>{var i;if(o instanceof MouseEvent&&o.button>0||null!==(i=t.current)&&void 0!==i&&i.contains(o.target)||e&&e.some(({current:t})=>null==t?void 0:t.contains(o.target)))return!0;n(o)},[t,e,n]);(0,o.useEffect)(()=>(0===Object.keys(r).length&&document.addEventListener("mousedown",i,{capture:!0}),l(d,s),()=>{a(d),0===Object.keys(r).length&&document.removeEventListener("mousedown",i,{capture:!0})}),[d,s])}},16903:(t,e,n)=>{n.d(e,{I:()=>d});var o=n(24178),i=n(67294),r=n(78160);function l({initialFocusRef:t,returnFocusRef:e,containerRef:n,preventFocusOnOpen:o}){(0,i.useEffect)(()=>{if(o)return;let i=e.current;if(t&&t.current)t.current.focus();else if(n.current){let t=(0,r.hT)(n.current).next().value;null==t||t.focus()}return function(){null==i||i.focus()}},[t,e,n,o])}var a=n(35048),u=n(66044);let d=({overlayRef:t,returnFocusRef:e,initialFocusRef:n,onEscape:i,ignoreClickRefs:r,onClickOutside:d,preventFocusOnOpen:s})=>{let c=(0,u.i)(t);l({containerRef:c,returnFocusRef:e,initialFocusRef:n,preventFocusOnOpen:s}),(0,o.t)({containerRef:c,ignoreClickRefs:r,onClickOutside:d});let f=t=>{i(t),t.preventDefault()};return(0,a.o)(f),{ref:c}}},66044:(t,e,n)=>{n.d(e,{i:()=>i});var o=n(67294);function i(t){let e=o.useRef(null);return null!=t?t:e}},55744:(t,e,n)=>{n.d(e,{d:()=>i});var o=n(67294);function i(t){let[e,n]=(0,o.useState)(t||null),i=(0,o.useRef)(null);i.current=e;let r=(0,o.useCallback)(t=>{i.current=t,n(t)},[i]);return[i,r]}},8930:(t,e,n)=>{n.d(e,{y:()=>r});var o=n(67294),i=n(69848);function r(t,e){let n=(0,o.useRef)(t);(0,i.Z)(()=>{n.current=t}),(0,i.Z)(()=>{let t=e&&"current"in e?e.current:document.documentElement;if(!t)return;let o=new ResizeObserver(t=>{n.current(t)});return o.observe(t),()=>{o.disconnect()}},[e])}}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_Button_index_js-node_modules_primer_react_lib-esm_O-279bf8-4565f430fe2f.js.map