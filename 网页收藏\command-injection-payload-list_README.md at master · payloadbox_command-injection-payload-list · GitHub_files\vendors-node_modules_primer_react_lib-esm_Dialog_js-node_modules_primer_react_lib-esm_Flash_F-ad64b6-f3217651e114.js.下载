"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_Dialog_js-node_modules_primer_react_lib-esm_Flash_F-ad64b6"],{12470:(e,r,t)=>{t.d(r,{Z:()=>_});var n=t(67294),o=t(15388),a=t(89283),i=t(42379),l=t(15173);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}let c=o.ZP.button.withConfig({displayName:"ButtonClose__StyledButton",componentId:"sc-azdk6r-0"})(["border:none;padding:0;background:transparent;cursor:pointer;border-radius:",";color:",";&:focus{outline:solid 2px ",";}&:hover{color:",";}",";"],(0,i.U2)("radii.2"),(0,i.U2)("colors.fg.muted"),(0,i.U2)("colors.accent.fg"),(0,i.U2)("colors.accent.fg"),l.Z),u=(0,n.forwardRef)((e,r)=>n.createElement(c,s({ref:r,"aria-label":"Close"},e),n.createElement(a.b0D,null)));var d=u,f=t(35048);let p=()=>null;function b(e){return!e.hidden&&(!e.type||"hidden"!==e.type)&&(e.offsetWidth>0||e.offsetHeight>0)}function g(e){return e.tabIndex>=0&&!e.disabled&&b(e)}function v({modalRef:e,overlayRef:r,isOpen:t,onDismiss:o=p,initialFocusRef:a,closeButtonRef:i}){let l=(0,n.useCallback)(t=>{e.current&&r.current&&t.target instanceof Node&&!e.current.contains(t.target)&&r.current.contains(t.target)&&o()},[o,e,r]);(0,n.useEffect)(()=>{if(t)return document.addEventListener("click",l),()=>{document.removeEventListener("click",l)}},[t,l]),(0,n.useEffect)(()=>{t&&(a&&a.current?a.current.focus():i&&i.current&&i.current.focus())},[t,a,i]);let s=(0,n.useCallback)((r,t)=>{if(e.current){let n=Array.from(e.current.querySelectorAll("*")).filter(g);if(0===n.length)return;r.preventDefault();let o=document.activeElement;if(!o)return;let a=n.indexOf(o),i=1===t?0:n.length-1,l=n[a+t]||n[i];return l}},[e]),c=(0,n.useCallback)(e=>{let r=e.shiftKey?-1:1,t=s(e,r);t&&t.focus()},[s]),u=(0,n.useCallback)(e=>{"Tab"===e.key&&c(e)},[c]),d=()=>({onKeyDown:u});return(0,f.o)(e=>{t&&(o(),e.preventDefault())},[t,o]),{getDialogProps:d}}var m=t(31171),h=t(42483),y=t(97011);function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}let w=()=>null,C=o.ZP.div.withConfig({displayName:"Dialog__DialogBase",componentId:"sc-13rdxb7-0"})(["box-shadow:",";border-radius:",";position:fixed;top:0;left:50%;transform:translateX(-50%);max-height:80vh;z-index:999;margin:10vh auto;background-color:",";width:",";outline:none;@media screen and (max-width:750px){width:100vw;margin:0;border-radius:0;height:100vh;}",";"],(0,i.U2)("shadows.shadow.large"),(0,i.U2)("radii.2"),(0,i.U2)("colors.canvas.default"),e=>e.narrow?"320px":e.wide?"640px":"440px",l.Z),k=(0,o.ZP)(h.Z).withConfig({displayName:"Dialog__DialogHeaderBase",componentId:"sc-13rdxb7-1"})(["border-radius:"," "," 0px 0px;border-bottom:1px solid ",";display:flex;@media screen and (max-width:750px){border-radius:0px;}",";"],(0,i.U2)("radii.2"),(0,i.U2)("radii.2"),(0,i.U2)("colors.border.default"),l.Z);function O({theme:e,children:r,backgroundColor:t="canvas.subtle",...o}){return n.Children.toArray(r).every(e=>"string"==typeof e)&&(r=n.createElement(y.Z,{theme:e,color:"fg.default",fontSize:1,fontWeight:"bold",fontFamily:"sans-serif"},r)),n.createElement(k,x({theme:e,p:3,backgroundColor:t},o),r)}O.displayName="DialogHeader";let E=o.ZP.span.withConfig({displayName:"Dialog__Overlay",componentId:"sc-13rdxb7-2"})(["&:before{position:fixed;top:0;right:0;bottom:0;left:0;display:block;cursor:default;content:' ';background:transparent;z-index:99;background:",";}"],(0,i.U2)("colors.primer.canvas.backdrop")),z=(0,n.forwardRef)(({children:e,onDismiss:r=w,isOpen:t,initialFocusRef:o,returnFocusRef:a,...i},l)=>{let s=(0,n.useRef)(null),c=(0,n.useRef)(null);(0,m.z)(l,c);let u=(0,n.useRef)(null),f=()=>{r(),a&&a.current&&a.current.focus()},{getDialogProps:p}=v({modalRef:c,onDismiss:f,isOpen:t,initialFocusRef:o,closeButtonRef:u,returnFocusRef:a,overlayRef:s});return t?n.createElement(n.Fragment,null,n.createElement(E,{ref:s}),n.createElement(C,x({tabIndex:-1,ref:c,role:"dialog","aria-modal":"true"},i,p()),n.createElement(d,{ref:u,onClick:f,sx:{position:"absolute",top:"16px",right:"16px"}}),e)):null});O.propTypes={...h.Z.propTypes},O.displayName="Dialog.Header",z.displayName="Dialog";var _=Object.assign(z,{Header:O})},51461:(e,r,t)=>{t.d(r,{Z:()=>f});var n=t(67294),o=t(15388),a=t(27999),i=t(42379),l=t(15173);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}let c=(0,a.bU)({variants:{default:{color:"fg.default",backgroundColor:"accent.subtle",borderColor:"accent.muted",svg:{color:"accent.fg"}},success:{color:"fg.default",backgroundColor:"success.subtle",borderColor:"success.muted",svg:{color:"success.fg"}},danger:{color:"fg.default",backgroundColor:"danger.subtle",borderColor:"danger.muted",svg:{color:"danger.fg"}},warning:{color:"fg.default",backgroundColor:"attention.subtle",borderColor:"attention.muted",svg:{color:"attention.fg"}}}}),u=o.ZP.div.withConfig({displayName:"Flash__StyledFlash",componentId:"sc-hzrzfc-0"})(["position:relative;color:",";padding:",";border-style:solid;border-width:",";border-radius:",";margin-top:",";p:last-child{margin-bottom:0;}svg{margin-right:",";}",";",";"],(0,i.U2)("colors.fg.default"),(0,i.U2)("space.3"),e=>e.full?"1px 0px":"1px",e=>e.full?"0":(0,i.U2)("radii.2"),e=>e.full?"-1px":"0",(0,i.U2)("space.2"),c,l.Z),d=n.forwardRef(function({as:e,variant:r="default",...t},o){return n.createElement(u,s({ref:o,as:e,variant:r},t))});var f=d},63309:(e,r,t)=>{t.d(r,{Z:()=>p});var n=t(67294),o=t(15388),a=t(27999),i=t(42379),l=t(15173);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}let c={default:{borderColor:"border.default"},primary:{borderColor:"fg.default"},secondary:{borderColor:"border.muted",color:"fg.muted"},accent:{borderColor:"accent.emphasis",color:"accent.fg"},success:{borderColor:"success.emphasis",color:"success.fg"},attention:{borderColor:"attention.emphasis",color:"attention.fg"},severe:{borderColor:"severe.emphasis",color:"severe.fg"},danger:{borderColor:"danger.emphasis",color:"danger.fg"},done:{borderColor:"done.emphasis",color:"done.fg"},sponsors:{borderColor:"sponsors.emphasis",color:"sponsors.fg"}},u={small:{height:"20px",padding:"0 7px"},large:{height:"24px",padding:"0 10px"}},d=o.ZP.span.withConfig({displayName:"Label__StyledLabel",componentId:"sc-1dgcne-0"})(["align-items:center;background-color:transparent;border-width:1px;border-radius:999px;border-style:solid;display:inline-flex;font-weight:",";font-size:",";line-height:1;white-space:nowrap;",";",";",";"],(0,i.U2)("fontWeights.bold"),(0,i.U2)("fontSizes.0"),(0,a.bU)({variants:c}),(0,a.bU)({prop:"size",variants:u}),l.Z),f=n.forwardRef(function({as:e,size:r="small",variant:t="default",...o},a){return n.createElement(d,s({as:e,size:r,variant:t,ref:a},o))});var p=f},31147:(e,r,t)=>{t.d(r,{Z:()=>m});var n=t(86010),o=t(67294),a=t(15388),i=t(42379),l=t(17840),s=t(15173),c=t(23383),u=t(22114);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}let f=a.ZP.div.withConfig({displayName:"TabNav__TabNavBase",componentId:"sc-pwdi4r-0"})(["",""],s.Z),p=a.ZP.div.withConfig({displayName:"TabNav__TabNavTabList",componentId:"sc-pwdi4r-1"})(["display:flex;margin-bottom:-1px;overflow:auto;"]),b=a.ZP.nav.withConfig({displayName:"TabNav__TabNavNav",componentId:"sc-pwdi4r-2"})(["margin-top:0;border-bottom:1px solid ",";"],(0,i.U2)("colors.border.default"));function g({children:e,"aria-label":r,...t}){let n=(0,o.useRef)(null),[a,i]=(0,o.useState)(!1);o.useEffect(()=>{if(n.current){let e=n.current.closest("[role=menu]");e&&i(!0)}},[n]);let s=o.useCallback(()=>{var e,r,t;let o=null===(e=n.current)||void 0===e?void 0:e.querySelector("[role=tab][aria-selected=true]"),a=null===(r=n.current)||void 0===r?void 0:r.querySelector("[role=tab]");return null!==(t=null!=o?o:a)&&void 0!==t?t:void 0},[n]),{containerRef:c}=(0,l.v)({containerRef:n,bindKeys:u.Qw.ArrowHorizontal|u.Qw.HomeAndEnd,focusOutBehavior:"wrap",focusInStrategy:a?"previous":s,focusableElementFilter:e=>"tab"===e.getAttribute("role")},[a]);return o.createElement(f,d({},t,{ref:c}),o.createElement(b,{"aria-label":r},o.createElement(p,{role:"tablist"},e)))}g.displayName="TabNav";let v=a.ZP.a.attrs(e=>({activeClassName:"string"==typeof e.to?"selected":void 0,className:(0,n.Z)("TabNav-item",e.selected&&"selected",e.className),role:"tab","aria-selected":!!e.selected,tabIndex:-1})).withConfig({displayName:"TabNav__TabNavLink",componentId:"sc-pwdi4r-3"})(["padding:8px 12px;font-size:",";line-height:20px;color:",";text-decoration:none;background-color:transparent;border:1px solid transparent;border-bottom:0;",";&:hover,&:focus{color:",";text-decoration:none;}&.selected{color:",";border-color:",";border-top-right-radius:",";border-top-left-radius:",";background-color:",";}",";"],(0,i.U2)("fontSizes.1"),(0,i.U2)("colors.fg.default"),(0,c.Z)("-6px"),(0,i.U2)("colors.fg.default"),(0,i.U2)("colors.fg.default"),(0,i.U2)("colors.border.default"),(0,i.U2)("radii.2"),(0,i.U2)("radii.2"),(0,i.U2)("colors.canvas.default"),s.Z);v.displayName="TabNav.Link";var m=Object.assign(g,{Link:v})},78720:(e,r,t)=>{t.d(r,{M:()=>h,o:()=>y});var n,o=t(67294);function a(){return(a=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e}).apply(this,arguments)}function i(e,r){if(null==e)return{};var t,n,o={},a=Object.keys(e);for(n=0;n<a.length;n++)t=a[n],r.indexOf(t)>=0||(o[t]=e[t]);return o}var l=["bottom","height","left","right","top","width"],s=function(e,r){return void 0===e&&(e={}),void 0===r&&(r={}),l.some(function(t){return e[t]!==r[t]})},c=new Map,u=function e(){var r=[];c.forEach(function(e,t){var n=t.getBoundingClientRect();s(n,e.rect)&&(e.rect=n,r.push(e))}),r.forEach(function(e){e.callbacks.forEach(function(r){return r(e.rect)})}),n=window.requestAnimationFrame(e)};function d(e,r){return{observe:function(){var t=0===c.size;c.has(e)?c.get(e).callbacks.push(r):c.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[r]}),t&&u()},unobserve:function(){var t=c.get(e);if(t){var o=t.callbacks.indexOf(r);o>=0&&t.callbacks.splice(o,1),t.callbacks.length||c.delete(e),c.size||cancelAnimationFrame(n)}}}}var f="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;function p(e,r){void 0===r&&(r={width:0,height:0});var t=o.useState(e.current),n=t[0],a=t[1],i=o.useReducer(b,r),l=i[0],s=i[1],c=o.useRef(!1);return f(function(){e.current!==n&&a(e.current)}),f(function(){n&&!c.current&&(c.current=!0,s({rect:n.getBoundingClientRect()}))},[n]),o.useEffect(function(){if(n){var e=d(n,function(e){s({rect:e})});return e.observe(),function(){e.unobserve()}}},[n]),l}function b(e,r){var t=r.rect;return e.height!==t.height||e.width!==t.width?t:e}var g=function(){return 50},v=function(e){return e},m=function(e,r){return e[r?"offsetWidth":"offsetHeight"]},h=function(e){for(var r=Math.max(e.start-e.overscan,0),t=Math.min(e.end+e.overscan,e.size-1),n=[],o=r;o<=t;o++)n.push(o);return n};function y(e){var r,t=e.size,n=void 0===t?0:t,l=e.estimateSize,s=void 0===l?g:l,c=e.overscan,u=void 0===c?1:c,d=e.paddingStart,b=void 0===d?0:d,y=e.paddingEnd,x=e.parentRef,C=e.horizontal,k=e.scrollToFn,O=e.useObserver,E=e.initialRect,z=e.onScrollElement,_=e.scrollOffsetFn,N=e.keyExtractor,U=void 0===N?v:N,Z=e.measureSize,S=void 0===Z?m:Z,R=e.rangeExtractor,j=void 0===R?h:R,T=C?"scrollLeft":"scrollTop",I=o.useRef({scrollOffset:0,measurements:[]}),P=o.useState(0),D=P[0],F=P[1];I.current.scrollOffset=D;var L=(O||p)(x,E)[C?"width":"height"];I.current.outerSize=L;var M=o.useCallback(function(e){x.current&&(x.current[T]=e)},[x,T]),A=k||M;k=o.useCallback(function(e){A(e,M)},[M,A]);var B=o.useState({}),H=B[0],q=B[1],W=o.useCallback(function(){return q({})},[]),K=o.useRef([]),Q=o.useMemo(function(){var e=K.current.length>0?Math.min.apply(Math,K.current):0;K.current=[];for(var r=I.current.measurements.slice(0,e),t=e;t<n;t++){var o=U(t),a=H[o],i=r[t-1]?r[t-1].end:b,l="number"==typeof a?a:s(t),c=i+l;r[t]={index:t,start:i,size:l,end:c,key:o}}return r},[s,H,b,n,U]),X=((null==(r=Q[n-1])?void 0:r.end)||b)+(void 0===y?0:y);I.current.measurements=Q,I.current.totalSize=X;var G=z?z.current:x.current,J=o.useRef(_);J.current=_,f(function(){if(!G){F(0);return}var e=function(e){F(J.current?J.current(e):G[T])};return e(),G.addEventListener("scroll",e,{capture:!1,passive:!0}),function(){G.removeEventListener("scroll",e)}},[G,T]);var V=w(I.current),Y=V.start,$=V.end,ee=o.useMemo(function(){return j({start:Y,end:$,overscan:u,size:Q.length})},[Y,$,u,Q.length,j]),er=o.useRef(S);er.current=S;var et=o.useMemo(function(){for(var e=[],r=0,t=ee.length;r<t;r++)!function(r,t){var n=ee[r],o=a(a({},Q[n]),{},{measureRef:function(e){if(e){var r=er.current(e,C);if(r!==o.size){var t=I.current.scrollOffset;o.start<t&&M(t+(r-o.size)),K.current.push(n),q(function(e){var t;return a(a({},e),{},((t={})[o.key]=r,t))})}}}});e.push(o)}(r);return e},[ee,M,C,Q]),en=o.useRef(!1);f(function(){en.current&&q({}),en.current=!0},[s]);var eo=o.useCallback(function(e,r){var t=(void 0===r?{}:r).align,n=void 0===t?"start":t,o=I.current,a=o.scrollOffset,i=o.outerSize;"auto"===n&&(n=e<=a?"start":e>=a+i?"end":"start"),"start"===n?k(e):"end"===n?k(e-i):"center"===n&&k(e-i/2)},[k]),ea=o.useCallback(function(e,r){var t=void 0===r?{}:r,o=t.align,l=void 0===o?"auto":o,s=i(t,["align"]),c=I.current,u=c.measurements,d=c.scrollOffset,f=c.outerSize,p=u[Math.max(0,Math.min(e,n-1))];if(p){if("auto"===l){if(p.end>=d+f)l="end";else{if(!(p.start<=d))return;l="start"}}eo("center"===l?p.start+p.size/2:"end"===l?p.end:p.start,a({align:l},s))}},[eo,n]),ei=o.useCallback(function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];ea.apply(void 0,r),requestAnimationFrame(function(){ea.apply(void 0,r)})},[ea]);return{virtualItems:et,totalSize:X,scrollToOffset:eo,scrollToIndex:ei,measure:W}}var x=function(e,r,t,n){for(;e<=r;){var o=(e+r)/2|0,a=t(o);if(a<n)e=o+1;else{if(!(a>n))return o;r=o-1}}return e>0?e-1:0};function w(e){for(var r=e.measurements,t=e.outerSize,n=e.scrollOffset,o=r.length-1,a=x(0,o,function(e){return r[e].start},n),i=a;i<o&&r[i].end<n+t;)i++;return{start:a,end:i}}}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_Dialog_js-node_modules_primer_react_lib-esm_Flash_F-ad64b6-d0cea9e6796d.js.map