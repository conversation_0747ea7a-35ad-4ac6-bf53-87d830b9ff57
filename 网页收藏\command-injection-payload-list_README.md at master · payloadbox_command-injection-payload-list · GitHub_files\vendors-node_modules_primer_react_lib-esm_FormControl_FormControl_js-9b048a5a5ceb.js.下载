"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_FormControl_FormControl_js"],{96721:(e,t,a)=>{a.d(t,{Z:()=>I});var n=a(67294);let r=(0,n.createContext)(null);var l=a(31171),i=a(70697),o=a(51526);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let d=n.forwardRef(({as:e=o.Z,onFocus:t,onBlur:a,onChange:d,onKeyDown:c,onKeyUp:u,onKeyPress:p,value:m,...f},g)=>{let v=(0,n.useContext)(r);if(null===v)throw Error("AutocompleteContext returned null values");let{activeDescendantRef:h,autocompleteSuggestion:b="",id:y,inputRef:x,inputValue:E="",isMenuDirectlyActivated:w,setInputValue:Z,setShowMenu:C,showMenu:k}=v;(0,l.z)(g,x);let[O,S]=(0,n.useState)(!0),{safeSetTimeout:j}=(0,i.Z)(),z=(0,n.useCallback)(e=>{t&&t(e),C(!0)},[t,C]),R=(0,n.useCallback)(e=>{a&&a(e),j(()=>{document.activeElement!==x.current&&C(!1)},0)},[a,C,x,j]),V=(0,n.useCallback)(e=>{d&&d(e),Z(e.currentTarget.value),k||C(!0)},[d,Z,C,k]),N=(0,n.useCallback)(e=>{var t;c&&c(e),"Backspace"===e.key&&S(!1),"Escape"===e.key&&null!==(t=x.current)&&void 0!==t&&t.value&&(Z(""),x.current.value="")},[x,Z,S,c]),I=(0,n.useCallback)(e=>{u&&u(e),"Backspace"===e.key&&S(!0)},[S,u]),P=(0,n.useCallback)(e=>{if(p&&p(e),k&&"Enter"===e.key&&h.current){e.preventDefault(),e.nativeEvent.stopImmediatePropagation();let t=new KeyboardEvent(e.type,e.nativeEvent);h.current.dispatchEvent(t)}},[h,k,p]);return(0,n.useEffect)(()=>{x.current&&(b||(x.current.value=E),O&&b&&(E||w)&&(x.current.value=b,0===b.toLowerCase().indexOf(E.toLowerCase())&&x.current.setSelectionRange(E.length,b.length)))},[b,E,x,w]),(0,n.useEffect)(()=>{Z(void 0!==m?m.toString():"")},[m,Z]),n.createElement(e,s({onFocus:z,onBlur:R,onChange:V,onKeyDown:N,onKeyPress:P,onKeyUp:I,ref:x,"aria-controls":`${y}-listbox`,"aria-autocomplete":"both",role:"combobox","aria-expanded":k,"aria-haspopup":"listbox","aria-owns":`${y}-listbox`,autoComplete:"off",id:y},f))});d.displayName="AutocompleteInput";var c=d,u=a(13275),p=a(33827),m=a(17840),f=a(44288),g=a(89283),v=a(53670),h=a(42483),b=a(74121);let y=e=>(t,a)=>e(t)===e(a)?0:e(t)?-1:1,x={startMargin:0,endMargin:8};function E(e){return function(t,a){var n;return Boolean(null===(n=t.text)||void 0===n?void 0:n.toLowerCase().startsWith(e.toLowerCase()))}}function w(e){return function(t){let{text:a=""}=Array.isArray(t)?t.slice(-1)[0]:t;e(a)}}let Z=(e,t)=>t.includes(e);function C(e,t){return t.find(t=>t.id===e)}function k(e){let t=(0,n.useContext)(r);if(null===t)throw Error("AutocompleteContext returned null values");let{activeDescendantRef:a,id:l,inputRef:i,inputValue:o="",scrollContainerRef:s,setAutocompleteSuggestion:d,setShowMenu:c,setInputValue:k,setIsMenuDirectlyActivated:O,setSelectedItemLength:S,showMenu:j}=t,{items:z,selectedItemIds:R,sortOnCloseFn:V,emptyStateText:N="No selectable options",addNewItem:I,loading:P,selectionVariant:A="single",filterFn:T,"aria-labelledby":L,onOpenChange:B,onSelectedChange:F,customScrollContainerRef:W}=e,_=(0,n.useRef)(null),[M,U]=(0,n.useState)(),[D,H]=(0,n.useState)(z.map(({id:e})=>e)),q=(0,f.M)(l),G=(0,n.useMemo)(()=>z.map(e=>({...e,role:"option",id:e.id,selected:"multiple"===A?R.includes(e.id):void 0,onAction:e=>{let t=R.filter(t=>t!==e.id),a=R.includes(e.id)?t:[...t,e.id],n=F||w(k);if(n(a.map(e=>C(e,z))),"multiple"===A)k(""),d("");else{var r;c(!1),null===(r=i.current)||void 0===r||r.setSelectionRange(i.current.value.length,i.current.value.length)}}})),[z,R,i,F,A,d,k,c]),K=(0,n.useMemo)(()=>D.reduce((e,t,a)=>(e[t]=a,e),{}),[D]),$=(0,n.useMemo)(()=>G.filter(T||E(o)).sort((e,t)=>K[e.id]-K[t.id]),[G,K,T,o]),J=(0,n.useMemo)(()=>[...$,...I?[{...I,leadingVisual:()=>n.createElement(g.pOD,null),onAction:e=>{I.handleAddItem({...e,id:e.id||q,leadingVisual:void 0}),"multiple"===A&&(k(""),d(""))}}]:[]],[$,I,d,A,k,q]);return(0,m.v)({containerRef:_,focusOutBehavior:"wrap",focusableElementFilter:e=>!(e instanceof HTMLInputElement),activeDescendantFocus:i,onActiveDescendantChanged:(e,t,n)=>{if(a.current=e||null,e){let t=G.find(t=>t.id.toString()===e.getAttribute("data-id"));U(t),O(n)}e&&W&&W.current&&n?(0,u.z)(e,W.current,x):e&&s.current&&n&&(0,u.z)(e,s.current,x)}},[P]),(0,n.useEffect)(()=>{var e;null!=M&&null!==(e=M.text)&&void 0!==e&&e.startsWith(o)&&!R.includes(M.id)?d(M.text):d("")},[M,o,R,d]),(0,n.useEffect)(()=>{let e=[...D].sort(V||y(e=>Z(e,R))),t=e.length===D.length&&e.every((e,t)=>e===D[t]);!1!==j||t||H(e),B&&B(Boolean(j))},[j,B,R,V,D]),(0,n.useEffect)(()=>{R.length&&S(R.length)},[R,S]),n.createElement(v.Z,{isVisible:j},P?n.createElement(h.Z,{p:3,display:"flex",justifyContent:"center"},n.createElement(b.Z,null)):n.createElement("div",{ref:_},J.length?n.createElement(p.S,{selectionVariant:"multiple",items:J,role:"listbox",id:`${l}-listbox`,"aria-labelledby":L}):n.createElement(h.Z,{p:3},N)))}k.displayName="AutocompleteMenu",k.displayName="AutocompleteMenu";var O=a(48158),S=a(8677);function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}function z({menuAnchorRef:e,overlayProps:t,children:a,...i}){let o=(0,n.useContext)(r);if(null===o)throw Error("AutocompleteContext returned null values");let s={...t,...i},{inputRef:d,scrollContainerRef:c,selectedItemLength:u,setShowMenu:p,showMenu:m=!1}=o,{floatingElementRef:f,position:g}=(0,O.a)({side:"outside-bottom",align:"start",anchorElementRef:e||d},[m,u]);(0,l.z)(c,f);let h=(0,n.useCallback)(()=>{p(!1)},[p]);return"undefined"==typeof window?null:m?n.createElement(S.Z,j({returnFocusRef:d,preventFocusOnOpen:!0,onClickOutside:h,onEscape:h,ref:f,top:null==g?void 0:g.top,left:null==g?void 0:g.left,sx:{overflow:"auto"}},s),a):n.createElement(v.Z,{"aria-hidden":"true"},a)}z.displayName="AutocompleteOverlay";let R={inputValue:"",showMenu:!1,isMenuDirectlyActivated:!1,autocompleteSuggestion:"",selectedItemLength:0},V=(e,t)=>{let{type:a,payload:n}=t;switch(a){case"inputValue":return{...e,inputValue:n};case"showMenu":return{...e,showMenu:n};case"isMenuDirectlyActivated":return{...e,isMenuDirectlyActivated:n};case"autocompleteSuggestion":return{...e,autocompleteSuggestion:n};case"selectedItemLength":return{...e,selectedItemLength:n};default:return e}},N=({children:e,id:t})=>{let a=(0,n.useRef)(null),l=(0,n.useRef)(null),i=(0,n.useRef)(null),[o,s]=(0,n.useReducer)(V,R),{inputValue:d,showMenu:c,autocompleteSuggestion:u,isMenuDirectlyActivated:p,selectedItemLength:m}=o,g=(0,n.useCallback)(e=>{s({type:"inputValue",payload:e})},[]),v=(0,n.useCallback)(e=>{s({type:"showMenu",payload:e})},[]),h=(0,n.useCallback)(e=>{s({type:"autocompleteSuggestion",payload:e})},[]),b=(0,n.useCallback)(e=>{s({type:"isMenuDirectlyActivated",payload:e})},[]),y=(0,n.useCallback)(e=>{s({type:"selectedItemLength",payload:e})},[]),x=(0,f.M)(t);return n.createElement(r.Provider,{value:{activeDescendantRef:a,autocompleteSuggestion:u,id:x,inputRef:i,inputValue:d,isMenuDirectlyActivated:p,scrollContainerRef:l,selectedItemLength:m,setAutocompleteSuggestion:h,setInputValue:g,setIsMenuDirectlyActivated:b,setShowMenu:v,setSelectedItemLength:y,showMenu:c}},e)};N.displayName="Autocomplete";var I=Object.assign(N,{Context:r,Input:c,Menu:k,Overlay:z})},9770:(e,t,a)=>{a.d(t,{Z:()=>N});var n=a(67294),r=a(13503),l=a(85099),i=a(42379),o=a(69889),s=a(44288),d=a(97011);let c=({children:e,disabled:t,id:a,sx:r})=>n.createElement(d.Z,{color:t?"fg.subtle":"fg.muted",display:"block",fontSize:0,id:a,sx:r},e);c.displayName="InputCaption";var u=c,p=a(26408);let m=({children:e,sx:t,id:a})=>{let{captionId:r,disabled:l}=(0,p.NJ)();return n.createElement(u,{id:a||r||"",disabled:l,sx:t},e)};m.displayName="FormControlCaption";var f=m,g=a(95415);let v=({as:e,children:t,htmlFor:a,id:r,visuallyHidden:l,sx:i,...o})=>{let{disabled:s,id:d,required:c}=(0,p.NJ)(),u="legend"===e||"span"===e?{as:e,id:r,visuallyHidden:l,required:c,disabled:s,sx:i,...o}:{as:e,id:r,visuallyHidden:l,htmlFor:a||d,required:c,disabled:s,sx:i,...o};return n.createElement(g.Z,u,t)};v.displayName="FormControlLabel";var h=v,b=a(42483);let y=({children:e,sx:t})=>{let{disabled:a,captionId:r}=(0,p.NJ)();return n.createElement(b.Z,{color:a?"fg.muted":"fg.default",sx:{"> *":{minWidth:r?(0,i.U2)("fontSizes.4"):(0,i.U2)("fontSizes.2"),minHeight:r?(0,i.U2)("fontSizes.4"):(0,i.U2)("fontSizes.2"),fill:"currentColor"},...t},ml:2},e)};y.displayName="FormControlLeadingVisual";var x=y,E=a(15209);let w=({children:e,variant:t,sx:a,id:r})=>{let{validationMessageId:l}=(0,p.NJ)();return n.createElement(E.Z,{validationStatus:t,id:r||l||"",sx:a},e)};w.displayName="FormControlValidation";var Z=w,C=a(49297),k=a(99782),O=a(5359),S=a(96721),j=a(51526),z=a(70294),R=a(17791);let V=n.forwardRef(({children:e,disabled:t,layout:a="vertical",id:d,required:c,sx:u},m)=>{var g,v,y;let[E,w]=(0,o.R)(e,{caption:f,label:h,leadingVisual:x,validation:Z}),V=[S.Z,k.Z,O.Z,r.Z,j.Z,z.Z,R.ZP],N=(0,n.useContext)(C.j),I=N.disabled||t,P=(0,s.M)(d),A=E.validation?`${P}-validationMessage`:void 0,T=E.caption?`${P}-caption`:void 0,L=null===(g=E.validation)||void 0===g?void 0:g.props.variant,B=w.find(e=>V.some(t=>n.isValidElement(e)&&e.type===t)),F=n.isValidElement(B)&&B.props,W=n.isValidElement(B)&&(B.type===k.Z||B.type===O.Z);B&&(null!=F&&F.id&&console.warn("instead of passing the 'id' prop directly to the input component, it should be passed to the parent component, <FormControl>"),null!=F&&F.disabled&&console.warn("instead of passing the 'disabled' prop directly to the input component, it should be passed to the parent component, <FormControl>"),null!=F&&F.required&&console.warn("instead of passing the 'required' prop directly to the input component, it should be passed to the parent component, <FormControl>")),E.label||console.error(`The input field with the id ${P} MUST have a FormControl.Label child.

If you want to hide the label, pass the 'visuallyHidden' prop to the FormControl.Label component.`),W?(E.validation&&console.warn("Validation messages are not rendered for an individual checkbox or radio. The validation message should be shown for all options."),w.find(e=>{var t;return n.isValidElement(e)&&(null===(t=e.props)||void 0===t?void 0:t.required)})&&console.warn("An individual checkbox or radio cannot be a required field.")):E.leadingVisual&&console.warn("A leading visual is only rendered for a checkbox or radio form control. If you want to render a leading visual inside of your input, check if your input supports a leading visual.");let _=null===(v=E.label)||void 0===v?void 0:v.props.visuallyHidden;return n.createElement(p.wg,{value:{captionId:T,disabled:I,id:P,required:c,validationMessageId:A}},W||"horizontal"===a?n.createElement(b.Z,{ref:m,display:"flex",alignItems:E.leadingVisual?"center":void 0,sx:u},n.createElement(b.Z,{sx:{"> input":{marginLeft:0,marginRight:0}}},n.isValidElement(B)&&n.cloneElement(B,{id:P,disabled:I,"aria-describedby":T}),w.filter(e=>n.isValidElement(e)&&![k.Z,O.Z].some(t=>e.type===t))),E.leadingVisual&&n.createElement(b.Z,{color:I?"fg.muted":"fg.default",sx:{"> *":{minWidth:E.caption?(0,i.U2)("fontSizes.4"):(0,i.U2)("fontSizes.2"),minHeight:E.caption?(0,i.U2)("fontSizes.4"):(0,i.U2)("fontSizes.2"),fill:"currentColor"}},ml:2},E.leadingVisual),!(null!==(y=E.label)&&void 0!==y&&y.props.visuallyHidden)||E.caption?n.createElement(b.Z,{display:"flex",flexDirection:"column",ml:2},E.label,E.caption):n.createElement(n.Fragment,null,E.label,E.caption)):n.createElement(b.Z,{ref:m,display:"flex",flexDirection:"column",alignItems:"flex-start",sx:{..._?{"> *:not(label) + *":{marginTop:1}}:{"> * + *":{marginTop:1}},...u}},E.label,n.isValidElement(B)&&n.cloneElement(B,Object.assign({id:P,required:c,disabled:I,validationStatus:L,"aria-describedby":[A,T].filter(Boolean).join(" ")},B.props)),w.filter(e=>n.isValidElement(e)&&!V.some(t=>e.type===t)),E.validation?n.createElement(l.Z,{show:!0},E.validation):null,E.caption))});var N=Object.assign(V,{Caption:f,Label:h,LeadingVisual:x,Validation:Z})},26408:(e,t,a)=>{a.d(t,{NJ:()=>i,op:()=>o,wg:()=>l});var n=a(67294);let r=(0,n.createContext)(null),l=r.Provider;function i(){var e;return null!==(e=(0,n.useContext)(r))&&void 0!==e?e:{}}function o(e={}){let t=(0,n.useContext)(r);return t?{disabled:t.disabled,id:t.id,required:t.required,validationStatus:t.validationStatus,"aria-describedby":[t.validationMessageId,t.captionId].filter(Boolean).join(" ")||void 0,...e}:e}},5359:(e,t,a)=>{a.d(t,{Z:()=>m});var n=a(15388),r=a(67294),l=a(15173),i=a(56397),o=a(23383),s=a(42379),d=a(29178);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let u=n.ZP.input.withConfig({displayName:"Radio__StyledRadio",componentId:"sc-1tx0ht9-0"})(["",";border-radius:var(--borderRadius-full,100vh);transition:background-color,border-color 80ms cubic-bezier(0.33,1,0.68,1);&:checked{border-color:",";border-width:var(--base-size-4,4px);&:disabled{cursor:not-allowed;border-color:",";}}",";@media (forced-colors:active){background-color:canvastext;border-color:canvastext;}",""],d.m,(0,s.U2)("colors.accent.fg"),(0,s.U2)("colors.fg.muted"),(0,o.Z)(),l.Z),p=r.forwardRef(({checked:e,disabled:t,name:a,onChange:n,sx:l,required:o,validationStatus:s,value:d,...p},m)=>{let f=(0,r.useContext)(i.L),g=e=>{(null==f?void 0:f.onChange)&&f.onChange(e),n&&n(e)},v=a||(null==f?void 0:f.name);return v||console.warn("A radio input must have a `name` attribute. Pass `name` as a prop directly to each Radio, or nest them in a `RadioGroup` component with a `name` prop"),r.createElement(u,c({type:"radio",value:d,name:v,ref:m,disabled:t,checked:e,"aria-checked":e?"true":"false",required:o,"aria-required":o?"true":"false","aria-invalid":"error"===s?"true":"false",sx:l,onChange:g},p))});p.displayName="Radio";var m=p},56397:(e,t,a)=>{a.d(t,{L:()=>c,Z:()=>p});var n=a(67294),r=a(48987),l=a(4776),i=a(14682),o=a(55744),s=a(49297);function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let c=(0,n.createContext)(null),u=({children:e,disabled:t,onChange:a,name:r,...l})=>{let[i,u]=(0,o.d)(null),p=e=>{let{value:t,checked:a}=e.currentTarget;if(a){u(t);return}};return n.createElement(c.Provider,{value:{disabled:t,name:r,onChange:e=>{a&&(p(e),a(i.current,e))}}},n.createElement(s.Z,d({disabled:t},l),e))};u.displayName="RadioGroup";var p=Object.assign(u,{Caption:r.Z,Label:l.Z,Validation:i.Z})},13503:(e,t,a)=>{a.d(t,{Z:()=>m});var n=a(67294),r=a(15388),l=a(20682);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let o=r.ZP.select.withConfig({displayName:"Select__StyledSelect",componentId:"sc-li6bhs-0"})(["appearance:none;border-radius:inherit;border:0;color:currentColor;font-size:inherit;outline:none;width:100%;background-color:inherit;margin-top:1px;margin-left:1px;margin-bottom:1px;&:disabled{background-color:transparent;}@media screen and (forced-colors:active){&:disabled{background-color:-moz-combobox;}}"]),s=({className:e})=>n.createElement("svg",{width:"16",height:"16",fill:"currentColor",xmlns:"http://www.w3.org/2000/svg",className:e},n.createElement("path",{d:"m4.074 9.427 3.396 3.396a.25.25 0 0 0 .354 0l3.396-3.396A.25.25 0 0 0 11.043 9H4.251a.25.25 0 0 0-.177.427ZM4.074 7.47 7.47 4.073a.25.25 0 0 1 .354 0L11.22 7.47a.25.25 0 0 1-.177.426H4.251a.25.25 0 0 1-.177-.426Z"}));s.displayName="ArrowIndicatorSVG";let d=(0,r.ZP)(s).withConfig({displayName:"Select__ArrowIndicator",componentId:"sc-li6bhs-1"})(["pointer-events:none;position:absolute;right:",";top:50%;transform:translateY(-50%);"],"4px"),c=n.forwardRef(({block:e,children:t,contrast:a,disabled:r,placeholder:s,size:c,required:u,validationStatus:p,...m},f)=>n.createElement(l.ZP,{sx:{overflow:"hidden",position:"relative","@media screen and (forced-colors: active)":{svg:{fill:r?"GrayText":"FieldText"}}},block:e,contrast:a,disabled:r,size:c,validationStatus:p},n.createElement(o,i({ref:f,required:u,disabled:r,"aria-invalid":"error"===p?"true":"false","data-hasplaceholder":Boolean(s),defaultValue:null!=s?s:void 0},m),s&&n.createElement("option",{value:"",disabled:u,hidden:u},s),t),n.createElement(d,null))),u=e=>n.createElement("option",e);u.displayName="Option";let p=e=>n.createElement("optgroup",e);p.displayName="OptGroup";var m=Object.assign(c,{Option:u,OptGroup:p})},70294:(e,t,a)=>{a.d(t,{Z:()=>Z});var n=a(22114),r=a(78160),l=a(27999),i=RegExp("^("+(0,l.compose)(l.Dh,l.cp,l.$_,l.bK,l.GQ,l.Cg,l.Oq,l.FK,l.eC,l.AF,l.jn,l.YK,l.ui).propNames.join("|")+")$"),o=function(e){var t={};for(var a in e)i.test(a)||(t[a]=e[a]);return t},s=function(e){var t={};for(var a in e)i.test(a)&&(t[a]=e[a]);return t},d=a(67294),c=a(14890),u=a(31171),p=a(17840),m=a(84817),f=a(20682),g=a(38535),v=a(72691),h=a(42483),b=a(97011);function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let x={small:0,medium:1,large:1,extralarge:2,xlarge:2};function E({icon:e,leadingVisual:t,trailingVisual:a,loading:l,loaderPosition:i="auto",contrast:s,className:E,block:w,disabled:Z,sx:C,tokens:k,onTokenRemove:O,tokenComponent:S=m.Z,preventTokenWrapping:j=!1,size:z="xlarge",hideTokenRemoveButtons:R=!1,maxHeight:V,width:N,minWidth:I,maxWidth:P,validationStatus:A,variant:T,visibleTokenCount:L,...B},F){let{onBlur:W,onFocus:_,onKeyDown:M,...U}=o(B),D=(0,d.useRef)(null);(0,u.z)(F,D);let[H,q]=(0,d.useState)(),[G,K]=(0,d.useState)(Boolean(L)),{containerRef:$}=(0,p.v)({focusOutBehavior:"wrap",bindKeys:n.Qw.ArrowHorizontal|n.Qw.HomeAndEnd,focusableElementFilter:e=>!e.getAttributeNames().includes("aria-hidden"),getNextFocusable:e=>{var t;if(!H&&0!==H)return;let a=H+1;return("next"===e&&(a+=1),"previous"===e&&(a-=1),a>k.length||a<1)?D.current||void 0:null===(t=$.current)||void 0===t?void 0:t.children[a]}},[H]),J=e=>{O(e),setTimeout(()=>{var e,t,a;let n=null===(e=$.current)||void 0===e?void 0:e.children[H||0],l=n&&(0,r.EB)(n)?n:Array.from((null===(t=$.current)||void 0===t?void 0:t.children)||[]).find(e=>(0,r.EB)(e));l?l.focus():null===(a=D.current)||void 0===a||a.focus()},0)},Q=e=>()=>{Z||q(e)},Y=()=>{q(void 0),setTimeout(()=>{var e;!(null!==(e=$.current)&&void 0!==e&&e.contains(document.activeElement))&&L&&K(!0)},0)},X=e=>{if("Escape"===e.key){var t;null===(t=D.current)||void 0===t||t.focus()}},ee=e=>{_&&_(e),q(void 0),L&&K(!1)},et=e=>{W&&W(e),setTimeout(()=>{var e;!(null!==(e=$.current)&&void 0!==e&&e.contains(document.activeElement))&&L&&K(!0)},0)},ea=e=>{var t;if(M&&M(e),null!==(t=D.current)&&void 0!==t&&t.value)return;let a=k[k.length-1];"Backspace"===e.key&&a&&(J(a.id),D.current&&(D.current.value=`${a.text} `),setTimeout(()=>{var e;null===(e=D.current)||void 0===e||e.select()},0))},en=()=>{var e;null===(e=D.current)||void 0===e||e.focus()},er=e=>{e.stopPropagation()},el=G?k.slice(0,L):k,ei=l&&("leading"===i||Boolean(t&&"trailing"!==i)),eo=l&&("trailing"===i||"auto"===i&&!t);return d.createElement(f.ZP,{block:w,className:E,contrast:s,disabled:Z,hasLeadingVisual:Boolean(t||ei),hasTrailingVisual:Boolean(a||eo),width:N,minWidth:I,maxWidth:P,size:{small:"small",medium:"small",large:"medium",extralarge:"medium",xlarge:"medium"}[z],validationStatus:A,variant:T,onClick:en,sx:{paddingLeft:f.Qk,py:`calc(${f.Qk} / 2)`,...w?{display:"flex",width:"100%"}:{},...V?{maxHeight:V,overflow:"auto"}:{},...j?{overflow:"auto"}:{},...C}},e&&!t&&d.createElement(e,{className:"TextInput-icon"}),d.createElement(v.Z,{hasLoadingIndicator:"boolean"==typeof l,visualPosition:"leading",showLoadingIndicator:ei},"string"!=typeof t&&(0,c.isValidElementType)(t)?d.createElement(t,null):t),d.createElement(h.Z,{ref:$,display:"flex",sx:{alignItems:"center",flexWrap:j?"nowrap":"wrap",marginLeft:"-0.25rem",marginBottom:"-0.25rem",flexGrow:1,"> *":{flexShrink:0,marginLeft:"0.25rem",marginBottom:"0.25rem"}}},d.createElement(h.Z,{sx:{order:1,flexGrow:1}},d.createElement(g.Z,y({ref:D,disabled:Z,onFocus:ee,onBlur:et,onKeyDown:ea,type:"text",sx:{height:"100%"},"aria-invalid":"error"===A?"true":"false"},U))),el.map(({id:e,...t},a)=>d.createElement(S,y({disabled:Z,key:e,onFocus:Q(a),onBlur:Y,onKeyUp:X,onClick:er,isSelected:H===a,onRemove:()=>{J(e)},hideRemoveButton:Z||R,size:z,tabIndex:0},t))),G&&k.length-el.length?d.createElement(b.Z,{color:"fg.muted",fontSize:x[z]},"+",k.length-el.length):null),d.createElement(v.Z,{hasLoadingIndicator:"boolean"==typeof l,visualPosition:"trailing",showLoadingIndicator:eo},"string"!=typeof a&&(0,c.isValidElementType)(a)?d.createElement(a,null):a))}E.displayName="TextInputWithTokensInnerComponent";let w=d.forwardRef(E);w.displayName="TextInputWithTokens";var Z=w},17791:(e,t,a)=>{a.d(t,{ZP:()=>c});var n=a(15388),r=a(67294),l=a(20682),i=a(15173);function o(){return(o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let s=n.ZP.textarea.withConfig({displayName:"Textarea__StyledTextarea",componentId:"sc-1lf8it-0"})(["border:0;font-size:inherit;font-family:inherit;background-color:transparent;-webkit-appearance:none;color:inherit;width:100%;resize:both;&:focus{outline:0;}"," "," ",";"],e=>e.resize&&(0,n.iv)(["resize:",";"],e.resize),e=>e.disabled&&(0,n.iv)(["resize:none;"]),i.Z),d=r.forwardRef(({value:e,disabled:t,sx:a,required:n,validationStatus:i,rows:d=7,cols:c=30,resize:u="both",block:p,...m},f)=>r.createElement(l.FD,{sx:a,validationStatus:i,disabled:t,block:p},r.createElement(s,o({value:e,resize:u,required:n,"aria-required":n?"true":"false","aria-invalid":"error"===i?"true":"false",ref:f,disabled:t,rows:d,cols:c},m))));d.displayName="Textarea";var c=d},84817:(e,t,a)=>{a.d(t,{Z:()=>g});var n=a(67294),r=a(7261),l=a(423),i=a(39663),o=a(53722),s=a(53670),d=a(42483),c=a(9996),u=a.n(c);function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let m=({children:e,size:t})=>n.createElement(d.Z,{sx:{flexShrink:0,lineHeight:0,marginRight:t&&["large","extralarge","xlarge"].includes(t)?2:1}},e);m.displayName="LeadingVisualContainer";let f=(0,n.forwardRef)((e,t)=>{let{as:a,onRemove:d,id:c,leadingVisual:f,text:g,size:v=l.rK,hideRemoveButton:h,href:b,onClick:y,sx:x=r.P,...E}=e,w=(0,l.Zq)(e)&&Boolean(d)&&!h,Z=e=>{e.stopPropagation(),d&&d()},C={as:a,href:b,onClick:y},k=u()({backgroundColor:"neutral.subtle",borderColor:e.isSelected?"fg.default":"border.subtle",borderStyle:"solid",borderWidth:"1px",color:e.isSelected?"fg.default":"fg.muted",maxWidth:"100%",paddingRight:h||!d?void 0:0,...(0,l.Zq)(e)?{"&:hover":{backgroundColor:"neutral.muted",boxShadow:"shadow.medium",color:"fg.default"}}:{}},x);return n.createElement(l.ZP,p({onRemove:d,id:null==c?void 0:c.toString(),text:g,size:v,sx:k},w?{}:C,E,{ref:t}),f?n.createElement(m,{size:v},n.createElement(f,null)):null,n.createElement(o.Z,w?C:{},g),d&&n.createElement(s.Z,null," (press backspace or delete to remove)"),!h&&d?n.createElement(i.Z,{borderOffset:1,onClick:Z,size:v,isParentInteractive:(0,l.Zq)(e),"aria-hidden":w?"true":"false"}):null)});f.displayName="Token";var g=f},423:(e,t,a)=>{a.d(t,{W6:()=>c,ZP:()=>h,Zq:()=>p,rK:()=>u});var n=a(67294),r=a(15388),l=a(27999),i=a(42379),o=a(15173);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let d="32px",c={small:"16px",medium:"20px",large:"24px",extralarge:d,xlarge:d},u="medium",p=({as:e="span",onClick:t,onFocus:a,tabIndex:n=-1,disabled:r})=>!r&&Boolean(a||t||n>-1||["a","button"].includes(e)),m={fontSize:1,height:c.xlarge,lineHeight:c.xlarge,paddingLeft:3,paddingRight:3,paddingTop:0,paddingBottom:0},f=(0,l.bU)({prop:"size",variants:{small:{fontSize:0,height:c.small,lineHeight:c.small,paddingLeft:1,paddingRight:1},medium:{fontSize:0,height:c.medium,lineHeight:c.medium,paddingLeft:2,paddingRight:2},large:{fontSize:0,height:c.large,lineHeight:c.large,paddingLeft:2,paddingRight:2},extralarge:m,xlarge:m}}),g=r.ZP.span.withConfig({displayName:"TokenBase__StyledTokenBase",componentId:"sc-1ju9l7y-0"})(["align-items:center;border-radius:999px;cursor:",";display:inline-flex;font-weight:",";font-family:inherit;text-decoration:none;position:relative;white-space:nowrap;"," ",""],e=>p(e)?"pointer":"auto",(0,i.U2)("fontWeights.bold"),f,o.Z),v=n.forwardRef(({onRemove:e,onKeyDown:t,id:a,size:r=u,...l},i)=>n.createElement(g,s({onKeyDown:a=>{t&&t(a),("Backspace"===a.key||"Delete"===a.key)&&e&&e()},id:null==a?void 0:a.toString(),size:r},l,{ref:i})));var h=v},39663:(e,t,a)=>{a.d(t,{Z:()=>g});var n=a(67294),r=a(89283),l=a(15388),i=a(27999),o=a(42379),s=a(15173),d=a(423);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let u=(0,i.bU)({prop:"size",variants:{small:{height:d.W6.small,width:d.W6.small},medium:{height:d.W6.medium,width:d.W6.medium},large:{height:d.W6.large,width:d.W6.large},extralarge:{height:d.W6.extralarge,width:d.W6.extralarge},xlarge:{height:d.W6.xlarge,width:d.W6.xlarge}}}),p=e=>.75*parseInt(d.W6[e||d.rK],10),m=l.ZP.span.withConfig({displayName:"_RemoveTokenButton__StyledTokenButton",componentId:"sc-urhpr1-0"})(["background-color:transparent;font-family:inherit;color:currentColor;cursor:pointer;display:inline-flex;justify-content:center;align-items:center;user-select:none;appearance:none;text-decoration:none;padding:0;transform:",";align-self:baseline;border:0;border-radius:999px;"," &:hover,&:focus{background-color:",";}&:active{background-color:",";}"," ",""],e=>`translate(${e.borderOffset}px, -${e.borderOffset}px)`,e=>{switch(e.size){case"large":case"extralarge":case"xlarge":return(0,l.iv)(["margin-left:",";"],(0,o.U2)("space.2"));default:return(0,l.iv)(["margin-left:",";"],(0,o.U2)("space.1"))}},(0,o.U2)("colors.neutral.muted"),(0,o.U2)("colors.neutral.subtle"),u,s.Z),f=({"aria-label":e,isParentInteractive:t,size:a=d.rK,...l})=>(delete l.children,n.createElement(m,c({as:t?"span":"button",tabIndex:t?-1:void 0,"aria-label":t?e:"Remove token",size:a},l),n.createElement(r.b0D,{size:p(a)})));f.displayName="RemoveTokenButton";var g=f},53722:(e,t,a)=>{a.d(t,{Z:()=>l});var n=a(15388);let r=(0,n.ZP)("span").withConfig({displayName:"_TokenTextContainer__TokenTextContainer",componentId:"sc-6l9eri-0"})(["flex-grow:1;min-width:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;background:transparent;border:none;color:inherit;font:inherit;margin:0;padding:0;width:auto;-webkit-font-smoothing:inherit;-moz-osx-font-smoothing:inherit;-webkit-appearance:none;line-height:normal;color:currentColor;text-decoration:none;&:is(a,button,[tabIndex='0']){cursor:pointer;&:after{content:'';position:absolute;left:0;top:0;right:0;bottom:0;}}"]);var l=r},49297:(e,t,a)=>{a.d(t,{Z:()=>b,j:()=>g});var n=a(67294),r=a(15388),l=a(85099),i=a(42379),o=a(44288),s=a(48987),d=a(4776),c=a(14682),u=a(53670),p=a(69889),m=a(42483);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let g=n.createContext({}),v=r.ZP.div.withConfig({displayName:"CheckboxOrRadioGroup__Body",componentId:"sc-1t4rdk-0"})(["display:flex;flex-direction:column;list-style:none;margin:0;padding:0;> * + *{margin-top:",";}"],(0,i.U2)("space.2")),h=({"aria-labelledby":e,children:t,disabled:a=!1,id:r,required:i=!1,sx:h})=>{let[b,y]=(0,p.R)(t,{caption:s.Z,label:d.Z,validation:c.Z}),x=n.Children.toArray(t).find(e=>n.isValidElement(e)&&e.type===d.Z),E=n.Children.toArray(t).find(e=>n.isValidElement(e)&&e.type===c.Z?e:null),w=n.Children.toArray(t).find(e=>n.isValidElement(e)&&e.type===s.Z?e:null),Z=(0,o.M)(r),C=E?`${Z}-validationMessage`:void 0,k=w?`${Z}-caption`:void 0;x||e||console.warn("A choice group must be labelled using a `CheckboxOrRadioGroup.Label` child, or by passing `aria-labelledby` to the CheckboxOrRadioGroup component.");let O=n.isValidElement(x)&&!x.props.visuallyHidden;return n.createElement(g.Provider,{value:{disabled:a,required:i,captionId:k,validationMessageId:C}},n.createElement("div",null,n.createElement(m.Z,f({border:"none",margin:0,mb:E?2:void 0,padding:0},x&&{as:"fieldset",disabled:a},{sx:h}),x?n.createElement(m.Z,{as:"legend",mb:O?2:void 0,padding:0},b.label,b.caption,n.isValidElement(b.validation)&&b.validation.props.children&&n.createElement(u.Z,null,b.validation.props.children)):b.caption,n.createElement(v,!x&&{"aria-labelledby":e,"aria-describedby":[C,k].filter(Boolean).join(" "),as:"div",role:"group"},n.Children.toArray(y).filter(e=>n.isValidElement(e)))),E&&n.createElement(l.Z,{"aria-hidden":Boolean(x),show:!0},b.validation)))};h.displayName="CheckboxOrRadioGroup";var b=Object.assign(h,{Caption:s.Z,Label:d.Z,Validation:c.Z})},48987:(e,t,a)=>{a.d(t,{Z:()=>o});var n=a(67294),r=a(49297),l=a(97011);let i=({children:e,sx:t})=>{let{disabled:a,captionId:i}=n.useContext(r.j);return n.createElement(l.Z,{color:a?"fg.muted":"fg.subtle",fontSize:1,id:i,sx:t},e)};i.displayName="CheckboxOrRadioGroupCaption";var o=i},4776:(e,t,a)=>{a.d(t,{Z:()=>s});var n=a(67294),r=a(53670),l=a(49297),i=a(42483);let o=({children:e,visuallyHidden:t=!1,sx:a})=>{let{required:o,disabled:s}=n.useContext(l.j);return n.createElement(r.Z,{isVisible:!t,title:o?"required field":void 0,sx:{display:"block",color:s?"fg.muted":void 0,fontSize:2,...a}},o?n.createElement(i.Z,{display:"flex",as:"span"},n.createElement(i.Z,{mr:1},e),n.createElement("span",null,"*")):e)};o.displayName="CheckboxOrRadioGroupLabel";var s=o},14682:(e,t,a)=>{a.d(t,{Z:()=>o});var n=a(67294),r=a(15209),l=a(49297);let i=({children:e,variant:t,sx:a})=>{let{validationMessageId:i=""}=n.useContext(l.j);return n.createElement(r.Z,{validationStatus:t,id:i,sx:a},e)};i.displayName="CheckboxOrRadioGroupValidation";var o=i},95415:(e,t,a)=>{a.d(t,{Z:()=>s});var n=a(67294),r=a(53670),l=a(42483);function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var a=arguments[t];for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(e[n]=a[n])}return e}).apply(this,arguments)}let o=({children:e,disabled:t,htmlFor:a,id:o,required:s,visuallyHidden:d,sx:c,as:u="label",...p})=>n.createElement(r.Z,i({isVisible:!d,as:u,htmlFor:a,id:o,sx:{fontWeight:"bold",fontSize:1,display:"block",color:t?"fg.muted":"fg.default",cursor:t?"not-allowed":"pointer",alignSelf:"flex-start",...c}},p),s?n.createElement(l.Z,{display:"flex",as:"span"},n.createElement(l.Z,{mr:1},e),n.createElement("span",null,"*")):e);o.displayName="InputLabel";var s=o},15209:(e,t,a)=>{a.d(t,{Z:()=>c});var n=a(89283),r=a(67294),l=a(97011),i=a(42483);let o={success:n.kD1,error:n.enX,warning:n.enX},s={success:"success.fg",error:"danger.fg",warning:"attention.fg"},d=({children:e,id:t,validationStatus:a,sx:n})=>{let d=a?o[a]:void 0,c=a?s[a]:void 0,u=16/12,p=12*u;return r.createElement(l.Z,{sx:{fontSize:0,fontWeight:"bold",alignItems:"start",color:c,display:"flex",a:{color:"currentColor",textDecoration:"underline"},...n},"aria-live":"polite"},d&&r.createElement(i.Z,{as:"span",alignItems:"center",display:"flex",minHeight:p,mr:1,"aria-hidden":"true"},r.createElement(d,{size:12,fill:"currentColor"})),r.createElement(i.Z,{as:"span",id:t,sx:{lineHeight:u}},e))};d.displayName="InputValidation";var c=d},85099:(e,t,a)=>{a.d(t,{Z:()=>d});var n=a(67294),r=a(15388),l=a(42483);let i=(0,r.F4)(["0%{opacity:0;transform:translateY(-100%);}100%{opacity:1;transform:translateY(0);}"]),o=r.ZP.div.withConfig({displayName:"ValidationAnimationContainer__AnimatedElement",componentId:"sc-8z5a3g-0"})(["animation:",";@media (prefers-reduced-motion){animation:none;}"],e=>e.show&&(0,r.iv)(["170ms "," cubic-bezier(0.44,0.74,0.36,1);"],i)),s=({show:e,children:t})=>{let[a,r]=(0,n.useState)(e);(0,n.useEffect)(()=>{e&&r(!0)},[e]);let i=()=>{e||r(!1)};return a?n.createElement(l.Z,{height:e?"auto":0,overflow:"hidden"},n.createElement(o,{show:e,onAnimationEnd:i},t)):null};var d=s}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_FormControl_FormControl_js-2c96a90fb5bd.js.map