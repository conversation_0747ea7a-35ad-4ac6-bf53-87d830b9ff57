"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_PageLayout_PageLayout_js"],{81313:(e,t,n)=>{let r;n.d(t,{X:()=>K});var a=n(67294),i=n(15388),l=n(44288),o=n(31171),d=n(11791),u=n(69889),s=n(41632),c=n(54085),m=n(53670);let p=new Map,g=new WeakMap,f=0;function h(e){return e?(g.has(e)||(f+=1,g.set(e,f.toString())),g.get(e)):"0"}function w(e){return Object.keys(e).sort().filter(t=>void 0!==e[t]).map(t=>`${t}_${"root"===t?h(e.root):e[t]}`).toString()}function b(e){let t=w(e),n=p.get(t);if(!n){let r;let a=new Map,i=new IntersectionObserver(t=>{t.forEach(t=>{var n;let i=t.isIntersecting&&r.some(e=>t.intersectionRatio>=e);e.trackVisibility&&void 0===t.isVisible&&(t.isVisible=i),null==(n=a.get(t.target))||n.forEach(e=>{e(i,t)})})},e);r=i.thresholds||(Array.isArray(e.threshold)?e.threshold:[e.threshold||0]),n={id:t,observer:i,elements:a},p.set(t,n)}return n}function v(e,t,n={},a=r){if(void 0===window.IntersectionObserver&&void 0!==a){let r=e.getBoundingClientRect();return t(a,{isIntersecting:a,target:e,intersectionRatio:"number"==typeof n.threshold?n.threshold:0,time:0,boundingClientRect:r,intersectionRect:r,rootBounds:r}),()=>{}}let{id:i,observer:l,elements:o}=b(n),d=o.get(e)||[];return o.has(e)||o.set(e,d),d.push(t),l.observe(e),function(){d.splice(d.indexOf(t),1),0===d.length&&(o.delete(e),l.unobserve(e)),0===o.size&&(l.disconnect(),p.delete(i))}}function x({threshold:e,delay:t,trackVisibility:n,rootMargin:r,root:i,triggerOnce:l,skip:o,initialInView:d,fallbackInView:u,onChange:s}={}){var c;let[m,p]=a.useState(null),g=a.useRef(),[f,h]=a.useState({inView:!!d,entry:void 0});g.current=s,a.useEffect(()=>{let a;if(!o&&m)return a=v(m,(e,t)=>{h({inView:e,entry:t}),g.current&&g.current(e,t),t.isIntersecting&&l&&a&&(a(),a=void 0)},{root:i,rootMargin:r,threshold:e,trackVisibility:n,delay:t},u),()=>{a&&a()}},[Array.isArray(e)?e.toString():e,m,i,r,l,o,n,u,t]);let w=null==(c=f.entry)?void 0:c.target,b=a.useRef();m||!w||l||o||b.current===w||(b.current=w,h({inView:!!d,entry:void 0}));let x=[p,f.inView,f.entry];return x.ref=x[0],x.inView=x[1],x.entry=x[2],x}var y=n(69848),E=n(87691);function S(){let e=a.useRef(null),[t,n]=a.useState(L(100)),[r,i]=a.useState(0),[l,o,d]=x(),[u,s]=x(),c=a.useCallback(()=>{let t="",a=(0,E.J)(e.current),i=null==d?void 0:d.target.getBoundingClientRect(),l="number"==typeof r?`${r}px`:r;if(a){let e=a.getBoundingClientRect(),n=i?Math.max(i.top-e.top,0):0;t=`calc(${e.height}px - (max(${n}px, ${l})))`}else{let e=i?Math.max(i.top,0):0;t=`calc(${L(100)} - (max(${e}px, ${l})))`}n(t)},[d,r]),[m,p]=a.useState(!1);return(0,y.Z)(()=>{let t=(0,E.J)(e.current);return m&&(o||s)&&(c(),t?t.addEventListener("scroll",c):window.addEventListener("scroll",c),window.addEventListener("resize",c)),()=>{t?t.removeEventListener("scroll",c):window.removeEventListener("scroll",c),window.removeEventListener("resize",c)}},[m,o,s,c]),{rootRef:e,enableStickyPane:function(e){p(!0),i(e)},disableStickyPane:function(){p(!1)},contentTopRef:l,contentBottomRef:u,stickyPaneHeight:t}}let C=!!s.N&&CSS.supports("-webkit-touch-callout","none"),k=!!s.N&&CSS.supports("max-height","100dvh")&&C;function L(e){return k?`${e}dvh`:`${e}vh`}var N=n(42483),$=n(9996),R=n.n($);function V(){return(V=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}let Z={header:0,paneStart:1,content:2,paneEnd:3,footer:4},D={none:0,condensed:3,normal:[3,null,null,4]},P=a.createContext({padding:"normal",rowGap:"normal",columnGap:"normal"}),M={full:"100%",medium:"768px",large:"1012px",xlarge:"1280px"},O=({containerWidth:e="xlarge",padding:t="normal",rowGap:n="normal",columnGap:r="normal",children:i,sx:l={},_slotsConfig:o})=>{let{rootRef:d,enableStickyPane:s,disableStickyPane:c,contentTopRef:m,contentBottomRef:p,stickyPaneHeight:g}=S(),[f,h]=(0,u.R)(i,null!=o?o:{header:j,footer:U});return a.createElement(P.Provider,{value:{padding:t,rowGap:n,columnGap:r,enableStickyPane:s,disableStickyPane:c,contentTopRef:m,contentBottomRef:p}},a.createElement(N.Z,{ref:d,style:{"--sticky-pane-height":g},sx:R()({padding:D[t]},l)},a.createElement(N.Z,{sx:{maxWidth:M[e],marginX:"auto",display:"flex",flexWrap:"wrap"}},f.header,a.createElement(N.Z,{sx:{display:"flex",flex:"1 1 100%",flexWrap:"wrap",maxWidth:"100%"}},h),f.footer)))};O.displayName="Root",O.displayName="PageLayout";let W={none:{display:"none"},line:{display:"block",height:1,backgroundColor:"border.default"},filled:{display:"block",height:8,backgroundColor:"canvas.inset",boxShadow:e=>`inset 0 -1px 0 0 ${e.colors.border.default}, inset 0 1px 0 0 ${e.colors.border.default}`}};function B(e){return Array.isArray(e)?e.map(e=>null===e?null:-e):null===e?null:-e}let I=({variant:e="none",sx:t={}})=>{let{padding:n}=a.useContext(P),r=(0,d.Vt)(e,"none");return a.createElement(N.Z,{sx:e=>R()({marginX:B(D[n]),...W[r],[`@media screen and (min-width: ${e.breakpoints[1]})`]:{marginX:"0 !important"}},t)})};I.displayName="HorizontalDivider";let A={none:{display:"none"},line:{display:"block",width:1,backgroundColor:"border.default"},filled:{display:"block",width:8,backgroundColor:"canvas.inset",boxShadow:e=>`inset -1px 0 0 0 ${e.colors.border.default}, inset 1px 0 0 0 ${e.colors.border.default}`}},H=(0,i.vJ)(['body[data-page-layout-dragging="true"]{cursor:col-resize;}body[data-page-layout-dragging="true"] *{user-select:none;}']),_=({variant:e="none",draggable:t=!1,onDragStart:n,onDrag:r,onDragEnd:i,onDoubleClick:l,sx:o={}})=>{let[u,s]=a.useState(!1),c=(0,d.Vt)(e,"none"),m=a.useRef(r),p=a.useRef(i);return a.useEffect(()=>{m.current=r},[r]),a.useEffect(()=>{p.current=i},[i]),a.useEffect(()=>{function e(e){var t;null===(t=m.current)||void 0===t||t.call(m,e.movementX),e.preventDefault()}function t(e){var t;s(!1),null===(t=p.current)||void 0===t||t.call(p),e.preventDefault()}return u?(window.addEventListener("mousemove",e),window.addEventListener("mouseup",t),document.body.setAttribute("data-page-layout-dragging","true")):(window.removeEventListener("mousemove",e),window.removeEventListener("mouseup",t),document.body.removeAttribute("data-page-layout-dragging")),()=>{window.removeEventListener("mousemove",e),window.removeEventListener("mouseup",t),document.body.removeAttribute("data-page-layout-dragging")}},[u]),a.createElement(N.Z,{sx:R()({height:"100%",position:"relative",...A[c]},o)},t?a.createElement(a.Fragment,null,a.createElement(N.Z,{sx:{position:"absolute",inset:"0 -2px",cursor:"col-resize",bg:u?"accent.fg":"transparent",transitionDelay:"0.1s","&:hover":{bg:u?"accent.fg":"neutral.muted"}},role:"separator",onMouseDown:()=>{s(!0),null==n||n()},onDoubleClick:l}),a.createElement(H,null)):null)};_.displayName="VerticalDivider";let j=({"aria-label":e,"aria-labelledby":t,padding:n="none",divider:r="none",dividerWhenNarrow:i="inherit",hidden:l=!1,children:o,sx:u={}})=>{let s=(0,d.fd)(r)||"inherit"===i?r:{regular:r,narrow:i},c=(0,d.Vt)(s,"none"),m=(0,d.Vt)(l,!1),{rowGap:p}=a.useContext(P);return a.createElement(N.Z,{as:"header","aria-label":e,"aria-labelledby":t,hidden:m,sx:R()({width:"100%",marginBottom:D[p]},u)},a.createElement(N.Z,{sx:{padding:D[n]}},o),a.createElement(I,{variant:c,sx:{marginTop:D[p]}}))};j.displayName="Header",j.displayName="PageLayout.Header";let z={full:"100%",medium:"768px",large:"1012px",xlarge:"1280px"},X=({as:e="main","aria-label":t,"aria-labelledby":n,width:r="full",padding:i="none",hidden:l=!1,children:o,sx:u={}})=>{let s=(0,d.Vt)(l,!1),{contentTopRef:c,contentBottomRef:m}=a.useContext(P);return a.createElement(N.Z,{as:e,"aria-label":t,"aria-labelledby":n,sx:R()({display:s?"none":"flex",flexDirection:"column",order:Z.content,flexBasis:0,flexGrow:1,flexShrink:1,minWidth:1},u)},a.createElement(N.Z,{ref:c}),a.createElement(N.Z,{sx:{width:"100%",maxWidth:z[r],marginX:"auto",flexGrow:1,padding:D[i]}},o),a.createElement(N.Z,{ref:m}))};X.displayName="Content",X.displayName="PageLayout.Content";let T=e=>void 0!==e.default,F=e=>["small","medium","large"].includes(e),G={start:Z.paneStart,end:Z.paneEnd},J={small:["100%",null,"240px","256px"],medium:["100%",null,"256px","296px"],large:["100%",null,"256px","320px","336px"]},Y={small:256,medium:296,large:320},q=a.forwardRef(({"aria-label":e,"aria-labelledby":t,position:n="end",positionWhenNarrow:r="inherit",width:i="medium",minWidth:u=256,padding:p="none",resizable:g=!1,widthStorageKey:f="paneWidth",divider:h="none",dividerWhenNarrow:w="inherit",sticky:b=!1,offsetHeader:v=0,hidden:x=!1,children:y,id:E,sx:S={}},C)=>{let k=(0,d.fd)(n)||"inherit"===r?n:{regular:n,narrow:r},L=(0,d.Vt)(k,"end"),$=(0,d.fd)(h)||"inherit"===w?h:{regular:h,narrow:w},Z=(0,d.Vt)($,"none"),M=(0,d.Vt)(x,!1),{rowGap:O,columnGap:W,enableStickyPane:B,disableStickyPane:A}=a.useContext(P);a.useEffect(()=>{b?null==B||B(v):null==A||A()},[b,B,A,v]);let H=e=>F(e)?Y[e]:T(e)?Number(e.default.split("px")[0]):0,[j,z]=a.useState(()=>{let e;if(!s.N)return H(i);try{e=localStorage.getItem(f)}catch(t){e=null}return e&&!isNaN(Number(e))?Number(e):H(i)}),X=e=>{z(e);try{localStorage.setItem(f,e.toString())}catch(e){}},q=a.useRef(null);(0,o.z)(C,q);let[U,K]=a.useState(0),[Q,ee]=a.useState(0),et=(0,c.I)(q),en=a.useCallback(()=>{if(null!==q.current){let e=getComputedStyle(q.current).getPropertyValue("--pane-max-width-diff"),t=q.current.getBoundingClientRect().width,n=Number(e.split("px")[0]),r=window.innerWidth,a=r>n?r-n:r,i=Math.round(100*u/r);K(i);let l=Math.round(100*a/r);ee(l);let o=Math.round(100*t/r);ea(o.toString())}},[q,u]),[er,ea]=a.useState(""),[ei,el]=a.useState(""),eo=e=>{e.preventDefault();let t=Number(er);Number.isNaN(t)?t=Number(ei)||U:t>Q?t=Q:t<U&&(t=U),ea(t.toString()),el(t.toString()),X(t/100*window.innerWidth)},ed=(0,l.M)(E),eu={};return et&&(t?eu["aria-labelledby"]=t:e&&(eu["aria-label"]=e)),a.createElement(N.Z,{ref:en,sx:e=>R()({display:M?"none":"flex",order:G[L],width:"100%",marginX:0,..."end"===L?{flexDirection:"column",marginTop:D[O]}:{flexDirection:"column-reverse",marginBottom:D[O]},[`@media screen and (min-width: ${e.breakpoints[1]})`]:{width:"auto",marginY:"0 !important",...b?{position:"sticky",top:"number"==typeof v?`${v}px`:v,maxHeight:"var(--sticky-pane-height)"}:{},..."end"===L?{flexDirection:"row",marginLeft:D[W]}:{flexDirection:"row-reverse",marginRight:D[W]}}},S)},a.createElement(I,{variant:{narrow:Z,regular:"none"},sx:{["end"===L?"marginBottom":"marginTop"]:D[O]}}),a.createElement(_,{variant:{narrow:"none",regular:g?"line":Z},draggable:g,sx:{["end"===L?"marginRight":"marginLeft"]:D[W]},onDrag:e=>{let t="end"===L?-e:e;X(j+t)},onDragEnd:()=>{var e;let t=null===(e=q.current)||void 0===e?void 0:e.getBoundingClientRect();t&&X(t.width)},onDoubleClick:()=>X(H(i))}),a.createElement(N.Z,V({ref:q,style:{"--pane-width":`${j}px`},sx:e=>({"--pane-min-width":T(i)?i.min:`${u}px`,"--pane-max-width-diff":"511px","--pane-max-width":T(i)?i.max:"calc(100vw - var(--pane-max-width-diff))",width:g?["100%",null,"clamp(var(--pane-min-width), var(--pane-width), var(--pane-max-width))"]:F(i)?J[i]:i.default,padding:D[p],overflow:[null,null,"auto"],[`@media screen and (min-width: ${e.breakpoints[3]})`]:{"--pane-max-width-diff":"959px"}})},et&&{tabIndex:0,role:"region"},eu,E&&{id:ed}),g&&a.createElement(m.Z,null,a.createElement("form",{onSubmit:eo},a.createElement("label",{htmlFor:`${ed}-width-input`},"Pane width"),a.createElement("p",{id:`${ed}-input-hint`},"Use a value between ",U,"% and ",Q,"%"),a.createElement("input",{id:`${ed}-width-input`,"aria-describedby":`${ed}-input-hint`,name:"pane-width",inputMode:"numeric",pattern:"[0-9]*",value:er,autoCorrect:"off",autoComplete:"off",type:"text",onChange:e=>{ea(e.target.value)}}),a.createElement("button",{type:"submit"},"Change width"))),y))});q.displayName="PageLayout.Pane";let U=({"aria-label":e,"aria-labelledby":t,padding:n="none",divider:r="none",dividerWhenNarrow:i="inherit",hidden:l=!1,children:o,sx:u={}})=>{let s=(0,d.fd)(r)||"inherit"===i?r:{regular:r,narrow:i},c=(0,d.Vt)(s,"none"),m=(0,d.Vt)(l,!1),{rowGap:p}=a.useContext(P);return a.createElement(N.Z,{as:"footer","aria-label":e,"aria-labelledby":t,hidden:m,sx:R()({order:Z.footer,width:"100%",marginTop:D[p]},u)},a.createElement(I,{variant:c,sx:{marginBottom:D[p]}}),a.createElement(N.Z,{sx:{padding:D[n]}},o))};U.displayName="Footer",U.displayName="PageLayout.Footer";let K=Object.assign(O,{Header:j,Content:X,Pane:q,Footer:U})},11791:(e,t,n)=>{n.d(t,{fd:()=>d,Vt:()=>u,dq:()=>o});var r=n(67294),a=n(41632);function i(e,t){let n=(0,r.useContext)(l),[i,o]=r.useState(()=>void 0!==n[e]?n[e]:void 0!==t?t:!!a.N&&window.matchMedia(e).matches);return(0,r.useEffect)(()=>{void 0!==n[e]&&o(n[e])},[n,e]),(0,r.useEffect)(()=>{if(void 0!==n[e])return;function t(e){o(e.matches)}let r=window.matchMedia(e);return r.addEventListener?r.addEventListener("change",t):r.addListener(t),o(r.matches),()=>{r.addEventListener?r.removeEventListener("change",t):r.removeListener(t)}},[n,e]),i}let l=(0,r.createContext)({}),o={narrow:"(max-width: calc(768px - 0.02px))",regular:"(min-width: 768px)",wide:"(min-width: 1400px)"};function d(e){return"object"==typeof e&&Object.keys(e).some(e=>["narrow","regular","wide"].includes(e))}function u(e,t){let n=i(o.narrow,!1),r=i(o.regular,!1),a=i(o.wide,!1);return d(e)?n&&"narrow"in e?e.narrow:a&&"wide"in e?e.wide:r&&"regular"in e?e.regular:t:e}},54085:(e,t,n)=>{n.d(t,{I:()=>a});var r=n(67294);function a(e){let[t,n]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{if(null===e.current)return;let t=new ResizeObserver(e=>{for(let t of e)n(t.target.scrollHeight>t.target.clientHeight||t.target.scrollWidth>t.target.clientWidth)});return t.observe(e.current),()=>{t.disconnect()}},[e]),t}},41632:(e,t,n)=>{n.d(t,{N:()=>r});let r=!!("undefined"!=typeof window&&window.document&&window.document.createElement)},87691:(e,t,n)=>{function r(e){return e&&e!==document.body?a(e)?e:r(e.parentElement):null}function a(e){let t=e.scrollHeight>e.clientHeight;return t&&!(-1!==window.getComputedStyle(e).overflowY.indexOf("hidden"))}n.d(t,{J:()=>r})}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_PageLayout_PageLayout_js-fca0c7a79798.js.map