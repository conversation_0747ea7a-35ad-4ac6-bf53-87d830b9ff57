"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_Text_Text_js-node_modules_primer_react_lib-esm_Text-85a14b"],{50919:(e,t,o)=>{o.d(t,{h:()=>s});var i=o(67294),n=o(21413),a=o(7261),r=o(88216);function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let s=(0,i.forwardRef)(({sx:e=a.P,icon:t,...o},s)=>{let p=e,{size:c}=o;return null!==e&&Object.keys(e).length>0&&(p=(0,r.Z)({size:c},e)),i.createElement(n.X,l({icon:t,"data-component":"IconButton",sx:p,type:"button"},o,{ref:s}))})},74121:(e,t,o)=>{o.d(t,{Z:()=>c});var i=o(67294),n=o(15388),a=o(15173);function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let l={small:"16px",medium:"32px",large:"64px"};function s({size:e="medium",...t}){let o=l[e];return i.createElement("svg",r({height:o,width:o,viewBox:"0 0 16 16",fill:"none"},t),i.createElement("circle",{cx:"8",cy:"8",r:"7",stroke:"currentColor",strokeOpacity:"0.25",strokeWidth:"2",vectorEffect:"non-scaling-stroke"}),i.createElement("path",{d:"M15 8a7.002 7.002 0 00-7-7",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",vectorEffect:"non-scaling-stroke"}))}s.displayName="Spinner";let p=(0,n.ZP)(s).withConfig({displayName:"Spinner__StyledSpinner",componentId:"sc-1knt686-0"})(["@keyframes rotate-keyframes{100%{transform:rotate(360deg);}}animation:rotate-keyframes 1s linear infinite;",""],a.Z);p.displayName="Spinner";var c=p},97011:(e,t,o)=>{o.d(t,{Z:()=>l});var i=o(15388),n=o(42379),a=o(15173);let r=i.ZP.span.withConfig({displayName:"Text",componentId:"sc-17v1xeu-0"})(["",";",";",";"],n.l$,n.CW,a.Z);var l=r},51526:(e,t,o)=>{o.d(t,{Z:()=>U});var i=o(67294),n=o(14890),a=o(86010),r=o(72691),l=o(20682),s=o(78912),p=o(38490),c=o(42483),d=o(50919),f=o(9996),u=o.n(f);function m(){return(m=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let g={paddingTop:"2px",paddingRight:"4px",paddingBottom:"2px",paddingLeft:"4px",position:"relative",'&[data-component="IconButton"]':{width:"var(--inner-action-size)",height:"var(--inner-action-size)"},"@media (pointer: coarse)":{":after":{content:'""',position:"absolute",left:0,right:0,transform:"translateY(-50%)",top:"50%",minHeight:"44px"}}},h=({"aria-label":e,children:t,tooltipDirection:o})=>i.createElement(i.Fragment,null,e?i.createElement(p.Z,{"aria-label":e,direction:o,sx:{display:"inline-block"}},t):t),b=(0,i.forwardRef)(({"aria-label":e,tooltipDirection:t,children:o,icon:n,sx:a,variant:r="invisible",...l},f)=>{let b="invisible"===r?u()(g,a||{}):a||{};return(!n||e)&&(o||e)||console.warn("Use the `aria-label` prop to provide an accessible label for assistive technology"),i.createElement(c.Z,{as:"span",className:"TextInput-action",marginLeft:1,marginRight:1,lineHeight:"0"},n&&!o?i.createElement(p.Z,{direction:t,"aria-label":e},i.createElement(d.h,m({variant:r,type:"button",icon:n,size:"small",sx:b},l,{"aria-label":e,"aria-labelledby":void 0,ref:f}))):i.createElement(h,{"aria-label":e},i.createElement(s.z,m({variant:r,type:"button",sx:b},l,{ref:f}),o)))});var y=b,x=o(38535),v=o(66044);function w(){return(w=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let k=i.forwardRef(({icon:e,leadingVisual:t,trailingVisual:o,trailingAction:s,block:p,className:c,contrast:d,disabled:f,loading:u,loaderPosition:m="auto",monospace:g,validationStatus:h,sx:b,size:y,onFocus:k,onBlur:U,width:T,minWidth:Z,maxWidth:I,variant:S,type:E="text",...z},O)=>{let[j,P]=(0,i.useState)(!1),_=(0,v.i)(O),$=(0,a.Z)(c,"TextInput-wrapper"),B=u&&("leading"===m||Boolean(t&&"trailing"!==m)),N=u&&("trailing"===m||Boolean("auto"===m&&!t)),C=()=>{var e;null===(e=_.current)||void 0===e||e.focus()},V=(0,i.useCallback)(e=>{P(!0),k&&k(e)},[k]),W=(0,i.useCallback)(e=>{P(!1),U&&U(e)},[U]);return i.createElement(l.ZP,{block:p,className:$,validationStatus:h,contrast:d,disabled:f,monospace:g,sx:b,size:y,width:T,minWidth:Z,maxWidth:I,variant:S,hasLeadingVisual:Boolean(t||B),hasTrailingVisual:Boolean(o||N),hasTrailingAction:Boolean(s),isInputFocused:j,onClick:C,"aria-busy":Boolean(u)},e&&i.createElement(e,{className:"TextInput-icon"}),i.createElement(r.Z,{visualPosition:"leading",showLoadingIndicator:B,hasLoadingIndicator:"boolean"==typeof u},"string"!=typeof t&&(0,n.isValidElementType)(t)?i.createElement(t,null):t),i.createElement(x.Z,w({ref:_,disabled:f,onFocus:V,onBlur:W,type:E},z,{"data-component":"input"})),i.createElement(r.Z,{visualPosition:"trailing",showLoadingIndicator:N,hasLoadingIndicator:"boolean"==typeof u},"string"!=typeof o&&(0,n.isValidElementType)(o)?i.createElement(o,null):o),s)});k.displayName="TextInput";var U=Object.assign(k,{Action:y})},38490:(e,t,o)=>{o.d(t,{Z:()=>c});var i=o(86010),n=o(67294),a=o(15388),r=o(42379),l=o(15173);function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(e[i]=o[i])}return e}).apply(this,arguments)}let p=a.ZP.span.withConfig({displayName:"Tooltip__TooltipBase",componentId:"sc-uha8qm-0"})(["position:relative;display:inline-block;&::before{position:absolute;z-index:1000001;display:none;width:0px;height:0px;color:",";pointer-events:none;content:'';border:6px solid transparent;opacity:0;}&::after{position:absolute;z-index:1000000;display:none;padding:0.5em 0.75em;font:normal normal 11px/1.5 ",";-webkit-font-smoothing:subpixel-antialiased;color:",";text-align:center;text-decoration:none;text-shadow:none;text-transform:none;letter-spacing:normal;word-wrap:break-word;white-space:pre;pointer-events:none;content:attr(aria-label);background:",";border-radius:",";opacity:0;}@keyframes tooltip-appear{from{opacity:0;}to{opacity:1;}}&:hover,&:active,&:focus,&:focus-within{&::before,&::after{display:inline-block;text-decoration:none;animation-name:tooltip-appear;animation-duration:0.1s;animation-fill-mode:forwards;animation-timing-function:ease-in;animation-delay:0.4s;}}&.tooltipped-no-delay:hover,&.tooltipped-no-delay:active,&.tooltipped-no-delay:focus,&.tooltipped-no-delay:focus-within{&::before,&::after{animation-delay:0s;}}&.tooltipped-multiline:hover,&.tooltipped-multiline:active,&.tooltipped-multiline:focus,&.tooltipped-multiline:focus-within{&::after{display:table-cell;}}&.tooltipped-s,&.tooltipped-se,&.tooltipped-sw{&::after{top:100%;right:50%;margin-top:6px;}&::before{top:auto;right:50%;bottom:-7px;margin-right:-6px;border-bottom-color:",";}}&.tooltipped-se{&::after{right:auto;left:50%;margin-left:-",";}}&.tooltipped-sw::after{margin-right:-",";}&.tooltipped-n,&.tooltipped-ne,&.tooltipped-nw{&::after{right:50%;bottom:100%;margin-bottom:6px;}&::before{top:-7px;right:50%;bottom:auto;margin-right:-6px;border-top-color:",";}}&.tooltipped-ne{&::after{right:auto;left:50%;margin-left:-",";}}&.tooltipped-nw::after{margin-right:-",";}&.tooltipped-s::after,&.tooltipped-n::after{transform:translateX(50%);}&.tooltipped-w{&::after{right:100%;bottom:50%;margin-right:6px;transform:translateY(50%);}&::before{top:50%;bottom:50%;left:-7px;margin-top:-6px;border-left-color:",";}}&.tooltipped-e{&::after{bottom:50%;left:100%;margin-left:6px;transform:translateY(50%);}&::before{top:50%;right:-7px;bottom:50%;margin-top:-6px;border-right-color:",";}}&.tooltipped-multiline{&::after{width:max-content;max-width:250px;word-wrap:break-word;white-space:pre-line;border-collapse:separate;}&.tooltipped-s::after,&.tooltipped-n::after{right:auto;left:50%;transform:translateX(-50%);}&.tooltipped-w::after,&.tooltipped-e::after{right:100%;}}&.tooltipped-align-right-2::after{right:0;margin-right:0;}&.tooltipped-align-right-2::before{right:15px;}&.tooltipped-align-left-2::after{left:0;margin-left:0;}&.tooltipped-align-left-2::before{left:10px;}",";"],(0,r.U2)("colors.neutral.emphasisPlus"),(0,r.U2)("fonts.normal"),(0,r.U2)("colors.fg.onEmphasis"),(0,r.U2)("colors.neutral.emphasisPlus"),(0,r.U2)("radii.1"),(0,r.U2)("colors.neutral.emphasisPlus"),(0,r.U2)("space.3"),(0,r.U2)("space.3"),(0,r.U2)("colors.neutral.emphasisPlus"),(0,r.U2)("space.3"),(0,r.U2)("space.3"),(0,r.U2)("colors.neutral.emphasisPlus"),(0,r.U2)("colors.neutral.emphasisPlus"),l.Z);function c({direction:e="n",children:t,className:o,text:a,noDelay:r,align:l,wrap:c,...d}){let f=(0,i.Z)(o,`tooltipped-${e}`,l&&`tooltipped-align-${l}-2`,r&&"tooltipped-no-delay",c&&"tooltipped-multiline");return n.createElement(p,s({role:"tooltip","aria-label":a},d,{className:f}),t)}c.displayName="Tooltip",c.alignments=["left","right"],c.directions=["n","ne","e","se","s","sw","w","nw"]},72691:(e,t,o)=>{o.d(t,{Z:()=>l});var i=o(67294),n=o(42483),a=o(74121);let r=({children:e,hasLoadingIndicator:t,showLoadingIndicator:o,visualPosition:r})=>(e||t)&&("leading"!==r||e||o)?t?i.createElement("span",{className:"TextInput-icon"},i.createElement(n.Z,{display:"flex",position:"relative"},e&&i.createElement(n.Z,{sx:{visibility:o?"hidden":"visible"}},e),i.createElement(a.Z,{sx:e?{position:"absolute",top:0,height:"100%",maxWidth:"100%",visibility:o?"visible":"hidden",..."leading"===r?{left:0}:{right:0}}:{visibility:o?"visible":"hidden"},size:e?void 0:"small"}))):i.createElement("span",{className:"TextInput-icon"},e):null;r.displayName="TextInputInnerVisualSlot";var l=r},20682:(e,t,o)=>{o.d(t,{FD:()=>f,Qk:()=>c,ZP:()=>m});var i=o(15388),n=o(27999),a=o(42379),r=o(15173);let l=(0,n.bU)({variants:{small:{minHeight:"28px",px:2,py:"3px",fontSize:0,lineHeight:"20px"},large:{px:2,py:"10px",fontSize:3}}}),s=(0,n.bU)({prop:"size",variants:{small:{"--inner-action-size":"20px",minHeight:"28px",px:2,py:"3px",fontSize:0,lineHeight:"20px"},medium:{"--inner-action-size":"24px"},large:{"--inner-action-size":"28px",px:2,py:"10px",height:"40px"}}}),p="12px",c=p,d=(e,t)=>e?t&&(0,i.iv)(["border-color:",";outline:none;box-shadow:inset 0 0 0 1px ",";"],(0,a.U2)("colors.accent.fg"),(0,a.U2)("colors.accent.fg")):(0,i.iv)(["&:focus-within{border-color:",";outline:none;box-shadow:inset 0 0 0 1px ",";}"],(0,a.U2)("colors.accent.fg"),(0,a.U2)("colors.accent.fg")),f=i.ZP.span.withConfig({displayName:"TextInputWrapper__TextInputBaseWrapper",componentId:"sc-1mqhpbi-0"})(["font-size:",";line-height:20px;color:",";vertical-align:middle;background-color:",";border:1px solid var(--control-borderColor-rest,",");border-radius:",";outline:none;box-shadow:",";display:inline-flex;align-items:stretch;min-height:32px;input,textarea{cursor:text;}select{cursor:pointer;}&::placeholder{color:",";}"," > textarea{padding:",";}"," "," "," "," "," "," @media (min-width:","){font-size:",";}"," "," "," "," "," ",";"],(0,a.U2)("fontSizes.1"),(0,a.U2)("colors.fg.default"),(0,a.U2)("colors.canvas.default"),(0,a.U2)("colors.border.default"),(0,a.U2)("radii.2"),(0,a.U2)("shadows.primer.shadow.inset"),(0,a.U2)("colors.fg.subtle"),e=>d(Boolean(e.hasTrailingAction),Boolean(e.isInputFocused)),p,e=>e.contrast&&(0,i.iv)(["background-color:",";"],(0,a.U2)("colors.canvas.inset")),e=>e.disabled&&(0,i.iv)(["color:",";background-color:",";border-color:",";input,textarea,select{cursor:not-allowed;}"],(0,a.U2)("colors.primer.fg.disabled"),(0,a.U2)("colors.input.disabledBg"),(0,a.U2)("colors.border.default")),e=>e.monospace&&(0,i.iv)(["font-family:",";"],(0,a.U2)("fonts.mono")),e=>"error"===e.validationStatus&&(0,i.iv)(["border-color:",";",""],(0,a.U2)("colors.danger.emphasis"),d(Boolean(e.hasTrailingAction),Boolean(e.isInputFocused))),e=>"success"===e.validationStatus&&(0,i.iv)(["border-color:",";"],(0,a.U2)("colors.success.emphasis")),e=>e.block&&(0,i.iv)(["width:100%;display:flex;align-self:stretch;"]),(0,a.U2)("breakpoints.1"),(0,a.U2)("fontSizes.1"),n.bf,n.ih,n.kk,l,s,r.Z),u=(0,i.ZP)(f).withConfig({displayName:"TextInputWrapper",componentId:"sc-1mqhpbi-1"})(["background-repeat:no-repeat;background-position:right 8px center;& > :not(:last-child){margin-right:",";}.TextInput-icon,.TextInput-action{align-self:center;color:",";flex-shrink:0;}"," "," ",";"],(0,a.U2)("space.2"),(0,a.U2)("colors.fg.muted"),e=>(0,i.iv)(["padding-left:",";padding-right:",";> input,> select{padding-left:",";padding-right:",";}"],e.hasLeadingVisual?c:0,e.hasTrailingVisual&&!e.hasTrailingAction?c:0,e.hasLeadingVisual?0:c,e.hasTrailingVisual||e.hasTrailingAction?0:c),e=>"warning"===e.validationStatus&&(0,i.iv)(["border-color:",";",""],(0,a.U2)("colors.attention.emphasis"),d(Boolean(e.hasTrailingAction),Boolean(e.isInputFocused))),r.Z);var m=u},38535:(e,t,o)=>{o.d(t,{Z:()=>r});var i=o(15388),n=o(15173);let a=i.ZP.input.withConfig({displayName:"UnstyledTextInput",componentId:"sc-14ypya-0"})(["border:0;font-size:inherit;font-family:inherit;background-color:transparent;-webkit-appearance:none;color:inherit;width:100%;&:focus{outline:0;}",";"],n.Z);var r=a},91615:(e,t)=>{/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var o,i,n=Symbol.for("react.element"),a=Symbol.for("react.portal"),r=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),p=Symbol.for("react.provider"),c=Symbol.for("react.context"),d=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),m=Symbol.for("react.suspense_list"),g=Symbol.for("react.memo"),h=Symbol.for("react.lazy"),b=Symbol.for("react.offscreen");function y(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case r:case s:case l:case u:case m:return e;default:switch(e=e&&e.$$typeof){case d:case c:case f:case h:case g:case p:return e;default:return t}}case a:return t}}}i=Symbol.for("react.module.reference"),o=c,o=p,o=n,o=f,o=r,o=h,o=g,o=a,o=s,o=l,o=u,o=m,o=function(){return!1},o=function(){return!1},o=function(e){return y(e)===c},o=function(e){return y(e)===p},o=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===n},o=function(e){return y(e)===f},o=function(e){return y(e)===r},o=function(e){return y(e)===h},o=function(e){return y(e)===g},o=function(e){return y(e)===a},o=function(e){return y(e)===s},o=function(e){return y(e)===l},o=function(e){return y(e)===u},o=function(e){return y(e)===m},t.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===s||e===l||e===u||e===m||e===b||"object"==typeof e&&null!==e&&(e.$$typeof===h||e.$$typeof===g||e.$$typeof===p||e.$$typeof===c||e.$$typeof===f||e.$$typeof===i||void 0!==e.getModuleId)},o=y},14890:(e,t,o)=>{e.exports=o(91615)},86010:(e,t,o)=>{function i(e){var t,o,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(o=i(e[t]))&&(n&&(n+=" "),n+=o);else for(t in e)e[t]&&(n&&(n+=" "),n+=t)}return n}function n(){for(var e,t,o=0,n="";o<arguments.length;)(e=arguments[o++])&&(t=i(e))&&(n&&(n+=" "),n+=t);return n}o.d(t,{W:()=>n,Z:()=>a});let a=n}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_Text_Text_js-node_modules_primer_react_lib-esm_Text-85a14b-64a5a08eb027.js.map