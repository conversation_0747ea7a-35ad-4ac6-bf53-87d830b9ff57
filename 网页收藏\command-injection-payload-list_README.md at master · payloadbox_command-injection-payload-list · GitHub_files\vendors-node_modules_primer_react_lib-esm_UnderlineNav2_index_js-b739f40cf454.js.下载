"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_primer_react_lib-esm_UnderlineNav2_index_js"],{21921:(e,t,n)=>{n.d(t,{J:()=>z});var a=n(67294),l=n(15173);let r=(0,a.createContext)({theme:{},setChildrenWidth:()=>null,setNoIconChildrenWidth:()=>null,loadingCounters:!1,iconsVisible:!0});var i=n(8930),o=n(8386),s=n(53670);let c={alignItems:"center",display:"inline-flex",marginRight:2},d={marginLeft:2,display:"flex",alignItems:"center"},u=e=>({display:"flex",paddingX:3,justifyContent:"flex-start",borderBottom:"1px solid",borderBottomColor:`${null==e?void 0:e.colors.border.muted}`,align:"row",alignItems:"center",minHeight:"48px"}),p={display:"flex",listStyle:"none",whiteSpace:"nowrap",paddingY:0,paddingX:0,margin:0,marginBottom:"-1px",alignItems:"center",gap:"8px",position:"relative"},m=e=>({display:"inline-block",borderLeft:"1px solid",width:"1px",borderLeftColor:`${null==e?void 0:e.colors.border.muted}`,marginRight:1,height:"24px"}),f={margin:0,border:0,background:"transparent",fontWeight:"normal",boxShadow:"none",paddingY:1,paddingX:2,'& > span[data-component="trailingIcon"]':{marginLeft:0}},g=(e,t)=>({position:"relative",display:"inline-flex",color:"fg.default",textAlign:"center",textDecoration:"none",lineHeight:"calc(20/14)",'& span[data-component="icon"]':{color:"fg.muted"},borderRadius:2,fontSize:1,paddingX:2,paddingY:"calc((2rem - 1.25rem) / 2)","@media (hover:hover)":{"&:hover ":{backgroundColor:null==e?void 0:e.colors.neutral.muted,transition:"background .12s ease-out",textDecoration:"none"}},"&:focus":{outline:"2px solid transparent","&":{boxShadow:`inset 0 0 0 2px ${null==e?void 0:e.colors.accent.fg}`},"&:not(:focus-visible)":{boxShadow:"none"}},"&:focus-visible":{outline:"2px solid transparent",boxShadow:`inset 0 0 0 2px ${null==e?void 0:e.colors.accent.fg}`},"& span[data-content]::before":{content:"attr(data-content)",display:"block",height:0,fontWeight:"600",visibility:"hidden",whiteSpace:"nowrap"},"&::after":{position:"absolute",right:"50%",bottom:"calc(50% - 25px)",width:"100%",height:2,content:'""',backgroundColor:Boolean(t)&&"false"!==t?null==e?void 0:e.colors.primer.border.active:"transparent",borderRadius:0,transform:"translate(50%, -50%)"},"@media (forced-colors: active)":{"::after":{backgroundColor:Boolean(t)&&t?"LinkText":"transparent"}}}),h={"& > span":{display:"none"},textDecoration:"none"},b={position:"absolute",zIndex:1,top:"90%",right:"0",boxShadow:"0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)",borderRadius:"12px",backgroundColor:"canvas.overlay",listStyle:"none",minWidth:"192px",maxWidth:"640px"};var x=n(15388),v=n(78912),y=n(89283),k=n(35048),C=n(24178),w=n(44288),E=n(52516),R=n(7261),Z=n(42379);let I=(0,x.F4)(["from{opacity:1;}to{opacity:0.2;}"]),S=x.ZP.span.withConfig({displayName:"LoadingCounter",componentId:"sc-ouonic-0"})(["animation:"," 1.2s ease-in-out infinite alternate;background-color:",";border-color:",";width:1.5rem;height:1rem;display:inline-block;border-radius:20px;"],I,(0,Z.U2)("colors.neutral.muted"),(0,Z.U2)("colors.border.default"));var N=n(42483),B=n(54901),P=n(9996),_=n.n(P);function j(){return(j=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}let L=x.ZP.ul.withConfig({displayName:"UnderlineNav__NavigationList",componentId:"sc-3wwkh2-0"})(["",";"],l.Z),O=x.ZP.li.withConfig({displayName:"UnderlineNav__MoreMenuListItem",componentId:"sc-3wwkh2-1"})(["display:flex;align-items:center;height:","px;"],45),A=(e,t,n,a,l,r)=>{let i=!0;0===a.length&&r({items:n,menuItems:[]},i);let o=W(a,e),s=W(l,e),c=W(l,e,t||86),d=[],u=[];if(n.length<=o)d.push(...n);else if(n.length<=s)i=!1,d.push(...n);else{i=!1;let e=n.length-c,t=1===e?c-1:c;for(let[e,a]of n.entries())if(e<t)d.push(a);else{let e=a.props["aria-current"],n=Boolean(e)&&"false"!==e;if(n&&t>0){let e=t-1,n=d.splice(e,1,a)[0];u.push(n)}else u.push(a)}}r({items:d,menuItems:u},i)},U=e=>a.Children.toArray(e).filter(e=>a.isValidElement(e)),W=(e,t,n=0)=>{let a=t-n,l=e.length,r=0;for(let[t,n]of e.entries())if((r=r+n.width+8)>a){l=t;break}return l},$=(0,a.forwardRef)(({as:e="nav","aria-label":t,sx:n=R.P,loadingCounters:l=!1,children:c},d)=>{let g=(0,a.useRef)(null),x=null!=d?d:g,Z=(0,a.useRef)(null),I=(0,a.useRef)(null),P=(0,a.useRef)(null),W=a.useRef(null),$=(0,w.M)(),{theme:D}=(0,o.Fg)(),[M,F]=(0,a.useState)(!1),[X,z]=(0,a.useState)(!0),[T,V]=(0,a.useState)([]),[Y,H]=(0,a.useState)([]),J=U(c),[K,q]=(0,a.useState)({items:J,menuItems:[]}),G=K.items.map(e=>{var t;return null!==(t=J.find(t=>t.key===e.key))&&void 0!==t?t:e}),Q=K.menuItems.map(e=>{var t;return null!==(t=J.find(t=>t.key===e.key))&&void 0!==t?t:e}),ee=0===K.items.length;function et(e){var t,n;return null!==(t=null===(n=Y.find(t=>t.text===e))||void 0===n?void 0:n.width)&&void 0!==t?t:0}let en=(e,t,n,a)=>{var l,r;let i=et(e.props.children),o=x.current.getBoundingClientRect().width-(null!==(l=null===(r=Z.current)||void 0===r?void 0:r.getBoundingClientRect().width)&&void 0!==l?l:0),s=ea(i,o),c=K.items.length-1-s,d=[...K.items].slice(0,c),u=[...d,e],p=[...K.items].slice(c),m=[...Q];m.splice(t,1,...p),a({items:u,menuItems:m},!1)};function ea(e,t){let n=0,a=0;for(let[l,r]of[...K.items].reverse().entries())if(e<(n+=et(r.props.children))+t){a=l;break}return a}let el=(0,a.useCallback)((e,t)=>{q(e),z(t)},[]),er=(0,a.useCallback)(e=>{V(t=>{let n=[...t,e];return n})},[]),ei=(0,a.useCallback)(e=>{H(t=>{let n=[...t,e];return n})},[]),eo=a.useCallback(()=>{F(!1)},[F]),es=a.useCallback(()=>{var e;null===(e=P.current)||void 0===e||e.focus()},[]),ec=(0,a.useCallback)(e=>{e.defaultPrevented||0!==e.button||F(e=>!e)},[]);return(0,k.o)(e=>{M&&(e.preventDefault(),eo(),es())},[M]),(0,C.t)({onClickOutside:eo,containerRef:W,ignoreClickRefs:[P]}),(0,i.y)(e=>{var t,n;let a=e[0].contentRect.width,l=null!==(t=null===(n=I.current)||void 0===n?void 0:n.getBoundingClientRect().width)&&void 0!==t?t:0;0!==a&&A(a,l,J,T,Y,el)},x),a.createElement(r.Provider,{value:{theme:D,setChildrenWidth:er,setNoIconChildrenWidth:ei,loadingCounters:l,iconsVisible:X}},t&&a.createElement(s.Z,{as:"h2"},`${t} navigation`),a.createElement(N.Z,{as:e,sx:_()(u(D),n),"aria-label":t,ref:x},a.createElement(L,{sx:p,ref:Z,role:"list"},G,Q.length>0&&a.createElement(O,{ref:I},!ee&&a.createElement(N.Z,{sx:m(D)}),a.createElement(v.z,{ref:P,sx:f,"aria-controls":$,"aria-expanded":M,onClick:ec,trailingAction:y.AS7},a.createElement(N.Z,{as:"span"},ee?a.createElement(a.Fragment,null,a.createElement(s.Z,{as:"span"},`${t}`,"\xa0"),"Menu"):a.createElement(a.Fragment,null,"More",a.createElement(s.Z,{as:"span"},"\xa0",`${t} items`)))),a.createElement(E.S,{selectionVariant:"single",ref:W,id:$,sx:b,style:{display:M?"block":"none"}},Q.map((e,t)=>{let{children:n,counter:r,"aria-current":i,onSelect:o,...s}=e.props;if(Boolean(i)&&"false"!==i){let n=new MouseEvent("click");ee||en(e,t,n,el)}return a.createElement(E.S.LinkItem,j({key:n,sx:h,onClick:n=>{ee||en(e,t,n,el),eo(),es(),"function"==typeof o&&o(n)}},s),a.createElement(N.Z,{as:"span",sx:{display:"flex",alignItems:"center",justifyContent:"space-between"}},n,l?a.createElement(S,null):void 0!==r&&a.createElement(N.Z,{as:"span","data-component":"counter"},a.createElement(B.Z,null,r))))}))))))});$.displayName="UnderlineNav";var D=n(69848),M=n(73290);function F(){return(F=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e}).apply(this,arguments)}let X=(0,a.forwardRef)(({sx:e=R.P,as:t="a",href:n="#",children:l,counter:i,onSelect:o,"aria-current":s,icon:u,...p},m)=>{let f=(0,a.useRef)(null),h=null!=m?m:f,{theme:b,setChildrenWidth:x,setNoIconChildrenWidth:v,loadingCounters:y,iconsVisible:k}=(0,a.useContext)(r);(0,D.Z)(()=>{if(h.current){let e=h.current.getBoundingClientRect(),t=Array.from(h.current.children).find(e=>"icon"===e.getAttribute("data-component")),n=Array.from(h.current.children).find(e=>"text"===e.getAttribute("data-component")),a=n.textContent,l=t?t.getBoundingClientRect().width+Number(getComputedStyle(t).marginRight.slice(0,-2))+Number(getComputedStyle(t).marginLeft.slice(0,-2)):0;x({text:a,width:e.width}),v({text:a,width:e.width-l})}},[h,x,v]);let C=a.useCallback(e=>{" "!==e.key&&"Enter"!==e.key||e.defaultPrevented||"function"!=typeof o||o(e)},[o]),w=a.useCallback(e=>{e.defaultPrevented||"function"!=typeof o||o(e)},[o]);return a.createElement(N.Z,{as:"li",sx:{display:"flex",flexDirection:"column",alignItems:"center"}},a.createElement(M.Z,F({ref:h,as:t,href:n,"aria-current":s,onKeyDown:C,onClick:w,sx:_()(g(b,s),e)},p),k&&u&&a.createElement(N.Z,{as:"span","data-component":"icon",sx:c},a.createElement(u,null)),l&&a.createElement(N.Z,{as:"span","data-component":"text","data-content":l,sx:Boolean(s)&&"false"!==s?{fontWeight:600}:{}},l),y?a.createElement(N.Z,{as:"span","data-component":"counter",sx:d},a.createElement(S,null)):void 0!==i&&a.createElement(N.Z,{as:"span","data-component":"counter",sx:d},a.createElement(B.Z,null,i))))});X.displayName="UnderlineNavItem";let z=Object.assign($,{Item:X})}}]);
//# sourceMappingURL=vendors-node_modules_primer_react_lib-esm_UnderlineNav2_index_js-7be4e2447f55.js.map