"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_react-router-dom_dist_index_js"],{79655:(e,t,n)=>{n.d(t,{OL:()=>E,aj:()=>R,gs:()=>S,lr:()=>j,rU:()=>k,yq:()=>O});var r,o,a=n(67294),i=n(89250),l=n(12599);/**
 * React Router DOM v6.13.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function s(e,t){if(null==e)return{};var n,r,o={},a=Object.keys(e);for(r=0;r<a.length;r++)n=a[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}let c="application/x-www-form-urlencoded";function f(e){return null!=e&&"string"==typeof e.tagName}function d(e){return f(e)&&"button"===e.tagName.toLowerCase()}function p(e){return f(e)&&"form"===e.tagName.toLowerCase()}function h(e){return f(e)&&"input"===e.tagName.toLowerCase()}function m(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function w(e,t){return 0===e.button&&(!t||"_self"===t)&&!m(e)}function v(e){return void 0===e&&(e=""),new URLSearchParams("string"==typeof e||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(e=>[n,e]):[[n,r]])},[]))}function y(e,t){let n=v(e);if(t)for(let e of t.keys())n.has(e)||t.getAll(e).forEach(t=>{n.append(e,t)});return n}let g=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset"],b=["aria-current","caseSensitive","className","end","style","to","children"];function R(e,t){return(0,l.p7)({basename:null==t?void 0:t.basename,future:u({},null==t?void 0:t.future,{v7_prependBasename:!0}),history:(0,l.lX)({window:null==t?void 0:t.window}),hydrationData:(null==t?void 0:t.hydrationData)||C(),routes:e,mapRouteProperties:i.us}).initialize()}function C(){var e;let t=null==(e=window)?void 0:e.__staticRouterHydrationData;return t&&t.errors&&(t=u({},t,{errors:L(t.errors)})),t}function L(e){if(!e)return null;let t=Object.entries(e),n={};for(let[e,r]of t)if(r&&"RouteErrorResponse"===r.__type)n[e]=new l.iQ(r.status,r.statusText,r.data,!0===r.internal);else if(r&&"Error"===r.__type){let t=Error(r.message);t.stack="",n[e]=t}else n[e]=r;return n}let U="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,_=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,k=a.forwardRef(function(e,t){let n,{onClick:r,relative:o,reloadDocument:c,replace:f,state:d,target:p,to:h,preventScrollReset:m}=e,w=s(e,g),{basename:v}=a.useContext(i.Us),y=!1;if("string"==typeof h&&_.test(h)&&(n=h,U))try{let e=new URL(window.location.href),t=new URL(h.startsWith("//")?e.protocol+h:h),n=(0,l.Zn)(t.pathname,v);t.origin===e.origin&&null!=n?h=n+t.search+t.hash:y=!0}catch(e){}let b=(0,i.oQ)(h,{relative:o}),R=S(h,{replace:f,state:d,target:p,preventScrollReset:m,relative:o});return a.createElement("a",u({},w,{href:n||b,onClick:y||c?r:function(e){r&&r(e),e.defaultPrevented||R(e)},ref:t,target:p}))}),E=a.forwardRef(function(e,t){let n,{"aria-current":r="page",caseSensitive:o=!1,className:l="",end:c=!1,style:f,to:d,children:p}=e,h=s(e,b),m=(0,i.WU)(d,{relative:h.relative}),w=(0,i.TH)(),v=a.useContext(i.FR),{navigator:y}=a.useContext(i.Us),g=y.encodeLocation?y.encodeLocation(m).pathname:m.pathname,R=w.pathname,C=v&&v.navigation&&v.navigation.location?v.navigation.location.pathname:null;o||(R=R.toLowerCase(),C=C?C.toLowerCase():null,g=g.toLowerCase());let L=R===g||!c&&R.startsWith(g)&&"/"===R.charAt(g.length),U=null!=C&&(C===g||!c&&C.startsWith(g)&&"/"===C.charAt(g.length));n="function"==typeof l?l({isActive:L,isPending:U}):[l,L?"active":null,U?"pending":null].filter(Boolean).join(" ");let _="function"==typeof f?f({isActive:L,isPending:U}):f;return a.createElement(k,u({},h,{"aria-current":L?r:void 0,className:n,ref:t,style:_,to:d}),"function"==typeof p?p({isActive:L,isPending:U}):p)});function S(e,t){let{target:n,replace:r,state:o,preventScrollReset:u,relative:s}=void 0===t?{}:t,c=(0,i.s0)(),f=(0,i.TH)(),d=(0,i.WU)(e,{relative:s});return a.useCallback(t=>{if(w(t,n)){t.preventDefault();let n=void 0!==r?r:(0,l.Ep)(f)===(0,l.Ep)(d);c(e,{replace:n,state:o,preventScrollReset:u,relative:s})}},[f,c,d,r,o,n,e,u,s])}function j(e){let t=a.useRef(v(e)),n=a.useRef(!1),r=(0,i.TH)(),o=a.useMemo(()=>y(r.search,n.current?null:t.current),[r.search]),l=(0,i.s0)(),u=a.useCallback((e,t)=>{let r=v("function"==typeof e?e(o):e);n.current=!0,l("?"+r,t)},[l,o]);return[o,u]}!function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmitImpl="useSubmitImpl",e.UseFetcher="useFetcher"}(r||(r={})),function(e){e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(o||(o={}));function O(e,t){let{capture:n}=t||{};a.useEffect(()=>{let t=null!=n?{capture:n}:void 0;return window.addEventListener("beforeunload",e,t),()=>{window.removeEventListener("beforeunload",e,t)}},[e,n])}}}]);
//# sourceMappingURL=vendors-node_modules_react-router-dom_dist_index_js-fe487f84b8f8.js.map