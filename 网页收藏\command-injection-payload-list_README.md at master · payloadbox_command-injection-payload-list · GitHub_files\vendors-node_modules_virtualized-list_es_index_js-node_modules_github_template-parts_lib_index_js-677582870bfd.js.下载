"use strict";(globalThis.webpackChunk=globalThis.webpackChunk||[]).push([["vendors-node_modules_virtualized-list_es_index_js-node_modules_github_template-parts_lib_index_js"],{6570:(t,e,n)=>{n.d(e,{Z:()=>p});var i=n(39492);function o(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}var r="start",s="center",a="end",h=function(){function t(e){var n=e.itemCount,i=e.itemSizeGetter,r=e.estimatedItemSize;o(this,t),this._itemSizeGetter=i,this._itemCount=n,this._estimatedItemSize=r,this._itemSizeAndPositionData={},this._lastMeasuredIndex=-1}return t.prototype.getLastMeasuredIndex=function(){return this._lastMeasuredIndex},t.prototype.getSizeAndPositionForIndex=function(t){if(t<0||t>=this._itemCount)throw Error("Requested index "+t+" is outside of range 0.."+this._itemCount);if(t>this._lastMeasuredIndex){for(var e=this.getSizeAndPositionOfLastMeasuredItem(),n=e.offset+e.size,i=this._lastMeasuredIndex+1;i<=t;i++){var o=this._itemSizeGetter({index:i});if(null==o||isNaN(o))throw Error("Invalid size returned for index "+i+" of value "+o);this._itemSizeAndPositionData[i]={offset:n,size:o},n+=o}this._lastMeasuredIndex=t}return this._itemSizeAndPositionData[t]},t.prototype.getSizeAndPositionOfLastMeasuredItem=function(){return this._lastMeasuredIndex>=0?this._itemSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}},t.prototype.getTotalSize=function(){var t=this.getSizeAndPositionOfLastMeasuredItem();return t.offset+t.size+(this._itemCount-this._lastMeasuredIndex-1)*this._estimatedItemSize},t.prototype.getUpdatedOffsetForIndex=function(t){var e=t.align,n=t.containerSize,i=t.targetIndex;if(n<=0)return 0;var o=this.getSizeAndPositionForIndex(i),h=o.offset,l=h-n+o.size,d=void 0;switch(void 0===e?r:e){case a:d=l;break;case s:d=h-(n-o.size)/2;break;default:d=h}return Math.max(0,Math.min(this.getTotalSize()-n,d))},t.prototype.getVisibleRange=function(t){var e=t.containerSize,n=t.offset,i=t.overscanCount;if(0===this.getTotalSize())return{};var o=n+e,r=this._findNearestItem(n),s=r,a=this.getSizeAndPositionForIndex(r);for(n=a.offset+a.size;n<o&&s<this._itemCount-1;)s++,n+=this.getSizeAndPositionForIndex(s).size;return i&&(r=Math.max(0,r-i),s=Math.min(s+i,this._itemCount)),{start:r,stop:s}},t.prototype.resetItem=function(t){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,t-1)},t.prototype._binarySearch=function(t){for(var e=t.low,n=t.high,i=t.offset,o=void 0,r=void 0;e<=n;){if(o=e+Math.floor((n-e)/2),(r=this.getSizeAndPositionForIndex(o).offset)===i)return o;r<i?e=o+1:r>i&&(n=o-1)}if(e>0)return e-1},t.prototype._exponentialSearch=function(t){for(var e=t.index,n=t.offset,i=1;e<this._itemCount&&this.getSizeAndPositionForIndex(e).offset<n;)e+=i,i*=2;return this._binarySearch({high:Math.min(e,this._itemCount-1),low:Math.floor(e/2),offset:n})},t.prototype._findNearestItem=function(t){if(isNaN(t))throw Error("Invalid offset "+t+" specified");t=Math.max(0,t);var e=this.getSizeAndPositionOfLastMeasuredItem(),n=Math.max(0,this._lastMeasuredIndex);return e.offset>=t?this._binarySearch({high:n,low:0,offset:t}):this._exponentialSearch({index:n,offset:t})},t}();function l(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}var d="position:relative; overflow:hidden; width:100%; min-height:100%; will-change: transform;",u="position:absolute; top:0; left:0; height:100%; width:100%; overflow:visible;",p=function(){function t(e,n){var i=this;l(this,t),this.getRowHeight=function(t){var e=t.index,n=i.options.rowHeight;return"function"==typeof n?n(e):Array.isArray(n)?n[e]:n},this.container=e,this.options=n,this.state={},this._initializeSizeAndPositionManager(n.rowCount),this.render=this.render.bind(this),this.handleScroll=this.handleScroll.bind(this),this.componentDidMount()}return t.prototype.componentDidMount=function(){var t=this,e=this.options,n=e.onMount,i=e.initialScrollTop,o=e.initialIndex,r=e.height,s=i||null!=o&&this.getRowOffset(o)||0,a=this.inner=document.createElement("div"),h=this.content=document.createElement("div");a.setAttribute("style",d),h.setAttribute("style",u),a.appendChild(h),this.container.appendChild(a),this.setState({offset:s,height:r},function(){s&&(t.container.scrollTop=s),t.container.addEventListener("scroll",t.handleScroll),"function"==typeof n&&n()})},t.prototype._initializeSizeAndPositionManager=function(t){this._sizeAndPositionManager=new h({itemCount:t,itemSizeGetter:this.getRowHeight,estimatedItemSize:this.options.estimatedRowHeight||100})},t.prototype.setState=function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments[1];this.state=Object.assign(this.state,e),requestAnimationFrame(function(){t.render(),"function"==typeof n&&n()})},t.prototype.resize=function(t,e){this.setState({height:t},e)},t.prototype.handleScroll=function(t){var e=this.options.onScroll,n=this.container.scrollTop;this.setState({offset:n}),"function"==typeof e&&e(n,t)},t.prototype.getRowOffset=function(t){return this._sizeAndPositionManager.getSizeAndPositionForIndex(t).offset},t.prototype.scrollToIndex=function(t,e){var n=this.state.height,i=this._sizeAndPositionManager.getUpdatedOffsetForIndex({align:e,containerSize:n,targetIndex:t});this.container.scrollTop=i},t.prototype.setRowCount=function(t){this._initializeSizeAndPositionManager(t),this.render()},t.prototype.onRowsRendered=function(t){var e=this.options.onRowsRendered;"function"==typeof e&&e(t)},t.prototype.destroy=function(){this.container.removeEventListener("scroll",this.handleScroll),this.container.innerHTML=""},t.prototype.render=function(){for(var t=this.options,e=t.overscanCount,n=t.renderRow,o=this.state,r=o.height,s=o.offset,a=this._sizeAndPositionManager.getVisibleRange({containerSize:r,offset:void 0===s?0:s,overscanCount:e}),h=a.start,l=a.stop,d=document.createDocumentFragment(),u=h;u<=l;u++)d.appendChild(n(u));this.inner.style.height=this._sizeAndPositionManager.getTotalSize()+"px",this.content.style.top=this.getRowOffset(h)+"px",(0,i.Z)(this.content,d,{childrenOnly:!0,getNodeKey:function(t){return t.nodeIndex}}),this.onRowsRendered({startIndex:h,stopIndex:l})},t}();function f(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}function c(t,e){if(!t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&("object"==typeof e||"function"==typeof e)?e:t}function m(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var g=function(t){function e(){return f(this,e),c(this,t.apply(this,arguments))}return m(e,t),e.prototype.onRowsRendered=function(t){var e=this,n=t.startIndex,i=t.stopIndex,o=this.options,r=o.isRowLoaded,s=o.loadMoreRows,a=o.minimumBatchSize,h=o.rowCount,l=void 0===h?0:h,d=o.threshold,u=void 0===d?15:d;v({isRowLoaded:r,minimumBatchSize:void 0===a?10:a,rowCount:l,startIndex:Math.max(0,n-u),stopIndex:Math.min(l-1,i+u)}).forEach(function(t){var o=s(t);o&&o.then(function(){x({lastRenderedStartIndex:n,lastRenderedStopIndex:i,startIndex:t.startIndex,stopIndex:t.stopIndex})&&e.render()})})},e}(p);function x(t){var e=t.lastRenderedStartIndex,n=t.lastRenderedStopIndex,i=t.startIndex,o=t.stopIndex;return!(i>n||o<e)}function v(t){for(var e=t.isRowLoaded,n=t.minimumBatchSize,i=t.rowCount,o=t.startIndex,r=t.stopIndex,s=[],a=null,h=null,l=o;l<=r;l++)e(l)?null!==h&&(s.push({startIndex:a,stopIndex:h}),a=h=null):(h=l,null===a&&(a=l));if(null!==h){for(var d=Math.min(Math.max(h,a+n-1),i-1),u=h+1;u<=d&&!e({index:u});u++)h=u;s.push({startIndex:a,stopIndex:h})}if(s.length)for(var p=s[0];p.stopIndex-p.startIndex+1<n&&p.startIndex>0;){var f=p.startIndex-1;if(e({index:f}))break;p.startIndex=f}return s}},69567:(t,e,n)=>{function*i(t){let e="",n=0,i=!1;for(let o=0;o<t.length;o+=1)"{"!==t[o]||"{"!==t[o+1]||"\\"===t[o-1]||i?"}"===t[o]&&"}"===t[o+1]&&"\\"!==t[o-1]&&i&&(i=!1,yield{type:"part",start:n,end:o+2,value:e.slice(2).trim()},e="",o+=2,n=o):(i=!0,e&&(yield{type:"string",start:n,end:o,value:e}),e="{{",n=o,o+=2),e+=t[o]||"";e&&(yield{type:"string",start:n,end:t.length,value:e})}n.d(e,{R:()=>TemplateInstance,XK:()=>x});var o,r,s,a,h,l=function(t,e,n){if(!e.has(t))throw TypeError("attempted to set private field on non-instance");return e.set(t,n),n},d=function(t,e){if(!e.has(t))throw TypeError("attempted to get private field on non-instance");return e.get(t)};let AttributeTemplatePart=class AttributeTemplatePart{constructor(t,e){this.expression=e,o.set(this,void 0),r.set(this,""),l(this,o,t),d(this,o).updateParent("")}get attributeName(){return d(this,o).attr.name}get attributeNamespace(){return d(this,o).attr.namespaceURI}get value(){return d(this,r)}set value(t){l(this,r,t||""),d(this,o).updateParent(t)}get element(){return d(this,o).element}get booleanValue(){return d(this,o).booleanValue}set booleanValue(t){d(this,o).booleanValue=t}};o=new WeakMap,r=new WeakMap;let AttributeValueSetter=class AttributeValueSetter{constructor(t,e){this.element=t,this.attr=e,this.partList=[]}get booleanValue(){return this.element.hasAttributeNS(this.attr.namespaceURI,this.attr.name)}set booleanValue(t){if(1!==this.partList.length)throw new DOMException("Operation not supported","NotSupportedError");this.partList[0].value=t?"":null}append(t){this.partList.push(t)}updateParent(t){if(1===this.partList.length&&null===t)this.element.removeAttributeNS(this.attr.namespaceURI,this.attr.name);else{let t=this.partList.map(t=>"string"==typeof t?t:t.value).join("");this.element.setAttributeNS(this.attr.namespaceURI,this.attr.name,t)}}};var u=function(t,e,n){if(!e.has(t))throw TypeError("attempted to set private field on non-instance");return e.set(t,n),n},p=function(t,e){if(!e.has(t))throw TypeError("attempted to get private field on non-instance");return e.get(t)};let NodeTemplatePart=class NodeTemplatePart{constructor(t,e){this.expression=e,s.set(this,void 0),u(this,s,[t]),t.textContent=""}get value(){return p(this,s).map(t=>t.textContent).join("")}set value(t){this.replace(t)}get previousSibling(){return p(this,s)[0].previousSibling}get nextSibling(){return p(this,s)[p(this,s).length-1].nextSibling}replace(...t){let e=t.map(t=>"string"==typeof t?new Text(t):t);for(let t of(e.length||e.push(new Text("")),p(this,s)[0].before(...e),p(this,s)))t.remove();u(this,s,e)}};function f(t){return{createCallback(t,e,n){this.processCallback(t,e,n)},processCallback(e,n,i){var o;if("object"==typeof i&&i){for(let e of n)if(e.expression in i){let n=null!==(o=i[e.expression])&&void 0!==o?o:"";t(e,n)}}}}}function c(t,e){t.value=String(e)}function m(t,e){return"boolean"==typeof e&&t instanceof AttributeTemplatePart&&"boolean"==typeof t.element[t.attributeName]&&(t.booleanValue=e,!0)}s=new WeakMap;let g=f(c),x=f((t,e)=>{m(t,e)||c(t,e)});var v=function(t,e,n){if(!e.has(t))throw TypeError("attempted to set private field on non-instance");return e.set(t,n),n},y=function(t,e){if(!e.has(t))throw TypeError("attempted to get private field on non-instance");return e.get(t)};function*I(t){let e;let n=t.ownerDocument.createTreeWalker(t,NodeFilter.SHOW_TEXT|NodeFilter.SHOW_ELEMENT,null,!1);for(;e=n.nextNode();)if(e instanceof Element&&e.hasAttributes())for(let t=0;t<e.attributes.length;t+=1){let n=e.attributes.item(t);if(n&&n.value.includes("{{")){let t=new AttributeValueSetter(e,n);for(let e of i(n.value))if("string"===e.type)t.append(e.value);else{let n=new AttributeTemplatePart(t,e.value);t.append(n),yield n}}}else if(e instanceof Text&&e.textContent&&e.textContent.includes("{{"))for(let t of i(e.textContent)){t.end<e.textContent.length&&e.splitText(t.end),"part"===t.type&&(yield new NodeTemplatePart(e,t.value));break}}let TemplateInstance=class TemplateInstance extends DocumentFragment{constructor(t,e,n=g){var i,o;super(),a.set(this,void 0),h.set(this,void 0),Object.getPrototypeOf(this!==TemplateInstance.prototype)&&Object.setPrototypeOf(this,TemplateInstance.prototype),this.appendChild(t.content.cloneNode(!0)),v(this,h,Array.from(I(this))),v(this,a,n),null===(o=(i=y(this,a)).createCallback)||void 0===o||o.call(i,this,y(this,h),e)}update(t){y(this,a).processCallback(this,y(this,h),t)}};a=new WeakMap,h=new WeakMap}}]);
//# sourceMappingURL=vendors-node_modules_virtualized-list_es_index_js-node_modules_github_template-parts_lib_index_js-e081e1320153.js.map